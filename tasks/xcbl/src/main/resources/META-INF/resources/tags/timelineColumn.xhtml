<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:p="http://primefaces.org/ui"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:ui="http://java.sun.com/jsf/facelets">
    <p:column>
        <p:outputPanel styleClass="col-md-12">
            <p:outputPanel styleClass="form-group row">
                <p:outputPanel styleClass="col-sm-12 row">
                    <p:outputPanel styleClass="col-sm-3">
                        <h:outputText styleClass="step-title" value="#{doc.date}">
                            <f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" dateStyle="medium" timeStyle="short" timeZone="#{secSessionHandler.timeZone}" pattern="#{gnxSessionHandler.userDateFormat}" />
                        </h:outputText>
                    </p:outputPanel>
                    <p:outputPanel styleClass="timeline col-sm-2">
                        <p:outputPanel styleClass="timelineActionIcon fa fa-paperclip" rendered="#{doc.action eq 'ATTACHFILE'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-comments" rendered="#{doc.action eq 'COMMENT'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-user" rendered="#{doc.action eq 'LOCK'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-times-circle" rendered="#{doc.action eq 'REFUSE'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-user" rendered="#{doc.action eq 'UNLOCK'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-check-circle" rendered="#{doc.action eq 'VALID'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-stop" style="font-size: 22px;" rendered="#{doc.action eq 'END'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-play" rendered="#{doc.action eq 'INIT'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-sign-out" rendered="#{doc.action eq 'WKF_EXIT'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-play" rendered="#{doc.action eq 'REINIT'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-play" rendered="#{doc.action eq 'DISCONNECTED'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-pencil" rendered="#{doc.action eq 'ENRICH'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-clipboard" rendered="#{doc.action eq 'REDIRECT'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-arrow-circle-left" rendered="#{doc.action eq 'TASK_DONE'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-ban" rendered="#{doc.action eq 'TASK_CANCEL'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-pencil-square-o" rendered="#{doc.action eq 'EDIT'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-pencil-square-o" rendered="#{doc.action eq 'FIX'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-bell-o fontSizeInvoicePayment" rendered="#{doc.action eq 'REMINDER'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-bell-slash-o fontSizeInvoicePayment" rendered="#{doc.action eq 'PAUSE'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-wrench" rendered="#{doc.action eq 'CUSTOM'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-ban" rendered="#{doc.action eq 'RECONCILIATION_EMPTY_SELLER_PARTY'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-ban" rendered="#{doc.action eq 'RECONCILIATION_EMPTY_ORDER_NUMBER'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-ban" rendered="#{doc.action eq 'RECONCILIATION_ORDER_NOT_FOUND'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-ban" rendered="#{doc.action eq 'RECONCILIATION_EMPTY_RECEPTION_NUMBER'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-ban" rendered="#{doc.action eq 'RECONCILIATION_RECEPTION_NOT_FOUND'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-calendar" rendered="#{doc.action eq 'RECONCILIATION_START'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-check-square-o" rendered="#{doc.action eq 'RECONCILIATION_SUCCESS'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-exclamation" rendered="#{doc.action eq 'RECONCILIATION_GAP_FOUND'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-calendar" rendered="#{doc.action eq 'RECONCILIATION_RELAUNCH'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-envelope-open" rendered="#{doc.action eq 'CONSULT'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-handshake fontSizeInvoicePayment" rendered="#{doc.action eq 'EP_ACCEPTED'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-ban fontSizeInvoicePayment" rendered="#{doc.action eq 'EP_REFUSED'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-file-invoice-dollar fontSizeInvoicePayment" rendered="#{doc.action eq 'EP_SUBMITED'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-hand-pointer" rendered="#{doc.action eq 'CUSTOM_ACTION'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-play" rendered="#{doc.action eq 'INIT_NA'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-calendar" rendered="#{doc.action eq 'RECONCILIATION_NA'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-landmark" rendered="#{doc.action eq 'CTRL_RULES_FR' or doc.action eq 'CTRL_RULES_EU'
                                or doc.action eq 'CTRL_RULES_B2G2024' or doc.action eq 'CTRL_RULES_B2B2024' or doc.action eq 'CTRL_RULES_B2B' or doc.action eq 'CTRL_RULES_B2G' or doc.action eq 'CTRL_DUPLICATE'
                                or doc.action eq 'CTRL_MODIF_SIREN_CLIENT' or doc.action eq 'CTRL_MODIF_SIREN_SUPPLIER' or doc.action eq 'CTRL_MODIF_TVAINTRA_CLIENT' or doc.action eq 'CTRL_MODIF_TVAINTRA_SUPPLIER'
                                or doc.action eq 'CTRL_SCHEMATRON'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-project-diagram" rendered="#{doc.action eq 'LEGAL_ACTION_COMPLETED' or doc.action eq 'LEGAL_ACTION_PAYMENT_RECEIVED'
                                or doc.action eq 'LEGAL_ACTION_APPROVED' or doc.action eq 'LEGAL_ACTION_REFUSED' or doc.action eq 'LEGAL_ACTION_APPROVED_SUBROGATE' or doc.action eq 'LEGAL_ACTION_PAYMENT_SENT'
                                or doc.action eq 'LEGAL_ACTION_FACTOR' or doc.action eq 'LEGAL_ACTION_FACTOR_CONFIDENTIAL'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-user-check" rendered="#{doc.action eq 'ACTORS_DESIGNATION'}"/>
                        <p:outputPanel styleClass="timelineActionIcon fa fa-ballot-check" rendered="#{doc.action eq 'OCR_VERIFY' or doc.action eq 'OCR_POSTPONED'}"/>
                    </p:outputPanel>

                    <p:outputPanel styleClass="col-sm-7 timelineActions left">
                        <h:outputText styleClass="timelineUsername step-title" value="#{cc.attrs.value.getUsernameById(doc.user)} " rendered="#{doc.action ne 'INIT' and doc.action ne 'END' and doc.user ne 'System'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.init_action_message} #{cc.attrs.value.getTimelineComment(doc.id, doc.comment)}" rendered="#{doc.action eq 'INIT'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.wkf_exit_action_message}" rendered="#{doc.action eq 'WKF_EXIT'}"/>
                        <h:outputText styleClass="step-title" value="#{not empty doc.comment ? gnxxcblinvlbls.attatch_file_action_message_comment : gnxxcblinvlbls.attatch_file_action_message} " rendered="#{doc.action eq 'ATTACHFILE'}"/>
                        <h:outputText styleClass="step-title" value="#{not empty doc.comment ? gnxxcblinvlbls.refuse_action_message_comment : gnxxcblinvlbls.refuse_action_message} " rendered="#{doc.action eq 'REFUSE'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.custom_action_message}" rendered="#{doc.action eq 'CUSTOM' or doc.action eq 'CUSTOM_ACTION'}"/>
                        <h:outputText styleClass="step-title timelineCustomAction" value="#{cc.attrs.value.getTimelineComment(doc.id, doc.comment)}" rendered="#{doc.action eq 'CUSTOM'}"/>
                        <h:outputText styleClass="step-title timelineCustomAction" value="#{gnxHandler.label(cc.attrs.value.getTimelineComment(doc.id, doc.comment))}" rendered="#{doc.action eq 'CUSTOM_ACTION'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.transferTask_action_message}" rendered="#{doc.action eq 'REDIRECT'}"/>
                        <h:outputText styleClass="timelineUsername step-title" value="#{doc.redirectUser}" rendered="#{doc.action eq 'REDIRECT'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.give_back_control_action_message} " rendered="#{doc.action eq 'TASK_DONE'}"/>
                        <h:outputText styleClass="timelineUsername step-title" value="#{doc.redirectUser}" rendered="#{doc.action eq 'TASK_DONE'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.cancel_assign_task_action_message} " rendered="#{doc.action eq 'TASK_CANCEL'}"/>
                        <h:outputText styleClass="timelineUsername step-title" value="#{doc.redirectUser}" rendered="#{doc.action eq 'TASK_CANCEL'}"/>
                        <h:outputText styleClass="step-title" value=" #{gnxxcblinvlbls.transferTask_action_message_comment} " rendered="#{doc.action eq 'REDIRECT'}"/>
                        <h:outputText styleClass="step-title" value="#{doc.action eq 'COMMENT' and doc.user ne 'System' ? gnxxcblinvlbls.comment_action_message : gnxxcblinvlbls.comment_automatic_message}" rendered="#{doc.action eq 'COMMENT'}"/>
                        <h:outputText styleClass="step-title timelineComment" escape="false" value="&lt;br /&gt; #{cc.attrs.value.getTimelineComment(doc.id, doc.comment)}"
                                      rendered="#{doc.comment != null and doc.comment ne '' and doc.action ne 'INIT' and doc.action ne 'CUSTOM' and doc.action ne 'VALID' and doc.action ne 'END' and doc.action ne 'REMINDER'
	                                                           and doc.action ne 'RECONCILIATION_START' and doc.action ne 'RECONCILIATION_EMPTY_SELLER_PARTY' and doc.action ne 'RECONCILIATION_EMPTY_ORDER_NUMBER'
	                                                           and doc.action ne 'RECONCILIATION_ORDER_NOT_FOUND' and doc.action ne 'RECONCILIATION_EMPTY_RECEPTION_NUMBER' and doc.action ne 'RECONCILIATION_RECEPTION_NOT_FOUND' and doc.action ne 'RECONCILIATION_GAP_FOUND' and doc.action ne 'RECONCILIATION_SUCCESS'
	                                                           and doc.action ne 'EP_SUBMITED' and doc.action ne 'EP_ACCEPTED' and doc.action ne 'EP_REFUSED' and doc.action ne 'REINIT' and doc.action ne 'DISCONNECTED' and doc.action ne 'CUSTOM_ACTION' and doc.action ne 'CTRL_RULES_FR'
	                                                           and doc.action ne 'CTRL_RULES_EU' and doc.action ne 'CTRL_RULES_B2G2024' and doc.action ne 'CTRL_RULES_B2B2024' and doc.action ne 'CTRL_RULES_B2B' and doc.action ne 'CTRL_RULES_B2G' and doc.action ne 'CTRL_DUPLICATE'
	                                                           and doc.action ne 'CTRL_MODIF_SIREN_SUPPLIER' and doc.action ne 'CTRL_MODIF_SIREN_CLIENT' and doc.action ne 'CTRL_MODIF_TVAINTRA_CLIENT' and doc.action ne 'CTRL_MODIF_TVAINTRA_SUPPLIER' and doc.action ne 'CTRL_SCHEMATRON'
	                                                           and doc.action ne 'LEGAL_ACTION_COMPLETED' and doc.action ne 'LEGAL_ACTION_PAYMENT_RECEIVED' and doc.action ne 'LEGAL_ACTION_APPROVED' and doc.action ne 'LEGAL_ACTION_REFUSED'
                                                               and doc.action ne 'LEGAL_ACTION_APPROVED_SUBROGATE' and doc.action ne 'LEGAL_ACTION_PAYMENT_SENT' and doc.action ne 'LEGAL_ACTION_FACTOR' and doc.action ne 'LEGAL_ACTION_FACTOR_CONFIDENTIAL' and doc.action ne 'ACTORS_DESIGNATION'}"/>
                        <p:commandLink action="#{cc.attrs.value.showFullTimelineComment(doc.id)}"
                                       process="@this"
                                       global="false"
                                       value=" #{gnxxcblinvlbls.see_more}"
                                       rendered="#{doc.comment != null and doc.comment ne '' and cc.attrs.value.timelineTruncatedComments.get(doc.id)}"
                                       update="@(.timelineStyleClass)">
                        </p:commandLink>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.lock_action_message}" rendered="#{doc.action eq 'LOCK'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.unlock_action_message}" rendered="#{doc.action eq 'UNLOCK'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.validate_action_message}" rendered="#{doc.action eq 'VALID' and doc.user ne 'System'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.end_action_message}" rendered="#{doc.action eq 'END'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.enrich_action_message}" rendered="#{doc.action eq 'ENRICH'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.edit_action_message}" rendered="#{doc.action eq 'EDIT'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.fix_action_message}" rendered="#{doc.action eq 'FIX'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_start_message}" rendered="#{doc.action eq 'RECONCILIATION_START'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_empty_seller_party_message}" rendered="#{doc.action eq 'RECONCILIATION_EMPTY_SELLER_PARTY'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_empty_order_number_message}" rendered="#{doc.action eq 'RECONCILIATION_EMPTY_ORDER_NUMBER'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_order_not_found_message}" rendered="#{doc.action eq 'RECONCILIATION_ORDER_NOT_FOUND'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_empty_reception_number_message}" rendered="#{doc.action eq 'RECONCILIATION_EMPTY_RECEPTION_NUMBER'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_reception_not_found_message}" rendered="#{doc.action eq 'RECONCILIATION_RECEPTION_NOT_FOUND'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_gap_found_message}" rendered="#{doc.action eq 'RECONCILIATION_GAP_FOUND'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_success_message}" rendered="#{doc.action eq 'RECONCILIATION_SUCCESS'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.relaunch_reconciliation_message}" rendered="#{doc.action eq 'RECONCILIATION_RELAUNCH'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.consult_message}" rendered="#{doc.action eq 'CONSULT'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','early_payment_seller_accept_add_timeline',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'EP_ACCEPTED'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','early_payment_seller_reject_add_timeline',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'EP_REFUSED'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelForEpSubmitedAction('gnxxcblinvlbls','ep_submited_message',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'EP_SUBMITED'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','reinit_action_message',doc,xtdTimelineHandler.commentSeparator ,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'REINIT'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.disconnected_action_message} #{doc.comment}" rendered="#{doc.action eq 'DISCONNECTED'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.workflow_not_applicable_message}" rendered="#{doc.action eq 'INIT_NA'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.reconciliation_not_applicable_message}" rendered="#{doc.action eq 'RECONCILIATION_NA'}"/>
                        <h:outputText styleClass="step-title" value=" (#{gnxxcblinvlbls.by_delegation_of_action_message}  " rendered="#{doc.delegatorUser != null and doc.delegatorUser ne ''}"/>
                        <h:outputText styleClass="timelineUsername step-title" value="#{doc.delegatorUser}" rendered="#{doc.delegatorUser != null and doc.delegatorUser ne ''}"/>
                        <h:outputText styleClass="step-title" value=") " rendered="#{doc.delegatorUser != null and doc.delegatorUser ne ''}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.control_fr_message}" rendered="#{doc.action eq 'CTRL_RULES_FR'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.control_eu_message}" rendered="#{doc.action eq 'CTRL_RULES_EU'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParametersByProfile('gnxxcblinvlbls','control_b2g2024_message',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_RULES_B2G2024'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParametersByProfile('gnxxcblinvlbls','control_b2b2024_message',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_RULES_B2B2024'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParametersByProfile('gnxxcblinvlbls','control_b2b_message',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_RULES_B2B'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParametersByProfile('gnxxcblinvlbls','control_b2g_message',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_RULES_B2G'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.control_duplicate_message}" rendered="#{doc.action eq 'CTRL_DUPLICATE'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','control_modif_siren_client',doc,';',gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_MODIF_SIREN_CLIENT'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','control_modif_siren_supplier',doc,';',gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_MODIF_SIREN_SUPPLIER'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','control_modif_tvaintra_client',doc,';',gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_MODIF_TVAINTRA_CLIENT'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','control_modif_tvaintra_supplier',doc,';',gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_MODIF_TVAINTRA_SUPPLIER'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelMultipleCommentParameters('gnxxcblinvlbls','control_schematron_message',doc,xtdTimelineHandler.commentSeparator ,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'CTRL_SCHEMATRON'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.legal_action_completed}" rendered="#{doc.action eq 'LEGAL_ACTION_COMPLETED'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.legal_action_payment_received}" rendered="#{doc.action eq 'LEGAL_ACTION_PAYMENT_RECEIVED'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.legal_action_approved}" rendered="#{doc.action eq 'LEGAL_ACTION_APPROVED'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.legal_action_refused}" rendered="#{doc.action eq 'LEGAL_ACTION_REFUSED'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.legal_action_approved_subrogate}" rendered="#{doc.action eq 'LEGAL_ACTION_APPROVED_SUBROGATE'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.legal_action_payment_sent}" rendered="#{doc.action eq 'LEGAL_ACTION_PAYMENT_SENT'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.legal_action_factor}" rendered="#{doc.action eq 'LEGAL_ACTION_FACTOR'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.legal_action_factor_confidential}" rendered="#{doc.action eq 'LEGAL_ACTION_FACTOR_CONFIDENTIAL'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelForDesignationOfActors('gnxxcblinvlbls','actors_designation_message',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'ACTORS_DESIGNATION'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.ocr_verify_action_message}" rendered="#{doc.action eq 'OCR_VERIFY'}"/>
                        <h:outputText styleClass="step-title" value="#{gnxxcblinvlbls.ocr_postponed_action_message}" rendered="#{doc.action eq 'OCR_POSTPONED'}"/>

                        <!-- Automatic messages -->
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelForAutomaticValidation('gnxxcblinvlbls','valid_action_automatic_message',doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'VALID' and doc.user eq 'System'}"/>
                        <h:outputText styleClass="step-title" value="#{xtdTimelineHandler.getLabelForAutomaticReminder('gnxxcblinvlbls','reminder_action_automatic_message', doc,gnxSessionHandler.locale)}" rendered="#{doc.action eq 'REMINDER' and doc.user eq 'System'}"/>
                    </p:outputPanel>

                </p:outputPanel>

            </p:outputPanel>
        </p:outputPanel>
    </p:column>
</ui:composition>