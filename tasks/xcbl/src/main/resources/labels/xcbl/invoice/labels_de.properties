order_ref_num = Bestellreferenz
PREPAYMENT_INVOICE = Anzahlungsrechnung
owners = Eigent\u00FCmer
COMMERCIAL_INVOICE = Rechnung
invoiceHeader_invoiceDates_invoiceDueDate = F\u00E4lligkeit der Rechnung
invoice_reference_period_end_date = Ende des Zeitraums
edit_product = Rechnungszeile bearbeiten
advance_shipment_notice_ref = Referenz der Versandanzeige bearbeiten
asn_ord_number=Bestellnummer bearbeiten
debit_note_financial_adjustment = Lastschrift im Zusammenhang mit der Finanzanpassung
ERROR = Fehler
tel = Tel.
FACTORED_INVOICE = Factoring-Rechnung
payment_date = Anzahlungsdatum
proforma_invoice = Proforma-Rechnung
ref_allow_or_charge_tax = Referenzrabatte und -zuschl\u00E4ge
edit = Bearbeiten
list_tax_summary = USt.-Aufteilung
invoiceHeader_invoiceParty_sellerParty_nameAddress_name1 = Anbieter
payment_amount = Anzahlungsbetrag
credit_note_financial_adjustment = Gutschrift im Zusammenhang mit Finanzanpassungen
invoice_reference_date = Referenzdatum
invoice_reference_period_coherence = Das Enddatum des Zeitraums muss nach dem Startdatum liegen.
reason_tax_exempt_coded_other = Grund f\u00FCr die Befreiung
detail = Details
invoicing_end_date = Ablaufdatum der G\u00FCltigkeit
actions = Rechnungsaktionen
DEBIT_NOTE_FINANCIAL_ADJUSTMENT = Lastschrift im Zusammenhang mit der Finanzanpassung
invoice_reference_date_or_period_required = Datum oder Zeitraum sind Pflichtfelder.
invoiceHeader_invoiceParty_billToParty_partyTaxInformation_taxIdentifier_ident = MWSt-Kennzeichen Rechnungsempf\u00E4nger
invoicing_credit = Im Falle einer Gutschrift oder einer Dienstleistungsrechnung
debit_note_goods_and_services = Lastschrift \u2013 Waren und Dienstleistungen
invoiceHeader_invoiceParty_sellerParty_partyID_ident = Code Anbieter
Buyer = K\u00E4ufer
EDIUNIT = EDI-Datei
invoice_reference_type = Referenztyp
search = Suchen
error_duplicate_invoice_number = Die Rechnungsnummer existiert bereits.
CREDIT_NOTE_FINANCIAL_ADJUSTMENT = Gutschrift im Zusammenhang mit Finanzanpassungen
LINE_ITEM_ALLOWANCE = Rabatt
HeadOffice = Firmensitz des Lieferanten
unit_net_price = Netto-Einheitspreis
taxable_amount = USt.-Aufteilung: steuerpflichtiger Betrag
invoiceHeader_invoiceParty_billToParty_nameAddress_country_countryCoded = Land Rechnungsempf\u00E4nger
refused = Abgelehnt
indicator_coded = Indikator
invoice_reference_type_CONTRACT = Vertrag
stage = Rechtsstatus
invoice_reference_num = Referenznummer
PERCENT = Prozent
ShipTo = Geliefert an
to = Empf\u00E4nger
error_empty_invoice_number = Die Rechnungsnummer darf nicht leer sein.
item_description = Beschreibung
PROFORMA_INVOICE = Proforma-Rechnung
country = Land
invoiceHeader_invoiceNumber = Rechnungsnummer
monetary_amount = Betrag
unit_of_measurement = Einheit
LINE_ITEM_CHARGE = Belastung
invoiceHeader_invoiceType_invoiceTypeCoded = Rechnungsart
contact = Kontakt
details = Details
invoiceHeader_invoiceCurrency_currencyCoded = W\u00E4hrung
order_type = Bestelltyp
consolidated_invoice = Konsolidierte Rechnung
product_identifier = Code EAN
invoiceHeader_invoiceParty_buyerParty_nameAddress_country_countryCoded = Land K\u00E4ufer
invoice_party_header = Rechnungseinheiten
asn_ref_date = Versandavis-Datum
invoiceHeader_invoiceParty_billToParty_nameAddress_name1 = Rechnungsempf\u00E4nger
invoice_columns_details = Spalten der Rechnungsdetails
invoice_total = Gesamtbetrag inkl. USt.
name = Name
line_allowance_or_charge = Rabatte und Zuschl\u00E4ge auf die Zeile
pending = Ausstehend
description = Beschreibung
invoiceHeader_invoiceParty_billToParty_partyID_ident = Code Rechnungsempf\u00E4nger
invoiceHeader_invoiceParty_buyerParty_partyTaxInformation_taxIdentifier_ident = MWSt-Kennzeichen K\u00E4ufer
DEBIT_NOTE_GOODS_AND_SERVICES = Lastschrift \u2013 Waren und Dienstleistungen
seller_part_number = Produktcode des Lieferanten
error_empty_party_identifier = Einer der drei Werte ist ein Pflichtfeld (RCS-RCM, SIREN, USt.-IdNr): %1$s
legend_accepted = akzeptiert
product_identifier_ext = Aktionsvariante
street = Stra\u00DFe
add_invoice_reference = Referenz hinzuf\u00FCgen
CHARGE = Belastung
summary = Fu\u00DFzeile der Rechnung
error_in_invoice_generation = Fehler beim Erstellen der Rechnung
total_net_amount = Nettogesamtbetrag
quantity_value = Fakt. Menge
invoice_dates = Rechnungsdaten
tax_category = USt.-Aufteilung: Satz
invoice_reference_period_start_date = Beginn des Zeitraums
order_date = Bestelldatum
preparation_date = Vorbereitungsdatum
header_note = Rechtsform und Stammkapital des Lieferanten
CORRECTED_INVOICE = Berichtigte Rechnung
invoice_reference_type_DISPATCH_ADVICE = Versandavis
Seller = Lieferant
error_date = Das Startdatum muss vor dem Enddatum liegen.
select = Ausw\u00E4hlen
invoiceSummary_invoiceTotals_totalAmountPayable_monetaryAmount = Zahlbetrag
credit_note_goods_and_services = Haben
other_party_info = Andere Einheiten
none = Ungelesen
type = Standard
remove_product = Rechnungszeile l\u00F6schen
remove_alloworchrg = Rabatt oder Geb\u00FChr l\u00F6schen
error_empty_invoice_item_details = Die Produktliste darf nicht leer sein.
legend_refused = abgelehnt
duplicate_invoice_number = Eindeutigkeit der Rechnungsnummer
fax = Fax
invoice_number = Rechnungs-Nr.
invoice_reference_date_only = Datum und Zeitraum k\u00F6nnen nicht gleichzeitig angegeben werden.
identifier = ID
ShipFrom = Absender
invoiced = Fakturiert
tax_amounty = USt.-Aufteilung: USt.-Betrag
parties_tabs = Registerkarte Entit\u00E4ten
CONSOLIDATED_INVOICE = Konsolidierte Rechnung
commercial_invoice = Rechnung
prepayment_invoice = Vorauszahlungsrechnung
status = Status
info_confirm_before_leaving = Wenn Sie \u00C4nderungen vorgenommen haben, ohne diese zu best\u00E4tigen, gehen Ihre \u00C4nderungen verloren.
add_alloworchrg = Rabatt oder Zuschlag hinzuf\u00FCgen
factored_invoice = Factoring-Rechnung
other = Andere
parties_tabs_fieldset = Bearbeiten
city = Stadt
info_confirm_action = Ihre Rechnung wurde erfolgreich generiert
asn_ref_num = Referenz des Versandavis
invoice_reference_period_required = Zeitraum ist f\u00FCr diese Referenzart ein Pflichtfeld.
invoice_reference_type_ORDER = Bestellung
issue_date = Datum
net_due_date = F\u00E4lligkeitsdatum
remove_invoice_reference = Referenz l\u00F6schen
product_from_repository = Rechnungszeile hinzuf\u00FCgen
total_tax_amount = USt.-Betrag
date_of_operation = Transaktionsdatum
legend_invoice = Fakturiert
add_product = Produkt hinzuf\u00FCgen
gouv_ref = SIREN
PENDING = Ausstehend
invoice_reference_type_INVOICE = Rechnung
complete_edition = Vollst\u00E4ndige Ausgabe generieren
filter = Filtern
ref_num = Nummer der Anzahlung
payment_terms_note = Bedingungen f\u00FCr Strafgeb\u00FChren
invoiceHeader_invoiceParty_buyerParty_nameAddress_name1 = K\u00E4ufer
postal_code = Postleitzahl
invoice_reference_date_required = Datum ist f\u00FCr diese Referenzart ein Pflichtfeld.
payment_instructions = Zahlungsanweisungen
discount_terms = Skontobedingungen
confirm_invoice = Best\u00E4tigen
EDI = Datenaustausch
invoice_references = Referenzen
editable_fields = Bearbeitbare Felder
allowance_or_charge = Globale Kopfzeilenrabatte und -zuschl\u00E4ge
invoiceHeader_invoiceParty_sellerParty_nameAddress_country_countryCoded = Land Anbieter
Factor = Factor
BillTo = Rechnungsempf\u00E4nger
LINE_ITEM_TAX_REGULATORY_TAX = Parafiskalische Steuer
tax_mandatory_header = USt.-Code erforderlich f\u00FCr
SERVICE = Parafiskalische Steuer
from = Sender
new = Neu
read = Gelesen
tax_exempt = Befreiung von der USt
accepted = Akzeptiert
tax = USt.-Satz
header_info = Rechnungsinformationen
taxable_value = Gesamtbetrag ohne Steuern
ALLOWANCE = Rabatt
invoiceSummary_invoiceTotals_invoiceTotal_monetaryAmount = Gesamtbetrag
list_actual_payment = Anzahlungen
standard_dialog_header = Rechnung importieren (*.csv)
files = Anzeigen
header = Kopfzeile
DeclarantsAgentOrRepresentative = Steuervertreter
total_net = Nettobetrag ohne Steuer
option = Option
legal_form_share_capital = Rechtsform und Aktienkapital
invoice_reference_type_DEBIT_NOTE = Belastungsnote
error = Fehler
tva_ident = USt-IdNr.
percent = Prozent
CREDIT_NOTE_GOODS_AND_SERVICES = Haben
buyer_part_number = K\u00E4ufer-Produktcode
enable_safebox_search = Im Tresor gespeichert
add_entity = Entit\u00E4t hinzuf\u00FCgen
MONETARY_AMOUNT = Betrag
error_empty_invoice_item_vat = USt.-Satz ist ein Pflichtfeld: %1$s
actual_ship_date = Versanddatum
invoicing_start_date = Startdatum der G\u00FCltigkeit
inv_type = Typ
value = Wert
free_quantity_value = Kostenlose Menge
info_lengthy_operation = M\u00F6glicherweise langwieriger Vorgang
party_role_coded = Rolle der Entit\u00E4t
due_date = F\u00E4lligkeitsdatum
actual_delivery_date = Lieferdatum
invoiceHeader_invoiceParty_buyerParty_partyID_ident = K\u00E4ufercode
allowCompleteEdition = Vollst\u00E4ndige Bearbeitung zulassen
recordNumber = RCS-RCM
invoiceHeader_invoiceIssueDate = Rechnungsdatum
total_ttc = Gesamtbetrag inkl. USt.
XML = XML
credit_invoice = Gutschriftsrechnung
invoiceSummary_invoiceTotals_taxableValue_monetaryAmount = Versteuerbarer Betrag
gross_price = Bruttoeinheitspreis
CREDIT_INVOICE = Gutschriftsrechnung
invoice = Rechnung stellen
invoiceSummary_invoiceTotals_totalTaxAmount_monetaryAmount = Steuerbetrag
invoice_currency = W\u00E4hrung
invoiceHeader_invoiceParty_sellerParty_partyTaxInformation_taxIdentifier_ident = MWSt-Kennzeichen Anbieter
visible_fields = Sichtbare Felder
corrected_invoice = Korrigierte Rechnung
invoiceHeader_invoiceParty_sellerParty_listOfIdentifier_identifier_ident=ID
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#1=Adresskennung\u00A01
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#2=Adresskennung\u00A02
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#3=Adresskennung\u00A03
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#4=Adresskennung\u00A04
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#5=Adresskennung\u00A05
reference=Referenz
undefined=Nicht definiert
error_unit_price_negative=Der Einzelpreis ist negativ.
error_total_price_negative=Der Gesamtbetrag ist negativ.
gross_amount = Gesamtbetrag
vat_amount = USt.-Betrag
add_allowance = Rabatt
add_charge = Belastung
archiveStatus = Archivierungsstatus
unselect_all = Alles abw\u00E4hlen
select_all = Alles ausw\u00E4hlen

#Archive statuses
UNDEFINED=Unbestimmt
ARCHIVED = Archiviert
multiple_invoices_selected = Elemente ausgew\u00E4hlt
confirm_delete = M\u00F6chten Sie die Rechnung wirklich l\u00F6schen?
open = \u00D6ffnen
history_action = Pr\u00FCfen
invoice_add_attachment = Anhang hinzuf\u00FCgen
history_time = An
validate_action = Best\u00E4tigen
custom_action_message = die folgende Aktion durchgef\u00FChrt:
all_attached_files = Alle Anh\u00E4nge
size = Gr\u00F6\u00DFe
received_on = erhalten am
error_no_demat_errors = Diese Rechnung hat keine Fehlerdatei zur Digitalisierung.
tax_original = Steuerlich g\u00FCltiges Original
demat_errors = Fehler bei der Digitalisierung
error_no_tax_original = Kein Steueroriginal f\u00FCr diese Rechnung
legal_action_refused = Die Rechnung wurde abgelehnt.
legal_action_factor = Die Rechnung wurde zum Factoring eingereicht.
A1 = A1: Einreichung einer Rechnung durch einen Lieferanten
A2 = A2: Einreichung einer bereits bezahlten Rechnung
A9 = A9: Einreichung einer Rechnung durch einen Subunternehmer
correct_file_not_found = Die Korrekturdatei wurde nicht gefunden
standard_inv_edition_mode = Ausgabe
assignTask_dialog_comment = Erkl\u00E4ren und begr\u00FCnden Sie Ihre Erwartungen
legal_action_factor_confidential = Die Rechnung wurde zum vertraulichen Factoring eingereicht.
add_parafiscal_tax_tooltip_line7 = Der Endpreis betr\u00E4gt: 10 + 1 = 11 \u2013 10\u00A0% = 9,9
add_parafiscal_tax_tooltip_line8 = Aber wenn die Reihenfolge der Zeilen umgekehrt wird.
add_parafiscal_tax_tooltip_line9 = Der Endpreis betr\u00E4gt: 10 -10\u00A0% = 9 + 1 = 10
add_parafiscal_tax_tooltip_line3 = Rabatte, Zuschl\u00E4ge und Steuern sind kumulativ.
DELCREDERE_INVOICE = Delkredere-Rechnung
add_parafiscal_tax_tooltip_line4 = Zum Beispiel: ein Produkt mit einem Bruttopreis von 10\u00A0\u20AC.
refused_manually = Ablehnen
add_parafiscal_tax_tooltip_line5 = Wenn auf der ersten Zeile eine Transportgeb\u00FChr von 1\u00A0\u20AC hinzugef\u00FCgt wird.
add_parafiscal_tax_tooltip_line6 = Dann ein Rabatt von 10\u00A0% auf die zweite Zeile.
add_parafiscal_tax_tooltip_line1 = F\u00FCr einen Rabatt, eine Geb\u00FChr oder eine parafiskalische Steuer muss der Betrag f\u00FCr ein Produkt angegeben werden.
add_parafiscal_tax_tooltip_line2 = Beachten Sie die Reihenfolge der Zeilen.
invoice_VAT_type = USt.-Typ
sellerPartyOtherID = Verk\u00E4ufer (andere Kennung)
std_invoicenumber_title_add = Hinzuf\u00FCgen der Rechnung Nr.
DEBIT_TRANSFER = Bank\u00FCberweisung
rec_difference = Abweichung
reconciliation_success_message = Abgleich abgeschlossen.
allowance_or_charge_customer = Rabatte und Zuschl\u00E4ge
end_action_message = Automatische Nachricht: Ende des Workflows
assignTask_dialog_actors = F\u00FCr die Aufgabe zust\u00E4ndiger Akteur
detail_precision = Anzahl der Dezimalstellen f\u00FCr Preis und Betrag auf Zeilenebene
rossumId = Rossum-ID
error_invoice_export_no_records = Sie k\u00F6nnen keine leere Liste exportieren.
otherPartyOtherID = Anderer Beteiligter (andere Kennung)
addOtherParties = Weitere Entit\u00E4t hinzuf\u00FCgen
ocr_warning_details = Achtung, mindestens ein nicht sichtbares Feld auf dem Bildschirm muss in dieser Zeile \u00FCberpr\u00FCft werden.
shipToAddressEmail = E-Mail-Adresse des Empf\u00E4ngers
invoice_control_portlet = Portlet f\u00FCr die Rechnungspr\u00FCfung
invoice_portlet_visualization = Portlet zur Anzeige von Rechnungen
manage_parafiscal_tax = Zusammenfassung der parafiskalischen Steuern verwalten
files_title = Anh\u00E4nge
correct = Korrigieren
no_error_found = Es wurden keine Fehler gefunden
stopNotification_action = Benachrichtigungen pausieren
amount_payable = - Zu zahlender Betrag:
confirm_creation = Best\u00E4tigen und senden
billingFramework = Rechnungsrahmen
contractID = Vertrag (Kennung)
rec_dld__header_of = vom
rec_all_lines = Alle Zeilen ansehen
buyerPartyTaxIdentifier = USt.-Code des K\u00E4ufers
confirmation_title_edit = Best\u00E4tigung
contactName = Name des Kontakts
invoiceIssueDate = Rechnungsdatum
shipToPartyID = Kennung des Empf\u00E4ngers
gouv_hashcode = Staatlicher Hashcode
SELF_BILLED_FACTORED_CREDIT_NOTE = Selbstfakturierte Factoring-Gutschrift
otherPartyCountryCoded = L\u00E4ndercode (anderer Beteiligter)
comment_action = Kommentieren
hidden_fields = Versteckte Felder
sellerAddressEmail = E-Mail-Adresse des Lieferanten
RemitTo = Beg\u00FCnstigter
enrich_action_message = hat die Rechnungsdaten angereichert
sellerOrderNumber = Bestellnummer des Verk\u00E4ufers
control_b2b2024_message = Dieses Dokument wurde den geltenden Pr\u00FCfungen f\u00FCr eine B2B-Rechnung (erste Phase der Reform) gem\u00E4\u00DF den Verwaltungsregeln ({0}) aus Anhang\u00A07 der externen B2B-Spezifikationen der franz\u00F6sischen Regierung unterzogen.
rec_reception = Empfang
error_const_allocation_double = Es ist nicht m\u00F6glich, einen analytischen Code sowohl in der Kopfzeile als auch auf Zeilenebene f\u00FCr dieselbe Rechnung zu verwenden.
mail_not_valid = Ung\u00FCltige E-Mail
lines = Zeilen
error_invoice_select_line = Es ist keine Rechnung ausgew\u00E4hlt.
aap_missing_po_message = Verbuchung nicht m\u00F6glich: Bestellung nicht gefunden
order_response = Antwort auf die Bestellung
rec_reception_number = Empfang Nr.
not_rec_all_lines = Fehlerhafte Zeilen ansehen
copy_bill_to_information = Informationen des Rechnungsempf\u00E4ngers kopieren
third_party_consolidated_invoice = Konsolidierte Rechnung eines Drittanbieters
vat_breakdown = USt.-Aufteilung
legal_action_approved = Die Rechnung wurde genehmigt.
mandatory_fields = Pflichtfelder
reconciliation_reception_not_found_message = Abgleich nicht m\u00F6glich: Kein Empfang gefunden.
see_more = mehr anzeigen
portlet_mode = Art der Verwendung des Portlets
legal_action_completed = Die Rechnung wurde vervollst\u00E4ndigt.
invoice_reference_type_ORDER_RESPONSE = Antwort auf die Bestellung
quick_search_strict = Streng
triggerActions_fieldset = Aktionen hinzuf\u00FCgen
triggerActions_multiStatusError = Die angeforderte Aktion ist f\u00FCr Rechnungen mit Status \u201E{0}\u201C nicht zul\u00E4ssig. Die Aktion wurde f\u00FCr diese Rechnungen nicht gestartet.
manageInvoiceReference = Referenzen verwalten
debit_memo = Belastungsnote
vat_code = USt.-Code
valid_action_automatic_message = Automatische Meldung: Anzahl der Erinnerungen erreicht, automatische Validierung von Schritt Nr. {0}.
confirmation_information = mit den folgenden Informationen:
immatpdp = PDP
sovosId = Sovos-ID
legal_action_approved_subrogate = Die Rechnung wurde zur Subrogation eingereicht.
redirect_user_comment_message = F\u00FCr diese Aktion ist Kommentar ein Pflichtfeld. Der Kommentar wird an die zust\u00E4ndige Person weitergeleitet. Beschreiben Sie Ihre Anfrage also bitte m\u00F6glichst pr\u00E4zise.
error_create_invoice = Es ist nicht m\u00F6glich, eine Rechnung als Kundenbenutzer zu erstellen. Bitte melden Sie sich als Lieferant an, wenn Sie eine Rechnung erstellen m\u00F6chten.
eReporting_portlet_selection = Portlet zum Hinzuf\u00FCgen von Berichten
invoice_billing_framework = Rechnungsrahmen
stopNotification_message = hat die Erinnerungen pausiert
street_supplement = Adresszusatz
ART_262 = Artikel 262 I und II des franz\u00F6sischen Steuergesetzbuchs (CGI)
early_payment_seller_reject_add_timeline = : hat den Vorschlag zur vorzeitigen Zahlung mit folgendem Kommentar abgelehnt: \u201E{0}\u201C
standard_inv_creation_mode = Erstellung
rec_reconcilation_done = Abgleich durchgef\u00FChrt
assignTask_dialog_actions = Auszuf\u00FChrende Aktionen
buyerPartyCountryCoded = Land des K\u00E4ufers
refuse_action_message_comment = hat die Rechnung mit folgendem Kommentar abgelehnt:
cancel_assign_task_action_message = hob die Aufgabenanforderungen storniert f\u00FCr
parafiscal_tax_breakdown = Aufteilung der steuer\u00E4hnlichen Steuer
rec_dld_header = Analyse der Abstimmungsabweichungen der Rechnung Nr.
control_fr_message = Dieses Dokument wurde den in Frankreich im Jahr\u00A02024 geltenden Pr\u00FCfungen unterzogen.
aap_partial_message = Verbuchung teilweise erfolgt
sellerPartyCountryCoded = Land des Lieferanten
confirmation_number = Nr.
BuyersAgentOrRepresentative = K\u00E4uferagent
control_duplicate_message = Die Duplikatspr\u00FCfung wurde durchgef\u00FChrt.
confirm_refuse_selected = M\u00F6chten Sie die ausgew\u00E4hlte(n) Rechnung(en) wirklich ablehnen?
reconciliation_order_not_found_message = Abgleich nicht m\u00F6glich: Bestellung nicht gefunden.
ART_283 = Artikel 283-1 franz\u00F6sischen Steuergesetzbuchs (CGI)
error_invoice_file_missing = Elektronische Rechnung nicht vorhanden. Bitte wenden Sie sich an Ihren Administrator.
give_back_control_action = \u00DCbergabe
earlyPaymentAnnualDiscountRate = Jahreszinssatz (%)
control_modif_siren_supplier = Die SIREN-Nummer des Lieferanten wurde ge\u00E4ndert. Alter Wert: {0}. Neuer Wert: {1}
cache_pdf = Verwaltung des PDF-Caches
buyerPartyName = K\u00E4ufer
aap_missing_ref_message = Verbuchung nicht m\u00F6glich: Verzeichnis nicht gefunden
payment_terms_note_incorrect = Ung\u00FCltige Strafbedingungen
make_file_readable_checkbox_label = Diese Datei verwenden, um die aktuelle lesbare Datei zu ersetzen.
control_b2g2024_message = Dieses Dokument wurde den geltenden Pr\u00FCfungen f\u00FCr eine B2G-Rechnung (erste Phase der Reform) gem\u00E4\u00DF den Verwaltungsregeln ({0}) aus Anhang\u00A07 der externen B2B-Spezifikationen der franz\u00F6sischen Regierung unterzogen.
footer = Fu\u00DFzeile
display_pdf = PDF neben der Eingabe anzeigen
taxableValueInTaxAccountingCurrency = Steuerpflichtiger Betrag (in der Zahlungsw\u00E4hrung der USt)
cause = Grund
reconciliation_start_message = Abgleich gestartet.
force_cause_required = Der Grund ist ein Pflichtfeld.
confirmation_message_edit = Best\u00E4tigen Sie die \u00C4nderungen an der Rechnung
confirm_return = Bedenken Sie, dass beim Verlassen der Bearbeitungsseite die korrigierten Daten nicht gespeichert werden. M\u00F6chten Sie die Bearbeitungsseite verlassen?
ep_submited_message = : schl\u00E4gt vor, die Rechnung {0} Tage vor dem F\u00E4lligkeitsdatum zu bezahlen, um im Gegenzug ein Skonto zu erhalten.
purchaseOrderDate = Bestelldatum
std_invoicenumber_title = Bearbeitung der Rechnung Nr.
document_refused = Das Dokument wurde abgelehnt.
general_product_tab = Allgemein
sellerPartyName = Lieferant
editable = Bearbeitbar
otherPartyID = ID (anderer Beteiligter)
error_total_lt_zero = Die Gesamtsumme Ihrer Rechnung darf nicht negativ sein.
metered_services_invoice = Verbrauchsabh\u00E4ngige Dienstleistungsrechnung
otherPartyRole = Rolle (anderer Beteiligter)
edit_action_message = hat die Rechnung ge\u00E4ndert
INSTRUMENT_NOT_DEFINED = Andere
CORRECTED_FACTORED_INVOICE = Berichtigte Factoring-Rechnung
transferTask_action = Aufgabe zuweisen
rec_amount = Betrag
link_to_referential_tooltip_supplier = Die Informationen werden aus dem Kontaktbereich abgerufen, au\u00DFer im Fall der Selbstfakturierung, wo sie aus dem Adressverzeichnis stammen.
ocr_verification_status_list = Liste der Status, die Videocodierung zulassen nur f\u00FCr jsf
confirmation_title_creation = Versandbest\u00E4tigung
A12 = A12: Einreichung einer Rechnung durch einen Mitunternehmer
asnNumber = Lieferschein-Nr.
orderingParty = Auftraggeber
footer_precision = Anzahl der Dezimalstellen f\u00FCr Preis und Betrag in der Fu\u00DFzeile
confirm_no_workflow = Diese Rechnung unterliegt keinem Workflow mehr. Alle Schritte des alten Workflows gehen verloren. M\u00F6chten Sie dies best\u00E4tigen?
add_charge_customer = Zuschlag hinzuf\u00FCgen
error_order_select_line = Keine Bestellung ausgew\u00E4hlt
SUBMITTED = \u00DCberwiesen
manage_files = Hinzuf\u00FCgen
consultStatus = Beratungsstatus
valueControl = Pr\u00FCfliste
DETOUR_BILLING = Umleitungsabrechnung
triggerActions_add = Hinzuf\u00FCgen
duplicate_check_error = Die Eindeutigkeitsregeln f\u00FCr franz\u00F6sische Rechnungen wurden nicht eingehalten (Rechnungs-Nr.; Rechnungsjahr; Absender).
disconnected_action_message = Automatische Nachricht: Das Dokument wurde vom Workflow getrennt:
designate_next_step_actors_dialog = Benennung der Akteure des n\u00E4chsten Schritts
rec_differences_invoice_line_title = Abstimmungsdifferenzen auf Zeilenebene
receptionNumber = Empfangs-Nr.
OTHER_VIREMENTETRANGER = Auslands\u00FCberweisung
tip_one_correct_invoice = Fehlende Daten bitte unten gem\u00E4\u00DF Ihrem Rechnungsformat eingeben
triggerActions_global = Global
add_allow_customer = Rabatt hinzuf\u00FCgen
init_action_message = Automatische Nachricht: Das Dokument ist mit folgendem Workflow verkn\u00FCpft:
SELF_BILLED_CREDIT_NOTE = Selbstfakturierte Gutschrift
archiving_error = Fehler bei der Archivierung einer Datei
legal_action_payment_sent = Die Zahlung der Rechnung wurde weitergeleitet.
control_modif_tvaintra_client = Die USt-IdNr. des Kunden wurde ge\u00E4ndert. Alter Wert: {0}. Neuer Wert: {1}
discard_btn = Verlassen
street_supplement_incorrect = Adresszusatz unvollst\u00E4ndig
item_tax_amount = Umsatzsteuerbetrag
dispatch_advice = Versandavis
stop_notification_comment = Mahnbenachrichtigungen wurden gestoppt
lifeCycleComplete = Details zum Lebenszyklus
invoiceProcessDateTime = Digitalisierungsdatum
error_process_not_deployed = Prozess nicht bereitgestellt
DEBIT_INVOICE = Belastungsrechnung
earlyPaymentAmountDiscount = Skonto exkl. USt
error_in_conversion = Eine Rechnung kann nicht erstellt werden. Bitte wenden Sie sich an Ihren Administrator.
unauthorizedValidation = Eine oder mehrere Rechnungen wurden bereits von einem anderen Benutzer validiert oder gesperrt.
billToPartyCountryCoded = Land des Rechnungsempf\u00E4ngers
rec_invoiced = Fakturiert
referenceInvoice = Referenzrechnungsnummer
amount_to_pay = Zu zahlender Betrag
print_fieldset = Drucken
ART_275A = Artikel 275A franz\u00F6sischen Steuergesetzbuchs (CGI) \u00FCber die Mehrwertsteuerbefreiung
add_date = Datum der Hinzuf\u00FCgung
hidden = Verborgen
quick_search_start_with = Beginnt mit (Standardwert)
OTHER_TRAITEACCEPTFOUR = Wechselannahme durch Lieferanten
itemLineNumber = Zeilen-Nr.
unauthorizedRefuse = Eine oder mehrere Rechnungen wurden bereits von einem anderen Benutzer validiert, abgelehnt oder gesperrt.
debit_note = Belastungsnote
invoiceNumber = Rechnung Nr.
rec_total = (Gesamt)
late_payment = Verzugsentsch\u00E4digung
invoice_cost_allocation_number_control = Pr\u00FCfung der Anwesenheit des analytischen Codes
triggerActions_errorInProgress = Es l\u00E4uft bereits eine Aktion f\u00FCr diese Rechnung
error_allwOrChrg_equals_zero = Ein Rabatt oder eine Steuer darf nicht 0 betragen.
sovosStatus = Sovos-Status
customerReferenceNumber = Kundenreferenz
refuse_message_mandatory = Ablehnungsgrund ist ein Pflichtfeld.
taxableValue = Gesamtbetrag ohne Steuern
aap_na_message = Verbuchung nicht anwendbar
vat_breakdown_taxable_amount = Steuerpflichtiger Betrag
refuse_action_message = hat die Rechnung abgelehnt
by_delegation_of_action_message = im Auftrag von
history_none = Kein Verlauf f\u00FCr die Rechnung {0} gefunden
invoice_payment_choice = Zahlungsart
error_invoice_download = Fehler beim Herunterladen
required = Obligatorisch
confirmation_to = an Empf\u00E4nger
DIRECT_DEBIT = Lastschrift
credit_memo = Gutschrift
invoice_reference_type_CREDIT_NOTE = Gutschrift
CASH = Bargeld
invoiceTotal = Gesamtbetrag inkl. Steuern
buyerPartyID = K\u00E4ufer-ID
standard_erep_edition_mode = Ausgabe
select_rte_refuse_label = Prozess nach Ablehnung
error_empty_invoice_item_gross_price = Bruttoeinheitspreis ist ein Pflichtfeld: {0}
buyerOrderNumber = Bestell-Nr.
lock_action_message = hat die Rechnung gesperrt
METERED_SERVICES_INVOICE = Verbrauchsabh\u00E4ngige Dienstleistungsrechnung
default_value = Standardwert
attachFile_action = Datei anh\u00E4ngen
control_modif_tvaintra_supplier = Die USt-IdNr. des Lieferanten wurde ge\u00E4ndert. Alter Wert: {0}. Neuer Wert: {1}
standard_erep_creation_mode = Erstellung
invoicing_data_sheet = Rechnungsdatenblatt
PartytoReceiveInvoiceFOrGoodsOrServices = Zahlbar an
confirm_refuse_doc_wkf = M\u00F6chten Sie dieses Dokument wirklich ablehnen?
lifeCycle = Lebenszyklus der Rechnung Nr.
ocr_validation_message_content = Bitte beachten Sie, dass nach dieser Validierung die Farbmarkierung der zu \u00FCberpr\u00FCfenden Felder dauerhaft entfernt wird. Stellen Sie sicher, dass Sie alle Felder \u00FCberpr\u00FCft haben.
add_comment = Kommentar hinzuf\u00FCgen
workflow_not_applicable_message = Workflow ohne Bezug
thirdParty = Drittperson
SELF_BILLED_CORRECTED_INVOICE = Berichtigte selbstfakturierte Rechnung
discount_charge_product_tab = Rabatt/Geb\u00FChr
day = Tage
rec_line_number = Zeilen-Nr.
canNotRefuse = Eine oder mehrere Rechnungen haben keine Ablehnungsaktion
triggerActions_errorInProgress_multi = Achtung: Achtung, es lief bereits eine Aktion f\u00FCr einige Rechnungen, diese wurden nicht bearbeitet.
description_correct_invoice = Gehen Sie zur Korrektur Ihres Fehlers wie folgt vor:
sovosTransactionId = Transaktions-ID
history_status = Status
editDetail = Zeile bearbeiten
tva_ident_incorrect = Ung\u00FCltige USt-IdNr.
rec_quantity = Menge
rec_receipt = Abweichung
attachment = Anh\u00E4nge
control_b2b2024_message_profile = Dieses Dokument wurde den geltenden Pr\u00FCfungen f\u00FCr eine B2B-Rechnung im Profil ({1}) (erste Phase der Reform) gem\u00E4\u00DF den Verwaltungsregeln ({0}) aus Anhang\u00A07 der externen B2B-Spezifikationen der franz\u00F6sischen Regierung unterzogen.
attatch_file_action_message_comment = hat eine Datei zur Rechnung mit folgendem Kommentar hinzugef\u00FCgt:
invoice_escompte_condition_placeholder = Skontobedingungen
triggerActions_noneSelected = Liste der zul\u00E4ssigen Status
quick_search_disable = Deaktiviert
error_asnNumber_double = Es ist nicht m\u00F6glich, eine ASN-Nummer sowohl in der Kopfzeile als auch in der Zeile einer Rechnung zu haben.
validate_comment = Best\u00E4tigen
confirmation_message_creation = Bitte best\u00E4tigen Sie den Versand Ihrer Rechnung
error_allwOrChrg_multi_edit = Es wird gerade ein Rabatt oder eine Steuer bearbeitet.
limit_address_and_product = Achtung! Es werden nur die ersten 100 Elemente angezeigt. Beginnen Sie mit der Eingabe, um die intelligente Suche zu nutzen\u00A0...
error_document_select_line = Es ist kein Dokument ausgew\u00E4hlt.
legal_form = Rechtsform
identifier_incorrect = Ung\u00FCltige Kennung
ART_262_TER = Artikel 262 ter-i des franz\u00F6sischen Steuergesetzbuchs (CGI)
strict = Streng
unit_number__value = Menge
multiple_process_execution = Mehrere Prozesse wurden ausgef\u00FChrt
rec_financial_gaps_tab = Finanzielle Abweichungen
triggerActions_execute_info = Die Aktion \u201E{0}\u201C wird auf Rechnung \u201E{1}\u201C angewendet
sovosNotificationId = Benachrichtigungs-ID
quick_search_warning_message = Achtung: Wenn viele Dokumente vorhanden sind, dauert die Suche lange, was die Portalleistung verlangsamen und zu einer hohen CPU-Auslastung f\u00FChren kann.
parafiscal_tax_amount = Steuerbetrag
unit_number_unit_of_measurement = Mengeneinheit pro Packung
ART_196 = Artikel 196 der USt.-Richtlinie
unlock_action_message = hat die Rechnung freigegeben
correct_field_error = Es gibt neue Felder zur Korrektur
relatedInvoiceNumber = Referenzrechnungsnummer
street_incorrect = Ung\u00FCltige Stra\u00DFe
replay_workflow = Workflow nach Bearbeitung erneut ausf\u00FChren
totalAmountPayable = Zu zahlender Betrag
control_modif_siren_client = Die SIREN-Nummer des Kunden wurde ge\u00E4ndert. Alter Wert: {0}. Neuer Wert: {1}
rec_differences_bottom_title = Abstimmungsabweichungen
massValidation = Massenhaft validieren
self_billed_invoice = Selbstausgestellte Rechnung
history_title = Audit
payment_title_tab = Zahlung
error_allwOrChrg_miss_description = Ein Rabatt oder eine Steuer muss eine Beschreibung haben.
diagnose = Diagnose durchf\u00FChren
debit_invoice = Belastungsrechnung
display_warning_params_only_in_angular = Achtung: Einige Parameter werden nur in Angular ber\u00FCcksichtigt.
relaunch_reconciliation_action = Abgleich neu starten
give_back_control_action_message = hat die \u00DCbergabe vorgenomm an
credit_note = Gutschrift
DEBIT_MEMO = Belastungsnote
triggerActions_limitWarning = Es k\u00F6nnen maximal 100 Rechnungen f\u00FCr diese Aktion ausgew\u00E4hlt werden. Bitte w\u00E4hlen Sie maximal 100 Rechnungen aus
non_autoComplete = Keine automatische Eingabe
detour_billing = Umleitungsabrechnung
sellerPartyID = Lieferanten-ID
invoiceCostAllocationNumber = Analytischer Buchungscode
rec_creditNote_number = Gutschrift Nr.
validate_action_message = hat die Rechnung validiert
invoice_commitment_number = Verpflichtungsnummer
ENC = USt. auf Einnahmen
gouv_ref_incorrect = Ung\u00FCltige SIREN
transferTask_action_message_comment = mit Folgendem Kommentar:
error_invoice_export_birt_pdf_missing = Keine BIRT-Vorlage f\u00FCr den PDF-Typ der Rechnung zugeordnet. Bitte wenden Sie sich an den Support.
refusedMessage = {0} Rechnung(en) erfolgreich abgelehnt
rec_invoice = Rechnung
confirm_edit = Best\u00E4tigen
control_eu_message = Dieses Dokument wurde den europ\u00E4ischen Pr\u00FCfungen unterzogen.
registered_name = Firmenname
rec_product_code = Produktcode
invoice_penalty_terms_default_value = Bei Nichteinhaltung der Frist k\u00F6nnen Verzugszinsen in H\u00F6he des dreifachen gesetzlichen Zinssatzes berechnet werden.
vat_breakdown_tax_amount = Umsatzsteuerbetrag
amount_ht = - Gesamtsumme ohne USt:
paymentStatus = Zahlungsstatus
ereportingOption = Option f\u00FCr E-Reporting
aap_manual_mode_message = Manuell erzwungene Verbuchung
description_diagnose_invoice = Die Rechnung wurde aus folgenden Gr\u00FCnden abgelehnt:
one_invoice_selected = Element ausgew\u00E4hlt
no_actions = Keine Aktien verf\u00FCgbar
PREPAYMENT_INVOICE_CREDIT_NOTE = Gutschrift auf eine Anzahlungsrechnung
bic = BIC
buyerAddressEmail = E-Mail des K\u00E4ufers
confirm_new_workflow = Diese Rechnung muss einem neuen Workflow zugewiesen werden. Alle Schritte des alten Workflows gehen verloren. M\u00F6chten Sie dies best\u00E4tigen?
error_empty_invoice_item_description = Beschreibung ist ein Pflichtfeld: {0}
control_b2g_message = Dieses Dokument wurde den geltenden Pr\u00FCfungen f\u00FCr eine B2G-Rechnung gem\u00E4\u00DF den Verwaltungsregeln ({0}) aus Anhang\u00A07 der externen B2B-Spezifikationen der franz\u00F6sischen Regierung unterzogen.
billToPartyOtherID = Abnehmer (andere Kennung)
actors_designation_message = hat {0} als Akteur(e) f\u00FCr Schritt {1} bestimmt
parafiscal_tax = Parafiskalische Steuer
rec_total_amount_including_tax = Gesamtbetrag inkl. Steuern
assignTask_dialog_title = Aufgabe zuweisen
comment_automatic_message = Automatische Nachricht: Ein Kommentar wurde hinzugef\u00FCgt:
processingWay = Richtung
aap_already_completed = Nicht \u00E4nderbare Verbuchung (bereits abgeschlossen)
earlyPaymentDiscountRate = Skontosatz
unlock_action = Entsperren
referenceInvoiceDate = Datum der Referenzrechnung
additional_info = Zus\u00E4tzliche Informationen
OTHER_LCRPAPIER = Papierbeleg Wechselinkasso
error_missing_template = Keine BIRT-Vorlage konfiguriert, bitte wenden Sie sich an Ihren Administrator.
rec_is_not_gap = Nicht
CREDIT_MEMO = Gutschrift
force_validation = Rechnung {0} erzwungen
billToAddressEmail = E-Mail des Rechnungsempf\u00E4ngers
OTHER_EFFETEMIS = Ausgestellter Wechsel
b2gRules = B2G-Regeln
invoice_portlet_selection = Portlet zum Hinzuf\u00FCgen von Rechnungen
all_invoices_selected = Alle Elemente der Liste sind ausgew\u00E4hlt
lock_action = Sperren
portlet = Portlet
SellersAgentOrRepresentative = Verk\u00E4uferagent
control_schematron_message = Die Schematron-Pr\u00FCfungen \u201E{0}\u201C wurden angewendet.
delcredere_invoice = Delkredere-Rechnung
cancel_assign_task_action = Anfrage stornieren
UNARCHIVABLE = Nicht archivierbar
manage_tax = USt.-Aufteilung verwalten
quick_search_settings = Einstellungen der Schnellsuche
SELF_BILLED_PREPAYMENT_INVOICE = Selbstfakturierte Anzahlungsrechnung
EXEMPT = Befreit
rec_name_provider = Name des Lieferanten:
confirm_delete_selected = M\u00F6chten Sie die ausgew\u00E4hlte(n) Rechnung(en) wirklich l\u00F6schen?
title_force_invoice = Rechnung Nr. erzwingen
return = Zur\u00FCck zur Liste
buyer_order = K\u00E4uferbestellung
postal_code_incorrect = Ung\u00FCltige Postleitzahl
chorus_warn_pro_unavailable = Die ChorusPro-Dienste sind derzeit nicht verf\u00FCgbar. Bitte versuchen Sie es sp\u00E4ter noch einmal.
mail = E-Mail
no_template = Keine
rec_descpription = Produktbeschreibung
rec_detected_deviation = Erkannte Abweichungen
triggerActions_emptyAction = <Unbestimmt>
comment_action_message = hat die Rechnung kommentiert:
error_invoice_item_quantity = Die Menge darf nicht 0 betragen.
triggerActions_execute_info_global = Die ''Aktion \u201E{0}\u201C wird auf die ausgew\u00E4hlten Rechnungen angewendet.
deleteDetail = Zeile l\u00F6schen
history_modified = Ge\u00E4ndert am
validate_message = Die Rechnungs-Nr. ist falsch
history_bill_to = Rechnungsempf\u00E4nger
error_document_locked = Ein anderer Benutzer hat die Rechnung gesperrt. Weitere Details sind der Zeitleiste zu entnehmen.
billToPartyID = Identifikator des Rechnungsempf\u00E4ngers
totalTaxAmount = USt.-Betrag
info_confirm_invoice = Rechnung Nr. {0} gesendet
free_unit_of_measurement = Ma\u00DFeinheit der kostenlosen Menge
city_incorrect = Ung\u00FCltige Stadt
buyerPartyOtherID = K\u00E4ufer (andere Kennung)
transferTask_checkboxes_message = Mindestens eine Aktion muss ausgew\u00E4hlt werden.
share_capital = Grundkapital
CREDIT_TRANSFER = Kredit\u00FCbertragung
autoComplete = Automatische Eingabe
asnDate = Lieferscheindatum
ocr_validation_message = Best\u00E4tigen
chorus_service = Dienst
error_const_allocation_not_filed = Die analytischen Codes in den Details sind Pflichtfelder.
nature = Art
INVOICING_DATA_SHEET = Rechnungsdatenblatt
NIF = Steueridentifikationsnummer
display_warning_sellerPartyID = Achtung: Das Feld \u201EKennung\u201C kann nur f\u00FCr Rechnungen bearbeitet werden, die aus einem OCR-Prozess stammen (Erfassung = \u201EOCR\u201C).
invoiceDueDate = F\u00E4lligkeitsdatum
addDetail = Zeile hinzuf\u00FCgen
rec_unit_price = St\u00FCckpreis
taxAccountingCurrencyCoded = Zahlungsw\u00E4hrung f\u00FCr die USt. (Code)
parties_info = Parteien
referenceInvoiceNumber = Referenzrechnungsnummer
ok_btn = OK
freeText09 = Freitext 09
freeText08 = Freitext 08
freeText07 = Freitext 07
link_to_referential_tooltip = Die Informationen werden aus dem Adressverzeichnis abgerufen, au\u00DFer im Fall der Selbstfakturierung, wo sie aus dem Kontaktbereich stammen.
manageAllowOrCharge = Rabatte und Zuschl\u00E4ge verwalten
confirmation_vat = USt:
legal_action_payment_received = Die Zahlung der Rechnung ist eingegangen.
freeText17 = Freitext 17
invoiceTypeCoded = Rechnungstyp
freeText16 = Freitext 16
freeText15 = Freitext 15
freeText14 = Freitext 14
freeText13 = Freitext 13
freeText12 = Freitext 12
freeText11 = Freitext 11
freeText10 = Freitext 10
transferTask_action_message = hat eine Aufgabe zugewiesen an
name_incorrect = Ung\u00FCltiger Name
freeText19 = Freitext 19
freeText18 = Freitext 18
new_bql_filter_on_role = Achtung: F\u00FCr den Benutzer und/oder seine zugeh\u00F6rige Rolle kann ein BQL-Filter existieren, der zus\u00E4tzlich zu diesem Filter angewendet wird.
tooltip_default_value = Standardzahlungsfrist auf heute + X Tage setzen
freeText20 = Freitext 20
comment_dialog_title = Kommentar hinzuf\u00FCgen
early_payment_seller_accept_add_timeline = : hat das Angebot zur vorzeitigen Zahlung angenommen. Die Rechnung wird {0} Tage fr\u00FCher bezahlt, im Gegenzug wird ein Skonto von {1}\u00A0% gew\u00E4hrt, das {2}\u00A0\u20AC entspricht.
rec_is_gap = Ja
rec_total_amount_excluding_tax = Gesamtbetrag ohne USt
freeText28 = Freitext 28
freeText27 = Freitext 27
reconciliation_not_applicable_message = Abgleich nicht relevant
freeText26 = Freitext 26
freeText25 = Freitext 25
freeText24 = Freitext 24
freeText23 = Freitext 23
freeText22 = Freitext 22
freeText21 = Freitext 21
warning_partially_editable = Achtung: Die Sichtbarkeit der Spalte creationDate kann nicht ge\u00E4ndert werden.
quick_search_full_text_option = Enth\u00E4lt das Wort
reconciliation_empty_order_number_message = Abgleich nicht m\u00F6glich: Bestell-Nr. fehlt.
discount_terms_incorrect = Falsche Skontobedingungen
freeText29 = Freitext 29
process_error = Fehler im Prozess
error_alloworchrg_greater_price_item = Die Gesamtsumme der Rabatte und Zuschl\u00E4ge darf den Produktpreis nicht \u00FCbersteigen.
recordNumber_incorrect = RCS-RCM ung\u00FCltig
freeText31 = Freitext 31
freeText30 = Freitext 30
control_b2g2024_message_profile = Dieses Dokument wurde den geltenden Pr\u00FCfungen f\u00FCr eine B2G-Rechnung im Profil ({1}) (erste Phase der Reform) gem\u00E4\u00DF den Verwaltungsregeln ({0}) aus Anhang\u00A07 der externen B2B-Spezifikationen der franz\u00F6sischen Regierung unterzogen.
open_in_new_tab = In einem neuen Tab \u00F6ffnen
footer_customer = Fu\u00DF
error_in_processus = Interner Fehler, Rechnung nicht \u00FCberpr\u00FCft.
rec_ordered = Bestellung
freeText35 = Freitext 35
modifiable = Bearbeiten
freeText34 = Freitext 34
tip_two_correct_invoice = Klicken Sie auf die Schaltfl\u00E4che \u201EKorrigieren\u201C, um die Korrekturen anzuwenden und die Rechnung einzureichen.
freeText33 = Freitext 33
freeText32 = Freitext 32
reconciliation_gap_found_message = Abgleich abgeschlossen, Abweichungen festgestellt.
invoice_market_number = Vergabenummer
amount_ttc = - Gesamtsumme inkl. USt:
control_b2g_message_profile = Dieses Dokument wurde den geltenden Pr\u00FCfungen f\u00FCr eine B2G-Rechnung im Profil ({1}) gem\u00E4\u00DF den Verwaltungsregeln ({0}) aus Anhang\u00A07 der externen B2B-Spezifikationen der franz\u00F6sischen Regierung unterzogen.
relatedInvoiceDate = Datum der Referenzrechnung
useCase = Anwendungsf\u00E4lle
BANK_ACCOUNT = Zahlung auf ein Bankkonto
THIRD_PARTY_CONSOLIDATED_INVOICE = Konsolidierte Rechnung eines Drittanbieters
parsing_error = Fehler bei der Dateianalyse
count_enabled = Gesamtzahl der Rechnungen anzeigen
aap_completed_automatically = Automatisch abgeschlossen Verbuchung
manageActualPayment = Anzahlungen verwalten
BANK_CARD = Bankkarte
invoiceCurrencyCoded = W\u00E4hrung
shipToPartyName = Name des Empf\u00E4ngers
forced = Erzwingen
project = Projekt
triggerActions_complete_info = Die Aktion \u201E{0}\u201C wurde f\u00FCr die Rechnung \u201E{1}\u201C ausgef\u00FChrt.
consult_message = Das Dokument wurde eingesehen.
confirmation_siren = SIREN:
history_system = System
amount_tva = - Gesamtsumme USt:
penalty_terms = Bedingungen f\u00FCr Strafgeb\u00FChren
massRefusal = Massenhaft ablehnen
triggerActions_actionName_placeholder = Name der Aktion
confirm_file_readable_message = Es existiert bereits eine lesbare Version dieser Rechnung. M\u00F6chten Sie sie ersetzen?
triggerActions_complete_info_multi = Die Aktion \u201E{0}\u201C wurde f\u00FCr alle ausgew\u00E4hlten Rechnungen ausgef\u00FChrt.
control_b2b_message = Dieses Dokument wurde den geltenden Pr\u00FCfungen f\u00FCr eine B2B-Rechnung gem\u00E4\u00DF den Verwaltungsregeln ({0}) aus Anhang\u00A07 der externen B2B-Spezifikationen der franz\u00F6sischen Regierung unterzogen.
PROMISSORY_NOTE = Eigenwechsel
label_required = Dies ist ein Pflichtfeld.
freeText06 = Freitext 06
freeText05 = Freitext 05
comment = Kommentar
freeText04 = Freitext 04
freeText03 = Freitext 03
freeText02 = Freitext 02
freeText01 = Freitext 01
invoice_escompte_condition_default_value = Kein Skonto bei Vorauszahlung
reconciliation_empty_seller_party_message = Abgleich nicht m\u00F6glich: Lieferantencode fehlt.
triggerActions_notAllDocumentsPresent = Es gibt Dokumente, die nicht verarbeitet werden k\u00F6nnen. Bitte wenden Sie sich an Ihren Administrator.
title_diagnose_invoice = Die Gr\u00FCnde f\u00FCr die Ablehnung Ihrer Rechnung\u00A0Nr. anzeigen
billToPartyTaxIdentifier = USt.-Code des Rechnungsempf\u00E4ngers
error_invoice_not_attachment = Dieser Rechnung sind keine Anh\u00E4nge beigef\u00FCgt.
chorus_structure_placeholder = Struktur anhand von Kennung oder Bezeichnung suchen\u00A0...
aap_missing_rcp_message = Verbuchung nicht m\u00F6glich: Empfang nicht gefunden
seller_order = Verk\u00E4uferbestellung
contract = Vertrag
description_force_invoice = Geben Sie zum Erzwingen der Rechnung bitte den Grund an:
addData_action = Erweitern
quick_search_case_sensitive = Gro\u00DF-/Kleinschreibung beachten
rec_order_number = Bestell-Nr.
invoicePreparationDateTime = Vorbereitungsdatum
iban = IBAN
CHECK = Scheck
force = Erzwingen
fix_action_message = hat die Rechnung korrigiert
add_parafiscal_tax = Parafiskalische Steuer hinzuf\u00FCgen
vat_breakdown_rate = Satz
link_to_referential = Mit dem Adressverzeichnis verlinken
standard_inv_visualization_mode = Portlet
archiveExpirationDate = Ablaufdatum des Archivs
rossum_fail = Studio Rossum kann nicht ge\u00F6ffnet werden: {0}
link_to_address_referential = Mit dem Adressverzeichnis verkn\u00FCpfen
chorus_structureId = ID
COMMISSION_NOTE = Kommissionsnotiz
otherPartyTaxIdentifier = USt.-IdNr (anderer Beteiligter)
confirm_refuse_doc_wkf_title = Ablehnung best\u00E4tigen
history_invoice_number = Rechnung Nr.
standard_invoice_edition_status_list = Liste der Status, die das Bearbeiten erlauben
title_correct_invoice = Ihre Rechnung Nr. korrigieren
SELF_BILLED_CORRECTED_FACTORED_INVOICE = Berichtigte selbstfakturierte Factoring-Rechnung
shipToName = Name des Empf\u00E4ngers
CLEARING_BETWEEN_PARTNERS = Verrechnung zwischen Partnern
rec_invoice_number = Rechnung Nr.
link_to_contact_scope = Mit dem Kontaktbereich verkn\u00FCpfen
commission_note = Kommissionsnotiz
reinit_action_message = Automatische Nachricht: Der Workflow {0} wurde durch den Workflow {1} ersetzt.
due_date_calculation = Berechnung des F\u00E4lligkeitsdatums
refuse_action = Ablehnen
invoice_exo_tva_default_value = USt.-Befreiung, Art. 262 ter-I des franz\u00F6sischen Steuergesetzbuchs
only_workflow_invoices = Nur Rechnungen anzeigen, die eine Workflow-Aktion erfordern
reconciliation_empty_reception_number_message = Abgleich nicht m\u00F6glich: Empfangs-Nr. fehlt.
ocr_verify = Videocoder
Payer = Rechnungszahler
SELF_BILLED_FACTORED_INVOICE = Selbstfakturierte Factoring-Rechnung
rec_no_product_identifier = Keine Produkt-ID gefunden
SELF_BILLED_INVOICE = Selbstausgestellte Rechnung
invoice_penalty_condition_default_value = Verzugszinsen: (BITTE SATZ ANGEBEN) % pro Monat Verzug gem\u00E4\u00DF Gesetz 92-1442 vom 31.12.92
history_by = Durch
parafiscal_tax_code = Steuercode
refuse_invoice_title = Rechnung ablehnen
rec_tolerance = Toleranz
validator_date_range = Zeitraum zwischen 0 und 90\u00A0Tagen festlegen
daysPayedEarly = Im Voraus bezahlte Tage
aap_started_message = Beginn der Verbuchung
creation_edition_change_message = Achtung: Diese \u00C4nderung wird nicht empfohlen, da eine \u00C4nderung der Nutzung dieser PT zu Problemen f\u00FChren kann, wenn sie in anderen PTs (Rechnung, ASN, Bestellungen\u00A0...) aufgerufen wird.
sellerPartyTaxIdentifier = USt.-ID des Lieferanten
error_existing_vat_rate = Es k\u00F6nnen nicht mehrere Zeilen f\u00FCr die Aufteilung der USt. mit einem Satz von {0}\u00A0% geben.
triggerActions_generalError = Bei der Ausf\u00FChrung der Aktion ist ein Fehler aufgetreten. Bitte wenden Sie sich an Ihren Administrator.
error_invoice_export_birt_xls_missing = Keine BIRT-Vorlage f\u00FCr den Excel-Typ der Rechnung zugeordnet. Bitte wenden Sie sich an den Support.
correct_validation = Die Korrektur wurde erfolgreich durchgef\u00FChrt
billToPartyName = Fakturiert
attachment_name = Name des Anhangs
total_net_chargeallowance = Nettobetrag
member_single_subject = Mitglied einer einzigen steuerpflichtigen Einheit
rec_comment = Kommentar
control_b2b_message_profile = Dieses Dokument wurde den geltenden Pr\u00FCfungen f\u00FCr eine B2B-Rechnung im Profil ({1}) gem\u00E4\u00DF den Verwaltungsregeln ({0}) aus Anhang\u00A07 der externen B2B-Spezifikationen der franz\u00F6sischen Regierung unterzogen.
FACTORED_CREDIT_NOTE = Factoring-Gutschrift
invoice_modification_status_label = Statusaktualisierung nach abgeschlossener Validierung
IN_SUBMISSION = Auszahlung l\u00E4uft
history_buyer = K\u00E4ufer
rec_other_gaps_tab = Sonstige Abweichungen
DEB = USt. auf Sollbuchungen
MILKING = Wechsel vom Gl\u00E4ubiger auf den Schuldner
sovosStatusCode = Statuscode
reminder_action_automatic_message = Automatische Nachricht: Erinnerung Nr. {0} wurde per E-Mail versendet.
triggerActions_statusError = Die angeforderte Aktion ist f\u00FCr eine Rechnung mit Status \u201E{0}\u201C nicht zul\u00E4ssig.
relaunch_reconciliation_message = Abgleich neu gestartet
attatch_file_action_message = hat eine Datei zur Rechnung hinzugef\u00FCgt
invoice_compliance_portlet = Portlet zur Einhaltung der Rechnungsrichtlinien
otherPartyName = Name (anderer Beteiligter)
reconciliation_not_found = Kein Abgleich gefunden.
invoiceHeader_invoiceReferences[0]_asnNumber_refNum = Lieferschein-Nr.
invoiceHeader_invoiceReferences[0]_purchaseOrderReference_purchaseOrderDate = Bestelldatum
security_field_index_warning = Bitte beachten Sie, dass bei einer \u00C4nderung dieser Parameter eine Warnung an den BE_BDD gesendet werden muss, um die entsprechenden Indizes festzulegen. Es k\u00F6nnen maximal 3 Felder ausgew\u00E4hlt werden.
invoiceHeader_invoiceReferences[0]_purchaseOrderReference_buyerOrderNumber = Bestellnummer
invoice_cost_allocation_number = Kostenzuordnungsnummer
profile = Profil
ocr_verify_action_message = f\u00FChrte die OCR-\u00DCberpr\u00FCfungen durch.
invoiceHeader_invoiceReferences[0]_asnNumber_refDate = Lieferscheindatum
min_security_fields_selected_error_message = Es ist zwingend erforderlich, mindestens einen Rechnungssteller auszuw\u00E4hlen.
wkf_exit_action_message = Wokflow-Ausgang
invoiceHeader_invoiceParty_shipToParty_partyID_ident = Kennung des Empf\u00E4ngers
max_security_fields_selected_error_message = Aus Performancegr\u00FCnden k\u00F6nnen maximal 3 Felder ausgew\u00E4hlt werden.
security_fields_list = Sicherheit der Liste auf Felder basieren