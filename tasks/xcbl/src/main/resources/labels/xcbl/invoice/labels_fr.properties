## fr - Task invoice

# Config : portal task
archiveStatus=Statut d'archivage
buyerPartyID=Identifiant de l'acheteur
buyerPartyName=Acheteur
buyerPartyTaxIdentifier=Code TVA de l'acheteur
buyerPartyCountryCoded=Pays de l'acheteur
buyerOrderNumber=N\u00B0 de commande
buyerPartyOtherID=Acheteur (autre identifiant)
purchaseOrderDate=Date de la commande
sellerPartyID=Identifiant du fournisseur
sellerPartyName=Fournisseur
sellerPartyTaxIdentifier=Code TVA du fournisseur
sellerPartyCountryCoded=Pays du fournisseur
sellerPartyOtherID=Vendeur (autre identifiant)
billToPartyID=Identifiant du factur\u00E9
billToPartyName=Factur\u00E9
billToPartyTaxIdentifier=Code TVA du factur\u00E9
billToPartyCountryCoded=Pays du factur\u00E9
billToPartyOtherID=Preneur (autre identifiant)
otherPartyRole=R\u00F4le (autre intervenant)
otherPartyID=ID (autre intervenant)
otherPartyName=Nom (autre intervenant)
otherPartyCountryCoded=Code pays (autre intervenant)
otherPartyTaxIdentifier=Code TVA intracommunautaire (autre intervenant)
otherPartyOtherID=Autre intervenant (autre identifiant)
contractID=Contrat (identifiant)
invoiceNumber=Facture n\u00B0
invoiceIssueDate=Date de la facture
invoiceDueDate=Date limite de paiement
invoiceCurrencyCoded=Devise
taxAccountingCurrencyCoded=Devise de paiement de la TVA (code)
taxableValueInTaxAccountingCurrency=Montant taxable (en devise de paiement de la TVA)
invoiceTypeCoded=Type de facture
invoiceTotal=Total T.T.C.
taxableValue=Total H.T.
totalTaxAmount=Montant TVA
totalAmountPayable=Montant \u00E0 payer
shipToPartyID=Identifiant livr\u00E9 \u00E0
asnNumber=N\u00B0 BL
asnDate=Date BL
customerReferenceNumber=R\u00E9f\u00E9rence client
receptionNumber=N\u00B0 de r\u00E9ception
relatedInvoiceNumber=Num\u00E9ro de facture de r\u00E9f\u00E9rence
relatedInvoiceDate=Date de facture de r\u00E9f\u00E9rence
invoiceProcessDateTime=Date de d\u00E9materialisation
invoicePreparationDateTime=Date de pr\u00E9paration
from=Emetteur
to=Destinataire
owners=Propri\u00E9taire
thirdParty=Tierce personne
archiveExpirationDate=Date d'expiration de l'archive
rossumId=Id Rossum
type=Norme
status=Statut
stage=Statut l\u00E9gal
duplicate_invoice_number=Unicit\u00E9 du n\u00B0 de facture
option=Option
count_enabled=Afficher le nombre total de facture
invoice_columns_details=Colonnes du d\u00E9tail de la facture
invoice_compliance_portlet=Invoice compliance portlet
portlet=Portlet
enable_safebox_search=Dans le coffre fort
freeText01=Texte libre 01
freeText02=Texte libre 02
freeText03=Texte libre 03
freeText04=Texte libre 04
freeText05=Texte libre 05
freeText06=Texte libre 06
freeText07=Texte libre 07
freeText08=Texte libre 08
freeText09=Texte libre 09
freeText10=Texte libre 10
freeText11=Texte libre 11
freeText12=Texte libre 12
freeText13=Texte libre 13
freeText14=Texte libre 14
freeText15=Texte libre 15
freeText16=Texte libre 16
freeText17=Texte libre 17
freeText18=Texte libre 18
freeText19=Texte libre 19
freeText20=Texte libre 20
freeText21=Texte libre 21
freeText22=Texte libre 22
freeText23=Texte libre 23
freeText24=Texte libre 24
freeText25=Texte libre 25
freeText26=Texte libre 26
freeText27=Texte libre 27
freeText28=Texte libre 28
freeText29=Texte libre 29
freeText30=Texte libre 30
freeText31=Texte libre 31
freeText32=Texte libre 32
freeText33=Texte libre 33
freeText34=Texte libre 34
freeText35=Texte libre 35
orderingParty=Donneur d'ordre
consultStatus=Etat de consultation
processingWay=Sens
paymentStatus=Statut de paiement
daysPayedEarly=Jours pay\u00E9s en avance (JPA)
earlyPaymentAnnualDiscountRate=Taux annuel (%)
earlyPaymentDiscountRate=Taux d'escompte
earlyPaymentAmountDiscount=Escompte H.T.
day=jours
shipToPartyName=Nom livr\u00E9 \u00E0
shipToName=Nom livr\u00E9 \u00E0
sellerOrderNumber=N\u00B0 commande vendeur
referenceInvoice=N\u00B0 facture r\u00E9f\u00E9rence
referenceInvoiceNumber=N\u00B0 de facture de r\u00E9f\u00E9rence
referenceInvoiceDate= Date de facture de r\u00E9f\u00E9rence
profile=Profil

buyerAddressEmail=Email acheteur
sellerAddressEmail=Email fournisseur
billToAddressEmail=Email factur\u00E9 \u00E0
shipToAddressEmail=Email livr\u00E9 \u00E0

sovosId= Id sovos
nature=Nature
immatpdp=PDP
b2gRules=R\u00E8gles B2G
ereportingOption=Option e-reporting
useCase=Cas d'usage
billingFramework=Cadre de facturation
sovosStatus=Statut sovos
sovosTransactionId=Transaction Id
sovosStatusCode=Code statut
sovosNotificationId=Id de notification

new_bql_filter_on_role = Attention : un filtre BQL peut exister sur l'utilisateur et/ou son r\u00F4le associ\u00E9 et sera appliqu\u00E9 en plus de ce filtre
security_field_index_warning = Attention, la modification de ces param\u00E8tres n\u00E9cessite d'avertir le BE_BDD afin de poser les index correspondants. Il n'est possible de s\u00E9lectionner que 3 champs au maximum.
only_workflow_invoices = N'afficher que les factures n\u00E9cessitant une action de workflow

# Edit : Invoice
header_info=Information sur la facture
invoice_number=N\u00B0 de facture
validate_message=Le n\u00B0 de la facture est incorrect
inv_type=Type
other=Autre
invoice_currency=Devise
issue_date=Date
order_ref_num=R\u00E9f\u00E9rence de la commande
order_type=Type de la commande
order_date=Date de la commande
asn_ref_num=R\u00E9f\u00E9rence de l'avis d'exp\u00E9dition
asn_ref_date=Date de l'avis d'exp\u00E9dition
invoice_dates=Dates de la facture
due_date=Date d'\u00E9ch\u00E9ance
due_date_calculation=Calcul de la date d'\u00E9ch\u00E9ance
actual_ship_date=Date d'exp\u00E9dition
actual_delivery_date=Date de livraison
preparation_date=Date de pr\u00E9paration
date_of_operation=Date d'op\u00E9ration
invoicing_start_date=Date de d\u00E9but de validit\u00E9
invoicing_end_date=Date de fin de validit\u00E9
invoice_party_header=Entit\u00E9s de la facture
invoice_billing_framework=Cadre de facturation
invoice_market_number=Num\u00E9ro de march\u00E9
invoiceCostAllocationNumber=Code d'imputation analytique
invoice_cost_allocation_number_control=Contr\u00F4le de pr\u00E9sence du code Analytique
invoice_VAT_type=Type de TVA
invoice_payment_choice=Mode de r\u00E8glement
invoice_commitment_number=Num\u00E9ro d'engagement
ShipTo=Livr\u00E9 \u00E0
ShipFrom=Exp\u00E9diteur
BillTo=Factur\u00E9 \u00E0
Buyer=Acheteur
Seller=Fournisseur
RemitTo=B\u00E9n\u00E9ficiaire
other_party_info=Autres entit\u00E9s
PartytoReceiveInvoiceFOrGoodsOrServices=R\u00E9gler \u00E0
BuyersAgentOrRepresentative=Agent d'acheteur
SellersAgentOrRepresentative=Agent de vendeur
Payer=Payeur de la facture
identifier=Identifiant
identifier_incorrect=Identifiant incorrect
copy_bill_to_information=Copier les informations du Factur\u00E9 \u00E0
limit_address_and_product=Attention ! Seuls les 100 premiers \u00E9l\u00E9ments seront affich\u00E9s. Commencez votre saisie pour b\u00E9n\u00E9ficier de la recherche intelligente ...
name=Nom
name_incorrect=Nom incorrect
contactName=Nom du contact
mail=Email
mail_not_valid=Email non valide
phone=T\u00E9l\u00E9phone
street=Rue
street_incorrect=Rue incorrecte
street_supplement=Compl\u00E9ment d'adresse
street_supplement_incorrect=Compl\u00E9ment d'adresse incorrecte
postal_code=Code postal
postal_code_incorrect=Code postal incorrect
city=Ville
city_incorrect=Ville incorrecte
country=Pays
country_subdivision=Subdivision du pays
contact=Contact
tel=Tel
fax=Fax
recordNumber=RCS-RCM
recordNumber_incorrect=RCS-RCM incorrect
gouv_ref=SIREN
gouv_ref_incorrect=SIREN incorrect
gouv_hashcode=Hahscode gouvernemental
NIF = Num\u00E9ro d'identification fiscale
bic=BIC
iban=IBAN
tva_ident=N\u00B0 TVA intracom.
tva_ident_incorrect=N\u00B0 TVA intracommunautaire incorrect
registered_name=Raison sociale
routing_code=Code routage
addressing_identifier=Identifiant d'adressage
legal_form=Forme juridique
share_capital=Capital social
member_single_subject=Membre d'un assujetti unique
party_role_coded=R\u00F4le de l'entit\u00E9
allowance_or_charge=Remises et charges d'ent\u00EAtes globales
line_allowance_or_charge=Remises et charges \u00E0 la ligne
ref_allow_or_charge_tax=Remises et charges du r\u00E9f\u00E9rentiel
indicator_coded=Indicateur
description=Description
percent=Pourcentage
monetary_amount=Montant
payment_instructions=Instructions de paiement
net_due_date=Date limite de paiement
payment_terms_note=Conditions de p\u00E9nalit\u00E9s
payment_terms_note_incorrect=Conditions de p\u00E9nalit\u00E9s incorrectes
discount_terms=Conditions d'escompte
discount_terms_incorrect=Conditions d'escompte incorrectes
late_payment=Indemnit\u00E9 de retard
penalty_terms=Conditions de p\u00E9nalit\u00E9s
tax_exempt=Exemption de la TVA
reason_tax_exempt_coded_other=Raison de l'exemption
header_note=Forme juridique et capital social du fournisseur
details=D\u00E9tails
discount_charge_product_tab=Remise/Charge
general_product_tab=G\u00E9n\u00E9ral
buyer_part_number=Code produit acheteur
seller_part_number=Code produit fournisseur
product_identifier=Code EAN
product_identifier_ext=Variante promotionnelle
item_description=Description
quantity_value=Qt\u00E9 fact.
itemLineNumber=N\u00B0 de ligne
free_quantity_value=Qt\u00E9 gratuite
free_unit_of_measurement=Unit\u00E9 de mesure de la quantit\u00E9 gratuite
unit_number__value=Quantit\u00E9
unit_number_unit_of_measurement=Unit\u00E9 de mesure de la quantit\u00E9 par pack
unit_net_price=Prix unitaire net
gross_price=Prix unitaire brut
gross_amount=Montant total
vat_code=Code TVA
total_net=Montant net H.T
total_ttc=Total T.T.C
unit_of_measurement=Unit\u00E9
tax=Taux TVA
item_tax_amount=Montant de TVA
summary=Pied de la facture
list_actual_payment=Acomptes
payment_amount=Montant acompte
ref_num=Num\u00E9ro de l'acompte
payment_date=Date de l'acompte
list_tax_summary=Ventilation TVA
vat_breakdown=TVA ventil\u00E9
vat_breakdown_rate=Taux
vat_breakdown_taxable_amount=Montant taxable
vat_breakdown_tax_amount=Montant de TVA
tax_category=TVA ventil\u00E9: taux
taxable_amount=TVA ventil\u00E9: montant taxable
tax_amounty=TVA ventil\u00E9: montant de TVA
manage_tax=G\u00E9rer la ventilation de TVA
taxable_value=Total HT
total_tax_amount=Total T.V.A
vat_amount=Montant T.V.A
invoice_total=Total T.T.C
amount_to_pay=Montant \u00E0 payer
parafiscal_tax_breakdown=Ventilation de taxe parafiscale
parafiscal_tax=Taxe parafiscale
parafiscal_tax_code=Code de la taxe
parafiscal_tax_amount=Montant taxe
manage_parafiscal_tax=G\u00E9rer la synth\u00E8se des taxes parafiscales
value=Valeur
confirm_invoice=Confirmer
add_entity=Ajouter une entit\u00E9
add_alloworchrg=Ajouter une remise ou charge
add_allowance=Remise
add_charge=Charge
remove_alloworchrg=Supprimer la remise ou charge
edit_product=Editer la ligne de facture
remove_product=Supprimer ligne facture
total_net_amount=Montant total net
invoicing_credit=Dans le cas d'un avoir ou d'une facture de service
invoice_references=R\u00E9f\u00E9rences
add_invoice_reference=Ajouter une r\u00E9f\u00E9rence
remove_invoice_reference=Supprimer la r\u00E9f\u00E9rence
invoice_reference_type=Type de r\u00E9f\u00E9rence
invoice_reference_type_CONTRACT=Contrat
invoice_reference_type_ORDER=Commande
invoice_reference_type_ORDER_RESPONSE=R\u00E9ponse \u00E0 la Commande
invoice_reference_type_DISPATCH_ADVICE=Avis d'exp\u00E9dition
invoice_reference_type_INVOICE=Facture
invoice_reference_type_DEBIT_NOTE=Note de d\u00E9bit
invoice_reference_type_CREDIT_NOTE=Note de cr\u00E9dit
buyer_order=Commande acheteur
seller_order=Commande vendeur
order_response=R\u00E9ponse \u00E0 la commande
dispatch_advice=Avis d'exp\u00E9dition
debit_note=Note de d\u00E9bit
credit_note=Note de cr\u00E9dit
contract=Contrat
project=Projet
invoice_reference_num=Num\u00E9ro de r\u00E9f\u00E9rence
invoice_reference_date=Date de la r\u00E9f\u00E9rence
invoice_reference_period_start_date=D\u00E9but de p\u00E9riode
invoice_reference_period_end_date=Fin de p\u00E9riode
invoice_reference_date_or_period_required=La date ou la p\u00E9riode doivent \u00EAtre renseign\u00E9es
invoice_reference_period_coherence=La date de fin de p\u00E9riode doit \u00EAtre post\u00E9rieure \u00E0 la date de d\u00E9but
invoice_reference_date_only=La date et la p\u00E9riode ne peuvent \u00EAtre renseign\u00E9es en m\u00EAme temps
invoice_reference_period_required=La p\u00E9riode est obligatoire pour une r\u00E9f\u00E9rence de ce type
invoice_reference_date_required=La date est obligatoire pour une r\u00E9f\u00E9rence de ce type
detail_precision=Nombre de d\u00E9cimal prix et montant niveau ligne
footer_precision=Nombre de d\u00E9cimal prix et montant pied facture
footer=Pied de page
parties_tabs_fieldset=Editer
cache_pdf=Gestion cache PDF
print_fieldset=Imprimer
autoComplete=Saisie automatique
non_autoComplete=Aucune saisie automatique

#Combo
new=Nouveau
HeadOffice=Siege social du fournisseur
DeclarantsAgentOrRepresentative=Repr\u00E9sentant fiscal
Factor=Affactureur
ALLOWANCE=Remise
LINE_ITEM_ALLOWANCE=Remise
CHARGE=Charge
LINE_ITEM_CHARGE=Charge
SERVICE=Taxe parafiscale
LINE_ITEM_TAX_REGULATORY_TAX=Taxe parafiscale
PERCENT=Pourcentage
MONETARY_AMOUNT=Montant
METERED_SERVICES_INVOICE=Facture de services au compteur
CREDIT_NOTE_FINANCIAL_ADJUSTMENT=Note de cr\u00E9dit li\u00E9e aux ajustements financiers
DEBIT_NOTE_FINANCIAL_ADJUSTMENT=Note de d\u00E9bit li\u00E9e \u00E0 l'ajustement financier
INVOICING_DATA_SHEET=Fiche de donn\u00E9es de facturation
PROFORMA_INVOICE=Facture proforma
COMMERCIAL_INVOICE=Facture
CREDIT_NOTE_GOODS_AND_SERVICES=Avoir
DEBIT_NOTE_GOODS_AND_SERVICES=Note de d\u00E9bit - Biens et services
COMMISSION_NOTE=Note de commission
CORRECTED_INVOICE=Facture rectificative
CONSOLIDATED_INVOICE=Facture consolid\u00E9e
PREPAYMENT_INVOICE=Facture d'acompte
SELF_BILLED_INVOICE=Facture auto-factur\u00E9e
DELCREDERE_INVOICE=Facture Delcredere
FACTORED_INVOICE=Facture affactur\u00E9e
CREDIT_INVOICE=Facture de cr\u00E9dit
CREDIT_MEMO=Note de cr\u00E9dit
DETOUR_BILLING=Facturation de d\u00E9tour
THIRD_PARTY_CONSOLIDATED_INVOICE=Facture consolid\u00E9e tierce
DEBIT_INVOICE=Facture de d\u00E9bit
DEBIT_MEMO=Note de d\u00E9bit
CORRECTED_FACTORED_INVOICE=Facture rectificative affactur\u00E9e
FACTORED_CREDIT_NOTE=Avoir affactur\u00E9
PREPAYMENT_INVOICE_CREDIT_NOTE=Avoir de facture d'acompte
SELF_BILLED_FACTORED_INVOICE=Facture auto-factur\u00E9e affactur\u00E9e
SELF_BILLED_PREPAYMENT_INVOICE=Facture d'acompte auto-factur\u00E9e
SELF_BILLED_CORRECTED_INVOICE=Facture rectificative auto-factur\u00E9e
SELF_BILLED_CORRECTED_FACTORED_INVOICE=Facture rectificative auto-factur\u00E9e affactur\u00E9e
SELF_BILLED_CREDIT_NOTE=Avoir auto-factur\u00E9
SELF_BILLED_FACTORED_CREDIT_NOTE=Avoir auto-factur\u00E9 affactur\u00E9
metered_services_invoice=Facture de services au compteur
credit_note_financial_adjustment=Note de cr\u00E9dit li\u00E9e aux ajustements financiers
debit_note_financial_adjustment=Note de d\u00E9bit li\u00E9e \u00E0 l'ajustement financier
invoicing_data_sheet=Fiche de donn\u00E9es de facturation
proforma_invoice=Facture proforma
commercial_invoice=Facture
credit_note_goods_and_services=Avoir
debit_note_goods_and_services=Note de d\u00E9bit - Biens et services
commission_note=Note de commission
corrected_invoice=Facture corrig\u00E9e
consolidated_invoice=Facture consolid\u00E9e
prepayment_invoice=Facture de pr\u00E9paiement
self_billed_invoice=Facture auto-factur\u00E9e
delcredere_invoice=Facture Delcredere
factored_invoice=Facture affactur\u00E9e
credit_invoice=Facture de cr\u00E9dit
credit_memo=Note de cr\u00E9dit
detour_billing=Facturation de d\u00E9tour
third_party_consolidated_invoice=Facture consolid\u00E9e tierce
debit_invoice=Facture de d\u00E9bit
debit_memo=Note de d\u00E9bit

# Edit : Product
add_product=Ajouter un produit
product_from_repository=Ajouter ligne facture

# View : portal task
open=Ouvrir
files=Visualiser
tax_original=Original fiscal
demat_errors=Erreurs de d\u00E9mat\u00E9rialisation
all_attached_files=Toutes les pi\u00E8ces attach\u00E9es
EDI=Interchange
EDIUNIT=Fichier EDI
ERROR=Erreur
XML=XML
actions=Actions sur la facture
no_actions=Pas d'actions disponible
legend_invoice=Factur\u00E9
legend_refused=refus\u00E9
legend_accepted=accept\u00E9
invoice=Facturer
info_confirm_action=Votre facture a \u00E9t\u00E9 g\u00E9n\u00E9r\u00E9e avec succ\u00E8s
info_confirm_invoice=Facture n\u00B0 {0} envoy\u00E9e
info_confirm_before_leaving=Si vous avez effectu\u00E9 des modifications sans avoir confirm\u00E9, vos changements seront perdus.
info_lengthy_operation=Op\u00E9ration potentiellement longue.
invoice_add_attachment=Attacher une pi\u00E8ce

# Advance search
select=S\u00E9lectionner

# Error
error_duplicate_invoice_number=Le num\u00E9ro de facture existe d\u00E9j\u00E0
error_empty_invoice_number=Le num\u00E9ro de facture ne doit pas \u00EAtre vide
error_empty_party_identifier=L''une des trois valeurs doit \u00EAtre renseign\u00E9e (RCS-RCM, SIREN, N\u00B0 TVA intracommunautaire) : %1$s
error_empty_invoice_item_vat=Taux TVA est obligatoire : %1$s
error_empty_invoice_item_details=La liste des produits ne peut pas \u00EAtre vide
error_in_invoice_generation=Erreur lors de la g\u00E9n\u00E9ration de la facture
error_invoice_file_missing=Facture \u00E9lectronique non pr\u00E9sente. Contactez votre administrateur
error_invoice_export_birt_xls_missing=Aucun template Birt n'est associ\u00E9 au type Excel pour la facture. Contactez le support
error_invoice_export_birt_pdf_missing=Aucun template Birt n'est associ\u00E9 au type Pdf pour la facture. Contactez le support
error_missing_template = Pas de Template BIRT configur\u00E9, contactez votre administrateur.
error_invoice_download=Erreur de t\u00E9l\u00E9chargement
error_invoice_not_attachment=Aucune pi\u00E8ce jointe attach\u00E9e \u00E0 cette facture
error_date=La date de d\u00E9but doit \u00EAtre inf\u00E9rieure \u00E0 la date de fin
error_unit_price_negative=Le montant unitaire a une valeur n\u00E9gative
error_total_price_negative=Le montant total a une valeur n\u00E9gative
error_allwOrChrg_equals_zero=Une remise ou une taxe ne peut \u00EAtre \u00E9gale \u00E0 0
error_allwOrChrg_miss_description=Une remise ou une taxe doit avoir une description
error_allwOrChrg_multi_edit=Une remise ou une taxe est en cours d'\u00E9dition
error_invoice_select_line=Aucune facture n'est s\u00E9lectionn\u00E9e
error_order_select_line=Aucune commande n'est s\u00E9lectionn\u00E9e
error_document_select_line=Aucune document n'est s\u00E9lectionn\u00E9e
error_invoice_export_no_records=Vous ne pouvez pas exporter une liste vide
error_total_lt_zero=Le total de votre facture ne peut pas \u00EAtre n\u00E9gatif
error_empty_invoice_item_gross_price=Le prix unitaire brut est obligatoire : {0}
error_empty_invoice_item_description=La description est obligatoire : {0}
error_alloworchrg_greater_price_item=Le total des remises et charges ne peut \u00EAtre sup\u00E9rieur au prix du produit
error_invoice_item_quantity=La quantit\u00E9 ne peut \u00EAtre \u00E9gale \u00E0 0
error_document_locked=Un autre utilisateur vient de verrouiller la facture, voir la timeline pour plus de d\u00E9tail
error_const_allocation_double=Il n'est pas possible de saisir un code analytique en ent\u00EAte, qui s'applique \u00E0 l'ensemble de la facture, et un code analytique \u00E0 la ligne
error_const_allocation_not_filed=Tous les codes analytiques dans les d\u00E9tails doivent \u00EAtre remplis
error_asnNumber_double=Il n'est pas possible d'avoir un num\u00E9ro ASN en t\u00EAte et un num\u00E9ro ASN sur la ligne en m\u00EAme temps pour une facture.
error_existing_vat_rate=Il ne peut pas y avoir plusieurs lignes de ventilation de TVA avec un taux de {0}%
document_properties =Propri\u00E9t\u00E9s du document
manage_notes =G\u00E9rer les notes
control_list =Liste de contr\u00F4le
parties_tabs=Onglet entit\u00E9s
editable_fields=Champs \u00E9ditables
tax_mandatory_header=Code TVA obligatoire pour
detail=D\u00E9tails
header=Ent\u00EAte
filter=Filtrer
edit=Editer
visible_fields=Champs visibles
search=Rechercher
advance_shipment_notice_ref=Editer la R\u00E9f. de l'avis d'exp\u00E9dition
asn_ord_number=Editer le Num\u00E9ro de commande
complete_edition=G\u00E9n\u00E9rer edition compl\u00E8te
allowCompleteEdition=Autoriser l'\u00E9dition compl\u00E8te

standard_dialog_header =Importer une facture (*.csv)
refused = Refus\u00E9e
pending = En attente
none = Non lu
invoiced = Factur\u00E9
PENDING = En attente
read = Lu
accepted = Accept\u00E9e
error = Erreur

#Correct Invoice
error_in_processus=Erreur interne, facture non contr\u00F4l\u00E9
no_error_found=Aucune erreur n'a \u00E9t\u00E9 trouv\u00E9
correct=Corriger
diagnose=Diagnostiquer
title_correct_invoice=Corriger votre facture N\u00B0
title_diagnose_invoice=Visualiser les causes du refus de votre facture N\u00B0
title_force_invoice=Forcer la facture N\u00B0
description_correct_invoice=Pour corriger votre erreur, il vous suffit de :
description_diagnose_invoice=La facture a \u00E9t\u00E9 refus\u00E9e pour les raisons suivantes :
description_force_invoice=Pour forcer la facture, veuillez renseigner la raison :
cause = Raison
force = Forcer
tip_one_correct_invoice=Saisir les donn\u00E9es manquantes ci-dessous en respectant le format de vos factures
tip_two_correct_invoice=Cliquez sur le bouton \"Corriger\" pour appliquer les corrections et soumettre la facture
process_error=Erreur avec le processus
error_process_not_deployed=Processus non d\u00E9ploy\u00E9
parsing_error=Erreur lors de l'analyse du fichier
archiving_error=Erreur avec l'archivage d'un fichier
correct_field_error=Il y a des nouveaux champs \u00E0 corriger
correct_validation=La correction a bien \u00E9t\u00E9 effectu\u00E9e
correct_file_not_found=Le fichier de correction est introuvable
force_validation=Facture {0} forc\u00E9e
force_cause_required=La cause doit \u00EAtre renseign\u00E9e
#Remise et charge en entete
total_net_chargeallowance=Montant net

invoice_escompte_condition_placeholder=Conditions d'escompte
invoice_penalty_condition_default_value=P\u00E9nalit\u00E9 de retard de paiement : (MERCI D'INDIQUER LE TAUX) % / mois de retard conform\u00E9ment \u00E0 la loi 92-1442 du 31/12/92
invoice_exo_tva_default_value=Exon\u00E9ration T.V.A , art. 262 ter-I du code g\u00E9n\u00E9ral des imp\u00F4ts
invoiceHeader_invoiceParty_sellerParty_listOfIdentifier_identifier_ident=Identifiant
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#1=Identifiant adresse 1
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#2=Identifiant adresse 2
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#3=Identifiant adresse 3
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#4=Identifiant adresse 4
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#5=Identifiant adresse 5
reference=R\u00E9f\u00E9rence
undefined=Non d\u00E9fini
refused_manually=Refuser
forced=Forcer
multiple_process_execution=Plusieurs processus ont \u00E9t\u00E9 \u00E9x\u00E9cut\u00E9s

confirm_delete=Etes-vous s\u00FBr de vouloir supprimer la facture ?
confirm_delete_selected=Etes-vous s\u00FBr de vouloir supprimer la ou les facture(s) selectionn\u00E9e(s) ?

valueControl=Liste de contr\u00F4le
strict=Strict

# Payment Items
CHECK=Ch\u00E8que
BANK_ACCOUNT=Paiement \u00E0 un compte bancaire
PROMISSORY_NOTE=Billet \u00E0 ordre
MILKING=Traite tir\u00E9e par le cr\u00E9ancier sur le d\u00E9biteur
CASH=Esp\u00E8ces
BANK_CARD=Carte bancaire
CLEARING_BETWEEN_PARTNERS=Compensation entre partenaires
CREDIT_TRANSFER=Transfert de cr\u00E9dit
DEBIT_TRANSFER=Virement bancaire
DIRECT_DEBIT=Pr\u00E9l\u00E8vement bancaire
INSTRUMENT_NOT_DEFINED=Autre
OTHER_LCRPAPIER=LCR Papier
OTHER_EFFETEMIS=Effet \u00E9mis
OTHER_TRAITEACCEPTFOUR=Traite acceptation fournisseur
OTHER_VIREMENTETRANGER=Virement bancaire \u00E9tranger

# Exempt VAT Items
ART_262_TER=Article 262 ter-i du CGI
ART_262=Article 262 I et II du CGI
ART_275A=Article 275A du CGI Franchise de TVA
ART_283=Article 283-1 du CGI
ART_196=Article 196 de la directive TVA

# history
history_action=Auditer
history_title=Audit
history_invoice_number=Facture n\u00B0
history_buyer=Acheteur
history_bill_to=Factur\u00E9 \u00E0
history_status=Statut
history_modified=Modifi\u00E9 le
history_time=\u00C0
history_by=Par
history_none=Aucun historique trouv\u00E9 pour la facture {0}
history_system=Syst\u00E8me

#PaymentConditions
invoice_penalty_condition_default_value=P\u00E9nalit\u00E9 de 40 EUROS appliqu\u00E9e pour non paiement \u00E0 \u00E9ch\u00E9ance - art 441.6CC
invoice_escompte_condition_default_value=Pas d'escompte pour paiement anticip\u00E9
invoice_penalty_terms_default_value=En cas de non respect de l'\u00E9ch\u00E9ance, des int\u00E9r\u00EAts de retard pourront \u00EAtre factur\u00E9s \u00E0 un taux \u00E9gal \u00E0 trois fois le taux d'int\u00E9r\u00EAt l\u00E9gal

# number of selected invoices
one_invoice_selected = \u00E9l\u00E9ment s\u00E9lectionn\u00E9
multiple_invoices_selected = \u00E9l\u00E9ments s\u00E9lectionn\u00E9s
all_invoices_selected = Tous les \u00E9l\u00E9ments de la liste sont s\u00E9lectionn\u00E9s
select_all = Tout s\u00E9lectionner
unselect_all = Tout d\u00E9s\u00E9lectionner

warning_partially_editable=Attention, la visibilit\u00E9 de la colonne creationDate ne peut \u00EAtre chang\u00E9e.


#trigger actions
triggerActions_fieldset=Ajouter des actions
triggerActions_add=Ajouter
triggerActions_actionName_placeholder=Nom de l'action
triggerActions_global=Global
triggerActions_complete_info=L''action ''{0}'' a \u00E9t\u00E9 ex\u00E9cut\u00E9e avec succ\u00E8s pour la facture ''{1}''
triggerActions_complete_info_multi=L''action ''{0}'' a \u00E9t\u00E9 ex\u00E9cut\u00E9e avec succ\u00E8s pour l''ensemble des factures s\u00E9lection\u00E9es
triggerActions_execute_info=L''action ''{0}'' est appliqu\u00E9e sur la facture ''{1}''
triggerActions_execute_info_global=L''action ''{0}'' est appliqu\u00E9e sur les factures s\u00E9lection\u00E9es
triggerActions_statusError=L''action demand\u00E9e n''est pas autoris\u00E9e pour une facture dont le statut est ''{0}''
triggerActions_multiStatusError=L''action demand\u00E9e n''est pas autoris\u00E9e pour les factures dont le statut est ''{0}''. L''action n''a pas \u00E9t\u00E9 lanc\u00E9e sur ces factures
triggerActions_errorInProgress_multi=Attention, une action \u00E9tait d\u00E9j\u00E0 en cours sur certaines factures, celle ci n'ont pas \u00E9t\u00E9 trait\u00E9es
triggerActions_generalError=Une erreur est survenue lors de l'execution de l'action. Contactez votre administrateur
triggerActions_errorInProgress=Une action est d\u00E9j\u00E0 en cours sur cette facture
triggerActions_notAllDocumentsPresent=Il y a des documents qui ne peuvent pas \u00EAtre trait\u00E9s. Contactez votre administrateur
triggerActions_emptyAction=<Ind\u00E9finie>
triggerActions_noneSelected=Liste des statuts autoris\u00E9s
triggerActions_limitWarning=Il n'est pas possible de s\u00E9lectionner plus de 100 factures pour ce type d'action. Merci de s\u00E9lectionner 100 factures au maximum
security_fields_list=Baser la s\u00E9curit\u00E9 de la liste sur les champs
#StandardInvoiceEdition
invoice_portlet_selection=Portlet d'ajout de factures
invoice_portlet_visualization=Portlet de visualisation de factures
eReporting_portlet_selection=Portlet d'ajout des rapports
parties_info=Parties
notes = Notes
payment_title_tab=Paiement
allowance_or_charge_customer=Remises et charges
add_allow_customer=Ajouter une remise
add_charge_customer=Ajouter une charge
add_parafiscal_tax=Ajouter une taxe parafiscale
add_parafiscal_tax_tooltip_line1=Pour une remise, une charge ou une taxe parafiscale en montant, il faut mettre le montant pour un produit.
add_parafiscal_tax_tooltip_line2=Attention \u00E0 l'ordre des lignes.
add_parafiscal_tax_tooltip_line3=Les remises, charges et taxes sont cumulatives.
add_parafiscal_tax_tooltip_line4=Par exemple: un produit avec un prix brut \u00E0 10\u20AC.
add_parafiscal_tax_tooltip_line5=Si on ajoute une charge de transport de 1\u20AC sur la premi\u00E8re ligne.
add_parafiscal_tax_tooltip_line6=Puis une remise de 10% sur la deuxi\u00E8me ligne.
add_parafiscal_tax_tooltip_line7=Le prix final sera: 10 + 1 = 11 - 10% = 9,9
add_parafiscal_tax_tooltip_line8=Mais si on inverse l'ordre des lignes.
add_parafiscal_tax_tooltip_line9=Le prix final sera: 10 -10% = 9 + 1 = 10
lines=Lignes
footer_customer=Pied
error_in_conversion=Impossible de cr\u00E9er une facture. Veuillez contacter votre administrateur.
error_create_invoice=Il n'est pas possible de cr\u00E9er une facture en tant qu'utilisateur de client. Merci de vous connecter comme un fournisseur si vous voulez cr\u00E9er une facture.
std_invoicenumber_title=Edition de la facture N\u00B0
std_invoicenumber_title_add=Ajout de la facture N\u00B0
confirm_return=Attention, en quittant la page d\\'\u00E9dition les donn\u00E9es corrig\u00E9es ne seront pas sauvegard\u00E9es. Voulez vous quitter la page d\\'\u00E9dition ?
open_in_new_tab=Ouvrir dans un nouvel onglet
display_pdf=Afficher le PDF \u00E0 c\u00F4t\u00E9 de la saisie
standard_inv_edition_mode=Edition
standard_inv_visualization_mode=Portlet
invoice_control_portlet=Portlet de Contr\u00F4le de factures
standard_erep_edition_mode=Edition
standard_inv_creation_mode=Creation
standard_erep_creation_mode=Creation
files_title=Pi\u00E8ces attach\u00E9es

portlet_mode=Mode d'utilisation de la portlet
invoice_modification_status_label=Mise \u00E0 jour du statut une fois la validation termin\u00E9e
creation_edition_change_message=Attention : Cette modification n'est pas conseill\u00E9e car changer le mode d'utilisation de cette PT va provoquer des probl\u00E8mes si elle est appel\u00E9e dans d'autres PT (Invoice, ASN, Orders ...)
standard_invoice_edition_status_list=Liste des statuts autorisant l'\u00E9dition
ocr_verification_status_list=Liste des status autorisant le vid\u00E9ocodage seulement pour jsf
invoice_compliance_actions=Configuration des actions de conformit\u00E9 de la facture

#FormPage template
no_template=Aucun

#Modiy
modifiable=Modifier
ocr_validation_message=Valider
ocr_validation_message_content=Veuillez noter que suite \u00E0 cette validation, la coloration des champs \u00E0 v\u00E9rifier sera d\u00E9finitivement supprim\u00E9e. Assurez vous d'avoir v\u00E9rifi\u00E9 tous les champs.

#Confirmation dialog
confirmation_title_creation=Confirmation d'envoi
confirmation_title_edit=Confirmation
confirmation_message_creation=Veuillez confimer l'envoi de votre facture
confirmation_message_edit=Confirmez vous les modifications apport\u00E9es \u00E0 la facture
confirmation_number=n\u00B0
confirmation_to=\u00E0 destination de
confirmation_siren=SIREN:
siret=SIRET
confirmation_vat=TVA:
confirmation_information=avec les informations suivantes:
amount_ht=- Total HT:
amount_tva=- Total TVA:
amount_ttc=- Total TTC:
amount_payable=- Montant \u00E0 payer:
confirm_creation=Confirmer et envoyer
confirm_edit=Confirmer

#OcrWarningDetails
ocr_warning_details=Attention, au moins un champ non visible \u00E0 l'\u00E9cran est \u00E0 v\u00E9rifier sur cette ligne.

#Document Viewer
lock_action=Verrouiller
unlock_action=D\u00E9verrouiller
validate_action=Valider
addData_action=Enrichir
comment_action=Commenter
refuse_action=Refuser
transferTask_action=Assigner une t\u00E2che
attachFile_action=Joindre un fichier
stopNotification_action=Mettre en pause les notifications
give_back_control_action=Rendre la main
cancel_assign_task_action=Annuler la demande
transferTask_checkboxes_message=La s\u00E9lection d'au moins une action est obligatoire.
relaunch_reconciliation_action=Relancer la r\u00E9conciliation
reconciliation_not_found=Aucun r\u00E9conciliation trouv\u00E9.

#Invoice Timeline
init_action_message=Message automatique: le document est associ\u00E9 au workflow:
wkf_exit_action_message=Sortie du workflow
attatch_file_action_message=a joint un fichier \u00E0 la facture
attatch_file_action_message_comment=a joint un fichier \u00E0 la facture avec le commentaire suivant:
comment_action_message=a comment\u00E9 la facture:
comment_automatic_message=Message automatique: un commentaire a \u00E9t\u00E9 ajout\u00E9:
validate_action_message=a valid\u00E9 la facture
stopNotification_message=a mis en pause les notifications de relance
end_action_message=Message automatique: fin du workflow.
refuse_action_message=a refus\u00E9 la facture
refuse_action_message_comment=a refus\u00E9 la facture avec le commentaire suivant:
enrich_action_message=a enrichi les donn\u00E9es de la facture
lock_action_message=a verrouill\u00E9 la facture
unlock_action_message=a d\u00E9verrouill\u00E9 la facture
valid_action_automatic_message=Message automatique: Nombre de relances atteint, validation automatique de l''\u00E9tape N\u00B0 {0}
edit_action_message=a modifi\u00E9 la facture
fix_action_message=a corrig\u00E9 la facture
reminder_action_automatic_message=Message automatique: Relance N\u00BA {0} envoy\u00E9e par mail.
custom_action_message=a effectu\u00E9 l'action:
transferTask_action_message=a assign\u00E9 une t\u00E2che \u00E0
transferTask_action_message_comment = avec le commentaire:
give_back_control_action_message=a rendu la main \u00E0
make_file_readable_checkbox_label=Utiliser ce fichier pour remplacer le lisible actuel.
confirm_file_readable_message=Il existe d\u00E9j\u00E0 un lisible pour cette facture, voulez vous le remplacer?
cancel_assign_task_action_message=a annul\u00E9 la demande de t\u00E2che pour
by_delegation_of_action_message=par d\u00E9l\u00E9gation de
reconciliation_empty_seller_party_message=R\u00E9conciliation impossible : code fournisseur absent.
reconciliation_empty_order_number_message=R\u00E9conciliation impossible : n\u00B0 de commande absent.
reconciliation_order_not_found_message=R\u00E9conciliation impossible : commande introuvable.
reconciliation_empty_reception_number_message=R\u00E9conciliation impossible : n\u00B0 de r\u00E9ception absent.
reconciliation_reception_not_found_message=R\u00E9conciliation impossible : r\u00E9ception introuvable.
reconciliation_start_message=D\u00E9but de la r\u00E9conciliation.
reconciliation_success_message=R\u00E9conciliation termin\u00E9e avec succ\u00E8s.
reconciliation_gap_found_message=R\u00E9conciliation termin\u00E9e, des \u00E9carts ont \u00E9t\u00E9 rencontr\u00E9s.
relaunch_reconciliation_message=R\u00E9conciliation relanc\u00E9e
consult_message=Le document a \u00E9t\u00E9 consult\u00E9.
control_fr_message=Les contr\u00F4les en vigueur en France en 2024 ont \u00E9t\u00E9 effectu\u00E9s sur ce document.
control_eu_message=Les contr\u00F4les europ\u00E9ens ont \u00E9t\u00E9 effectu\u00E9s sur ce document.

control_b2g2024_message=Les contr\u00F4les en vigueur pour une facture B2G (premi\u00E8re phase de la r\u00E9forme) ont \u00E9t\u00E9 effectu\u00E9s \u00E0 ce document conform\u00E9ment aux r\u00E8gles de gestion ({0}) de l'annexe 7 des sp\u00E9cifications externes B2B publi\u00E9es par le gouvernement fran\u00E7ais.
control_b2b2024_message=Les contr\u00F4les en vigueur pour une facture B2B (premi\u00E8re phase de la r\u00E9forme) ont \u00E9t\u00E9 effectu\u00E9s \u00E0 ce document conform\u00E9ment aux r\u00E8gles de gestion ({0}) de l'annexe 7 des sp\u00E9cifications externes B2B publi\u00E9es par le gouvernement fran\u00E7ais.
control_b2b_message=Les contr\u00F4les en vigueur pour une facture B2B ont \u00E9t\u00E9 effectu\u00E9s \u00E0 ce document conform\u00E9ment aux r\u00E8gles de gestion ({0}) de l'annexe 7 des sp\u00E9cifications externes B2B publi\u00E9es par le gouvernement fran\u00E7ais.
control_b2g_message=Les contr\u00F4les en vigueur pour une facture B2G ont \u00E9t\u00E9 appliqu\u00E9es \u00E0 ce document conform\u00E9ment aux r\u00E8gles de gestion ({0}) de l'annexe 7 des sp\u00E9cifications externes B2B publi\u00E9es par le gouvernement fran\u00E7ais.
control_b2g2024_message_profile=Les contr\u00F4les en vigueur pour une facture B2G profil ({1})(premi\u00E8re phase de la r\u00E9forme) ont \u00E9t\u00E9 effectu\u00E9s \u00E0 ce document conform\u00E9ment aux r\u00E8gles de gestion ({0}) de l'annexe 7 des sp\u00E9cifications externes B2B publi\u00E9es par le gouvernement fran\u00E7ais.
control_b2b2024_message_profile=Les contr\u00F4les en vigueur pour une facture B2B profil ({1}) (premi\u00E8re phase de la r\u00E9forme) ont \u00E9t\u00E9 effectu\u00E9s \u00E0 ce document conform\u00E9ment aux r\u00E8gles de gestion ({0}) de l'annexe 7 des sp\u00E9cifications externes B2B publi\u00E9es par le gouvernement fran\u00E7ais.
control_b2b_message_profile=Les contr\u00F4les en vigueur pour une facture B2B profil ({1}) ont \u00E9t\u00E9 effectu\u00E9s \u00E0 ce document conform\u00E9ment aux r\u00E8gles de gestion ({0}) de l''annexe 7 des sp\u00E9cifications externes B2B publi\u00E9es par le gouvernement fran\u00E7ais.
control_b2g_message_profile=Les contr\u00F4les en vigueur pour une facture B2G profil ({1}) ont \u00E9t\u00E9 appliqu\u00E9es \u00E0 ce document conform\u00E9ment aux r\u00E8gles de gestion ({0}) de l''annexe 7 des sp\u00E9cifications externes B2B publi\u00E9es par le gouvernement fran\u00E7ais.
control_duplicate_message=Le contr\u00F4le de doublon a \u00E9t\u00E9 effectu\u00E9.
aap_started_message=D\u00E9but de l''imputation comptable
aap_na_message=Imputation comptable sans objet
aap_manual_mode_message=Imputation comptable forc\u00E9e en mode manuel
aap_partial_message=Imputation comptable partiellement effectu\u00E9e
aap_missing_po_message=Imputation comptable impossible : commande introuvable
aap_unbalanced_message=Imputation comptable impossible : facture d\u00E9s\u00E9quilibr\u00E9e
aap_missing_rcp_message=Imputation comptable impossible : r\u00E9ception introuvable
aap_missing_ref_message=Imputation comptable impossible : r\u00E9f\u00E9rentiel introuvable
control_modif_siren_client=Le SIREN client a \u00E9t\u00E9 modifi\u00E9. Ancienne valeur : {0}. Nouvelle valeur : {1}
control_modif_siren_supplier=Le SIREN fournisseur a \u00E9t\u00E9 modifi\u00E9. Ancienne valeur : {0}. Nouvelle valeur : {1}
control_modif_tvaintra_client=Le num\u00E9ro de TVA intracommunautaire du client a \u00E9t\u00E9 modifi\u00E9. Ancienne valeur : {0}. Nouvelle valeur : {1}
control_modif_tvaintra_supplier=Le num\u00E9ro de TVA intracommunautaire du fournisseur a \u00E9t\u00E9 modifi\u00E9. Ancienne valeur : {0}. Nouvelle valeur : {1}
legal_action_completed=La facture a \u00E9t\u00E9 compl\u00E9t\u00E9e.
legal_action_payment_received=Le paiement de la facture a \u00E9t\u00E9 re\u00E7u.
legal_action_approved=La facture a \u00E9t\u00E9 approuv\u00E9e.
legal_action_refused=La facture a \u00E9t\u00E9 refus\u00E9e.
legal_action_approved_subrogate=La facture a \u00E9t\u00E9 soumise \u00E0 subrogation.
legal_action_payment_sent=Le paiement de la facture a \u00E9t\u00E9 transmis.
legal_action_factor=La facture a \u00E9t\u00E9 soumise \u00E0 affacturage.
legal_action_factor_confidential=La facture a \u00E9t\u00E9 soumise \u00E0 affacturage confidentiel.
aap_already_completed=Imputation comptable non modifiable (d\u00E9j\u00E0 compl\u00E9t\u00E9e)
aap_completed_automatically=Imputation comptable compl\u00E9t\u00E9e automatiquement
control_schematron_message=Les contr\u00F4les du sch\u00E9matron \"{0}\" ont \u00E9t\u00E9 appliqu\u00E9s.
actors_designation_message=a d\u00E9sign\u00E9 {0} comme acteur(s) de l''\u00E9tape {1}
ocr_verify_action_message=a lanc\u00E9 le vid\u00E9ocodage.
ocr_postponed_action_message=a report\u00E9 le vid\u00E9ocodage.

#EARLY PAYMENT
early_payment_seller_accept_add_timeline=: a accept\u00E9 la proposition de paiement anticip\u00E9. La facture sera r\u00E9gl\u00E9e {0} jours \u00E0 l''avance, en \u00E9change d''un escompte de {1} % qui repr\u00E9sente {2}\u20AC
early_payment_seller_reject_add_timeline=: a refus\u00E9 la proposition de paiement anticip\u00E9 avec le commentaire suivant : \"{0}\"
ep_submited_message=: propose de payer la facture {0} jours avant sa date d''\u00E9ch\u00E9ance en \u00E9change d''un escompte.

#Workflow comment
comment_dialog_title=Ajouter un commentaire
stop_notification_comment=Les notifications de relance ont \u00E9t\u00E9 stopp\u00E9es
max_security_fields_selected_error_message=Pour des raisons de performance, il n'est possible de s\u00E9lectionner que 3 champs au maximum.
min_security_fields_selected_error_message=Il est obligatoire de filtrer sur au moins un des acteurs de la facture.

#Workflow refuse
document_refused=Le document a \u00E9t\u00E9 refus\u00E9
refuse_message_mandatory=La raison du refus est obligatoire
return=Retour \u00E0 la liste
add_comment=Ajouter un commentaire
validate_comment=Valider
confirm_refuse_doc_wkf=Confirmez-vous le refus de ce document ?
confirm_refuse_doc_wkf_title=Confirmer le refus

#Workflow assign task
redirect_user_comment_message=La saisie d'un commentaire est obligatoire pour cette action. Le commentaire sera transmis \u00E0 la personne qui effectuera la t\u00E2che, soyez pr\u00E9cis dans votre demande.
assignTask_dialog_title=Assigner une t\u00E2che
assignTask_dialog_actors=Quel acteur doit r\u00E9aliser la t\u00E2che
assignTask_dialog_actions=Quelles actions doit-il r\u00E9aliser
assignTask_dialog_comment=Expliquez ce que vous attendez de lui et pourquoi

#Archive statuses
UNDEFINED=Ind\u00E9fini
IN_SUBMISSION=En cours de versement
SUBMITTED=Vers\u00E9
ARCHIVED=Archiv\u00E9
UNARCHIVABLE=Non archivable

massValidation=Valider en masse
unauthorizedValidation=Une ou plusieurs factures ont d\u00E9j\u00E0 \u00E9t\u00E9 valid\u00E9es ou verrouill\u00E9es par un autre utilisateur.

massRefusal=Refuser en masse
unauthorizedRefuse=Une ou plusieurs factures ont d\u00E9j\u00E0 \u00E9t\u00E9 valid\u00E9es, refus\u00E9es ou verrouill\u00E9es par un autre utilisateur
canNotRefuse=Une ou plusieurs factures n'ont pas d'action refuser
confirm_refuse_selected=Etes-vous s\u00FBr de vouloir refuser la ou les facture(s) s\u00E9lectionn\u00E9e(s) ?
refusedMessage={0} facture(s) refus\u00E9e(s) avec succ\u00E8s

#quick search configuration
quick_search_settings=Settings of quick search
quick_search_full_text_option=Contient le mot
quick_search_start_with=Commence par (valeur par d\u00E9faut)
quick_search_strict=Stricte
quick_search_disable=D\u00E9sactiv\u00E9e
quick_search_case_sensitive=Case sensitive
quick_search_warning_message=Attention, si il y a beaucoup de documents, la recherche sera longue, cela ralentira la fluidit\u00E9 du portail et peut provoquer des probl\u00E8mes de consommation CPU.

#freetext
additional_info=Informations additionnelles
editable=Editable
required=Obligatoire
hidden=Cach\u00E9
label_required=La valeur est requise

#Editable BO properties
manageInvoiceReference=G\u00E9rer les r\u00E9f\u00E9rences
addOtherParties=Ajouter une autre entit\u00E9
manageAllowOrCharge=G\u00E9rer les remises et charges
addDetail=Ajouter une ligne
editDetail=Modifier une ligne
deleteDetail=Supprimer une ligne
manageActualPayment=G\u00E9rer les acomptes
editable_fields=Champs \u00E9ditables
mandatory_fields=Champs obligatoires
hidden_fields=Champs cach\u00E9s
default_value=Valeur par d\u00E9faut
tooltip_default_value=D\u00E9finir la date limite de paiment par d\u00E9faut \u00E0 aujourd'hui + X jours
link_to_address_referential=Lier au r\u00E9f\u00E9rentiel adresse
link_to_contact_scope=Lier au p\u00E9rim\u00E8tre du contact
display_warning_sellerPartyID=Attention, le champ Identifiant ne sera \u00E9ditable que pour les factures provenant d'un processus d'OCR (acquisition = 'OCR')
manage_files=Ajout
link_to_referential=Lier au referentiel
link_to_referential_tooltip=Les infos sont cherch\u00E9es dans le r\u00E9f\u00E9rentiel adresse, sauf dans le cas de l'autofacturation ou les infos sont cherch\u00E9es dans le p\u00E9rim\u00E8tre du contact.
link_to_referential_tooltip_supplier=Les infos sont cherch\u00E9es dans le p\u00E9rim\u00E8tre du contact, sauf dans le cas de l'autofacturationout les infos sont cherch\u00E9es dans le r\u00E9f\u00E9rentiel adresse.

#Attachments
attachment=Pi\u00E8ces jointes
attachment_name=Nom de la pi\u00E8ce jointe
comment=Commentaire
add_date=Date de l'ajout
size=Taille
received_on= re\u00E7u le
error_no_demat_errors=Cette facture n'a pas de fichier d'erreur de d\u00E9mat\u00E9rialisation
error_no_tax_original=Aucun original fiscal pour cette facture

#Chorus
DEB=TVA sur les d\u00E9bits
ENC=TVA sur les encaissements
EXEMPT=Exon\u00E9r\u00E9
A1=A1: D\u00E9p\u00F4t par un fournisseur d'une facture
A2=A2: D\u00E9p\u00F4t d'une facture d\u00E9j\u00E0 pay\u00E9e
A9=A9: D\u00E9p\u00F4t d'une facture par un sous-traitant
A12=A12: D\u00E9p\u00F4t d'une facture par un cotraitant
chorus_warn_pro_unavailable=Les services ChorusPro ne sont actuellement pas disponibles, merci de bien vouloir r\u00E9essayer ult\u00E9rieurement
chorus_structureId=Identifiant
chorus_service=Service
chorus_structure_placeholder=Recherche une structure par identifiant ou libell\u00E9...

#Reconciliation Dialog
rec_dld_header=Analyse des \u00E9carts de rapprochement de la facture n\u00B0
rec_dld__header_of=du
rec_name_provider=Nom fournisseur:
rec_order_number=Commande n\u00B0
rec_reception_number=R\u00E9ception n\u00B0
rec_invoice_number=Facture n\u00B0
rec_creditNote_number=Note de cr\u00E9dit n\u00B0
 #bottom
rec_differences_bottom_title=\u00C9carts de rapprochement
rec_total_amount_excluding_tax=Montant Total HT
rec_total_amount_including_tax=Montant total TTC
rec_ordered=Commande
rec_reception=R\u00E9ception
rec_invoiced=Factur\u00E9
rec_invoice=Facture
rec_receipt=\u00C9cart
rec_tolerance=Tol\u00E9rance
rec_total=(Total)
 #lines	
rec_differences_invoice_line_title=\u00C9carts de rapprochement de niveau ligne
rec_line_number=N\u00B0 ligne
rec_descpription=Description du produit
rec_product_code=Code produit
rec_quantity=Quantit\u00E9
rec_unit_price=Prix unitaire
rec_amount=Montant
rec_difference=\u00C9cart
rec_all_lines=Voir toutes les lignes
not_rec_all_lines=Voir les lignes en erreur
rec_financial_gaps_tab=\u00C9carts financiers
rec_other_gaps_tab=Autres \u00E9carts
rec_comment=Commentaire
rec_is_gap=Oui
rec_is_not_gap=Non
rec_no_product_identifier=Aucun identifiant produit trouv\u00E9

select_rte_refuse_label=Processus post-refus
refuse_invoice_title=Refuser la facture

#button
rec_detected_deviation=\u00C9carts d\u00E9tect\u00E9s
rec_reconcilation_done=Rapprochement effectu\u00E9

validator_date_range=D\u00E9finir une plage de temps entre 0 et 90 jours

lifeCycle=Cycle de vie de la facture N\u00B0
lifeCycleComplete=D\u00E9tails du cycle de vie
see_more=voir plus

replay_workflow=Rejouer le workflow apr\u00E8s l'\u00E9dition
confirm_new_workflow=Cette facture doit \u00EAtre associ\u00E9e \u00E0 un nouveau workflow. Toutes les \u00E9tapes effectu\u00E9es sur l'ancien workflow seront perdues. Confirmez-vous ?
confirm_no_workflow=Cette facture n'est plus sujette \u00E0 un workflow. Toutes les \u00E9tapes effectu\u00E9es sur l'ancien workflow seront perdues. Confirmez-vous ?
disconnected_action_message=Message automatique : le document est dissoci\u00E9 du workflow :
reinit_action_message=Message automatique : le workflow {0} a \u00E9t\u00E9 remplac\u00E9 par le workflow {1}

workflow_not_applicable_message=Workflow sans objet
reconciliation_not_applicable_message=R\u00E9conciliation sans objet

ocr_verify=Vid\u00E9ocoder
rossum_fail=Impossible d''ouvrir le studio Rossum : {0}.

#Workflow designation of the actors for the next step
designate_next_step_actors_dialog=D\u00E9signation des acteurs de l'\u00E9tape suivante
ok_btn=OK
discard_btn=Abandonner

duplicate_check_error=Les r\u00E8gles d'unicit\u00E9 de la facture fran\u00E7aise ne sont pas respect\u00E9es (N\u00B0 de facture ; ann\u00E9e de facture ; \u00E9metteur)

display_warning_params_only_in_angular=Attention : Certains param\u00E8tres ne seront pris en compte qu'en Angular
invoiceHeader_invoiceParty_billToParty_partyTaxInformation_taxIdentifier_ident = Code TVA du factur\u00E9
invoiceHeader_invoiceNumber = Facture n\u00B0
invoiceHeader_invoiceReferences[0]_asnNumber_refNum = N\u00B0 bon livraison
invoiceHeader_invoiceReferences[0]_purchaseOrderReference_purchaseOrderDate = Date de commande
invoiceSummary_invoiceTotals_taxableValue_monetaryAmount = Total H.T.
invoiceHeader_invoiceParty_sellerParty_nameAddress_name1 = Vendeur
invoiceHeader_invoiceReferences[0]_purchaseOrderReference_buyerOrderNumber = Num\u00E9ro de commande
invoiceHeader_invoiceParty_buyerParty_nameAddress_name1 = Acheteur
invoiceSummary_invoiceTotals_invoiceTotal_monetaryAmount = Total T.T.C
invoice_cost_allocation_number = Code analytique
invoiceHeader_invoiceReferences[0]_asnNumber_refDate = Date bon livraison
invoiceHeader_invoiceCurrency_currencyCoded = Devise
invoiceHeader_invoiceParty_billToParty_nameAddress_name1 = Factur\u00E9
invoiceHeader_invoiceParty_billToParty_partyID_ident = Identifiant du factur\u00E9
invoiceSummary_invoiceTotals_totalAmountPayable_monetaryAmount = Montant \u00E0 payer
invoiceHeader_invoiceParty_sellerParty_partyTaxInformation_taxIdentifier_ident = Code TVA du vendeur
invoiceHeader_invoiceDates_invoiceDueDate = Date limite de paiement
invoiceHeader_invoiceParty_sellerParty_partyID_ident = Identifiant du vendeur
invoiceHeader_invoiceParty_billToParty_nameAddress_country_countryCoded = Pays du factur\u00E9
invoiceHeader_invoiceParty_shipToParty_partyID_ident = Identifiant livr\u00E9 \u00E0
invoiceHeader_invoiceType_invoiceTypeCoded = Type de facture
invoiceHeader_invoiceParty_buyerParty_nameAddress_country_countryCoded = Pays de l'acheteur
invoiceHeader_invoiceParty_buyerParty_partyTaxInformation_taxIdentifier_ident = Code TVA de l'acheteur
invoiceHeader_invoiceParty_sellerParty_nameAddress_country_countryCoded = Pays du vendeur
invoiceHeader_invoiceParty_buyerParty_partyID_ident = Identifiant de l'acheteur
invoiceSummary_invoiceTotals_totalTaxAmount_monetaryAmount = Montant TVA