## en - Task invoice

# Config : portal task
archiveStatus=Archive status
buyerPartyID=Buyer code
buyerPartyName=Buyer
buyerPartyTaxIdentifier=Buyer VAT code
buyerPartyCountryCoded=Buyer country
buyerOrderNumber=Buyer order number
buyerPartyOtherID=Buyer party other ID
purchaseOrderDate=Purchase order date
sellerPartyID=Supplier code
sellerPartyName=Supplier
sellerPartyTaxIdentifier=Supplier VAT code
sellerPartyCountryCoded=Supplier country
sellerPartyOtherID=Seller party other ID
billToPartyID=Invoicee code
billToPartyName=Invoicee
billToPartyTaxIdentifier=Invoicee VAT code
billToPartyCountryCoded=Invoicee country
billToPartyOtherID=Bill-To party other ID
otherPartyRole=Other party role
otherPartyID=Other party ID
otherPartyName=Other party name
otherPartyCountryCoded=Other party country coded
otherPartyTaxIdentifier=Other party tax identifier
otherPartyOtherID=Other party other ID
contractID=Contract ID
invoiceNumber=Invoice number
invoiceIssueDate=Invoice issue date
invoiceDueDate=Invoice due date
invoiceCurrencyCoded=Currency
taxAccountingCurrencyCoded=Tax accounting currency (code)
taxableValueInTaxAccountingCurrency=Taxable value in tax accounting currency
invoiceTypeCoded=Invoice type
invoiceTotal=Total (Inc. VAT)
taxableValue=Total (Ex. VAT)
totalTaxAmount=Tax amount
totalAmountPayable=Amount payable
shipToPartyID=Shipto Ident
asnNumber=ASN number
asnDate=ASN date
customerReferenceNumber=Customer reference number
receptionNumber=Reception Number
relatedInvoiceNumber=Related invoice number
relatedInvoiceDate=Related invoice date
invoiceProcessDateTime=Dematerialisation date
invoicePreparationDateTime=Preparation date
from=From
to=To
owners=Owner
thirdParty=Third Party
archiveExpirationDate=Archive expiration date
rossumId=Rossum Id
type=Norm
status=Status
stage=Legal status
duplicate_invoice_number=Unique invoice number
option=Option
count_enabled=View the total number of invoices
invoice_columns_details=Columns of invoice detail
enable_safebox_search=Safebox search
orderingParty=Ordering Party
consultStatus=Consultation status
processingWay=Direction
paymentStatus=Payment status
daysPayedEarly=Days paid early (DPE)
earlyPaymentAnnualDiscountRate=Annual rate (%)
earlyPaymentDiscountRate=Discount
earlyPaymentAmountDiscount=Discount without tax
day=days
freeText01=Free text 01
freeText02=Free text 02
freeText03=Free text 03
freeText04=Free text 04
freeText05=Free text 05
freeText06=Free text 06
freeText07=Free text 07
freeText08=Free text 08
freeText09=Free text 09
freeText10=Free text 10
freeText11=Free text 11
freeText12=Free text 12
freeText13=Free text 13
freeText14=Free text 14
freeText15=Free text 15
freeText16=Free text 16
freeText17=Free text 17
freeText18=Free text 18
freeText19=Free text 19
freeText20=Free text 20
freeText21=Free text 21
freeText22=Free text 22
freeText23=Free text 23
freeText24=Free text 24
freeText25=Free text 25
freeText26=Free text 26
freeText27=Free text 27
freeText28=Free text 28
freeText29=Free text 29
freeText30=Free text 30
freeText31=Free text 31
freeText32=Free text 32
freeText33=Free text 33
freeText34=Free text 34
freeText35=Free text 35

shipToPartyName=Ship to name
shipToName=Ship to name
sellerOrderNumber=Seller order number
referenceInvoice=Reference invoice number
referenceInvoiceNumber=Reference invoice number
referenceInvoiceDate=Reference invoice date
profile=Profile

buyerAddressEmail=Buyer email
sellerAddressEmail=Seller email
billToAddressEmail=Bill To email
shipToAddressEmail=Ship To email

sovosId= Sovos Id
nature=Kind
immatpdp=PDP
b2gRules=B2G Rules
ereportingOption=E-reporting option
useCase=Use case
billingFramework=Billing framework
sovosStatus=Sovos status
sovosTransactionId=Transaction Id
sovosStatusCode=Status code
sovosNotificationId=Sovos notification id

new_bql_filter_on_role = Warning: a BQL filter can exist on user and/or his associated role and will be applied in addition to this filter
security_field_index_warning = Please note, modifying these parameters requires warning the BE_BDD in order to set the corresponding indexes. It is only possible to select a maximum of 3 fields.

only_workflow_invoices = Display only invoices requiring a workflow action

# Edit : Invoice
header_info=Invoice information
invoice_number=Invoice number
validate_message=Invalid character used in the invoice number
inv_type=Type
other=Other
invoice_currency=Currency
issue_date=Date
order_ref_num=Order reference
order_type=Order type
order_date=Order date
asn_ref_num=Advice shipment notice reference
asn_ref_date=Advice shipment date
invoice_dates=Invoice dates
due_date=Due date
due_date_calculation=Due date calculation
actual_ship_date=Ship date
actual_delivery_date=Delivery date
preparation_date=Preparation date
date_of_operation=Date of operation
invoicing_start_date=Start date of validity
invoicing_end_date=End date of validity
invoice_party_header=Invoice party information
invoice_billing_framework=Billing framework
invoice_market_number=Market number
invoiceCostAllocationNumber=Cost allocation number
invoice_cost_allocation_number_control=Check presence of cost allocation number
invoice_payment_choice=Payment choice
invoice_VAT_type=VAT type
invoice_commitment_number=Commitment number
ShipTo=Ship to
ShipFrom=Ship from
BillTo=Bill to
Buyer=Buyer
Seller=Supplier
RemitTo=Remit to
PartytoReceiveInvoiceFOrGoodsOrServices=Pay to
BuyersAgentOrRepresentative=Buyer's agent
SellersAgentOrRepresentative=Seller's agent
Payer=Invoice payer
other_party_info=Other party
identifier=Identifier
identifier_incorrect=Incorrect identifier
copy_bill_to_information=Copy Bill to information
limit_address_and_product=Attention! Only the first 100 items will be displayed. Start your entry to benefit from smart search...
name=Name
name_incorrect=Incorrect name
mail=Email
mail_not_valid=Email not valid
phone=Phone
contactName=Contact name
street=Street
street_incorrect=Incorrect street
street_supplement=Street Supplement
street_supplement_incorrect=Incorrect street supplement
postal_code=Postal code
postal_code_incorrect=Incorrect postal code
city=City
city_incorrect=Incorrect city
country=Country
country_subdivision=Country subdivision
contact=Contact
tel=Tel
fax=Fax
recordNumber=Record number
recordNumber_incorrect=Incorrect record number
gouv_ref=Government reference
gouv_ref_incorrect=Incorrect government reference
gouv_hashcode = Government hashcode
NIF = Tax Identification Number
bic=BIC
iban=IBAN
tva_ident=VAT code
tva_ident_incorrect=Incorrect VAT code
routing_code=Routing Code
addressing_identifier =Access point
registered_name=Registered name
legal_form=Legal form
share_capital=Share capital
member_single_subject=Member of a single taxable person
party_role_coded=Party role
allowance_or_charge=Allowance or charge
line_allowance_or_charge=Line allowance or charge
ref_allow_or_charge_tax=Allowance or charge from repository
indicator_coded=Indicator
description=Description
percent=Percent
monetary_amount=Amount
payment_instructions=Payment instruction
net_due_date=Net due date
payment_terms_note=Payment terms note
payment_terms_note_incorrect=Incorrect payment terms note
discount_terms=Discount terms
discount_terms_incorrect=Incorrect discount terms
late_payment=Late payment
penalty_terms=Penalty terms
tax_exempt=VAT exempted
reason_tax_exempt_coded_other=Reason of the exemption
header_note=Supplier legal form and share capital
details=Details
discount_charge_product_tab=Discount/Charge
general_product_tab=General
buyer_part_number=Buyer Item Code
seller_part_number=Supplier Item Code
product_identifier=Product identifier
product_identifier_ext=Product identifier external
item_description=Description
quantity_value=Invoiced qty
itemLineNumber=Line number
free_quantity_value=Free qty
free_unit_of_measurement=Unit of measurement for free quantity
unit_number__value=Quantity
unit_number_unit_of_measurement=Unit of measurement for pack quantity
unit_net_price=Net unit price
gross_price=Gross unit price
gross_amount=Gross amount
vat_code=VAT code
total_net=Net amount
total_ttc=Total (Inc. VAT)
unit_of_measurement=Unit
tax=VAT rate
item_tax_amount=VAT amount
summary=Invoice summary
list_actual_payment=Actual payments
payment_amount=Payment amount
ref_num=Reference number
payment_date=Payment date
list_tax_summary=VAT summary
vat_breakdown=VAT breakdown
vat_breakdown_rate=Rate
vat_breakdown_taxable_amount=Taxable amount
vat_breakdown_tax_amount=Tax amount
tax_category=VAT breakdown: rate
taxable_amount=VAT breakdown: taxable amount
tax_amounty=VAT breakdown: tax amount
manage_tax=VAT breakdown: manage
taxable_value=Total (Without VAT)
total_tax_amount=Total VAT
vat_amount=VAT amount
invoice_total=Total (Inc. VAT)
amount_to_pay=Amount to be paid
parafiscal_tax_breakdown=Parafiscal tax breakdown
parafiscal_tax=Parafiscal tax
parafiscal_tax_code=Tax code
parafiscal_tax_amount=Tax amount
manage_parafiscal_tax=Parafiscal tax breakdown: manage
value=Value
confirm_invoice=Confirm
add_entity=Add party
add_alloworchrg=Add allowance or charge
add_allowance=Allowance
add_charge=Charge
remove_alloworchrg=Remove allowance or charge
edit_product=Edit invoice line
remove_product=Remove invoice line
total_net_amount=Total net amount
invoicing_credit=In the case of a credit or a service invoice
invoice_references=References
add_invoice_reference=Add reference
remove_invoice_reference=Delete reference
invoice_reference_type=Reference type
invoice_reference_type_CONTRACT=Contract
invoice_reference_type_ORDER=Order
invoice_reference_type_ORDER_RESPONSE=Order Response
invoice_reference_type_DISPATCH_ADVICE=Dispatch advice
invoice_reference_type_INVOICE=Invoice
invoice_reference_type_DEBIT_NOTE=Debit note
invoice_reference_type_CREDIT_NOTE=Credit note
buyer_order=Buyer order
seller_order=Seller order
order_response=Order response
dispatch_advice=Dispatch advice
debit_note=Debit note
credit_note=Credit note
contract=Contract
project=Project
invoice_reference_num=Reference number
invoice_reference_date=Reference date
invoice_reference_period_start_date=Period start date
invoice_reference_period_end_date=Period end date
invoice_reference_date_or_period_required=Date or period have to be provided
invoice_reference_period_coherence=Period end date has to bo be after period start date
invoice_reference_date_only=Date and period cannot be both provided
invoice_reference_period_required=Period is required for this reference type
invoice_reference_date_required=Date is required for this reference type
detail_precision=Number of decimals for price and amount at line level
footer_precision=Number of decimals for price and amount at footer level
footer=Footer
print_fieldset=Print
cache_pdf=Manage PDF cache
parties_tabs_fieldset=Edit
autoComplete=Autocomplete
non_autoComplete=No autocomplete

#Combo
new=New
HeadOffice=Head office
DeclarantsAgentOrRepresentative=Declarants agent or representative
Factor=Factor
ALLOWANCE=Allowance
LINE_ITEM_ALLOWANCE=Allowance
CHARGE=Charge
LINE_ITEM_CHARGE=Charge
SERVICE=Tax-regulatory tax
LINE_ITEM_TAX_REGULATORY_TAX=Tax-regulatory tax
PERCENT=Percent
MONETARY_AMOUNT=Monetary amount
METERED_SERVICES_INVOICE=Metered Services Invoice
CREDIT_NOTE_FINANCIAL_ADJUSTMENT=Credit Note Related To Financial Adjustments
DEBIT_NOTE_FINANCIAL_ADJUSTMENT=Debit Note Related To Financial Adjustment
INVOICING_DATA_SHEET=Invoicing Data Sheet
PROFORMA_INVOICE=Proforma Invoice
COMMERCIAL_INVOICE=Invoice
CREDIT_NOTE_GOODS_AND_SERVICES=Credit note
DEBIT_NOTE_GOODS_AND_SERVICES=Debit Note - Goods and Services
COMMISSION_NOTE=Commission Note
CORRECTED_INVOICE=Corrected Invoice
CONSOLIDATED_INVOICE=Consolidated Invoice
PREPAYMENT_INVOICE=Prepayment Invoice
SELF_BILLED_INVOICE=Self-Billed Invoice
DELCREDERE_INVOICE=Delcredere Invoice
FACTORED_INVOICE=Factored Invoice
CREDIT_INVOICE=Credit Invoice
CREDIT_MEMO=Credit Memo
DETOUR_BILLING=Detour Billing
THIRD_PARTY_CONSOLIDATED_INVOICE=Third Party Consolidated Invoice
DEBIT_INVOICE=Debit Invoice
DEBIT_MEMO=Debit Memo
CORRECTED_FACTORED_INVOICE=Factored Correcting Invoice
FACTORED_CREDIT_NOTE=Factored Credit Note
PREPAYMENT_INVOICE_CREDIT_NOTE=Prepayment Invoice Credit Note
SELF_BILLED_FACTORED_INVOICE=Factored Self-Billed Invoice
SELF_BILLED_PREPAYMENT_INVOICE=Self-Billed Prepayment Invoice
SELF_BILLED_CORRECTED_INVOICE=Self-Billed Correcting Invoice
SELF_BILLED_CORRECTED_FACTORED_INVOICE=Factored Self-Billed Correcting Invoice
SELF_BILLED_CREDIT_NOTE=Self-Billed Credit Note
SELF_BILLED_FACTORED_CREDIT_NOTE=Factored Self-Billed Credit Note
metered_services_invoice=Metered Services Invoice
credit_note_financial_adjustment=Credit Note Related To Financial Adjustments
debit_note_financial_adjustment=Debit Note Related To Financial Adjustment
invoicing_data_sheet=Invoicing Data Sheet
proforma_invoice=Proforma Invoice
commercial_invoice=Invoice
credit_note_goods_and_services=Credit note
debit_note_goods_and_services=Debit Note - Goods and Services
commission_note=Commission Note
corrected_invoice=Corrected Invoice
consolidated_invoice=Consolidated Invoice
prepayment_invoice=Prepayment Invoice
self_billed_invoice=Self-Billed Invoice
delcredere_invoice=Delcredere Invoice
factored_invoice=Factored Invoice
credit_invoice=Credit Invoice
credit_memo=Credit Memo
detour_billing=Detour Billing
third_party_consolidated_invoice=Third Party Consolidated Invoice
debit_invoice=Debit Invoice
debit_memo=Debit Memo

# Edit : Product
add_product=Add product
product_from_repository=Add invoice line

# View : portal task
open=Open
files=Visualize
tax_original=Tax original
demat_errors=E-invoicing errors
all_attached_files=All attached files
EDI=Interchange
EDIUNIT=EDI Unit
ERROR=Error
XML=XML
actions=Action on the invoice
no_actions=No actions available
legend_invoice=Invoiced
legend_refused=refused
legend_accepted=accepted
invoice=Invoice
info_confirm_action=Your invoice has been successfully generated
info_confirm_invoice=Invoice n\u00B0 {0} sent
info_confirm_before_leaving=If you have made any changes to the fields without confirming, your changes will be lost.
info_lengthy_operation=Potentially lengthy operation
invoice_add_attachment=Add an attachment

# Advance search
select=Select

# Error
error_duplicate_invoice_number=The invoice number already exist
error_empty_invoice_number=The invoice number should not be empty
error_empty_party_identifier=One of three values must be entered (Record number, Government reference, VAT code) : %1$s
error_empty_invoice_item_vat=VAT rate is required : %1$s
error_empty_invoice_item_details=Invoice item details can not be empty
error_in_invoice_generation=Error in invoice generation
error_invoice_file_missing=Electronic invoice not found. Please contact your administrator
error_invoice_export_birt_xls_missing=No Birt template is associated with the Excel type for the invoice. Contact support
error_invoice_export_birt_pdf_missing=Some invoices do not have an associated pdf file and no Birt template is associated with the Pdf type for the invoice. Contact support
error_missing_template = There is no Birt template configured, please contact your administrator.
error_invoice_download=Error download
error_invoice_not_attachment=This invoice has no attachment
error_date=The start date must be less than the end date
error_unit_price_negative=Unit amount can not be negative
error_total_price_negative=Total amount can not be negative
error_allwOrChrg_equals_zero=An allowance or charge can not be 0
error_allwOrChrg_miss_description=An allowance or charge must have a description
error_allwOrChrg_multi_edit=An allowance or charge is being edited
error_invoice_select_line=No invoice is selected
error_order_select_line=No order is selected
error_document_select_line=No document is selected
error_invoice_export_no_records=You can not export an empty list
error_total_lt_zero=The total of your invoice can be negativ
error_empty_invoice_item_gross_price=Gross unit price is required : {0}
error_empty_invoice_item_description=Description is required : {0}
error_alloworchrg_greater_price_item=The total discounts and charges can not be greater than the price of the product
error_invoice_item_quantity=The quantity can not be equal to zero
error_document_locked=Another user just lock the invoice, see timeline for more details
error_const_allocation_double=It is not possible to have a cost allocation number at the head, and a Cost allocation number on the line at the same time for an invoice
error_const_allocation_not_filed=All cost Allocation numbers in details must be field
error_asnNumber_double=It is not possible to have an ASN number at the head and an ASN number on the line at the same time for an invoice.
error_existing_vat_rate=It''s not possible to have more than one VAT breakdown rate of {0}%
parties_tabs=Parties tabs
editable_fields=Editable fields
tax_mandatory_header=VAT number mandatory for
detail=Details
header=Header
filter=Filter
edit=Edit
visible_fields=Visible fields
search=Search
advance_shipment_notice_ref=Edit advance shipment notice Ref.
asn_ord_number=Edit PO Number
complete_edition=Generate the comprehensive transcript
allowCompleteEdition=Enable comprehensive transcript

standard_dialog_header=Import an invoice (*.csv)
refused = Refused
pending = Pending
none = Unread
invoiced = Invoiced
PENDING = Pending
read = Read
accepted = Accepted
error = Error
total_net_chargeallowance=Net amount

#Correct Invoice
error_in_processus=Internal error, uncontrolled invoice
correct=Correct
diagnose=Diagnose
refused_manually=Refuse
forced=Force
title_correct_invoice=Correct your invoice N\u00B0
title_diagnose_invoice=View the reasons for your invoice rejection N\u00B0
description_correct_invoice= To correct your error, simply :
description_diagnose_invoice=The invoice was refused for the following reasons :
tip_one_correct_invoice=Enter the following missing data according to the format of your invoices
tip_two_correct_invoice=Clock on the \"Correct\" button to apply the corrections and submit your invoice
process_error=Error with the process
error_process_not_deployed=Process not deployed
parsing_error=Error when parsing file
archiving_error=Error when archiving a file
correct_field_error=There are new fields to correct
correct_validation=The correction has been made
correct_file_not_found=Correction file not found
multiple_process_execution=Multiple process execution

invoice_escompte_condition_placeholder=Rebate condition
invoice_penalty_condition_default_value=Penalty for late payment: (PLEASE INDICATE THE RATE)% / month delay according to Law 92-1442 of 31/12/92
invoice_exo_tva_default_value=Exoneration VAT , art. 262 ter-I of the General Tax Code
invoiceHeader_invoiceParty_sellerParty_listOfIdentifier_identifier_ident=Identifier
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#1=Address identifier 1
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#2=Address identifier 2
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#3=Address identifier 3
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#4=Address identifier 4
invoiceHeader_invoiceParty_listOfPartyCoded_partyCoded_nameAddress_identifier#5=Address identifier 5
reference=Reference
undefined=Undefined
no_error_found=No error was found
force_cause_required=The cause is required
force_validation=Invoice {0} forced
title_force_invoice=Force your invoice N\u00B0
description_force_invoice=To force your invoice, please fill in the reason :
cause = Reason
force = Force

confirm_delete=Do you want to remove the invoice?
confirm_delete_selected=Do you want to remove the selected invoice(s)?

valueControl=Control list
strict=Strict

# Payment Items
CHECK=Cheque
BANK_ACCOUNT=Payment To Bank Account
PROMISSORY_NOTE=Promissory Note
MILKING=Bank Draft
CASH=Cash
BANK_CARD=Bank Card
CLEARING_BETWEEN_PARTNERS=Clearing Between Partners
CREDIT_TRANSFER=Credit Transfer
DEBIT_TRANSFER=Debit Transfer
DIRECT_DEBIT=Direct Debit
INSTRUMENT_NOT_DEFINED=Instrument Not Defined
OTHER_LCRPAPIER=Paper bill of exchange
OTHER_EFFETEMIS=Bank draft issued
OTHER_TRAITEACCEPTFOUR=Vendor acceptance bank draft
OTHER_VIREMENTETRANGER=Foreign bank transfer

# Exempt VAT Items
ART_262_TER=Article 262 ter-i of the CGI
ART_262=Article 262 I and II of the CGI
ART_275A=Article 275A of the CGI Franchising of VAT
ART_283=Article 283-1 of the CGI
ART_196=Article 196 of the VAT Directive

# history
history_action=Auditing

history_title=Audit
history_invoice_number=Invoice number
history_buyer=Buyer
history_bill_to=Bill to
history_status=Status
history_modified=Modified
history_time=At
history_by=By
history_none=No history found for invoice {0}
history_system=System

#PaymentConditions
invoice_penalty_terms_default_value=In the event of non-compliance with the deadline, interest on late payment may be charged at a rate equal to three times the legal interest rate.
invoice_penalty_condition_default_value=Penalty of 40 EUROS applied for non-payment due - art 441.6CC
invoice_escompte_condition_default_value=No discount for early payment

# number of selected invoices
one_invoice_selected = element selected
multiple_invoices_selected = elements selected
all_invoices_selected = All elements of the list are selected
select_all = Select All
unselect_all = Unselect All

warning_partially_editable=Warning! The visibility of the creationDate column cannot be changed.

#trigger actions
triggerActions_fieldset=Manage actions
triggerActions_add=Add
triggerActions_actionName_placeholder=Action name
triggerActions_global=Global
triggerActions_execute_info=Action ''{0}'' was called on invoice ''{1}''
triggerActions_execute_info_global=Action ''{0}'' was called on the selected invoices
triggerActions_complete_info=Action ''{0}'' was successfully executed on invoice ''{1}''
triggerActions_complete_info_multi=Action ''{0}'' was successfully executed for all selected invoices
triggerActions_statusError=This action is not available for an invoice with status ''{0}''
triggerActions_multiStatusError=This action is not available for invoices with status ''{0}''. This action was not launched for these invoices
triggerActions_generalError=An error occurred while executing the action. Please contact your administrator.
triggerActions_errorInProgress=An action is already in progress on this invoice
triggerActions_errorInProgress_multi=Attention, an action is already in progress on some invoices, these were not processed.
triggerActions_notAllDocumentsPresent=Not all documents could be processed.Please contact your administrator.
triggerActions_emptyAction=<Undefined>
triggerActions_noneSelected=List of authorized status
triggerActions_limitWarning=This action only allows 100 invoices to be selected. Please select up to 100 invoices.
security_fields_list=Base list security on fields
#StandardInvoiceEdition
invoice_portlet_selection=Add invoices portlet
invoice_portlet_visualization=Invoice viewer portlet
eReporting_portlet_selection=Add EReporting portlet
parties_info=Parties
notes=Notes
document_properties =Document properties
manage_notes =Manage Notes
control_list =Control List
payment_title_tab=Payment
allowance_or_charge_customer=Allowance or charge
add_allow_customer=Add allowance
add_charge_customer=Add charge
add_parafiscal_tax=Add parafiscal tax
add_parafiscal_tax_tooltip_line1=For an allowance, a charge or a parafiscal tax, for an amount, put the amount for one product.
add_parafiscal_tax_tooltip_line2=Be careful with the order of the lines.
add_parafiscal_tax_tooltip_line3=The Allowances, Charges and Taxes are cumulatives.
add_parafiscal_tax_tooltip_line4=For example: a product with a gross price to 10\u20AC.
add_parafiscal_tax_tooltip_line5=If we put a transport charge of 1\u20AC on the first line.
add_parafiscal_tax_tooltip_line6=Then a allowance of 10% on the second line.
add_parafiscal_tax_tooltip_line7=Final price will be: 10 + 1 = 11 - 10% = 9,9.
add_parafiscal_tax_tooltip_line8=But if we change the order of the lines.
add_parafiscal_tax_tooltip_line9=Final price will be: 10 -10% = 9 + 1 = 10
lines=Lines
footer_customer=Footer
error_in_conversion=Failed to create invoice. Please contact your administrator.
error_create_invoice=This is not possible to create an invoice as company user. Please log as a supplier if you want to create an invoice.
std_invoicenumber_title=Edition of the invoice N\u00B0
std_invoicenumber_title_add=Adding of the invoice N\u00B0
confirm_return=Warning, when leaving the edit page the corrected data will not be saved. Do you want to leave the edit page?
open_in_new_tab=Open in a new tab
display_pdf=Display PDF next to input area
standard_inv_edition_mode=Edition
standard_inv_visualization_mode=Portlet
invoice_control_portlet=Invoice control portlet
invoice_compliance_portlet=Invoice compliance portlet
portlet=Portlet
standard_erep_edition_mode=Edition
standard_inv_creation_mode=Creation
standard_erep_creation_mode=Creation
portlet_mode=Portlet mode
invoice_modification_status_label=Status update when edit is done
creation_edition_change_message=Warning: This modification is not recommended because changing the mode of use of this PT will cause problems if it is called in other PTs (Invoice, ASN, Orders ...)
standard_invoice_edition_status_list=List of status authorizing the edition
files_title=Attachments
ocr_verification_status_list=List of status authorizing the OCR verification only for jsf
invoice_compliance_actions=Invoice compliance actions configuration

#FormPage template
no_template=None

#Modiy
modifiable=Modify
ocr_validation_message=Validate
ocr_validation_message_content=Please note that following this validation, the coloring of the fields to be checked will be permanently removed. Make sure you have checked all the fields.

#Confirmation dialog
confirmation_title_creation=Shipping confirmation
confirmation_title_edit=Confirmation
confirmation_message_creation=Please confirm the sending of your invoice
confirmation_message_edit=Confirm the modifications made to the invoice
confirmation_number=n\u00B0
confirmation_to=to
confirmation_siren=SIREN:
siret=SIRET
confirmation_vat=TVA:
confirmation_information=with the following information:
amount_ht=- Tax total:
amount_tva=- VAT total:
amount_ttc=- Total including tax:
amount_payable=- Amount to be paid:
confirm_creation=Confirm and send
confirm_edit=Confirm

#OcrWarningDetails
ocr_warning_details=Please note that at least one field not visible on the screen must be checked on this line.

#Document Viewer
lock_action=Lock
unlock_action=Unlock
validate_action=Validate
addData_action=Add data
comment_action=Comment
refuse_action=Refuse
transferTask_action=Assign task
attachFile_action=Attach file
stopNotification_action=Stop notifications
give_back_control_action=Give back control
cancel_assign_task_action=Cancel the assign task
transferTask_checkboxes_message=The selection of at least one action is mandatory.
relaunch_reconciliation_action=Relaunch the reconciliation
reconciliation_not_found=No reconciliation found.

#Invoice Timeline
init_action_message=Automatic message: the document is associated with the workflow:
wkf_exit_action_message=Wokflow exit
attatch_file_action_message=attached a file to the invoice
attatch_file_action_message_comment=attached a file to the invoice with the following comment:
comment_action_message=commented the invoice:
comment_automatic_message=Automatic message: a comment has been added:
validate_action_message=validated the invoice
stopNotification_message=has paused reminder notifications
end_action_message=Automatic message: end of workflow
refuse_action_message=refused the invoice
refuse_action_message_comment=refused the invoice with the following comment:
enrich_action_message=enriched the invoice
lock_action_message=locked the invoice
unlock_action_message=unlocked the invoice
valid_action_automatic_message=Automatic message: Number of reminder reach, the step {0} is validated automatically
edit_action_message=edited the invoice
fix_action_message=corrected the invoice
reminder_action_automatic_message=Automatic message: Reminder N\u00BA {0} sent by email.
custom_action_message=has performed the action:
transferTask_action_message=assigned a task to
transferTask_action_message_comment = with the comment:
give_back_control_action_message=gave back control to
make_file_readable_checkbox_label=Use this file to replace the current readable file.
confirm_file_readable_message=There already exists a readable for this invoice, do you want to replace it?
cancel_assign_task_action_message=cancelled the task for
by_delegation_of_action_message=by delegation of
reconciliation_empty_seller_party_message=Reconciliation not possible : supplier code missing.
reconciliation_empty_order_number_message=Reconciliation not possible : order number missing.
reconciliation_order_not_found_message=Reconciliation not possible : order number not found.
reconciliation_empty_reception_number_message=Reconciliation not possible : reception number missing.
reconciliation_reception_not_found_message=Reconciliation not possible : reception number not found.
reconciliation_start_message=Reconciliation started.
reconciliation_success_message=Reconciliation done successful.
reconciliation_gap_found_message=Reconciliation done, differences found.
relaunch_reconciliation_message=Reconciliation relaunch
consult_message=Document was consulted.
control_fr_message=The controls applicable in France in 2024 have been applied to this document.
control_eu_message=The european controls have been applied to this document.
control_b2g2024_message=The controls applicable for a B2G invoice (first phase of the reform) have been applied to this document in accordance with the management rules ({0}) of appendix 7 of the B2B external specifications published by the French government.
control_b2b2024_message=The controls applicable for a B2B invoice (first phase of the reform) have been applied to this document in accordance with the management rules ({0}) of appendix 7 of the B2B external specifications published by the French government.
control_b2b_message=The controls applicable for a B2B invoice have been applied to this document in accordance with the management rules ({0}) of appendix 7 of the B2B external specifications published by the French government.
control_b2g_message=The controls applicable for a B2G invoice have been applied to this document in accordance with the management rules ({0}) of appendix 7 of the B2B external specifications published by the French government.
control_b2g2024_message_profile=The controls applicable for a B2G invoice profile ({1}) (first phase of the reform) have been applied to this document in accordance with the management rules ({0}) of appendix 7 of the B2B external specifications published by the French government.
control_b2b2024_message_profile=The controls applicable for a B2B invoice profile ({1}) (first phase of the reform) have been applied to this document in accordance with the management rules ({0}) of appendix 7 of the B2B external specifications published by the French government.
control_b2b_message_profile=The controls applicable for a B2B invoice profile ({1}) have been applied to this document in accordance with the management rules ({0}) of appendix 7 of the B2B external specifications published by the French government.
control_b2g_message_profile=The controls applicable for a B2G invoice profile ({1}) have been applied to this document in accordance with the management rules ({0}) of appendix 7 of the B2B external specifications published by the French government.

control_duplicate_message=Duplicate control has been applied.
aap_started_message=Accounting posting started
aap_na_message=Accounting posting not applicable
aap_manual_mode_message=Forced accounting posting in manual mode
aap_partial_message=Accounting posting partially completed
aap_missing_po_message=Accounting posting not possible: order missing
aap_unbalanced_message=Accounting posting not possible: unbalanced invoice
aap_missing_rcp_message=Accounting posting not possible: reception missing
aap_missing_ref_message=Accounting posting not possible: referential missing
control_modif_siren_client=Client SIREN was modified. Old value : {0}. New value : {1}
control_modif_siren_supplier=Supplier SIREN was modified. Old value : {0}. New value : {1}
control_modif_tvaintra_client=Client VAT number was modified. Old value : {0}. New value : {1}
control_modif_tvaintra_supplier=Supplier VAT number was modified. Old value : {0}. New value : {1}
legal_action_completed=The invoice was completed.
legal_action_payment_received=Invoice payment was received.
legal_action_approved=The invoice was approved.
legal_action_refused=Invoice was refused.
legal_action_approved_subrogate=The invoice was submitted to subrogation.
legal_action_payment_sent=Invoice payment was sent.
legal_action_factor=The invoice was submitted to factoring.
legal_action_factor_confidential=The invoice was submitted to confidential factoring.
aap_already_completed=Accounting posting non-modifiable (already completed)
aap_completed_automatically=Accounting posting completed automatically
control_schematron_message=The controls specified in schematron \"{0}\" have been applied.
actors_designation_message=has designated {0} as actor(s) of step {1}
ocr_verify_action_message=made the OCR verifications.
ocr_postponed_action_message=postponed the OCR verifications.

#EARLY PAYMENT
early_payment_seller_accept_add_timeline=: accepted the prepayment proposal. The invoice will be paid {0} days in advance, in exchange for a discount of {1}% which represents {2}\u20AC
early_payment_seller_reject_add_timeline=: declined the prepayment proposal with the following comment : \"{0}\"
ep_submited_message=: offers to pay the invoice {0} days before its due date in exchange for a discount.

#Workflow comment
comment_dialog_title=Add a comment
stop_notification_comment=Reminder notifications have been paused

#Workflow refuse
document_refused=The document is refused
refuse_message_mandatory=The reason of the refusal is mandatory
max_security_fields_selected_error_message=For performance reasons, it is only possible to select a maximum of 3 fields.
min_security_fields_selected_error_message=It's mandatory to select at least one actor of the invoice.

return=Return to the list
add_comment=Add a comment
validate_comment=Validate
confirm_refuse_doc_wkf=Do you really want to refuse this document?
confirm_refuse_doc_wkf_title=Confirm the refusal

#Workflow assign task
redirect_user_comment_message=Entering a comment is mandatory for this action. The comment will be forwarded to the person who will perform the task, be clear in your request.
assignTask_dialog_title=Assign a task
assignTask_dialog_actors=Which actor must do the task
assignTask_dialog_actions=What actions he has to do
assignTask_dialog_comment=Explain what you are waiting from him and why

#Archive statuses
UNDEFINED=Undefined
IN_SUBMISSION=In submission
SUBMITTED=Submitted
ARCHIVED=Archived
UNARCHIVABLE=Unarchivable

massValidation=Mass validation
unauthorizedValidation=One or more invoices have already been validated or locked by another user.

massRefusal=Mass refuse
unauthorizedRefuse=One or more invoices have already been validated, refused or locked by another user
canNotRefuse=One or more invoices have no refuse action
confirm_refuse_selected=Do you want to refuse the selected invoice(s)?
refusedMessage={0} invoice(s) successfuly refused

#quick search configuration
quick_search_settings=Settings of quick search
quick_search_full_text_option=Contains
quick_search_start_with=Starts with (default)
quick_search_strict=Strict
quick_search_disable=Disabled
quick_search_case_sensitive=Case sensitive
quick_search_warning_message=Attention, if there are many documents, the search will be long, it will slow down the flow of the portal and may cause problems with CPU consumption.

#freetext
additional_info=Additional information
editable=Editable
required=Required
hidden=Hidden
label_required=Value is required

#Editable BO properties
manageInvoiceReference=Manage references
addOtherParties=Add other party
manageAllowOrCharge=Manage allowance or charge
addDetail=Add line
editDetail=Edit line
deleteDetail=Delete line
manageActualPayment=Manage payments
editable_fields=Editable fields
mandatory_fields=Required fields
hidden_fields=Hidden fields
default_value=Default value
tooltip_default_value=Define default due date with today + X days
link_to_address_referential=Link to address book
link_to_contact_scope=Link to contact scope
display_warning_sellerPartyID=Attention, the field Identifier will only be editable for invoices belonging to an OCR process (acquisition = 'OCR')
manage_files=Add
link_to_referential=Link to referential
link_to_referential_tooltip=The information is searched in the address repository, except in the case of self-billing where the information is searched within the scope of the contact.
link_to_referential_tooltip_supplier=The information is searched within the scope of the contact, except in the case of self-billing where the information is searched in the address repository.

#Attachments
attachment=Attachments
attachment_name=Attachment name
comment=Comment
add_date=Added date
size=Size
received_on=Received on
error_no_demat_errors=This invoice has no demat error file
error_no_tax_original=No tax original found

#Chorus
DEB=VAT on debits
ENC=VAT on collection
EXEMPT=Exempt
A1=A1: Submission by a supplier of an invoice
A2=A2: Deposit of an invoice already paid
A9=A9: Submission of an invoice by a subcontractor
A12=A12: Submission of an invoice by a co-contractor
chorus_warn_pro_unavailable=ChorusPro services are not available right now. Please try again latter
chorus_structureId=Identifier
chorus_service=Service
chorus_structure_placeholder=Search structure by identifier or label..

#Reconciliation Dialog
rec_dld_header=Analysis of reconciliation discrepancies for invoice no.
rec_dld__header_of=of
rec_name_provider=Supplier name:
rec_order_number=Order n\u00B0
rec_reception_number=Reception n\u00B0
rec_invoice_number=Invoice n\u00B0
rec_creditNote_number=Credit Note n\u00B0
 #bottom
rec_differences_bottom_title=Reconciliation differences
rec_total_amount_excluding_tax=Total amount without tax
rec_total_amount_including_tax=Total amount including tax
rec_ordered=Ordered
rec_reception=Received
rec_invoiced=Invoiced
rec_invoice=Invoice
rec_receipt=Gap
rec_tolerance=Tolerance
rec_total=(Total)
 #lines	
rec_differences_invoice_line_title=Reconciliation differences at line level
rec_line_number=Line N\u00B0
rec_descpription=Description
rec_product_code=Product code
rec_quantity=Quantity
rec_unit_price=Unit price
rec_amount=Amount
rec_difference=Gap
rec_all_lines=See all lines
not_rec_all_lines=See only lines with gaps
rec_financial_gaps_tab=Financial gaps
rec_other_gaps_tab=Other gaps
rec_comment=Comment
rec_is_gap=Yes
rec_is_not_gap=No
rec_no_product_identifier=No product identifier found

select_rte_refuse_label=Post-refusal process
refuse_invoice_title=Refuse invoice

#button
rec_detected_deviation=See gaps
rec_reconcilation_done=Reconciliation success

validator_date_range=Set a time range between 0 and 90 days

lifeCycle=Life cycle of invoice no.
lifeCycleComplete=Life cycle details
see_more=see more

replay_workflow=Replay workflow after editing
confirm_new_workflow=This invoice must be associated with a new workflow. All steps performed on the old workflow will be lost. Do you confirm ?
confirm_no_workflow=This invoice is no longer subject to a workflow. All steps performed on the old workflow will be lost. Do you confirm ?
disconnected_action_message=Automatic message : the document is dissociated from the workflow:
reinit_action_message=Automatic message : workflow {0} has been replaced by workflow {1}

workflow_not_applicable_message=Workflow not applicable
reconciliation_not_applicable_message=Reconciliation not applicable

#Workflow designation of the actors for the next step
designate_next_step_actors_dialog=Designation of the actors for the next step
ok_btn=OK
discard_btn=Discard

ocr_verify=OCR Verify
rossum_fail=Can''t open Rossum studio : {0}.

duplicate_check_error=Unicity rule of french invoice are not observed. (invoice number, year, from)

display_warning_params_only_in_angular=Warning! Some parameters will only be taken into account in Angular

invoiceHeader_invoiceParty_billToParty_partyTaxInformation_taxIdentifier_ident = Invoicee VAT code
invoiceHeader_invoiceNumber = Invoice number
invoiceHeader_invoiceReferences[0]_asnNumber_refNum = Delivery from number
invoiceHeader_invoiceReferences[0]_purchaseOrderReference_purchaseOrderDate = Order date
invoiceHeader_invoiceIssueDate = Invoice issue date
invoiceSummary_invoiceTotals_taxableValue_monetaryAmount = Total (Ex. VAT)
invoiceHeader_invoiceParty_sellerParty_nameAddress_name1 = Seller
invoiceHeader_invoiceReferences[0]_purchaseOrderReference_buyerOrderNumber = Order number
invoiceHeader_invoiceParty_buyerParty_nameAddress_name1 = Buyer
invoiceSummary_invoiceTotals_invoiceTotal_monetaryAmount = Total (Inc. VAT)
invoice_cost_allocation_number = Cost allocation number
invoiceHeader_invoiceReferences[0]_asnNumber_refDate = Delivery from date
invoiceHeader_invoiceCurrency_currencyCoded = Currency
invoiceHeader_invoiceParty_billToParty_nameAddress_name1 = Invoicee
invoiceHeader_invoiceParty_billToParty_partyID_ident = Invoicee code
invoiceSummary_invoiceTotals_totalAmountPayable_monetaryAmount = Amount payable
invoiceHeader_invoiceParty_sellerParty_partyTaxInformation_taxIdentifier_ident = Seller VAT code
invoiceHeader_invoiceDates_invoiceDueDate = Invoice due date
invoiceHeader_invoiceParty_sellerParty_partyID_ident = Seller code
invoiceHeader_invoiceParty_billToParty_nameAddress_country_countryCoded = Invoicee country
invoiceHeader_invoiceParty_shipToParty_partyID_ident = Ship to Ident
invoiceHeader_invoiceType_invoiceTypeCoded = Invoice type
invoiceHeader_invoiceParty_buyerParty_nameAddress_country_countryCoded = Buyer country
invoiceHeader_invoiceParty_buyerParty_partyTaxInformation_taxIdentifier_ident = Buyer VAT code
invoiceHeader_invoiceParty_sellerParty_nameAddress_country_countryCoded = Seller country
invoiceHeader_invoiceParty_buyerParty_partyID_ident = Buyer code
invoiceSummary_invoiceTotals_totalTaxAmount_monetaryAmount = Tax amount