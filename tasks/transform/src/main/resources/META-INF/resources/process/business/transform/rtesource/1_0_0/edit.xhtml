<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:cc="http://xmlns.jcp.org/jsf/composite"
    xmlns:p="http://primefaces.org/ui"
    xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:pt="http://xmlns.jcp.org/jsf/passthrough">


    <!-- INTERFACE -->
    <cc:interface name="rteSrouceEdit" />

    <!-- IMPLEMENATION -->
    <cc:implementation>
      #{cc.attrs.value.setSelectedInstance(cc.attrs.owner.project.workspace.name)}
        <ui:param name="taskViewModel"
            value="#{cc.attrs.type.getViewModel(cc.attrs.mode)}" />
        <p:outputPanel styleClass="form-group">
            <p:outputLabel styleClass="col-sm-3 control-label" value="#{tsksiglbls.disabled}" />
            <p:outputPanel styleClass="col-sm-8">
                <p:selectBooleanCheckbox value="#{cc.attrs.value.disabled}" styleClass="form-control" />
            </p:outputPanel>
        </p:outputPanel>        <!-- ** ENGINE ** -->
        <tsk:fieldset group="#{taskViewModel.getGroup('engine')}" renderFields="false" styleClass="psEngine">
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('engine')['timeout']}" />
          <!-- Source -->
            <p:outputPanel id="field" styleClass="form-group" >
          		<p:outputLabel id="fieldlabel" styleClass="col-sm-3 control-label"
					value="#{trftsklbls.RteSource_source} *" />
          		<p:outputPanel id="fieldvalue"  for="selectRte" styleClass="col-sm-5 cc-input">
          			<p:selectOneMenu id="selectRte" value="#{cc.attrs.value.source}" 
						required="true"
						converter="#{comApplicationHandler.getConverter(taskViewModel.getGroup('engine')['source'].converter)}"
						rendered="true" style="width:100%">
						 <p:ajax process="@this" listener="#{cc.attrs.value.onChangeSourceSelected()}" update="@(.psEditButton) @(.psDynamicPanel) @(.psIO)"/>
						    <f:selectItem itemLabel="#{trftsklbls.select_rte_source}" />
                            <f:selectItem itemLabel="#{trftsklbls.dynamic_rte}" itemValue="#{'isDynamic'}"/>
						<f:selectItems value="#{gnxTransformAppHandler.getTemplates(gnxInstanceService.getInstanceByCode(cc.attrs.owner.project.workspace.name), 'RTE_SOURCE', null, true)}" />
					</p:selectOneMenu>
          		</p:outputPanel>
          		<p:outputPanel>
                   <p:button icon="ui-icon-pencil" value="#{labels.button_edit}" styleClass="psEditButton"
                     		disabled="#{cc.attrs.value.selectedResource == null || cc.attrs.value.selectedResource == 'isDynamic'}"	href="#{cc.attrs.value.getRteStudioUrl()}" target="_blank" />
          		</p:outputPanel>
            </p:outputPanel>

            <p:outputPanel styleClass="psDynamicPanel">
                <p:outputPanel rendered="#{cc.attrs.value.source != null and cc.attrs.value.render(false, 'isDynamic')}">
                    <tsk:field
                            value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                            mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                            multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                            locales="#{cc.attrs.locales}"
                            defaultLocale="#{cc.attrs.defaultLocale}"
                            labelProvider="#{cc.attrs.labelProvider}"
                            inputStyleClass="dynamicRteValidator"
                            property="#{taskViewModel.getGroup('engine')['dynamicSource']}">
                    </tsk:field>
                </p:outputPanel>
            </p:outputPanel>

            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('engine')['engineConfig']}" />

            <p:outputPanel styleClass="form-group">
                <p:outputLabel value="#{labels.parameters}"
                    styleClass="col-sm-3 control-label" />
                <p:outputPanel styleClass="col-sm-8 cc-input">
                    <p:dataTable id="engineParams" value="#{cc.attrs.value.params}"
                        var="par" editable="true" styleClass="psEngineParams"
                        emptyMessage="#{labels.no_records_found}">
                        <f:facet name="header">
                            <p:outputPanel styleClass="row">
                                <p:outputPanel styleClass="col-md-8" />
                                <p:outputPanel styleClass="col-md-4">
                                    <p:outputPanel styleClass="form-group right"
                                        style="padding-right:0px">
                                        <p:commandButton icon="fa fa-plus" value="#{labels.add}"
                                            immediate="true" process="@this" disabled="#{cc.attrs.readonly}"
                                            actionListener="#{cc.attrs.value.onAddEngineParam()}"
                                            update="@(.psEngineParams)"
                                            oncomplete="editLastDatatableRowRemoveCloseButton('engineParams')" />
                                    </p:outputPanel>
                                </p:outputPanel>
                            </p:outputPanel>
                        </f:facet>
                        <p:ajax event="rowEdit" process="@this"
                            listener="#{cc.attrs.value.onEditEngineParam(null)}" update="@(.psEngineParams)" disabled="#{cc.attrs.readonly}"/>
                        <p:ajax event="rowEditCancel" process="@this"
                            listener="#{cc.attrs.value.onCancelEngineParam(null)}" update="@(.psEngineParams)" disabled="#{cc.attrs.readonly}"/>

                        <p:column headerText="#{labels.name}">
                            <p:cellEditor>
                                <f:facet name="input">
                                    <p:inputText style="width:100%"
                                        validator="xssValidator"
                                        styleClass="form-control input-sm" value="#{par.key}"
                                        required="true"
                                        requiredMessage="#{labels.name} : #{gnxxcblinvlbls.value_required}" />
                                </f:facet>
                                <f:facet name="output">
                                    <h:outputText value="#{par.key}" />
                                </f:facet>
                            </p:cellEditor>
                        </p:column>
                        <p:column headerText="#{labels.value}" style="text-align: center">
                            <p:cellEditor>
                                <f:facet name="input">
                                    <p:inputText style="width:100%"
                                        validator="xssValidator"
                                        styleClass="form-control input-sm" value="#{par.value}"
                                        required="true"
                                        requiredMessage="#{labels.value} : #{gnxxcblinvlbls.value_required}" />
                                </f:facet>
                                <f:facet name="output">
                                    <h:outputText value="#{par.value}" />
                                </f:facet>
                            </p:cellEditor>
                        </p:column>
                        <p:column style="width:10%; text-align: center">
                            <p:rowEditor style="float:left;" />
                            <p:commandLink styleClass="ui-icon ui-icon-trash"
                                action="#{cc.attrs.value.onRemoveEngineParam(par)}"
                                process="@this" update="@(.psEngineParams)" />
                        </p:column>
                    </p:dataTable>
                </p:outputPanel>
            </p:outputPanel>
        </tsk:fieldset>
        <!-- ** INPUT ** -->
        <tsk:fieldset group="#{taskViewModel.getGroup('input')}"
            renderFields="false" styleClass="psIO">
            <!-- input? -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('input')['input']}"
                pAjaxEvent="change" pAjaxImmediate="false" pAjaxProcess="@this"
                pAjaxUpdate="@(.psIO)" />
            <!-- Document query -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                   defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('input')['contextualDocumentsQuery']}"
                rendered="#{cc.attrs.value.render(false, 'query')}" />
            <!-- File query -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('input')['query']}"
                rendered="#{cc.attrs.value.render(false, 'query')}" />
        </tsk:fieldset>
        <!-- ** OUTPUT: STANDARD ** -->
        <tsk:fieldset group="#{taskViewModel.getGroup('output')}"
            renderFields="false" styleClass="psIO psStdOut">
            <!-- CONTENT -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['stdout']}"
                inputOptions="#{cc.attrs.value.getOutputContentOptions(false, cc.attrs.locale)}"
                pAjaxEvent="change" pAjaxImmediate="true" pAjaxProcess="@this"
                pAjaxUpdate="@(.psStdOut)" />
            <!-- DOCUMENT -->
            <!-- documentClassName -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['documentClassName']}"
                rendered="#{cc.attrs.value.render(false, 'documentClassName')}" />
            <!-- documentProperties -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['documentProperties']}"
                rendered="#{cc.attrs.value.render(false, 'documentProperties')}" />
            <!-- nonPersistentDocProperties -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                       mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                       multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                       locales="#{cc.attrs.locales}"
                       defaultLocale="#{cc.attrs.defaultLocale}"
                       labelProvider="#{cc.attrs.labelProvider}"
                       property="#{taskViewModel.getGroup('output')['nonPersistentProperties']}"
                       rendered="#{cc.attrs.value.render(false, 'nonPersistentProperties')}"/>
            <!-- DOCUMENT FILE -->
            <!-- fileType -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileType']}"
                rendered="#{cc.attrs.value.render(false, 'fileType')}" />
            <!-- fileComment -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileComment']}"
                rendered="#{cc.attrs.value.render(false, 'fileComment')}" />
            <!-- fileDescription -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileDescription']}"
                rendered="#{cc.attrs.value.render(false, 'fileDescription')}" />
            <!-- fileSubtype -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileSubtype']}"
                rendered="#{cc.attrs.value.render(false, 'fileSubtype')}" />
            <!-- fileActionName -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileActionName']}"
                rendered="#{cc.attrs.value.render(false, 'fileActionName')}" />
            <!--fileNotInArchive-->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                       mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                       multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                       locales="#{cc.attrs.locales}"
                       defaultLocale="#{cc.attrs.defaultLocale}"
                       labelProvider="#{cc.attrs.labelProvider}"
                       property="#{taskViewModel.getGroup('output')['fileNotInArchive']}"
                       rendered="#{cc.attrs.value.render(false, 'fileNotInArchive')}" />
            
        </tsk:fieldset>
        <!-- ** OUTPUT: ERROR ** -->
        <tsk:fieldset group="#{taskViewModel.getGroup('error')}"
            renderFields="false" styleClass="psIO psStdErr">
            <!-- CONTENT -->
            <tsk:field id="errcontent" value="#{cc.attrs.value}"
                type="#{cc.attrs.type}" mode="#{cc.attrs.mode}"
                owner="#{cc.attrs.owner}" multilingual="#{cc.attrs.multilingual}"
                locale="#{cc.attrs.locale}" locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('error')['stderr']}"
                inputOptions="#{cc.attrs.value.getOutputContentOptions(true, cc.attrs.locale)}"
                pAjaxEvent="change" pAjaxImmediate="true" pAjaxProcess="@this"
                pAjaxUpdate="@(.psStdErr)" />
            <!-- DEAD -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('error')['dead']}"
                rendered="#{cc.attrs.value.render(true, 'dead')}" />
        </tsk:fieldset>
        
         <!-- repository -->
        <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup()['isAssociatedDocument']}"
                pAjaxEvent="change" pAjaxImmediate="false" pAjaxProcess="@this"
                pAjaxUpdate="@(.psAsscDocPanel)" />  
         
        <p:outputPanel styleClass="psAsscDocPanel">
	        <p:outputPanel rendered="#{cc.attrs.value.render(false, 'isAssociatedDocument')}">
	                <!-- ** ASSOCIATED DOCS: ** -->
	        <tsk:fieldset group="#{taskViewModel.getGroup('associatedDocument')}"
	            renderFields="false" styleClass="psAsscDoc">
	            <!-- repository -->
	            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
					mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
					multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
					locales="#{cc.attrs.locales}"
					defaultLocale="#{cc.attrs.defaultLocale}"
					labelProvider="#{cc.attrs.labelProvider}"
					property="#{taskViewModel.getGroup('associatedDocument')['associatedRepository']}"
					pAjaxEvent="change" pAjaxImmediate="false" pAjaxProcess="@this"
	                pAjaxUpdate="@(.psAsscDoc)" />
	            <!-- indexClassName -->
				<tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
					mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
					multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
					locales="#{cc.attrs.locales}"
					defaultLocale="#{cc.attrs.defaultLocale}"
					labelProvider="#{cc.attrs.labelProvider}"
					property="#{taskViewModel.getGroup('associatedDocument')['associatedIndexClass']}" 
					rendered="#{cc.attrs.value.render(false, 'associatedIndexClass')}" />
				 <!-- Associated Document query -->
	            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
	                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
	                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
	                locales="#{cc.attrs.locales}"
	                defaultLocale="#{cc.attrs.defaultLocale}"
	                labelProvider="#{cc.attrs.labelProvider}"
	                property="#{taskViewModel.getGroup('associatedDocument')['associatedFilter']}" 
					rendered="#{cc.attrs.value.render(false, 'associatedFilter')}" />
				 <!-- Associated Document properties -->
	            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
	                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
	                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
	                locales="#{cc.attrs.locales}"
	                defaultLocale="#{cc.attrs.defaultLocale}"
	                labelProvider="#{cc.attrs.labelProvider}"
	                property="#{taskViewModel.getGroup('associatedDocument')['associatedDocumentProperties']}" 
					rendered="#{cc.attrs.value.render(false, 'associatedDocumentProperties')}" />
                <!-- Associated non-persistent document properties -->
                <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                           mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                           multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                           locales="#{cc.attrs.locales}"
                           defaultLocale="#{cc.attrs.defaultLocale}"
                           labelProvider="#{cc.attrs.labelProvider}"
                           property="#{taskViewModel.getGroup('associatedDocument')['associatedDocNonPersistentProperties']}"
                           rendered="#{cc.attrs.value.render(false, 'associatedDocNonPersistentProperties')}" />
				 <!-- Log level -->
	            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
	                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
	                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
	                locales="#{cc.attrs.locales}"
	                defaultLocale="#{cc.attrs.defaultLocale}"
	                labelProvider="#{cc.attrs.labelProvider}"
	                property="#{taskViewModel.getGroup('associatedDocument')['associatedDocLogsLevel']}" 
					rendered="#{cc.attrs.value.render(false, 'associatedDocLogsLevel')}" />
				 <!-- hierarchical Associationl -->
	            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
	                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
	                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
	                locales="#{cc.attrs.locales}"
	                defaultLocale="#{cc.attrs.defaultLocale}"
	                labelProvider="#{cc.attrs.labelProvider}"
	                property="#{taskViewModel.getGroup('associatedDocument')['hierarchicalAssociation']}" 
					rendered="#{cc.attrs.value.render(false, 'hierarchicalAssociation')}" />
	        </tsk:fieldset>
	        
	        </p:outputPanel>
        </p:outputPanel>
    </cc:implementation>
</ui:component>
