<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://xmlns.jcp.org/jsf/core"
    xmlns:h="http://xmlns.jcp.org/jsf/html"
    xmlns:cc="http://xmlns.jcp.org/jsf/composite"
    xmlns:p="http://primefaces.org/ui"
    xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task"
    xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

    <!-- INTERFACE -->
    <cc:interface name="rteSrouceEdit" />

    <!-- IMPLEMENATION -->
    <cc:implementation>
        <ui:param name="taskViewModel"
            value="#{cc.attrs.type.getViewModel(cc.attrs.mode)}" />
        <p:outputPanel styleClass="form-group">
            <p:outputLabel styleClass="col-sm-3 control-label" value="#{tsksiglbls.disabled}" />
            <p:outputPanel styleClass="col-sm-8">
                <p:selectBooleanCheckbox value="#{cc.attrs.value.disabled}" styleClass="form-control" disabled="true" />
            </p:outputPanel>
        </p:outputPanel>        <!-- ** ENGINE ** -->
        <tsk:fieldset group="#{taskViewModel.getGroup('engine')}" renderFields="false" styleClass="psEngine">
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('engine')['timeout']}"
                readonly="true" />

            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('engine')['source']}"
                readonly="true" />
                
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('engine')['engineConfig']}" 
                readonly="true" />
            
            <p:outputPanel styleClass="form-group">
                <p:outputLabel value="#{labels.parameters}"
                    styleClass="col-sm-3 control-label" />
                <p:outputPanel styleClass="col-sm-8 cc-input">
                    <p:dataTable id="engineParams" value="#{cc.attrs.value.params}"
                        var="par" styleClass="psEngineParams"
                        emptyMessage="#{labels.no_records_found}" editable="false" >
                        <p:column headerText="#{labels.name}">
                            <h:outputText value="#{par.key}" />
                        </p:column>
                        <p:column headerText="#{labels.value}" style="text-align: center">
                        	<h:outputText value="#{par.value}" />
                        </p:column>
                    </p:dataTable>
                </p:outputPanel>
            </p:outputPanel>
        </tsk:fieldset>
        <!-- ** INPUT ** -->
        <tsk:fieldset group="#{taskViewModel.getGroup('input')}"
            renderFields="false" styleClass="psIO">
            <!-- input? -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('input')['input']}"
                readonly="true"
                pAjaxEvent="change" pAjaxImmediate="false" pAjaxProcess="@this"
                pAjaxUpdate="@(.psIO)" />
            <!-- Document query -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('input')['contextualDocumentsQuery']}"
                rendered="#{cc.attrs.value.render(false, 'query')}"
                readonly="true" />
            <!-- File query -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('input')['query']}"
                rendered="#{cc.attrs.value.render(false, 'query')}" 
                readonly="true" />
        </tsk:fieldset>
        <!-- ** OUTPUT: STANDARD ** -->
        <tsk:fieldset group="#{taskViewModel.getGroup('output')}"
            renderFields="false" styleClass="psIO psStdOut">
            <!-- CONTENT -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['stdout']}"
                inputOptions="#{cc.attrs.value.getOutputContentOptions(false, cc.attrs.locale)}"
                readonly="true"
                pAjaxEvent="change" pAjaxImmediate="true" pAjaxProcess="@this"
                pAjaxUpdate="@(.psStdOut)" />
            <!-- DOCUMENT -->
            <!-- documentClassName -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['documentClassName']}"
                rendered="#{cc.attrs.value.render(false, 'documentClassName')}" />
            <!-- documentProperties -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['documentProperties']}"
                rendered="#{cc.attrs.value.render(false, 'documentProperties')}"
                readonly="true" />
            <!-- nonPersistentDocProperties -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                       mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                       multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                       locales="#{cc.attrs.locales}"
                       defaultLocale="#{cc.attrs.defaultLocale}"
                       labelProvider="#{cc.attrs.labelProvider}"
                       property="#{taskViewModel.getGroup('output')['nonPersistentProperties']}"
                       rendered="#{cc.attrs.value.render(false, 'nonPersistentProperties')}"
                       readonly="true"/>
            <!-- DOCUMENT FILE -->
            <!-- fileType -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileType']}"
                rendered="#{cc.attrs.value.render(false, 'fileType')}" />
            <!-- fileComment -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileComment']}"
                rendered="#{cc.attrs.value.render(false, 'fileComment')}" />
            <!-- fileDescription -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileDescription']}"
                rendered="#{cc.attrs.value.render(false, 'fileDescription')}" />
            <!-- fileSubtype -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileSubtype']}"
                rendered="#{cc.attrs.value.render(false, 'fileSubtype')}" />
            <!-- fileActionName -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('output')['fileActionName']}"
                rendered="#{cc.attrs.value.render(false, 'fileActionName')}" />
            <!--fileNotInArchive-->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                       mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                       multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                       locales="#{cc.attrs.locales}"
                       defaultLocale="#{cc.attrs.defaultLocale}"
                       labelProvider="#{cc.attrs.labelProvider}"
                       property="#{taskViewModel.getGroup('output')['fileNotInArchive']}"
                       rendered="#{cc.attrs.value.render(false, 'fileNotInArchive')}" />
            
        </tsk:fieldset>
        <!-- ** OUTPUT: ERROR ** -->
        <tsk:fieldset group="#{taskViewModel.getGroup('error')}"
            renderFields="false" styleClass="psIO psStdErr">
            <!-- CONTENT -->
            <tsk:field id="errcontent" value="#{cc.attrs.value}"
                type="#{cc.attrs.type}" mode="#{cc.attrs.mode}"
                owner="#{cc.attrs.owner}" multilingual="#{cc.attrs.multilingual}"
                locale="#{cc.attrs.locale}" locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('error')['stderr']}"
                readonly="true"
                inputOptions="#{cc.attrs.value.getOutputContentOptions(true, cc.attrs.locale)}"
                pAjaxEvent="change" pAjaxImmediate="true" pAjaxProcess="@this"
                pAjaxUpdate="@(.psStdErr)" />
            <!-- DEAD -->
            <tsk:field value="#{cc.attrs.value}" type="#{cc.attrs.type}"
                mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}"
                locales="#{cc.attrs.locales}"
                defaultLocale="#{cc.attrs.defaultLocale}"
                labelProvider="#{cc.attrs.labelProvider}"
                property="#{taskViewModel.getGroup('error')['dead']}"
                rendered="#{cc.attrs.value.render(true, 'dead')}" />
        </tsk:fieldset>
    </cc:implementation>
</ui:component>
