# /process/business/transform
# French (fr)
engine=Moteur
input=Entr\u00E9e
stdout=Sortie: Standard
output=Sortie: Standard
stderr=Sortie: Erreur
error=Sortie: Erreur
dead=Dead
documentClassName=Classe du document
documentProperties=Propri\u00E9t\u00E9s du document
nonPersistentProperties=Propri\u00E9t\u00E9s de document non persistantes
fileType=Type
fileComment=Commentaire
fileDescription=Description
fileSubtype=Subtype
fileActionName=Nom de l'action
fileNotInArchive=Ne pas inclure le fichier dans l'archive
index=Index
indexClassName=Classe de l'index
indexProperties=Propri\u00E9t\u00E9s de l'index
indexFile=Indexer les fichiers
transform=Transformation
contextualDocumentsQuery=Filtre des documents du contexte
associatedDocument= Document associ\u00E9

# RteSource
RteSource_input=Active
RteSource_contextualDocumentsQuery=Filtre des documents du contexte
RteSource_query=Filtre des fichiers du contexte
RteSource_engine=Moteur
RteSource_engineConfig=Configuration
RteSource_timeout=Timeout (secondes)
RteSource_source=Source
select_rte_source=S\u00E9lection du RTE
dynamic_rte=Dynamique RTE
RteSource_error_dynamic_rte=La valeur n'est pas valide
RteSource_warning_dynamic_rte=Entr\u00E9e activ\u00E9e
RteSource_dynamicSource=Choix du RTE dynamique
RteSource_stdout=Contenu
RteSource_stdoutLogsLevel=Niveau
RteSource_stderr=Contenu
RteSource_stderrLogsLevel=Niveau
RteSource_Debug  =Debug
RteSource_Info   =Info
RteSource_Warning=Avertissement
RteSource_Error  =Erreur
RteSource_Document    =Multi-document
RteSource_DocumentFile=Mono-document
RteSource_LogFile     =Fichier de log
RteSource_Logs        =Rapport
RteSource_Substitution= Substitution

RteSource_isAssociatedDocument= Document associ\u00E9
RteSource_associatedRepository= Stockage
RteSource_associatedFilter= Filtre des documents
RteSource_associatedDocumentProperties=Propri\u00E9t\u00E9s du document associ\u00E9
RteSource_associatedDocNonPersistentProperties=Propri\u00E9t\u00E9s de document associ\u00E9es non persistantes
RteSource_associatedDocLogsLevel= Niveau d'erreur
RteSource_hierarchicalAssociation= Association hi\u00E9rarchique
RteSource_associatedIndexClass=Classe de l'index


# Xml2Index
indexer = Indexation
Xml2Index_namespaceDeclarations = D\u00E9claration des Namespaces
Xml2Index_valueProperty = Nom de la propri\u00E9t\u00E9 valeur
Xml2Index_omitRootAttributes = Ignorer les attributs de la racine
Xml2Index_collection = Collection
Xml2Index_namespaceSeparator = S\u00E9parateur de Namespace
Xml2Index_contextualDocumentsQuery = Filtre des documents
Xml2Index_query = Filtre des fichiers
Xml2Index_autoPrimitive = D\u00E9tection des types primitifs
Xml2Index_autoArray = D\u00E9tection des tableaux
Xml2Index_datePatterns = Formats des dates
Xml2Index_omitRoot = Ignorer l'\u00E9l\u00E9ment racine
Xml2Index_timeZone = Time zone des dates
Xml2Index_namespacePrefix = Pr\u00E9fixe des Namespaces
Xml2Index_attributePrefix = Pr\u00E9fixe des attributs

# PDF Container
filename=Nom du fichier
queryPdf=Filtre sur les fichiers PDF
queryPdfPlaceHolder=type = PDF
queryMetadata=Filtre sur les m\u00E9tadonn\u00E9s
queryMetadataPlaceHolder=type = XCBL

# PDF Container Extract
PdfContainerExtract = Extraction conteneur PDF
PdfContainerExtract_fileType = Type des fichiers de sortie

# GS1 PDF Extraction
queryXml=Filtre sur le fichier
queryXmlPlaceHolder=type = XML
fileName=Nom du fichier
queryComment= Filtre sur le commentaire
verifySignature=V\u00E9rifier signature

# GS1 PDF Injection
queryMetadataPdf=Filtre sur le PDF
queryMetadataXml=Filtre sur le XML
queryPdfComment= Filtre sur les commentaires du PDF
queryXmlComment= Filtre sur les commentaires du XML
useClientCertificate=Utiliser le certificat du client

# PDF To Image
generationStrategy= G\u00E9n\u00E9ration
systematic= G\u00E9n\u00E9ration syst\u00E9matique
replace_if_exists= G\u00E9n\u00E9ration avec remplacement
no_generation_if_exists= pas de g\u00E9n\u00E9ration si l'image existe
compressionType=Type de compression
dpi=Point par pouce (DPI)
config=Configuration

# BIRT
Birt_contextualDocumentsQuery=Filtre des documents du contexte
Birt_query=Filtre des fichiers du contexte
Birt_engine=Moteur
Birt_engineConfig=Configuration
Birt_timeout=Timeout (secondes)
Birt_source=Source
selectTemplateAutomatically=S\u00E9lectionner la source automatiquement
partnerField=Champ du partenaire

#FacturXInsert
FacturXInsert_convert=Convertir en PDF/A/3
FacturXInsert_format=Format
FacturXInsert_version=Version
FacturXInsert_profile=Profile

Synchrolink_source = Source
Synchrolink_toDirectory = R\u00E9pertoire de sortie
Synchrolink_stdout = Contenu
Synchrolink_outputStatus = Statut du document
Synchrolink_fileComment = Commentaire
Synchrolink_documentStatus = Output status
Synchrolink_inputType = Type du fichier d'entr\u00E9e
fileInArchive = Inclure le fichier dans l'archive
Synchrolink_outputType = Type du fichier de sortie
Synchrolink_engineConfig = Configuration
Synchrolink_outputDirectory = Directory
Synchrolink_engineParams = Param\u00E8tre
Synchrolink_query = Filtre des documents du contexte
Synchrolink_contextualDocumentsQuery = Filtre des documents du contexte