# /process/business/transform
# English (en)
engine=Engine
input=Input
stdout=Output: Standard
output=Output: Standard
stderr=Output: Error
error=Output: Error
dead=Dead
documentClassName=Document class
documentProperties=Document properties
nonPersistentProperties=Non-persistent document properties
fileType=Type
fileComment=Comment
fileDescription=Description
fileSubtype=Subtype
fileActionName=Action name
fileNotInArchive=Don't include this file in archive
index=Index
indexClassName=Index class
indexProperties=Index properties
indexFile=Index file
transform=Transformation
contextualDocumentsQuery=Document filter
associatedDocument= Associated document

# RteSource
RteSource_input=Enabled
RteSource_contextualDocumentsQuery=Context document filter
RteSource_query=Context document file filter
RteSource_engine=Engine
RteSource_engineConfig=Configuration
RteSource_timeout=Timeout (seconds)
RteSource_source=Source
select_rte_source=Choose RTE
dynamic_rte=Dynamic RTE
RteSource_dynamicSource=Choice of dynamic RTE
RteSource_error_dynamic_rte=The value is not valid
RteSource_warning_dynamic_rte=Input enabled
RteSource_stdout=Content
RteSource_stdoutLogsLevel=Level
RteSource_stderr=Content
RteSource_stderrLogsLevel=Level
RteSource_Debug  =Debug
RteSource_Info   =Info
RteSource_Warning=Warning
RteSource_Error  =Error
RteSource_Document    =Multiple Document
RteSource_DocumentFile=Single Document
RteSource_LogFile     =Log File
RteSource_Logs        =Report
RteSource_Substitution= Substitution

RteSource_isAssociatedDocument= Associated document
RteSource_associatedRepository= Repository
RteSource_associatedFilter= Document filter
RteSource_associatedDocumentProperties= Associated document properties
RteSource_associatedDocNonPersistentProperties=Non-persistent associated document properties
RteSource_associatedDocLogsLevel= Log level
RteSource_hierarchicalAssociation= Hierarchical association
RteSource_associatedIndexClass=Index class

# Xml2Index
indexer = Indexer
Xml2Index_namespaceDeclarations = Namespace declaration
Xml2Index_valueProperty = Value property name
Xml2Index_omitRootAttributes = Omit root's attributes
Xml2Index_collection = Collection
Xml2Index_namespaceSeparator = Namespace separator
Xml2Index_contextualDocumentsQuery = Document filter
Xml2Index_query = Document files filter
Xml2Index_autoPrimitive = Detect primitive
Xml2Index_autoArray = Detect arrays
Xml2Index_datePatterns = Date patterns
Xml2Index_omitRoot = Omit root element
Xml2Index_timeZone = Time zone of the dates
Xml2Index_namespacePrefix = Namespace prefix
Xml2Index_attributePrefix = Attribute prefix

# PDF Container
filename=File name
queryPdf=Filter on PDF files
queryPdfPlaceHolder=type = PDF
queryMetadata=Filter on metadata
queryMetadataPlaceHolder=type = XCBL

# PDF Container Extract
PdfContainerExtract = PDF container extraction
PdfContainerExtract_fileType = Output file type

# GS1 PDF Extraction
queryXml=Filter on XML files
queryXmlPlaceHolder=type = XML
fileName=File name
queryComment= Filter on comment
verifySignature=Check signature

# GS1 PDF Injection
queryMetadataPdf=Filter on PDF
queryMetadataXml=Filter on XML
queryPdfComment= Filter on PDF comment
queryXmlComment= Filter on XML comment
useClientCertificate=Use client certificate

# PDF To Image
generationStrategy= Generation
systematic= Always create the image
replace_if_exists= Replace older images
no_generation_if_exists= No image if it exists already
compressionType=Compression type
dpi=Dot per inch (DPI)
config=Configuration

# BIRT
Birt_contextualDocumentsQuery=Document filter
Birt_query=Document files filter
Birt_engine=Engine
Birt_engineConfig=Configuration
Birt_timeout=Timeout (seconds)
Birt_source=Source
selectTemplateAutomatically=Select source automatically
partnerField=Partner field

#FacturXInsert
FacturXInsert_convert=Convert to PDF A/3
FacturXInsert_format=Format
FacturXInsert_version=Version
FacturXInsert_profile=Profile

Synchrolink_source = Source
Synchrolink_toDirectory = Output folder
Synchrolink_stdout = Content
Synchrolink_outputStatus = Document status
Synchrolink_fileComment = Comment
Synchrolink_documentStatus = Output status
Synchrolink_inputType = Input file type
fileInArchive = Include file in archive
Synchrolink_outputType = Output file type
Synchrolink_engineConfig = Configuration
Synchrolink_outputDirectory = Directory
Synchrolink_engineParams = Parameter
Synchrolink_query = Document Files Filter
Synchrolink_contextualDocumentsQuery = Contextual documents query