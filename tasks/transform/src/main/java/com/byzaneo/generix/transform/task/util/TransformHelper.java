package com.byzaneo.generix.transform.task.util;

import com.byzaneo.commons.util.*;
import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.transform.task.business.FacturXInsertTask;
import com.byzaneo.generix.transform.task.business.PdfToImageTask.CompressionType;
import com.byzaneo.transform.engine.TransformEngineBuilder;
import com.byzaneo.xtrade.bean.DocumentFile;
import org.activiti.engine.delegate.DelegateExecution;
import org.apache.commons.io.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.jempbox.xmp.*;
import org.apache.jempbox.xmp.pdfa.XMPSchemaPDFAId;
import org.apache.pdfbox.cos.*;
import org.apache.pdfbox.pdmodel.*;
import org.apache.pdfbox.pdmodel.common.PDMetadata;
import org.apache.pdfbox.pdmodel.common.filespecification.*;
import org.apache.pdfbox.pdmodel.font.*;
import org.apache.pdfbox.pdmodel.graphics.color.PDOutputIntent;
import org.apache.pdfbox.preflight.*;
import org.apache.pdfbox.preflight.exception.SyntaxValidationException;
import org.apache.pdfbox.preflight.parser.PreflightParser;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.xerces.dom.DOMInputImpl;
import org.apache.xerces.impl.dv.util.Base64;
import org.mustangproject.ZUGFeRD.*;
import org.slf4j.Logger;
import org.w3c.dom.*;
import org.w3c.dom.ls.*;
import org.xml.sax.SAXException;

import javax.imageio.*;
import javax.imageio.stream.ImageOutputStream;
import javax.xml.XMLConstants;
import javax.xml.parsers.*;
import javax.xml.transform.*;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.*;
import javax.xml.validation.*;
import javax.xml.xpath.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.MalformedURLException;
import java.nio.file.*;
import java.nio.file.attribute.*;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static com.byzaneo.xtrade.process.VariableHelper.getNonPersistentProperties;
import static java.util.Collections.emptyMap;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * <AUTHOR>
 */
public class TransformHelper {

  private static final Logger log = getLogger(TransformHelper.class);

  private static final String STANDARD_PROFILE = "sRGB IEC61966-2.1";

  public static final String GS1_PDF_XPathExpression = "/StandardBusinessDocument/Signature/Object[@Id='GS1_PDF']";

  public static final String GS1_PDF_NODE_PARENT = "/StandardBusinessDocument/Signature";

  public static final String XSD_PATH = "schemas/sbdh/StandardBusinessDocumentHeader.xsd";

  public static final String GS1_PDF_ID = "GS1_PDF";

  public enum ImageType {
    JPG,
    TIF,
    TIFF,
    JPEG
  }

  private TransformHelper() {
    throw new AssertionError();
  }

  public static File transformPdfToImage(File pdf, ImageType type) throws IOException {
    return transformPdfToImage(pdf, type, CompressionType.JPEG, 300);
  }

  public static File transformPdfToImage(File pdf, ImageType type, CompressionType compressionType, Integer dpi) throws IOException {
    Assert.notNull(pdf, "File is required");
    Assert.notNull(type, "Image type is required");

    String filenameWithoutExtn = FileHelper.getFileNameWithoutExt(pdf);
    File destinationFile = new File(pdf.getParentFile(), filenameWithoutExtn + "." + type.name()
        .toLowerCase());
    ImageOutputStream ios = null;
    ImageWriter writer = null;
    try (PDDocument document = PDDocument.load(pdf)) {
      ios = ImageIO.createImageOutputStream(destinationFile);
      writer = ImageIO.getImageWritersByFormatName(type.name())
          .next();
      writer.setOutput(ios);
      ImageWriteParam param = writer.getDefaultWriteParam();
      param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
      param.setCompressionType(compressionType.toString());
      param.setCompressionQuality(0.7f);

      PDFRenderer renderer = new PDFRenderer(document);
      int totalPages = document.getNumberOfPages();
      if (log.isDebugEnabled()) {
        log.debug("Total pages to be converted of file ({}) -> {}", filenameWithoutExtn, totalPages);
      }

      int pageNumber = 1;
      for (int count = 0; count < totalPages; count++) {
        BufferedImage image = renderer.renderImageWithDPI(count, dpi,
            org.apache.pdfbox.rendering.ImageType.RGB);
        BufferedImage blackAndWhite = new BufferedImage(image.getWidth(), image.getHeight(),
            BufferedImage.TYPE_3BYTE_BGR);

        Graphics2D g2 = blackAndWhite.createGraphics();
        g2.drawImage(image, 0, 0, null);
        g2.rotate(45);
        IIOImage img = new IIOImage(blackAndWhite, null, null);
        if (pageNumber == 1) {
          writer.write(null, img, param);
        }
        else {
          writer.writeInsert(-1, img, param);
        }
        image.flush();
        g2.dispose();
        pageNumber++;
      }
      return destinationFile;
    }
    catch (IllegalArgumentException | IOException e) {
      log.error("Failed to transform file {} to image {} : {}", filenameWithoutExtn, type, e.getMessage());
      FileHelper.deleteQuietly(destinationFile);
      return null;
    }
    finally {
      if (ios != null) {
        ios.flush();
        ios.close();
      }
      if (writer != null) {
        writer.dispose();
      }
    }
  }

  public static void embbededFileInPdf(File result, File pdf, Path embbeded) throws IOException {
    try (PDDocument document = PDDocument.load(pdf); InputStream is = new FileInputStream(embbeded.toFile())) {
      if (document.isEncrypted()) {
        log.error("Error: Cannot add metadata to encrypted document.");
        throw new IOException("Error: Cannot add metadata to encrypted document.");
      }
      PDEmbeddedFilesNameTreeNode efTree = new PDEmbeddedFilesNameTreeNode();
      PDComplexFileSpecification fs = new PDComplexFileSpecification();
      fs.setFile(FilenameUtils.getName(embbeded.toFile()
          .getAbsolutePath()));
      PDEmbeddedFile ef = new PDEmbeddedFile(document, is);
      ef.setSubtype(getFileMimeType(embbeded));
      ef.setSize((int) FileHelper.sizeOf(embbeded.toFile()));
      ef.setCreationDate(getFileCreationDate(embbeded));
      fs.setEmbeddedFile(ef);
      Map<String, PDComplexFileSpecification> efMap = new HashMap<>();
      efMap.put(FilenameUtils.getName(embbeded.toFile()
          .getAbsolutePath()), fs);
      efTree.setNames(efMap);
      PDDocumentNameDictionary names = new PDDocumentNameDictionary(document.getDocumentCatalog());
      names.setEmbeddedFiles(efTree);
      document.getDocumentCatalog()
          .setNames(names);
      document.save(result);
    }
  }

  public static List<File> getEmbbededFileInPdf(File pdf) {
    try (PDDocument document = PDDocument.load(pdf)) {

      PDDocumentNameDictionary dictionarydocument = document.getDocumentCatalog()
          .getNames();
      if (dictionarydocument == null) {
        return null;
      }

      PDEmbeddedFilesNameTreeNode treeNode = dictionarydocument.getEmbeddedFiles();
      if (treeNode == null) {
        return null;
      }

      Map<String, PDComplexFileSpecification> embeddedFiles = treeNode.getNames();
      if (embeddedFiles == null) {
        return null;

      }
      String directory = createDirectory(pdf);
      if (directory == null) {
        return null;
      }
      return extractFiles(embeddedFiles, directory);
    }
    catch (IOException e) {
      log.warn("Failed to get the file {} : {}", pdf.getPath(), e.getMessage());
      return null;
    }
  }

  /**
   * @param pdf
   * @return
   */
  private static String createDirectory(File pdf) {
    try {
      return Files.createDirectories(Paths.get(pdf.getParent()
          .concat(File.separator)
          .concat(StringUtils.replace(pdf.getName(), ".", "_"))))
          .toString();
    }
    catch (IOException e) {
      log.error("Failed to create directory for {}:{}", pdf.getName(), e);
      return null;
    }
  }

  private static List<File> extractFiles(Map<String, PDComplexFileSpecification> embeddedFiles,
      String fileParentPath) {
    List<File> files = new ArrayList<>();

    for (Entry<String, PDComplexFileSpecification> entry : embeddedFiles.entrySet()) {
      File file = Paths.get(fileParentPath)
          .resolve(entry.getKey())
          .toFile();
      PDEmbeddedFile embeddedFile = getEmbeddedFile(entry.getValue());

      if (embeddedFile == null) {
        log.warn("Any embedded file found.");
        return null;
      }

      files.add(extractFile(file, embeddedFile));
    }

    return files;
  }

  private static File extractFile(File file, PDEmbeddedFile embeddedFile) {
    try (FileOutputStream fos = new FileOutputStream(file)) {
      fos.write(embeddedFile.toByteArray());
    }
    catch (Exception e) {
      log.error("Error during the process : {}", e.getMessage());
      return null;
    }
    return file;
  }

  private static PDEmbeddedFile getEmbeddedFile(PDComplexFileSpecification fileSpec) {
    if (fileSpec == null) {
      return null;
    }

    PDEmbeddedFile embeddedFile = fileSpec.getEmbeddedFileUnicode();

    if (embeddedFile != null) {
      return embeddedFile;
    }
    embeddedFile = fileSpec.getEmbeddedFileDos();

    if (embeddedFile != null) {
      return embeddedFile;
    }
    embeddedFile = fileSpec.getEmbeddedFileMac();

    if (embeddedFile != null) {
      return embeddedFile;
    }
    embeddedFile = fileSpec.getEmbeddedFileUnix();

    if (embeddedFile != null) {
      return embeddedFile;
    }
    return fileSpec.getEmbeddedFile();

  }

  public static Calendar getFileCreationDate(Path path) {
    Calendar calendar = Calendar.getInstance();
    try {
      calendar.setTimeInMillis(((FileTime) Files.getAttribute(path, "unix:ctime")).toMillis());
    }
    catch (UnsupportedOperationException | IOException e) {
      log.debug("Not an unix system");
    }
    BasicFileAttributes attributes;
    try {
      attributes = Files.readAttributes(path, BasicFileAttributes.class);
      calendar.setTimeInMillis(attributes.creationTime()
          .toMillis());
    }
    catch (IOException e) {
      log.debug("Failed to get file attributes : {}", e.getMessage());
      calendar.setTimeInMillis(System.currentTimeMillis());
    }
    return calendar;
  }

  public static String getFileMimeType(Path path) {
    try {
      return Files.probeContentType(path);
    }
    catch (IOException e) {
      log.error("Failed to get file mime type", e.getMessage());
      return "";
    }
  }

  public static String getPDFFromGS1PDFIn64ByXpath(String fileUri, String xPathExpression) {

    Document doc = buildSAXDocument(fileUri);
    XPathExpression expr = BuildXPathExpression(xPathExpression);

    try {
      return (String) expr.evaluate(doc, XPathConstants.STRING);
    }
    catch (XPathExpressionException e) {
      log.error("Failed to extract PDF from GS1 PDF", e.getMessage());
      return "";
    }

  }

  public static Document buildSAXDocument(String fileUri) {
    try {
      DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
      DocumentBuilder builder = factory.newDocumentBuilder();
      return builder.parse(fileUri);
    }
    catch (ParserConfigurationException | SAXException e) {
      log.error(String.format("Failed to parse the xml file %s", fileUri), e.getMessage());
      return null;
    }
    catch (IOException e) {
      log.error(String.format("Failed to find document at %s", fileUri), e.getMessage());
      return null;
    }
  }

  public static XPathExpression BuildXPathExpression(String expression) {
    try {
      XPathFactory xPathfactory = XPathFactory.newInstance();
      XPath xpath = xPathfactory.newXPath();
      return xpath.compile(expression + "/text()");
    }
    catch (XPathExpressionException e) {
      log.error(String.format("Failed to extract the content of the xml file at %s", expression), e.getMessage());
      return null;
    }
  }

  public static boolean decodedBase64(String encoded, File file) {
    try {
      byte[] decoded = Base64.decode(encoded);
      if (decoded == null) {
        log.error("Failed to decode in www.w3.org/2000/09/xmldsig#base64");
        return false;
      }

      FileUtils.writeByteArrayToFile(file, decoded);
      return true;
    }
    catch (IOException e) {
      log.error("Failed to decode in www.w3.org/2000/09/xmldsig#base64", e.getMessage());
    }
    return false;
  }

  public static String encodeBase64(File file) {
    try {
      return Base64.encode(FileUtils.readFileToByteArray(file));
    }
    catch (IOException e) {
      log.error("Failure during the process of PDF encoding", e.getMessage());
      return "";
    }
  }

  public static boolean isSignedGS1PDFValid(String signedGS1PDFPath, Schema schema) {
    String urlFile = "";
    try {

      urlFile = Paths.get(signedGS1PDFPath)
          .toUri()
          .toURL()
          .toString();
      Source xmlFile = new StreamSource(urlFile);

      Validator validator = schema.newValidator();
      validator.validate(xmlFile);

      if (checkIfManyGs1PdfElement(urlFile, GS1_PDF_XPathExpression)) {
        log.error("Invalid file : Many pdf found in {} ", urlFile);
        return false;
      }
      return true;
    }
    catch (SAXException e) {
      log.error("Failure during the process of validation", e.getMessage());
      return false;
    }
    catch (IOException e) {
      log.error(String.format("Failed to get file(s) %s , %s ", urlFile, XSD_PATH), e.getMessage());
      return false;
    }

  }

  public static Schema loadSchema(String schemaPath) {
    try {

      SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
      schemaFactory.setResourceResolver(new LocalSchemaLSResourceResolver());
      return schemaFactory.newSchema(
          new StreamSource(TransformHelper.class.getClassLoader()
              .getResource(schemaPath)
              .toExternalForm()));

    }
    catch (SAXException e) {
      log.error("Failure during validation schema loading, {}", e.getMessage());
      return null;
    }
  }

  public static class LocalSchemaLSResourceResolver implements LSResourceResolver {

    @Override
    public LSInput resolveResource(String type, String namespaceURI, String publicId, String systemId,
        String baseURI) {

      LSInput input = new DOMInputImpl();
      try {
        InputStream fis = loadFileToStream(systemId, baseURI);
        input.setByteStream(fis);
        input.setSystemId(systemId);
        input.setBaseURI(baseURI);
        return input;
      }
      catch (IOException ex) {
        log.error("Failed to get file", baseURI, ex);
        return null;
      }
    }

    protected InputStream loadFileToStream(String systemId, String baseURI)
        throws IOException, MalformedURLException {
      InputStream fis;
      if (StringUtils.contains(baseURI, "!")) {
        Path base = Paths.get(new File(baseURI.split("!")[1]).getParent(), systemId);
        fis = TransformHelper.class.getClassLoader()
            .getResource(base.toString())
            .openStream();
      }
      else {
        Path base = Paths.get(new File(baseURI).getParent()
            .replace("file:\\", ""), systemId);
        fis = base.toUri()
            .toURL()
            .openStream();
      }
      return fis;
    }
  }

  public static boolean checkIfManyGs1PdfElement(String fileUri, String xPathExpression) {
    return getNodeListFromExpression(fileUri, xPathExpression).getLength() > 1;
  }

  public static void rewriteNodes(Document doc, File file) {
    try {
      TransformerFactory transformerFactory = TransformerFactory.newInstance();
      Transformer transformer = transformerFactory.newTransformer();
      DOMSource source = new DOMSource(doc);
      StreamResult result = new StreamResult(file);
      transformer.transform(source, result);
    }
    catch (TransformerException e) {
      log.error("Failure during the override of the file : {} ", file.getPath());
    }
  }

  public static NodeList getNodeListFromExpression(String fileUri, String xPathExpression) {
    try {
      Document doc = TransformHelper.buildSAXDocument(fileUri);
      XPathExpression expr = TransformHelper.BuildXPathExpression(xPathExpression);
      return (NodeList) expr.evaluate(doc, XPathConstants.NODESET);
    }
    catch (XPathExpressionException e) {
      return null;
    }
  }

  public static Node getNodeFromExpression(String gs1FilePath, String expression) {
    try {
      DocumentBuilderFactory f = DocumentBuilderFactory.newInstance();
      DocumentBuilder b = f.newDocumentBuilder();
      org.w3c.dom.Document doc = b.parse(new File(gs1FilePath));

      XPath xPath = XPathFactory.newInstance()
          .newXPath();

      return (Node) xPath.compile(expression)
          .evaluate(doc, XPathConstants.NODE);
    }
    catch (IOException e) {
      log.error("Cannot find file {} , {}", gs1FilePath, e.getMessage());
    }
    catch (XPathExpressionException | SAXException | ParserConfigurationException e) {
      log.error("Failed during evalutaion of GS1 file {}, {}", gs1FilePath, e.getMessage());
    }
    return null;
  }

  public static boolean hasPdfGs1Element(NodeList nodeList) {

    for (int i = 0; i < nodeList.getLength(); i++) {
      if (hasGs1PdfElement(nodeList.item(i))) {
        return true;
      }
    }
    return false;

  }

  public static boolean hasGs1PdfElement(Node node) {

    if ("ds:Object".equals(node.getNodeName())) {
      Element element = (Element) node;
      return "GS1_PDF".equals(element.getAttribute("Id"));
    }

    return false;
  }

  public static boolean isPdfGs1ElementEmpty(NodeList node) {
    for (int i = 0; i < node.getLength(); i++) {
      if (!isPdfGs1ElementEmpty(node.item(i))) {
        return false;
      }
    }
    return true;
  }

  private static boolean isPdfGs1ElementEmpty(Node node) {
    if ("ds:Object".equals(node.getNodeName())) {
      Element element = (Element) node;
      if ("GS1_PDF".equals(element.getAttribute("Id"))) {
        return StringUtils.isEmpty(element.getTextContent());
      }
    }
    return true;
  }

  public static void addNonPersistentPropertiesAsParameters(final TransformEngineBuilder builder, DelegateExecution execution,
      DocumentFile documentFile) {
    Map<String, Map<String, Object>> nonPersistentProperties = getNonPersistentProperties(execution);
    String docUuid = Optional.ofNullable(documentFile)
        .map(DocumentFile::getDocument)
        .map(com.byzaneo.xtrade.bean.Document::getUuid)
        .orElse(null);
    if (nonPersistentProperties.isEmpty() || docUuid == null)
      return;
    Map<String, String> properties = nonPersistentProperties.getOrDefault(docUuid, emptyMap())
        .entrySet()
        .stream()
        .collect(
            Collectors.toMap(
                Entry::getKey,
                entry -> String.valueOf(entry.getValue())
            ));
    ((Map<String, String>) builder.getContext()
        .getParameter(RteRuntime.PARAMETERS)).putAll(properties);
  }

  public static void addDocPropetiesAsParameters(final TransformEngineBuilder builder, DocumentFile dof) {
    if (dof == null) {
      return;
    }
    builder.getContext()
        .addParameter(RteRuntime.PARAMETERS, getGlobalParameters(dof));
  }

  public static Map<String, String> getGlobalParameters(DocumentFile dof) {
    if (dof == null) {
      return Collections.emptyMap();
    }
    com.byzaneo.xtrade.bean.Document document = dof.getDocument();
    Map<String, String> globalParameters = new HashMap<>();
    SimpleDateFormat sdf = new SimpleDateFormat("dd.MM.yyyy HH:mm");

    if (StringUtils.isNotEmpty(document.getOwners())) {
      globalParameters.put("DOC.OWNERS", document.getOwners());
    }

    if (StringUtils.isNotEmpty(document.getFrom())) {
      globalParameters.put("DOC.FROM", document.getFrom());
    }

    if (StringUtils.isNotEmpty(document.getTo())) {
      globalParameters.put("DOC.TO", document.getTo());
    }

    if (StringUtils.isNotEmpty(document.getReference())) {
      globalParameters.put("DOC.REFERENCE", document.getReference());
    }

    if (document.getStatus() != null) {
      globalParameters.put("DOC.STATUS", document.getStatus()
          .getStatusCode());
    }

    if (document.getCreationDate() != null) {
      globalParameters.put("DOC.CREATION.DATE", sdf.format(document.getCreationDate()));
    }

    if (document.getModificationDate() != null) {
      globalParameters.put("DOC.MODIFICATION.DATE", sdf.format(document.getModificationDate()));
    }

    if (document.getStage() != null) {
      globalParameters.put("DOC.STAGE", document.getStage()
          .name());
    }

    if (StringUtils.isNotEmpty(document.getType())) {
      globalParameters.put("DOC.TYPE", document.getType());
    }

    if (StringUtils.isNotEmpty(document.getSubtype())) {
      globalParameters.put("DOC.SUBTYPE", document.getSubtype());
    }

    if (StringUtils.isNotEmpty(document.getNumber())) {
      globalParameters.put("DOC.NUMBER", document.getNumber());
    }

    if (StringUtils.isNotEmpty(document.getUuid())) {
      globalParameters.put("DOC.UUID", document.getUuid());
    }

    if (StringUtils.isNotEmpty(document.getTransport())) {
      globalParameters.put("DOC.TRANSPORT", document.getTransport());
    }

    if (StringUtils.isNotEmpty(document.getPurpose())) {
      globalParameters.put("DOC.PURPOSE", document.getPurpose());
    }

    if (StringUtils.isNotEmpty(document.getTranslator())) {
      globalParameters.put("DOC.TRANSLATOR", document.getTranslator());
    }

    if (StringUtils.isNotEmpty(document.getThirdParty())) {
      globalParameters.put("DOC.THIRDPARTY", document.getThirdParty());
    }

    if (StringUtils.isNotEmpty(document.getNature())) {
      globalParameters.put("DOC.NATURE", document.getNature());
    }

    if (StringUtils.isNotEmpty(document.getImmatpdp())) {
      globalParameters.put("DOC.IMMATPDP", document.getImmatpdp());
    }

    if (StringUtils.isNotEmpty(document.getB2gRules())) {
      globalParameters.put("DOC.B2G_RULES", document.getB2gRules());
    }

    if (StringUtils.isNotEmpty(document.getEreportingOption())) {
      globalParameters.put("DOC.EREPORTING_OPTION", document.getEreportingOption());
    }

    if (StringUtils.isNotEmpty(document.getCountryRules1())) {
      globalParameters.put("DOC.COUNTRY_RULES_1", document.getCountryRules1());
    }

    if (StringUtils.isNotEmpty(document.getCountryRules2())) {
      globalParameters.put("DOC.COUNTRY_RULES_2", document.getCountryRules2());
    }

    if (StringUtils.isNotEmpty(document.getUseCase())) {
      globalParameters.put("DOC.USE_CASE", document.getUseCase());
    }

    if (StringUtils.isNotEmpty(document.getBillingFramework())) {
      globalParameters.put("DOC.BILLING_FRAMEWORK", document.getBillingFramework());
    }

    if (StringUtils.isNotEmpty(document.getSovosId())) {
      globalParameters.put("DOC.SOVOS_ID", document.getSovosId());
    }

    if (StringUtils.isNotEmpty(document.getSovosStatus())) {
      globalParameters.put("DOC.SOVOS_STATUS", document.getSovosStatus());
    }

    if (StringUtils.isNotEmpty(document.getSovosTransactionId())) {
      globalParameters.put("DOC.SOVOS_TRANSACTION_ID", document.getSovosTransactionId());
    }

    if (StringUtils.isNotEmpty(document.getSovosStatusCode())) {
      globalParameters.put("DOC.SOVOS_STATUS_CODE", document.getSovosStatusCode());
    }

    if (StringUtils.isNotEmpty(document.getSovosNotificationId())) {
      globalParameters.put("DOC.SOVOS_NOTIFICATION_ID", document.getSovosNotificationId());
    }

    if (document.getArchiveExpirationDate() != null) {
      globalParameters.put("DOC.ARCHIVE_EXPIRATION_DATE", sdf.format(document.getArchiveExpirationDate()));
    }

    if (StringUtils.isNotEmpty(document.getRossumId())) {
      globalParameters.put("DOC.ROSSUM_ID", document.getRossumId());
    }

    if (document.getIssueDate() != null) {
      globalParameters.put("DOC.ISSUEDATE", sdf.format(document.getIssueDate()));
    }

    if (document.getLinCount() != null) {
      globalParameters.put("DOC.LIN.COUNT", document.getLinCount()
          .toString());
    }

    if (document.getEmail() != null) {
      globalParameters.put("DOC.EMAIL", document.getEmail());
    }

    if (document.getAcquisition() != null) {
      globalParameters.put("DOC.ACQUISITION", document.getAcquisition()
          .name());
    }

    if (document.getOrderingParty() != null) {
      globalParameters.put("DOC.ORDERING.PARTY", document.getOrderingParty());
    }

    if (dof.getCreationDate() != null) {
      globalParameters.put("DOF.CREATION.DATE", sdf.format(dof.getCreationDate()));
    }

    if (dof.getType() != null) {
      globalParameters.put("DOF.TYPE", dof.getType()
          .name());
    }

    if (StringUtils.isNotEmpty(dof.getComment())) {
      globalParameters.put("DOF.COMMENT", dof.getComment());
    }
    if (StringUtils.isNotEmpty(dof.getSubtype())) {
      globalParameters.put("DOF.SUBTYPE", dof.getSubtype());
    }
    if (StringUtils.isNotEmpty(dof.getDescription())) {
      globalParameters.put("DOF.DESCRIPTION", dof.getDescription());
    }
    if (StringUtils.isNotEmpty(dof.getActionName())) {
      globalParameters.put("DOF.ACTIONNAME", dof.getActionName());
    }
    if (dof.getInArchive() != null)
      globalParameters.put("DOF.INARCHIVE", String.valueOf(dof.getInArchive()));

    if (document.getConsultStatus() != null) {
      globalParameters.put("DOC.CONSULTSTATUS", document.getConsultStatus()
          .name());
    }

    if (document.getProcessingWay() != null) {
      globalParameters.put("DOC.PROCESSING_WAY", document.getProcessingWay()
          .name());
    }

    globalParameters.put("EDISEND.ORIGINAL.NAME", dof.getFile()
        .getName());

    globalParameters.put("EDISEND.ORIGINAL.FULLNAME", dof.getFile()
        .getAbsolutePath());
    if (StringUtils.isNotEmpty(document.getProfile())) {
      globalParameters.put("DOC.PROFILE", document.getProfile());
    }
    return globalParameters;
  }

  public static boolean isValidFileName(String text) {
    if (StringUtils.containsAny(text, '\\', '/')) {
      return false;
    }
    try {
      File tmp = new File(FileHelper.getTempDirectory(), text);
      if (tmp.createNewFile()) {
        tmp.delete();
      }
      return true;
    }
    catch (Exception e) {
    }
    return false;
  }

  /**
   * Create a {@link ZUGFeRDExporter Factur-x} {@code pdf} which is a {@code pdf} with an {@code xml} embedded. File is then saved in
   * {@code filePath}
   * 
   * @param pdf - {@link File}
   * @param xml - {@link File}
   * @param format - {@link FacturXInsertTask.Format}
   * @param version - {@link FacturXInsertTask.Version}
   * @param profile - {@link ZUGFeRDConformanceLevel}
   * @param os - {@link OutputStream}
   * @throws IOException
   */
  public static void exportZugferdFile(File pdf, File xml, FacturXInsertTask.Format format, FacturXInsertTask.Version version,
      ZUGFeRDConformanceLevel profile, OutputStream os, boolean ignorevalidationErrors) throws IOException {
    if (pdf == null || xml == null || format == null || profile == null || os == null) {
      throw new IOException("Mandatory parameters");
    }
    ZUGFeRDExporterFromA1Factory ze = new GnxZUGFeRDExporterFromA1Factory();
    if (ignorevalidationErrors) {
      // if the operating system doesn't have the font used to write this PDF the files won't be valid
      // for testing purposes we can skip the validation
      ze.ignorePDFAErrors();
    }
    if (FacturXInsertTask.Format.ZUGFERD.equals(format)) {
      ze.setZUGFeRDVersion(FacturXInsertTask.Version.ONE.equals(version) ? 1 : 2);
    }
    ZUGFeRDExporter zee = ze.setZUGFeRDConformanceLevel(profile)
        .load(Files.readAllBytes(pdf.toPath()));
    if (FacturXInsertTask.Format.FACTUR_X.equals(format)) {
      zee.setFacturX();
    }
    zee.setZUGFeRDXMLData(Files.readAllBytes(xml.toPath()));
    zee.export(os);
  }

  /**
   * Transform a {@code PDF A-1} to {@code PDF A-3}
   * 
   * @param pdf - {@link File}
   * @param os - {@link OutputStream}
   * @throws IOException
   */
  public static void convertA1ToA3(File pdf, OutputStream os) throws IOException {
    if (pdf == null || os == null) {
      return;
    }
    ZUGFeRDExporter ze = new ZUGFeRDExporterFromA1Factory().setAttachZUGFeRDHeaders(false)
        .load(Files.readAllBytes(pdf.toPath()));
    ze.export(os);
  }

  /**
   * Transform a {@code PDF} to {@code PDF/A}
   * 
   * @param pdf - {@link File}
   * @param os - {@link OutputStream}
   */
  public static void convertToPDFA(File pdf, OutputStream os) {
    if (pdf == null || os == null) {
      return;
    }
    PDDocument doc = null;
    try {
      doc = PDDocument.load(pdf);
      doc.getDocument()
          .setIsXRefStream(false);
      PDFont font = PDType0Font.load(doc, TransformHelper.class
          .getResourceAsStream("/pdfa/times.ttf"), true);
      font.subset();
      PDPageTree pages = doc.getDocumentCatalog()
          .getPages();
      for (PDPage page : pages) {
        if (page.getCOSObject()
            .getCOSDictionary(COSName.GROUP) != null &&
            page.getCOSObject()
                .getCOSDictionary(COSName.GROUP)
                .getItem(COSName.S) != null) {
          page.getCOSObject()
              .getCOSDictionary(COSName.GROUP)
              .setItem(COSName.S, COSName.GTS_PDFA1);
        }
        Iterable<COSName> fontNames = page.getResources()
            .getFontNames();
        for (COSName cosName : fontNames) {
          COSDictionary dict = page.getResources()
              .getFont(cosName)
              .getCOSObject();
          fixFont(font, dict, cosName);
          page.getResources()
              .getFont(cosName)
              .getFontDescriptor()
              .setCIDSet(font.getFontDescriptor()
                  .getCIDSet());
        }
      }

      PDDocumentCatalog cat = addMissingMetaData(doc);
      PDOutputIntent oi = createColorProfile(doc);
      cat.addOutputIntent(oi);

      doc.save(os);
    }
    catch (Exception e) {
      log.error("Could not convert file {} ", e);
    }
    finally {
      try {
        if (doc != null) {
          doc.close();
        }
      }
      catch (IOException e) {
        log.error("Failed to close document {}", e);
      }
    }
  }

  /**
   * @param font
   * @param page
   * @param cosName
   * @throws IOException
   */
  private static void fixFont(PDFont font, COSDictionary fontDictionary, COSName cosName) throws IOException {
    if (fontDictionary.getInt(COSName.FIRST_CHAR) == -1) {
      fontDictionary
          .setInt(COSName.FIRST_CHAR, font.getCOSObject()
              .getInt(COSName.FIRST_CHAR));
    }
    if (fontDictionary.getInt(COSName.LAST_CHAR) == -1) {
      fontDictionary
          .setInt(COSName.LAST_CHAR, font.getCOSObject()
              .getInt(COSName.LAST_CHAR));
    }
    if (fontDictionary.getItem(COSName.WIDTHS) == null) {
      fontDictionary
          .setItem(COSName.WIDTHS, font.getCOSObject()
              .getCOSArray(COSName.WIDTHS));
    }
    if (fontDictionary.getItem(COSName.FONT_DESC) == null) {
      fontDictionary.setItem(COSName.FONT_DESC, font.getCOSObject()
          .getCOSDictionary(COSName.FONT_DESC));
    }
  }

  /**
   * Adds missing metadata. All the data needed is already in {@link PDDocumentInformation}.
   * 
   * @param doc - {@link PDDocument}
   * @return {@link PDDocumentCatalog}
   * @throws IOException
   * @throws Exception
   */
  private static PDDocumentCatalog addMissingMetaData(PDDocument doc) throws IOException, Exception {
    PDMetadata metadata = new PDMetadata(doc);
    PDDocumentCatalog cat = doc.getDocumentCatalog();
    cat.setMetadata(metadata);
    PDDocumentInformation info = doc.getDocumentInformation();
    XMPMetadata xmp = new XMPMetadata();
    XMPSchemaDublinCore dc = xmp.addDublinCoreSchema();
    dc.setTitle(info.getTitle());
    dc.addCreator(info.getAuthor());
    XMPSchemaPDF sp = xmp.addPDFSchema();
    sp.setProducer(info.getProducer());
    XMPSchemaBasic sb = xmp.addBasicSchema();
    sb.setCreatorTool(info.getCreator());
    sb.setCreateDate(info.getCreationDate());
    sb.setModifyDate(info.getModificationDate());
    XMPSchemaPDFAId pdfaid = new XMPSchemaPDFAId(xmp);
    // Can be "A" or "B"
    pdfaid.setConformance("B");
    // Can be "1", "2", "3"
    // for now it only works with "1"
    pdfaid.setPart(1);
    pdfaid.setAbout("");
    xmp.addSchema(pdfaid);
    metadata.importXMPMetadata(xmp.asByteArray());
    return cat;
  }

  /**
   * Creates a color profile. It is mandatory to include the color profile used by the document
   * 
   * @param doc - {@link PDDocument}
   * @return {@link PDOutputIntent}
   * @throws FileNotFoundException
   * @throws IOException
   */
  private static PDOutputIntent createColorProfile(PDDocument doc) throws FileNotFoundException, IOException {
    InputStream colorProfile = TransformHelper.class
        .getResourceAsStream("/pdfa/sRGB.icc");
    PDOutputIntent oi = new PDOutputIntent(doc, colorProfile);
    oi.setInfo(STANDARD_PROFILE);
    oi.setOutputCondition(STANDARD_PROFILE);
    oi.setOutputConditionIdentifier(STANDARD_PROFILE);
    oi.setRegistryName("http://www.color.org");
    return oi;
  }

  /**
   * Verify if a {@code PDF} is {@code PDF/A}
   * 
   * @param file {@link File}
   * @return {@link ValidationResult}
   * @see https://pdfbox.apache.org/1.8/cookbook/pdfavalidation.html
   */
  public static ValidationResult isPDFA(File file) {
    ValidationResult r = null;
    PreflightDocument document = null;

    try {
      PreflightParser parser = new PreflightParser(file);

      /*
       * Parse the PDF file with PreflightParser that inherits from the NonSequentialParser. Some additional controls are present to check a
       * set of PDF/A requirements. (Stream length consistency, EOL after some Keyword...)
       */
      parser.parse();

      /*
       * Once the syntax validation is done, the parser can provide a PreflightDocument (that inherits from PDDocument) This document
       * process the end of PDF/A validation.
       */
      document = parser.getPreflightDocument();
      document.validate();

      // Get validation result
      r = document.getResult();

    }
    catch (SyntaxValidationException e) {
      /*
       * the parse method can throw a SyntaxValidationException if the PDF file can't be parsed. In this case, the exception contains an
       * instance of ValidationResult
       */
      r = e.getResult();
    }
    catch (IOException e) {
      log.error("File {} could not be found ", file.getName());
      return null;
    }
    finally {
      if (document != null) {
        try {
          document.close();
        }
        catch (IOException e) {
          log.error("Failed to close doucment {} ", e);
        }
      }
    }
    return r;
  }

}
