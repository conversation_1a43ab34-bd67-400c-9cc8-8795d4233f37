package com.byzaneo.generix.transform.task.business;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.service.MultiNodeService;
import com.byzaneo.commons.ui.util.*;
import com.byzaneo.commons.util.GsonHelper;
import com.byzaneo.generix.api.Variable;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.RteRuntime.RteEnv;
import com.byzaneo.generix.rtemachine.util.RteCommandeLineHelper;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.transform.rte.RteEngine;
import com.byzaneo.generix.transform.rte.RteEngine.Config;
import com.byzaneo.generix.util.FormPageHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.BqlParseException;
import com.byzaneo.security.bean.Company;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.api.TaskException;
import com.byzaneo.transform.engine.*;
import com.byzaneo.xtrade.InvalidStatusException;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.ipm.api.RestTrigger;
import com.byzaneo.xtrade.process.*;
import com.byzaneo.xtrade.process.el.FixedValue;
import com.byzaneo.xtrade.service.SharedLockingService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Getter;
import lombok.Setter;
import org.activiti.engine.delegate.*;
import org.apache.commons.cli.ParseException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.event.RowEditEvent;
import org.slf4j.Logger;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.persistence.Transient;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.net.MalformedURLException;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.byzaneo.commons.bean.FileType.RTE;
import static com.byzaneo.commons.ui.util.JSFHelper.getRequest;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.generix.edocument.task.util.DynamicTemplateHelper.getDynamicFile;
import static com.byzaneo.generix.service.TransformService.Transform.RTE_SOURCE;
import static com.byzaneo.generix.transform.rte.RteEngine.Config.ThrowOnError;
import static com.byzaneo.generix.transform.rte.RteEngine.Config.Timeout;
import static com.byzaneo.generix.transform.task.business.RteSourceTask.AssociatedDocLogLevel.Error;
import static com.byzaneo.generix.transform.task.business.RteSourceTask.AssociatedDocLogLevel.Warning;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.transform.engine.TransformEngineBuilder.engine;
import static com.byzaneo.xtrade.process.Variable.INPUT_DIR;
import static com.byzaneo.xtrade.process.Variable.OUTPUT_DIR;
import static com.byzaneo.xtrade.process.Variable.PROCESS_INPUT_DIR;
import static com.byzaneo.xtrade.process.Variable.PROCESS_OUTPUT_DIR;
import static com.byzaneo.xtrade.process.Variable.PROCESS_WORK_DIR;
import static com.byzaneo.xtrade.process.VariableHelper.*;
import static com.byzaneo.xtrade.process.VariableHelper.addNonPersistentPropertiesToExecution;
import static com.byzaneo.xtrade.service.BrokerMessageDelegatorService.MESSAGE_BROKER_TRIGGER_VARIABLE;
import static com.byzaneo.xtrade.ui.convert.ProcessExpressionConverter.CONVERTER_ID;
import static com.byzaneo.xtrade.util.DocumentHelper.searchFiles;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.util.concurrent.Executors.newSingleThreadExecutor;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.util.Assert.isTrue;
import static org.springframework.util.Assert.notNull;

/**
 * RTE source interpretation task thru the {@link RteEngine}.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Mar 11, 2016
 * @since 5.0 GNX-3722
 */
@Task(name = "RteSource", library = "process/business/transform", version = "1.0.0")
@TaskViewModel(labelFamily = RteSourceTask.LABEL_FAMILY, groups = { "engine", "input", "output", "error",
    "associatedDocument" }, excludes = { "engineParams", "outputDirectory", "documentStatus" }, overrides = {
    @TaskPropertyOverride(name = "source", property = @TaskProperty(group = "engine", required = true, options = "gnxTransformAppHandler.getTemplates(gnxInstanceService.getInstanceByCode(cc.attrs.owner.project.workspace.name), 'RTE_SOURCE', null, true)"))
})
public class RteSourceTask extends AbstractTransformTask {
  private static final long serialVersionUID = -2811372308200697923L;

  private static final Logger log = getLogger(RteSourceTask.class);

  private static final List<com.byzaneo.xtrade.process.Variable> EXECUTION_DIR_EXPOSED = Arrays.asList(INPUT_DIR, OUTPUT_DIR,
      PROCESS_INPUT_DIR, PROCESS_OUTPUT_DIR, PROCESS_WORK_DIR);

  private static final String CDV_VARIABLES_PREFIX = "CDV_";

  private static final List<String> PARAMETERS_PREFIXES = List.of(CDV_VARIABLES_PREFIX, RestTrigger.REST_TRIGGER_PARAMETERS_PREFIX, MESSAGE_BROKER_TRIGGER_VARIABLE);

  @TaskTransient
  private final transient RteService rteService;

  @TaskTransient
  private final transient SecurityService securityService;

  @TaskTransient
  protected transient InstanceService instanceService;

  private transient MultiNodeService multiNodeService;

  private transient SharedLockingService sharedLockingService;

  @TaskTransient
  private transient List<Parameter> params;

  @TaskTransient
  private transient boolean parameterEdit = false;

  @TaskProperty(type = Boolean.class)
  private Expression isAssociatedDocument = new FixedValue(FALSE.toString());

  @TaskProperty(group = "associatedDocument", required = true, type = Repository.class, converter = CONVERTER_ID)
  private Expression associatedRepository = new FixedValue(Repository.CONTEXT);

  @TaskProperty(group = "associatedDocument", required = true, options = "#{xtdIpmAppHandler.indexerTypeOptions()}")
  protected Expression associatedIndexClass;

  @TaskProperty(group = "associatedDocument", required = true, type = Query.class, model = "#{xtdIpmAppHandler.documentFileQueryModel}")
  protected Expression associatedFilter;

  @TaskProperty(group = "associatedDocument")
  protected Expression associatedDocumentProperties;

  @Getter
  @Setter
  @TaskProperty(group = "associatedDocument")
  protected Expression associatedDocNonPersistentProperties;

  @Getter
  @Setter
  @TaskProperty(group = "output", labelPrefixed = false)
  protected Expression nonPersistentProperties;

  @TaskProperty(group = "associatedDocument", type = AssociatedDocLogLevel.class)
  protected Expression associatedDocLogsLevel = new FixedValue(AssociatedDocLogLevel.Warning.toString());

  @TaskProperty(group = "associatedDocument", type = Boolean.class)
  protected Expression hierarchicalAssociation = new FixedValue(FALSE.toString());

  @TaskProperty(group = "engine", type = String.class, description = "\"CONTROLES_METIERS_\"+${translator}")
  protected Expression dynamicSource;

  @Transient
  protected DocumentFile documentFile;

  private transient Instance selectedInstance;

  private transient String selectedResource;

  /* -- CONSTRUCTOR -- */

  public RteSourceTask() {
    super();
    this.rteService = getBean(RteService.class, RteService.SERVICE_NAME);
    this.securityService = getBean(SecurityService.class, SecurityService.SERVICE_NAME);
    this.instanceService = getBean(InstanceService.class, InstanceService.SERVICE_NAME);
    this.multiNodeService = getBean(MultiNodeService.class, MultiNodeService.SERVICE_NAME);
    sharedLockingService = getBean(SharedLockingService.class, SharedLockingService.SERVICE_NAME);
    this.stdout = new FixedValue(OutputContent.Logs.toString());
  }

  /* -- IMPL. -- */

  @Override
  protected synchronized TransformEngineBuilder createEngineBuilder(final DelegateExecution execution, final Template sourceTemplate) {
    // is service enabled?
    if (!rteService.isEnabled()) {
      isTrue(rteService.isEnabled(), "RteService is not started. rte.home property is not defined in application properties.");
    }
    String companyCode = execution.getVariable(Variable.instance_company_code.toString(), String.class);
    isTrue(isNotBlank(companyCode), "The process variable 'instance_company_code' is missing");
    final Company company = this.securityService.getGroupByName(companyCode);
    notNull(company, "Company is required to allow document exchange");

    Path companyFolder = rteService.getCompanyFolder(company.getName());
    try {
      ExecutorService executor = newSingleThreadExecutor(new ThreadFactoryBuilder()
          .setNameFormat(Thread.currentThread()
              .getName() + "-%d")
          .build());
      return engine(RTE_SOURCE.getEngine())
          .withTemplate(sourceTemplate.getFile(), RTE)
          .config(execution.getVariables())
          .config(getRteOptionsFromEngineConfig(execution, sourceTemplate.getFile()
              .toString()), false)
          .config(ThrowOnError.toString(), FALSE)
          .config(Timeout.toString(), getLong(execution, this.timeout, DEFAULT_TIMEOUT) * 1000L)
          .config(RteEnv.EDIHOME.name(), rteService.getCompanyPublicFolder(companyFolder)
              .toString())
          .config(RteEnv.HOME.name(), companyFolder.toString())
          .config(Config.Executor, executor);
    }
    catch (ParseException e) {
      log.error("Failed to parse rte options", e.getMessage());
      return null;
    }
  }

  @Override
  public void executeTaskDynamicRte(final DelegateExecution execution) {
    List<Document> documents;
    List<DocumentFile> dofs;
    // document's files to process
    final boolean hasInputDocumentFiles = getBoolean(execution, this.input, FALSE);
    if (hasInputDocumentFiles) {
      documents = VariableHelper.getDocuments(execution, contextualDocumentsQuery);
      if (log.isInfoEnabled()) {
        log.info("Contextual documents ({}): {}", getString(execution, contextualDocumentsQuery, ""), documents.size());
      }
      if (documents.isEmpty()) {
        return;
      }
      dofs = searchFiles(documents, getString(execution, query));
      if (log.isInfoEnabled()) {
        log.info("Document files ({}): {}", getString(execution, query, ""), dofs.size());
      }
      if (dofs.isEmpty()) {
        return;
      }
    }
    else {
      //to do for when we have the default RTE template we should do the single processing(there will be another JIRA for it)
      log.error("RTE BT is configured in Dynamic mode but the input is disabled");
      return;
    }
    HashMap<String, String> docTemplateMap = new HashMap<>();
    HashSet<Template> templates = new HashSet<>();
    Template sourceTemplate;
    //get the dynamic RTE template for each document
    try {
      for (Document doc : documents
      ) {
        //get the dynamic RTE template
        sourceTemplate = getTemplateDynamicRTE(doc, execution);
        //invalid source template
        if (sourceTemplate == null) {
          //TO DO: use the default RTE
          continue;
        }
        docTemplateMap.put(doc.getUuid(), sourceTemplate.getName());
        templates.add(sourceTemplate);
      }
    }
    catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException ex) {
      log.error("RTE BT is configured in Dynamic mode and try to use an invalid document property", ex);
      addDeads(execution, documents);
      return;
    }
    // output directory
    Map<String, DynamicRteBuilder> dynamicRteBuilderMap = getDynamicBuilder(execution, templates);
    if (dynamicRteBuilderMap.isEmpty()) {
      return;
    }
    //process document files
    try {
      for (DocumentFile dof : dofs
      ) {
        DynamicRteBuilder dynamicRteBuilder;
        // contextual document files processing...
        if (isProcessable(execution, dof) && docTemplateMap.containsKey(dof.getDocument()
            .getUuid())) {
          String templateName = docTemplateMap.get(dof.getDocument()
              .getUuid());
          dynamicRteBuilder = dynamicRteBuilderMap.get(templateName);
          if (dynamicRteBuilder != null) {
            log.debug("Document file {} is processed by {} ", dof.getFile()
                .getName(), templateName);
            process(execution, dynamicRteBuilder.getBuilder(), dynamicRteBuilder.getTemplate(), dof, dynamicRteBuilder.getStdout(),
                dynamicRteBuilder.getStderr());
          }
        }
      }
    }
    catch (Exception ex) {
      log.error("Error processing", ex);
    }
    finally {
      if (CollectionUtils.isNotEmpty(dynamicRteBuilderMap.values()))
        for (DynamicRteBuilder b : dynamicRteBuilderMap.values()
        ) {
          b.getBuilder()
              .shutdown();
        }
    }

  }

  private Template getTemplateDynamicRTE(Document doc, DelegateExecution execution)
      throws NoSuchMethodException, IllegalArgumentException, InvocationTargetException, IllegalAccessException {

    String newDynamicExpression = getDynamicFile(doc, dynamicSource);
    //get the dynamic RTE template
    String instanceCode = (String) execution.getVariable(Variable.instance_code.name());
    Template sourceTemplate = null;
    if (newDynamicExpression != null)
      sourceTemplate = transformService.getTemplate(instanceCode, RTE_SOURCE, newDynamicExpression, null);
    // if rte source not found
    if (sourceTemplate == null) {
      log.error(
          "RTE BT is configured in Dynamic mode and try to use the RTE " + newDynamicExpression +
              " , but this is not an existing RTE_SOURCE");
      addDeads(execution, doc);
    }
    return sourceTemplate;
  }

  private Map<String, DynamicRteBuilder> getDynamicBuilder(DelegateExecution execution, Set<Template> templates) {
    Map<String, DynamicRteBuilder> mapBuilder = new HashMap<>();
    // output directory
    final File outdir = resolveOutputDir(execution);
    if (outdir == null) {
      log.warn("Process output directory variable not set ({})", PROCESS_OUTPUT_DIR);
      return mapBuilder;
    }
    // output contents
    OutputContent stdoutContent = getEnum(execution, this.stdout, OutputContent.class, OutputContent.Logs);
    OutputContent stderrContent = getEnum(execution, this.stderr, OutputContent.class, OutputContent.Logs);
    if (OutputContent.Logs != stdoutContent || OutputContent.Logs != stderrContent) {
      outdir.mkdirs();
      log.debug("Output directory: {}", outdir);
    }

    //create builder for each rte source template
    try {
      for (Template template : templates
      ) {
        // engine
        log.info("Transform template: {}", template.getName());
        TransformEngineBuilder builder = createEngineBuilder(execution, template);
        // outputs contents
        final Output stdout = this.resolveOutput(stdoutContent,
            execution,
            false,
            outdir, builder);
        final Output stderr = this.resolveOutput(stderrContent,
            execution,
            true,
            outdir, builder);
        mapBuilder.put(template.getName(), new DynamicRteBuilder(builder, template, stdout, stderr));
      }
    }
    catch (Exception e) {
      log.error("Error creating builder", e);
    }
    return mapBuilder;
  }

  @Override
  public boolean render(boolean error, String property) {

    switch (property) {
    case "isAssociatedDocument":
      return this.isAssociatedDocument != null && TRUE.toString()
          .equals(this.isAssociatedDocument.getExpressionText());
    case "associatedIndexClass":
      return this.associatedRepository != null && Repository.INDEX.toString()
          .equals(this.associatedRepository.getExpressionText());
    case "isDynamic":
      return "isDynamic".equals(this.source.getExpressionText());
    case "associatedFilter":
    case "associatedDocumentProperties":
    case "associatedDocLogsLevel":
    case "hierarchicalAssociation":
    case "associatedDocNonPersistentProperties":
      return this.associatedRepository != null;
    default:
      return super.render(error, property);
    }
  }

  @Override
  protected Output resolveOutput(OutputContent content, DelegateExecution execution, boolean error, File outputDir,
      TransformEngineBuilder builder)
      throws IOException {
    switch (content) {
    case Logs:
      return new RteLogsOutput(execution, error, builder);
    case Document:
      return new RteDocumentOutput(execution, outputDir);
    case DocumentFile:
      return new RteDocumentFileOutput(execution, outputDir);
    case LogFile:
      return new RteLogFileOutput(execution, outputDir, error ? FileType.ERROR : FileType.INFO);
    case Substitution:
      return new RteDocumentFileOutput(execution, outputDir);
    default:
      throw new IllegalArgumentException("Illegal output content type: " + this);
    }
  }

  AtomicInteger unlockAtTheEnd;

  @Override
  public void setUnlockAtTheEnd(final DelegateExecution execution, int size) {
    if (size > 0 && getBoolean(execution, isAssociatedDocument)) {
      log.debug("RTE might need to wait some time to perform the lock");
      sharedLockingService.waitUntilLockedDocumentsMapLockReleased();
      while (!sharedLockingService.tryLockLockedDocumentsMap()) ;
      unlockAtTheEnd = new AtomicInteger(size);
      log.debug("Finished locking");
    }
  }

  @Override
  public void unlockDocumentsInCaseOfError(final DelegateExecution execution) {
    if (getBoolean(execution, isAssociatedDocument)) {
      sharedLockingService.unlockLockedDocumentsMap();
      unlockAtTheEnd = null;
    }
  }

  /**
   * Standard output LOG FILE Always process and associate documents
   *
   * <AUTHOR> <<EMAIL>>
   */
  protected class RteLogFileOutput extends LogFileOutput {

    RteLogFileOutput(DelegateExecution execution, File outputDir, FileType fileType) {
      super(execution, outputDir, fileType);
    }

    @Override
    public void processDocument(DocumentFile dof, OutputResult result) throws IOException {
      try {
        super.processDocument(dof, result);
        processAssociatedDocuments(execution, dof.getDocument(), result);
      }
      catch (java.text.ParseException e) {
        log.error("Failed to report execution ", e.getMessage());
      }
      catch (InvalidStatusException e) {
        log.error("Failed to report execution: {}", e.getMessage());
      }
    }
  }

  /**
   * Standard output REPORT Always process and associate documents (not when is error logger)
   *
   * <AUTHOR> <<EMAIL>>
   */
  protected class RteLogsOutput extends LogsOutput {

    RteLogsOutput(DelegateExecution execution, boolean error, TransformEngineBuilder builder) {
      super(execution, error, builder);
    }

    @Override
    public Writer getWriter(DocumentFile dof, Charset charset) {
      return new Writer() {
        @Override
        public void write(String str, int off, int len) {
          consume(str.substring(off, off + len));
        }

        @Override
        public void write(char[] cbuf, int off, int len) {
          consume(String.valueOf(cbuf, off, len));
        }

        @Override
        public void flush() {
        }

        @Override
        public void close() {
        }

        private void consume(String content) {
          if (isEmpty(content)) {
            return;
          }
          logMessageWithLevelDependingOnRteLogLevel(builder.getContext()
                  .getParameter(LOGLEVEL, LOGLEVEL_DEFAULT),
              builder.getContext()
                  .getParameter(RteRuntime.EXIT_CODE),
              content);
        }
      };
    }


    @Override
    public void processDocument(DocumentFile dof, OutputResult result) throws IOException {
      try {
        if (!this.isLogger) {
          if (dof != null) {
            updateDocumentFileName(dof, DOF_INPUT_NAME);
            updateDocumentFileComment(dof, DOF_INPUT_COMMENT);
            updateDocumentFileType(dof, DOF_INPUT_TYPE);
            updateDocumentFileSubtype(dof, DOF_INPUT_SUBTYPE);
            updateDocumentFileActionname(dof, DOF_INPUT_ACTIONNAME);
            updateDocumentFileDescription(dof, DOF_INPUT_DESCRIPTION);
            updateDocumentAndIndexProperties(dof);
          }
          processAssociatedDocuments(execution, dof != null ? dof.getDocument() : null, result);
        }
      }
      catch (java.text.ParseException e) {
        log.error("Failed to report execution", e.getMessage());
      }
      catch (InvalidStatusException e) {
        log.error(e.getMessage());
      }
    }

  }

  /**
   * Standard output Mono-document or Substitution: Always process and associate documents
   *
   * <AUTHOR> <<EMAIL>>
   */
  protected class RteDocumentFileOutput extends DocumentFileOutput {

    RteDocumentFileOutput(DelegateExecution execution, File outputDir) {
      super(execution, outputDir);
    }

    @Override
    public void processDocument(DocumentFile dof, OutputResult result) throws IOException {
      try {
        documentFile = dof;
        updateDocumentFile(dof);
        processAssociatedDocuments(execution, dof.getDocument(), result);
      }
      catch (InvalidStatusException e) {
        log.error("Failed to create document file: {}", e.getMessage());
        VariableHelper.addDeads(execution, dof.getDocument());
      }
      catch (java.text.ParseException | IllegalArgumentException e) {
        log.error("Failed to create document file: {}", e.getMessage());
        VariableHelper.addDeads(execution, dof.getDocument());
      }

    }

    // values can be update after each execution of a rte
    @Override
    protected String getDfActionName() {
      try {
        return getPropertyFromDocument(fileActionName, documentFile, execution);
      }
      catch (RuntimeException e) {
        return getString(execution, fileActionName);
      }
    }

    @Override
    protected String getDfComment() {
      try {
        return getPropertyFromDocument(fileComment, documentFile, execution);
      }
      catch (RuntimeException e) {
        return getString(execution, fileComment);
      }
    }

    @Override
    protected Boolean getDfInArchive() {
      return !getBoolean(execution, fileNotInArchive, FALSE);
    }

    @Override
    protected String getDfDescription() {
      try {
        return getPropertyFromDocument(fileDescription, documentFile, execution);
      }
      catch (RuntimeException e) {
      return getString(execution, fileDescription);
      }
    }

    @Override
    protected String getDfSubtype() {
      try {
        return getPropertyFromDocument(fileSubtype, documentFile, execution);
      }
      catch (RuntimeException e) {
      return getString(execution, fileSubtype);
      }
    }
  }

  /**
   * Standard output Multi-document Always process associated documents and associate only if a document was created
   *
   * <AUTHOR> <<EMAIL>>
   */
  protected class RteDocumentOutput extends DocumentOutput {

    RteDocumentOutput(DelegateExecution execution, File outputDir) {
      super(execution, outputDir);
    }

    @Override
    public void processDocument(DocumentFile dof, OutputResult result) throws IOException {
      try {
        updateDocumentFileName(dof, DOF_INPUT_NAME);
        updateDocumentFileComment(dof, DOF_INPUT_COMMENT);
        updateDocumentFileType(dof, DOF_INPUT_TYPE);
        updateDocumentFileSubtype(dof, DOF_INPUT_SUBTYPE);
        updateDocumentFileActionname(dof, DOF_INPUT_ACTIONNAME);
        updateDocumentFileDescription(dof, DOF_INPUT_DESCRIPTION);
        Document doc = createDocument();

        processAssociatedDocuments(execution, doc, result);
        if (doc == null) {
          return;
        }
        // if the parent wasn't already set as the associated document, make the initial document parent
        if (dof != null && doc.getParent() == null) {
          dof.getDocument()
              .addChild(doc);
        }
        // adds new document to context
        addDocuments(execution, doc);
      }
      catch (ReflectiveOperationException ex) {
        log.error("Could not create document", ex);
        throw new TaskException(ex);
      }
      catch (java.text.ParseException | IllegalArgumentException e) {
        log.error("Failed to create document ", e.getMessage());
        if (dof != null) {
          VariableHelper.addDeads(execution, dof.getDocument());
        }
      }
      catch (InvalidStatusException e) {
        log.error("Failed to create document: {}", e.getMessage());
        if (dof != null) {
          VariableHelper.addDeads(execution, dof.getDocument());
        }
      }
    }
  }

  public class DynamicRteBuilder {

    private TransformEngineBuilder builder;
    private Template template;
    private AbstractTransformTask.Output stdout;
    private AbstractTransformTask.Output stderr;

    public DynamicRteBuilder(TransformEngineBuilder builder, Template template, Output stdout, Output stderr) {
      this.builder = builder;
      this.template = template;
      this.stdout = stdout;
      this.stderr = stderr;
    }

    public TransformEngineBuilder getBuilder() {
      return builder;
    }

    public void setBuilder(TransformEngineBuilder builder) {
      this.builder = builder;
    }

    public Template getTemplate() {
      return template;
    }

    public void setTemplate(Template template) {
      this.template = template;
    }

    public Output getStdout() {
      return stdout;
    }

    public void setStdout(Output stdout) {
      this.stdout = stdout;
    }

    public Output getStderr() {
      return stderr;
    }

    public void setStderr(Output stderr) {
      this.stderr = stderr;
    }
  }

  /**
   * If the repository is defined search for one document (based on the associatedFilter) and:
   * <li>add it to the context</li>
   * <li>update its properties (based on associatedDocumentProperties)</li>
   * <li>make this document the parent of the currently processed input document (or output in case of multi-document)</li>
   *
   * @param execution
   * @param originDocument the document to be associated; can be null
   * @param result
   * @throws java.text.ParseException
   * @throws InvalidStatusException
   */
  private void processAssociatedDocuments(DelegateExecution execution, Document originDocument, OutputResult result)
      throws java.text.ParseException, InvalidStatusException {

    if (!getBoolean(execution, isAssociatedDocument)) {
      return;
    }
    if (unlockAtTheEnd == null)
      setUnlockAtTheEnd(execution, 1);
    final Repository repo = getEnum(execution, associatedRepository, Repository.class, null);
    final Class<Indexable> idxType = VariableHelper.<Indexable> getClass(execution, associatedIndexClass, null);
    AssociatedDocLogLevel logLevel = getEnum(execution, associatedDocLogsLevel, AssociatedDocLogLevel.class, Warning);
    Document document = getDocumentsFromDatabase(3, 0, execution, repo, idxType, logLevel, originDocument, result);
    unlockAtTheEnd.decrementAndGet();
    if (document != null) {
      boolean unlockLockedDocumentsMap = unlockAtTheEnd.get() > 0 ? FALSE : TRUE;
      VariableHelper.addDocuments(repo.name(), execution, Arrays.asList(document), unlockLockedDocumentsMap);
      updateDocAndIndexProperties(execution, document, associatedDocumentProperties, null);
      addNonPersistentProperties(execution, associatedDocNonPersistentProperties, document);

      if (originDocument != null && VariableHelper.getBoolean(execution, hierarchicalAssociation)) {
        log.info("Document {} was associated to the document found", originDocument.getReference());
        document.addChild(originDocument);
      }
    }
    else if (unlockAtTheEnd.get() <= 0) {
      sharedLockingService.unlockLockedDocumentsMap();
    }
  }

  public Document getDocumentsFromDatabase(int maxNrRetry, int currentNbRetry, DelegateExecution execution,
      final Repository repo,
      final Class<Indexable> idxType, AssociatedDocLogLevel logLevel, Document originDocument, OutputResult result) {
    if (currentNbRetry >= maxNrRetry) {
      log.debug("Max number of retries reached while trying to lock the document: {}", currentNbRetry);
      return null;
    }
    Document document = searchDocument(execution, repo, idxType, logLevel, originDocument, result);
    Set<Long> lockedDocIds = getDocumentLockingService().getLockedDocumentsInAnotherProcesses(
        execution.getProcessInstanceId());
    log.debug("documents locked in another process: {}", lockedDocIds);
    // in this case the document is already locked(used in another process)
    if (document != null && lockedDocIds.contains(document.getId())) {
      log.debug("This document is already used by another process. We will do another try to get document.");
      sharedLockingService.removeDocumentsFromSession(Arrays.asList(document.getId()));
      try {
        TimeUnit.SECONDS.sleep(2);
      }
      catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
      getDocumentsFromDatabase(3, currentNbRetry + 1, execution, repo, idxType, logLevel, originDocument, result);
    }
    return document;
  }

  /**
   * Searches a unique document based on the type of Repository
   *
   * @param execution
   * @param repo
   * @param idxType
   * @param logLevel
   * @param result
   * @param originDocument
   * @return
   */
  private Document searchDocument(DelegateExecution execution, final Repository repo, final Class<Indexable> idxType,
      AssociatedDocLogLevel logLevel, Document originDocument, OutputResult result) {
    String bql = getString(execution, associatedFilter);

    try {
      if (repo == null) {
        log.warn("Repository not set");
        return null;
      }
      if (repo == Repository.INDEX && idxType == null) {
        log.warn("Index query required index class name");
        return null;
      }
      bql = org.thymeleaf.util.StringUtils.concat("locks is null AND ", bql);
      Query q = parse(bql);
      List<Document> documents = searchDocumentsWithIndexable(execution, repo, q, Document.class, idxType, null);
      if (documents.size() != 1) {
        StringBuilder builder = new StringBuilder();
        String message = builder
            .append(documents.size() == 0 ? "No associated document found for " : "More than one associated documents found for ")
            .append(repo.toString()
                .toLowerCase())
            .append(" query: ")
            .append(q.toString())
            .toString();
        logMessage(message, logLevel);
        if (logLevel.equals(Error)) {
          result.setError("");
          result.setExitCode(-1);
          throw new IllegalArgumentException();
        }
        return null;
      }
      Document document = documents.get(0);
      if (document.equals(originDocument)) {
        log.warn("Cannot associate a document to itself (reference={}) ", document.getReference());
        return null;
      }
      log.info("One associated document found (reference={}) ", document.getReference());
      return document;
    }
    catch (BqlParseException e) {
      log.error("Error parsing BQL query: {} ({})", bql, e.getMessage());
      return null;
    }
  }

  private void logMessage(String message, AssociatedDocLogLevel logLEvel) {
    switch (logLEvel) {
    case Error:
      log.error(message);
      break;
    case Info:
      log.info(message);
      break;
    case Warning:
    default:
      log.warn(message);
      break;
    }

  }

  private Map<String, Object> getRteOptionsFromEngineConfig(final DelegateExecution execution, String rteName) throws ParseException {
    Map<String, Object> options = new HashMap<>();
    options.put(RteRuntime.RTE_FILE_PATH, rteName);
    String config = VariableHelper.getString(execution, engineConfig);
    RteCommandeLineHelper.extractOptions(config, options, rteName);
    // extract option to set outputCharset
    if (options.get(RteRuntime.EDI_OUTCHARSET) != null) {
      setOutputCharset(options.get(RteRuntime.EDI_OUTCHARSET)
          .toString());
    }
    addGlobalParameters(execution, options);
    String params = getParametersFromContext(execution);
    if (StringUtils.isNotBlank(params)) {
      String paramValues = (String) options.get(RteRuntime.INPUT_PARAM_VALUE);
      options.put(RteRuntime.INPUT_PARAM_VALUE,
          paramValues == null ? getParametersFromContext(execution)
              : String.join(RteRuntime.PARAMETER_SEPARATOR, paramValues, params));
    }
    return options;
  }

  /**
   * This methods is called to add PROCESS_OUTPUT_DIR value to existing options
   *
   * @param execution
   * @param options rte options from config
   */
  private void addGlobalParameters(DelegateExecution execution, Map<String, Object> options) {

    StringBuilder ctxParams = new StringBuilder();
    ctxParams.append(String.join("=", "APP_TYPE", getConfigurationService().getProperties()
        .getProperty("app.type")));

    EXECUTION_DIR_EXPOSED.forEach(variable -> ctxParams.append(RteRuntime.PARAMETER_SEPARATOR)
        .append(String.join("=", variable.name(), VariableHelper.getVariable(execution, variable, new File(""))
            .getPath())));

    execution.getVariables()
        .entrySet()
        .stream()
        .filter(this::matchesPrefix)
        .forEach(e -> ctxParams.append(RteRuntime.PARAMETER_SEPARATOR)
            .append(String.join("=", e.getKey()
                .toUpperCase(), e.getValue()
                .toString())));

    String paramValues = (String) options.get(RteRuntime.INPUT_PARAM_VALUE);
    String values = StringUtils.isEmpty(paramValues) ? ctxParams.toString()
        : String.join(RteRuntime.PARAMETER_SEPARATOR, paramValues, ctxParams.toString());
    options.put(RteRuntime.INPUT_PARAM_VALUE, values);

  }

  @Override
  protected void addNonPersistentProperties(DelegateExecution execution, Document document) {
    addNonPersistentProperties(execution, nonPersistentProperties, document);
  }

  private void addNonPersistentProperties(DelegateExecution execution, Expression nonPersistentProperties, Document document) {
    if (nonPersistentProperties != null && document != null) {
      Map<String, Object> properties = Optional.ofNullable(VariableHelper.getMap(execution, nonPersistentProperties))
          .orElse(Collections.emptyMap())
          .entrySet()
          .stream()
          .filter(entry -> entry.getValue() != null && !entry.getValue().equals(""))
          .collect(Collectors.toMap(
              entry -> NON_PERSISTENT_PROPERTY_PREFIX + entry.getKey()
                  .toUpperCase(),
              Map.Entry::getValue
          ));
      addNonPersistentPropertiesToExecution(execution, properties, document.getUuid());
    }
  }

  private String getParametersFromContext(final DelegateExecution execution) {
    StringBuilder contextParameters = new StringBuilder();
    if (engineParams != null) {
      List<Parameter> listOfParameters = GsonHelper.fromTypeWrappedJsonList(VariableHelper.getString(execution, engineParams));
      int i = 0;
      if (CollectionUtils.isNotEmpty(listOfParameters)) {
        for (Parameter p : listOfParameters) {
          if (StringUtils.isNotBlank(p.getKey()) && StringUtils.isNotBlank(p.getValue())) {
            Object obj = VariableHelper.resolveStringExpression(execution, p.getValue());
            if (obj != null) {
              contextParameters.append(p.getKey() + RteRuntime.OPERATOR_EQUAL + obj.toString());
              if (i < listOfParameters.size() - 1) {
                contextParameters.append(RteRuntime.PARAMETER_SEPARATOR);
              }
            }
          }
          i++;
        }
      }
    }
    addFormPageVariable(contextParameters, execution);
    return contextParameters.toString();

  }

  private void addFormPageVariable(StringBuilder sb, final DelegateExecution execution) {
    String variables = execution.getVariables()
        .entrySet()
        .stream()
        .filter(e -> e.getKey()
            .startsWith(FormPageHelper.PAGE_VARIABLE_IDENTIFIER))
        .map(e -> e.getKey()
            .substring(1) + RteRuntime.OPERATOR_EQUAL + e.getValue())
        .collect(Collectors.joining(RteRuntime.PARAMETER_SEPARATOR));

    if (sb.length() > 0 && StringUtils.isNotBlank(variables))
      sb.append(RteRuntime.PARAMETER_SEPARATOR);

    sb.append(variables);
  }

  public void onAddEngineParam() {
    if (this.parameterEdit) {
      int size = this.params.size();
      params.set(size - 1, new Parameter());
      return;
    }
    params.add(new Parameter());
    this.parameterEdit = true;
  }

  public void onEditEngineParam(RowEditEvent event) {
    this.engineParams = new FixedValue(GsonHelper.toTypeWrappedJsonList(params));
    this.parameterEdit = false;
  }

  public void onCancelEngineParam(RowEditEvent event) {
    this.parameterEdit = false;
  }

  public void onRemoveEngineParam(Parameter parameter) {
    params.remove(parameter);
    this.engineParams = new FixedValue(GsonHelper.toTypeWrappedJsonList(params));
    this.parameterEdit = false;
  }

  private boolean matchesPrefix(Map.Entry<String, Object> entry) {
    return PARAMETERS_PREFIXES.stream().anyMatch(pref -> entry.getKey().startsWith(pref));
  }

  /* -- IMPL. -- */

  @Override
  public Expression getEngineParams() {
    return engineParams;
  }

  @Override
  public void setEngineParams(Expression engineParams) {
    if (engineParams != null) {
      String json = "null".equals(engineParams.getExpressionText()) ? null : engineParams.getExpressionText();
      this.params = GsonHelper.fromTypeWrappedJsonList(json);
      this.engineParams = engineParams;
    }
  }

  public List<Parameter> getParams() {
    if (params == null) {
      params = new ArrayList<>();
    }
    return params;
  }

  public void setParams(List<Parameter> params) {
    this.params = params;
  }

  public Expression getAssociatedRepository() {
    return associatedRepository;
  }

  public void setAssociatedRepository(Expression repository) {
    this.associatedRepository = repository;
  }

  public Expression getAssociatedIndexClass() {
    return associatedIndexClass;
  }

  public void setAssociatedIndexClass(Expression indexClassName) {
    this.associatedIndexClass = indexClassName;
  }

  public Expression getAssociatedFilter() {
    return associatedFilter;
  }

  public void setAssociatedFilter(Expression filter) {
    this.associatedFilter = filter;
  }

  public Expression getAssociatedDocumentProperties() {
    return associatedDocumentProperties;
  }

  public void setAssociatedDocumentProperties(Expression associatedDocumentProperties) {
    this.associatedDocumentProperties = associatedDocumentProperties;
  }

  public Expression getAssociatedDocLogsLevel() {
    return associatedDocLogsLevel;
  }

  public void setAssociatedDocLogsLevel(Expression associatedDocLogsLevel) {
    this.associatedDocLogsLevel = associatedDocLogsLevel;
  }

  public Expression getHierarchicalAssociation() {
    return hierarchicalAssociation;
  }

  public void setHierarchicalAssociation(Expression hierarchicalAssociation) {
    this.hierarchicalAssociation = hierarchicalAssociation;
  }

  public Expression getIsAssociatedDocument() {
    return isAssociatedDocument;
  }

  public void setIsAssociatedDocument(Expression isAssociatedDocument) {
    this.isAssociatedDocument = isAssociatedDocument;
  }

  public Instance getSelectedInstance() {
    return selectedInstance;
  }

  public void setSelectedInstance(String selectedInstance) {
    this.selectedInstance = instanceService.getInstanceByCode(selectedInstance);
  }

  public void onChangeSourceSelected() {
    selectedResource = this.source.toString();
    if ("isDynamic".equals(selectedResource)) {
      FacesContext fc = FacesContext.getCurrentInstance();
      FacesMessage fm = MessageHelper.toFacesMessage("trftsklbls.RteSource_warning_dynamic_rte", FacesMessage.SEVERITY_WARN);
      fc.addMessage(null, fm);
      input = new FixedValue(TRUE.toString());
    }
  }

  public Expression getDynamicSource() {
    return dynamicSource;
  }

  public void setDynamicSource(Expression dynamicSource) {
    this.dynamicSource = dynamicSource;
  }

  public String getRteStudioUrl() throws MalformedURLException {
    if (StringUtils.isEmpty(getSelectedResource()))
      return "";
    StringBuilder sb = new StringBuilder();
    String scheme = JSFHelper.getScheme();
    return sb.append(scheme)
        .append("://")
        .append(getRequest().getServerName())
        .append(JSFHelper.getPort(scheme))
        .append(getRequest().getContextPath())
        .append("/ui/admin/instance/repository/rte.jsf")
        .append("?iid=")
        .append(selectedInstance.getId())
        .append("&selectedResource=")
        .append(getSelectedResource())
        .append(".")
        .append("rte")
        .toString();
  }

  public String getSelectedResource() {
    if (StringUtils.isEmpty(selectedResource))
      selectedResource = this.source == null ? null : this.source.toString();
    return selectedResource;
  }

  public class Parameter implements Serializable {
    private static final long serialVersionUID = 1L;

    private String key;

    private String value;

    public String getKey() {
      return key;
    }

    public void setKey(String key) {
      this.key = key;
    }

    public String getValue() {
      return value;
    }

    public void setValue(String value) {
      this.value = value;
    }
  }

  protected enum AssociatedDocLogLevel {
    Info,
    Warning,
    Error;
  }
}