package com.byzaneo.generix.transform.task.business;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.exception.ProcessInterruptedException;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.commons.util.mapper.BeanPropertyMapperHelper;
import com.byzaneo.generix.bean.Template;
import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.context.NumericVariable;
import com.byzaneo.generix.rtemachine.exception.RteException;
import com.byzaneo.generix.service.TransformService;
import com.byzaneo.generix.transform.rte.RteEngine.Config;
import com.byzaneo.generix.transform.task.util.TransformHelper;
import com.byzaneo.query.Query;
import com.byzaneo.task.annotation.TaskProperty;
import com.byzaneo.task.api.TaskException;
import com.byzaneo.transform.engine.*;
import com.byzaneo.xtrade.InvalidStatusException;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.process.VariableHelper;
import com.byzaneo.xtrade.process.el.FixedValue;
import com.byzaneo.xtrade.process.task.*;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.ui.convert.ProcessExpressionConverter;
import org.activiti.engine.delegate.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.*;
import javax.validation.valueextraction.Unwrapping;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.*;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.transform.rte.RteEngine.Config.Logger;
import static com.byzaneo.generix.transform.rte.RteEngine.Config.Timeout;
import static com.byzaneo.generix.transform.task.util.TransformHelper.addNonPersistentPropertiesAsParameters;
import static com.byzaneo.xtrade.process.Variable.PROCESS_OUTPUT_DIR;
import static com.byzaneo.xtrade.process.VariableHelper.*;
import static com.byzaneo.xtrade.util.DocumentHelper.searchFiles;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.util.Arrays.stream;
import static java.util.UUID.randomUUID;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.MapUtils.isEmpty;
import static org.apache.commons.io.FilenameUtils.getBaseName;
import static org.apache.commons.lang3.StringEscapeUtils.escapeXml11;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.util.Assert.notNull;

/**
 * Transformation task abstraction.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Mar 11, 2016
 * @since 5.0 GNX-3722
 */
public abstract class AbstractTransformTask extends AbstractTask implements CancelableTask {
  private static final String FILE_TYPE = "fileType";

  private static final String FILE_COMMENT = "fileComment";

  private static final String FILE_DESCRPTION = "fileDescription";

  private static final String FILE_SUBTYPE = "fileSubtype";

  private static final String FILE_ACTIONNAME = "fileActionName";

  private static final String FILE_NOT_IN_ARCHIVE = "fileNotInArchive";

  private static final String STDOUT_LOGS_LEVEL = "stdoutLogsLevel";

  private static final String STDERR_LOGS_LEVEL = "stderrLogsLevel";

  private static final String DOCUMENT_PROPERTIES = "documentProperties";

  private static final String NON_PERSISTENT_PROPERTIES = "nonPersistentProperties";

  private static final long serialVersionUID = -4375960278710840993L;

  private static final Logger log = getLogger(AbstractTransformTask.class);

  protected static final String LOGLEVEL = "LOGLEVEL";

  private static final String DOF_OUTPUT_NAME = "pDOF.OUTPUT.NAME";

  protected static final int LOGLEVEL_DEFAULT = 1;

  protected static final String DOF_INPUT_NAME = "pDOF.INPUT.NAME";

  protected static final String DOF_INPUT_COMMENT = "pDOF.INPUT.COMMENT";

  protected static final String DOF_INPUT_ACTIONNAME = "pDOF.INPUT.ACTIONNAME";

  protected static final String DOF_INPUT_TYPE = "pDOF.INPUT.TYPE";

  protected static final String DOF_INPUT_SUBTYPE = "pDOF.INPUT.SUBTYPE";

  protected static final String DOF_INPUT_DESCRIPTION = "pDOF.INPUT.DESCRIPTION";

  public static final String LABEL_FAMILY = "trftsklbls";

  private static Set<String> UPDATABLE_PROPERTIES = new HashSet<String>(
      Arrays.asList("reference", "from", "to", "issueDate", "linCount", "number",
          "stage", "status", "subtype", "type", "transport", "purpose", "acquisition", "orderingParty", "consultStatus", "processingWay",
          "translator", "thirdParty", "nature", "immatpdp", "b2gRules", "ereportingOption", "countryRules1", "countryRules2", "useCase",
          "billingFramework", "sovosId", "sovosStatus", "sovosTransactionId", "sovosStatusCode", "sovosNotificationId",
          "archiveExpirationDate", "rossumId", "profile"));

  /**
   * Default time out in seconds.
   *
   * @see Config#Timeout
   */
  protected static final long DEFAULT_TIMEOUT = Timeout.<Long> getDefaultValue() / 1000L;

  /**
   * Log levels
   */
  protected enum LogLevel {
    Debug,
    Info,
    Warning,
    Error;
  }

  /**
   * Output content management.
   */
  protected enum OutputContent {
    /**
     * {@link Document} output
     */
    Document(false,
        false),
    /**
     * {@link DocumentFile} output
     */
    DocumentFile(false,
        true),
    /**
     * TXT file attached to the process {@link Document} as a {@link DocumentFile}
     */
    LogFile(true,
        true),
    /**
     * Report logs
     */
    Logs(true,
        false),
    /**
     * Replace the Document's DocFile (type = XML or XCBL)
     */
    Substitution(false,
        true);

    private final boolean error;

    private final boolean input;

    private OutputContent(boolean error, boolean input) {
      this.error = error;
      this.input = input;
    }

    public boolean needInput() {
      return input;
    }

    public boolean isError() {
      return error;
    }

    public static final OutputContent valueOf(final Expression exp) {
      return exp == null ? Logs : valueOf(exp.getExpressionText());
    }
  }

  // -- SERVICE --

  protected transient final TransformService transformService;

  protected transient final DocumentService documentService;

  protected transient DocumentStatusService documentstatusService;

  // -- PROPERTIES --

  private transient String outputCharset;

  // - ENGINE -

  /**
   * (Required: No) Execution timeout in seconds. Defaults {@link Config#Timeout}.
   */
  @TaskProperty(group = "engine", type = Integer.class, required = true)
  @Min(value = 1, payload = Unwrapping.Unwrap.class)
  @Max(value = 9999, payload = Unwrapping.Unwrap.class)
  protected Expression timeout = new FixedValue(DEFAULT_TIMEOUT);

  /**
   * (Required: Yes) Source transform template URI
   */
  @TaskProperty(group = "engine", required = true, options = "gnxTransformAppHandler.getTemplates(gnxInstanceService.getInstanceByCode(cc.attrs.owner.project.workspace.name), 'RTE_SOURCE', null, true)")
  protected Expression source;

  /**
   * Engine configuration
   */
  @TaskProperty(group = "engine")
  protected Expression engineConfig;

  /**
   * Engine's transformation parameters
   */
  @TaskProperty(group = "engine")
  protected Expression engineParams;

  // - INPUT -

  /**
   * (Required: No) Indicates if the engine will use documents files as input
   */
  @TaskProperty(group = "input", type = Boolean.class)
  protected Expression input = new FixedValue(FALSE.toString());

  /**
   * Contextual documents filtering query (default: all documents)
   */
  @TaskProperty(group = "input", type = Query.class, model = "#{xtdIpmAppHandler.documentQueryModel}")
  protected Expression contextualDocumentsQuery;

  /**
   * (Required: No) String query expression to filter the contextual document's files to process. Default all documents files
   */
  @TaskProperty(group = "input", type = Query.class, model = "#{xtdIpmAppHandler.documentFileQueryModel}")
  protected Expression query;

  // - STDOUT -

  /**
   * Output directory
   */
  @TaskProperty(group = "output")
  protected Expression outputDirectory = new FixedValue(PROCESS_OUTPUT_DIR.toStringPlaceHolder());

  /**
   * (Required: Yes) Standard output content
   */
  @TaskProperty(group = "output", required = true, type = OutputContent.class)
  protected Expression stdout = new FixedValue(OutputContent.Logs.toString());

  // STDOUT: LOGS
  /**
   * Logs level to write in the report
   */
  @TaskProperty(group = "output", required = true, type = LogLevel.class)
  protected Expression stdoutLogsLevel = new FixedValue(LogLevel.Info.toString());

  // STDOUT: DOCUMENT
  /**
   * Document class name (default {@link Document} class name)
   */
  @TaskProperty(group = "output", options = "#{xtdIpmAppHandler.documentOptions}", required = true, labelPrefixed = false)
  protected Expression documentClassName = new FixedValue(Document.class.getName());

  /**
   * {@link Document} properties
   */
  @TaskProperty(group = "output", labelPrefixed = false)
  protected Expression documentProperties;

  /**
   * Output document's status
   */
  @TaskProperty(group = "output", required = false, type = DocumentStatusEntity.class)
  protected Expression documentStatus;

  // STDOUT: DOCUMENT FILE
  /**
   * {@link FileType} (default: {@link FileType#UNKNOWN}
   */
  @TaskProperty(group = "output", required = true, type = FileType.class, labelPrefixed = false)
  protected Expression fileType = new FixedValue(FileType.UNKNOWN.toString());

  /**
   * {@link DocumentFile#getComment()}
   */
  @TaskProperty(group = "output", labelPrefixed = false)
  protected Expression fileComment;

  /**
   * {@link DocumentFile#getDescription()}
   */
  @TaskProperty(group = "output", labelPrefixed = false)
  protected Expression fileDescription;

  /**
   * {@link DocumentFile#getSubtype()}
   */
  @TaskProperty(group = "output", labelPrefixed = false)
  protected Expression fileSubtype;

  @TaskProperty(group = "output", labelPrefixed = false)
  protected Expression fileActionName;

  @TaskProperty(type = Boolean.class, group = "output", labelPrefixed = false)
  protected Expression fileNotInArchive;

  // - STDERR -

  /**
   * (Required: Yes) Error output content
   */
  @TaskProperty(group = "error", required = true, type = OutputContent.class)
  protected Expression stderr = new FixedValue(OutputContent.Logs.toString());

  /**
   * Error output log Level
   */
  @TaskProperty(group = "error", required = true, type = LogLevel.class)
  protected Expression stderrLogsLevel = new FixedValue(LogLevel.Error.toString());

  /* -- CONSTRUCTOR -- */

  public AbstractTransformTask() {
    this.transformService = getBean(TransformService.class, TransformService.SERVICE_NAME);
    this.documentService = getBean(DocumentService.class, DocumentService.SERVICE_NAME);
    documentstatusService = getBean(DocumentStatusService.class, DocumentStatusService.SERVICE_NAME);
  }

  /* -- IMPL. -- */

  /**
   * @see com.byzaneo.xtrade.process.task.AbstractTask#execute(org.activiti.engine.delegate.DelegateExecution)
   */
  @Override
  public void executeTask(final DelegateExecution execution) throws Exception {
    if ("isDynamic".equals(this.source.getExpressionText())) {
      //use dynamic RTE source
      executeTaskDynamicRte(execution);
    }
    else {
      // source
      notNull(source, "Source is required");
      String templateUri = getString(execution, this.source);
      final Template sourceTemplate = this.transformService.getTemplate(templateUri);
      notNull(sourceTemplate, "Template not found for: " + templateUri);

      // output directory
      final File outdir = resolveOutputDir(execution);
      if (outdir == null) {
        log.warn("Process output directory variable not set ({})", PROCESS_OUTPUT_DIR);
        return;
      }

      // output contents
      OutputContent stdoutContent = getEnum(execution, this.stdout, OutputContent.class, OutputContent.Logs);
      OutputContent stderrContent = getEnum(execution, this.stderr, OutputContent.class, OutputContent.Logs);
      if (OutputContent.Logs != stdoutContent || OutputContent.Logs != stderrContent) {
        outdir.mkdirs();
        log.debug("Output directory: {}", outdir);
      }

      TransformEngineBuilder builder = null;
      try {
        // engine
        log.info("Transform template: {}", sourceTemplate.getName());
        builder = createEngineBuilder(execution, sourceTemplate);

        // outputs contents
        final Output stdout = this.resolveOutput(stdoutContent,
            execution,
            false,
            outdir, builder);
        final Output stderr = this.resolveOutput(stderrContent,
            execution,
            true,
            outdir, builder);

        // contextual document files processing...
        final boolean hasInputDocumentFiles = getBoolean(execution, this.input, FALSE);
        if (hasInputDocumentFiles) {
          this.processDocumentFiles(execution, builder, sourceTemplate, stdout, stderr);
        }
        // single processing...
        else {
          this.process(execution, builder, sourceTemplate, null, stdout, stderr);
        }
      }
      catch (Exception e) {
        log.error(e.getMessage());
      }
      finally {
        if (builder != null)
          builder.shutdown();
      }
    }
  }

  /* -- ABSTRACTS -- */

  protected abstract TransformEngineBuilder createEngineBuilder(final DelegateExecution execution,
      final Template sourceTemplate);

  protected void executeTaskDynamicRte(final DelegateExecution execution) {
  }

  protected void setUnlockAtTheEnd(final DelegateExecution execution, int size) {
  }

  protected void unlockDocumentsInCaseOfError(final DelegateExecution execution) {
  }
  /* -- UTILS -- */

  protected void processDocumentFiles(final DelegateExecution execution, final TransformEngineBuilder builder,
      final Template sourceTemplate,
      final Output stdout, final Output stderr) {
    // document's files to process
    final List<Document> documents = VariableHelper.getDocuments(execution, contextualDocumentsQuery);
    if (log.isInfoEnabled()) {
      log.info("Contextual documents ({}): {}", getString(execution, contextualDocumentsQuery, ""), documents.size());
    }
    if (documents.isEmpty()) {
      return;
    }

    List<DocumentFile> dofs = searchFiles(documents, getString(execution, query));
    if (log.isInfoEnabled()) {
      log.info("Document files ({}): {}", getString(execution, query, ""), dofs.size());
    }
    if (dofs.isEmpty()) {
      return;
    }

    dofs = dofs.stream()
        .filter(dof -> isProcessable(execution, dof))
        .collect(Collectors.toList());

    setUnlockAtTheEnd(execution, dofs.size());

    dofs
        .forEach(dof -> process(execution, builder, sourceTemplate, dof, stdout, stderr));
  }

  protected static boolean isProcessable(DelegateExecution execution, DocumentFile dof) {
    boolean processable = dof != null && dof.getFile() != null && dof.getFile()
        .isFile() && dof.getDocument() != null && !isDead(execution, dof.getDocument());
    if (processable) {
      log.info("Processing {}...", dof.getFile()
          .getName());
    }
    else {
      log.warn("Ignored document file: {}", dof);
    }
    return processable;
  }

  protected void process(
      final DelegateExecution execution, final TransformEngineBuilder builder,
      final Template sourceTemplate, final DocumentFile dof,
      final Output stdout, final Output stderr) {

    if (Thread.currentThread()
        .isInterrupted()) {
      throw new ProcessInterruptedException("Canceled by user. Some document files may have already been processed");
    }
    try (final InputSource inputSource = (dof == null) ? null : new InputSource(dof.getFile(), dof.getType());
        Writer writer = stdout.getWriter(dof, getOutputCharsetResolved());
        Writer errorWriter = stderr.getWriter(dof, getOutputCharsetResolved())) {/* initialize streams in try with resources block */
      // prepares result
      final OutputResult result = new OutputResult(writer, getExpressionValue(execution, fileType, FileType.class, FileType.UNKNOWN));

      TransformHelper.addDocPropetiesAsParameters(builder, dof);

      builder.addParameter(Logger, errorWriter);

      addNonPersistentPropertiesAsParameters(builder, execution, dof);

      // transforms...
      builder.config(Logger, errorWriter)
          .transform(inputSource, result, true);

      if (builder.getContext()
          .getParameter(RteRuntime.PARAMETERS) != null) {
        Map<String, String> params = builder.getContext()
            .getParameter(RteRuntime.PARAMETERS);
        params.forEach((k, v) -> {
          String key = "p" + StringUtils.capitalize(k);
          VariableHelper.setVariable(execution, key, v);
        });
      }

      if (builder.getContext()
          .getParameter(RteRuntime.DOCUMENT_ERRORS) != null) {
        List<DocumentError> params = builder.getContext()
            .getParameter(RteRuntime.DOCUMENT_ERRORS);
        VariableHelper.setVariable(execution, RteRuntime.DOCUMENT_ERRORS, params);
      }


      // handles result
      handle(execution, result, dof);
      if (result.getExitCode() == 0) {
        stdout.processDocument(dof, result);
        stderr.processDocument(dof, result);
      }

    }
    catch (Exception e) {
      final String message = getRootCauseMessage(e);
      log.error("Error while transforming: template={}{} ({})",
          sourceTemplate,
          dof != null ? ", " + dof : "",
          message);
    }
  }

  protected void handle(DelegateExecution execution, OutputResult result, DocumentFile dof) {
    if (result.getExitCode() != 0) {
      log.error("Exit code: {}", result.getExitCode());
      unlockDocumentsInCaseOfError(execution);
    }
    else
      log.info("Exit code: {}", result.getExitCode());
    if (result.getExitCode() != 0) {
      if (result.getError() != null) {
        log.error("Error message: {}", result.getError());
      }
      if (dof != null && result.getExitCode() > 0) {
        addDeads(execution, dof.getDocument());
        log.error("{} moved to dead", dof);
      }
      return;
    }
  }

  protected Output resolveOutput(
      final OutputContent content,
      final DelegateExecution execution,
      final boolean error,
      final File outputDir, TransformEngineBuilder builder) throws IOException {
    switch (content) {
    case Logs:
      return new LogsOutput(execution, error, builder);
    case Document:
      return new DocumentOutput(execution, outputDir);
    case DocumentFile:
      return new DocumentFileOutput(execution, outputDir);
    case LogFile:
      return new LogFileOutput(execution, outputDir, error ? FileType.ERROR : FileType.INFO);
    case Substitution:
      return new DocumentFileOutput(execution, outputDir);
    default:
      throw new IllegalArgumentException("Illegal output content type: " + this);
    }
  }

  protected File resolveOutputDir(final DelegateExecution execution) {
    File outdir = getFile(
        execution,
        outputDirectory,
        getVariable(execution, PROCESS_OUTPUT_DIR));
    if (outdir != null)
      outdir.mkdirs();
    return outdir;
  }

  /* -- ACCESSORS -- */

  // - ENGINE -
  public Expression getTimeout() {
    return timeout;
  }

  public void setTimeout(Expression timeout) {
    this.timeout = timeout;
  }

  public Expression getSource() {
    return source;
  }

  public void setSource(Expression source) {
    this.source = source;
  }

  public Expression getEngineConfig() {
    return engineConfig;
  }

  public void setEngineConfig(Expression engineConfig) {
    this.engineConfig = engineConfig;
  }

  public Expression getEngineParams() {
    return engineParams;
  }

  public void setEngineParams(Expression engineParams) {
    this.engineParams = engineParams;
  }

  public Charset getOutputCharsetResolved() {
    try {
      com.byzaneo.generix.rtemachine.edi.dictionary.Charset outEdiCharset = com.byzaneo.generix.rtemachine.edi.dictionary.Charset
          .fromOption(outputCharset);
      if (outEdiCharset == null) {
        return Charset.defaultCharset();
      }
      return outEdiCharset.toCharset();
    }
    catch (RteException re) {
      log.warn("Output EDI charset unrecognized. It will not be taken into account.");
      return Charset.defaultCharset();
    }
    catch (IllegalCharsetNameException | UnsupportedCharsetException e) {
      log.warn("Output charset unrecognized or unsupported. It will not be taken into account.");
      return Charset.defaultCharset();
    }
  }

  public void setOutputCharset(String outputCharset) {
    this.outputCharset = outputCharset;
  }

  // - INPUT -
  public Expression getInput() {
    return input;
  }

  public void setInput(Expression input) {
    this.input = input;
    if (this.input == null || this.input.getExpressionText()
        .equals(FALSE.toString())) {
      this.stdout = this.stderr = new FixedValue(OutputContent.Logs.toString());
    }
  }

  public Expression getContextualDocumentsQuery() {
    return contextualDocumentsQuery;
  }

  public void setContextualDocumentsQuery(Expression contextualDocumentsQuery) {
    this.contextualDocumentsQuery = contextualDocumentsQuery;
  }

  public Expression getQuery() {
    return query;
  }

  public void setQuery(Expression query) {
    this.query = query;
  }

  // - STDOUT -
  public Expression getStdout() {
    return stdout;
  }

  public void setStdout(Expression stdout) {
    this.stdout = stdout;
  }

  public Expression getOutputDirectory() {
    return outputDirectory;
  }

  public void setOutputDirectory(Expression outputDirectory) {
    this.outputDirectory = outputDirectory;
  }

  // Logs
  public Expression getStdoutLogsLevel() {
    return stdoutLogsLevel;
  }

  public void setStdoutLogsLevel(Expression stdoutLogsLevel) {
    this.stdoutLogsLevel = stdoutLogsLevel;
  }

  // Document
  public Expression getDocumentClassName() {
    return documentClassName;
  }

  public void setDocumentClassName(Expression documentClassName) {
    this.documentClassName = documentClassName;
  }

  public Expression getDocumentProperties() {
    return documentProperties;
  }

  public void setDocumentProperties(Expression documentProperties) {
    this.documentProperties = documentProperties;
  }

  public Expression getDocumentStatus() {
    return documentStatus;
  }

  public void setDocumentStatus(Expression documentStatus) {
    this.documentStatus = documentStatus;
  }

  // DocumentFile
  public Expression getFileType() {
    return fileType;
  }

  public void setFileType(Expression fileType) {
    this.fileType = fileType;
  }

  public Expression getFileComment() {
    return fileComment;
  }

  public void setFileComment(Expression fileComment) {
    this.fileComment = fileComment;
  }

  public Expression getFileDescription() {
    return fileDescription;
  }

  public void setFileDescription(Expression fileDescription) {
    this.fileDescription = fileDescription;
  }

  public Expression getFileSubtype() {
    return fileSubtype;
  }

  public void setFileSubtype(Expression fileSubtype) {
    this.fileSubtype = fileSubtype;
  }

  public Expression getFileActionName() {
    return fileActionName;
  }

  public void setFileActionName(Expression fileActionName) {
    this.fileActionName = fileActionName;
  }

  public Expression getFileNotInArchive() {
    return fileNotInArchive;
  }

  public void setFileInArchive(Expression fileInArchive) {
    this.fileNotInArchive = new FixedValue(Boolean.toString(!new FixedValue(TRUE.toString()).equals(fileInArchive)));
  }

  public void setFileNotInArchive(Expression fileNotInArchive) {
    this.fileNotInArchive = fileNotInArchive;
  }

  // - STDERR -
  public Expression getStderr() {
    return stderr;
  }

  public void setStderr(Expression stderr) {
    this.stderr = stderr;
  }

  // Logs
  public Expression getStderrLogsLevel() {
    return stderrLogsLevel;
  }

  public void setStderrLogsLevel(Expression stderrLogsLevel) {
    this.stderrLogsLevel = stderrLogsLevel;
  }

  /* -- RENDERING -- */

  public boolean render(boolean error, String property) {
    // dead & query
    final boolean hasInput = this.input != null && Boolean.valueOf(this.input.getExpressionText());
    if ("query".equals(property)) {
      // enabled only if input is also enabled
      return hasInput;
    }
    // content options
    final OutputContent output = OutputContent.valueOf(error ? this.stderr : this.stdout);
    switch (output) {
    case Logs:
      return DOCUMENT_PROPERTIES.equals(property) || STDERR_LOGS_LEVEL.equals(property) || STDOUT_LOGS_LEVEL.equals(property);
    case Document:
      return !STDERR_LOGS_LEVEL.equals(property) && !STDOUT_LOGS_LEVEL.equals(property);
    case DocumentFile:
      return DOCUMENT_PROPERTIES.equals(property) || FILE_TYPE.equals(property) || FILE_COMMENT.equals(property) ||
          FILE_ACTIONNAME.equals(property) || FILE_DESCRPTION.equals(property) || FILE_SUBTYPE.equals(
          property) || FILE_NOT_IN_ARCHIVE.equals(property) || NON_PERSISTENT_PROPERTIES.equals(property);
    case Substitution:
      return DOCUMENT_PROPERTIES.equals(property) || FILE_COMMENT.equals(property) || FILE_ACTIONNAME.equals(property) ||
          FILE_DESCRPTION.equals(property) || FILE_SUBTYPE.equals(property) || FILE_NOT_IN_ARCHIVE.equals(property) || NON_PERSISTENT_PROPERTIES.equals(property);
    case LogFile:
      return false;
    default:
      return true;
    }
  }

  /* -- UI -- */

  public Map<String, String> getOutputContentOptions(boolean error, Locale lang) {
    boolean hasInput = this.input != null && Boolean.valueOf(this.input.getExpressionText());
    return stream(OutputContent.values())
        .filter(e -> (!error || e.isError()) && (hasInput || !e.needInput()))
        .collect(toMap(
            content -> JSFHelper.getLabel(LABEL_FAMILY, "RteSource_" + content.name(), content.toString(), lang),
            OutputContent::toString));
  }

  public Map<String, String> getLogLevelOptions(Locale lang) {
    return stream(LogLevel.values())
        .collect(toMap(
            level -> JSFHelper.getLabel(LABEL_FAMILY, "RteSource_" + level.name(), level.toString(), lang),
            LogLevel::toString));
  }

  public String getConverter() {
    return ProcessExpressionConverter.CONVERTER_ID;
  }

  /* -- NEESTED -- */

  /**
   * Output management
   */
  protected abstract class Output {
    protected final DelegateExecution execution;

    protected final File outputDir;

    protected File outFile;

    Output(DelegateExecution execution, File outputDir) {
      this.execution = execution;
      this.outputDir = outputDir;
    }

    public abstract Writer getWriter(DocumentFile dof, Charset charset) throws IOException;

    public abstract void processDocument(DocumentFile dof, OutputResult result) throws IOException;

    protected final DocumentFile createDocumentFile(FileType ftype, String comment, String description, String subtype, String actionName)
        throws IOException {
      // creates document file
      return new DocumentFile(outFile, ftype, actionName,
          execution.getId(), null, comment, description, subtype);
    }

    protected final DocumentFile createDocumentFile(FileType ftype, String comment, String description, String subtype, String actionName, Boolean inArchive)
        throws IOException {
      // creates document file
      return new DocumentFile(outFile, ftype, actionName,
          execution.getId(), null, comment, description, subtype, inArchive);
    }

    protected File createOutputFile(String fname, FileType ftype) {
      File outfile;
      int count = 0;
      String index = "";
      do {
        outfile = new File(this.outputDir, fname.concat(index)
            .concat(ftype.getExtension()));
        index = "-" + (++count);
      }
      while (outfile.isFile());
      return outfile;
    }

    protected void updateDocumentFileName(DocumentFile dof, String property) {
      String newFileName = VariableHelper.getVariable(execution, property, "");
      if (dof == null || dof.getFile() == null || StringUtils.isEmpty(newFileName)) {
        return;
      }
      if (!TransformHelper.isValidFileName(newFileName)) {
        log.warn("Failed to rename filename {} to {} : invalid name", dof.getFile()
            .getName(), newFileName);
        return;
      }
      File newFile = new File(dof.getFile()
          .getParentFile(), newFileName);
      if (newFile.exists()) {
        log.warn("Failed to rename filename {} to {} : rename file already exist", dof.getFile()
            .getName(), newFileName);
        return;
      }
      dof.getFile()
          .renameTo(newFile);
      dof.setFile(newFile);
      VariableHelper.setVariable(execution, property, "");
    }

    protected void updateDocumentFileComment(DocumentFile dof, String property) {
      String newComment = VariableHelper.getVariable(execution, property, null);
      if (dof == null || dof.getFile() == null || StringUtils.isEmpty(newComment)) {
        return;
      }
      dof.setComment(newComment);
      VariableHelper.setVariable(execution, property, "");
    }

    protected void updateDocumentFileType(DocumentFile dof, String property) {
      String newType = VariableHelper.getVariable(execution, property, null);
      if (dof == null || dof.getFile() == null || StringUtils.isEmpty(newType)) {
        return;
      }
      FileType newFileType = null;
      try {
        newFileType = FileType.valueOf(newType);
      }
      catch (IllegalArgumentException illegalArgumentException) {
        log.error("Input file type cannot be interpreted.");
        return;
      }
      dof.setType(newFileType);
      VariableHelper.setVariable(execution, property, "");
    }

    protected void updateDocumentFileSubtype(DocumentFile dof, String property) {
      String newSubtype = VariableHelper.getVariable(execution, property, null);
      if (dof == null || dof.getFile() == null || StringUtils.isEmpty(newSubtype)) {
        return;
      }
      dof.setSubtype(newSubtype);
      VariableHelper.setVariable(execution, property, "");
    }

    protected void updateDocumentFileActionname(DocumentFile dof, String property) {
      String newActionname = VariableHelper.getVariable(execution, property, null);
      if (dof == null || dof.getFile() == null || StringUtils.isEmpty(newActionname)) {
        return;
      }
      dof.setActionName(newActionname);
      VariableHelper.setVariable(execution, property, "");
    }

    protected void updateDocumentFileDescription(DocumentFile dof, String property) {
      String newDescription = VariableHelper.getVariable(execution, property,null);
      if (dof == null || dof.getFile() == null || StringUtils.isEmpty(newDescription)) {
        return;
      }
      dof.setDescription(newDescription);
      VariableHelper.setVariable(execution, property, "");
    }
    protected void updateDocumentAndIndexProperties(DocumentFile dof) throws ParseException, InvalidStatusException {
      updateDocAndIndexProperties(execution, dof.getDocument(), documentProperties, outFile);
    }

  }

  protected void addNonPersistentProperties(DelegateExecution execution, Document document) {}

  protected void updateDocAndIndexProperties(DelegateExecution execution, Document document, Expression documentPropertie, File outFile)
      throws ParseException, InvalidStatusException {
    updateDocumentProperties(execution, document, documentPropertie, outFile);
    documentstatusService.checkStatusCode(document.getStatusAsString());
    if (document.isIndexed()) {
      if (document.getIndexValue() == null) {
        document.setIndexValue(documentService.getIndexable(document));
      }
      BeanUtils.copyProperties(document, document.getIndexValue());
    }
  }

  protected void updateDocumentProperties(DelegateExecution execution, Document document, Expression documentProperties, File outFile)
      throws ParseException {
    final Map<String, Object> props = VariableHelper.getMap(execution, documentProperties);
    if (isEmpty(props)) {
      return;
    }
    BeanPropertyMapperHelper.setValues(outFile, document, props, UPDATABLE_PROPERTIES);
  }

  /**
   * Logs output content
   */
  protected class LogsOutput extends Output {

    boolean isLogger;

    protected TransformEngineBuilder builder;

    LogsOutput(DelegateExecution execution, boolean error, TransformEngineBuilder builder) {
      super(execution, null);
      this.isLogger = error;
      this.builder = builder;
    }

    @Override
    public Writer getWriter(DocumentFile dof, Charset charset) {
      return new Writer() {
        @Override
        public void write(String str, int off, int len) {
          consume(dof, str.substring(off, off + len));
        }

        @Override
        public void write(char[] cbuf, int off, int len) {
          consume(dof, String.valueOf(cbuf, off, len));
        }

        @Override
        public void flush() {
        }

        @Override
        public void close() {
        }

        private void consume(DocumentFile dof, String content) {
          if (isEmpty(content)) {
            return;
          }
          final String message = dof != null && dof.getFile() != null
              ? String.format("%s:%n%s", dof.getFile()
                  .getName(), content)
              : content;
          logMessageWithLevelDependingOnRteLogLevel(builder.getContext()
              .getParameter(LOGLEVEL, LOGLEVEL_DEFAULT),
              builder.getContext()
                  .getParameter(RteRuntime.EXIT_CODE),
              message);
        }
      };
    }

    public void logMessageWithLevelDependingOnRteLogLevel(Object numericVariable, Object exitCode, String msg) {
      if (StringUtils.isEmpty(msg)) {
        return;
      }

      String message = escapeXml11(msg);

      if (exitCode != null && (int) exitCode != 0) {
        log.error(message);
        return;
      }

      int loglevel = ((NumericVariable) numericVariable).getValue()
          .intValue();

      if (loglevel <= 0) {
        log.info(message);
        return;
      }
      else if (loglevel == 1) {
        log.warn(message);
        return;
      }
      else if (loglevel == 2) {
        log.debug(message);
        return;
      }
      log.error(message);
    }

    @Override
    public void processDocument(DocumentFile dof, OutputResult result) throws IOException {
      try {
        if (!this.isLogger && dof != null) {
          updateDocumentFileName(dof, DOF_INPUT_NAME);
          updateDocumentAndIndexProperties(dof);
        }
      }
      catch (ParseException e) {
        log.error("Failed to report execution", e.getMessage());
      }
      catch (InvalidStatusException e) {
        log.error("Failed to report execution {}", e.getMessage());
      }
    }
  }

  /**
   * Document output content
   */
  protected class DocumentOutput extends DocumentFileOutput {
    DocumentOutput(DelegateExecution execution, File outputDir) {
      super(execution, outputDir);
    }

    @Override
    public Writer getWriter(DocumentFile dof, Charset charset) throws IOException {
      final String fname = dof != null && dof.getFile() != null
          ? getBaseName(dof.getFile()
              .getName()) // input dof base nam
          : randomUUID().toString(); // UUID
      outFile = createOutputFile(fname + "-output", this.type);
      return new OutputStreamWriter(new FileOutputStream(outFile), charset == null ? StandardCharsets.UTF_8 : charset);
    }

    @Override
    public void processDocument(DocumentFile dof, OutputResult result) throws IOException {
      try {
        updateDocumentFileName(dof, DOF_INPUT_NAME);
        Document doc = createDocument();
        if (doc == null) {
          return;
        }
        // adds new document to context
        if (dof != null) {
          dof.getDocument()
              .addChild(doc);
        }
        addDocuments(execution, doc);
      }
      catch (ReflectiveOperationException ex) {
        log.error("Could not create document", ex);
        throw new TaskException(ex);
      }
    }

    protected Document createDocument() throws IOException, IllegalAccessException, InvocationTargetException {
      final DocumentFile dfile = createDocumentFile(this.type, getDfComment(), getDfDescription(), getDfSubtype(),
          StringUtils.isNotEmpty(getDfActionName()) ? getDfActionName() : AbstractTransformTask.class.getSimpleName(), getDfInArchive());

      if (isStandardOutputEmpty(dfile)) {
        FileUtils.deleteQuietly(dfile.getFile());
        return null;
      }
      // document file

      updateDocumentFileName(dfile, DOF_OUTPUT_NAME);
      // document
      Document doc = newDocumentInstancePopulated(execution, documentClassName,
          documentProperties, dfile.getFile());
      addNonPersistentProperties(execution, doc);
      // adds DoF
      doc.addFile(dfile);
      // sets empty reference with the DoF file name.
      if (isBlank(doc.getReference())) {
        doc.setReference(getBaseName(dfile.getFile()
            .getName()));
      }
      // status
      if (documentStatus != null) {
        doc.setStatus(status);
      }
      log.debug("new {}", doc);
      return doc;
    }
  }

  /**
   * DocumentFile output content
   */
  protected class DocumentFileOutput extends Output {

    protected final FileType type;

    protected String dfComment;

    protected String dfActionName;

    protected String dfDescription;

    protected String dfSubtype;

    protected final DocumentStatusEntity status;

    DocumentFileOutput(DelegateExecution execution, File outputDir) {
      super(execution, outputDir);
      this.type = getExpressionValue(execution, fileType, FileType.class, FileType.UNKNOWN);
      this.status = new DocumentStatusEntity(getString(execution, documentStatus));
    }

    @Override
    public Writer getWriter(DocumentFile dof, Charset charset) throws IOException {
      outFile = createOutputFile(getBaseName(dof.getFile()
          .getName()) + "-output", this.type);
      return new OutputStreamWriter(new FileOutputStream(outFile), charset == null ? StandardCharsets.UTF_8 : charset);
    }

    public void setOutFile(DocumentFile dof) throws IOException {
      outFile = new File(this.outputDir, getBaseName(dof.getFile()
          .getName()).concat(this.type.getExtension()));
    }

    @Override
    public void processDocument(DocumentFile dof, OutputResult result) throws IOException {
      try {
        updateDocumentFile(dof);
      }
      catch (ParseException e) {
        log.error("Failed to create document file ", e.getMessage());
        VariableHelper.addDeads(execution, dof.getDocument());
      }
      catch (InvalidStatusException e) {
        log.error("Failed to create document file {}", e.getMessage());
        VariableHelper.addDeads(execution, dof.getDocument());
      }
    }

    protected boolean updateDocumentFile(DocumentFile dof) throws ParseException, IOException, InvalidStatusException {
      updateDocumentFileName(dof, DOF_INPUT_NAME);
      updateDocumentFileComment(dof, DOF_INPUT_COMMENT);
      updateDocumentFileType(dof, DOF_INPUT_TYPE);
      updateDocumentFileSubtype(dof, DOF_INPUT_SUBTYPE);
      updateDocumentFileActionname(dof, DOF_INPUT_ACTIONNAME);
      updateDocumentFileDescription(dof, DOF_INPUT_DESCRIPTION);
      updateDocumentProperties(execution, dof.getDocument(), documentProperties, outFile);
      addNonPersistentProperties(execution, dof != null ? dof.getDocument() : null);

      updateDocumentAndIndexProperties(dof);
      if (documentStatus != null) {
        dof.getDocument()
            .setStatus(status);
      }
      String actionName = null;
      if (StringUtils.isNotEmpty(getDfActionName()) && OutputContent.Substitution.name()
          .equals(stdout.getExpressionText())) {
        actionName = getDfActionName();
      }
      else if (StringUtils.isEmpty(getDfActionName()) && OutputContent.Substitution.name()
          .equals(stdout.getExpressionText())) {
            actionName = dof.getActionName();
          }
      else {
        actionName = isNotEmpty(getDfActionName()) ? getDfActionName() : AbstractTransformTask.class.getSimpleName();
      }
      final DocumentFile dfile = createDocumentFile(this.type, getDfComment(), getDfDescription(), getDfSubtype(), actionName, getDfInArchive());

      if (isStandardOutputEmpty(dfile)) {
        FileUtils.deleteQuietly(dfile.getFile());
        return false;
      }

      if (OutputContent.Substitution.name()
          .equals(stdout.getExpressionText())) {
        updateInputDocumentFile(dof, dfile);
        updateDocumentFileName(dof, DOF_OUTPUT_NAME);
        return false;
      }

      dof.getDocument()
          .addFile(dfile);
      updateDocumentFileName(dfile, DOF_OUTPUT_NAME);
      return true;
    }

    public void updateInputDocumentFile(DocumentFile oldDfile, DocumentFile newDfile) throws IOException {
      if (StringUtils.isNotEmpty(newDfile.getComment())) {
        oldDfile.setComment(newDfile.getComment());
      }
      if (StringUtils.isNotEmpty(newDfile.getActionName())) {
        oldDfile.setActionName(newDfile.getActionName());
      }
      if (StringUtils.isNotEmpty(newDfile.getSubtype())) {
        oldDfile.setSubtype(newDfile.getSubtype());
      }
      if (StringUtils.isNotEmpty(newDfile.getDescription())) {
        oldDfile.setDescription(newDfile.getDescription());
      }
      FileUtils.copyFile(newDfile.getFile(), oldDfile.getFile());
      FileUtils.deleteQuietly(newDfile.getFile());
    }

    protected String getDfActionName() {
      if (dfActionName == null)
        dfActionName = getString(execution, fileActionName, "");
      return dfActionName;
    }

    protected String getDfComment() {
      if (dfComment == null)
        dfComment = getString(execution, fileComment, "");
      return dfComment;
    }

    protected Boolean getDfInArchive() {
      return !getBoolean(execution, fileNotInArchive, FALSE);
    }

    protected String getDfDescription() {
      if (dfDescription == null)
        dfDescription = getString(execution, fileDescription, "");
      return dfDescription;
    }

    protected String getDfSubtype() {
      if (dfSubtype == null)
        dfSubtype = getString(execution, fileSubtype, "");
      return dfSubtype;
    }
  }

  protected boolean isStandardOutputEmpty(DocumentFile dfile) throws IOException {
    try (BufferedReader br = new BufferedReader(new FileReader(dfile.getFile()))) {
      return br.readLine() == null;
    }
  }

  /**
   * LogFile output content
   */
  protected class LogFileOutput extends Output {
    private final FileType fileType;

    LogFileOutput(DelegateExecution execution, File outputDir, FileType fileType) {
      super(execution, outputDir);
      this.fileType = fileType;
    }

    @Override
    public Writer getWriter(DocumentFile dof, Charset charset) throws IOException {
      outFile = createOutputFile(getBaseName(dof.getFile()
          .getName()) + "-error", fileType);
      return new OutputStreamWriter(new FileOutputStream(outFile), charset == null ? StandardCharsets.UTF_8 : charset);
    }

    @Override
    public void processDocument(DocumentFile dof, OutputResult result) throws IOException {
      final DocumentFile dfile = createDocumentFile(fileType, null, null, null, AbstractTransformTask.class.getSimpleName());

      if (isStandardOutputEmpty(dfile)) {
        FileUtils.deleteQuietly(dfile.getFile());
        return;
      }

      dof.getDocument()
          .addFile(dfile);
      updateDocumentFileName(dof, DOF_INPUT_NAME);
      updateDocumentFileComment(dof, DOF_INPUT_COMMENT);
      updateDocumentFileType(dof, DOF_INPUT_TYPE);
      updateDocumentFileSubtype(dof, DOF_INPUT_SUBTYPE);
      updateDocumentFileActionname(dof, DOF_INPUT_ACTIONNAME);
      updateDocumentFileDescription(dof, DOF_INPUT_DESCRIPTION);
    }
  }
}
