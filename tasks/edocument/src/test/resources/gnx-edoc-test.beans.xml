<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	<!-- P R O P E R T I E S -->
	<bean id="comPropertyConfigurer" class="com.byzaneo.commons.util.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:/commons.properties</value>
				<value>classpath:/location.properties</value>
				<value>classpath:/xtrade.properties</value>
				<value>/gnx.properties</value>
			</list>
		</property>
	</bean>

    <!-- G C M -->
    <import resource="classpath:/META-INF/spring/gnx-core.beans.xml" />
    
</beans>
