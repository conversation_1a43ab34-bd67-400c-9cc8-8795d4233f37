<!--

    Licensed under European Union Public Licence (EUPL) version 1.2.

-->
<!-- Schematron binding rules generated by <PERSON> -->
<!-- Abstract rules for binding CII to EN16931 -->
<!-- Timestamp: 2016-07-27 22:50:02 +0200 -->
<pattern xmlns="http://purl.oclc.org/dsdl/schematron" abstract="true" id="EN16931-CII">
    <rule context="$Document_Context ">
        <assert test="$CII-SR-001" flag="warning" id="CII-SR-001">[CII-SR-001] - SpecifiedTransactionID should not be present</assert>
        <assert test="$CII-SR-002" flag="warning" id="CII-SR-002">[CII-SR-002] - TestIndicator should not be present</assert>
        <assert test="$CII-SR-003" flag="warning" id="CII-SR-003">[CII-SR-003] - BusinessProcessSpecifiedDocumentContextParameter should exist maximum once</assert>
        <assert test="$CII-SR-006" flag="warning" id="CII-SR-006">[CII-SR-006] - BIMSpecifiedDocumentContextParameter should not be present</assert>
        <assert test="$CII-SR-007" flag="warning" id="CII-SR-007">[CII-SR-007] - ScenarioSpecifiedDocumentContextParameter should not be present</assert>
        <assert test="$CII-SR-008" flag="warning" id="CII-SR-008">[CII-SR-008] - ApplicationSpecifiedDocumentContextParameter should not be present</assert>
        <assert test="$CII-SR-009" flag="fatal" id="CII-SR-009">[CII-SR-009] - GuidelineSpecifiedDocumentContextParameter must exist exactly once</assert>
        <assert test="$CII-SR-010" flag="fatal" id="CII-SR-010">[CII-SR-010] - ID must exist exactly once</assert>
        <assert test="$CII-SR-011" flag="warning" id="CII-SR-011">[CII-SR-011] - SubsetSpecifiedDocumentContextParameter should not be present</assert>
        <assert test="$CII-SR-012" flag="warning" id="CII-SR-012">[CII-SR-012] - MessageStandardSpecifiedDocumentContextParameter should not be present</assert>
    </rule>
    <rule context="$Exchanged_Document ">
        <assert test="$CII-SR-013" flag="warning" id="CII-SR-013">[CII-SR-013] - Name should not be present</assert>
        <assert test="$CII-SR-014" flag="fatal" id="CII-SR-014">[CII-SR-014] - TypeCode must exist exactly once</assert>
        <assert test="$CII-SR-015" flag="warning" id="CII-SR-015">[CII-SR-015] - DateTime should not be present</assert>
        <assert test="$CII-SR-016" flag="warning" id="CII-SR-016">[CII-SR-016] - CopyIndicator should not be present</assert>
        <assert test="$CII-SR-017" flag="warning" id="CII-SR-017">[CII-SR-017] - Purpose should not be present</assert>
        <assert test="$CII-SR-018" flag="warning" id="CII-SR-018">[CII-SR-018] - ControlRequirementIndicator should not be present</assert>
        <assert test="$CII-SR-019" flag="warning" id="CII-SR-019">[CII-SR-019] - LanguageID should not be present</assert>
        <assert test="$CII-SR-020" flag="warning" id="CII-SR-020">[CII-SR-020] - PurposeCode should not be present</assert>
        <assert test="$CII-SR-021" flag="warning" id="CII-SR-021">[CII-SR-021] - RevisionDateTime should not be present</assert>
        <assert test="$CII-SR-022" flag="warning" id="CII-SR-022">[CII-SR-022] - VersionID should not be present</assert>
        <assert test="$CII-SR-023" flag="warning" id="CII-SR-023">[CII-SR-023] - GlobalID should not be present</assert>
        <assert test="$CII-SR-024" flag="warning" id="CII-SR-024">[CII-SR-024] - RevisionID should not be present</assert>
        <assert test="$CII-SR-025" flag="warning" id="CII-SR-025">[CII-SR-025] - PreviousRevisionID should not be present</assert>
        <assert test="$CII-SR-026" flag="warning" id="CII-SR-026">[CII-SR-026] - CategoryCode should not be present</assert>
        <assert test="$CII-SR-027" flag="warning" id="CII-SR-027">[CII-SR-027] - Subject should not be present</assert>
        <assert test="$CII-SR-028" flag="warning" id="CII-SR-028">[CII-SR-028] - ContentCode should not be present</assert>
        
        <assert test="$CII-SR-032" flag="warning" id="CII-SR-032">[CII-SR-032] - ID should not be present</assert>
        <assert test="$CII-SR-033" flag="warning" id="CII-SR-033">[CII-SR-033] - EffectiveSpecifiedPeriod should not be present</assert>
        <assert test="$CII-SR-034" flag="warning" id="CII-SR-034">[CII-SR-034] - IssuerTradeParty should not be present</assert>
    </rule>
    <rule context="$Exchanged_Document_Note ">
        <assert test="$CII-SR-030" flag="warning" id="CII-SR-030">[CII-SR-030] - Content should exist maximum once</assert>
    </rule>
    <rule context="$Invoice_line ">
        <assert test="$CII-SR-035" flag="warning" id="CII-SR-035">[CII-SR-035] - DescriptionCode should not be present</assert>
        <assert test="$CII-SR-036" flag="warning" id="CII-SR-036">[CII-SR-036] - ParentLineID should not be present</assert>
        <assert test="$CII-SR-037" flag="warning" id="CII-SR-037">[CII-SR-037] - LineStatusCode should not be present</assert>
        <assert test="$CII-SR-038" flag="warning" id="CII-SR-038">[CII-SR-038] - LineStatusReasonCode should not be present</assert>
        <assert test="$CII-SR-039" flag="warning" id="CII-SR-039">[CII-SR-039] - IncludedNote should exist maximum once</assert>
        <assert test="$CII-SR-040" flag="warning" id="CII-SR-040">[CII-SR-040] - Content should exist maximum once</assert>
        <assert test="$CII-SR-041" flag="warning" id="CII-SR-041">[CII-SR-041] - SubjectCode should not be present</assert>
        <assert test="$CII-SR-042" flag="warning" id="CII-SR-042">[CII-SR-042] - ID should not be present</assert>
        <assert test="$CII-SR-043" flag="warning" id="CII-SR-043">[CII-SR-043] - CategoryCode should not be present</assert>
        <assert test="$CII-SR-044" flag="warning" id="CII-SR-044">[CII-SR-044] - Subject should not be present</assert>
        <assert test="$CII-SR-221" flag="warning" id="CII-SR-221">[CII-SR-221] - IncludedSubordinateTradeLineItem should not be present</assert>
    </rule>
    <rule context="$SpecifiedTradeProduct ">
        <assert test="$CII-SR-045" flag="warning" id="CII-SR-045">[CII-SR-045] - ID should not be present</assert>
        <assert test="$CII-SR-046" flag="fatal" id="CII-SR-046">[CII-SR-046] - schemeID must be present if GlobalID is present</assert>
        
        <assert test="$CII-SR-048" flag="warning" id="CII-SR-048">[CII-SR-048] - ManufacturerAssignedID should not be present</assert>
        <assert test="$CII-SR-049" flag="warning" id="CII-SR-049">[CII-SR-049] - TradeName should not be present</assert>
        <assert test="$CII-SR-050" flag="warning" id="CII-SR-050">[CII-SR-050] - TypeCode should not be present</assert>
        <assert test="$CII-SR-051" flag="warning" id="CII-SR-051">[CII-SR-051] - NetWeightMeasure should not be present</assert>
        <assert test="$CII-SR-052" flag="warning" id="CII-SR-052">[CII-SR-052] - GrossWeightMeasure should not be present</assert>
        <assert test="$CII-SR-053" flag="warning" id="CII-SR-053">[CII-SR-053] - ProductGroupID should not be present</assert>
        <assert test="$CII-SR-054" flag="warning" id="CII-SR-054">[CII-SR-054] - EndItemTypeCode should not be present</assert>
        <assert test="$CII-SR-055" flag="warning" id="CII-SR-055">[CII-SR-055] - EndItemName should not be present</assert>
        <assert test="$CII-SR-056" flag="warning" id="CII-SR-056">[CII-SR-056] - AreaDensityMeasure should not be present</assert>
        <assert test="$CII-SR-057" flag="warning" id="CII-SR-057">[CII-SR-057] - UseDescription should not be present</assert>
        <assert test="$CII-SR-058" flag="warning" id="CII-SR-058">[CII-SR-058] - BrandName should not be present</assert>
        <assert test="$CII-SR-059" flag="warning" id="CII-SR-059">[CII-SR-059] - SubBrandName should not be present</assert>
        <assert test="$CII-SR-060" flag="warning" id="CII-SR-060">[CII-SR-060] - DrainedNetWeightMeasure should not be present</assert>
        <assert test="$CII-SR-061" flag="warning" id="CII-SR-061">[CII-SR-061] - VariableMeasureIndicator should not be present</assert>
        <assert test="$CII-SR-062" flag="warning" id="CII-SR-062">[CII-SR-062] - ColourCode should not be present</assert>
        <assert test="$CII-SR-063" flag="warning" id="CII-SR-063">[CII-SR-063] - ColourDescription should not be present</assert>
        <assert test="$CII-SR-064" flag="warning" id="CII-SR-064">[CII-SR-064] - Designation should not be present</assert>
        <assert test="$CII-SR-065" flag="warning" id="CII-SR-065">[CII-SR-065] - FormattedCancellationAnnouncedLaunchDateTime should not be present</assert>
        <assert test="$CII-SR-066" flag="warning" id="CII-SR-066">[CII-SR-066] - FormattedLatestProductDataChangeDateTime should not be present</assert>
        <assert test="$CII-SR-067" flag="warning" id="CII-SR-067">[CII-SR-067] - ID should not be present</assert>
        <assert test="$CII-SR-068" flag="warning" id="CII-SR-068">[CII-SR-068] - TypeCode should not be present</assert>
        
        <assert test="$CII-SR-070" flag="warning" id="CII-SR-070">[CII-SR-070] - ValueMeasure should not be present</assert>
        <assert test="$CII-SR-071" flag="warning" id="CII-SR-071">[CII-SR-071] - MeasurementMethodCode should not be present</assert>
    	
        <assert test="$CII-SR-073" flag="warning" id="CII-SR-073">[CII-SR-073] - ValueCode should not be present</assert>
        <assert test="$CII-SR-074" flag="warning" id="CII-SR-074">[CII-SR-074] - ValueDateTime should not be present</assert>
        <assert test="$CII-SR-075" flag="warning" id="CII-SR-075">[CII-SR-075] - ValueIndicator should not be present</assert>
        <assert test="$CII-SR-076" flag="warning" id="CII-SR-076">[CII-SR-076] - ContentTypeCode should not be present</assert>
        <assert test="$CII-SR-077" flag="warning" id="CII-SR-077">[CII-SR-077] - ValueSpecifiedBinaryFile should not be present</assert>
        <assert test="$CII-SR-078" flag="warning" id="CII-SR-078">[CII-SR-078] - ApplicableProductCharacteristicCondition should not be present</assert>
        <assert test="$CII-SR-079" flag="warning" id="CII-SR-079">[CII-SR-079] - ApplicableReferencedStandard should not be present</assert>
        <assert test="$CII-SR-080" flag="warning" id="CII-SR-080">[CII-SR-080] - ApplicableMaterialGoodsCharacteristic should not be present</assert>
        
        <assert test="$CII-SR-081" flag="warning" id="CII-SR-081">[CII-SR-081] - SystemID should not be present</assert>
        <assert test="$CII-SR-082" flag="warning" id="CII-SR-082">[CII-SR-082] - SystemName should not be present</assert>
        <assert test="$CII-SR-083" flag="warning" id="CII-SR-083">[CII-SR-083] - ClassName should not be present</assert>
        <assert test="$CII-SR-084" flag="warning" id="CII-SR-084">[CII-SR-084] - SubClassCode should not be present</assert>
        <assert test="$CII-SR-085" flag="warning" id="CII-SR-085">[CII-SR-085] - ClassProductCharacteristic should not be present</assert>
        <assert test="$CII-SR-086" flag="warning" id="CII-SR-086">[CII-SR-086] - ApplicableReferencedStandard should not be present</assert>
        
        <assert test="$CII-SR-087" flag="warning" id="CII-SR-087">[CII-SR-087] - IndividualTradeProductInstance should not be present</assert>
        <assert test="$CII-SR-088" flag="warning" id="CII-SR-088">[CII-SR-088] - CertificationEvidenceReferenceReferencedDocument should not be present</assert>
        <assert test="$CII-SR-089" flag="warning" id="CII-SR-089">[CII-SR-089] - InspectionReferenceReferencedDocument should not be present</assert>
        
        <assert test="$CII-SR-090" flag="fatal" id="CII-SR-090">[CII-SR-090] - ID should exist maximum once.</assert>
        <assert test="$CII-SR-091" flag="warning" id="CII-SR-091">[CII-SR-091] - Name should not be present</assert>
        <assert test="$CII-SR-092" flag="warning" id="CII-SR-092">[CII-SR-092] - SubordinateTradeCountrySubDivision should not be present</assert>
        
        <assert test="$CII-SR-093" flag="warning" id="CII-SR-093">[CII-SR-093] - LinearSpatialDimension should not be present</assert>
        <assert test="$CII-SR-094" flag="warning" id="CII-SR-094">[CII-SR-094] - MinimumLinearSpatialDimension should not be present</assert>
        <assert test="$CII-SR-095" flag="warning" id="CII-SR-095">[CII-SR-095] - MaximumLinearSpatialDimension should not be present</assert>
        <assert test="$CII-SR-096" flag="warning" id="CII-SR-096">[CII-SR-096] - ManufacturerTradeParty should not be present</assert>
        <assert test="$CII-SR-097" flag="warning" id="CII-SR-097">[CII-SR-097] - PresentationSpecifiedBinaryFile should not be present</assert>
        <assert test="$CII-SR-098" flag="warning" id="CII-SR-098">[CII-SR-098] - MSDSReferenceReferencedDocument should not be present</assert>
        <assert test="$CII-SR-099" flag="warning" id="CII-SR-099">[CII-SR-099] - AdditionalReferenceReferencedDocument should not be present</assert>
        <assert test="$CII-SR-100" flag="warning" id="CII-SR-100">[CII-SR-100] - LegalRightsOwnerTradeParty should not be present</assert>
        <assert test="$CII-SR-101" flag="warning" id="CII-SR-101">[CII-SR-101] - BrandOwnerTradeParty should not be present</assert>
        <assert test="$CII-SR-102" flag="warning" id="CII-SR-102">[CII-SR-102] - IncludedReferencedProduct should not be present</assert>
        <assert test="$CII-SR-103" flag="warning" id="CII-SR-103">[CII-SR-103] - InformationNote should not be present</assert>
    </rule>
	<rule context="$ApplicableProductCharacteristic ">
		<assert test="$CII-SR-069" flag="fatal" id="CII-SR-069">[CII-SR-069] - Description should exist maximum once.</assert>
		<assert test="$CII-SR-072" flag="fatal" id="CII-SR-072">[CII-SR-072] - Value should exist maximum once.</assert>
	</rule>
    <rule context="$SpecifiedLineTradeAgreement ">
		<assert test="$CII-SR-104" flag="warning" id="CII-SR-104">[CII-SR-104] - BuyerReference should not be present</assert>
		<assert test="$CII-SR-105" flag="warning" id="CII-SR-105">[CII-SR-105] - BuyerRequisitionerTradeParty should not be present</assert>
		<assert test="$CII-SR-106" flag="warning" id="CII-SR-106">[CII-SR-106] - ApplicableTradeDeliveryTerms should not be present</assert>
		<assert test="$CII-SR-107" flag="warning" id="CII-SR-107">[CII-SR-107] - SellerOrderReferencedDocument should not be present</assert>
		<assert test="$CII-SR-108" flag="warning" id="CII-SR-108">[CII-SR-108] - IssuerAssignedID should not be present</assert>
		<assert test="$CII-SR-109" flag="warning" id="CII-SR-109">[CII-SR-109] - QuotationReferencedDocument should not be present</assert>
		<assert test="$CII-SR-110" flag="warning" id="CII-SR-110">[CII-SR-110] - ContractReferencedDocument should not be present</assert>
		<assert test="$CII-SR-111" flag="warning" id="CII-SR-111">[CII-SR-111] - DemandForecastReferencedDocument should not be present</assert>
		<assert test="$CII-SR-112" flag="warning" id="CII-SR-112">[CII-SR-112] - PromotionalDealReferencedDocument should not be present</assert>
		<assert test="$CII-SR-113" flag="warning" id="CII-SR-113">[CII-SR-113] - AdditionalReferencedDocument should not be present</assert>

		<assert test="$CII-SR-114" flag="warning" id="CII-SR-114">[CII-SR-114] - TypeCode should not be present</assert>
		<assert test="$CII-SR-115" flag="warning" id="CII-SR-115">[CII-SR-115] - MinimumQuantity should not be present</assert>
		<assert test="$CII-SR-116" flag="warning" id="CII-SR-116">[CII-SR-116] - MaximumQuantity should not be present</assert>
		<assert test="$CII-SR-117" flag="warning" id="CII-SR-117">[CII-SR-117] - ChangeReason should not be present</assert>
		<assert test="$CII-SR-118" flag="warning" id="CII-SR-118">[CII-SR-118] - OrderUnitConversionFactorNumeric should not be present</assert>
		<assert test="$CII-SR-439" flag="fatal" id="CII-SR-439">[CII-SR-439] - ChargeAmount should exist maximum once</assert>
		
		<assert test="$CII-SR-119" flag="warning" id="CII-SR-119">[CII-SR-119] - Only allowances on price a price should be present</assert>
		<assert test="$CII-SR-120" flag="warning" id="CII-SR-120">[CII-SR-120] - ID should not be present</assert>
		<assert test="$CII-SR-121" flag="warning" id="CII-SR-121">[CII-SR-121] - SequenceNumeric should not be present</assert>
		<assert test="$CII-SR-122" flag="warning" id="CII-SR-122">[CII-SR-122] - CalculationPercent should not be present</assert>
		<assert test="$CII-SR-123" flag="warning" id="CII-SR-123">[CII-SR-123] - BasisAmount should not be present</assert>
		<assert test="$CII-SR-124" flag="warning" id="CII-SR-124">[CII-SR-124] - BasisQuantityshould not be present</assert>
		<assert test="$CII-SR-125" flag="warning" id="CII-SR-125">[CII-SR-125] - PrepaidIndicator should not be present</assert>
		<assert test="$CII-SR-126" flag="warning" id="CII-SR-126">[CII-SR-126] - UnitBasisAmount should not be present</assert>
		<assert test="$CII-SR-127" flag="warning" id="CII-SR-127">[CII-SR-127] - ReasonCode should not be present</assert>
		<assert test="$CII-SR-128" flag="warning" id="CII-SR-128">[CII-SR-128] - Reason should not be present</assert>
		<assert test="$CII-SR-129" flag="warning" id="CII-SR-129">[CII-SR-129] - TypeCode should not be present</assert>
		<assert test="$CII-SR-130" flag="warning" id="CII-SR-130">[CII-SR-130] - CategoryTradeTax should not be present</assert>
		<assert test="$CII-SR-131" flag="warning" id="CII-SR-131">[CII-SR-131] - ActualTradeCurrencyExchange should not be present</assert>
		<assert test="$CII-SR-440" flag="fatal" id="CII-SR-440">[CII-SR-440] - ActualAmount should exist maximum once</assert>
		
    	<assert test="$CII-SR-445" flag="warning" id="CII-SR-445">[CII-SR-445] - IncludedTradeTax should not be present</assert>
    	<assert test="$CII-SR-132" flag="warning" id="CII-SR-132">[CII-SR-132] - ValiditySpecifiedPeriod should not be present</assert>
		<assert test="$CII-SR-133" flag="warning" id="CII-SR-133">[CII-SR-133] - DeliveryTradeLocation should not be present</assert>
		<assert test="$CII-SR-134" flag="warning" id="CII-SR-134">[CII-SR-134] - TradeComparisonReferencePrice should not be present</assert>
		<assert test="$CII-SR-135" flag="warning" id="CII-SR-135">[CII-SR-135] - AssociatedReferencedDocument should not be present</assert>
		
		<assert test="$CII-SR-136" flag="warning" id="CII-SR-136">[CII-SR-136] - TypeCode should not be present</assert>
		
		<assert test="$CII-SR-138" flag="warning" id="CII-SR-138">[CII-SR-138] - MinimumQuantity should not be present</assert>
		<assert test="$CII-SR-139" flag="warning" id="CII-SR-139">[CII-SR-139] - MaximumQuantity should not be present</assert>
		<assert test="$CII-SR-140" flag="warning" id="CII-SR-140">[CII-SR-140] - ChangeReason should not be present</assert>
		<assert test="$CII-SR-141" flag="warning" id="CII-SR-141">[CII-SR-141] - OrderUnitConversionFactorNumeric should not be present</assert>
		<assert test="$CII-SR-142" flag="warning" id="CII-SR-142">[CII-SR-142] - AppliedTradeAllowanceCharge should not be present</assert>
    	<assert test="$CII-SR-446" flag="warning" id="CII-SR-446">[CII-SR-446] - IncludedTradeTax should not be present</assert>
		<assert test="$CII-SR-143" flag="warning" id="CII-SR-143">[CII-SR-143] - ValiditySpecifiedPeriod should not be present</assert>
		<assert test="$CII-SR-144" flag="warning" id="CII-SR-144">[CII-SR-144] - DeliveryTradeLocation should not be present</assert>
		<assert test="$CII-SR-145" flag="warning" id="CII-SR-145">[CII-SR-145] - TradeComparisonReferencePrice should not be present</assert>
		<assert test="$CII-SR-146" flag="warning" id="CII-SR-146">[CII-SR-146] - AssociatedReferencedDocument should not be present</assert>
		<assert test="$CII-SR-441" flag="fatal" id="CII-SR-441">[CII-SR-441] - ChargeAmount should exist maximum once</assert>

		<assert test="$CII-SR-147" flag="warning" id="CII-SR-147">[CII-SR-147] - RequisitionerReferencedDocument should not be present</assert>
		<assert test="$CII-SR-148" flag="warning" id="CII-SR-148">[CII-SR-148] - ItemSellerTradeParty should not be present</assert>
		<assert test="$CII-SR-149" flag="warning" id="CII-SR-149">[CII-SR-149] - ItemBuyerTradeParty should not be present</assert>
		<assert test="$CII-SR-150" flag="warning" id="CII-SR-150">[CII-SR-150] - IncludedSpecifiedMarketplace should not be present</assert>
    	<assert test="$CII-SR-447" flag="warning" id="CII-SR-447">[CII-SR-447] - UltimateCustomerOrderReferencedDocument should not be present</assert>
				
    </rule>
    <rule context="$SpecifiedLineTradeDelivery ">
		<assert test="$CII-SR-151" flag="warning" id="CII-SR-151">[CII-SR-151] - RequestedQuantity should not be present</assert>
		<assert test="$CII-SR-152" flag="warning" id="CII-SR-152">[CII-SR-152] - ReceivedQuantity should not be present</assert>
		<assert test="$CII-SR-153" flag="warning" id="CII-SR-153">[CII-SR-153] - ChargeFreeQuantity should not be present</assert>
		<assert test="$CII-SR-154" flag="warning" id="CII-SR-154">[CII-SR-154] - PackageQuantity should not be present</assert>
		<assert test="$CII-SR-155" flag="warning" id="CII-SR-155">[CII-SR-155] - ProductUnitQuantity should not be present</assert>
		<assert test="$CII-SR-156" flag="warning" id="CII-SR-156">[CII-SR-156] - PerPackageUnitQuantity should not be present</assert>
		<assert test="$CII-SR-157" flag="warning" id="CII-SR-157">[CII-SR-157] - NetWeightMeasure should not be present</assert>
		<assert test="$CII-SR-158" flag="warning" id="CII-SR-158">[CII-SR-158] - GrossWeightMeasure should not be present</assert>
		<assert test="$CII-SR-159" flag="warning" id="CII-SR-159">[CII-SR-159] - TheoreticalWeightMeasure should not be present</assert>
		<assert test="$CII-SR-160" flag="warning" id="CII-SR-160">[CII-SR-160] - DespatchedQuantity should not be present</assert>
		<assert test="$CII-SR-161" flag="warning" id="CII-SR-161">[CII-SR-161] - SpecifiedDeliveryAdjustment should not be present</assert>
		<assert test="$CII-SR-162" flag="warning" id="CII-SR-162">[CII-SR-162] - IncludedSupplyChainPackaging should not be present</assert>
		<assert test="$CII-SR-163" flag="warning" id="CII-SR-163">[CII-SR-163] - RelatedSupplyChainConsignment should not be present</assert>
		<assert test="$CII-SR-164" flag="warning" id="CII-SR-164">[CII-SR-164] - ShipToTradeParty should not be present</assert>
		<assert test="$CII-SR-165" flag="warning" id="CII-SR-165">[CII-SR-165] - UltimateShipToTradeParty should not be present</assert>
		<assert test="$CII-SR-166" flag="warning" id="CII-SR-166">[CII-SR-166] - ShipFromTradeParty should not be present</assert>
		<assert test="$CII-SR-167" flag="warning" id="CII-SR-167">[CII-SR-167] - ActualDespatchSupplyChainEvent should not be present</assert>
		<assert test="$CII-SR-168" flag="warning" id="CII-SR-168">[CII-SR-168] - ActualPickUpSupplyChainEvent should not be present</assert>
		<assert test="$CII-SR-169" flag="warning" id="CII-SR-169">[CII-SR-169] - RequestedDeliverySupplyChainEvent should not be present</assert>
		<assert test="$CII-SR-170" flag="warning" id="CII-SR-170">[CII-SR-170] - ActualDeliverySupplyChainEvent should not be present</assert>
		<assert test="$CII-SR-171" flag="warning" id="CII-SR-171">[CII-SR-171] - ActualReceiptSupplyChainEvent should not be present</assert>
		<assert test="$CII-SR-172" flag="warning" id="CII-SR-172">[CII-SR-172] - AdditionalReferencedDocument should not be present</assert>
		<assert test="$CII-SR-173" flag="warning" id="CII-SR-173">[CII-SR-173] - DespatchAdviceReferencedDocument should not be present</assert>
		<assert test="$CII-SR-174" flag="warning" id="CII-SR-174">[CII-SR-174] - ReceivingAdviceReferencedDocument should not be present</assert>
		<assert test="$CII-SR-175" flag="warning" id="CII-SR-175">[CII-SR-175] - DeliveryNoteReferencedDocument should not be present</assert>
		<assert test="$CII-SR-176" flag="warning" id="CII-SR-176">[CII-SR-176] - ConsumptionReportReferencedDocument should not be present</assert>
		<assert test="$CII-SR-177" flag="warning" id="CII-SR-177">[CII-SR-177] - RequestedQuantity should not be present</assert>
	</rule>
	<rule context="$SpecifiedLineTradeSettlement ">
		<assert test="$CII-SR-178" flag="warning" id="CII-SR-178">[CII-SR-178] - PaymentReference should not be present</assert>
		<assert test="$CII-SR-179" flag="warning" id="CII-SR-179">[CII-SR-179] - InvoiceIssuerReference should not be present</assert>
		<assert test="$CII-SR-180" flag="warning" id="CII-SR-180">[CII-SR-180] - TotalAdjustmentAmount should not be present</assert>
		<assert test="$CII-SR-181" flag="warning" id="CII-SR-181">[CII-SR-181] - DiscountIndicator should not be present</assert>
		<assert test="$CII-SR-182" flag="warning" id="CII-SR-182">[CII-SR-182] - CalculatedAmount should not be present</assert>
		
		<assert test="$CII-SR-183" flag="warning" id="CII-SR-183">[CII-SR-183] - IndicatorString should not be present</assert>
		<assert test="$CII-SR-184" flag="warning" id="CII-SR-184">[CII-SR-184] - ID should not be present</assert>
		<assert test="$CII-SR-185" flag="warning" id="CII-SR-185">[CII-SR-185] - SequenceNumeric should not be present</assert>
		<assert test="$CII-SR-186" flag="warning" id="CII-SR-186">[CII-SR-186] - @format should not be present</assert>
		<assert test="$CII-SR-187" flag="warning" id="CII-SR-187">[CII-SR-187] - BasisQuantity should not be present</assert>
		<assert test="$CII-SR-188" flag="warning" id="CII-SR-188">[CII-SR-188] - PrepaidIndicator should not be present</assert>
		<assert test="$CII-SR-189" flag="warning" id="CII-SR-189">[CII-SR-189] - UnitBasisAmount should not be present</assert>
		<assert test="$CII-SR-190" flag="warning" id="CII-SR-190">[CII-SR-190] - TypeCode should not be present</assert>
		<assert test="$CII-SR-191" flag="warning" id="CII-SR-191">[CII-SR-191] - CategoryTradeTax should not be present</assert>
		<assert test="$CII-SR-192" flag="warning" id="CII-SR-192">[CII-SR-192] - ActualTradeCurrencyExchange should not be present</assert>
		<assert test="$CII-SR-193" flag="warning" id="CII-SR-193">[CII-SR-193] - ID should not be present</assert>
		
		<assert test="$CII-SR-194" flag="warning" id="CII-SR-194">[CII-SR-194] - SubtotalCalculatedTradeTax should not be present</assert>
		<assert test="$CII-SR-195" flag="warning" id="CII-SR-195">[CII-SR-195] - SpecifiedLogisticsServiceCharge should not be present</assert>
		<assert test="$CII-SR-196" flag="warning" id="CII-SR-196">[CII-SR-196] - SpecifiedTradePaymentTerms should not be present</assert>
		<assert test="$CII-SR-197" flag="warning" id="CII-SR-197">[CII-SR-197] - ChargeTotalAmount should not be present</assert>
		<assert test="$CII-SR-198" flag="warning" id="CII-SR-198">[CII-SR-198] - AllowanceTotalAmount should not be present</assert>
		<assert test="$CII-SR-199" flag="warning" id="CII-SR-199">[CII-SR-199] - TaxBasisTotalAmount should not be present</assert>
		<assert test="$CII-SR-200" flag="warning" id="CII-SR-200">[CII-SR-200] - TaxTotalAmount should not be present</assert>
		<assert test="$CII-SR-201" flag="warning" id="CII-SR-201">[CII-SR-201] - GrandTotalAmount should not be present</assert>
		<assert test="$CII-SR-202" flag="warning" id="CII-SR-202">[CII-SR-202] - InformationAmount should not be present</assert>
		<assert test="$CII-SR-203" flag="warning" id="CII-SR-203">[CII-SR-203] - TotalAllowanceChargeAmount should not be present</assert>
		<assert test="$CII-SR-204" flag="warning" id="CII-SR-204">[CII-SR-204] - TotalRetailValueInformationAmount should not be present</assert>
		<assert test="$CII-SR-205" flag="warning" id="CII-SR-205">[CII-SR-205] - GrossLineTotalAmount should not be present</assert>
		<assert test="$CII-SR-206" flag="warning" id="CII-SR-206">[CII-SR-206] - NetLineTotalAmount should not be present</assert>
		<assert test="$CII-SR-207" flag="warning" id="CII-SR-207">[CII-SR-207] - NetIncludingTaxesLineTotalAmount should not be present</assert>
		<assert test="$CII-SR-208" flag="warning" id="CII-SR-208">[CII-SR-208] - ProductWeightLossInformationAmount should not be present</assert>
				
		<assert test="$CII-SR-209" flag="warning" id="CII-SR-209">[CII-SR-209] - SpecifiedFinancialAdjustment should not be present</assert>
		<assert test="$CII-SR-210" flag="warning" id="CII-SR-210">[CII-SR-210] - InvoiceReferencedDocument should not be present</assert>
		
		<assert test="$CII-SR-212" flag="warning" id="CII-SR-212">[CII-SR-212] - PayableSpecifiedTradeAccountingAccount should not be present</assert>
		<assert test="$CII-SR-213" flag="warning" id="CII-SR-213">[CII-SR-213] - SetTriggerCode should not be present</assert>
		<assert test="$CII-SR-214" flag="warning" id="CII-SR-214">[CII-SR-214] - TypeCode should not be present</assert>
		<assert test="$CII-SR-215" flag="warning" id="CII-SR-215">[CII-SR-215] - AmountTypeCode should not be present</assert>
		<assert test="$CII-SR-216" flag="warning" id="CII-SR-216">[CII-SR-216] - Name should not be present</assert>
		<assert test="$CII-SR-217" flag="warning" id="CII-SR-217">[CII-SR-217] - CostReferenceDimensionPattern should not be present</assert>
		<assert test="$CII-SR-218" flag="warning" id="CII-SR-218">[CII-SR-218] - PurchaseSpecifiedTradeAccountingAccount should not be present</assert>
		<assert test="$CII-SR-219" flag="warning" id="CII-SR-219">[CII-SR-219] - SalesSpecifiedTradeAccountingAccount should not be present</assert>
		<assert test="$CII-SR-220" flag="warning" id="CII-SR-220">[CII-SR-220] - SpecifiedTradeSettlementFinancialCard should not be present</assert>
		
    </rule>		
	<rule context="$ApplicableHeaderTradeAgreement ">
		<assert test="$CII-SR-442" flag="warning" id="CII-SR-442">[CII-SR-442] - Reference should not be present</assert>
		<assert test="$CII-SR-222" flag="warning" id="CII-SR-222">[CII-SR-222] - RoleCode should not be present</assert>
		
		<assert test="$CII-SR-223" flag="warning" id="CII-SR-223">[CII-SR-223] - LegalClassificationCode should not be present</assert>
		<assert test="$CII-SR-224" flag="warning" id="CII-SR-224">[CII-SR-224] - Name should not be present</assert>
		<assert test="$CII-SR-225" flag="warning" id="CII-SR-225">[CII-SR-225] - PostalTradeAddress should not be present</assert>
		<assert test="$CII-SR-226" flag="warning" id="CII-SR-226">[CII-SR-226] - RoleCode should not be present</assert>
		<assert test="$CII-SR-227" flag="warning" id="CII-SR-227">[CII-SR-227] - ID should not be present</assert>
		<assert test="$CII-SR-228" flag="warning" id="CII-SR-228">[CII-SR-228] - TypeCode should not be present</assert>
		<assert test="$CII-SR-229" flag="warning" id="CII-SR-229">[CII-SR-229] - JobTitle should not be present</assert>
		<assert test="$CII-SR-230" flag="warning" id="CII-SR-230">[CII-SR-230] - Responsibility should not be present</assert>
		<assert test="$CII-SR-231" flag="warning" id="CII-SR-231">[CII-SR-231] - PersonID should not be present</assert>
		<assert test="$CII-SR-232" flag="warning" id="CII-SR-232">[CII-SR-232] - URIID should not be present</assert>
		<assert test="$CII-SR-233" flag="warning" id="CII-SR-233">[CII-SR-233] - ChannelCode should not be present</assert>
		
		<assert test="$CII-SR-234" flag="warning" id="CII-SR-234">[CII-SR-234] - DirectTelephoneUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-235" flag="warning" id="CII-SR-235">[CII-SR-235] - MobileTelephoneUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-236" flag="warning" id="CII-SR-236">[CII-SR-236] - FaxUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-237" flag="warning" id="CII-SR-237">[CII-SR-237] - ChannelCode should not be present</assert>
		<assert test="$CII-SR-238" flag="warning" id="CII-SR-238">[CII-SR-238] - CompleteNumber should not be present</assert>
		<assert test="$CII-SR-239" flag="warning" id="CII-SR-239">[CII-SR-239] - TelexUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-240" flag="warning" id="CII-SR-240">[CII-SR-240] - VOIPUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-241" flag="warning" id="CII-SR-241">[CII-SR-241] - InstantMessagingUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-242" flag="warning" id="CII-SR-242">[CII-SR-242] - SpecifiedNote should not be present</assert>
		<assert test="$CII-SR-243" flag="warning" id="CII-SR-243">[CII-SR-243] - SpecifiedContactPerson should not be present</assert>
		
		<assert test="$CII-SR-244" flag="warning" id="CII-SR-244">[CII-SR-244] - ChannelCode should not be present</assert>
		<assert test="$CII-SR-245" flag="warning" id="CII-SR-245">[CII-SR-245] - CompleteNumber should not be present</assert>
		
		<assert test="$CII-SR-246" flag="warning" id="CII-SR-246">[CII-SR-246] - AssociatedRegisteredTax should not be present</assert>
		<assert test="$CII-SR-247" flag="warning" id="CII-SR-247">[CII-SR-247] - EndPointURIUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-248" flag="warning" id="CII-SR-248">[CII-SR-248] - LogoAssociatedSpecifiedBinaryFile should not be present</assert>
		
		<assert test="$CII-SR-249" flag="warning" id="CII-SR-249">[CII-SR-249] - RoleCode should not be present</assert>
		<assert test="$CII-SR-250" flag="warning" id="CII-SR-250">[CII-SR-250] - Description should not be present</assert>		
		<assert test="$CII-SR-251" flag="warning" id="CII-SR-251">[CII-SR-251] - LegalClassificationCode should not be present</assert>
		<assert test="$CII-SR-252" flag="warning" id="CII-SR-252">[CII-SR-252] - Name should not be present</assert>
		
		<assert test="$CII-SR-254" flag="warning" id="CII-SR-254">[CII-SR-254] - PostalTradeAddress should not be present</assert>
		<assert test="$CII-SR-255" flag="warning" id="CII-SR-255">[CII-SR-255] - AuthorizedLegalRegistration should not be present</assert>

		<assert test="$CII-SR-256" flag="warning" id="CII-SR-256">[CII-SR-256] - ID should not be present</assert>
		<assert test="$CII-SR-257" flag="warning" id="CII-SR-257">[CII-SR-257] - TypeCode should not be present</assert>
		<assert test="$CII-SR-258" flag="warning" id="CII-SR-258">[CII-SR-258] - JobTitle should not be present</assert>
		<assert test="$CII-SR-259" flag="warning" id="CII-SR-259">[CII-SR-259] - Responsibility should not be present</assert>
		<assert test="$CII-SR-260" flag="warning" id="CII-SR-260">[CII-SR-260] - PersonID should not be present</assert>
		<assert test="$CII-SR-261" flag="warning" id="CII-SR-261">[CII-SR-261] - URIID should not be present</assert>
		<assert test="$CII-SR-262" flag="warning" id="CII-SR-262">[CII-SR-262] - ChannelCode should not be present</assert>
		
		<assert test="$CII-SR-263" flag="warning" id="CII-SR-263">[CII-SR-263] - DirectTelephoneUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-264" flag="warning" id="CII-SR-264">[CII-SR-264] - MobileTelephoneUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-265" flag="warning" id="CII-SR-265">[CII-SR-265] - FaxUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-266" flag="warning" id="CII-SR-266">[CII-SR-266] - ChannelCode should not be present</assert>
		<assert test="$CII-SR-267" flag="warning" id="CII-SR-267">[CII-SR-267] - CompleteNumber should not be present</assert>
		<assert test="$CII-SR-268" flag="warning" id="CII-SR-268">[CII-SR-268] - TelexUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-269" flag="warning" id="CII-SR-269">[CII-SR-269] - VOIPUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-270" flag="warning" id="CII-SR-270">[CII-SR-270] - InstantMessagingUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-271" flag="warning" id="CII-SR-271">[CII-SR-271] - SpecifiedNote should not be present</assert>
		<assert test="$CII-SR-272" flag="warning" id="CII-SR-272">[CII-SR-272] - SpecifiedContactPerson should not be present</assert>		
		
		<assert test="$CII-SR-273" flag="warning" id="CII-SR-273">[CII-SR-273] - ChannelCode should not be present</assert>
		<assert test="$CII-SR-274" flag="warning" id="CII-SR-274">[CII-SR-274] - CompleteNumber should not be present</assert>
		
		<assert test="$CII-SR-275" flag="warning" id="CII-SR-275">[CII-SR-275] - AssociatedRegisteredTax should not be present</assert>
		<assert test="$CII-SR-276" flag="warning" id="CII-SR-276">[CII-SR-276] - EndPointURIUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-277" flag="warning" id="CII-SR-277">[CII-SR-277] - LogoAssociatedSpecifiedBinaryFile should not be present</assert>
		
		<assert test="$CII-SR-278" flag="warning" id="CII-SR-278">[CII-SR-278] - SalesAgentTradeParty should not be present</assert>
		<assert test="$CII-SR-279" flag="warning" id="CII-SR-279">[CII-SR-279] - BuyerRequisitionerTradeParty should not be present</assert>
		<assert test="$CII-SR-280" flag="warning" id="CII-SR-280">[CII-SR-280] - BuyerAssignedAccountantTradeParty should not be present</assert>
		<assert test="$CII-SR-281" flag="warning" id="CII-SR-281">[CII-SR-281] - SellerAssignedAccountantTradeParty should not be present</assert>
		<assert test="$CII-SR-282" flag="warning" id="CII-SR-282">[CII-SR-282] - BuyerTaxRepresentativeTradeParty should not be present</assert>
		
		<assert test="$CII-SR-283" flag="warning" id="CII-SR-283">[CII-SR-283] - GlobalID should not be present</assert>
		<assert test="$CII-SR-284" flag="warning" id="CII-SR-284">[CII-SR-284] - RoleCode should not be present</assert>
		<assert test="$CII-SR-285" flag="warning" id="CII-SR-285">[CII-SR-285] - Description should not be present</assert>
		<assert test="$CII-SR-286" flag="warning" id="CII-SR-286">[CII-SR-286] - SpecifiedLegalOrganization should not be present</assert>
		<assert test="$CII-SR-287" flag="warning" id="CII-SR-287">[CII-SR-287] - DefinedTradeContact should not be present</assert>
		<assert test="$CII-SR-288" flag="warning" id="CII-SR-288">[CII-SR-288] - URIUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-289" flag="warning" id="CII-SR-289">[CII-SR-289] - AssociatedRegisteredTax should not be present</assert>
		<assert test="$CII-SR-290" flag="warning" id="CII-SR-290">[CII-SR-290] - EndPointURIUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-291" flag="warning" id="CII-SR-291">[CII-SR-291] - LogoAssociatedSpecifiedBinaryFile should not be present</assert>
		
		<assert test="$CII-SR-292" flag="warning" id="CII-SR-292">[CII-SR-292] - ProductEndUserTradeParty should not be present</assert>
		<assert test="$CII-SR-293" flag="warning" id="CII-SR-293">[CII-SR-293] - ApplicableTradeDeliveryTerms should not be present</assert>
		<assert test="$CII-SR-294" flag="warning" id="CII-SR-294">[CII-SR-294] - LineID should not be present</assert>
		<assert test="$CII-SR-295" flag="warning" id="CII-SR-295">[CII-SR-295] - LineID should not be present</assert>
		<assert test="$CII-SR-296" flag="warning" id="CII-SR-296">[CII-SR-296] - QuotationReferencedDocument should not be present</assert>
		<assert test="$CII-SR-297" flag="warning" id="CII-SR-297">[CII-SR-297] - OrderResponseReferencedDocument should not be present</assert>
		<assert test="$CII-SR-298" flag="warning" id="CII-SR-298">[CII-SR-298] - LineID should not be present</assert>
		<assert test="$CII-SR-299" flag="warning" id="CII-SR-299">[CII-SR-299] - DemandForecastReferencedDocument should not be present</assert>
		<assert test="$CII-SR-300" flag="warning" id="CII-SR-300">[CII-SR-300] - SupplyInstructionReferencedDocument should not be present</assert>
		<assert test="$CII-SR-301" flag="warning" id="CII-SR-301">[CII-SR-301] - PromotionalDealReferencedDocument should not be present</assert>
		<assert test="$CII-SR-302" flag="warning" id="CII-SR-302">[CII-SR-302] - PriceListReferencedDocument should not be present</assert>
		<assert test="$CII-SR-303" flag="warning" id="CII-SR-303">[CII-SR-303] - LineID should not be present</assert>
		<assert test="$CII-SR-304" flag="warning" id="CII-SR-304">[CII-SR-304] - RequisitionerReferencedDocument should not be present</assert>
		<assert test="$CII-SR-305" flag="warning" id="CII-SR-305">[CII-SR-305] - BuyerAgentTradeParty should not be present</assert>
		<assert test="$CII-SR-306" flag="warning" id="CII-SR-306">[CII-SR-306] - PurchaseConditionsReferencedDocument should not be present</assert>
		<assert test="$CII-SR-307" flag="warning" id="CII-SR-307">[CII-SR-307] - Description should not be present</assert>
		<assert test="$CII-SR-448" flag="warning" id="CII-SR-448">[CII-SR-448] - UltimateCustomerOrderReferencedDocument should not be present</assert>
		<assert test="$CII-SR-450" flag="warning" id="CII-SR-450">[CII-SR-450] - Only one  buyer identifier should be present (either the ID or the Global ID)</assert>
		
	</rule>
	<rule context="$ApplicableHeaderTradeDelivery ">
		<assert test="$CII-SR-308" flag="warning" id="CII-SR-308">[CII-SR-308] - RelatedSupplyChainConsignment should not be present</assert>
		
		<assert test="$CII-SR-309" flag="warning" id="CII-SR-309">[CII-SR-309] - RoleCode should not be present</assert>
		<assert test="$CII-SR-310" flag="warning" id="CII-SR-310">[CII-SR-310] - Description should not be present</assert>
		<assert test="$CII-SR-311" flag="warning" id="CII-SR-311">[CII-SR-311] - SpecifiedLegalOrganization should not be present</assert>
		<assert test="$CII-SR-312" flag="warning" id="CII-SR-312">[CII-SR-312] - DefinedTradeContact should not be present</assert>
		<assert test="$CII-SR-313" flag="warning" id="CII-SR-313">[CII-SR-313] - URIUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-314" flag="warning" id="CII-SR-314">[CII-SR-314] - SpecifiedTaxRegistration should not be present</assert>
		<assert test="$CII-SR-315" flag="warning" id="CII-SR-315">[CII-SR-315] - EndPointURIUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-316" flag="warning" id="CII-SR-316">[CII-SR-316] - LogoAssociatedSpecifiedBinaryFile should not be present</assert>
		
		<assert test="$CII-SR-317" flag="warning" id="CII-SR-317">[CII-SR-317] - UltimateShipToTradeParty should not be present</assert>
		<assert test="$CII-SR-318" flag="warning" id="CII-SR-318">[CII-SR-318] - ShipFromTradeParty should not be present</assert>
		<assert test="$CII-SR-319" flag="warning" id="CII-SR-319">[CII-SR-319] - ActualDespatchSupplyChainEvent should not be present</assert>
		<assert test="$CII-SR-320" flag="warning" id="CII-SR-320">[CII-SR-320] - ActualPickUpSupplyChainEvent should not be present</assert>
		
		<assert test="$CII-SR-321" flag="warning" id="CII-SR-321">[CII-SR-321] - ID should not be present</assert>
		<assert test="$CII-SR-322" flag="warning" id="CII-SR-322">[CII-SR-322] - DateTime should not be present</assert>
		<assert test="$CII-SR-323" flag="warning" id="CII-SR-323">[CII-SR-323] - TypeCode should not be present</assert>
		<assert test="$CII-SR-324" flag="warning" id="CII-SR-324">[CII-SR-324] - Description should not be present</assert>
		<assert test="$CII-SR-325" flag="warning" id="CII-SR-325">[CII-SR-325] - DescriptionBinaryObject should not be present</assert>
		<assert test="$CII-SR-326" flag="warning" id="CII-SR-326">[CII-SR-326] - UnitQuantity should not be present</assert>
		<assert test="$CII-SR-327" flag="warning" id="CII-SR-327">[CII-SR-327] - LatestOccurrenceDateTime should not be present</assert>
		<assert test="$CII-SR-328" flag="warning" id="CII-SR-328">[CII-SR-328] - EarliestOccurrenceDateTime should not be present</assert>
		<assert test="$CII-SR-329" flag="warning" id="CII-SR-329">[CII-SR-329] - OccurrenceSpecifiedPeriod should not be present</assert>
		<assert test="$CII-SR-330" flag="warning" id="CII-SR-330">[CII-SR-330] - OccurrenceLogisticsLocation should not be present</assert>
		
		<assert test="$CII-SR-331" flag="warning" id="CII-SR-331">[CII-SR-331] - ActualReceiptSupplyChainEvent should not be present</assert>
		<assert test="$CII-SR-332" flag="warning" id="CII-SR-332">[CII-SR-332] - AdditionalReferencedDocument should not be present</assert>
		<assert test="$CII-SR-333" flag="warning" id="CII-SR-333">[CII-SR-333] - LineID should not be present</assert>
		<assert test="$CII-SR-334" flag="warning" id="CII-SR-334">[CII-SR-334] - LineID should not be present</assert>
		<assert test="$CII-SR-335" flag="warning" id="CII-SR-335">[CII-SR-335] - DeliveryNoteReferencedDocument should not be present</assert>
		<assert test="$CII-SR-336" flag="warning" id="CII-SR-336">[CII-SR-336] - ConsumptionReportReferencedDocument should not be present</assert>
		<assert test="$CII-SR-337" flag="warning" id="CII-SR-337">[CII-SR-337] - PreviousDeliverySupplyChainEvent should not be present</assert>
		<assert test="$CII-SR-338" flag="warning" id="CII-SR-338">[CII-SR-338] - PackingListReferencedDocument should not be present</assert>
		<assert test="$CII-SR-449" flag="warning" id="CII-SR-449">[CII-SR-449] - Only one delivery to location identifier should be present (either the ID or the Global ID)</assert>
	</rule>
	<rule context="$ApplicableHeaderTradeSettlement ">
		<assert test="$CII-SR-339" flag="warning" id="CII-SR-339">[CII-SR-339] - DuePayableAmount should not be present</assert>
		<assert test="$CII-SR-340" flag="warning" id="CII-SR-340">[CII-SR-340] - CreditorReferenceTypeCode should not be present</assert>
		<assert test="$CII-SR-341" flag="warning" id="CII-SR-341">[CII-SR-341] - CreditorReferenceType should not be present</assert>
		<assert test="$CII-SR-342" flag="warning" id="CII-SR-342">[CII-SR-342] - CreditorReferenceIssuerID should not be present</assert>
		
		<assert test="$CII-SR-344" flag="warning" id="CII-SR-344">[CII-SR-344] - PaymentCurrencyCode should not be present</assert>
		<assert test="$CII-SR-345" flag="warning" id="CII-SR-345">[CII-SR-345] - InvoiceIssuerReference should not be present</assert>
		<assert test="$CII-SR-346" flag="warning" id="CII-SR-346">[CII-SR-346] - InvoiceDateTime should not be present</assert>
		<assert test="$CII-SR-347" flag="warning" id="CII-SR-347">[CII-SR-347] - NextInvoiceDateTime should not be present</assert>
		<assert test="$CII-SR-348" flag="warning" id="CII-SR-348">[CII-SR-348] - CreditReasonCode should not be present</assert>
		<assert test="$CII-SR-349" flag="warning" id="CII-SR-349">[CII-SR-349] - CreditReason should not be present</assert>
		<assert test="$CII-SR-350" flag="warning" id="CII-SR-350">[CII-SR-350] - InvoicerTradeParty should not be present</assert>
		<assert test="$CII-SR-351" flag="warning" id="CII-SR-351">[CII-SR-351] - InvoiceeTradeParty should not be present</assert>
		
		<assert test="$CII-SR-352" flag="warning" id="CII-SR-352">[CII-SR-352] - RoleCode should not be present</assert>
		<assert test="$CII-SR-353" flag="warning" id="CII-SR-353">[CII-SR-353] - Description should not be present</assert>
		<assert test="$CII-SR-354" flag="warning" id="CII-SR-354">[CII-SR-354] - LegalClassificationCode should not be present</assert>
		<assert test="$CII-SR-355" flag="warning" id="CII-SR-355">[CII-SR-355] - Name should not be present</assert>
		<assert test="$CII-SR-356" flag="warning" id="CII-SR-356">[CII-SR-356] - TradingBusinessName should not be present</assert>
		<assert test="$CII-SR-357" flag="warning" id="CII-SR-357">[CII-SR-357] - PostalTradeAddress should not be present</assert>
		<assert test="$CII-SR-358" flag="warning" id="CII-SR-358">[CII-SR-358] - AuthorizedLegalRegistration should not be present</assert>
		<assert test="$CII-SR-359" flag="warning" id="CII-SR-359">[CII-SR-359] - DefinedTradeContact should not be present</assert>
		<assert test="$CII-SR-360" flag="warning" id="CII-SR-360">[CII-SR-360] - PostalTradeAddress should not be present</assert>
		<assert test="$CII-SR-361" flag="warning" id="CII-SR-361">[CII-SR-361] - URIUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-362" flag="warning" id="CII-SR-362">[CII-SR-362] - SpecifiedTaxRegistration should not be present</assert>
		<assert test="$CII-SR-363" flag="warning" id="CII-SR-363">[CII-SR-363] - EndPointURIUniversalCommunication should not be present</assert>
		<assert test="$CII-SR-364" flag="warning" id="CII-SR-364">[CII-SR-364] - LogoAssociatedSpecifiedBinaryFile should not be present</assert>
		<assert test="$CII-SR-451" flag="warning" id="CII-SR-451">[CII-SR-451] - Only one payee identifier should be present (either the ID or the Global ID)</assert>
		
		<assert test="$CII-SR-365" flag="warning" id="CII-SR-365">[CII-SR-365] - PayerTradeParty should not be present</assert>
		<assert test="$CII-SR-366" flag="warning" id="CII-SR-366">[CII-SR-366] - TaxApplicableTradeCurrencyExchange should not be present</assert>
		<assert test="$CII-SR-367" flag="warning" id="CII-SR-367">[CII-SR-367] - InvoiceApplicableTradeCurrencyExchange should not be present</assert>
		<assert test="$CII-SR-368" flag="warning" id="CII-SR-368">[CII-SR-368] - PaymentApplicableTradeCurrencyExchange should not be present</assert>
		
		<assert test="$CII-SR-369" flag="warning" id="CII-SR-369">[CII-SR-369] - PaymentChannelCode should not be present</assert>
		<assert test="$CII-SR-370" flag="warning" id="CII-SR-370">[CII-SR-370] - GuaranteeMethodCode should not be present</assert>
		<assert test="$CII-SR-371" flag="warning" id="CII-SR-371">[CII-SR-371] - PaymentMethodCode should not be present</assert>
		<assert test="$CII-SR-443" flag="warning" id="CII-SR-443">[CII-SR-443] - ID should not be present</assert>
		<assert test="$CII-SR-372" flag="warning" id="CII-SR-372">[CII-SR-372] - MicrochipIndicator should not be present</assert>
		<assert test="$CII-SR-373" flag="warning" id="CII-SR-373">[CII-SR-373] - TypeCode should not be present</assert>
		
		<assert test="$CII-SR-375" flag="warning" id="CII-SR-375">[CII-SR-375] - ExpiryDate should not be present</assert>
		<assert test="$CII-SR-376" flag="warning" id="CII-SR-376">[CII-SR-376] - VerificationNumeric should not be present</assert>
		<assert test="$CII-SR-377" flag="warning" id="CII-SR-377">[CII-SR-377] - ValidFromDateTime should not be present</assert>
		<assert test="$CII-SR-378" flag="warning" id="CII-SR-378">[CII-SR-378] - CreditLimitAmount should not be present</assert>
		<assert test="$CII-SR-379" flag="warning" id="CII-SR-379">[CII-SR-379] - CreditAvailableAmount should not be present</assert>
		<assert test="$CII-SR-380" flag="warning" id="CII-SR-380">[CII-SR-380] - InterestRatePercent should not be present</assert>
		<assert test="$CII-SR-381" flag="warning" id="CII-SR-381">[CII-SR-381] - Description should not be present</assert>
		<assert test="$CII-SR-382" flag="warning" id="CII-SR-382">[CII-SR-382] - AccountName should not be present</assert>
		<assert test="$CII-SR-444" flag="warning" id="CII-SR-444">[CII-SR-444] - ProprietaryID should not be present</assert>
		
		<assert test="$CII-SR-384" flag="warning" id="CII-SR-384">[CII-SR-384] - ClearingSystemName should not be present</assert>
		<assert test="$CII-SR-385" flag="warning" id="CII-SR-385">[CII-SR-385] - Name should not be present</assert>
		<assert test="$CII-SR-386" flag="warning" id="CII-SR-386">[CII-SR-386] - LocationFinancialInstitutionAddress should not be present</assert>
		
		
		<assert test="$CII-SR-388" flag="warning" id="CII-SR-388">[CII-SR-388] - ID should not be present</assert>		
		<assert test="$CII-SR-389" flag="warning" id="CII-SR-389">[CII-SR-389] - SequenceNumeric should not be present</assert>
		<assert test="$CII-SR-390" flag="warning" id="CII-SR-390">[CII-SR-390] - BasisQuantity should not be present</assert>
		<assert test="$CII-SR-391" flag="warning" id="CII-SR-391">[CII-SR-391] - PrepaidIndicator should not be present</assert>
		<assert test="$CII-SR-392" flag="warning" id="CII-SR-392">[CII-SR-392] - UnitBasisAmount should not be present</assert>
		<assert test="$CII-SR-393" flag="warning" id="CII-SR-393">[CII-SR-393] - TypeCode should not be present</assert>
		<assert test="$CII-SR-394" flag="warning" id="CII-SR-394">[CII-SR-394] - ActualTradeCurrencyExchange should not be present</assert>
		<assert test="$CII-SR-395" flag="warning" id="CII-SR-395">[CII-SR-395] - SubtotalCalculatedTradeTax should not be present</assert>
		<assert test="$CII-SR-396" flag="warning" id="CII-SR-396">[CII-SR-396] - SpecifiedLogisticsServiceCharge should not be present</assert>
		
		<assert test="$CII-SR-397" flag="warning" id="CII-SR-397">[CII-SR-397] - ID should not be present</assert>
		<assert test="$CII-SR-398" flag="warning" id="CII-SR-398">[CII-SR-398] - FromEventCode should not be present</assert>
		<assert test="$CII-SR-399" flag="warning" id="CII-SR-399">[CII-SR-399] - SettlementPeriodMeasure should not be present</assert>
		<assert test="$CII-SR-400" flag="warning" id="CII-SR-400">[CII-SR-400] - DateTime should not be present</assert>
		<assert test="$CII-SR-401" flag="warning" id="CII-SR-401">[CII-SR-401] - TypeCode should not be present</assert>
		<assert test="$CII-SR-402" flag="warning" id="CII-SR-402">[CII-SR-402] - InstructionTypeCode should not be present</assert>
		
		<assert test="$CII-SR-404" flag="warning" id="CII-SR-404">[CII-SR-404] - PartialPaymentPercent should not be present</assert>
		<assert test="$CII-SR-405" flag="warning" id="CII-SR-405">[CII-SR-405] - PaymentMeansID should not be present</assert>
		<assert test="$CII-SR-406" flag="warning" id="CII-SR-406">[CII-SR-406] - PartialPaymentAmount should not be present</assert>
		<assert test="$CII-SR-407" flag="warning" id="CII-SR-407">[CII-SR-407] - ApplicableTradePaymentPenaltyTerms should not be present</assert>
		<assert test="$CII-SR-408" flag="warning" id="CII-SR-408">[CII-SR-408] - ApplicableTradePaymentDiscountTerms should not be present</assert>
		<assert test="$CII-SR-409" flag="warning" id="CII-SR-409">[CII-SR-409] - PayeeTradeParty should not be present</assert>

		<assert test="$CII-SR-421" flag="warning" id="CII-SR-421">[CII-SR-421] - SpecifiedFinancialAdjustment should not be present</assert>
		<assert test="$CII-SR-422" flag="warning" id="CII-SR-422">[CII-SR-422] - LineID should not be present</assert>
		<assert test="$CII-SR-423" flag="warning" id="CII-SR-423">[CII-SR-423] - ProFormaInvoiceReferencedDocument should not be present</assert>
		<assert test="$CII-SR-424" flag="warning" id="CII-SR-424">[CII-SR-424] - LetterOfCreditReferencedDocument should not be present</assert>
		<assert test="$CII-SR-425" flag="warning" id="CII-SR-425">[CII-SR-425] - FactoringAgreementReferencedDocument should not be present</assert>
		<assert test="$CII-SR-426" flag="warning" id="CII-SR-426">[CII-SR-426] - FactoringListReferencedDocument should not be present</assert>
		<assert test="$CII-SR-427" flag="warning" id="CII-SR-427">[CII-SR-427] - PayableSpecifiedTradeAccountingAccount should not be present</assert>
		<assert test="$CII-SR-428" flag="warning" id="CII-SR-428">[CII-SR-428] - SetTriggerCode should not be present</assert>
		<assert test="$CII-SR-429" flag="warning" id="CII-SR-429">[CII-SR-429] - TypeCode should not be present</assert>
		<assert test="$CII-SR-430" flag="warning" id="CII-SR-430">[CII-SR-430] - AmountTypeCode should not be present</assert>
		<assert test="$CII-SR-431" flag="warning" id="CII-SR-431">[CII-SR-431] - Name should not be present</assert>
		<assert test="$CII-SR-432" flag="warning" id="CII-SR-432">[CII-SR-432] - CostReferenceDimensionPattern should not be present</assert>
		<assert test="$CII-SR-433" flag="warning" id="CII-SR-433">[CII-SR-433] - PurchaseSpecifiedTradeAccountingAccount should not be present</assert>
		<assert test="$CII-SR-434" flag="warning" id="CII-SR-434">[CII-SR-434] - SalesSpecifiedTradeAccountingAccount should not be present</assert>
		<assert test="$CII-SR-435" flag="warning" id="CII-SR-435">[CII-SR-435] - SpecifiedTradeSettlementFinancialCard should not be present</assert>
		<assert test="$CII-SR-436" flag="warning" id="CII-SR-436">[CII-SR-436] - SpecifiedAdvancePayment should not be present</assert>
		<assert test="$CII-SR-437" flag="warning" id="CII-SR-437">[CII-SR-437] - UltimatePayeeTradeParty should not be present</assert>
		
	</rule>
	<rule context="$SpecifiedTradeSettlementHeaderMonetarySummation ">
		
		<assert test="$CII-SR-411" flag="warning" id="CII-SR-411">[CII-SR-411] - InformationAmount should not be present</assert>
		<assert test="$CII-SR-412" flag="warning" id="CII-SR-412">[CII-SR-412] - TotalDiscountAmount should not be present</assert>
		<assert test="$CII-SR-413" flag="warning" id="CII-SR-413">[CII-SR-413] - TotalAllowanceChargeAmount should not be present</assert>
		<assert test="$CII-SR-414" flag="warning" id="CII-SR-414">[CII-SR-414] - RetailValueExcludingTaxInformationAmount should not be present</assert>
		<assert test="$CII-SR-415" flag="warning" id="CII-SR-415">[CII-SR-415] - TotalDepositFeeInformationAmount should not be present</assert>
		<assert test="$CII-SR-416" flag="warning" id="CII-SR-416">[CII-SR-416] - ProductValueExcludingTobaccoTaxInformationAmount should not be present</assert>
		<assert test="$CII-SR-417" flag="warning" id="CII-SR-417">[CII-SR-417] - TotalRetailValueInformationAmount should not be present</assert>
		<assert test="$CII-SR-418" flag="warning" id="CII-SR-418">[CII-SR-418] - GrossLineTotalAmount should not be present</assert>
		<assert test="$CII-SR-419" flag="warning" id="CII-SR-419">[CII-SR-419] - NetLineTotalAmount should not be present</assert>
		<assert test="$CII-SR-420" flag="warning" id="CII-SR-420">[CII-SR-420] - NetIncludingTaxesLineTotalAmount should not be present</assert>
	</rule>
    <rule context="$Invoice ">
        <assert test="$CII-DT-013" flag="fatal" id="CII-DT-013">[CII-DT-013] - languageID should not be present</assert>
        <assert test="$CII-DT-014" flag="fatal" id="CII-DT-014">[CII-DT-014] - languageLocaleID should not be present</assert>

        <assert test="$CII-SR-438" flag="warning" id="CII-SR-438">[CII-SR-438] - ValuationBreakdownStatement should not be present</assert>	
    </rule>
    <rule context="$DocumentContextParameter ">
        <assert test="$CII-SR-004" flag="warning" id="CII-SR-04">[CII-SR-004] - Value should not be present</assert>
        <assert test="$CII-SR-005" flag="warning" id="CII-SR-05">[CII-SR-005] - SpecifiedDocumentVersion should not be present</assert>
    </rule>
    <rule context="$IDType ">
        <assert test="$CII-DT-001" flag="fatal" id="CII-DT-001">[CII-DT-001] - schemeName should not be present</assert>
        <assert test="$CII-DT-002" flag="fatal" id="CII-DT-002">[CII-DT-002] - schemeAgencyName should not be present</assert>
        <assert test="$CII-DT-003" flag="fatal" id="CII-DT-003">[CII-DT-003] - schemeDataURI should not be present</assert>
        <assert test="$CII-DT-004" flag="fatal" id="CII-DT-004">[CII-DT-004] - schemeURI should not be present</assert>
    </rule>
	<rule context="$IDTypeNoAttributes ">
        <assert test="$CII-DT-005" flag="fatal" id="CII-DT-005">[CII-DT-005] - schemeID should not be present</assert>
        <assert test="$CII-DT-006" flag="fatal" id="CII-DT-006">[CII-DT-006] - schemeAgencyID should not be present</assert>
		<assert test="$CII-DT-007" flag="fatal" id="CII-DT-007">[CII-DT-007] - schemeVersionID should not be present</assert>
    </rule>
    <rule context="$TypeCodeType ">
        <assert test="$CII-DT-008" flag="fatal" id="CII-DT-008">[CII-DT-008] - name should not be present</assert>
        <assert test="$CII-DT-009" flag="fatal" id="CII-DT-009">[CII-DT-009] - listURI should not be present</assert>
    </rule>
	<rule context="$TypeCodeTypeNoAttributes ">
        <assert test="$CII-DT-010" flag="fatal" id="CII-DT-010">[CII-DT-010] - listID should not be present</assert>
        <assert test="$CII-DT-011" flag="fatal" id="CII-DT-011">[CII-DT-011] - listAgencyID should not be present</assert>
        <assert test="$CII-DT-012" flag="fatal" id="CII-DT-012">[CII-DT-012] - listVersionID should not be present</assert>
    </rule>
	<rule context="$CategoryCodeNoAttributes ">
		<assert test="$CII-DT-045" flag="warning" id="CII-DT-045">[CII-DT-045] - @listID should not be present</assert>
		<assert test="$CII-DT-046" flag="warning" id="CII-DT-046">[CII-DT-046] - @listAgencyID should not be present</assert>
		<assert test="$CII-DT-047" flag="warning" id="CII-DT-047">[CII-DT-047] - @listVersionID should not be present</assert>
		<assert test="$CII-DT-048" flag="warning" id="CII-DT-048">[CII-DT-048] - @listURI should not be present</assert>
	</rule>
    <rule context="$ReferencedDocumentType ">
        <assert test="$CII-DT-015" flag="fatal" id="CII-DT-015">[CII-DT-015] - URIID should not be present</assert>
        <assert test="$CII-DT-016" flag="fatal" id="CII-DT-016">[CII-DT-016] - StatusCode should not be present</assert>
        <assert test="$CII-DT-017" flag="fatal" id="CII-DT-017">[CII-DT-017] - CopyIndicator should not be present</assert>
        <assert test="$CII-DT-018" flag="fatal" id="CII-DT-018">[CII-DT-018] - TypeCode should not be present</assert>
        <assert test="$CII-DT-019" flag="fatal" id="CII-DT-019">[CII-DT-019] - GlobalID should not be present</assert>
        <assert test="$CII-DT-020" flag="fatal" id="CII-DT-020">[CII-DT-020] - RevisionID should not be present</assert>
        <assert test="$CII-DT-021" flag="fatal" id="CII-DT-021">[CII-DT-021] - Name should not be present</assert>
        <assert test="$CII-DT-022" flag="fatal" id="CII-DT-022">[CII-DT-022] - AttachmentBinaryObject should not be present</assert>
        <assert test="$CII-DT-023" flag="fatal" id="CII-DT-023">[CII-DT-023] - Information should not be present</assert>
        <assert test="$CII-DT-024" flag="fatal" id="CII-DT-024">[CII-DT-024] - ReferenceTypeCode should not be present</assert>
        <assert test="$CII-DT-025" flag="fatal" id="CII-DT-025">[CII-DT-025] - SectionName should not be present</assert>
        <assert test="$CII-DT-026" flag="fatal" id="CII-DT-026">[CII-DT-026] - PreviousRevisionID should not be present</assert>
        <assert test="$CII-DT-027" flag="fatal" id="CII-DT-027">[CII-DT-027] - FormattedIssueDateTime should not be present</assert>
        <assert test="$CII-DT-028" flag="fatal" id="CII-DT-028">[CII-DT-028] - EffectiveSpecifiedPeriod should not be present</assert>
        <assert test="$CII-DT-029" flag="fatal" id="CII-DT-029">[CII-DT-029] - IssuerTradeParty should not be present</assert>
        <assert test="$CII-DT-030" flag="fatal" id="CII-DT-030">[CII-DT-030] - AttachedSpecifiedBinaryFile should not be present</assert>
    </rule>
    <rule context="$AmountType ">
        <assert test="$CII-DT-031" flag="fatal" id="CII-DT-031">[CII-DT-031] - currencyID should not be present</assert>
        <assert test="$CII-DT-032" flag="fatal" id="CII-DT-032">[CII-DT-032] - currencyCodeListVersionID should not be present</assert>
	</rule>
	<rule context="$QuantityType ">
        <assert test="$CII-DT-033" flag="fatal" id="CII-DT-033">[CII-DT-033] - unitCode should not be present</assert>
        <assert test="$CII-DT-034" flag="fatal" id="CII-DT-034">[CII-DT-034] - unitCodeListID should not be present</assert>
        <assert test="$CII-DT-035" flag="fatal" id="CII-DT-035">[CII-DT-035] - unitCodeListAgencyID should not be present</assert>
        <assert test="$CII-DT-036" flag="fatal" id="CII-DT-036">[CII-DT-036] - unitCodeListAgencyName should not be present</assert>
	</rule>
	<rule context="$TradeTaxType ">
        <assert test="$CII-DT-037" flag="fatal" id="CII-DT-037">[CII-DT-037] - TypeCode shall be 'VAT'</assert>
        <assert test="$CII-DT-038" flag="warning" id="CII-DT-038">[CII-DT-038] - CalculatedRate should not be present</assert>
        <assert test="$CII-DT-039" flag="warning" id="CII-DT-039">[CII-DT-039] - CalculationSequenceNumeric should not be present</assert>
        <assert test="$CII-DT-040" flag="warning" id="CII-DT-040">[CII-DT-040] - BasisQuantity should not be present</assert>
        <assert test="$CII-DT-041" flag="warning" id="CII-DT-041">[CII-DT-041] - BasisAmount should not be present</assert>
        <assert test="$CII-DT-042" flag="warning" id="CII-DT-042">[CII-DT-042] - UnitBasisAmount should not be present</assert>
        <assert test="$CII-DT-043" flag="warning" id="CII-DT-043">[CII-DT-043] - LineTotalBasisAmount should not be present</assert>
        <assert test="$CII-DT-044" flag="warning" id="CII-DT-044">[CII-DT-044] - AllowanceChargeBasisAmount should not be present</assert>
        <assert test="$CII-DT-049" flag="warning" id="CII-DT-049">[CII-DT-049] - CurrencyCode should not be present</assert>
        <assert test="$CII-DT-050" flag="warning" id="CII-DT-050">[CII-DT-050] - Jurisdiction should not be present</assert>
        <assert test="$CII-DT-051" flag="warning" id="CII-DT-051">[CII-DT-051] - CustomsDutyIndicator should not be present</assert>
        <assert test="$CII-DT-052" flag="warning" id="CII-DT-052">[CII-DT-052] - ExemptionReasonCode should not be present</assert>
		<assert test="$CII-DT-098" flag="warning" id="CII-DT-098">[CII-DT-098] - ExemptionReason should not be present</assert>
        <assert test="$CII-DT-053" flag="warning" id="CII-DT-053">[CII-DT-053] - TaxBasisAllowanceRate should not be present</assert>
        <assert test="$CII-DT-054" flag="warning" id="CII-DT-054">[CII-DT-054] - TaxPointDate should not be present</assert>
        <assert test="$CII-DT-055" flag="warning" id="CII-DT-055">[CII-DT-055] - Type should not be present</assert>
        <assert test="$CII-DT-056" flag="warning" id="CII-DT-056">[CII-DT-056] - InformationAmount should not be present</assert>
        <assert test="$CII-DT-057" flag="warning" id="CII-DT-057">[CII-DT-057] - CategoryName should not be present</assert>
        <assert test="$CII-DT-058" flag="warning" id="CII-DT-058">[CII-DT-058] - DueDateTypeCode should not be present</assert>
        <assert test="$CII-DT-059" flag="warning" id="CII-DT-059">[CII-DT-059] - @format should not be present</assert>
        <assert test="$CII-DT-060" flag="warning" id="CII-DT-060">[CII-DT-060] - SpecifiedTradeAccountingAccount should not be present</assert>
        <assert test="$CII-DT-061" flag="warning" id="CII-DT-061">[CII-DT-061] - ServiceSupplyTradeCountry should not be present</assert>
        <assert test="$CII-DT-062" flag="warning" id="CII-DT-062">[CII-DT-062] - BuyerRepayableTaxSpecifiedTradeAccountingAccount should not be present</assert>
        <assert test="$CII-DT-063" flag="warning" id="CII-DT-063">[CII-DT-063] - SellerPayableTaxSpecifiedTradeAccountingAccount should not be present</assert>
        <assert test="$CII-DT-064" flag="warning" id="CII-DT-064">[CII-DT-064] - SellerRefundableTaxSpecifiedTradeAccountingAccount should not be present</assert>
        <assert test="$CII-DT-065" flag="warning" id="CII-DT-065">[CII-DT-065] - BuyerDeductibleTaxSpecifiedTradeAccountingAccount should not be present</assert>
        <assert test="$CII-DT-066" flag="warning" id="CII-DT-066">[CII-DT-066] - BuyerNonDeductibleTaxSpecifiedTradeAccountingAccount should not be present</assert>   
        <assert test="$CII-DT-067" flag="warning" id="CII-DT-067">[CII-DT-067] - PlaceApplicableTradeLocation should not be present</assert>        
	</rule>
	<rule context="$BillingSpecifiedPeriodType ">
        <assert test="$CII-DT-068" flag="fatal" id="CII-DT-068">[CII-DT-068] - DateTime shall not be used.</assert>
        <assert test="$CII-DT-069" flag="fatal" id="CII-DT-069">[CII-DT-069] - DurationMeasure shall not be used.</assert>
        <assert test="$CII-DT-070" flag="fatal" id="CII-DT-070">[CII-DT-070] - InclusiveIndicator shall not be used.</assert>
        <assert test="$CII-DT-071" flag="fatal" id="CII-DT-071">[CII-DT-071] - Description shall not be used.</assert>
        <assert test="$CII-DT-072" flag="fatal" id="CII-DT-072">[CII-DT-072] - DateTime shall not be used.</assert>
        <assert test="$CII-DT-073" flag="fatal" id="CII-DT-073">[CII-DT-073] - CompleteDateTime shall not be used.</assert>
        <assert test="$CII-DT-074" flag="fatal" id="CII-DT-074">[CII-DT-074] - OpenIndicator shall not be used.</assert>
        <assert test="$CII-DT-075" flag="fatal" id="CII-DT-075">[CII-DT-075] - SeasonCode shall not be used.</assert>
        <assert test="$CII-DT-076" flag="fatal" id="CII-DT-076">[CII-DT-076] - ID shall not be used.</assert>
        <assert test="$CII-DT-077" flag="fatal" id="CII-DT-077">[CII-DT-077] - Name shall not be used.</assert>
        <assert test="$CII-DT-078" flag="fatal" id="CII-DT-078">[CII-DT-078] - SequenceNumeric shall not be used.</assert>
        <assert test="$CII-DT-079" flag="fatal" id="CII-DT-079">[CII-DT-079] - StartDateFlexibilityCode shall not be used.</assert>
        <assert test="$CII-DT-080" flag="fatal" id="CII-DT-080">[CII-DT-080] - ContinuousIndicator shall not be used.</assert>
        <assert test="$CII-DT-081" flag="fatal" id="CII-DT-081">[CII-DT-081] - PurposeCode shall not be used.</assert>
    </rule>
    <rule context="$PostalTradeAddressType ">
        <assert test="$CII-DT-082" flag="fatal" id="CII-DT-082">[CII-DT-082] - ID shall not be used.</assert>
        <assert test="$CII-DT-083" flag="fatal" id="CII-DT-083">[CII-DT-083] - PostOfficeBox shall not be used.</assert>
        <assert test="$CII-DT-084" flag="fatal" id="CII-DT-084">[CII-DT-084] - BuildingName shall not be used.</assert>
        <assert test="$CII-DT-086" flag="fatal" id="CII-DT-086">[CII-DT-086] - LineFour shall not be used.</assert>
        <assert test="$CII-DT-087" flag="fatal" id="CII-DT-087">[CII-DT-087] - LineFive shall not be used.</assert>
        <assert test="$CII-DT-088" flag="fatal" id="CII-DT-088">[CII-DT-088] - StreetName shall not be used.</assert>
        <assert test="$CII-DT-089" flag="fatal" id="CII-DT-089">[CII-DT-089] - CitySubDivisionName shall not be used.</assert>
        <assert test="$CII-DT-090" flag="fatal" id="CII-DT-090">[CII-DT-090] - CountryName shall not be used.</assert>
        <assert test="$CII-DT-091" flag="fatal" id="CII-DT-091">[CII-DT-091] - CountrySubDivisionID shall not be used.</assert>
        <assert test="$CII-DT-092" flag="fatal" id="CII-DT-092">[CII-DT-092] - AttentionOf shall not be used.</assert>
        <assert test="$CII-DT-093" flag="fatal" id="CII-DT-093">[CII-DT-093] - CareOf shall not be used.</assert>
        <assert test="$CII-DT-094" flag="fatal" id="CII-DT-094">[CII-DT-094] - BuildingNumber shall not be used.</assert>
        <assert test="$CII-DT-095" flag="fatal" id="CII-DT-095">[CII-DT-095] - DepartmentName shall not be used.</assert>
        <assert test="$CII-DT-096" flag="fatal" id="CII-DT-096">[CII-DT-096] - AdditionalStreetName shall not be used.</assert>
    </rule>
</pattern>
