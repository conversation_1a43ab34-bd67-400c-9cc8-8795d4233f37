<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<svrl:schematron-output xmlns:svrl="http://purl.oclc.org/dsdl/svrl"
                        xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                        xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
                        xmlns:cn="urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2"
                        xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"
                        xmlns:iso="http://purl.oclc.org/dsdl/schematron"
                        xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDataTypes-2"
                        xmlns:saxon="http://saxon.sf.net/"
                        xmlns:schold="http://www.ascc.net/xml/schematron"
                        xmlns:ubl="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                        xmlns:udt="urn:oasis:names:specification:ubl:schema:xsd:UnqualifiedDataTypes-2"
                        xmlns:xhtml="http://www.w3.org/1999/xhtml"
                        xmlns:xs="http://www.w3.org/2001/XMLSchema"
                        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                        schemaVersion=""
                        title="EN16931  model bound to UBL"><!--   
		   
		   
		 -->
   <svrl:ns-prefix-in-attribute-values prefix="ext"
                                       uri="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"/>
   <svrl:ns-prefix-in-attribute-values prefix="cbc"
                                       uri="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"/>
   <svrl:ns-prefix-in-attribute-values prefix="cac"
                                       uri="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"/>
   <svrl:ns-prefix-in-attribute-values prefix="qdt"
                                       uri="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDataTypes-2"/>
   <svrl:ns-prefix-in-attribute-values prefix="udt"
                                       uri="urn:oasis:names:specification:ubl:schema:xsd:UnqualifiedDataTypes-2"/>
   <svrl:ns-prefix-in-attribute-values prefix="cn"
                                       uri="urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2"/>
   <svrl:ns-prefix-in-attribute-values prefix="ubl"
                                       uri="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"/>
   <svrl:ns-prefix-in-attribute-values prefix="xs" uri="http://www.w3.org/2001/XMLSchema"/>
   <svrl:active-pattern document="" id="UBL-model" name="UBL-model"/>
   <svrl:active-pattern document="" id="UBL-syntax" name="UBL-syntax"/>
   <svrl:active-pattern document="" id="Codesmodel" name="Codesmodel"/>
</svrl:schematron-output>
