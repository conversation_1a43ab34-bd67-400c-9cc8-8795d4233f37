<Invoice xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd">
    <!--TESTED EXT-FR-FE-137-->
    <InvoiceHeader>
        <!--BT-1-->
        <InvoiceNumber>10000046</InvoiceNumber>
        <!--BT-2-->
        <InvoiceIssueDate>2020-01-05T00:00:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <!--BT-13-->
                <core:BuyerOrderNumber>10000046</core:BuyerOrderNumber>
                <!--BT-14-->
                <core:SellerOrderNumber>999</core:SellerOrderNumber>
                <core:PurchaseOrderDate>2020-01-02T00:00:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>
            <OtherInvoiceReferences>
                <core:ReferenceCoded>
                    <!--BT-11-->
                    <core:ReferenceTypeCoded>ProjectNumber</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>10000046</core:RefNum>
                        <core:RefDate>2020-01-04T00:00:00</core:RefDate>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
                <core:ReferenceCoded>
                    <!--BT-15-->
                    <core:ReferenceTypeCoded>ReceiptNumber</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>GI87976</core:RefNum>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
                <core:ReferenceCoded>
                    <!--BT-17-->
                    <core:ReferenceTypeCoded>RequestforQuotationReference</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>GI87976</core:RefNum>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
                <core:ReferenceCoded>
                    <!--BT-18-->
                    <core:ReferenceTypeCoded>ObjectIdentifier</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>GI87976</core:RefNum>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
                <core:ReferenceCoded>
                    <!--BT-18-1-->
                    <core:ReferenceTypeCoded>Other</core:ReferenceTypeCoded>
                    <core:ReferenceTypeCodedOther>AAA</core:ReferenceTypeCodedOther>
                </core:ReferenceCoded>
                <core:ReferenceCoded>
                    <core:ReferenceTypeCoded>PriceListNumber</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>GI87976</core:RefNum>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
                <core:ReferenceCoded>
                    <core:ReferenceTypeCoded>AccountNumber</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>5R7897</core:RefNum>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
            </OtherInvoiceReferences>
            <AccountNumber>
                <!--BT-10-->
                <core:RefNum>RefNum</core:RefNum>
                <core:RefDate>2003-01-01T00:00:07</core:RefDate>
            </AccountNumber>
            <ContractReference>
                <!--BT-12-->
                <core:ContractID>
                    <core:Ident>4567</core:Ident>
                </core:ContractID>
                <!--EXT-FR-FE-01-->
                <core:TypeOfContract>
                    <core:ContractTypeCoded>Other</core:ContractTypeCoded>
                    <core:ContractTypeCodedOther>MARCHE</core:ContractTypeCodedOther>
                </core:TypeOfContract>
            </ContractReference>
            <!--BT-16-->
            <ASNNumber>
                <core:RefNum>5679</core:RefNum>
            </ASNNumber>
            <!--BT-19-->
            <CostAllocation>
                <CostAllocationNumber>
                    <core:RefNum>6789</core:RefNum>
                </CostAllocationNumber>
            </CostAllocation>
            <!--BG-3-->
            <ListOfRelatedInvoiceRef>
                <RelatedInvoiceRef>
                    <!--EXT-FR-FE-02-->
                    <RelatedInvoiceType>
                        <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
                    </RelatedInvoiceType>
                    <InvoiceNumber>
                        <!--BT-25-->
                        <core:RefNum>6789</core:RefNum>
                        <!--BT-26-->
                        <core:RefDate>2020-01-05T00:00:00</core:RefDate>
                    </InvoiceNumber>
                </RelatedInvoiceRef>
            </ListOfRelatedInvoiceRef>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <!--BT-3-->
        <InvoiceType>
            <InvoiceTypeCoded>CreditNoteGoodsAndServices</InvoiceTypeCoded>
        </InvoiceType>
        <!--BT-5-->
        <InvoiceCurrency>
           <!-- <core:CurrencyCoded>Other</core:CurrencyCoded>-->
            <core:CurrencyCoded>EUR</core:CurrencyCoded>
        </InvoiceCurrency>
        <!--BT-6-->
        <TaxAccountingCurrency>
            <!--<core:CurrencyCoded>Other</core:CurrencyCoded>-->
            <core:CurrencyCoded>AED</core:CurrencyCoded>
        </TaxAccountingCurrency>
        <!--BT-7-->
        <TaxPointDate>2023-01-01T00:00:07</TaxPointDate>
        <InvoiceLanguage>
            <core:LanguageCoded>en</core:LanguageCoded>
        </InvoiceLanguage>
        <TaxReference>
            <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
            <core:TaxFunctionQualifierCoded>TaxRelatedInformation</core:TaxFunctionQualifierCoded>
            <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
            <core:TaxCategoryCodedOther>NotApplicable</core:TaxCategoryCodedOther>
            <!--BT-8-->
            <core:TaxTreatmentCoded>Other</core:TaxTreatmentCoded>
            <core:TaxTreatmentCodedOther>29</core:TaxTreatmentCodedOther>
        </TaxReference>
        <InvoiceDates>
            <!--BT-9-->
            <InvoiceDueDate>2020-02-20T00:00:00</InvoiceDueDate>
            <!--BT-72-->
            <ActualShipDate>2020-01-04T00:00:00</ActualShipDate>
            <!--BG-14-->
            <InvoicingPeriod>
                <!--BT-73-->
                <core:StartDate>2020-01-04T00:00:00</core:StartDate>
                <!--BT-74-->
                <core:EndDate>2025-01-04T00:00:00</core:EndDate>
            </InvoicingPeriod>
            <ListOfOtherInvoiceDates>
                <core:DateCoded>
                    <core:Date>2024-01-03T12:18:43</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>DocumentReceivedDateTime</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
                <core:DateCoded>
                    <core:Date>2020-01-05T12:32:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>PreparationDateTimeOfDocument</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfOtherInvoiceDates>
        </InvoiceDates>
        <InvoiceParty>
            <!--BG-7-->
            <BuyerParty>
                <core:PartyID>
                    <!--BT-46a-->
                    <core:Ident>3026990099715</core:Ident>
                    <core:Agency>
                        <!--BT-46a-1-->
                        <core:AgencyCoded>Other</core:AgencyCoded>
                        <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                    </core:Agency>
                </core:PartyID>
                <!--BG-8-->
                <core:NameAddress>
                    <!--BT-44-->
                    <core:Name1>BUYER ADDRESS</core:Name1>
                    <!--BT-50-->
                    <core:Street>5 RUE AVENUE</core:Street>
                    <!--BT-51-->
                    <core:StreetSupplement1>6 RUE AVENUE</core:StreetSupplement1>
                    <!--BT-163-->
                    <core:StreetSupplement2>7 RUE AVENUE</core:StreetSupplement2>
                    <!--BT-52-->
                    <core:City>Paris</core:City>
                    <!--BT-53-->
                    <core:PostalCode>127431</core:PostalCode>
                    <!--BT-54-->
                    <core:Region>
                        <core:RegionCoded>Other</core:RegionCoded>
                        <core:RegionCodedOther>ALSACE</core:RegionCodedOther>
                    </core:Region>
                    <!--BT-55-->
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <!--BT-45-->
                    <core:RegisteredName></core:RegisteredName>
                    <!--BT-48-->
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR6578392</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
                <core:ListOfIdentifier>
                    <!--BT-46b-->
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIRET</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>0226</core:Ident>
                    </core:Identifier>
                    <!--BT-46c-->
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>ROUTAGE</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>FR45677710</core:Ident>
                    </core:Identifier>
                    <!--BT-47-->
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <!--BT-47-1-->
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>FR439787360</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:PrimaryContact>
                    <core:ListOfContactNumber>
                        <!--BT-49-->
                        <core:ContactNumber>
                            <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                            <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                </core:PrimaryContact>
                <!--BG-9-->
                <core:OtherContacts>
                    <core:Contact>
                        <!--BT-56-->
                        <core:ContactName>Andreea Pop</core:ContactName>
                        <core:ListOfContactNumber>
                            <!--BT-57-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue>0745643123</core:ContactNumberValue>
                            </core:ContactNumber>
                            <!--BT-58-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:Contact>
                </core:OtherContacts>
            </BuyerParty>
            <!--BG-4-->
            <SellerParty>
                <core:PartyID>
                    <!--BT-29-->
                    <core:Ident>ABC</core:Ident>
                    <!--BT-29-1-->
                    <core:Agency>
                        <core:AgencyCoded>Other</core:AgencyCoded>
                        <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                    </core:Agency>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <!--BT-33-->
                        <core:Agency>
                            <core:AgencyCoded>AssignedByNationalTradeAgency</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>BusinessLegalStructureType</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>SOCIETE ANONYME</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <!--BT-33-->
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>LegalCapital</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>250000 EUR</core:Ident>
                    </core:Identifier>
                    <!--BT-30-->
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>439787360</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                        <core:Ident>403501596 RCS NANCY</core:Ident>
                    </core:Identifier>
                    <!--BT-29-b-->
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIRET</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>0226</core:Ident>
                    </core:Identifier>
                    <!--BT-29-b-1-->
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <!--<core:AgencyCodedOther>0226</core:AgencyCodedOther>-->
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIRET</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>0226</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <!--BG-5-->
                <core:NameAddress>
                    <!--BT-27-->
                    <core:Name1>CEREAL</core:Name1>
                    <!--BT-35-->
                    <core:Street>2 RUE DES ARBRES</core:Street>
                    <!--BT-36-->
                    <core:StreetSupplement1>3 RUE DES ARBRES</core:StreetSupplement1>
                    <!--BT-162-->
                    <core:StreetSupplement2>4 RUE DES ARBRES</core:StreetSupplement2>
                    <!--BT-38-->
                    <core:PostalCode>54000</core:PostalCode>
                    <!--BT-37-->
                    <core:City>NANCY</core:City>
                    <core:Country>
                        <!--BT-40-->
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                    <!--BT-39-->
                    <core:Region>
                        <core:RegionCoded>Other</core:RegionCoded>
                        <core:RegionCodedOther>LORENA</core:RegionCodedOther>
                    </core:Region>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <!--PRECONDITION FOR BT-31-->
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <!--PRECONDITION FOR BT-32-->
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>FiscalID</core:AgencyCodedOther>
                        </core:Agency>
                        <!--BT-31 && BT-32-->
                        <core:Ident>FR25439787360</core:Ident>
                    </core:TaxIdentifier>
                    <!--BT-28-->
                    <core:RegisteredName>PEINTOU</core:RegisteredName>
                </core:PartyTaxInformation>
                <core:PrimaryContact>
                    <core:ListOfContactNumber>
                        <!--BT-34-->
                        <core:ContactNumber>
                            <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                            <core:ContactNumberValue>768</core:ContactNumberValue>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                </core:PrimaryContact>
                <!--BG-6-->
                <core:OtherContacts>
                    <core:Contact>
                        <!--BT-41-->
                        <core:ContactName>Peter Jonson</core:ContactName>
                        <core:ListOfContactNumber>
                            <!--BT-42-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue>0762345678</core:ContactNumberValue>
                            </core:ContactNumber>
                            <!--BT-43-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:Contact>
                </core:OtherContacts>
            </SellerParty>
            <!--BG-13-->
            <ShipToParty>
                <core:PartyID>
                    <!--BT-71-->
                    <core:Ident>3026990099807</core:Ident>
                    <!--BT-71-1-->
                    <core:Agency>
                        <core:AgencyCoded>Other</core:AgencyCoded>
                        <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                    </core:Agency>
                </core:PartyID>
                <!--BG-15-->
                <core:NameAddress>
                    <!--BT-70-->
                    <core:Name1>DISTRIBUTOUT</core:Name1>
                    <!--BT-75-->
                    <core:Street>7 RUE CONRAD 1E</core:Street>
                    <!--BT-76-->
                    <core:StreetSupplement1>8 RUE CONRAD 1E</core:StreetSupplement1>
                    <!--BT-165-->
                    <core:StreetSupplement2>9 RUE CONRAD 1E</core:StreetSupplement2>
                    <!--BT-77-->
                    <core:City>Lyon</core:City>
                    <!--BT-78-->
                    <core:PostalCode>848803</core:PostalCode>
                    <!--BT-79-->
                    <core:Region>
                        <core:RegionCoded>Other</core:RegionCoded>
                        <core:RegionCodedOther>Region ShipTo</core:RegionCodedOther>
                    </core:Region>
                    <!--BT-80-->
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
            </ShipToParty>
            <!--EXT-FR-FE-BG-04-->
            <BillToParty>
                <core:PartyID>
                    <!--EXT-FR-FE-92-->
                    <core:Ident>DAN</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <!--EXT-FR-FE-94-->
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>461312816</core:Ident>
                    </core:Identifier>
                    <!--EXT-FR-FE-93-->
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                        </core:Agency>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <!--EXT-FR-FE-100-->
                <core:NameAddress>
                    <!--EXT-FR-FE-89-->
                    <core:Name1>DISTRIBUTOUT</core:Name1>
                    <!--EXT-FR-FE-101-->
                    <core:Street>8 RUE CAUMARTIN</core:Street>
                    <!--EXT-FR-FE-102-->
                    <core:StreetSupplement1>8 RUE CAUMARTIN2</core:StreetSupplement1>
                    <!--EXT-FR-FE-103-->
                    <core:StreetSupplement2>8 RUE CAUMARTIN34</core:StreetSupplement2>
                    <!--EXT-FR-FE-105-->
                    <core:PostalCode>75009</core:PostalCode>
                    <!--EXT-FR-FE-104-->
                    <core:City>PARIS</core:City>
                    <!--EXT-FR-FE-106-->
                    <core:Region>
                        <core:RegionCoded>Other</core:RegionCoded>
                        <core:RegionCodedOther>Corsica</core:RegionCodedOther>
                    </core:Region>
                    <!--EXT-FR-FE-107-->
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <!--EXT-FR-FE-96-->
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR36461312816</core:Ident>
                    </core:TaxIdentifier>
                    <!--EXT-FR-FE-91-->
                    <core:RegisteredName>DISTRIBUTOUT NEW</core:RegisteredName>
                </core:PartyTaxInformation>
                <!--EXT-FR-FE-108-->
                <core:OtherContacts>
                    <core:Contact>
                        <!--EXT-FR-FE-109-->
                        <core:ContactName>Joanne Nous</core:ContactName>
                        <core:ListOfContactNumber>
                            <!--EXT-FR-FE-110-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue>0789654347</core:ContactNumberValue>
                            </core:ContactNumber>
                            <!--EXT-FR-FE-111-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:Contact>
                </core:OtherContacts>
            </BillToParty>
            <ShipFromParty>
                <core:PartyID>
                    <core:Ident>3014531200249</core:Ident>
                </core:PartyID>
            </ShipFromParty>
            <ListOfPartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <!--EXT-FR-FE-06-->
                        <core:Ident>3014531200300</core:Ident>
                        <!--EXT-FR-FE-07-->
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                        </core:Agency>
                    </core:PartyID>
                    <core:ListOfIdentifier>
                        <!--EXT-FR-FE-08-->
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>439787360</core:Ident>
                        </core:Identifier>
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>Other</core:AgencyCoded>
                                <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                                <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                            </core:Agency>
                            <core:Ident>403501596 RCS NANCY</core:Ident>
                        </core:Identifier>
                    </core:ListOfIdentifier>
                    <!--EXT-FR-FE-14-->
                    <core:NameAddress>
                        <!--EXT-FR-FE-03-->
                        <core:Name1>PEINTOU SIEGE</core:Name1>
                        <!--EXT-FR-FE-15-->
                        <core:Street>8 RUE DE LA CONCORDE</core:Street>
                        <!--EXT-FR-FE-16-->
                        <core:StreetSupplement1>9 RUE DE LA CONCORDE</core:StreetSupplement1>
                        <!--EXT-FR-FE-17-->
                        <core:StreetSupplement2>10 RUE DE LA CONCORDE</core:StreetSupplement2>
                        <!--EXT-FR-FE-19-->
                        <core:PostalCode>37000</core:PostalCode>
                        <!--EXT-FR-FE-18-->
                        <core:City>TOURS</core:City>
                        <!--EXT-FR-FE-21-->
                        <core:Country>
                            <core:CountryCoded>FR</core:CountryCoded>
                        </core:Country>
                        <!--EXT-FR-FE-20-->
                        <core:Region>
                            <core:RegionCoded>Other</core:RegionCoded>
                            <core:RegionCodedOther>Corsica</core:RegionCodedOther>
                        </core:Region>
                    </core:NameAddress>
                    <!--EXT-FR-FE-BG-01-->
                    <core:PartyRoleCoded>AgentOrRepresentative</core:PartyRoleCoded>
                    <core:PartyTaxInformation>
                        <!--EXT-FR-FE-10-->
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>CEC</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                            </core:Agency>
                            <core:Ident>FR</core:Ident>
                        </core:TaxIdentifier>
                        <!--EXT-FR-FE-05-->
                        <core:RegisteredName>CEREAL DEC</core:RegisteredName>
                    </core:PartyTaxInformation>
                    <core:PrimaryContact>
                        <core:ListOfContactNumber>
                            <!--EXT-FR-FE-12-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:PrimaryContact>
                    <!--EXT-FR-FE-22-->
                    <core:OtherContacts>
                        <core:Contact>
                            <!--EXT-FR-FE-23-->
                            <core:ContactName>Joanne Firb</core:ContactName>
                            <core:ListOfContactNumber>
                                <!--EXT-FR-FE-24-->
                                <core:ContactNumber>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberValue>0789654347</core:ContactNumberValue>
                                </core:ContactNumber>
                                <!--EXT-FR-FE-25-->
                                <core:ContactNumber>
                                    <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                    <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                        </core:Contact>
                    </core:OtherContacts>
                </core:PartyCoded>
                <core:PartyCoded>
                    <!--EXT-FR-FE-BG-02-->
                    <core:PartyRoleCoded>Payer</core:PartyRoleCoded>
                    <!--EXT-FR-FE-54-->
                    <core:NameAddress>
                        <!--EXT-FR-FE-43-->
                        <core:Name1>NameAddr1</core:Name1>
                        <!--EXT-FR-FE-55-->
                        <core:Street>88 RUE DE LA CONCORDE</core:Street>
                        <!--EXT-FR-FE-56-->
                        <core:StreetSupplement1>89 RUE DE LA CONCORDE</core:StreetSupplement1>
                        <!--EXT-FR-FE-57-->
                        <core:StreetSupplement2>90 RUE DE LA CONCORDE</core:StreetSupplement2>
                        <!--EXT-FR-FE-59-->
                        <core:PostalCode>17000</core:PostalCode>
                        <!--EXT-FR-FE-58-->
                        <core:City>TOURST</core:City>
                        <!--EXT-FR-FE-61-->
                        <core:Country>
                            <core:CountryCoded>FR</core:CountryCoded>
                        </core:Country>
                        <!--EXT-FR-FE-60-->
                        <core:Region>
                            <core:RegionCoded>Other</core:RegionCoded>
                            <core:RegionCodedOther>Corsica</core:RegionCodedOther>
                        </core:Region>
                    </core:NameAddress>
                    <core:PartyTaxInformation>
                        <!--EXT-FR-FE-45-->
                        <core:RegisteredName>PEINTOU2</core:RegisteredName>
                        <!--EXT-FR-FE-50-->
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>CEC</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                            </core:Agency>
                            <core:Ident>FR</core:Ident>
                        </core:TaxIdentifier>
                    </core:PartyTaxInformation>
                    <core:PartyID>
                        <!--EXT-FR-FE-46-->
                        <core:Ident>7890</core:Ident>
                        <!--EXT-FR-FE-47-->
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                        </core:Agency>
                    </core:PartyID>
                    <core:ListOfIdentifier>
                        <!--EXT-FR-FE-48-->
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>43978990360</core:Ident>
                        </core:Identifier>
                    </core:ListOfIdentifier>
                    <core:PrimaryContact>
                        <core:ListOfContactNumber>
                            <!--EXT-FR-FE-52-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:PrimaryContact>
                    <!--EXT-FR-FE-62-->
                    <core:OtherContacts>
                        <core:Contact>
                            <!--EXT-FR-FE-63-->
                            <core:ContactName>Jan Jonson</core:ContactName>
                            <core:ListOfContactNumber>
                                <!--EXT-FR-FE-64-->
                                <core:ContactNumber>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberValue>0762377678</core:ContactNumberValue>
                                </core:ContactNumber>
                                <!--EXT-FR-FE-65-->
                                <core:ContactNumber>
                                    <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                    <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                        </core:Contact>
                    </core:OtherContacts>
                </core:PartyCoded>
                <core:PartyCoded>
                    <!--EXT-FR-FE-BG-03-->
                    <core:PartyRoleCoded>SellersAgentOrRepresentative</core:PartyRoleCoded>
                    <!--EXT-FR-FE-77-->
                    <core:NameAddress>
                        <!--EXT-FR-FE-66-->
                        <core:Name1>NameAddr2</core:Name1>
                        <!--EXT-FR-FE-78-->
                        <core:Street>88 RUE DE LA CONCORDE</core:Street>
                        <!--EXT-FR-FE-79-->
                        <core:StreetSupplement1>89 RUE DE LA CONCORDE</core:StreetSupplement1>
                        <!--EXT-FR-FE-80-->
                        <core:StreetSupplement2>90 RUE DE LA CONCORDE</core:StreetSupplement2>
                        <!--EXT-FR-FE-82-->
                        <core:PostalCode>17000</core:PostalCode>
                        <!--EXT-FR-FE-81-->
                        <core:City>TOURST</core:City>
                        <!--EXT-FR-FE-84-->
                        <core:Country>
                            <core:CountryCoded>FR</core:CountryCoded>
                        </core:Country>
                        <!--EXT-FR-FE-83-->
                        <core:Region>
                            <core:RegionCoded>Other</core:RegionCoded>
                            <core:RegionCodedOther>Corsica</core:RegionCodedOther>
                        </core:Region>
                    </core:NameAddress>
                    <core:PartyTaxInformation>
                        <!--EXT-FR-FE-68-->
                        <core:RegisteredName>PEINTOUT</core:RegisteredName>
                        <!--EXT-FR-FE-73-->
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>CEC</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                            </core:Agency>
                            <core:Ident>FR</core:Ident>
                        </core:TaxIdentifier>
                    </core:PartyTaxInformation>
                    <core:PartyID>
                        <!--EXT-FR-FE-69-->
                        <core:Ident>7890</core:Ident>
                        <!--EXT-FR-FE-47-->
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                        </core:Agency>
                    </core:PartyID>
                    <core:ListOfIdentifier>
                        <!--EXT-FR-FE-71-->
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>43978990360</core:Ident>
                        </core:Identifier>
                    </core:ListOfIdentifier>
                    <core:PrimaryContact>
                        <core:ListOfContactNumber>
                            <!--EXT-FR-FE-75-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:PrimaryContact>
                    <!--EXT-FR-FE-85-->
                    <core:OtherContacts>
                        <core:Contact>
                            <!--EXT-FR-FE-86-->
                            <core:ContactName>Jan Jonson</core:ContactName>
                            <core:ListOfContactNumber>
                                <!--EXT-FR-FE-87-->
                                <core:ContactNumber>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberValue>0762377678</core:ContactNumberValue>
                                </core:ContactNumber>
                                <!--EXT-FR-FE-88-->
                                <core:ContactNumber>
                                    <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                    <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                        </core:Contact>
                    </core:OtherContacts>
                </core:PartyCoded>
                <core:PartyCoded>
                    <!--EXT-FR-FE-BG-05-->
                    <core:PartyRoleCoded>Factor</core:PartyRoleCoded>
                    <!--EXT-FR-FE-123-->
                    <core:NameAddress>
                        <!--EXT-FR-FE-112-->
                        <core:Name1>NameAddrParty4</core:Name1>
                        <!--EXT-FR-FE-124-->
                        <core:Street>88 RUE DE LA CONCORDE</core:Street>
                        <!--EXT-FR-FE-125-->
                        <core:StreetSupplement1>89 RUE DE LA CONCORDE</core:StreetSupplement1>
                        <!--EXT-FR-FE-126-->
                        <core:StreetSupplement2>90 RUE DE LA CONCORDE</core:StreetSupplement2>
                        <!--EXT-FR-FE-128-->
                        <core:PostalCode>17000</core:PostalCode>
                        <!--EXT-FR-FE-127-->
                        <core:City>TOURST</core:City>
                        <!--EXT-FR-FE-130-->
                        <core:Country>
                            <core:CountryCoded>FR</core:CountryCoded>
                        </core:Country>
                        <!--EXT-FR-FE-129-->
                        <core:Region>
                            <core:RegionCoded>Other</core:RegionCoded>
                            <core:RegionCodedOther>Corsica2</core:RegionCodedOther>
                        </core:Region>
                    </core:NameAddress>
                    <core:PartyTaxInformation>
                        <!--EXT-FR-FE-114-->
                        <core:RegisteredName>PEINTOUT</core:RegisteredName>
                        <!--EXT-FR-FE-119-->
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>CEC</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                            </core:Agency>
                            <core:Ident>FR</core:Ident>
                        </core:TaxIdentifier>
                    </core:PartyTaxInformation>
                    <core:PartyID>
                        <!--EXT-FR-FE-115-->
                        <core:Ident>789110</core:Ident>
                        <!--EXT-FR-FE-116-->
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                        </core:Agency>
                    </core:PartyID>
                    <core:ListOfIdentifier>
                        <!--EXT-FR-FE-117-->
                        <core:Identifier>
                            <core:Agency>
                                <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                                <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                            </core:Agency>
                            <core:Ident>43988860</core:Ident>
                        </core:Identifier>
                    </core:ListOfIdentifier>
                    <core:PrimaryContact>
                        <core:ListOfContactNumber>
                            <!--EXT-FR-FE-121-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:PrimaryContact>
                    <!--EXT-FR-FE-131-->
                    <core:OtherContacts>
                        <core:Contact>
                            <!--EXT-FR-FE-132-->
                            <core:ContactName>Jan Jan</core:ContactName>
                            <core:ListOfContactNumber>
                                <!--EXT-FR-FE-133-->
                                <core:ContactNumber>
                                    <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                    <core:ContactNumberValue>0232377678</core:ContactNumberValue>
                                </core:ContactNumber>
                                <!--EXT-FR-FE-134-->
                                <core:ContactNumber>
                                    <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                    <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                                </core:ContactNumber>
                            </core:ListOfContactNumber>
                        </core:Contact>
                    </core:OtherContacts>
                </core:PartyCoded>
                <core:PartyCoded>
                    <!--BG-11-->
                    <core:PartyRoleCoded>DeclarantsAgentOrRepresentative</core:PartyRoleCoded>
                    <!--BG-12-->
                    <core:NameAddress>
                        <!--BT-62-->
                        <core:Name1>NameAddrParty4</core:Name1>
                        <!--BT-64-->
                        <core:Street>8 RUE DE LA CONCORDE TS</core:Street>
                        <!--BT-65-->
                        <core:StreetSupplement1>89 RUE DE LA CONCORDE TS</core:StreetSupplement1>
                        <!--BT-164-->
                        <core:StreetSupplement2>90 RUE DE LA CONCORDE TS</core:StreetSupplement2>
                        <!--BT-67-->
                        <core:PostalCode>55000</core:PostalCode>
                        <!--BT-66-->
                        <core:City>COLMAR</core:City>
                        <!--BT-69-->
                        <core:Country>
                            <core:CountryCoded>FR</core:CountryCoded>
                        </core:Country>
                        <!--BT-68-->
                        <core:Region>
                            <core:RegionCoded>Other</core:RegionCoded>
                            <core:RegionCodedOther>Corsica4</core:RegionCodedOther>
                        </core:Region>
                    </core:NameAddress>
                    <core:PartyTaxInformation>
                        <!--BT-63-->
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>CEC</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                            </core:Agency>
                            <core:Ident>FR</core:Ident>
                        </core:TaxIdentifier>
                    </core:PartyTaxInformation>
                </core:PartyCoded>
            </ListOfPartyCoded>
            <!--BG-10-->
            <RemitToParty>
                <!--EXT-FR-FE-31-->
                <core:NameAddress>
                    <!--BT-59-->
                    <core:Name1>DISTRIBUTOUT</core:Name1>
                    <!--EXT-FR-FE-32-->
                    <core:Street>7 RUE CONRAD</core:Street>
                    <!--EXT-FR-FE-33-->
                    <core:StreetSupplement1>8 RUE CONRAD</core:StreetSupplement1>
                    <!--EXT-FR-FE-34-->
                    <core:StreetSupplement2>9 RUE CONRAD</core:StreetSupplement2>
                    <!--EXT-FR-FE-35-->
                    <core:City>Lyon</core:City>
                    <!--EXT-FR-FE-36-->
                    <core:PostalCode>840603</core:PostalCode>
                    <!--EXT-FR-FE-37-->
                    <core:Region>
                        <core:RegionCoded>Other</core:RegionCoded>
                        <core:RegionCodedOther>Region 2</core:RegionCodedOther>
                    </core:Region>
                    <!--EXT-FR-FE-38-->
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyID>
                    <!--BT-60-->
                    <core:Ident>6789</core:Ident>
                    <!--BT-60-1-->
                    <core:Agency>
                        <core:AgencyCoded>Other</core:AgencyCoded>
                        <core:AgencyCodedOther>0009</core:AgencyCodedOther>
                    </core:Agency>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <!--BT-61-->
                        <core:Ident>7890</core:Ident>
                        <!--BT-61-1-->
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                        </core:Agency>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <!--EXT-FR-FE-27-->
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR36461312816</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
                <core:PrimaryContact>
                    <core:ListOfContactNumber>
                        <!--EXT-FR-FE-29-->
                        <core:ContactNumber>
                            <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                            <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        </core:ContactNumber>
                    </core:ListOfContactNumber>
                </core:PrimaryContact>
                <!--EXT-FR-FE-39-->
                <core:OtherContacts>
                    <core:Contact>
                        <!--EXT-FR-FE-40-->
                        <core:ContactName>Anne Moost</core:ContactName>
                        <core:ListOfContactNumber>
                            <!--EXT-FR-FE-41-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue>0986783454</core:ContactNumberValue>
                            </core:ContactNumber>
                            <!--EXT-FR-FE-42-->
                            <core:ContactNumber>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:Contact>
                </core:OtherContacts>
            </RemitToParty>
        </InvoiceParty>
        <InvoicePaymentInstructions>
            <core:PaymentTerms>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>LatePayment</core:PaymentTermCoded>
                    <core:PaymentTermDescription>INDEMNITE FORFAITAIRE POUR FRAIS DE RECOUVREMENT DE 40 EUROS</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Discount</core:PaymentTermCoded>
                    <core:PaymentTermDescription>1</core:PaymentTermDescription>
                    <core:DiscountInformation>
                        <core:DiscountDueDate>2020-02-15T00:00:00</core:DiscountDueDate>
                    </core:DiscountInformation>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>PenaltyTerms</core:PaymentTermCoded>
                    <core:PaymentTermDescription>1.5</core:PaymentTermDescription>
                </core:PaymentTerm>
                <!--BT-20-->
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Other</core:PaymentTermCoded>
                    <core:PaymentTermCodedOther>GeneralTerms</core:PaymentTermCodedOther>
                    <core:PaymentTermDescription>DESC</core:PaymentTermDescription>
                </core:PaymentTerm>
            </core:PaymentTerms>
            <!--BG-16-->
            <core:PaymentMethod>
                <!--BT-81-->
                <core:PaymentMeanCoded>Other</core:PaymentMeanCoded>
                <!--BT-82-->
                <core:PaymentMeanCodedOther>4</core:PaymentMeanCodedOther>
                <!--BT-83-->
                <core:PaymentMeanReference>
                    <core:RefNum>789</core:RefNum>
                </core:PaymentMeanReference>
                <!--BG-17-->
                <core:FITransfer>
                    <core:ToFITransfer>
                        <core:AccountDetail>
                            <!--BT-86-->
                            <core:AccountID>Other</core:AccountID>
                            <!--BT-84-->
                            <core:IBAN>***************************</core:IBAN>
                            <!--BT-85-->
                            <core:AccountName1>PEINTOU SIEGE</core:AccountName1>
                        </core:AccountDetail>
                    </core:ToFITransfer>
                    <core:FromFITransfer>
                        <!--BG-19-->
                        <core:AccountDetail>
                            <!--BT-89-->
                            <core:AccountID>ANVC</core:AccountID>
                            <!--BT-90-->
                            <core:AccountName1>PEINTOU SIEGE 2</core:AccountName1>
                            <!--BT-91-->
                            <core:IBAN>***************************</core:IBAN>
                        </core:AccountDetail>
                    </core:FromFITransfer>
                </core:FITransfer>
                <!--BG-18-->
                <core:CardInfo>
                    <!--BT-87-->
                    <core:CardRefNum>************</core:CardRefNum>
                    <!--BT-88-->
                    <core:CardHolderName>Raul Pop</core:CardHolderName>
                </core:CardInfo>
            </core:PaymentMethod>
        </InvoicePaymentInstructions>
        <!--BG-1-->
        <ListOfStructuredNote>
            <core:StructuredNote>
                <!--BT-22-->
                <core:GeneralNote>APPLICATION DU TARIF K</core:GeneralNote>
                <!--BT-21-->
                <core:TextTypeCoded>Other</core:TextTypeCoded>
                <core:TextTypeCodedOther>AAI</core:TextTypeCodedOther>
            </core:StructuredNote>
        </ListOfStructuredNote>
        <CountrySpecificRequirements>
            <OtherCountryRequirements>
                <CountryRequirement>
                    <Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </Country>
                    <ListOfSpecificRequirement>
                        <SpecificRequirement>
                            <RequirementTypeCoded>BillingType</RequirementTypeCoded>
                            <RequirementReference>
                                <!--BT-23-->
                                <core:RefNum>B1</core:RefNum>
                            </RequirementReference>
                            <!--BT-24-->
                            <RequirementDetails>urn:cen.eu:en16931:2017</RequirementDetails>
                        </SpecificRequirement>
                    </ListOfSpecificRequirement>
                </CountryRequirement>
            </OtherCountryRequirements>
        </CountrySpecificRequirements>
        <InvoiceAllowancesOrCharges>
            <core:AllowOrCharge>
                <!--BG-20-->
                <core:IndicatorCoded>Allowance</core:IndicatorCoded>
                <core:TypeOfAllowanceOrCharge>
                    <core:MonetaryValue>
                        <!--BT-92-->
                        <core:MonetaryAmount>567.2</core:MonetaryAmount>
                    </core:MonetaryValue>
                    <!--BT-94-->
                    <core:PercentageAllowanceOrCharge>
                        <core:PercentQualifier>
                            <core:PercentQualifierCoded>Other</core:PercentQualifierCoded>
                            <core:PercentQualifierCodedOther>HeaderAllowanceorCharge</core:PercentQualifierCodedOther>
                        </core:PercentQualifier>
                        <core:Percent>12</core:Percent>
                    </core:PercentageAllowanceOrCharge>
                </core:TypeOfAllowanceOrCharge>
                <core:BasisMonetaryRange>
                    <core:MonetaryLimit>
                        <!--BT-93-->
                        <core:MonetaryLimitValue>567.2</core:MonetaryLimitValue>
                    </core:MonetaryLimit>
                </core:BasisMonetaryRange>
                <!--BT-95 && BT-96-->
                <core:Tax>
                    <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                    <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                    <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                    <core:TaxCategoryCodedOther>E</core:TaxCategoryCodedOther>
                    <core:TaxPercent>5.5</core:TaxPercent>
                </core:Tax>
                <!--BT-97-->
                <core:MethodOfHandlingCoded>AllowanceToBeIssuedByVendor</core:MethodOfHandlingCoded>
                <core:AllowanceOrChargeDescription>
                    <core:ListOfDescription>DESC</core:ListOfDescription>
                    <!--BT-98-->
                    <core:ServiceCoded>Other</core:ServiceCoded>
                    <core:ServiceCodedOther>60</core:ServiceCodedOther>
                </core:AllowanceOrChargeDescription>
            </core:AllowOrCharge>
            <core:AllowOrCharge>
                <!--BG-21-->
                <core:IndicatorCoded>Charge</core:IndicatorCoded>
                <!--BT-99-->
                <core:TypeOfAllowanceOrCharge>
                    <core:MonetaryValue>
                        <core:MonetaryAmount>22.2</core:MonetaryAmount>
                    </core:MonetaryValue>
                    <!--BT-101-->
                    <core:PercentageAllowanceOrCharge>
                        <core:PercentQualifier>
                            <core:PercentQualifierCoded>Other</core:PercentQualifierCoded>
                            <core:PercentQualifierCodedOther>HeaderAllowanceorCharge</core:PercentQualifierCodedOther>
                        </core:PercentQualifier>
                        <core:Percent>12</core:Percent>
                    </core:PercentageAllowanceOrCharge>
                </core:TypeOfAllowanceOrCharge>
                <!--BT-100-->
                <core:BasisMonetaryRange>
                        <core:MonetaryLimit>
                            <core:MonetaryLimitValue>234.4</core:MonetaryLimitValue>
                        </core:MonetaryLimit>
                </core:BasisMonetaryRange>
                <!--BT-102 && BT-103-->
                <core:Tax>
                    <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                    <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                    <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                    <core:TaxCategoryCodedOther>E</core:TaxCategoryCodedOther>
                    <core:TaxPercent>5.5</core:TaxPercent>
                </core:Tax>
                <!--BT-104-->
                <core:MethodOfHandlingCoded>ChargeToBePaidByCustomer</core:MethodOfHandlingCoded>
                <core:AllowanceOrChargeDescription>
                    <core:ListOfDescription>DESC</core:ListOfDescription>
                    <!--BT-105-->
                    <core:ServiceCoded>Other</core:ServiceCoded>
                    <core:ServiceCodedOther>AAD</core:ServiceCodedOther>
                </core:AllowanceOrChargeDescription>
            </core:AllowOrCharge>
        </InvoiceAllowancesOrCharges>
        <ListOfNameValueSet>
            <!--BT-114-->
            <core:NameValueSet>
                <SetName>invoiceSummaryAmount</SetName>
                <NameValuePair>
                    <Name>PayableRoundingAmount</Name>
                    <Datatype>MonetaryValueType</Datatype>
                    <Value>345</Value>
                </NameValuePair>
            </core:NameValueSet>
        </ListOfNameValueSet>
        <!--BG-24-->
        <ListOfAttachment>
            <core:Attachment>
                <!--BT-122-->
                <AttachmentPurpose>Transport</AttachmentPurpose>
                <!--BT-123-->
                <AttachmentDescription>Transport description</AttachmentDescription>
                <!--BT-124-->
                <AttachmentLocation>Europe</AttachmentLocation>
                <!--BT-125-->
                <AttachmentTitle>Car Transport</AttachmentTitle>
                <!--BT-125-1-->
                <MIMEType>CSV</MIMEType>
                <!--BT-125-2-->
                <FileName>abc.ZIP</FileName>
            </core:Attachment>
        </ListOfAttachment>
    </InvoiceHeader>
    <!--BG-25-->
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <!--BT-126-->
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Item</core:LineItemTypeCoded>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:StandardPartNumber>
                                <!--BT-157-1-->
                                <core:ProductIdentifierQualifierCoded>Other</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifierQualifierCodedOther>0060</core:ProductIdentifierQualifierCodedOther>
                                <!--BT-157-->
                                <core:ProductIdentifier>3083630026319</core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:OtherItemIdentifiers>
                                <!--BT-154-->
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierCoded>End-ItemDescription</core:ProductIdentifierCoded>
                                    <core:ProductIdentifier>65783</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:OtherItemIdentifiers>
                            <!--BT-155-->
                            <core:SellerPartNumber>
                                <core:PartID>LDL</core:PartID>
                            </core:SellerPartNumber>
                            <!--BT-156-->
                            <core:BuyerPartNumber>
                                <core:PartID>DAN</core:PartID>
                            </core:BuyerPartNumber>
                        </core:PartNumbers>
                        <!--BT-153-->
                        <core:ItemDescription>BOITE DE PETITS POIS 1 KG</core:ItemDescription>
                        <core:Category>
                            <!--BT-158-->
                            <core:CategoryID>453</core:CategoryID>
                            <core:StandardCategoryID>
                                <!--BT-158-1-->
                                <core:StandardCategoryType>AD</core:StandardCategoryType>
                                <!--BT-158-2-->
                                <core:ClassificationID>BCD</core:ClassificationID>
                            </core:StandardCategoryID>
                        </core:Category>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <!--BT-129-->
                        <core:QuantityValue>2050</core:QuantityValue>
                        <!--BT-130-->
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>KGM</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                    <LineItemReferences>
                        <OtherInvoiceReferences>
                            <core:ReferenceCoded>
                                <core:PrimaryReference>
                                    <!--BT-128-->
                                    <core:RefNum>ref1234</core:RefNum>
                                </core:PrimaryReference>
                                <!--BT-128-1-->
                                <core:ReferenceTypeCoded>Other</core:ReferenceTypeCoded>
                                <core:ReferenceTypeCodedOther>AAB</core:ReferenceTypeCodedOther>
                            </core:ReferenceCoded>
                            <!--EXT-FR-FE-135-->
                            <core:ReferenceCoded>
                                <core:ReferenceTypeCoded>CustomerReferenceNumber</core:ReferenceTypeCoded>
                                <core:PrimaryReference>
                                    <core:RefNum>ref2234</core:RefNum>
                                </core:PrimaryReference>
                            </core:ReferenceCoded>
                            <!--EXT-FR-FE-BG-08-->
                            <core:ReferenceCoded>
                                <core:ReferenceTypeCoded>Reception</core:ReferenceTypeCoded>
                                <core:PrimaryReference>
                                    <!--EXT-FR-FE-142 && EXT-FR-FE-143-->
                                    <core:RefNum>ref3234</core:RefNum>
                                </core:PrimaryReference>
                            </core:ReferenceCoded>
                        </OtherInvoiceReferences>
                        <!--BT-132-->
                        <PurchaseOrderReference>
                            <core:BuyerOrderNumber>4502</core:BuyerOrderNumber>
                            <!--EXT-FR-FE-144-->
                            <core:SellerOrderNumber>2805</core:SellerOrderNumber>
                            <!--EXT-FR-FE-145-->
                            <core:PurchaseOrderLineItemNumber>300</core:PurchaseOrderLineItemNumber>
                        </PurchaseOrderReference>
                        <!--BT-133-->
                        <CostAllocation>
                            <CostAllocationNumber>
                                <core:RefNum>456</core:RefNum>
                            </CostAllocationNumber>
                        </CostAllocation>
                        <!--EXT-FR-FE-BG-06-->
                        <ListOfRelatedInvoiceRef>
                            <RelatedInvoiceRef>
                                <InvoiceNumber>
                                    <!--EXT-FR-FE-136-->
                                    <core:RefNum>789</core:RefNum>
                                    <!--EXT-FR-FE-138-->
                                    <core:RefDate>2021-01-01T00:00:07</core:RefDate>
                                </InvoiceNumber>
                                <!--EXT-FR-FE-137-->
                                <RelatedInvoiceType>
                                    <InvoiceTypeCoded></InvoiceTypeCoded>
                                    <InvoiceTypeCoded></InvoiceTypeCoded>
                                </RelatedInvoiceType>
                                <!--EXT-FR-FE-139-->
                                <InvoiceLineItemNumber>678</InvoiceLineItemNumber>
                            </RelatedInvoiceRef>
                        </ListOfRelatedInvoiceRef>
                        <!--EXT-FR-FE-140 && EXT-FR-FE-141-->
                        <ASNNumber>
                            <core:RefNum>555</core:RefNum>
                        </ASNNumber>
                        <DeliveryNoteNumber>
                            <!--EXT-FR-FE-158-0 && EXT-FR-FE-158-->
                            <core:RefDate>2021-01-01T00:00:07</core:RefDate>
                        </DeliveryNoteNumber>
                    </LineItemReferences>
                    <!--BT-159-->
                    <CountryOfOrigin>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </CountryOfOrigin>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <!--BG-29-->
                    <core:ListOfPrice>
                        <core:Price>
                            <!--BT-146-->
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <!--BT-146-->
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.54</core:UnitPriceValue>
                            </core:UnitPrice>
                            <core:PriceBasisQuantity>
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KGM</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:PriceBasisQuantity>
                        </core:Price>
                        <core:Price>
                            <!--BT-147-->
                            <core:PricingType>
                                <core:PriceTypeCoded>DiscountAmountAllowed</core:PriceTypeCoded>
                            </core:PricingType>
                            <!--BT-147-->
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.54</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <!--BT-148-->
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <!--BT-148-->
                            <core:UnitPrice>
                                <core:UnitPriceValue>3.09</core:UnitPriceValue>
                            </core:UnitPrice>
                            <core:PriceBasisQuantity>
                                <!--BT-149-->
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <!--BT-150-->
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>KGM</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:PriceBasisQuantity>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <!--BT-152-->
                        <core:TaxPercent>5.5</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>
                    <core:ItemAllowancesOrCharges>
                        <core:AllowOrCharge>
                            <!--BG-27-->
                            <core:IndicatorCoded>LineItemAllowance</core:IndicatorCoded>
                            <core:BasisCoded>MonetaryAmount</core:BasisCoded>
                            <core:MethodOfHandlingCoded>AllowanceToBeIssuedByVendor</core:MethodOfHandlingCoded>
                            <core:AllowOrChargeTreatment>
                                <core:AllowOrChargeTreatmentCoded>Other</core:AllowOrChargeTreatmentCoded>
                                <core:AllowOrChargeTreatmentCodedOther>FirstStepOfCalculation</core:AllowOrChargeTreatmentCodedOther>
                            </core:AllowOrChargeTreatment>
                            <core:AllowanceOrChargeDescription>
                                <!--BT-139-->
                                <core:ListOfDescription>Manufacturer's consumer discount</core:ListOfDescription>
                                <!--BT-140-->
                                <core:ServiceCoded>Other</core:ServiceCoded>
                                <core:ServiceCodedOther>60</core:ServiceCodedOther>
                            </core:AllowanceOrChargeDescription>
                            <core:BasisMonetaryRange>
                                <core:MonetaryLimit>
                                    <!--BT-137-->
                                    <core:MonetaryLimitLineValue>1.5</core:MonetaryLimitLineValue>
                                </core:MonetaryLimit>
                            </core:BasisMonetaryRange>
                            <core:TypeOfAllowanceOrCharge>
                                <!--BT-138-->
                                <core:PercentageAllowanceOrCharge>
                                    <core:PercentQualifier>
                                        <core:PercentQualifierCoded>Other</core:PercentQualifierCoded>
                                        <core:PercentQualifierCodedOther>HeaderAllowanceorCharge</core:PercentQualifierCodedOther>
                                    </core:PercentQualifier>
                                    <core:Percent>2</core:Percent>
                                </core:PercentageAllowanceOrCharge>
                                <core:MonetaryValue>
                                    <!--BT-136-->
                                    <core:MonetaryLineAmount>456.9</core:MonetaryLineAmount>
                                </core:MonetaryValue>
                            </core:TypeOfAllowanceOrCharge>
                        </core:AllowOrCharge>
                        <core:AllowOrCharge>
                            <!--BG-28-->
                            <core:IndicatorCoded>LineItemCharge</core:IndicatorCoded>
                            <core:MethodOfHandlingCoded>ChargeToBePaidByCustomer</core:MethodOfHandlingCoded>
                            <core:AllowOrChargeTreatment>
                                <core:AllowOrChargeTreatmentCoded>Other</core:AllowOrChargeTreatmentCoded>
                                <core:AllowOrChargeTreatmentCodedOther>FirstStepOfCalculation</core:AllowOrChargeTreatmentCodedOther>
                            </core:AllowOrChargeTreatment>
                            <core:AllowanceOrChargeDescription>
                                <!--BT-144-->
                                <core:ListOfDescription>Technical modification</core:ListOfDescription>
                                <!--BT-145-->
                                <core:ServiceCoded>Other</core:ServiceCoded>
                                <core:ServiceCodedOther>AAC</core:ServiceCodedOther>
                            </core:AllowanceOrChargeDescription>
                            <core:TypeOfAllowanceOrCharge>
                                <core:MonetaryValue>
                                    <!--BT-141-->
                                    <core:MonetaryLineAmount>0.03</core:MonetaryLineAmount>
                                </core:MonetaryValue>
                                <!--BT-141-->
                                <core:PercentageAllowanceOrCharge>
                                    <core:PercentQualifier>
                                        <core:PercentQualifierCoded>BasePriceAmount</core:PercentQualifierCoded>
                                    </core:PercentQualifier>
                                    <core:PercentageMonetaryValue>
                                        <core:MonetaryLineAmount>784.09</core:MonetaryLineAmount>
                                    </core:PercentageMonetaryValue>
                                </core:PercentageAllowanceOrCharge>
                                <!--BT-143-->
                                <core:PercentageAllowanceOrCharge>
                                    <core:PercentQualifier>
                                        <core:PercentQualifierCoded>Other</core:PercentQualifierCoded>
                                        <core:PercentQualifierCodedOther>ItemAllowanceorCharge</core:PercentQualifierCodedOther>
                                    </core:PercentQualifier>
                                    <core:Percent>2</core:Percent>
                                </core:PercentageAllowanceOrCharge>
                            </core:TypeOfAllowanceOrCharge>
                            <!--BT-142-->
                            <core:BasisMonetaryRange>
                                <core:MonetaryLimit>
                                    <core:MonetaryLimitLineValue>789.2</core:MonetaryLimitLineValue>
                                </core:MonetaryLimit>
                            </core:BasisMonetaryRange>
                        </core:AllowOrCharge>
                    </core:ItemAllowancesOrCharges>
                    <!--BT-131-->
                    <core:LineItemTotal>
                        <core:MonetaryAmount>3171.43</core:MonetaryAmount>
                    </core:LineItemTotal>
                    <core:PricingDetailType>
                        <!--BT-151-->
                        <core:Tax>
                            <core:TaxTypeCodedOther>taxes</core:TaxTypeCodedOther>
                        </core:Tax>
                    </core:PricingDetailType>
                </InvoicePricingDetail>
                <ListOfStructuredNote>
                    <!--BT-127-00-->
                    <core:StructuredNote>
                        <!--EXT-FR-FE-183-->
                        <core:TextTypeCoded>TXD</core:TextTypeCoded>
                        <!--BT-127-->
                        <core:GeneralNote>I</core:GeneralNote>
                    </core:StructuredNote>
                </ListOfStructuredNote>
                <DeliveryDetail>
                    <!--EXT-FR-FE-BG-10-->
                    <core:ShipToLocation>
                        <!--EXT-FR-FE-146-->
                        <core:LocationIdentifier>
                            <core:LocID>
                                <core:Ident>RO</core:Ident>
                                <!--EXT-FR-FE-148-->
                                <core:Agency>
                                    <core:AgencyCoded>Other</core:AgencyCoded>
                                    <core:AgencyCodedOther>0226</core:AgencyCodedOther>
                                </core:Agency>
                            </core:LocID>
                        </core:LocationIdentifier>
                        <core:NameAddress>
                            <!--EXT-FR-FE-149-->
                            <core:Name1>Aga</core:Name1>
                            <!--EXT-FR-FE-151-->
                            <core:Street>2 RUE ALADIN</core:Street>
                            <!--EXT-FR-FE-152-->
                            <core:StreetSupplement1> 3 RUE SIR</core:StreetSupplement1>
                            <!--EXT-FR-FE-153-->
                            <core:StreetSupplement1> 4 RUE TRADE</core:StreetSupplement1>
                            <!--EXT-FR-FE-154-->
                            <core:City>ORADEA</core:City>
                            <!--EXT-FR-FE-155-->
                            <core:PostalCode>427162</core:PostalCode>
                            <!--EXT-FR-FE-156-->
                            <core:Region>
                                <core:RegionCoded>Other</core:RegionCoded>
                                <core:RegionCodedOther>ARD</core:RegionCodedOther>
                            </core:Region>
                            <!--EXT-FR-FE-157-->
                            <core:Country>RO</core:Country>
                        </core:NameAddress>
                    </core:ShipToLocation>
                </DeliveryDetail>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <core:StartDate>2021-01-01T00:00:07</core:StartDate>
                        <!--BT-135-->
                        <core:EndDate>2025-01-01T00:00:07</core:EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
                <!--BG-32-->
                <ListOfNameValueSet>
                    <core:NameValueSet>
                        <core:ListOfNameValuePair>
                            <core:NameValuePair>
                                <!--BT-160-->
                                <core:Name>ANN</core:Name>
                                <!--BT-161-->
                                <core:Value>ANNE</core:Value>
                            </core:NameValuePair>
                        </core:ListOfNameValuePair>
                    </core:NameValueSet>
                </ListOfNameValueSet>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <!--BG-22-->
        <InvoiceTotals>
            <!--BT-112-->
            <InvoiceTotal>
                <core:MonetaryAmount>7992.50</core:MonetaryAmount>
            </InvoiceTotal>
            <!--BT-106-->
            <InvoiceSubTotal>
                <core:MonetaryAmount>3171.43</core:MonetaryAmount>
            </InvoiceSubTotal>
            <!--BT-109-->
            <TaxableValue>
                <core:MonetaryAmount>6773.93</core:MonetaryAmount>
            </TaxableValue>
            <!--BT-115-->
            <TotalAmountPayable>
                <core:MonetaryAmount>6992.5</core:MonetaryAmount>
            </TotalAmountPayable>
            <!--BT-110-->
            <TotalTaxAmount>
                <core:MonetaryAmount>42.56</core:MonetaryAmount>
            </TotalTaxAmount>
            <!--BT-107-->
            <AllowanceTotal>
                <core:MonetaryAmount>567.2</core:MonetaryAmount>
            </AllowanceTotal>
            <!--BT-108-->
            <ChargeTotal>
                <core:MonetaryAmount>22.2</core:MonetaryAmount>
            </ChargeTotal>
            <!--BT-111-->
            <TaxableValueInTaxAccountingCurrency>
                <core:MonetaryAmount>245.23</core:MonetaryAmount>
            </TaxableValueInTaxAccountingCurrency>
            <!--BT-113-->
            <PrepaidAmount>
                <core:MonetaryAmount>567.12</core:MonetaryAmount>
            </PrepaidAmount>
        </InvoiceTotals>
        <!--BG-23-->
        <ListOfTaxSummary>
            <core:TaxSummary>
                <!--BT-118-->
                <core:TaxTypeCoded>Other</core:TaxTypeCoded>
                <!--BT-118-->
                <core:TaxTypeCodedOther>S</core:TaxTypeCodedOther>
                <!--BT-120-->
 				<core:TaxExemptReason></core:TaxExemptReason>
				<!--BT-121-->
				<core:TaxExemptCode></core:TaxExemptCode>
                <!--BT-119-->
                <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                <core:TaxCategoryCodedOther>5.5</core:TaxCategoryCodedOther>
                <!--BT-116-->
                <core:TaxableAmount>6773.93</core:TaxableAmount>
                <!--BT-117-->
                <core:TaxAmount>372.57</core:TaxAmount>
                <!--BT-117-->
                <core:TaxAmountInTaxAccountingCurrency>372.57</core:TaxAmountInTaxAccountingCurrency>
            </core:TaxSummary>
        </ListOfTaxSummary>
        <ListOfActualPayment>
            <core:ActualPayment>
                <core:PaymentAmount>
                    <core:InvoiceCurrencyAmt>
                        <core:MonetaryAmount>1000.23</core:MonetaryAmount>
                    </core:InvoiceCurrencyAmt>
                    <core:ForeignCurrencyPayment>
                        <core:MonetaryAmount>1000.23</core:MonetaryAmount>
                    </core:ForeignCurrencyPayment>
                </core:PaymentAmount>
                <core:PaymentDate>2020-01-03T00:00:00</core:PaymentDate>
                <core:PaymentMean>
                    <core:PaymentMeanCoded>Other</core:PaymentMeanCoded>
                    <core:PaymentCodedMeanOther>Unknow</core:PaymentCodedMeanOther>
                </core:PaymentMean>
                <core:OtherPaymentInfo>********</core:OtherPaymentInfo>
            </core:ActualPayment>
        </ListOfActualPayment>
    </InvoiceSummary>
</Invoice>
