<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.byzaneo.generix.tasks</groupId>
		<artifactId>generix-tasks</artifactId>
		<version>6.0.0-SNAPSHOT</version>
	</parent>
	<packaging>jar</packaging>
	<artifactId>generix-tasks-edocument</artifactId>
	<name>[GNX] Generix - Tasks - eDocument</name>
	<dependencies>
		<!-- ** GENERIX ** -->
		<dependency>
			<groupId>com.byzaneo.generix.services</groupId>
			<artifactId>generix-service-audit</artifactId>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.generix.services</groupId>
			<artifactId>generix-service-signature</artifactId>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.generix.services</groupId>
			<artifactId>generix-service-archiving</artifactId>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.generix.tasks</groupId>
			<artifactId>generix-tasks-doc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.generix.tasks</groupId>
			<artifactId>generix-tasks-commons</artifactId>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.generix.services</groupId>
			<artifactId>generix-service-edocument</artifactId>
		</dependency>
		<!-- ** OTHER ** -->
		<dependency>
			<groupId>uk.com.robust-it</groupId>
			<artifactId>cloning</artifactId>
		</dependency>
		<!-- ** TEST ** -->
		<dependency>
			<groupId>com.byzaneo.xtrade</groupId>
			<artifactId>byzaneo-xtrade-core</artifactId>
			<classifier>tests</classifier>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.xtrade.process</groupId>
			<artifactId>byzaneo-xtrade-process-ant</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.xtrade.process</groupId>
			<artifactId>byzaneo-xtrade-process-document</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.xtrade.process</groupId>
			<artifactId>byzaneo-xtrade-process-utils</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.generix.transform</groupId>
			<artifactId>generix-transform-translator</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.generix.transform</groupId>
			<artifactId>generix-transform-edi</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.assertj</groupId>
			<artifactId>assertj-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.xtrade</groupId>
			<artifactId>byzaneo-xtrade-core</artifactId>
			<type>test-jar</type>
			<scope>test</scope>
			<version>${byzaneo-version}</version>
		</dependency>
		<!-- lombok -->
  		<dependency>
			<groupId>org.projectlombok</groupId>
        	<artifactId>lombok</artifactId>
  		</dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
        </dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-junit-jupiter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-junit</artifactId>
		</dependency>
	</dependencies>
<!--  BUILD 	
<build>
	<plugins>
		<plugin>
			<groupId>org.jvnet.jaxb2.maven2</groupId>
			<artifactId>maven-jaxb22-plugin</artifactId>
			<version>0.8.3</version>
			<executions>
				<execution>
					<id>metadata</id>
					<goals>
						<goal>generate</goal>
					</goals>
					<configuration>
						<schemaDirectory>src/main/resources/schemas/archive/metadata</schemaDirectory>
						<generateDirectory>src/main/java</generateDirectory>
						<generatePackage>com.byzaneo.generix.edocument.task.archive.metadata</generatePackage>
						<strict>true</strict>
						<episode>false</episode>
					</configuration>
				</execution>
				<execution>
					<id>strategy</id>
					<goals>
						<goal>generate</goal>
					</goals>
					<configuration>
						<schemaDirectory>src/main/resources/schemas/archive/strategy</schemaDirectory>
						<generateDirectory>src/main/java</generateDirectory>
						<generatePackage>com.byzaneo.generix.edocument.task.archive.strategy</generatePackage>
						<strict>true</strict>
						<episode>false</episode>
					</configuration>
				</execution>
				<execution>
					<id>report</id>
					<goals>
						<goal>generate</goal>
					</goals>
					<configuration>
						<schemaDirectory>src/main/resources/schemas/archive/report</schemaDirectory>
						<generateDirectory>src/main/java</generateDirectory>
						<generatePackage>com.byzaneo.generix.edocument.task.archive.report</generatePackage>
						<bindingDirectory>src/main/resources/binding</bindingDirectory>
						<strict>true</strict>
						<episode>false</episode>
					</configuration>
				</execution>
			</executions>
		</plugin>
	</plugins>
</build> 
-->
</project>
