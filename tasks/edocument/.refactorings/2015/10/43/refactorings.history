<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename type &apos;com.byzaneo.generix.edocument.test.process.XCBLValidationTaskTest&apos; to &apos;XMLValidationTaskXcblTest&apos;&#x0D;&#x0A;- Original project: &apos;generix-tasks-edocument&apos;&#x0D;&#x0A;- Original element: &apos;com.byzaneo.generix.edocument.test.process.XCBLValidationTaskTest&apos;&#x0D;&#x0A;- Renamed element: &apos;com.byzaneo.generix.edocument.test.process.XMLValidationTaskXcblTest&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;XCBLValidationTaskTest&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/test\/java&lt;com.byzaneo.generix.edocument.test.process{XCBLValidationTaskTest.java[XCBLValidationTaskTest" matchStrategy="1" name="XMLValidationTaskXcblTest" qualified="false" references="true" similarDeclarations="false" stamp="1445422181136" textual="false" version="1.0"/>&#x0A;<refactoring comment="Rename type &apos;com.byzaneo.generix.edocument.test.process.XMLValidationTaskTest&apos; to &apos;XMLValidationTaskXmlTest&apos;&#x0D;&#x0A;- Original project: &apos;generix-tasks-edocument&apos;&#x0D;&#x0A;- Original element: &apos;com.byzaneo.generix.edocument.test.process.XMLValidationTaskTest&apos;&#x0D;&#x0A;- Renamed element: &apos;com.byzaneo.generix.edocument.test.process.XMLValidationTaskXmlTest&apos;&#x0D;&#x0A;- Update references to refactored element&#x0D;&#x0A;- Update textual occurrences in comments and strings" description="Rename type &apos;XMLValidationTaskTest&apos;" flags="589830" id="org.eclipse.jdt.ui.rename.type" input="/src\/test\/java&lt;com.byzaneo.generix.edocument.test.process{XMLValidationTaskTest.java[XMLValidationTaskTest" matchStrategy="1" name="XMLValidationTaskXmlTest" qualified="false" references="true" similarDeclarations="false" stamp="1445422199468" textual="false" version="1.0"/>
</session>