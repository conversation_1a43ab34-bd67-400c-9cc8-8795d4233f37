<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html" 
	xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
	xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task"
	xmlns:b="http://byzaneo.com/ui"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

	<!-- INTERFACE -->
	<cc:interface name="freeTextEdit">
		<cc:attribute name="value" required="true" />
		<cc:attribute name="type" required="true" />
		<cc:attribute name="mode" default="edit" />
	</cc:interface>

	<!-- IMPLEMENATION -->
	<cc:implementation>
		<p:outputPanel styleClass="form-group">
			<p:outputLabel styleClass="col-sm-2 control-label" indicateRequired="true" for="ftxTextEdt" value="#{labels.text}" />
			<p:outputPanel styleClass="col-sm-10" indicateRequired="true">
				<p:editor id="ftxTextEdt" 
						  widgetVar="wFtxTextEdt" 
						  required="true"
						  requiredMessage="#{labels.text} : #{comtsklbls.value_required}"
						  value="#{cc.attrs.value.text}"
				/>
				<b:labelSet for="ftxTextEdt" widgetVar="wFtxTextLst" locales="#{cc.attrs.locales}" defaultLocale="#{cc.attrs.defaultLocale}" localeSelected="#{cc.attrs.locale}" rendered="#{cc.attrs.multilingual}" selector="false"/>
			</p:outputPanel>
		</p:outputPanel>			
 	</cc:implementation>
</ui:component>
