package com.byzaneo.generix.commons.task.business;

import static com.byzaneo.commons.util.ExpressionHelper.getProperty;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.xtrade.process.DocumentPolicy.DEAD;
import static com.byzaneo.xtrade.process.Repository.CONTEXT;
import static com.byzaneo.xtrade.process.Repository.DATABASE;
import static com.byzaneo.xtrade.process.VariableHelper.addDeads;
import static com.byzaneo.xtrade.process.VariableHelper.applyDocumentPolicy;
import static com.byzaneo.xtrade.process.VariableHelper.getDocuments;
import static com.byzaneo.xtrade.process.VariableHelper.getEnum;
import static com.byzaneo.xtrade.process.VariableHelper.getList;
import static com.byzaneo.xtrade.process.VariableHelper.getLogLevel;
import static com.byzaneo.xtrade.process.VariableHelper.getString;
import static com.byzaneo.xtrade.process.VariableHelper.getNonPersistentPropertiesWithoutPrefix;
import static com.byzaneo.xtrade.ui.convert.ProcessExpressionConverter.CONVERTER_ID;
import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static java.text.MessageFormat.format;
import static java.util.Collections.emptyList;
import static java.util.Collections.singleton;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

import org.activiti.engine.delegate.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import com.byzaneo.commons.exception.ProcessInterruptedException;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.util.QueryHelper;
import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.process.*;
import com.byzaneo.xtrade.process.el.FixedValue;
import com.byzaneo.xtrade.process.task.*;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.google.common.collect.*;

/**
 * This task manages the policy for duplicate documents. Duplicates may be ignored, cancel and replace previous copy, ...
 * 
 * <AUTHOR>
 */
@Task(name = "DuplicateDocument2", library = "process/business/document", version = "1.0.0")
@TaskViewModel(labelFamily = "comtsklbls")
public class DuplicateDocument2Task extends AbstractTask implements CancelableTask {
  private static final Logger log = getLogger(DuplicateDocument2Task.class);

  private static final long serialVersionUID = -9127053855373480332L;

  public enum ErrorLevel {
    INFO,
    WARN,
    ERROR
  }

  /** BQL query to search for duplicate document */
  /** BQL query parameters list as property path in a {@link Document} (may use {@link Document#getIndexValue()} path to request index) */

  @TaskProperty(type = Query.class)
  private Expression contextQuery = new FixedValue("'type' = (0) AND 'from' = (1) AND 'to' = (2) AND 'number' = (3)");

  @TaskProperty()
  private Expression contextParameters = new FixedValue("[\"type\", \"from\", \"to\", \"number\"]");

  @TaskProperty(type = Query.class)
  private Expression mongoQuery;

  @TaskProperty()
  private Expression mongoParameters;

  @TaskProperty(type = Query.class)
  private Expression databaseQuery = new FixedValue("'type' = (0) AND 'from' = (1) AND 'to' = (2) AND 'number' = (3)");;

  @TaskProperty()
  private Expression databaseParameters = new FixedValue("[\"type\", \"from\", \"to\", \"number\"]");

  /** Policy to apply to the duplicate documents */
  @TaskProperty(required = true, type = DocumentPolicy.class, converter = CONVERTER_ID)
  private Expression policy = new FixedValue(DEAD);

  @TaskProperty(type = DocumentType.class, converter = CONVERTER_ID)
  private Expression typeToIgnore;

  @TaskProperty(type = ErrorLevel.class)
  private Expression errorLevel;

  private transient DocumentService documentService;

  public DuplicateDocument2Task() {
    documentService = getBean(DocumentService.class, DocumentService.SERVICE_NAME);
  }

  /* -- EXECUTION -- */
  /** @see org.activiti.engine.delegate.JavaDelegate#execute(org.activiti.engine.delegate.DelegateExecution) */
  @Override
  public void executeTask(DelegateExecution execution) throws Exception {
    String message = validFieldsQueryAndParameters();
    if (message != null) {
      log.error(message);
      return;
    }

    // documents to process
    final List<Document> documents = getDocuments(execution);
    if (documents.isEmpty()) {
      log.debug("Contextual document not found.");
      return;
    }
    log.info("Contextual document count to process: {}", documents.size());

    final DocumentPolicy epolicy = getEnum(execution, policy, DocumentPolicy.class, DEAD);
    final DocumentType type = getEnum(execution, typeToIgnore, DocumentType.class, null);
    // process contextual documents
    int policied = 0;
    List<Document> duplicatesRemoved = new ArrayList<>();
    documents.removeAll(singleton(null));
    for (Document document : documents) {
      List<Document> duplicates;
      if (Thread.currentThread()
          .isInterrupted()) {
        throw new ProcessInterruptedException("Canceled by user");
      }
      try {
        duplicates = getDuplications(execution, documents, document, CONTEXT, duplicatesRemoved);
        if (duplicates.isEmpty()) {
          duplicates = getDuplications(execution, documents, document, Repository.INDEX, null);
          if (duplicates.isEmpty()) {
            duplicates = getDuplications(execution, documents, document, DATABASE, null);
          }
        }
        long size = duplicates.stream()
            .filter(d -> isEqualsStatusCode(d.getStatus(), DocumentStatus.SENT))
            .count();
        if (size > 0) {
          String msg = "Duplicate document number ({0}) : This document is already sent.";
          logByLevel(execution, message);
          addDeads(execution, document);
          execution.setVariable(document.getReference(), format(msg, document.getReference()));
        }
      }
      catch (Exception e) {
        log.warn("Duplicate search failed for document {} ({})",
            document.getReference(), getRootCauseMessage(e));
        continue;
      }
      // applies duplicate policy
      try {
        if (applyDocumentPolicy(epolicy, execution, document, duplicates, Arrays.asList(type)))
          policied++;
      }
      catch (Exception e) {
        String msg = format("Duplicate check failed for document {0} ({1})", document.getReference(), getRootCauseMessage(e));
        log.error(msg);
        addDeads(execution, document);
        execution.setVariable(document.getReference(), msg);
      }
    }

    log.info("Duplicate document policy applied on {}/{} contextual documents",
        policied, documents.size());
  }

  private String validFieldsQueryAndParameters() {
    Multimap<Expression, Expression> map = ArrayListMultimap.create();

    map.put(contextQuery, contextParameters);
    map.put(databaseQuery, databaseParameters);
    map.put(mongoQuery, mongoParameters);

    if (map.entries()
        .stream()
        .allMatch(k -> k.getKey() == null && k.getValue() == null))
      return "Duplicate BQL query  : Please enter at least one query";

    if (map.entries()
        .stream()
        .anyMatch(k -> (k.getKey() == null && k.getValue() != null) ||
            (k.getKey() != null && k.getValue() == null)))
      return "Duplicate BQL query  : Please enter at least one field parameters";

    return null;
  }

  private List<Document> getDuplications(DelegateExecution execution, List<Document> documents, Document document, Repository repo,
      List<Document> duplicatesRemoved) throws Exception {
    Expression query;
    Expression parameters;
    String message;
    switch (repo) {
    case INDEX:
      query = mongoQuery;
      parameters = mongoParameters;
      message = "Duplicate document ({0}) number ({1}) in mongo";
      break;
    case DATABASE:
      query = databaseQuery;
      parameters = databaseParameters;
      message = "Duplicate document ({0}) number ({1}) in database";
      break;
    default:
      query = contextQuery;
      parameters = contextParameters;
      message = "Duplicate document ({0}) number ({1}) in context";
    }
    String bql = getString(execution, query);
    List<String> params = getList(execution, parameters);
    if (bql == null || params == null)
      return emptyList();
    Query queryDoc = createBuilder(bql).query(resolveQueryParameters(document, params), false);
    if (repo == CONTEXT)
      queryDoc = createBuilder(queryDoc).and(Clauses.notEqual("reference", document.getReference()))
          .query();

    List<Document> duplications = search(execution, repo, queryDoc, document.getClass(), document.getIndexType(), documents);

    // applies duplicate policy
    if (repo == CONTEXT && (duplicatesRemoved.isEmpty() || search(execution, repo,
        createBuilder(bql).query(resolveQueryParameters(document, params), false),
        document.getClass(), document.getIndexType(), duplicatesRemoved).isEmpty())) {
      duplicatesRemoved.add(document);
      duplications = emptyList();
    }

    if (!duplications.isEmpty()) {
      final DocumentPolicy epolicy = getEnum(execution, policy, DocumentPolicy.class, DEAD);

      if (repo == CONTEXT && epolicy == DocumentPolicy.REPLACE) {
        duplications.retainAll(duplicatesRemoved);
        addDeads(execution, duplications);
        duplicatesRemoved.add(document);
      }

      String msg = format(message, document.getReference(), document.getNumber());
      logByLevel(execution, msg);
      execution.setVariable(document.getReference(), msg);
    }

    return duplications;

  }

  @SuppressWarnings("unchecked")
  public final <D extends Document, I extends Indexable> List<Document> search(final DelegateExecution execution, final Repository repo,
      final Query query, final Class<D> documentClass, Class<I> indexClass, Collection<Document> documents) {
    switch (repo) {
    case CONTEXT:
      return QueryHelper.search(query, Document.class, documents == null && execution != null ? getDocuments(execution) : documents, getNonPersistentPropertiesWithoutPrefix(execution));
    case DATABASE:
      return (List<Document>) documentService.search(documentClass, query, null)
          .getContent();
    case INDEX:
      /*
       * this transformation is needed so as not to lose the specific fields of an Order / Invoice which will be saved in Mongo
       */
      if (indexClass.equals(Order.class))
        indexClass = (Class<I>) OrderIndex.class;
      if (indexClass.equals(Invoice.class))
        indexClass = (Class<I>) InvoiceIndex.class;
      List<Document> docs = (List<Document>) documentService.searchIndexedDocuments(documentClass, indexClass, query, null);
      List<Indexable> indexes = (List<Indexable>) documentService.searchIndexables(indexClass, query, null)
          .getContent();

      indexes.forEach(index -> {
        if (docs.stream()
            .noneMatch(doc -> index.getEntityId()
                .equals(doc.getIndex()
                    .getValue()
                    .getEntityId()))) {
          documentService.removeIndexable(index);
        }
      });
      return docs;
    default:
      throw new IllegalArgumentException("Unsupported repository: " + repo);
    }
  }

  /* -- UTILITIES -- */

  /**
   * @param document
   * @param propertyPathes
   * @return the parameters list resolved from the given property path list.
   * @throws IllegalAccessException
   * @throws InvocationTargetException
   * @throws NoSuchMethodException
   */
  private List<?> resolveQueryParameters(final Document document, final List<String> propertyPathes)
      throws IllegalAccessException, InvocationTargetException, NoSuchMethodException {
    final List<Object> r = new ArrayList<>(propertyPathes.size());
    for (String propertyPath : propertyPathes)
      r.add(getProperty(document, propertyPath));
    log.debug("Resolved document '{}' parameters: {}", document.getReference(), r);
    return r;
  }

  private void logByLevel(DelegateExecution execution, String message) {
    String errorLevel;
    errorLevel = getString(execution, getErrorLevel());
    if (StringUtils.isBlank(errorLevel)) {
      errorLevel = getLogLevel(execution).toString();
    }
    switch (errorLevel) {
    case "INFO":
      log.info(message);
      break;
    case "DEBUG":
      log.debug(message);
      break;
    case "WARN":
      log.warn(message);
      break;
    case "ERROR":
      log.error(message);
      break;
    default:
      log.error("Invalid error level");
      break;
    }
  }

  /* -- ACCESSORS -- */

  public Expression getPolicy() {
    return policy;
  }

  public void setPolicy(Expression policy) {
    this.policy = policy;
  }

  public Expression getContextQuery() {
    return contextQuery;
  }

  public void setContextQuery(Expression contextQuery) {
    this.contextQuery = contextQuery;
  }

  public Expression getDatabaseQuery() {
    return databaseQuery;
  }

  public void setDatabaseQuery(Expression databaseQuery) {
    this.databaseQuery = databaseQuery;
  }

  public Expression getMongoQuery() {
    return mongoQuery;
  }

  public void setMongoQuery(Expression mongoQuery) {
    this.mongoQuery = mongoQuery;
  }

  public Expression getContextParameters() {
    return contextParameters;
  }

  public void setContextParameters(Expression contextParameters) {
    this.contextParameters = contextParameters;
  }

  public Expression getDatabaseParameters() {
    return databaseParameters;
  }

  public void setDatabaseParameters(Expression databaseParameters) {
    this.databaseParameters = databaseParameters;
  }

  public Expression getMongoParameters() {
    return mongoParameters;
  }

  public void setMongoParameters(Expression mongoParameters) {
    this.mongoParameters = mongoParameters;
  }

  public Expression getTypeToIgnore() {
    return typeToIgnore;
  }

  public void setTypeToIgnore(Expression typeToIgnore) {
    this.typeToIgnore = typeToIgnore;
  }

  public Expression getErrorLevel() {
    return errorLevel;
  }

  public void setErrorLevel(Expression errorLevel) {
    this.errorLevel = errorLevel;
  }
}
