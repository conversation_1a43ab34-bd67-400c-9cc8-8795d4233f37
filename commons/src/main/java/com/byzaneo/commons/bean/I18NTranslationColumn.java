package com.byzaneo.commons.bean;

import com.byzaneo.query.clause.Clause;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Locale;
import java.util.function.BiFunction;

public enum I18NTranslationColumn {
  CODE("code"),
  DEFAULT_VALUE("defaultValue"),
  NEW_VALUE("newValue"),
  LOCALE("locale"),
  DEFAULT_VALUE_CHANGED_AT("defaultValueChangedAt"),
  NEW_VALUE_CHANGED_AT("newValueChangedAt"),
  MODULE_ID("i18NModule.id"),
  NEW_VALUE_CHANGED_BY("newValueChangedBy");

  private final String name;
  I18NTranslationColumn(String column) {
    this.name = column;
  }
  public String getName() {
    return this.name;
  }
}
