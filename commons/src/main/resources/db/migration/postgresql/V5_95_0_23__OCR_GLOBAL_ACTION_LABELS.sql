INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verifications', 'OCR verify selection', 'en', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verifications', 'Vidéocoder la sélection', 'fr', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_some_title', 'Ineligible Invoices Detected for OCR verification', 'en', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_some_title', 'Factures non éligibles au vidéocodage', 'fr', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_some_message', 'Some selected invoices are not in the "To videocode" status.
Do you want to proceed with only the eligible invoices?', 'en', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_some_message', 'Certaines factures sélectionnées ne sont pas au statut "À vidéocoder".
Voulez-vous continuer le vidéocodage uniquement sur les factures éligibles ?', 'fr', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_none_message', 'None of the selected invoices are in the "To videocode" status.
Please correct your selection before launching OCR verification.', 'en', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_none_message', 'Aucune des factures sélectionnées n''est au statut "À vidéocoder".
Merci de corriger votre sélection avant de lancer le vidéocodage. ', 'fr', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_none_title', 'No invoices eligible for OCR verification', 'en', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_none_title', 'Aucune facture éligible au vidéocodage', 'fr', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_line_message', 'Invoice n° {{number}} - Status : ', 'en', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_verify_line_message', 'Facture n° {{number}} – Statut : ', 'fr', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_selected_limit_warning', 'You cannot start video encoding on more than 50 invoices at once. Please reduce your selection.', 'en', NOW(), 6);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ocr_selected_limit_warning', 'Vous ne pouvez pas lancer le vidéocodage sur plus de 50 factures à la fois. Veuillez réduire votre sélection.', 'fr', NOW(), 6);


INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ok', 'Ok', 'en', NOW(), 3);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('ok', 'Ok', 'fr', NOW(), 3);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('proceed', 'Proceed', 'en', NOW(), 3);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('proceed', 'Confirmer', 'fr', NOW(), 3);

UPDATE doc_sts_values SET style = 'status_orange' WHERE id IN (SELECT doc_value FROM doc_status_values_mapping WHERE doc_status = 'OCR_POSTPONED');
UPDATE doc_sts_values SET style = 'default' WHERE id IN (SELECT doc_value FROM doc_status_values_mapping WHERE doc_status = 'OCR_TO_VERIFY');