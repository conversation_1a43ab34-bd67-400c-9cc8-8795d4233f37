# -----------------------------------------------------------------------------
# P R O P E R T I E S
# Targeted at system administrators, to avoid touching the context XML files.
# -----------------------------------------------------------------------------

version = ${version}-${timestamp}-r${buildNumber}
timezone = Europe/Paris

# ** D I R E C T O R I E S   L A Y O U T **

webapp.dir	  = ./target
data.dir	  = ${webapp.dir}/data
bin.dir		  = ${data.dir}/bin
temp.dir	  = ${data.dir}/tmp
work.dir	  = ${data.dir}/work
backup.dir	  = ${data.dir}/backup
input.dir	  = ${data.dir}/input
output.dir	  = ${data.dir}/output
resources.dir = ${data.dir}/resources


# ** D A T A B A S E **

# - DATASOURCE -
database.flyway.disable = true
database.schema		= ci
database.type		= mysql
database.datasource	= pooledDataSource
#database.datasource	= jdbcDataSource
# - JDBC -
database.driver		= com.mysql.jdbc.Driver
database.url		= ***************************/${database.schema}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false
database.username	= cidbuser
database.password	= cidbpwd
database.target		= com.byzaneo.commons.dao.hibernate.support.MySQL5Dialect
database.showSql	= false
# - DDL -
database.generateDdl		= false
# validate | update | create | create-drop
database.generateDdl.mode	=
# Comma-separated names of the optional files containing
# SQL DML statements executed if database.generateDdl.mode
# is set to create or create-drop.
# File order matters, the statements of a give file are executed
# before the statements of the following files.
database.generateDdl.imports= /META-INF/data/import.${database.type}.sql
# - POOL -
# (database.datasource is set to pooledDataSource)
# The maximum size that the pool is allowed to
# reach, including both idle and in-use connections.
# Basically this value will determine the maximum number of actual
# connections to the database backend.
# A reasonable value for this is best determined by your execution
# environment. When the pool reaches this size, and no idle connections
# are available, calls to getConnection() will block for up to
# connectionTimeout milliseconds before timing out.
database.pool.maxConnections=10
# The minimum number of idle connections that the pool tries
# to maintain in the pool. If the idle connections dip below this value,
# the pool will make a best effort to add additional connections quickly
# and efficiently.
# However, for maximum performance and responsiveness to spike demands,
# we recommend to set this value to the same value than the
# database.pool.maxConnections.
database.pool.minConnections=${database.pool.maxConnections}
# The default auto-commit behavior of connections returned from the pool.
database.pool.autoCommit=true
# The maximum number of milliseconds that a client (that's you) will wait
# for a connection from the pool.
# If this time is exceeded without a connection becoming available,
# a SQLException will be thrown.
# Lowest acceptable connection timeout is 250ms.
database.pool.connectionTimeout=30000
# The maximum amount of time that a connection is allowed to sit idle in
# the pool.
# This setting only applies when minimumIdle is defined to be less than
# maximumPoolSize.
# Whether a connection is retired as idle or not is subject to a maximum
# variation of +30 seconds, and average variation of +15 seconds.
# A connection will never be retired as idle before this timeout.
# A value of 0 means that idle connections are never removed from the pool.
# The mimimum allowed value is 10000ms (10 seconds).
database.pool.idleTimeout=600000
# The maximum lifetime of a connection in the pool. When a connection
# reaches this timeout it will be retired from the pool, subject to a
# maximum variation of +30 seconds. An in-use connection will never be
# retired, only when it is closed will it then be removed.
# We strongly recommend setting this value, and it should be at least
# 30 seconds less than any database-level connection timeout.
# A value of 0 indicates no maximum lifetime (infinite lifetime),
# subject of course to the idleTimeout setting.
database.pool.maxLifetime=1800000
# If your driver supports JDBC4 we strongly recommend not setting this
# property. This is for "legacy" databases that do not support the JDBC4
# Connection.isValid() API. This is the query that will be executed just
# before a connection is given to you from the pool to validate that the
# connection to the database is still alive. Again, try running the pool
# without this property, the pool will log an error if your driver is not
# JDBC4 compliant to let you know.
database.pool.connectionTestQuery=

# ** W E B A P P **

webapp.host.url = http://localhost:8080
webapp.contextname = commons

# ** S E R V I C E S **

# -- MAIL SERVER --
mail.debug          = true
mail.from.name      = Generix CM
mail.from.address   = <EMAIL>
mail.incoming.folder = INBOX
# INCOMING
mail.incoming.protocol = imaps
mail.incoming.host  = imap.generixgroup.com
mail.incoming.port  = 993
mail.incoming.user  = <EMAIL>
mail.incoming.password = #####
mail.incoming.ssl  = true
# OUTGOING
mail.outgoing.protocol = smtp
mail.outgoing.host  = smtp.generixgroup.com
mail.outgoing.port  = 587
mail.outgoing.user  = <EMAIL>
mail.outgoing.password = #####
mail.outgoing.starttls.enable = true
mail.outgoing.auth  = true

# -- BUNDLES --
labels.locale.default=en

# -- EXECUTOR --
executor.autostart=false
# the minimum number of threads to keep in the pool
executor.min.pool.size=0
# the maximum number of threads to allow in the pool (negative
# means not bound)
executor.max.pool.size=-1
# keepAliveTime when the number of threads is greater than
# the core, this is the maximum time that excess idle threads
# will wait for new tasks before terminating (in milliseconds).
executor.keepalive=60000
# -1 permet d'utiliser l'executor principal
report.executor.min.pool.size=${executor.min.pool.size}
report.executor.max.pool.size=${executor.max.pool.size}
report.executor.keepalive=${executor.keepalive}

# -- EVENTS --
# Enables the event storage.
# This imply also the EventOperations configuration (see below for the MongoDB
# configuration used by the Spring configuration file:
# /META-INF/spring/commons-events.beans.xml)
event.persist = false
# - MONGODB -
# -- I N D E X --
index.mongo.uri=mongodb://ci:ci@ci-mongo:27017/ci

# The MongoClient description.
#event.mongo.description=Mongo Index Operations Client
# The comma delimited list of username:password@database entries
# to use for authentication.
# Appending ? uri.authMechanism allows to specify the authentication
# challenge mechanism.
#event.mongo.uri=
# The number of connections allowed per host. Will block if run out.
# Default is 10. System property MONGO.POOLSIZE can override
#event.mongo.connectionsPerHost=10
# The minimum number of connections per host. 0 is default and infinite.
#event.mongo.minConnectionsPerHost=0
# The maximum idle time for a pooled connection. 0 is default and infinite.
#event.mongo.maxConnectionIdleTime=0
# The maximum life time for a pooled connection. 0 is default and infinite.
#event.mongo.maxConnectionLifeTime=0
# The connect timeout in milliseconds. 0 is infinite.
# Default is 10000 ms (10 seconds)
#event.mongo.connectTimeout=10000
# The multiplier for connectionsPerHost for # of threads that can block.
# Default is 5. If connectionsPerHost is 10, and threadsAllowedToBlockForConnectionMultiplier is 5,
# then 50 threads can block more than that and an exception will be thrown.
#event.mongo.threadsAllowedToBlockForConnectionMultiplier=5
# The max wait time of a blocking thread for a connection.
# Default is 180000 ms (3 minutes)
#event.mongo.maxWaitTime=180000
# The keep alive flag, controls whether or not to have socket keep alive timeout.
# Defaults to false.
#event.mongo.socketKeepAlive=false
# The socket timeout. 0 is default and infinite.
#event.mongo.socketTimeout=0
# The read preference.
#	- PRIMARY
#	- PRIMARY_PREFERRED (default)
#	- SECONDARY
#	- SECONDARY_PREFERRED
#	- NEAREST
#event.mongo.readPreference=PRIMARY_PREFERRED
# The connect timeout for connections used for the cluster heartbeat.
# Default 20000 ms (20s)
#event.mongo.heartbeatConnectTimeout=20000
# This is the frequency that the driver will attempt to determine the
# current state of each server in the cluster.
# Default 10000 ms (10s)
#event.mongo.heartbeatFrequency=10000
# The socket timeout for connections used for the cluster heartbeat.
# Default 20000 ms (20s)
#event.mongo.heartbeatSocketTimeout=20000
# In the event that the driver has to frequently re-check a server's
# availability, it will wait at least this long since the previous
# check to avoid wasted effort.
# Default 500 ms (0.5s)
#event.mongo.minHeartbeatFrequency=500