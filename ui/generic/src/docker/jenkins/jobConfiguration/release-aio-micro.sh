#/bin/bash

cd ${JENKINS_HOME}/release-workspace/chassagne-maven

SNAPSHOT="-SNAPSHOT"

GNX_SNAPSHOT=`grep '<gnx-version>' pom.xml | sed -e "s/<gnx-version>//g" | sed -e "s/<\/gnx-version>//g" | sed -e "s/ //g" | sed -e "s/\t//g" | sed -e "s/\r//g"`
export GNX_VERSION=`echo ${GNX_SNAPSHOT} | sed -e s/${SNAPSHOT}//g`
GNX_MAJOR=`echo ${GNX_VERSION} | cut -d. -f1`
GNX_MINOR=`echo ${GNX_VERSION} | cut -d. -f2`
GNX_MICRO=`echo ${GNX_VERSION} | cut -d. -f3`
GNX_NEW_MICRO=$(( $GNX_MICRO + 1))
GNX_NEW_SNAPSHOT=${GNX_MAJOR}"."${GNX_MINOR}"."${GNX_NEW_MICRO}${SNAPSHOT}
echo "gitlabSourceBranch=tags/GNX-$GNX_VERSION" > ${JENKINS_HOME}/release-workspace/gnx-version.properties
echo "BRANCH_TO_BUILD=tags/GNX-$GNX_VERSION" >> ${JENKINS_HOME}/release-workspace/gnx-version.properties
echo "DEPLOY=deploy:deploy" >> ${JENKINS_HOME}/release-workspace/gnx-version.properties
echo "PROFILE=" >> ${JENKINS_HOME}/release-workspace/gnx-version.properties
echo "GIT_TAG=GNX-$GNX_VERSION" > ${JENKINS_HOME}/release-workspace/docker.properties
echo "DOCKER_TAG=$GNX_VERSION" >> ${JENKINS_HOME}/release-workspace/docker.properties

BYZ_SNAPSHOT=`grep '<byzaneo-version>' pom.xml | sed -e "s/<byzaneo-version>//g" | sed -e "s/<\/byzaneo-version>//g" | sed -e "s/ //g" | sed -e "s/\t//g" | sed -e "s/\r//g"`
export BYZ_VERSION=`echo ${BYZ_SNAPSHOT} | sed -e s/${SNAPSHOT}//g`
BYZ_MAJOR=`echo ${BYZ_VERSION} | cut -d. -f1`
BYZ_MINOR=`echo ${BYZ_VERSION} | cut -d. -f2`
BYZ_MICRO=`echo ${BYZ_VERSION} | cut -d. -f3`
BYZ_NEW_MICRO=$(( $BYZ_MICRO + 1))
BYZ_NEW_SNAPSHOT=${BYZ_MAJOR}"."${BYZ_MINOR}"."${BYZ_NEW_MICRO}${SNAPSHOT}
echo "gitlabSourceBranch=tags/BYZ-$BYZ_VERSION" > ${JENKINS_HOME}/release-workspace/byz-version.properties
echo "DEPLOY=deploy:deploy" >> ${JENKINS_HOME}/release-workspace/byz-version.properties
echo "PROFILE=" >> ${JENKINS_HOME}/release-workspace/byz-version.properties

#Update pom.xml, commit, and tag sources

#Chassagne Maven
echo "Generating $GNX_VERSION for chassagne-maven"
cd ${JENKINS_HOME}/release-workspace/chassagne-maven && git checkout ${GNX_BRANCH} && git branch --set-upstream-to=origin/${GNX_BRANCH} ${GNX_BRANCH} && git pull --rebase \
&& find -name "pom.xml" -exec sed -i "s/${GNX_SNAPSHOT}/${GNX_VERSION}/g" {} \; \
&& find -name "pom.xml" -exec sed -i "s/${BYZ_SNAPSHOT}/${BYZ_VERSION}/g" {} \; \
&& git config --global user.name "jenkins" && git config --global user.email "<EMAIL>" \
&& find -name "pom.xml" -exec git add {} + && git commit -m "@Version ${GNX_VERSION}" && git push origin ${GNX_BRANCH} \
&& git tag -a GNX-${GNX_VERSION} -m "Version ${GNX_VERSION}" && git push origin GNX-${GNX_VERSION}

#Byzaneo
echo "Generating $BYZ_VERSION for byzaneo"
cd ${JENKINS_HOME}/release-workspace/byzaneo && git checkout ${BYZ_BRANCH} && git branch --set-upstream-to=origin/${BYZ_BRANCH} ${BYZ_BRANCH} && git pull --rebase \
&& find -name "pom.xml" -exec sed -i "s/${BYZ_SNAPSHOT}/${BYZ_VERSION}/g" {} \; \
&& find -name "pom.xml" -exec sed -i "s/${GNX_SNAPSHOT}/${GNX_VERSION}/g" {} \; \
&& git config --global user.name "jenkins" && git config --global user.email "<EMAIL>" \
&& find -name "pom.xml" -exec git add {} + && git commit -m "@Version ${BYZ_VERSION}" && git push origin ${BYZ_BRANCH} \
&& git tag -a BYZ-${BYZ_VERSION} -m "Version ${BYZ_VERSION}" && git push origin BYZ-${BYZ_VERSION}

#Chassagne
echo "Generating $GNX_VERSION for chassagne"
cd ${JENKINS_HOME}/release-workspace/chassagne && git checkout ${GNX_BRANCH} && git branch --set-upstream-to=origin/${GNX_BRANCH} ${GNX_BRANCH} && git pull --rebase \
&& find -name "pom.xml" -exec sed -i "s/${GNX_SNAPSHOT}/${GNX_VERSION}/g" {} \; \
&& git config --global user.name "jenkins" && git config --global user.email "<EMAIL>" \
&& find -name "pom.xml" -exec git add {} + && git commit -m "@Version ${GNX_VERSION}" && git push origin ${GNX_BRANCH} \
&& git tag -a GNX-${GNX_VERSION} -m "Version ${GNX_VERSION}" && git push origin GNX-${GNX_VERSION}

#Revert update pom.xml
#Chassagne Maven
echo "Updating chassagne-maven with ${GNX_NEW_SNAPSHOT} version"
cd ${JENKINS_HOME}/release-workspace/chassagne-maven && git checkout ${GNX_BRANCH} && git branch --set-upstream-to=origin/${GNX_BRANCH} ${GNX_BRANCH} && git pull --rebase \
&& find -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/${GNX_NEW_SNAPSHOT}/g" {} \; \
&& find -name "pom.xml" -exec sed -i "s/${BYZ_VERSION}/${BYZ_NEW_SNAPSHOT}/g" {} \; \
&& git config --global user.name "jenkins" && git config --global user.email "<EMAIL>" \
&& find -name "pom.xml" -exec git add {} + && git commit -m "@PostVersion ${GNX_VERSION} (${GNX_NEW_SNAPSHOT})" && git push origin ${GNX_BRANCH} 

#Byzaneo
echo "Updating byzaneo with ${BYZ_NEW_SNAPSHOT} version"
cd ${JENKINS_HOME}/release-workspace/byzaneo && git checkout ${BYZ_BRANCH} && git branch --set-upstream-to=origin/${BYZ_BRANCH} ${BYZ_BRANCH} && git pull --rebase \
&& find -name "pom.xml" -exec sed -i "s/${BYZ_VERSION}/${BYZ_NEW_SNAPSHOT}/g" {} \; \
&& find -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/${GNX_NEW_SNAPSHOT}/g" {} \; \
&& git config --global user.name "jenkins" && git config --global user.email "<EMAIL>" \
&& find -name "pom.xml" -exec git add {} + && git commit -m "@PostVersion ${BYZ_VERSION} (${BYZ_NEW_SNAPSHOT})" && git push origin ${BYZ_BRANCH}

#Chassagne
echo "Updating chassagne with ${GNX_NEW_SNAPSHOT} version"
cd ${JENKINS_HOME}/release-workspace/chassagne && git checkout ${GNX_BRANCH} && git branch --set-upstream-to=origin/${GNX_BRANCH} ${GNX_BRANCH} && git pull --rebase \
&& find -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/${GNX_NEW_SNAPSHOT}/g" {} \; \
&& git config --global user.name "jenkins" && git config --global user.email "<EMAIL>" \
&& find -name "pom.xml" -exec git add {} + && git commit -m "@PostVersion ${GNX_VERSION} (${GNX_NEW_SNAPSHOT})" && git push origin ${GNX_BRANCH}

