/**
 * A simple theme for reveal.js presentations, similar
 * to the default theme. The accent color is brown.
 *
 * This theme is Copyright (C) 2012-2013 <PERSON>, http://owenversteeg.com - it is MIT licensed.
 */


// Default mixins and settings -----------------
@import "../template/mixins";
@import "../template/settings";
// ---------------------------------------------



// Override theme settings (see ../template/settings.scss)
$mainFont: 'Palatino Linotype', 'Book Antiqua', Palatino, FreeSerif, serif;
$mainColor: #000;
$headingFont: 'Palatino Linotype', 'Book Antiqua', Palatino, FreeSerif, serif;
$headingColor: #383D3D;
$headingTextShadow: none;
$headingTextTransform: none;
$backgroundColor: #F0F1EB;
$linkColor: #51483D;
$linkColorHover: lighten( $linkColor, 20% );
$selectionBackgroundColor: #26351C;

.reveal a {
  line-height: 1.3em;
}


// Theme template ------------------------------
@import "../template/theme";
// ---------------------------------------------
