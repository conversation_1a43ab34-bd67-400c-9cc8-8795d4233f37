package com.byzaneo.generix.ui.filter;

import static com.byzaneo.commons.ui.util.JSFHelper.getRequestURL;
import static com.byzaneo.generix.security.oidc.OIDCCodeAuthenticationFilter.BO_SSO_PATH;
import static com.byzaneo.generix.security.oidc.OIDCCodeAuthenticationFilter.FO_SSO_PATH;
import static com.byzaneo.generix.security.oidc.OIDCHelper.REST_SECURE;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.springframework.security.core.context.SecurityContextHolder.getContext;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.byzaneo.generix.api.Configuration;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.bean.KeycloakConfiguration;
import com.byzaneo.generix.bean.RoleMapping;
import com.byzaneo.generix.exception.KeycloakDuplicatedRealmException;
import com.byzaneo.generix.security.oidc.AsyncDownloadSession;
import com.byzaneo.generix.security.oidc.OIDCHelper;
import com.byzaneo.generix.service.InstanceService;
import com.byzaneo.generix.service.KeycloakService;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.dao.GroupDAO;
import com.byzaneo.security.dao.UserDAO;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.security.service.SecurityService.PrincipalType;
import com.byzaneo.security.service.authentication.AuthenticationRestService;
import com.byzaneo.security.spring.UserDetails;
import com.generix.tokenvalidator.check.config.TokenValidatorManager;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

public class ToOIDCAuthenticationFilter extends OncePerRequestFilter {
  public static final String REALM_ACCESS = "realm_access";
  public static final String REALM = "realm";
  private AntPathRequestMatcher urlMatcher;

  private String adminReal;
  private String clientId;
  private String baseUrl;
  private String loginPath;
  private final InstanceService instanceService;

  private TokenValidatorManager tokenValidator;

  @Autowired
  @Qualifier(AuthenticationRestService.SERVICE_NAME)
  private AuthenticationRestService authenticationRestService;

  @Autowired
  @Qualifier(AccountService.SERVICE_NAME)
  private  AccountService accountService;

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private transient SecurityService securityService;

  @Autowired
  @Qualifier(UserDAO.DAO_NAME)
  private transient UserDAO userAO;

  @Autowired
  @Qualifier(GroupDAO.DAO_NAME)
  private GroupDAO groupDAO;

  @Inject
  private AsyncDownloadSession asyncDownloadSession;

  public ToOIDCAuthenticationFilter(String adminReal, String clientId, String baseUrl, String pathToFilter, InstanceService instanceService,
      String tokenBaseUrl, int cacheTokenDelay, int cacheCertsKeysDelay) {
    this.adminReal = adminReal;
    this.clientId = clientId;
    this.baseUrl = baseUrl;
    this.urlMatcher = new AntPathRequestMatcher(pathToFilter);
    this.instanceService = instanceService;
    tokenValidator = TokenValidatorManager.getInstance(tokenBaseUrl, cacheTokenDelay, cacheCertsKeysDelay);
    if (isNotBlank(adminReal) && isNotBlank(clientId) && isNotBlank(baseUrl))
      loginPath = new StringBuilder(baseUrl).append("/realms/")
          .append(adminReal)
          .append("/protocol/openid-connect/auth?client_id=")
          .append(clientId)
          .append("&response_type=code")
          .toString();
  }

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {


    Instance instance = instanceService.findInstanceByHostName(request.getServerName());
    Map<String, String> authServerConfiguration = instanceService.getAuthenticationServerConfiguration(instance, request.getServerName());
    Authentication authentication = getContext().getAuthentication();

    if (checkAndRedirectFromAngular(request, response, authentication, instance, authServerConfiguration)) return;

    if (authentication == null || !authentication.isAuthenticated()) {
      checkAsyncDownloadUrl(getRequestURL(request));
      if (loginPath != null && urlMatcher.matches(request)) {
        response.sendRedirect(loginPath + "&redirect_uri=" + URLEncoder.encode(getSSOPath(request, BO_SSO_PATH), StandardCharsets.UTF_8));
        return;
      }

      boolean noAppuser = request.getRequestURL()
          .toString()
          .contains("no_user");
      boolean oidcError = request.getRequestURL()
          .toString()
          .contains("oidc_error");
      if (!authServerConfiguration.isEmpty() && !noAppuser && !oidcError) {
        response.sendRedirect(baseUrl + "/realms/" + authServerConfiguration.get(
            REALM) + "/protocol/openid-connect/auth?client_id=" +
            authServerConfiguration.get(
                "clientId") + "&response_type=code" + "&redirect_uri=" + URLEncoder.encode(getSSOPath(request, FO_SSO_PATH),
                StandardCharsets.UTF_8));
        return;
      }
    }
    filterChain.doFilter(request, response);
  }

  private boolean checkAndRedirectFromAngular(HttpServletRequest request, HttpServletResponse response, Authentication authentication, Instance instance, Map<String, String> authServerConfiguration) throws IOException {
    if(authentication == null) {
      if (Boolean.parseBoolean(request.getParameter("fromAngular")) && request.getParameter("token") != null) {
        String token = request.getParameter("token");
        String username = JWT.decode(token)
            .getClaim("preferred_username")
            .asString();
        String jsf_client_access_token = authenticationRestService.exchangeUserTokenAcrossClients(
            instanceService.getAuthenticationServerConfigurationByUser(instance, userAO.findByLoginOrOpenId(username), false), token);
        tokenValidator.startValidateToken(jsf_client_access_token);
        DecodedJWT decodedJWT = JWT.decode(jsf_client_access_token);
        @SuppressWarnings("unchecked")
        List<String> roles = decodedJWT.getClaims()
            .containsKey(REALM_ACCESS)
            ? (List<String>) decodedJWT.getClaim(REALM_ACCESS)
            .asMap()
            .get("roles")
            : null;
        boolean keycloakHasIDP = roles != null && roles.contains(AccountService.KEYCLOAK_ROLE_EXTERNAL_IDP);
        List<RoleMapping> roleMappings = null;
        Locale defaultLanguage = null;
        Group entity = null; // the Partner or Company for the user coming from external IDP of Keycloak
        List<Group> allCompanyRolesAsGroup = null;
        if (keycloakHasIDP) {
          defaultLanguage = instance.getConfiguration()
              .getDefaultLanguage();

          // A realm must be used by only a partner or only a company. If 2 entities use the same realm it is wrong, we abort all
          List<Group> groupsUsingRealm = groupDAO.getGroupsUsingRealm(authServerConfiguration.get(REALM));
          if (CollectionUtils.isEmpty(groupsUsingRealm)) {
            throw new KeycloakDuplicatedRealmException(
                "There is no Company / Partner using this realm: " + authServerConfiguration.get(REALM));
          }
          if (groupsUsingRealm.size() > 1) {
            throw new KeycloakDuplicatedRealmException("Not allowed to have same realm for more than one entity (Company / Partners) ");
          }
          // entity can be a company or a partner this is the entity the user will be added to when creating a new user from token
          entity = groupsUsingRealm.get(0);
          Map<String, Configuration> configs = this.instanceService.getConfigurations(entity);
          if (configs != null) {
            KeycloakConfiguration config = (KeycloakConfiguration) configs.get(KeycloakService.SERVICE_NAME);
            if (config != null) {
              roleMappings = config.getRoleMappings();
            }
          }

          allCompanyRolesAsGroup = groupDAO.getGroupChildrenByParent(instance.getGroup()
              .getId(), PrincipalType.ROLE.toString());

        }
        User user = accountService.getUserFromToken(decodedJWT, false, keycloakHasIDP, defaultLanguage, entity);
        if (keycloakHasIDP) {
          user = OIDCHelper.compareRoles(user, allCompanyRolesAsGroup, roleMappings, decodedJWT, securityService, userAO);
        }
        final UserDetails userDetails = new UserDetails(user, accountService);
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(userDetails, null,
            accountService.getAuthorities(user));
        SecurityContextHolder.getContext()
            .setAuthentication(authenticationToken);

        String queryString = request.getParameterMap().entrySet().stream()
            .flatMap(entry -> Stream.of(entry.getValue())
                .map(value -> entry.getKey() + "=" + value))
            .collect(Collectors.joining("&"));

        String ressource = request.getRequestURL() + "?" + queryString;
        response.sendRedirect(ressource);
        return true;
      }
    }
    return false;
  }


  private String getSSOPath(HttpServletRequest request, String ssoPath) {
    return URLDecoder.decode(getRequestURL(request)
        .replace(request.getServletPath(), ssoPath), StandardCharsets.UTF_8);
  }

  private void checkAsyncDownloadUrl(String requestURL) {
    Optional.ofNullable(requestURL)
        .filter(url -> url.contains(REST_SECURE))
        .ifPresent(asyncDownloadSession::setAsyncDownloadUrl);
  }

}
