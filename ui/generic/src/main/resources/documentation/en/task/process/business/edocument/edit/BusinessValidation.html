<p>This business task checks that each message complies with a set of rules.</p>

<p>The RTE executables used for the check must be imported beforehand in the customer&#39;s environment via <strong>Repository </strong>&gt; <strong>Templates</strong>, <strong>Import </strong>&gt; <strong>RTE</strong>.</p>

<p><strong>Input: </strong>EDIUNIT file</p>

<p><strong>Output: </strong>List of GS1 errors. Otherwise, move on the following task.</p>

<h2>Procedure performed by the task:</h2>

<ol>
	<li>Retrieve all documents with a DOC_TYPE other than INTERCHANGE_EDIFACT.</li>
	<li>For each document, verify that it is an INVOIC document, and that that the rule isn&rsquo;t empty (if empty, the document is added to the DEAD queue).</li>
	<li>Verify that the document has only one EDIUNIT or PDF DocumentFile.</li>
	<li>For each document with a status other than REFUSED, depending on the rule of the document and the input settings, the business control EDI is summoned.</li>
	<li>A control error report is generated by the RTE.</li>
	<li>All the errors reported by the RTE are stored as LabelSet in the document errors.</li>
	<li>If the document has at least one error., its status changes to REFUSED.</li>
	<li>INTERCHANGE_EDIFACT documents whose children are in the DEAD queue are moved to the DEAD queue.</li>
</ol>

<h1>Description</h1>

<ul>
	<li>[@bttitle]</li>
	<li>[@btdisabled]</li>
	<li><strong>Timeout:</strong> Select the time allocated to run the RTE program (in seconds). If the program runtime exceeds the set value, the process is interrupted.</li>
	<li><strong>Legal status:</strong> <em>Check </em>this box to fill in the legal status on top of the business status. If it is <em>unchecked</em>, the legal status is not changed by this business task.</li>
	<li><strong>Executable D96A:</strong> Select the name of the RTE program running the required business checks in keeping with the EDIFACT D96A standard. If the field is left blank, the default value is used. Default value: <em>edoc_validation_gs1_d96a.exe</em> program that comes as standard confirms the message in keeping with GS1 France&#39;s MIG.</li>
	<li><strong>Executable D93A:</strong> Select the name of the RTE program running the required business checks in keeping with the EDIFACT D93A standard (no default RTE program).</li>
	<li><strong>Executable D01B:</strong> Select the name of the RTE program running the required business checks in keeping with the EDIFACT D01B standard (no default RTE program).</li>
	<li><strong>Executable NAMED PDF:</strong> Select the name of the RTE program running the required business checks in keeping with the structure of the PDF file (no default RTE program). Only applicable for PDF processes.</li>
</ul>