<p>This portlet is used to upload a PDF file and retrieve its data using the mask technique (smartPDF).</p>

<p><b>Requirements:</b> SmartPDF templates created using the external tool called PDFExtractor.</p>

<div style="background: rgb(238, 238, 238); border: 1px solid rgb(204, 204, 204); padding: 5px 10px;"><strong>Note:</strong> The smart PDF technology is replaced by the OCR technique. The Generix OCR technique is a process (not a portlet), which launches Standard Invoice Edition portlet.</div>

<h1>Use Cases</h1>

<p>Use this portlet to upload a PDF file and get the data with Smart PDF technology.</p>

<h1>Description</h1>

<ul>
	<li>[@pttitle]</li>
	<li>[@ptdesc]</li>
	<li><strong>Filter</strong>
	<ul>
		<li><strong>Customer:</strong> Select a customer to display only the masks available for the selected customer.</li>
	</ul>
	</li>
	<li><strong>Labels</strong><br />Fill in the fields in this section with the title and the description of each of the 4 steps of this portlet. Find below the description of each step.
	<ul>
		<li><strong>Step 1 title:</strong> MASK. Select the format of the invoice. Make sure you select a mask that matches the format for the invoice you want to import.</li>
		<li><strong>Step 2 title:</strong> IMPORT. Select the invoices you want to upload in the application. You can select one or several invoices from your local hard drive or drag and drop them.</li>
		<li><strong>Step 3 title:</strong> UPLOAD. See the upload progress and select Next to upload your invoices. When you select Next, your documents are imported to the application.</li>
		<li><strong>Step 4 title:</strong> SEND. See the status of the uploaded documents. Go to Invoice KO to see and correct the invoices that couldn&rsquo;t be uploaded.</li>
	</ul>
	</li>
	<li><strong>Processing</strong>
	<ul>
		<li><strong>Name of process:</strong> Select the process will be launched once the upload is finished.</li>
	</ul>
	</li>
	<li><strong>Sequence</strong>
	<ul>
		<li><strong>Invoices in error:</strong> Select what page to redirect the user to when they select this button, which corresponds to the list of invoices that weren&rsquo;t imported.</li>
		<li><strong>Imported invoices:</strong> Select what page to redirect the user to when they select this button, which corresponds to the list of invoices that were successfully imported.</li>
		<li><strong>Invoice history:</strong> Select what page to redirect the user to when they select this button, which corresponds to the history of invoice import.</li>
	</ul>
	</li>
</ul>

<h1>Rights</h1>

<table>
	<thead>
		<tr>
			<th>Read</th>
			<th>Update</th>
			<th>Create</th>
			<th>Delete</th>
			<th>Execute</th>
			<th>Special</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>N/A</td>
			<td>Impossible to display this portlet with only this right.</td>
			<td>Partner contacts: Users can see the portlet.</td>
			<td>N/A</td>
			<td>N/A</td>
			<td>Customer users and Partner contacts: Users can see the portlet.</td>
		</tr>
	</tbody>
</table>