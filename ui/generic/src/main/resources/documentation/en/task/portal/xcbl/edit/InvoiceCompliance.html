

 
<p>The Invoice Compliance portlet allows users to access information about the invoice and display its readable.</p>
<p>The Compliance page contains the following elements:</p>

<ul>
  <li>
	<p><span class="bold"><strong>Compliance cockpit</strong></span>: This insert displays the invoice data and its legal status.</p>
  </li>
  <li>
	<p><span class="bold"><strong>Readable</strong></span>: This insert displays the invoice readable.</p>
  </li>
  <li>
	<p><span class="bold"><strong>Lifecycle</strong></span> : This insert displays the invoice's lifecycle statuses and the date they were generated. When a file is attached to the status, users can click on the status to display it.</p>
  </li>
  <li>
	<p><span class="bold"><strong>Traceability</strong></span>: This tab displays actions and edits performed on the invoice.</p>
  </li>
  <li>
	<p><span class="bold"><strong>Declaration</strong></span> : This tab allows you to display and download the e-reporting data you reported to the Public Billing Portal (PPF) in XML format.</p>
  </li>
  <li>
	<p><span class="bold"><strong>Compliance</strong></span>: The tab lists the version of the controls applied to the invoice, the date they were applied, their status, and, the cause of any errors.</p>
  </li>
  <li>
	<p><span class="bold"><strong>Attached files</strong></span>: The tab allows you to display and download documents that provide additional information to the invoice.</p>
  </li>
  <li>
	<p><span class="bold"><strong>Other files</strong></span>: This tab allows you to display and download the technical files received and issued (the readable, the invoice in canonical format, files from the customer's ERP, etc.).</p>
  </li>
  <li>
	<p><span class="bold"><strong>Credit note and rectification</strong></span> : This tab allows you to display and download the documents associated with the invoice, such as credit note invoices, progress invoices, and correction invoices.</p>
  </li>
</ul>
          
<div style="background: rgb(238, 238, 238); border: 1px solid rgb(204, 204, 204); padding: 5px 10px; margin: 5px 10px;">
	<p>To display the cockpit compliance to portal users, <a href="https://helpcenter-gis.generixgroup.com/en/index-en.html#invoice-portlet" target="_blank">go to the Invoice portlet</a>. On the <span class="guilabel" style="font-weight:bold;">Invoice viewer field</span>, select the Invoice Compliance portlet you have configured.
	</p>
</div>
        
        
<h1>Description</h1>

<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		<p>Field</p>
	  </th>
	  <th>
		<p>Description</p>
	  </th>
	  <th>
		<p>Mandatory</p>
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Title</span>
		</p>
	  </td>
	  <td>
		[@2pttitle]
	  </td>
	  <td>
		<p>Mandatory</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Description</span>
		</p>
	  </td>
	  <td>
		[@2ptdesc]	
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
  </tbody>
</table>

       
        
          
<h1>Display</h1>
          
	<table frame="border" rules="all" style="margin:25px;">
	  <thead>
		<tr>
		  <th>
			<p>Field</p>
		  </th>
		  <th>
			<p>Description</p>
		  </th>
		  <th>
			<p>Mandatory</p>
		  </th>
		</tr>
	  </thead>
	  <tbody>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Checkbox</span>
			</p>
		  </td>
		  <td>
			[@2ptdischk]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Fieldname</span>
			</p>
		  </td>
		  <td>
			[@2ptdisfie]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Column Title</span>
			</p>
		  </td>
		  <td>
			[@2ptdiscol]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Style</span>
			</p>
		  </td>
		  <td>
			[@2ptdisty]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">List</span>
			</p>
		  </td>
		  <td>
			[@2ptdislist]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Arrow</span>
			</p>
		  </td>
		  <td>
			[@2ptdisarr]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	  </tbody>
	</table>
          
        
<h1>Invoice control portlet</h1>
             
          
	<table frame="border" rules="all" style="margin:25px;">
	  <thead>
		<tr>
		  <th>
			<p>Field</p>
		  </th>
		  <th>
			<p>Description</p>
		  </th>
		  <th>
			<p>Mandatory</p>
		  </th>
		</tr>
	  </thead>
	  <tbody>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Portlet</span>
			</p>
		  </td>
		  <td>
			<div style="background: rgb(252, 232, 218); border: 1px solid rgb(247, 185, 145); padding: 5px 10px; margin: 5px 10px;">
				<strong>Prerequisite</strong>
				<p>Have created at least one Invoice Control portlet.</p>
			</div>
			<p>Select an Invoice Control portlet to allow users to navigate from this portlet to the information displayed on the Invoice Control portlet.</p>
			<p>On the portal, the navigation button is above the Compliance cockpit sidebar and displays the label entered in the <span class="guilabel" style="font-weight:bold;">Title</span> field of the Invoice Control portlet.</p>
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	  </tbody>
	</table>
          
        
       
<h1>Post-validation process</h1>
              
	<table frame="border" rules="all" style="margin:25px;">
	  <thead>
		<tr>
		  <th>
			<p>Field</p>
		  </th>
		  <th>
			<p>Description</p>
		  </th>
		  <th>
			<p>Mandatory</p>
		  </th>
		</tr>
	  </thead>
	  <tbody>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Post-validation process</span>
			</p>
		  </td>
		  <td>
			[@2ptpostvalidbp]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	  </tbody>
	</table>
          
        
        
<h1>Rights</h1>

<p>Rights available in the General tab.</p>

<table frame="border" rules="all" style="margin:25px;">
	<thead>
		<tr>
			<th>
			<p>Name</p>
			</th>
			<th>
			<p>Right</p>
			</th>
			<th>
			<p>Description</p>
			</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
			<p>INVOICE: Attach</p>
			</td>
			<td>
			<p>Create</p>
			</td>
			<td>
			<p>Attach a file to an invoice from the Invoice, Invoice Compliance, and Invoice Control portlets.</p>
			</td>
		</tr>
	</tbody>
</table>

<p>Rights available in the Compliance tab.</p>
              
          
	<table frame="border" rules="all" style="margin:25px;">
	  <thead>
		<tr>
		  <th>
			<p>Name</p>
		  </th>
		  <th>
			<p>Right</p>
		  </th>
		  <th>
			<p>Description</p>
		  </th>
		</tr>
	  </thead>
	  <tbody>
		<tr>
		  <td>
			<p>Approve</p>
		  </td>
		  <td rowspan="6" class="td">
			<p>Read</p>
		  </td>
		  <td>
			<p>Display the <span class="guibutton" style="background-color:#CCD3D6; color:#002334; border-style:solid; border-width:1px; border-radius:4px; border-color:#002334; padding: 2px 4px;">Approve</span> button on the Invoice Compliance portlet to allow users to affect the invoice lifecycle.</p>
			<p>To display the button on the portal, the document must have the following properties:</p>
			  <ul>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">processing_way = RECEIVING</code>
				  </p>
				</li>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">stage = UPLOADED OR SENT OR RECEIVED OR AVAILABLE OR OPENED OR COMPLETED</code>
				  </p>
				</li>
			  </ul>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>Refuse</p>
		  </td>
		  <td>
			<p>Display the <span class="guibutton" style="background-color:#CCD3D6; color:#002334; border-style:solid; border-width:1px; border-radius:4px; border-color:#002334; padding: 2px 4px;">Refuse</span> button on the Invoice Compliance portlet to allow users to affect the invoice lifecycle.</p>
			<p>To display the button on the portal, the document must have the following properties:</p>
			  <ul>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">processing_way = RECEIVING</code>
				  </p>
				</li>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">stage = UPLOADED OR SENT OR RECEIVED OR AVAILABLE OR OPENED OR COMPLETED OR DISPUTED</code>
				  </p>
				</li>
			  </ul>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>Subrogate</p>
		  </td>
		  <td>
			<p>Display the <span class="guibutton" style="background-color:#CCD3D6; color:#002334; border-style:solid; border-width:1px; border-radius:4px; border-color:#002334; padding: 2px 4px;">Subrogate</span> button on the Invoice Compliance portlet to allow users to affect the invoice lifecycle.</p>
			<p>To display the button on the portal, the document must have the following properties:</p>
			  <ul>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">processing_way = SENDING</code>
				  </p>
				</li>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">stage = UPLOADED OR SENT OR RECEIVED OR AVAILABLE OR OPENED OR COMPLETED OR APPROVED OR APPROVED_PARTIALLY</code>
				  </p>
				</li>
			  </ul>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>Payment sent</p>
		  </td>
		  <td>
			<p>Display the <span class="guibutton" style="background-color:#CCD3D6; color:#002334; border-style:solid; border-width:1px; border-radius:4px; border-color:#002334; padding: 2px 4px;">Payment sent</span> button on the Invoice Compliance portlet to allow users to affect the invoice lifecycle.</p>
			<p>To display the button on the portal, the document must have the following properties:</p>
			
			  <ul>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">processing_way = RECEIVING</code>
				  </p>
				</li>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">stage = UPLOADED OR SENT OR RECEIVED OR AVAILABLE OR OPENED OR COMPLETED OR APPROVED OR APPROVED_PARTIALLY</code>
				  </p>
				</li>
			  </ul>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>CDV: Complete</p>
		  </td>
		  <td>
			<p>Display the <span class="guibutton" style="background-color:#CCD3D6; color:#002334; border-style:solid; border-width:1px; border-radius:4px; border-color:#002334; padding: 2px 4px;">Complete</span> button on the Invoice Compliance portlet to allow users to attach files to the invoice.</p>
			<p>When users attach files to the invoice with this button, the files are then available under the <span class="guilabel" style="font-weight:bold;">Attached files</span> tab.</p>
			<p>To display the button on the portal, the document must have the following properties:</p>
			
			  <ul>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">processing_way = SENDING</code>
				  </p>
				</li>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">stage = SUSPENDED</code>
				  </p>
				</li>
			  </ul>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>Cash in</p>
		  </td>
		  <td>
			<p>Display the <span class="guibutton" style="background-color:#CCD3D6; color:#002334; border-style:solid; border-width:1px; border-radius:4px; border-color:#002334; padding: 2px 4px;">Cash in</span> button on the Invoice Compliance portlet to allow users to affect the invoice lifecycle.</p>
			<p>To display the button on the portal, the document must have the following properties:</p>
			  <ul>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">processing_way = SENDING</code>
				  </p>
				</li>
				<li>
				  <p>
					<code class="code" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;">stage = UPLOADED OR SENT OR RECEIVED OR AVAILABLE OR OPENED OR COMPLETED OR APPROVED OR APPROVED_PARTIALLY OR PAYMENT_SENT</code>
				  </p>
				</li>
			  </ul>
		  </td>
		</tr>
	  </tbody>
	</table>
          



