<ui:composition xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets" xmlns:pt="http://xmlns.jcp.org/jsf/passthrought" 
	xmlns:p="http://primefaces.org/ui"
	xmlns:b="http://byzaneo.com/ui"
	xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx" 
	template="/resources/template/#{gnxSessionHandler.template}/default.layout.xhtml">
	
	<ui:param name="portalFormId" value="@([id$=portalPnl])"></ui:param>
	<!-- TITLE -->
	<ui:define name="title">
		<gnx:instanceTitle id="instanceTitle" instance="#{gnxPortalISHandler.instance}" service="portal" />
	</ui:define>
	<!-- SIDEBAR -->
	<ui:define name="sidebar">
		<gnx:instanceSidebar id="instanceMenu" instance="#{gnxPortalISHandler.instance}" service="portal" />
	</ui:define>
	<!-- CONTENT -->
	
	<ui:define name="content">
		<h:form id="portalForm" pt:role="form" styleClass="form-horizontal">
			<!-- * HEADER * -->
			<p:panelGrid>
				<p:row>
					<p:column styleClass="left">
						<h:outputText value="" styleClass="title1" />
					</p:column>
					<p:column styleClass="right">
						<p:commandButton icon="fa fa-wrench" value="#{labels.configuration}" title="#{labels.configuration}" type="button" onclick="PF('wPtlConfDlg').show()" styleClass="mls"  />
						<p:commandButton icon="fa fa-floppy-o" value="#{labels.save}" actionListener="#{gnxPortalISHandler.onSave}" process="@this @(.psPortalPnl)" update="@(.psPortalPnl)" styleClass="mls" />
					</p:column>
				</p:row>
			</p:panelGrid>
			<!-- * CONTENT * -->
			<p:outputPanel id="portalPnl" styleClass="psPortalPnl" rendered="#{gnxPortalISHandler.instance!=null}">
				<p:layout style="min-width:800px;height:750px;" id="tskLayout" >
					<!-- PAGE TREE PANEL -->
					<p:layoutUnit position="west" size="250" resizable="true">
						<p:outputPanel id="ptlPagesPnl" styleClass="psPtlPagesPnl">
							<h:panelGrid columns="2" columnClasses="left,right">
								<h:outputText value="#{labels.pages}" styleClass="title2" />
								<p:outputPanel id="addPageButtons">
								  <p:commandButton id="ptlAddIconPage" icon="fa fa-plus-square" title="#{labels.add_icon_page}" 
	        				         actionListener="#{gnxPortalISHandler.onAddIconPage}"
	        						 process="@this" update="ptlPageTre @([id$=pageConfDlg])" oncomplete="PF('wPageLayoutDlg').show()">
	        				      </p:commandButton>
	        					  <p:commandButton id="ptlAddPage" icon="fa fa-plus-square-o" title="#{labels.add_page}" 
	        						 actionListener="#{gnxPortalISHandler.onAddPage}" process="@this" update="ptlPageTre @([id$=pageConfDlg])" 
	        						 oncomplete="PF('wPageLayoutDlg').show()"/>
	        				    </p:outputPanel>
							</h:panelGrid>
							<p:tree id="ptlPageTre" value="#{gnxPortalISHandler.root}" var="node" cache="true"
								selectionMode="single" selection="#{gnxPortalISHandler.selectedNode}"
								style="width:98%;border:none;" draggable="true" droppable="true" dragdropScope="dsPageTree">
								<p:ajax event="dragdrop" update="@none" listener="#{gnxPortalISHandler.onDragAndDropPageNode}" />  
								<p:ajax event="select" update="@(.psPtlPagePnl) @(.wPageConfDlg)" listener="#{gnxPortalISHandler.onSelectPageNode}" />
								<p:treeNode> 
									<h:outputText value="#{gnxHandler.label(node.name, gnxPortalISHandler.defaultLanguage)}" style="font-size:1.2em;"/>
									<h:outputText styleClass="#{node.toBeDisplayedOption ? 'fa fa-eye-slash fa-pull-right' : ''}" style="font-size:1.2em;margin-top: 6px;"/> 
									<h:outputText styleClass="#{node.fontAwesomeIcon} fa-pull-right" style="font-size:1.2em;margin-top: 6px;"/> 
									<h:outputText styleClass="#{node.submenuDisplayWarning ? 'fa fa-exclamation-triangle fa-pull-right' : ''}" title="#{labels.page_submenu_warning}" style="font-size:1.2em;margin-top: 6px;"/>
								</p:treeNode>
							</p:tree>
							<p:contextMenu for="ptlPageTre">
						    	<p:menuitem icon="fa fa-plus-square-o" value="#{comlbls.add}" actionListener="#{gnxPortalISHandler.onAddChildPage}" 
						    		process="@this ptlPageTre" update="ptlPageTre" oncomplete="PF('wPageLayoutDlg').show()" />
						    	<p:menuitem icon="fa fa-trash-o" value="#{comlbls.delete}" actionListener="#{gnxPortalISHandler.onRemovePage}" 
						    		process="@this ptlPageTre" update="ptlPageTre ptlPagePnl"
						    		onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }" />
							</p:contextMenu>
						</p:outputPanel> 
				    </p:layoutUnit>
					<!-- PAGE PANEL -->
					<p:layoutUnit position="center" style="overflow-y:hidden;">  
						<p:outputPanel id="ptlPagePnl" styleClass="portal-edition psPtlPagePnl">
							<p:outputPanel id="ptlNoPagePnl" rendered="#{gnxPortalISHandler.page==null}" 
								style="margin-top:50px;font-size:18pt;font-weight:bold;color:#999;text-align:center;">
								<h:outputText value="#{labels.page_noselection}"/>
							</p:outputPanel>
							<p:outputPanel id="ptlPageEditPnl" rendered="#{gnxPortalISHandler.page!=null}">
								<!-- PAGE HEADER -->
								<p:outputPanel styleClass="row" style="padding: 5px 10px 10px 15px;">
									<p:outputPanel styleClass="col-sm-4" style="width:30%;">
										<p:inputText id="ppgNameIpt" value="#{gnxPortalISHandler.page.name}" validator="xssValidator" required="true" styleClass="form-control title1" />
										<b:labelSet for="ppgNameIpt" locales="#{gnxPortalISHandler.languages}" defaultLocale="#{gnxPortalISHandler.defaultLanguage}" localeSelected="#{gnxPortalISHandler.language}" />
									</p:outputPanel>
									<p:outputPanel styleClass="col-sm-8 right" style="width:70%;">
									   <!-- Icon for page -->
	            	                    <p:outputPanel id="pageIcon" styleClass="form-group row col-sm-8" rendered="#{gnxPortalISHandler.page.isShowFontAwesomeIcon()}"> 
						                   <p:outputLabel for="fontAwesomeId" value="#{doctsklbls.icon}" styleClass="col-sm-3 control-label"/>
						                   <p:outputPanel styleClass="col-sm-8">
						                    <p:inputText id="fontAwesomeId" value="#{gnxPortalISHandler.page.fontAwesomeIcon}" validator="xssValidator" size="30">
						                       <f:validateRegex pattern="^fa fa-[a-z-]*[a-z]$"/>
						                    </p:inputText>
						                    <h:outputText id="fontInfoId" styleClass="fa fa fa-info-circle" style="margin-left:5px;font-size: 1.5em"/>
						                    <p:tooltip for="fontInfoId" hideDelay="1000">
						                     <h:outputLink value="https://fontawesome.com/v4.7.0/icons/" target="_blank">
						                       <h:outputText value="#{doctsklbls.iconTooltip}" />
						                     </h:outputLink>
						                    </p:tooltip>
						                    <p:watermark for="fontAwesomeId" value="#{labels.pageIconPlaceholder}" />
					                       </p:outputPanel>
				                        </p:outputPanel>
											<span>
												<p:outputLabel style="margin-right: 10px" value="#{labels.angular_page}"/>
												<p:selectBooleanCheckbox  style=" top: 2px;  position: relative" value="#{gnxPortalISHandler.page.displayPageOnAngular}" />
											</span>
								    	<p:commandButton icon="fa fa-plus-square-o" title="#{labels.add_child_page}"
								    		process="@this ptlPageTre" update="ptlPageTre" oncomplete="PF('wPageLayoutDlg').show()" 
								    		actionListener="#{gnxPortalISHandler.onAddChildPage(gnxPortalISHandler.page)}" 
								    		rendered="#{not gnxPortalISHandler.isIconParentPage(gnxPortalISHandler.page)}"
								    		styleClass="mls" />

										<p:commandButton icon="fa fa-upload"
								    	    title="#{gnxPortalISHandler.hasShadowPortal(gnxPortalISHandler.page) ? labels.error_export_shadow_portlet : labels.export}" 
								    		actionListener="#{gnxPortalISHandler.onExportPage(gnxPortalISHandler.page)}" 
								    		rendered="#{not gnxPortalISHandler.isExportable() and not gnxPortalISHandler.hasCollectionRtePortlet(gnxPortalISHandler.page)}"
								    		disabled="#{gnxPortalISHandler.hasShadowPortal(gnxPortalISHandler.page)}"
								    		process="@this ptlPageTre" ajax="false"
								    		styleClass="mls"/>
								    	<p:commandButton icon="fa fa-upload" title="#{labels.export}" 
								    		actionListener="#{gnxPortalISHandler.onExportPageWithRTECollection(gnxPortalISHandler.page)}" 
								    		rendered="#{not gnxPortalISHandler.isExportable() and gnxPortalISHandler.hasCollectionRtePortlet(gnxPortalISHandler.page)}"
								    		process="@this ptlPageTre"
								    		styleClass="mls"/> 
								    	<p:commandButton icon="fa fa-download" title="#{labels.import}" 
								    		oncomplete="if (!args.validationFailed){PF('wInstImportDlg').show()}"
									    	rendered="#{gnxPortalISHandler.isExportable()}" update="@(.psPortalPnl)"
								    		styleClass="mls">
											<f:setPropertyActionListener target="#{gnxInstanceISHandler.instance}" value="#{instance}" />
										</p:commandButton>
								    	<p:commandButton icon="fa fa-trash-o" title="#{comlbls.delete}" 
								    		process="@this ptlPageTre" update="ptlPageTre ptlPagePnl"
								    		actionListener="#{gnxPortalISHandler.onRemovePage(gnxPortalISHandler.page)}" 
								    		onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }" 
								    		styleClass="mls" />
										<p:commandButton id="ppgPermBtn" icon="fa fa-shield" title="#{labels.security}"
											actionListener="#{gnxPortalISHandler.onOpenPermissions}" process="@this" update="@none" styleClass="mls" />
										<p:commandButton id="ppgConfBtn" icon="fa fa-wrench" title="#{labels.configuration_page}" oncomplete="PF('wPageConfDlg').show()" styleClass="mls"  process="@this" />
										<p:commandButton id="ppgLayoutBtn" icon="fa fa-th-large" title="#{labels.layout}" type="button" onclick="PF('wPageLayoutDlg').show()" styleClass="mls"  />
									</p:outputPanel>
								</p:outputPanel>								
								<!-- LAYOUTS -->
								<ui:repeat value="#{gnxPortalISHandler.page.layouts}" var="currentLayout" varStatus="layoutStatus">
									<p:outputPanel  style="display:grid;  padding: 5px 10px 10px 15px;">
									<ui:include src="portal/layout-none.inc.xhtml">
										<ui:param name="layout"   value="#{currentLayout}" />
										<ui:param name="layoutIndex" value="#{layoutStatus.index}"/> 
									</ui:include>
									<ui:include src="portal/layout-empty.inc.xhtml" >
										<ui:param name="layout"   value="#{currentLayout}" />
										<ui:param name="layoutIndex" value="#{layoutStatus.index}"/> 
									</ui:include>
									<ui:include src="portal/layout-columns.inc.xhtml" >
										<ui:param name="layout"   value="#{currentLayout}" />
										<ui:param name="layoutIndex" value="#{layoutStatus.index}"/> 
									</ui:include>
									<ui:include src="portal/layout-grid.inc.xhtml" >
										<ui:param name="layout"   value="#{currentLayout}" />
										<ui:param name="layoutIndex" value="#{layoutStatus.index}"/> 
									</ui:include>
									<p:spacer width="15px" />
									<p:separator style="width:100%;height:5px" />
									</p:outputPanel>
								</ui:repeat>
							</p:outputPanel>
				        </p:outputPanel>  
				    </p:layoutUnit>
				</p:layout>
				<!-- PAGE CONF DIALOG -->
				
				<p:dialog id="pageConfDlg" widgetVar="wPageConfDlg" styleClass="wPageConfDlg" height="400" width="700" header="#{labels.configuration_page}" modal="true" >
					<p:fieldset legend="#{labels.configuration}" toggleable="true">
						<p:panelGrid id="pageConfGrd" styleClass="left" rendered="#{gnxPortalISHandler.page!=null}">
							<p:outputPanel  styleClass="searchAndOptionsOutput" class="row" 
				 				style="margin-bottom: 15px ! important;" >
								<p:outputPanel class="col-xs-3">
									<p:outputLabel value="#{labels.style}" />
								</p:outputPanel>
								<p:outputPanel class="col-xs-6">
									<p:inputText value="#{gnxPortalISHandler.page.style}" validator="xssValidator" />
								</p:outputPanel>
							</p:outputPanel>
							<p:outputPanel  styleClass="searchAndOptionsOutput" class="row" 
				 				style="margin-bottom: 15px ! important;" >
								<p:outputPanel class="col-xs-3">
									<p:outputLabel value="#{labels.styleClass}" />
								</p:outputPanel>
								<p:outputPanel class="col-xs-6">
									<p:inputText value="#{gnxPortalISHandler.page.styleClass}" validator="xssValidator" />
								</p:outputPanel>
							</p:outputPanel>
							<p:outputPanel  styleClass="searchAndOptionsOutput" class="row"
							    style="margin-bottom: 15px ! important;">
							   <p:outputPanel class="col-xs-4">
				 		         <p:outputLabel value="#{labels.page_display_option}"/>
				 		       </p:outputPanel>
				 		       <p:outputPanel class="col-xs-6">
							     <p:selectBooleanCheckbox value="#{gnxPortalISHandler.page.toBeDisplayedOption}" />
							   </p:outputPanel>
                            </p:outputPanel>    
                        </p:panelGrid>
					</p:fieldset>
					<p:fieldset legend="#{labels.query}" toggleable="true">
						<p:outputPanel class="col-xs-3">
							<p:outputLabel value="#{labels.query}" />
						</p:outputPanel>
						<p:outputPanel class="col-xs-8"  rendered="#{gnxPortalISHandler.page!=null}">
							<b:query id="query" value="#{gnxPortalISHandler.page.query}" model="#{gnxPortalISHandler.queryModel}" 
							completeMethod="#{gnxPortalISHandler.onCompleteQuery}" widgetVar="wBqlQuery" helpRendered="false" />
						</p:outputPanel>
						
					</p:fieldset>
					<f:facet name="footer">
						<p:commandButton icon="ui-icon-check" value="OK" process="@this pageConfDlg" oncomplete="PF('wPageConfDlg').hide()"  />
					</f:facet>
				</p:dialog>
				
				<!-- PORTAL CONF DIALOG -->
				<p:dialog id="ptlConfDlg" widgetVar="wPtlConfDlg" width="400" header="#{labels.configuration_portal}" dynamic="true" modal="true">
					<p:panelGrid id="ptlConfGrd" rendered="#{gnxPortalISHandler.portal!=null}">
						<p:row>
							<p:column styleClass="input-panel-label"><p:outputLabel value="#{labels.style}" for="ptlStyleIpt" /></p:column>
							<p:column styleClass="input-panel-value"><p:inputText id="ptlStyleIpt" value="#{gnxPortalISHandler.portal.style}" validator="xssValidator" /></p:column>
						</p:row>
						<p:row>
							<p:column styleClass="input-panel-label"><p:outputLabel value="#{labels.styleClass}" for="ptlStyleClassIpt" /></p:column>
							<p:column styleClass="input-panel-value"><p:inputText id="ptlStyleClassIpt" value="#{gnxPortalISHandler.portal.styleClass}" validator="xssValidator" /></p:column>
						</p:row>
					</p:panelGrid>
					<f:facet name="footer">
						<p:commandButton icon="ui-icon-check" value="OK" process="@this ptlConfGrd" oncomplete="PF('wPtlConfDlg').hide()" />
					</f:facet>
				</p:dialog>
				<!-- PAGE LAYOUT DIALOG -->
				<p:dialog id="pageLayoutDlg" widgetVar="wPageLayoutDlg" header="#{labels.layout}" closable="false" closeOnEscape="false" dynamic="true" modal="true" fitViewport="true" resizable="false">
					<ui:include src="portal/layouts.inc.xhtml" />
					<p:panelGrid columns="1" styleClass="right" rendered="#{gnxPortalISHandler.portal!=null}">
						<p:commandButton icon="ui-icon-cancel" value="#{labels.cancel}" type="button" onclick="PF('wPageLayoutDlg').hide()" />
					</p:panelGrid>                    
				</p:dialog>
			</p:outputPanel>
			<!-- PERMISSION DIALOG 
			<p:outputPanel id="ppgPermissionPnl">
				<gnx:permissionDialog id="ppgPermissionDlg" />
			</p:outputPanel> -->
			<!--  IMPORT  DIALOG -->
			<p:dialog id="instImportDlg" header="#{labels.import}" modal="true" width="500" height="80" widgetVar="wInstImportDlg">
				<p:fileUpload fileUploadListener="#{gnxPortalISHandler.checkImportedPageLinks}" mode="advanced" update="-messages @form" auto="true" sizeLimit="10000000"
					label="#{labels.choose}" allowTypes="/(\.|\/)(json)$/" oncomplete="PF('wInstImportDlg').hide(); if (args.validationFailed){PF('wImportPageWarningConfirm').show();}" />
			</p:dialog>

			<!-- IMPORT PAGE WARNING CONFIRM DIALOG -->
			<p:dialog id="importPageWarningConfirm" resizable="false" modal="true" global="true"
					  header="#{labels.confirmationTitle}"
					  width="500px"
					  onShow="if(typeof centerDialogToWindow === 'function') {centerDialogToWindow($('.dlgImportPageWarningConfirm'));}"
					  styleClass="dlgImportPageWarningConfirm responsive-dialog"
					  widgetVar="wImportPageWarningConfirm" >
				<p:outputPanel styleClass="container-fluid form-group">
					<h:outputText value="#{labels.importPageWarningText}"/>
				</p:outputPanel>
				<p:outputPanel styleClass="ui-dialog-footer text-right">
					<p:outputPanel styleClass="validationButtonsDlg">
						<p:commandButton value="#{labels.yes}"
										 process="@this"
										 onclick="PF('wImportPageWarningConfirm').hide();"
										 actionListener="#{gnxPortalISHandler.onImportPage()}"
										 styleClass="btn btn-primary"/>
						<p:commandButton value="#{labels.no}"
										 process="@this"
										 onclick="PF('wImportPageWarningConfirm').hide();"
										 styleClass="btn btn-secondary"/>
					</p:outputPanel>
				</p:outputPanel>
			</p:dialog>
		</h:form>
	</ui:define>
</ui:composition>
