jQuery(document).ready(function(a){"use strict";var unishopBackLocale=function(){if(Byzaneo.locales&&Byzaneo.locales['Unishop']){var localeSettings=Byzaneo.getLocaleSettings(Byzaneo.locales['Unishop'],navigator.language);if(localeSettings==null){localeSettings=Byzaneo.getLocaleSettings(Byzaneo.locales['Unishop'],'en_US');}if(localeSettings){return localeSettings['back'];}}return'Back';};var backLabel='<li class="back-btn"><a href="#">'+unishopBackLocale()+'</a></li>';function b(b){var c=a("body"),d=a(b.target).attr("href");a(d).addClass("active"),console.log(d),c.css("overflow","hidden"),c.addClass("offcanvas-open"),b.preventDefault()}function c(){var b=a("body");b.removeClass("offcanvas-open"),setTimeout(function(){b.css("overflow","visible"),a(".offcanvas-container").removeClass("active")},450)}function d(){k.parent().removeClass("expanded")}function e(b){var c=b.item.index,d=a(".owl-item").eq(c).find("[data-hash]").attr("data-hash");a(".product-thumbnails li").removeClass("active"),a('[href="#'+d+'"]').parent().addClass("active"),a(".gallery-wrapper .gallery-item").removeClass("active"),a('[data-hash="'+d+'"]').parent().addClass("active")}(function(){if("number"==typeof window.innerWidth)return window.innerWidth>document.documentElement.clientWidth;var a,b=document.documentElement||document.body;void 0!==b.currentStyle&&(a=b.currentStyle.overflow),a=a||window.getComputedStyle(b,"").overflow;var c;void 0!==b.currentStyle&&(c=b.currentStyle.overflowY),c=c||window.getComputedStyle(b,"").overflowY;var d=b.scrollHeight>b.clientHeight,e=/^(visible|auto)$/.test(a)||/^(visible|auto)$/.test(c),f="scroll"===a||"scroll"===c;return d&&e||f})()&&a("body").addClass("hasScrollbar"),a('a[href="#"]').on("click",function(a){a.preventDefault()}),function(){var b=a("body"),c=a(".navbar-sticky"),d=a(".topbar").outerHeight(),e=c.outerHeight();c.length&&a(window).on("scroll",function(){a(this).scrollTop()>d?(c.addClass("navbar-stuck"),c.hasClass("navbar-ghost")||b.css("padding-top",e)):(c.removeClass("navbar-stuck"),b.css("padding-top",0))})}(),function(b,c,d,e){a(b).on("click",function(){a(e).addClass("search-visible"),setTimeout(function(){a(e+" > input").focus()},200)}),a(c).on("click",function(){a(e).removeClass("search-visible")}),a(d).on("click",function(){a(e+" > input").val(""),setTimeout(function(){a(e+" > input").focus()},200)})}(".toolbar .tools .search",".close-search",".clear-search",".site-search"),a(".lang-currency-switcher").on("click",function(){a(this).parent().addClass("show"),a(this).parent().find(".dropdown-menu").addClass("show")}),a(document).on("click",function(b){a(b.target).closest(".lang-currency-switcher-wrap").length||(a(".lang-currency-switcher-wrap").removeClass("show"),a(".lang-currency-switcher-wrap .dropdown-menu").removeClass("show"))}),a('[data-toggle="offcanvas"]').on("click",b),a(".site-backdrop").on("click",c);var f=a(".offcanvas-menu .menu").height();a(".offcanvas-menu .offcanvas-submenu").each(function(){a(this).prepend(backLabel)});var g=a(".has-children .sub-menu-toggle");a(".offcanvas-menu .offcanvas-submenu .back-btn").on("click",function(b){var c=this,d=a(c).parent(),e=a(c).parent().parent().siblings().parent(),g=a(c).parents(".menu");d.removeClass("in-view"),e.removeClass("off-view"),"menu"===e.attr("class")?g.css("height",f):g.css("height",e.height()),b.preventDefault()}),g.on("click",function(b){var c=this,d=a(c).parent().parent().parent(),e=a(c).parents(".menu");return d.addClass("off-view"),a(c).parent().parent().find("> .offcanvas-submenu").addClass("in-view"),e.css("height",a(c).parent().parent().find("> .offcanvas-submenu").height()),b.preventDefault(),!1});var h=a(".scroll-to-top-btn");if(h.length>0&&(a(window).on("scroll",function(){a(this).scrollTop()>600?h.addClass("visible"):h.removeClass("visible")}),h.on("click",function(b){b.preventDefault(),a("html").velocity("scroll",{offset:0,duration:1200,easing:"easeOutExpo",mobileHA:!1})})),a(document).on("click",".scroll-to",function(b){var c=a(this).attr("href");if("#"===c)return!1;var d=a(c);if(d.length>0){var e=d.data("offset-top")||70;a("html").velocity("scroll",{offset:a(this.hash).offset().top-e,duration:1e3,easing:"easeOutExpo",mobileHA:!1})}b.preventDefault()}),function(b){b.each(function(){var b=a(this),c=b.data("filter-list"),d=b.find("input[type=text]"),e=b.find("input[type=radio]"),f=a(c).find(".list-group-item");d.keyup(function(){var b=d.val();f.each(function(){0==a(this).text().toLowerCase().indexOf(b.toLowerCase())?a(this).show():a(this).hide()})}),e.on("click",function(b){var c=a(this).val();"all"!==c?(f.hide(),a("[data-filter-item="+c+"]").show()):f.show()})})}(a("[data-filter-list]")),function(b,c){b.each(function(){var b=a(this),d=a(this).data("date-time");(c||b).downCount({date:d,offset:10})})}(a(".countdown")),a("[data-toast]").on("click",function(){var b=a(this),c=b.data("toast-type"),d=b.data("toast-icon"),e=b.data("toast-position"),f=b.data("toast-title"),g=b.data("toast-message"),h="";switch(e){case"topRight":h={class:"iziToast-"+c||"",title:f||"Title",message:g||"toast message",animateInside:!1,position:"topRight",progressBar:!1,icon:d,timeout:3200,transitionIn:"fadeInLeft",transitionOut:"fadeOut",transitionInMobile:"fadeIn",transitionOutMobile:"fadeOut"};break;case"bottomRight":h={class:"iziToast-"+c||"",title:f||"Title",message:g||"toast message",animateInside:!1,position:"bottomRight",progressBar:!1,icon:d,timeout:3200,transitionIn:"fadeInLeft",transitionOut:"fadeOut",transitionInMobile:"fadeIn",transitionOutMobile:"fadeOut"};break;case"topLeft":h={class:"iziToast-"+c||"",title:f||"Title",message:g||"toast message",animateInside:!1,position:"topLeft",progressBar:!1,icon:d,timeout:3200,transitionIn:"fadeInRight",transitionOut:"fadeOut",transitionInMobile:"fadeIn",transitionOutMobile:"fadeOut"};break;case"bottomLeft":h={class:"iziToast-"+c||"",title:f||"Title",message:g||"toast message",animateInside:!1,position:"bottomLeft",progressBar:!1,icon:d,timeout:3200,transitionIn:"fadeInRight",transitionOut:"fadeOut",transitionInMobile:"fadeIn",transitionOutMobile:"fadeOut"};break;case"topCenter":h={class:"iziToast-"+c||"",title:f||"Title",message:g||"toast message",animateInside:!1,position:"topCenter",progressBar:!1,icon:d,timeout:3200,transitionIn:"fadeInDown",transitionOut:"fadeOut",transitionInMobile:"fadeIn",transitionOutMobile:"fadeOut"};break;case"bottomCenter":h={class:"iziToast-"+c||"",title:f||"Title",message:g||"toast message",animateInside:!1,position:"bottomCenter",progressBar:!1,icon:d,timeout:3200,transitionIn:"fadeInUp",transitionOut:"fadeOut",transitionInMobile:"fadeIn",transitionOutMobile:"fadeOut"};break;default:h={class:"iziToast-"+c||"",title:f||"Title",message:g||"toast message",animateInside:!1,position:"topRight",progressBar:!1,icon:d,timeout:3200,transitionIn:"fadeInLeft",transitionOut:"fadeOut",transitionInMobile:"fadeIn",transitionOutMobile:"fadeOut"}}iziToast.show(h)}),a(".btn-wishlist").on("click",function(){var b=a(this).data("iteration")||1,c={title:"Product",animateInside:!1,position:"topRight",progressBar:!1,timeout:3200,transitionIn:"fadeInLeft",transitionOut:"fadeOut",transitionInMobile:"fadeIn",transitionOutMobile:"fadeOut"};switch(b){case 1:a(this).addClass("active"),c.class="iziToast-info",c.message="added to your wishlist!",c.icon="icon-bell";break;case 2:a(this).removeClass("active"),c.class="iziToast-danger",c.message="removed from your wishlist!",c.icon="icon-ban"}iziToast.show(c),b++,b>2&&(b=1),a(this).data("iteration",b)}),a(".isotope-grid").length)var i=a(".isotope-grid").imagesLoaded(function(){i.isotope({itemSelector:".grid-item",transitionDuration:"0.7s",masonry:{columnWidth:".grid-sizer",gutter:".gutter-sizer"}})});if(a(".filter-grid").length>0){var j=a(".filter-grid");a(".nav-pills").on("click","a",function(b){b.preventDefault(),a(".nav-pills a").removeClass("active"),a(this).addClass("active");var c=a(this).attr("data-filter");j.isotope({filter:c})})}var k=a(".widget-categories .has-children > a");k.on("click",function(b){a(b.target).parent().is(".expanded")?d():(d(),a(this).parent().addClass("expanded"))}),a('[data-toggle="tooltip"]').tooltip(),a('[data-toggle="popover"]').popover();var l=document.querySelector(".ui-range-slider");if(void 0!==l&&null!==l){var m=parseInt(l.parentNode.getAttribute("data-start-min"),10),n=parseInt(l.parentNode.getAttribute("data-start-max"),10),o=parseInt(l.parentNode.getAttribute("data-min"),10),p=parseInt(l.parentNode.getAttribute("data-max"),10),q=parseInt(l.parentNode.getAttribute("data-step"),10),r=document.querySelector(".ui-range-value-min span"),s=document.querySelector(".ui-range-value-max span"),t=document.querySelector(".ui-range-value-min input"),u=document.querySelector(".ui-range-value-max input");noUiSlider.create(l,{start:[m,n],connect:!0,step:q,range:{min:o,max:p}}),l.noUiSlider.on("update",function(a,b){var c=a[b];b?(s.innerHTML=Math.round(c),u.value=Math.round(c)):(r.innerHTML=Math.round(c),t.value=Math.round(c))})}var v=a(".interactive-credit-card");if(v.length&&v.card({form:".interactive-credit-card",container:".card-wrapper"}),a(".gallery-wrapper").length){!function(b){function c(a,b){return(" "+a.className+" ").indexOf(" "+b+" ")>-1}for(var d=function(b){for(var c,d,e,f,g=a(b).find(".gallery-item:not(.isotope-hidden)").get(),h=g.length,i=[],j=0;j<h;j++)c=g[j],1===c.nodeType&&(d=c.children[0],"video"==a(d).data("type")?f={html:a(d).data("video")}:(e=d.getAttribute("data-size").split("x"),f={src:d.getAttribute("href"),w:parseInt(e[0],10),h:parseInt(e[1],10)}),c.children.length>1&&(f.title=a(c).find(".caption").html()),d.children.length>0&&(f.msrc=d.children[0].getAttribute("src")),f.el=c,i.push(f));return i},e=function a(b,c){return b&&(c(b)?b:a(b.parentNode,c))},f=function(b){b=b||window.event,b.preventDefault?b.preventDefault():b.returnValue=!1;var d=b.target||b.srcElement,f=e(d,function(a){return c(a,"gallery-item")});if(f){for(var h,i=f.closest(".gallery-wrapper"),j=a(f.closest(".gallery-wrapper")).find(".gallery-item:not(.isotope-hidden)").get(),k=j.length,l=0,m=0;m<k;m++)if(1===j[m].nodeType){if(j[m]===f){h=l;break}l++}return h>=0&&g(h,i),!1}},g=function(b,c,e,f){var g,h,i,j=document.querySelectorAll(".pswp")[0];if(i=d(c),h={closeOnScroll:!1,galleryUID:c.getAttribute("data-pswp-uid"),getThumbBoundsFn:function(b){var c=i[b].el.getElementsByTagName("img")[0];if(a(c).length>0){var d=window.pageYOffset||document.documentElement.scrollTop,e=c.getBoundingClientRect();return{x:e.left,y:e.top+d,w:e.width}}}},f)if(h.galleryPIDs){for(var k=0;k<i.length;k++)if(i[k].pid==b){h.index=k;break}}else h.index=parseInt(b,10)-1;else h.index=parseInt(b,10);isNaN(h.index)||(e&&(h.showAnimationDuration=0),g=new PhotoSwipe(j,PhotoSwipeUI_Default,i,h),g.init(),g.listen("beforeChange",function(){var b=a(g.currItem.container);a(".pswp__video").removeClass("active");b.find(".pswp__video").addClass("active");a(".pswp__video").each(function(){a(this).hasClass("active")||a(this).attr("src",a(this).attr("src"))})}),g.listen("close",function(){a(".pswp__video").each(function(){a(this).attr("src",a(this).attr("src"))})}))},h=document.querySelectorAll(b),i=0,j=h.length;i<j;i++)h[i].setAttribute("data-pswp-uid",i+1),h[i].onclick=f;var k=function(){var a=window.location.hash.substring(1),b={};if(a.length<5)return b;for(var c=a.split("&"),d=0;d<c.length;d++)if(c[d]){var e=c[d].split("=");e.length<2||(b[e[0]]=e[1])}return b.gid&&(b.gid=parseInt(b.gid,10)),b}();k.pid&&k.gid&&g(k.pid,h[k.gid-1],!0,!0)}(".gallery-wrapper")}var w=a(".product-carousel");w.length&&w.owlCarousel({items:1,loop:!1,dots:!1,URLhashListener:!0,startPosition:"URLHash",onTranslate:e});var x=a(".google-map");x.length&&x.each(function(){var b=a(this).data("height"),c=a(this).data("address"),d=a(this).data("zoom"),e=a(this).data("disable-controls"),f=a(this).data("scrollwheel"),g=a(this).data("marker"),h=a(this).data("marker-title"),i=a(this).data("styles");a(this).height(b),a(this).gmap3({marker:{address:c,data:h,options:{icon:g},events:{mouseover:function(b,c,d){var e=a(this).gmap3("get"),f=a(this).gmap3({get:{name:"infowindow"}});f?(f.open(e,b),f.setContent(d.data)):a(this).gmap3({infowindow:{anchor:b,options:{content:d.data}}})},mouseout:function(){var b=a(this).gmap3({get:{name:"infowindow"}});b&&b.close()}}},map:{options:{zoom:d,disableDefaultUI:e,scrollwheel:f,styles:i}}})})});
(function() {

	var _after			= 1;
	var _afterThrow		= 2;
	var _afterFinally	= 3;
	var _before			= 4;
	var _around			= 5;
	var _intro			= 6;
	var _regexEnabled = true;
	var _arguments = 'arguments';
	var _undef = 'undefined';

	var getType = (function() {
	 
		var toString = Object.prototype.toString,
			toStrings = {},
			nodeTypes = { 1: 'element', 3: 'textnode', 9: 'document', 11: 'fragment' },
			types = 'Arguments Array Boolean Date Document Element Error Fragment Function NodeList Null Number Object RegExp String TextNode Undefined Window'.split(' ');
	 
		for (var i = types.length; i--; ) {
			var type = types[i], constructor = window[type];
			if (constructor) {
				try { toStrings[toString.call(new constructor)] = type.toLowerCase(); }
				catch (e) { }
			}
		}
	 
		return function(item) {
			return item == null && (item === undefined ? _undef : 'null') ||
				item.nodeType && nodeTypes[item.nodeType] ||
				typeof item.length == 'number' && (
					item.callee && _arguments ||
					item.alert && 'window' ||
					item.item && 'nodelist') ||
				toStrings[toString.call(item)];
		};
	 
	})();

	var isFunc = function(obj) { return getType(obj) == 'function'; };

	/**
	 * Private weaving function.
	 */
	var weaveOne = function(source, method, advice) {

		var old = source[method];

		// Work-around IE6/7 behavior on some native method that return object instances
		if (advice.type != _intro && !isFunc(old)) {
			var oldObject = old;
			old = function() {
				var code = arguments.length > 0 ? _arguments + '[0]' : '';

				for (var i=1;i<arguments.length;i++) {
					code += ',' + _arguments + '[' + i + ']';
				}

				return eval('oldObject(' + code + ');');
			};
		}

		var aspect;
		if (advice.type == _after || advice.type == _afterThrow || advice.type == _afterFinally)
			aspect = function() {
				var returnValue, exceptionThrown = null;

				try {
					returnValue = old.apply(this, arguments);
				} catch (e) {
					exceptionThrown = e;
				}

				if (advice.type == _after)
					if (exceptionThrown == null)
						returnValue = advice.value.apply(this, [returnValue, method]);
					else
						throw exceptionThrown;
				else if (advice.type == _afterThrow && exceptionThrown != null)
					returnValue = advice.value.apply(this, [exceptionThrown, method]);
				else if (advice.type == _afterFinally)
					returnValue = advice.value.apply(this, [returnValue, exceptionThrown, method]);

				return returnValue;
			};
		else if (advice.type == _before)
			aspect = function() {
				advice.value.apply(this, [arguments, method]);
				return old.apply(this, arguments);
			};
		else if (advice.type == _intro)
			aspect = function() {
				return advice.value.apply(this, arguments);
			};
		else if (advice.type == _around) {
			aspect = function() {
				var invocation = { object: this, args: Array.prototype.slice.call(arguments) };
				return advice.value.apply(invocation.object, [{ arguments: invocation.args, method: method, proceed : 
					function() {
						return old.apply(invocation.object, invocation.args);
					}
				}] );
			};
		}

		aspect.unweave = function() { 
			source[method] = old;
			pointcut = source = aspect = old = null;
		};

		source[method] = aspect;

		return aspect;

	};

	/**
	 * Private method search
	 */
	var search = function(source, pointcut, advice) {
		
		var methods = [];

		for (var method in source) {

			var item = null;

			// Ignore exceptions during method retrival
			try {
				item = source[method];
			}
			catch (e) { }

			if (item != null && method.match(pointcut.method) && isFunc(item))
				methods[methods.length] = { source: source, method: method, advice: advice };

		}

		return methods;
	};

	/**
	 * Private weaver and pointcut parser.
	 */
	var weave = function(pointcut, advice) {

		var source = typeof(pointcut.target.prototype) != _undef ? pointcut.target.prototype : pointcut.target;
		var advices = [];

		// If it's not an introduction and no method was found, try with regex...
		if (advice.type != _intro && typeof(source[pointcut.method]) == _undef) {

			// First try directly on target
			var methods = search(pointcut.target, pointcut, advice);

			// No method found, re-try directly on prototype
			if (methods.length == 0)
				methods = search(source, pointcut, advice);

			for (var i in methods)
				advices[advices.length] = weaveOne(methods[i].source, methods[i].method, methods[i].advice);

		} 
		else
		{
			// Return as an array of one element
			advices[0] = weaveOne(source, pointcut.method, advice);
		}

		return _regexEnabled ? advices : advices[0];

	};

	jQuery.aop = 
	{
		/**
		 * Creates an advice after the defined point-cut. The advice will be executed after the point-cut method 
		 * has completed execution successfully, and will receive one parameter with the result of the execution.
		 * This function returns an array of weaved aspects (Function).
		 *
		 * @example jQuery.aop.after( {target: window, method: 'MyGlobalMethod'}, function(result) { 
		 *                alert('Returned: ' + result); 
		 *                return result;
		 *          } );
		 * @result Array<Function>
		 *
		 * @example jQuery.aop.after( {target: String, method: 'indexOf'}, function(index) { 
		 *                alert('Result found at: ' + index + ' on:' + this); 
		 *                return index;
		 *          } );
		 * @result Array<Function>
		 *
		 * @name after
		 * @param Map pointcut Definition of the point-cut to apply the advice. A point-cut is the definition of the object/s and method/s to be weaved.
		 * @option Object target Target object to be weaved. 
		 * @option String method Name of the function to be weaved. Regex are supported, but not on built-in objects.
		 * @param Function advice Function containing the code that will get called after the execution of the point-cut. It receives one parameter
		 *                        with the result of the point-cut's execution. The function can choose to return this same value or a different one.
		 *
		 * @type Array<Function>
		 * @cat Plugins/General
		 */
		after : function(pointcut, advice)
		{
			return weave( pointcut, { type: _after, value: advice } );
		},

		/**
		 * Creates an advice after the defined point-cut only for unhandled exceptions. The advice will be executed 
		 * after the point-cut method only if the execution failed and an exception has been thrown. It will receive one 
		 * parameter with the exception thrown by the point-cut method.
		 * This function returns an array of weaved aspects (Function).
		 *
		 * @example jQuery.aop.afterThrow( {target: String, method: 'indexOf'}, function(exception) { 
		 *                alert('Unhandled exception: ' + exception); 
		 *                return -1;
		 *          } );
		 * @result Array<Function>
		 *
		 * @example jQuery.aop.afterThrow( {target: calculator, method: 'Calculate'}, function(exception) { 
		 *                console.log('Unhandled exception: ' + exception);
		 *                throw exception;
		 *          } );
		 * @result Array<Function>
		 *
		 * @name afterThrow
		 * @param Map pointcut Definition of the point-cut to apply the advice. A point-cut is the definition of the object/s and method/s to be weaved.
		 * @option Object target Target object to be weaved. 
		 * @option String method Name of the function to be weaved. Regex are supported, but not on built-in objects.
		 * @param Function advice Function containing the code that will get called after the execution of the point-cut. It receives one parameter
		 *                        with the exception thrown by the point-cut method.
		 *
		 * @type Array<Function>
		 * @cat Plugins/General
		 */
		afterThrow : function(pointcut, advice)
		{
			return weave( pointcut, { type: _afterThrow, value: advice } );
		},

		/**
		 * Creates an advice after the defined point-cut. The advice will be executed after the point-cut method 
		 * regardless of its success or failure, and it will receive two parameters: one with the 
		 * result of a successful execution or null, and another one with the exception thrown or null.
		 * This function returns an array of weaved aspects (Function).
		 *
		 * @example jQuery.aop.afterFinally( {target: window, method: 'MyGlobalMethod'}, function(result, exception) {
		 *                if (exception == null)
		 *                    return 'Returned: ' + result;
		 *                else
		 *                    return 'Unhandled exception: ' + exception;
		 *          } );
		 * @result Array<Function>
		 *
		 * @name afterFinally
		 * @param Map pointcut Definition of the point-cut to apply the advice. A point-cut is the definition of the object/s and method/s to be weaved.
		 * @option Object target Target object to be weaved. 
		 * @option String method Name of the function to be weaved. Regex are supported, but not on built-in objects.
		 * @param Function advice Function containing the code that will get called after the execution of the point-cut regardless of its success or failure.
		 *                        It receives two parameters, the first one with the result of a successful execution or null, and the second one with the 
		 *                        exception or null.
		 *
		 * @type Array<Function>
		 * @cat Plugins/General
		 */
		afterFinally : function(pointcut, advice)
		{
			return weave( pointcut, { type: _afterFinally, value: advice } );
		},


		/**
		 * Creates an advice before the defined point-cut. The advice will be executed before the point-cut method 
		 * but cannot modify the behavior of the method, or prevent its execution.
		 * This function returns an array of weaved aspects (Function).
		 *
		 * @example jQuery.aop.before( {target: window, method: 'MyGlobalMethod'}, function() { 
		 *                alert('About to execute MyGlobalMethod'); 
		 *          } );
		 * @result Array<Function>
		 *
		 * @example jQuery.aop.before( {target: String, method: 'indexOf'}, function(index) {
		 *                alert('About to execute String.indexOf on: ' + this);
		 *          } );
		 * @result Array<Function>
		 *
		 * @name before
		 * @param Map pointcut Definition of the point-cut to apply the advice. A point-cut is the definition of the object/s and method/s to be weaved.
		 * @option Object target Target object to be weaved. 
		 * @option String method Name of the function to be weaved. Regex are supported, but not on built-in objects.
		 * @param Function advice Function containing the code that will get called before the execution of the point-cut.
		 *
		 * @type Array<Function>
		 * @cat Plugins/General
		 */
		before : function(pointcut, advice)
		{
			return weave( pointcut, { type: _before, value: advice } );
		},


		/**
		 * Creates an advice 'around' the defined point-cut. This type of advice can control the point-cut method execution by calling
		 * the functions '.proceed()' on the 'invocation' object, and also, can modify the arguments collection before sending them to the function call.
		 * This function returns an array of weaved aspects (Function).
		 *
		 * @example jQuery.aop.around( {target: window, method: 'MyGlobalMethod'}, function(invocation) {
		 *                alert('# of Arguments: ' + invocation.arguments.length); 
		 *                return invocation.proceed(); 
		 *          } );
		 * @result Array<Function>
		 *
		 * @example jQuery.aop.around( {target: String, method: 'indexOf'}, function(invocation) { 
		 *                alert('Searching: ' + invocation.arguments[0] + ' on: ' + this); 
		 *                return invocation.proceed(); 
		 *          } );
		 * @result Array<Function>
		 *
		 * @example jQuery.aop.around( {target: window, method: /Get(\d+)/}, function(invocation) {
		 *                alert('Executing ' + invocation.method); 
		 *                return invocation.proceed(); 
		 *          } );
		 * @desc Matches all global methods starting with 'Get' and followed by a number.
		 * @result Array<Function>
		 *
		 *
		 * @name around
		 * @param Map pointcut Definition of the point-cut to apply the advice. A point-cut is the definition of the object/s and method/s to be weaved.
		 * @option Object target Target object to be weaved. 
		 * @option String method Name of the function to be weaved. Regex are supported, but not on built-in objects.
		 * @param Function advice Function containing the code that will get called around the execution of the point-cut. This advice will be called with one
		 *                        argument containing one function '.proceed()', the collection of arguments '.arguments', and the matched method name '.method'.
		 *
		 * @type Array<Function>
		 * @cat Plugins/General
		 */
		around : function(pointcut, advice)
		{
			return weave( pointcut, { type: _around, value: advice } );
		},

		/**
		 * Creates an introduction on the defined point-cut. This type of advice replaces any existing methods with the same
		 * name. To restore them, just unweave it.
		 * This function returns an array with only one weaved aspect (Function).
		 *
		 * @example jQuery.aop.introduction( {target: window, method: 'MyGlobalMethod'}, function(result) {
		 *                alert('Returned: ' + result);
		 *          } );
		 * @result Array<Function>
		 *
		 * @example jQuery.aop.introduction( {target: String, method: 'log'}, function() {
		 *                alert('Console: ' + this);
		 *          } );
		 * @result Array<Function>
		 *
		 * @name introduction
		 * @param Map pointcut Definition of the point-cut to apply the advice. A point-cut is the definition of the object/s and method/s to be weaved.
		 * @option Object target Target object to be weaved. 
		 * @option String method Name of the function to be weaved.
		 * @param Function advice Function containing the code that will be executed on the point-cut. 
		 *
		 * @type Array<Function>
		 * @cat Plugins/General
		 */
		introduction : function(pointcut, advice)
		{
			return weave( pointcut, { type: _intro, value: advice } );
		},
		
		/**
		 * Configures global options.
		 *
		 * @name setup
		 * @param Map settings Configuration options.
		 * @option Boolean regexMatch Enables/disables regex matching of method names.
		 *
		 * @example jQuery.aop.setup( { regexMatch: false } );
		 * @desc Disable regex matching.
		 *
		 * @type Void
		 * @cat Plugins/General
		 */
		setup: function(settings)
		{
			_regexEnabled = settings.regexMatch;
		}
	};

})();