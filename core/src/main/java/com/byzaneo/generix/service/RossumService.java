package com.byzaneo.generix.service;

import java.io.IOException;
import java.net.URISyntaxException;

public interface RossumService {
  public final static String SERVICE_NAME = "gnxRossumService";

  boolean isRossumServiceActive();

  String getRossumStudioUrl(String referenceNumber, String returnUrl, String rossumKey)
      throws URISyntaxException, IOException, InterruptedException;

  String getRossumKey(String uri, String username, String password) throws URISyntaxException, IOException, InterruptedException;

  String getRossumStudioUrl(String referenceNumber, String returnUrl, String rossumKey, String query)
      throws URISyntaxException, IOException, InterruptedException;
}
