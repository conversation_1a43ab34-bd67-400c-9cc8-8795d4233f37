package com.byzaneo.generix.service;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.io.IOException;
import java.net.*;
import java.net.http.*;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.byzaneo.commons.service.ServiceException;

@Service(RossumService.SERVICE_NAME)
public class RossumServiceImpl implements RossumService {
  @Value("${rossum.studio.uri:}")
  private String rossumStudioUri;

  @Override
  public String getRossumKey(String uri, String username, String password) throws URISyntaxException, IOException, InterruptedException {
    String body = new StringBuilder().append("username=")
        .append(username)
        .append("&password=")
        .append(password)
        .toString();
    HttpResponse<String> response = HttpClient.newBuilder()
        .build()
        .send(HttpRequest.newBuilder(new URI(uri))
            .POST(HttpRequest.BodyPublishers.ofString(body))
            .header("Content-Type", "application/x-www-form-urlencoded")
            .build(), HttpResponse.BodyHandlers.ofString());

    int statusCode = response.statusCode();
    String responseBody = response.body();
    if (statusCode == 200) {
      JSONObject res = new JSONObject(responseBody);
      return res.getString("key");
    }
    else {
      // contains only the message to display in the portlet
      throw new ServiceException(responseBody);
    }
  }

  @Override
  public boolean isRossumServiceActive() {
    return isNotBlank(rossumStudioUri);
  }

  @Override
  public String getRossumStudioUrl(String referenceNumber, String returnUrl, String rossumKey)
      throws URISyntaxException, IOException, InterruptedException {
    return getRossumStudioUrl(referenceNumber, rossumKey, returnUrl, returnUrl, returnUrl);
  }

  @Override
  public String getRossumStudioUrl(String referenceNumber, String returnUrl, String rossumKey, String query)
      throws URISyntaxException, IOException, InterruptedException {
    String cancelUrl = URLEncoder.encode(returnUrl + "?" + query + "&action=cancel", StandardCharsets.UTF_8);
    String postponeUrl = URLEncoder.encode(returnUrl + "?" + query + "&action=postpone", StandardCharsets.UTF_8);
    String validationUrl = URLEncoder.encode(returnUrl + "?" + query + "&action=validate", StandardCharsets.UTF_8);
    return getRossumStudioUrl(referenceNumber, rossumKey, cancelUrl, postponeUrl, validationUrl);
  }

  private String getRossumStudioUrl(String referenceNumber, String rossumKey, String cancelUrl, String postponeUrl, String validationUrl)
      throws URISyntaxException, IOException, InterruptedException {
    String body = new StringBuilder().append("return_url=")
        .append(validationUrl)
        .append("&cancel_url=")
        .append(cancelUrl)
        .append("&postpone_url=")
        .append(postponeUrl)
        .toString();
    HttpResponse<String> response = HttpClient.newBuilder()
        .build()
        .send(HttpRequest.newBuilder(new URI(MessageFormat.format(rossumStudioUri, referenceNumber)))
            .POST(HttpRequest.BodyPublishers.ofString(body))
            .header("Authorization", "Bearer " + rossumKey)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .build(), HttpResponse.BodyHandlers.ofString());

    int statusCode = response.statusCode();
    String responseBody = response.body();
    if (statusCode == 200) {
      JSONObject res = new JSONObject(responseBody);
      return res.getString("url");
    }
    else {
      // contains only the message to display in the portlet
      throw new ServiceException(responseBody);
    }
  }
}
