package com.byzaneo.generix.ui;

import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.JSFHelper.getRequestParameter;
import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.info;
import static com.byzaneo.commons.util.I18NHelper.fromJsonLabelSet;
import static com.byzaneo.faces.model.query.QueryModelBuilder.createQueryModel;
import static com.byzaneo.generix.service.SecurityService.GroupName.NOTIFICATION;
import static com.byzaneo.security.util.PrincipalHelper.getGroupsByDescriptions;
import static com.byzaneo.security.util.PrincipalHelper.isInGroupNames;
import static java.lang.Boolean.FALSE;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.trimToNull;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.slf4j.LoggerFactory.getLogger;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.bean.PropertyDescriptor;
import com.byzaneo.commons.service.BeanService;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.faces.model.query.QueryModel;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.exception.DuplicateException;
import com.byzaneo.generix.exception.KeycloakUserException;
import com.byzaneo.generix.service.InstanceService;
import com.byzaneo.generix.service.MessagingService;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.service.SecurityService.Resource;
import com.byzaneo.generix.ui.ApplicationHandler.EDateFormat;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.security.bean.Role;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.service.authentication.AuthenticationRestService;
import com.byzaneo.task.ui.ClosableDialog;
import com.byzaneo.task.util.TaskHelper;
import com.google.common.collect.ImmutableMap;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

/**
 * Used to manage a {@link User} thru the PrimeFaces dialog framework.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date 15 nov. 2013
 * @since 2.4 GNX-801
 */
@ManagedBean(name = UserDialogHandler.MANAGED_BEAN_NAME)
@ViewScoped
public class UserDialogHandler implements Serializable, ClosableDialog {
    private static final long serialVersionUID = 3372035446490067580L;

    public static final String MANAGED_BEAN_NAME = "gnxUserDialogHandler";

    private static final Logger log = getLogger(UserDialogHandler.class);

    /**
     * Deploy dialog component options
     */
    public static final Map<String, Object> USER_DIALOG_OPTIONS = ImmutableMap.<String, Object>builder().put("modal", true).put("draggable", true).put("resizable", true).put("width", 900).put("height", 680).put("contentWidth", "100%").put("contentHeight", "100%").build();

    /**
     * User identifier used to create a new user
     */
    public static final String NEW_USER_ID = "new";

    // DIALOG PARAMETERS
    /**
     * User Identifier
     */
    public static final String REQUEST_PARAM_USER_ID = "uid";

    /**
     * User Primary Group
     */
    public static final String REQUEST_PARAM_USER_PRIMARY_GROUP_ID = "upid";

    /**
     * User Company
     */
    public static final String REQUEST_PARAM_USER_COMPANY_ID = "cpyid";

    /**
     * User Duplication
     */
    public static final String REQUEST_PARAM_USER_DUPLICATION = "duplicate";

    /**
     * Instance identifier
     */
    public static final String REQUEST_PARAM_INSTANCE_CODE = "icode";

    /**
     * Admin user Identifier
     */
    public static final String REQUEST_PARAM_ADMIN_USER_ID = "aid";

    /**
     * Read only Identifier
     */
    public static final String REQUEST_PARAM_READ_ONLY = "read";

    /**
     * Help fragment
     **/
    public static final String REQUEST_PARAM_HELP_FRAGMENT = "helpFragment";

    private transient SecurityService securityService;

    private transient InstanceService instanceService;

    private transient MessagingService messagingService;

    private transient SessionHandler sessionHandler;

    private transient AuthenticationRestService authenticationRestService;

    private User user;

    private User adminUser;

    private Group primaryGroup;

    private String companyId;

    private String instanceCode;

    private String partnerId;

    private List<SelectItem> instanceItems;

    private List<SelectItem> scopeItems;

    private boolean generatePassword;

    // This field manages the Keycloak behavior, send or not a renewal link, but by keycloak
    private boolean sendNewPassword;

    private boolean readOnly;

    private String helpFragment;

    @Getter
    @Setter
    private Boolean editionMode = false;

    protected transient BeanService beanService;

    @PostConstruct
    public void init() {
        this.securityService = getSpringBean(SecurityService.class, SecurityService.SERVICE_NAME);
        this.instanceService = getSpringBean(InstanceService.class, InstanceService.SERVICE_NAME);
        this.messagingService = getSpringBean(MessagingService.class, MessagingService.SERVICE_NAME);
        this.sessionHandler = getManagedBean(SessionHandler.class, SessionHandler.MANAGED_BEAN_NAME);
        this.beanService = getSpringBean(BeanService.class, BeanService.SERVICE_NAME);
        this.authenticationRestService = getSpringBean(AuthenticationRestService.class, AuthenticationRestService.SERVICE_NAME);

        this.companyId = getRequestParameter(REQUEST_PARAM_USER_COMPANY_ID);
        this.instanceCode = trimToNull(getRequestParameter(REQUEST_PARAM_INSTANCE_CODE));
        this.readOnly = Boolean.valueOf(getRequestParameter(REQUEST_PARAM_READ_ONLY));
        this.load(getRequestParameter(REQUEST_PARAM_USER_ID), getRequestParameter(REQUEST_PARAM_USER_PRIMARY_GROUP_ID), getRequestParameter(REQUEST_PARAM_ADMIN_USER_ID), Boolean.valueOf(getRequestParameter(REQUEST_PARAM_USER_DUPLICATION, FALSE.toString())), false);
        this.helpFragment = getRequestParameter(REQUEST_PARAM_HELP_FRAGMENT);
    this.sendNewPassword = isBlank(this.user.getId()) || NEW_USER_ID.equals(getRequestParameter(REQUEST_PARAM_USER_ID));
        this.editionMode = this.user.getId() != null && !NEW_USER_ID.equals(getRequestParameter(REQUEST_PARAM_USER_ID));
    }

    @PreDestroy
    public void reset() {
        this.user = null;
        this.primaryGroup = null;
        this.companyId = null;
        this.instanceCode = null;
        this.generatePassword = false;
        this.sendNewPassword = false;
    }

    /*
     * -- EVENTS --
     */

    public void onReinitPassword(ActionEvent event) {
        try {
            this.securityService.sendResetPasswordLink(user.getLogin(), instanceCode);
            info("labels.info_user_new_password", user.getFullname());
        } catch (ServiceException e) {
            error("labels.error_sending_new_password", user.getFullname());
        } catch (Exception e) {
            error("labels.exception_message", user.getFullname());
        }

    }

    public void onSaveUserDialog(ActionEvent event) {
        onSaveUser(true);
    }

    public void onSaveUserNoDialog(ActionEvent event) {
        onSaveUser(false);
    }

    public void onSaveUser(boolean closeDialog) {

        final Group primary;
        if (this.isRequireSelectPartner()) {
            primary = this.securityService.getGroup(this.partnerId);
            if (primary == null) {
                error("labels.error_save_user_no_partner");
                return;
            }
        } else {
            primary = this.primaryGroup;
        }

        try {
            // primary group null means back office user / admin and it is managed directly freom keycloak
            if (primary != null) {
                // if the user has no id means that the user is creating now and we must add him at the keycloak realm
        boolean kcSuccess = authenticationRestService.putUserToKeycloak(user, primary, sendNewPassword, !user.isDisabled());
                if (!kcSuccess) {
                    String errMessage = "Failed to " + (StringUtils.isBlank(this.user.getId()) ? "create" : "update") + " Keycloak user " + user.getLogin();
                    log.error(errMessage);
                    throw new KeycloakUserException((StringUtils.isBlank(this.user.getId()) ? MessageHelper.getMessage("labels.error_creating_user_keycloak", "Failed to create keycloak user", null, user.getLogin()) : MessageHelper.getMessage("labels.error_updating_user_keycloak", "Failed to update keycloak user", null, user.getLogin())));
                }
            }

            this.user = securityService.saveUser(user, primary, instanceCode, false, false, false);

            // if the user has no id means that the user is creating now and his password has to be checked
            // boolean checkPassword = !closeDialog || user.getId() == null || "admin".equals(user.getId());
            // this.user = securityService.saveUser(user, primary, instanceCode, generatePassword, notify, checkPassword);
            if (closeDialog) {
                ClosableDialog.closeDialog(this.user);
            }
        } catch (DuplicateException de) {
            error(de, "labels.error_saving_user", de.getMessage());
        } catch (KeycloakUserException kue) {
            error(kue, "labels.error_saving_user", kue.getMessage());
        } catch (Exception e) {
            error(e, "labels.error_saving_user", getRootCauseMessage(e));
        }
    }

    /*
     * -- UTILS --
     */

    public void load(String userId, String primaryGroupId, String adminUserId, boolean duplicate, boolean reset) {
        if (isBlank(userId)) return;
        if (reset) this.reset();
        // user
        this.user = NEW_USER_ID.equals(userId) ? new User() : this.securityService.getUser(userId);
        if (user == null) {
            error("labels.error_no_user_found", userId);
            return;
        }

        // primary group
        Optional<Group> userPrimaryGroup = this.securityService.getPrimaryGroupOfUser(this.user);
        this.primaryGroup = isBlank(primaryGroupId) ? userPrimaryGroup.orElse(null) : this.securityService.getGroup(primaryGroupId);
        // scope
        this.adminUser = isBlank(adminUserId) ? null : this.securityService.getUser(adminUserId);

        // fetch...
        this.user.getGroups();
        // duplicate...
        if (duplicate) {
            User newUser = new User();
            if (primaryGroup == null) {
                error("labels.error_user_without_primary_group");
                this.user = null;
                return;
            }
            newUser.setPrimaryGroup(primaryGroup);
            newUser.setFirstname(this.user.getFirstname());
            newUser.setLastname(this.user.getLastname());
            newUser.setFullname(this.user.getFullname());
            newUser.setEmail(this.user.getEmail());
            newUser.setGroups(this.user.getGroups());
            newUser.setLocale(this.user.getLocale());
            this.user = newUser;
        }
        // user date pattern
        user.setDatePattern((EDateFormat.findPatternBy(user, SessionHandler.DEFAULT_DATE_PATTERN)).toString());
    }

    public static final void openUserDialog(final String outcome, final String userId, final String primaryGroupId, final String companyId, final String instanceCode, final boolean duplicate, final String helpFragment) {
        openUserDialog(outcome, null, userId, primaryGroupId, companyId, instanceCode, null, duplicate, false, helpFragment);
    }

    public static final void openUserDialog(final String outcome, final Map<String, Object> options, final String userId, final String primaryGroupId, final String companyId, final String instanceCode, final boolean duplicate, final String helpFragment) {
        openUserDialog(outcome, null, userId, primaryGroupId, companyId, instanceCode, null, duplicate, false, helpFragment);
    }

    public static final void openUserDialog(final String outcome, final Map<String, Object> options, final String userId, final String primaryGroupId, final String companyId, final String instanceCode, final String adminUserId, final boolean duplicate, final boolean readOnly, final String helpFragment) {
        if (isBlank(userId)) return;

        getCurrentInstance().openDialog(outcome,
                // dialog options
                options == null ? USER_DIALOG_OPTIONS : options,
                // request parameters
                ImmutableMap.<String, List<String>>builder().put(REQUEST_PARAM_USER_ID, asList(userId == null ? "" : userId)).put(REQUEST_PARAM_USER_PRIMARY_GROUP_ID, asList(primaryGroupId == null ? "" : primaryGroupId)).put(REQUEST_PARAM_USER_COMPANY_ID, asList(companyId == null ? "" : companyId)).put(REQUEST_PARAM_INSTANCE_CODE, asList(instanceCode == null ? "" : instanceCode)).put(REQUEST_PARAM_USER_DUPLICATION, asList(Boolean.toString(duplicate))).put(REQUEST_PARAM_ADMIN_USER_ID, asList(adminUserId == null ? "" : adminUserId)).put(REQUEST_PARAM_READ_ONLY, asList(Boolean.toString(readOnly))).put(REQUEST_PARAM_HELP_FRAGMENT, asList(helpFragment == null ? "" : helpFragment)).build());
    }

    public QueryModel getQueryModel() {
        try {
            // bql filter can be added just for Invoice portlet
            BeanDescriptor bd = beanService.fromClasspath("descriptors/invoice-columns.xml");
            List<PropertyDescriptor> properties = bd.getProperties();
            for (PropertyDescriptor propertyDescriptor : properties) {
                propertyDescriptor.setLabel(getLabel("gnxxcblinvlbls", propertyDescriptor.getName(), propertyDescriptor.getName(), getLocale()));
            }
            bd.setProperties(properties);
            return createQueryModel(bd, null, null, null, getLocale(), null, true, true, "type", "staus");
        } catch (IOException ex) {
            TaskHelper.error(this, ex, "{0}", getRootCauseMessage(ex));
            return null;
        }

    }

    /*
     * -- ACCESSORS --
     */

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Group getPrimaryGroup() {
        return primaryGroup;
    }

    public boolean isCompanyUser() {
        return user != null && primaryGroup != null && primaryGroup instanceof Company;
    }

    public boolean isPartnerUser() {
        return user != null && primaryGroup != null && primaryGroup instanceof Partner;
    }

  public boolean renderRenewalLinkCheckbox() {
    return !this.readOnly && !Objects.equals("admin", this.user.getLogin()) && (this.isCompanyUser() || this.isPartnerUser());
    }

    public boolean isGeneratePassword() {
        if (!sessionHandler.isGranted(Resource.Users_Creation, Right.EXECUTE)) generatePassword = true;
        return generatePassword;
    }

    public void setGeneratePassword(boolean generatePassword) {
        this.generatePassword = generatePassword;
    }

    public boolean isRenderNewPassword() {
        return instanceCode != null;
    }

    public boolean isSendNewPassword() {
        return sendNewPassword;
    }

    public void setSendNewPassword(boolean sendNewPassword) {
        this.sendNewPassword = sendNewPassword;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    // -- INSTANCE --

    public String getInstanceCode() {
        return instanceCode;
    }

    public void setInstanceCode(String instanceCode) {
        this.instanceCode = instanceCode;
    }

    public boolean isRequireSelectInstance() {
        return primaryGroup != null && isBlank(instanceCode);
    }

    public List<SelectItem> getInstances() {
        if (primaryGroup == null) {
            return emptyList();
        }

        if (instanceItems == null) {
            Group companyGroup = Company.DESCRIPTION.equals(primaryGroup.getDescription()) ? primaryGroup : primaryGroup.getParent();
            List<Instance> instances = instanceService.getInstances(companyGroup);
            instanceItems = instances.stream().map(instance -> new SelectItem(instance.getCode(), fromJsonLabelSet(instance.getName(), JSFHelper.getLocale(), instance.getConfiguration().getDefaultLanguage(), "Environment"))).collect(toList());
        }
        return instanceItems;
    }

    // -- ROLES --

    public List<Group> getRoles() {
        return this.securityService.getGroupsByParentAndDescription(companyId, Role.DESCRIPTION);
    }

    public List<Group> getUserRoles() {
        List<Group> groups;
        try {
            groups = user.getGroups();
        } catch (Exception e) {
            groups = this.securityService.getUser(user.getId()).getGroups();
        }
        return getGroupsByDescriptions(groups, Role.DESCRIPTION);
    }

    public void setUserRoles(List<Group> roles) {
        this.user.setGroups(roles);
    }

    // ROLE: NOTIFICATION

    public boolean isNotified() {
        return isInGroupNames(user, NOTIFICATION.toString());
    }

    public void setNotified(boolean notified) {
        Role notification = this.messagingService.getNotificationRole();
        if (notified) user.addGroup(notification, false);
        else user.removeGroup(notification);
    }

    // -- INSTANCE --

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public boolean isRequireSelectPartner() {
        return (this.user.getId() == null || NEW_USER_ID.equals(getRequestParameter(REQUEST_PARAM_USER_ID))) && adminUser != null && primaryGroup == null;
    }

    public List<SelectItem> getPartners() {
        if (adminUser == null) {
            return emptyList();
        }

        if (scopeItems == null) {
            List<Partner> partners = this.securityService.getUserGroupsByType(adminUser, Partner.class);
            scopeItems = partners.stream().map(partner -> new SelectItem(partner.getId(), partner.getFullnameOrName())).collect(toList());

            this.partnerId = ((Partner) this.adminUser.getPrimaryGroup()).getId();
        }
        return scopeItems;
    }

    // help fragment
    public String getHelpFragment() {
        return helpFragment;
    }
}
