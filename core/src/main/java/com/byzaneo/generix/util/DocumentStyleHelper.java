package com.byzaneo.generix.util;

import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;

import java.util.*;

import org.apache.commons.lang3.StringUtils;

import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.DocumentStatusEntity;

/**
 * Document style helper.
 *
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Jun 10, 2016
 * @since GNX-4617
 */
public class DocumentStyleHelper {

  private DocumentStyleHelper() {
    super();
  }

  protected static final String LABEL_CORE = "labels";

  private static final String UNKNOWN = "UNKNOWN";

  public static final String STAGE_PREFIX = "Stage_";
  public static final String ACCOUNTING_PREFIX = "Accounting_";

  /* -- STATUS -- */
  public static String resolveStatusIconClasses(DocumentStatus status) {
    return resolveStatusIconClasses(status == null ? "UNKNOWN" : status.name());
  }

  public static String resolveStatusIconClasses(String status) {
    // TODO switch on Document.Status enum value of orderState, if certain
    // that orderState is a value of this enum
    return getLabel(LABEL_CORE, (status == null ? UNKNOWN : status.toString()) + "_ICON", "", Locale.ENGLISH);
  }

  public static String resolveStatusLabelClasses(DocumentStatusEntity status, String environmentCode) {
    if (isMissingEnvCode(environmentCode, status) ||
        status.getValues()
            .get(environmentCode)
            .getStyle() == null) {
      String statusLabel = status == null ? UNKNOWN : status.getStatusCode();
      return "";
    }
    StringBuilder sb = new StringBuilder("label label-").append(status.getStatusCode())
        .append(" label-")
        .append(
            status.getValues()
                .get(environmentCode)
                .getStyle());
    return sb.toString();
  }

  public static String resolveStatusLabelText(DocumentStatusEntity status, String environmentCode) {
    return resolveStatusLabelText(status, getLocale(), environmentCode);
  }

  public static String resolveStatusLabelText(DocumentStatusEntity status, Locale userLocale, String environmentCode) {
    if (isMissingEnvCode(environmentCode, status))
      return status == null ? "UNKNOWN" : status.getStatusCode();
    Map<Locale, String> labels = status.getValues()
        .get(environmentCode)
        .getLabels();
    String label = labels.get(userLocale) != null ? labels.get(userLocale) : labels.get(Locale.ENGLISH);
    return label != null ? label : status.getStatusCode();
  }

  public static boolean isMissingEnvCode(String environmentCode, DocumentStatusEntity stausImpl) {
    return (StringUtils.isEmpty(environmentCode) || stausImpl == null || stausImpl.getValues()
        .get(environmentCode) == null);
  }

  /* -- STAGE -- */

  public static String resolveStageLabelClasses(DocumentStage stage) {
    String stageLabel = stage == null ? UNKNOWN : stage.toString();
    return "label label-" + stageLabel + " label-" + getLabel(LABEL_CORE, stageLabel + "_STYLE_STAGE", "", Locale.ENGLISH);
  }

  public static String resolveStageLabelClasses(DocumentStage stage,Locale userLocale) {
    Locale locale = userLocale == null ? Locale.ENGLISH : userLocale;
    String stageLabel = stage == null ? UNKNOWN : stage.toString();
    return "label label-" + stageLabel + " label-" + getLabel(LABEL_CORE, stageLabel + "_STYLE_STAGE", "", locale);
  }

  public static String resolveStageLabelText(DocumentStage stage) {
    return resolveStageLabelText(stage, getLocale());
  }

  public static String resolveStageLabelText(DocumentStage stage, Locale userLocale) {
    if(userLocale != null) {
      String code = stage == null ? UNKNOWN : stage.toString();
      return getLabel(LABEL_CORE, STAGE_PREFIX + code, "", userLocale);
    }
    return resolveStageLabelText(stage, getLocale());
  }

  public static String resolveDocumentStatusLabelText(DocumentStatus status, Locale userLocale) {
    if(userLocale != null) {
      String code = status == null ? UNKNOWN : status.toString();
      return getLabel(LABEL_CORE,  code, "", userLocale);
    }
    return resolveDocumentStatusLabelText(status, getLocale());
  }

  public static String resolveAccountingPostingStatusLabelText(String accountingPostingStatus, Locale userLocale) {
    if(userLocale != null) {
      String code = accountingPostingStatus == null ? UNKNOWN : accountingPostingStatus;
      if(code.equals(UNKNOWN)){
        return  "";
      }
      return getLabel(LABEL_CORE, ACCOUNTING_PREFIX + code, "", userLocale);
    }
    return resolveAccountingPostingStatusLabelText(accountingPostingStatus, getLocale());
  }



  public static String resolveConsultStatusLabelText(DocumentConsultStatus consultStatus, Locale userLocale) {
    return getLabel(LABEL_CORE, consultStatus == null ? UNKNOWN : consultStatus.toString(), "", userLocale);
  }

  public static String resolveConsultStatusLabelText(DocumentConsultStatus consultStatus) {
    return getLabel(LABEL_CORE, consultStatus == null ? UNKNOWN : consultStatus.toString(), "", getLocale());
  }

  public static String resolvePaymentStatusLabelText(PaymentStatus payemtnStatus) {
    return getLabel(LABEL_CORE, payemtnStatus == null ? "" : payemtnStatus.toString(), "", getLocale());
  }

  public static String resolveArchiveStatusLabelClasses(ArchiveStatus status) {
    String statusLabel = status == null ? UNKNOWN : status.toString();
    return "label label-" + statusLabel + " label-" + getLabel(LABEL_CORE, statusLabel + "_STYLE", "", Locale.ENGLISH);
  }

  public static String resolveArchiveStatusLabelText(ArchiveStatus status) {
    ArchiveStatus archiveStatus = status == null ? ArchiveStatus.UNDEFINED : status;
    return getLabel(LABEL_CORE, archiveStatus.toString(), "", getLocale());
  }

  public static String resolveArchiveStatusLabelText(ArchiveStatus status, Locale userLocale) {
    ArchiveStatus archiveStatus = status == null ? ArchiveStatus.UNDEFINED : status;
    return getLabel(LABEL_CORE, archiveStatus.toString(), "", userLocale);
  }
}
