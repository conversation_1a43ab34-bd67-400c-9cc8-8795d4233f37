package com.byzaneo.generix.util;

import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.security.bean.TechnicalUser;
import com.byzaneo.security.spring.UserDetailsService;
import com.byzaneo.security.util.PrincipalHelper;
import org.slf4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static org.slf4j.LoggerFactory.getLogger;

public class PartnerHelper {

    private static final Logger logger = getLogger(PartnerHelper.class);

    public static final String LABEL_FAMILY = "gnxxcblinvlbls";

    private static SecurityService securityService;

    private static SecurityService getSecurityService() {
        if (securityService == null) {
            securityService = getBean(SecurityService.class, SecurityService.SERVICE_NAME);
        }
        return securityService;
    }

    public static List<Partner> filterPartnerList(TechnicalUser technicalUser, List<String> inputPartners) {
        boolean isCustomerUser = PrincipalHelper.isCompanyTechnicalUser(technicalUser);
        boolean isContactUser = PrincipalHelper.isPartnerTechnicalUser(technicalUser);
        List<Partner> scopedPartners = UserDetailsService.getTechnicalUserScopePartners(technicalUser);

        if (isCustomerUser) {
            List<Partner> partners = inputPartners.stream()
                    .map(id -> getSecurityService().getPartner(id, technicalUser.getPrimaryGroup()
                            .getCode()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (!scopedPartners.isEmpty()) {
                Set<String> scopedCodes = scopedPartners.stream()
                        .map(Partner::getCode)
                        .collect(Collectors.toSet());

                partners = partners.stream()
                        .filter(p -> scopedCodes.contains(p.getCode()))
                        .collect(Collectors.toList());
            }

            return new ArrayList<>(partners);
        }

        if (isContactUser) {
            Set<Partner> codes = new LinkedHashSet<>();

            Set<String> inputCodes = new HashSet<>(inputPartners);
            for (Partner partner : scopedPartners) {
                if (inputCodes.contains(partner.getCode())) {
                    codes.add(partner);
                }
            }

            return new ArrayList<>(codes);
        }

        return Collections.emptyList();
    }
}
