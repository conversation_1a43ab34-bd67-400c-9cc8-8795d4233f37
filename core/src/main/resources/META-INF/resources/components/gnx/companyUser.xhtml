<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:b="http://byzaneo.com/ui">

    <!-- INTERFACE -->
    <cc:interface name="companyUser">
        <cc:attribute name="handler" default="#{gnxCompanyHandler}"/>
        <cc:attribute name="title" type="java.lang.String"/>
        <cc:attribute name="renderAdminRole" type="boolean" default="false"/>
        <cc:attribute name="dialog" type="boolean" default="false"/>
        <cc:attribute name="readOnly" type="boolean" default="false"/>
        <cc:facet name="actions"/>
    </cc:interface>

    <!-- IMPLEMENTATION -->
    <cc:implementation>
        <!-- HEADER -->
        <!-- classic -->
        <p:panelGrid id="cPartnerHeader" rendered="#{not cc.attrs.dialog}">
            <p:row>
                <p:column><h:outputText value="#{cc.attrs.title}" styleClass="title1"/></p:column>
                <p:column styleClass="right">
                    <cc:renderFacet name="actions"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <!-- fixed -->
        <p:outputPanel styleClass="dialog-header navbar navbar-default navbar-fixed-top" rendered="#{cc.attrs.dialog}">
            <p:outputPanel styleClass="navbar-container">
                <p:outputPanel class="navbar-header pull-left">
                    <!-- <h:outputText value="#{cc.attrs.title}" styleClass="title1 navbar-brand" /> -->
                </p:outputPanel>
                <p:outputPanel class="navbar-form navbar-right">
                    <cc:renderFacet name="actions"/>
                </p:outputPanel>
            </p:outputPanel>
        </p:outputPanel>
        <p:spacer height="55px" width="100%" rendered="#{cc.attrs.dialog}"/>
        <p:messages closable="true" showDetail="false" showSummary="true" globalOnly="false" autoUpdate="true"
                    rendered="#{cc.attrs.dialog}"/>
        <!-- CONTENT -->
        <h:panelGroup layout="block" id="cUserContent">
			<p:outputPanel styleClass="form-group" rendered="#{cc.attrs.handler.user.keycloakUserId != null}">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cKeycloakId" value="#{seclbls.keycloak_id}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:inputText id="cKeycloakId" value="#{cc.attrs.handler.user.keycloakUserId}"
                                 disabled="#{cc.attrs.readOnly}"
                                 readonly="#{cc.attrs.handler.user.keycloakUserId!=null}"
                                 styleClass="form-control">
                    </p:inputText>
                </p:outputPanel>
            </p:outputPanel>
			
			<p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserDisabled" value="#{seclbls.disabled}"/>
				<p:outputPanel styleClass="col-sm-10">
                    <p:selectBooleanCheckbox id="cUserDisabled" value="#{cc.attrs.handler.user.disabled}" styleClass="form-control" disabled="#{cc.attrs.readOnly}"/>
			    </p:outputPanel>
            </p:outputPanel>

            <p:outputPanel styleClass="form-group" rendered="#{gnxUserDialogHandler.renderRenewalLinkCheckbox()}">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cSendNewPassword" value="#{labels.send_renewal_link}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:selectBooleanCheckbox id="cSendNewPassword" value="#{cc.attrs.handler.sendNewPassword}" styleClass="form-control" disabled="#{cc.attrs.readOnly}"/>
                </p:outputPanel>
            </p:outputPanel>
			
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserLogin" value="#{labels.login}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:inputText id="cUserLogin" value="#{cc.attrs.handler.user.login}" required="true"
                                 validatorMessage="#{labels.login_validator}"
                                 styleClass="form-control"
                                 validator="xssValidator"
                                 disabled="#{gnxUserDialogHandler.editionMode}">
                        <f:validateRegex pattern="#{gnxHandler.loginRegex}"/>
                        <f:validateBean validationGroups="com.byzaneo.generix.bean.groups.Generix"/>
                    </p:inputText>
                </p:outputPanel>
            </p:outputPanel>

            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserEmail" value="#{seclbls.email}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:inputText id="cUserEmail" value="#{cc.attrs.handler.user.email}" required="true"
                                 styleClass="form-control"
                                 disabled="#{not gnxUserHandler.companyUser and not gnxUserHandler.partnerUser and gnxUserDialogHandler.editionMode}"
                                 validatorMessage="#{labels.validator_email}">
                        <f:validateRegex pattern="#{gnxHandler.getEmailValidator()}"/>
                        <f:validator validatorId="xssValidator" />
                    </p:inputText>
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserFirst" value="#{seclbls.firstname}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:inputText id="cUserFirst" value="#{cc.attrs.handler.user.firstname}" validator="xssValidator" required="true"
                                 onchange="updateFullname()" styleClass="jQuerySelectorFirstname form-control"
                                 disabled="#{not gnxUserHandler.companyUser and not gnxUserHandler.partnerUser and gnxUserDialogHandler.editionMode}"
                                 converter="trimConverter"/>
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserLast" value="#{seclbls.lastname}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:inputText id="cUserLast" value="#{cc.attrs.handler.user.lastname}" validator="xssValidator" required="true"
                                 onchange="updateFullname()" styleClass="jQuerySelectorLastname form-control"
                                 disabled="#{not gnxUserHandler.companyUser and not gnxUserHandler.partnerUser and gnxUserDialogHandler.editionMode}"
                                 converter="trimConverter"/>
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserFull" value="#{seclbls.fullname}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:inputText id="cUserFull" value="#{cc.attrs.handler.user.fullname}" validator="xssValidator" required="true"
                                 styleClass="jQuerySelectorFullname form-control"
                                 disabled="#{not gnxUserHandler.companyUser and not gnxUserHandler.partnerUser and gnxUserDialogHandler.editionMode}"
                                 converter="trimConverter"/>
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserPhone" value="#{seclbls.phone}"/>
                <p:outputPanel styleClass="col-sm-10"><p:inputText id="cUserPhone"
                                                                   value="#{cc.attrs.handler.user.phone}"
                                                                   styleClass="form-control input-small"
                                                                   disabled="#{cc.attrs.readOnly}">
                                                                   <f:attribute name="field" value="#{seclbls.phone}"/>
                                     							   <f:validator validatorId="phoneNumberValidator"/>
                                                                   </p:inputText>
                	<p:watermark for="cUserPhone" value="+xx (x)x xx xx xx xx" />                                                   
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserMobile" value="#{seclbls.mobile}"/>
                <p:outputPanel styleClass="col-sm-10"><p:inputText id="cUserMobile"
                                                                   value="#{cc.attrs.handler.user.mobile}"
                                                                   styleClass="form-control input-small"
                                                                   disabled="#{cc.attrs.readOnly}">
                                                                   <f:attribute name="field" value="#{seclbls.mobile}"/>
                                     							   <f:validator validatorId="phoneNumberValidator"/>
                                                                   </p:inputText>
                	<p:watermark for="cUserMobile" value="+xx (x)x xx xx xx xx" />                                                   
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserLocale" value="#{seclbls.locale}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:selectOneMenu id="cUserLocale" value="#{cc.attrs.handler.user.locale}" required="true"
                                     converter="localeConverter" filter="true" filterMatchMode="startsWith"
                                     style="width:150px" disabled="#{cc.attrs.readOnly}">
                        <f:selectItem itemValue="#{null}" itemLabel=""/>
                        <f:selectItems value="#{gnxUserHandler.languageItems}"/>
                    </p:selectOneMenu>
                </p:outputPanel>
            </p:outputPanel>
            <p:outputPanel styleClass="form-group"
                           rendered="#{cc.attrs.handler.partnerUser or cc.attrs.handler.companyUser}">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserDateFormat" value="#{seclbls.dateFormat}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:selectOneMenu id="cUserDateFormat" value="#{cc.attrs.handler.user.datePattern}" required="false"
                                     filter="true" filterMatchMode="startsWith" style="width:200px"
                                     disabled="#{cc.attrs.readOnly}">
                        <f:selectItems value="#{gnxHandler.dateFormats}" var="dateFormat" itemValue="#{dateFormat}"
                                       itemLabel="#{dateFormat.pattern}"/>
                    </p:selectOneMenu>
                </p:outputPanel>
            </p:outputPanel>
            <!-- INV BQL FILTER -->
            <p:outputPanel styleClass="form-group"
            	rendered="#{cc.attrs.handler.partnerUser or cc.attrs.handler.companyUser}">
            	<p:outputLabel styleClass="col-sm-2 control-label" 
            		for="userInvBql"
            		value="#{labels.bql_filter_details}"/>
            	<p:outputPanel styleClass="col-sm-10">
           			<b:query id="userInvBql"
			             value="#{cc.attrs.handler.user.bqlInvQuery}"
			             model="#{cc.attrs.handler.queryModel}"
			             completeMethod="#{cc.attrs.handler.onCompleteQuery}"
			             widgetVar="wBqlQuery"
			             helpRendered="fase"/>
			    </p:outputPanel>
            </p:outputPanel>
            <!-- ROLES -->
            <p:outputPanel styleClass="form-group" rendered="#{cc.attrs.handler.class.simpleName=='UserDialogHandler'}">
                <p:outputLabel styleClass="col-sm-2 control-label" for="cUserNotified" value="#{labels.notification}"/>
                <p:outputPanel styleClass="col-sm-10">
                    <p:selectBooleanCheckbox id="cUserNotified" value="#{cc.attrs.handler.notified}"
                                             styleClass="form-control"/>
                </p:outputPanel>
            </p:outputPanel>
        </h:panelGroup>
        <script type="text/javascript">
            function updateFullname() {
                jQuery('.jQuerySelectorFullname').val(jQuery('.jQuerySelectorFirstname').val() + ' ' + jQuery('.jQuerySelectorLastname').val());
            }
        </script>
    </cc:implementation>
</ui:component>
