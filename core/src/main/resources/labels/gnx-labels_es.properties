cancel = Cancelar
year = A\u00F1o
WARNING_label = ADVERTENCIA
ks_password_pkcs11 = Contrase\u00F1a (c\u00F3digo PIN)
siret = N.\u00BA SIREN
importInstance = Importar (*.json)
info_portlet_removed = Se ha eliminado el contenido del portlet.
campaigns = Campa\u00F1as
template_invalid_name = El nombre de la plantilla debe contener entre 3 y 18 letras min\u00FAsculas y/o n\u00FAmeros.
company_view = Cliente {1} ({0})
invalid_file_type = Tipo de archivo no v\u00E1lido
move_left = Mover a la izquierda
alphanumeric = Alfanum\u00E9rico
AtLeastOneUser = Al menos un usuario de un socio
warn_partner_profile_deleted = El perfil de un socio no debe borrarse.
entry_add = A\u00F1adir
profile = Perfil
reminder_delay = Intervalo de tiempo
ks_comment = Descripci\u00F3n
version = Versi\u00F3n
order_issuer = Ordenante
docs_list = Lista de documentos
role_edit = Editar rol
DISABLED_label = DESACTIVADO
configuration_page = Configuraci\u00F3n de p\u00E1gina
menu_security_users = Usuarios de back office
selection = Seleccionar
size = Tama\u00F1o
stop = Parar
confirm_instance_enable = Se activar\u00E1n todos los servicios del entorno (integraci\u00F3n, onboarding, etc.).\\n\\n\u00BFEst\u00E1 seguro de que desea activar este entorno?
ks_hasprivatekey = Con clave privada
left = Izquierda
tasks_campaign = Flujo de tareas
ks_error_no_keystore_folder = Error: no se ha podido determinar la carpeta del keystore.
domain = Dominio
doc_select = Seleccionar
entry_type = Tipo
domain_modif = Modificar un dominio
error_no_company_specified = No se ha especificado ning\u00FAn cliente para la edici\u00F3n para socios {0}.
schema = Esquema
strong = fuerte
role = Rol
managed_by = Gestionado por
menu_security_groups = Grupos
infinite = Infinita
archiving = Archivado
ERROR_icon = fa fa-times-circle-o
length_min = Longitud m\u00EDnima
doc_update = Actualizar
doc_update_information=Actualizar mis datos
result = Resultados
occurence = N\u00FAmero de incidencias
search = Buscar
login_validator = El nombre de usuario debe contener al menos 3 caracteres alfanum\u00E9ricos, '_', '-', '@' o '.
refuse = Rechazar
reminder_subject = Objetivo del recordatorio
partner_parent = Padre
PENDING_color = #bebfbb
campaigns_new = Nueva campa\u00F1a
error_saving_file = Error al realizar el guardado del archivo ({0}).
close = Cerrar
connect = Conectar
communication=Comunicaci\u00F3n
validator_password = Contrase\u00F1a incorrecta (m\u00EDnimo 3 caracteres).
completion = Avance
mail_content = Cuerpo
keyword_$campaigncontactphone = Tel\u00E9fono del contacto de la campa\u00F1a
preferences = Preferencias
ks_uploaded = Keystore importado: {0}
company_add = A\u00F1adir cliente
report_partners_completion = ESTADO DE LA CAMPA\u00D1A
exception_no_backoffice_access = {0} no pertenece a ning\u00FAn socio ni cliente y no tiene acceso al back office.
import_cert_jks_pkcs12 = JKS/PKCS#12...
ui_required = Dato obligatorio
partner = Socio
confirmation_mail_message_html = Hola, {0}:<br/><br/>Tu cuenta se ha creado correctamente. Confirma tu correo electr\u00F3nico haciendo clic en el enlace siguiente: <a href=\"{1}\">Confirmaci\u00F3n de correo electr\u00F3nico</a>.<br/></br>Tambi\u00E9n puedes copiar y pegar esta direcci\u00F3n en tu navegador preferido: {1}.<br/><br/>\u00A1Hasta pronto!
NONE_color = #d4d4d4
partner_import_error = Error al importar socios: {0}
error_removing_role = Error al borrar el rol ({0}).
menu_config = Configuraci\u00F3n
ks_entry_signaturealgoname = Nombre del algoritmo de firma
doc_upload_error = Error de importaci\u00F3n de documento: {0} ({1})
information = Informaci\u00F3n
period_to = al
keyword_$currentDate = Fecha de hoy
ui_validator = Dato inv\u00E1lido
partner_import_already_existing_partners = socio(s) existente(s)
error_removing_partner = Error al borrar al socio ({0}).
error_saving_partner = Error al guardar el socio ({0}).
info_logo_uploaded = Logotipo cargado
language = Idioma
doc_deleted = Documento eliminado
styleClass = Clases de estilo
good = bien
error_uploding_logo = Error al cargar logo {0}.
OK_icon = fa fa-check-circle-o
add_below = A\u00F1adir abajo
error_uploding_template = Error en la carga de la plantilla ({0}).
ks_entry_fingerprintsha1 = Huella digital (SHA-1)
contact = Contacto
add_right = A\u00F1adir a la derecha
entry_add_title = A\u00F1adir un t\u00EDtulo
ks_hasRSApublickey = RSA
WARNING_icon = fa fa-exclamation-circle
export = Exportar
channel_save_ok = Canal guardado.
doc_new_name = Nuevo nombre
info_host_saved = Dominio {0} guardado
exception_import_instance_code_null = El c\u00F3digo del entorno es nulo.
add = A\u00F1adir
decimal_separator = Separador decimal
exception_id_duplication = El identificador ya existe: {0}
ks_friendlyname = Apellido
docs_available = Documentos disponibles
entry_delete_question = Borrar una pregunta
menu_messages = Correos electr\u00F3nicos
error_sending_new_password = Se ha producido un error al enviar el enlace de renovaci\u00F3n de contrase\u00F1a a {0} ({1}).
authentication_mail_template = Correo electr\u00F3nico de notificaci\u00F3n de nuevo contacto
contact_user = Contacto
count = Total
warn_select_partner_notification = Por favor, seleccione los socios que desea notificar.
campaign_documents = Documentos
encoding = Codificaci\u00F3n
undefine = No dematerializada
partner_field = Campos de entrada
error_no_user_found = No se han encontrado usuarios con el nombre de usuario: {0}.
month = Mes
lang_english = Ingl\u00E9s
ks_remove_error = Error al borrar el archivo: {0}.
mail_sent = Enviado
info_company_saved = Cliente {0} guardado
partner_import_no_user = El socio {0} no tiene usuario
sources = Fuentes
back = Volver
choose = Elegir
partner_delete_error = Error de eliminaci\u00F3n de socio: {0}.
title = T\u00EDtulo
content = Contenido
remove = Eliminar
page_new = P\u00E1gina nueva
duration = Duraci\u00F3n
ks_filename = Nombre del certificado
user_blocked = Su cuenta ha sido desactivada. P\u00F3ngase en contacto con su administrador.
entry_question = Pregunta
error_removing_user = Error al borrar usuario ({0}).
configuration_portal = Configuraci\u00F3n del portal
alpha = Alfa
scope = Per\u00EDmetro
entry_title = T\u00EDtulo
client = Cliente
company = Cliente
end = Fin
info_portal_saved = El portal se ha guardado.
sort_order = Direcci\u00F3n de clasificaci\u00F3n
ks_config_require_name = El nombre del keystore es obligatorio.
new_message = Nuevo mensaje
WARNING_color = #ffb752
clear_portlet_confirm = \u00BFEst\u00E1 seguro de que desea vaciar el contenido de este portlet?
info_instance_toggled = El entorno {0} ha sido conmutado.
partner_import_already_existing_users = usuario(s) existente(s)
no_records_found = No se ha encontrado ning\u00FAn registro
ks_entry_alias = Alias
length = Longitud
label = Etiquetado
keyword_$password = Contrase\u00F1a de usuario
message = Correo electr\u00F3nico
validator_email = Direcci\u00F3n de correo electr\u00F3nico incorrecta.
home = Inicio
modify = Modificar
schedule = Frecuencia
channel_not_selected = Ning\u00FAn canal seleccionado
print = Imprimir
ks_morethanoneentry = Su keystore contiene m\u00E1s de un certificado. No se prev\u00E9 gestionar este tipo de keystore.
menu_process_manage = Gesti\u00F3n
ks_unexpected_error_determing_alias = Error inesperado al determinar el alias.
generate_password = Generar enlace de renovaci\u00F3n de contrase\u00F1a
info_role_removed = Rol {0} eliminado
family = Familia
OK_label = OK
prompt = Introduzca la contrase\u00F1a
backup = Guardar
select = Selecci\u00F3n
exception_instance_associated = {0} entorno(s) relacionado(s)
ks_error_no_friendly_name_found = El apellido no se encuentra en los par\u00E1metros y no se puede determinar.
template_revision = Importar una plantilla de correo electr\u00F3nico
error_removing_portlet_content = Error al borrar el contenido del portlet ({0}).
info_user_role_saved = Rol del usuario guardado
partner_contacts = Contactos
channel = Canal
partner_imported = Socios importados: {0}.
shipment_date = Fecha de env\u00EDo
add_above = A\u00F1adir arriba
APPROVED_icon = fa fa-check-circle-o
ks_entrydetailstab = Certificado
ks_password = Contrase\u00F1a
menu_audit = Auditor\u00EDa
PENDING_label = INFORME EN CURSO...
value_default = Valor por defecto
action = Acci\u00F3n
info_file_saved = Archivo guardado
text = Texto
portal = Portal
report_not_completed = No completado
DISABLED_icon = fa fa-minus-circle
tasks = Tareas
order = N.\u00BA de pedido
WARN_icon = fa fa-exclamation-circle
period = Periodo
warn_host_already_used = El dominio ya est\u00E1 en uso.
ks_config_require_instance = El entorno del keystore es obligatorio.
io = DO
partner_user_add = A\u00F1adir un contacto
exception_backoffice_user = {0} es un usuario de back-office.
templates = Plantillas
permission_edit = Editar permisos para
exception_code_duplication = El c\u00F3digo ya existe: {0}.
DISABLED_color = #d4d4d4
WARN_label = ADVERTENCIA
info_user_new_password = Se ha enviado un enlace de renovaci\u00F3n de contrase\u00F1a a {0}.
keyword_$login = Inicio de sesi\u00F3n de usuario
ks_no_file_selected = Debe seleccionarse un certificado.
messaging = Mensajer\u00EDa
partner_deleted = Eliminaci\u00F3n de socios: {0}.
field = un campo
quick_search = B\u00FAsqueda r\u00E1pida
info_company_removed = Cliente {0} eliminado
campaign_date_creation = Fecha de creaci\u00F3n
analyze = Analizar
integration = Proceso: Actividad
messages = Correos electr\u00F3nicos
doc_cancel = Desmarcar
begin = Inicio
info_partner_file_import = {0} importado
ks_upload_error = Error de importaci\u00F3n del archivo: {0}
status = Estado
template = Plantilla
other = Otros
property_required = es necesario.
menurights = Men\u00FA derecho
companyinformation= Datos de mi empresa
error_importing_instance = Error al importar el entorno: {0}.
save = Guardar
exception_user_associated = {0} usuario(s) vinculado(s)
login = Inicio de sesi\u00F3n
notify = Notificar
channel_duplicate_error = Error al copiar el canal
error_saving_role = Error al guardar el rol ({0}).
ks_unexpected_multiple_keys = Varias claves en el keystore, un caso inesperado.
file = Archivo
library = Librer\u00EDa
folder_out = Carpeta de salida
campaign_date_start = Fecha de inicio
ERROR_color = #d15b47
entry_import_faq = Importar una pregunta frecuente
portlet = Portlets
exception_role_has_users = El rol {0} a {1} tiene usuario(s) asociado(s).
ks_entry_serialnumber = N\u00FAmero de serie
reminder = Recordatorio
exception_partner_subgroups = El socio {0} a {1} tiene subgrupo(s).
keyword_$campaigncontactname = Nombre del contacto de la campa\u00F1a
permissions_dialog_page_title = Determinar los derechos de acceso a la p\u00E1gina
menu_partners = Socios
channel_add_error_duplicatename = Ya existe un canal con este nombre. No se puede crear este canal.
removed = Eliminado
disable = Desactivar
role_add = A\u00F1adir uno
error_saving_instance = Error al guardar el entorno ({0}).
entry_add_question = A\u00F1adir una pregunta
doc_remove_error = Error al borrar un documento
doc_uploaded_success = Documento(s) importado(s): {0}/{1}
unread_message = mensaje(s) no le\u00EDdo(s)
exception_import_export_null = La exportaci\u00F3n es nula.
completion_bounded = Avance delimitado
doc_downloads = N\u00FAmero de descargas
is_locked = est\u00E1 bloqueado.
exception_user_more_companies = El usuario {0} del socio {1} tiene m\u00E1s de un cliente vinculado ({2}).
instance = Medioambiente
template_new = Crear una plantilla
doc_rename_error = Documento existente
menu_monitoring = Actividad
recent_mapping = Mapeo reciente
subject = Asunto
NONE_icon = fa fa-minus-circle
create_mail = Crear una carta
day_of_month = D\u00EDas del mes
report_partners_status_by_month = CAMBIOS EN EL ESTADO DE LOS SOCIOS
error_saving_user = Error al guardar usuario ({0}).
menu_admin = Administraci\u00F3n
download = Descargar
pages = P\u00E1ginas
ks_provide_library = La librer\u00EDa por rellenar.
sort_order_descending = Descendente
clear_page_confirm = \u00BFEst\u00E1 seguro de que desea borrar el contenido de esta p\u00E1gina?
info_role_saved = Rol {0} guardado
host = Anfitri\u00F3n
entry_radio = Radio
menu_search = Buscar
all = Todos
exception_import_instance_null = El entorno del archivo de exportaci\u00F3n es nulo.
modif_contact_partner = Cambiar el contacto de socio
workflow = Flujo de trabajo
error_changing_layout = Error al cambiar el dise\u00F1o ({0}).
upload_files = Carga de archivos
warn_profile_already_exists = Este perfil ya existe.
collapse_all = Reducir todo
task = Tarea
partner_delete_error_children_exist = Un socio no puede ser eliminado mientras tenga subsocios: {0}.
report_informations = Informaci\u00F3n
company_edit = Editar cliente {1} ({0})
length_max = Longitud m\u00E1xima
emitter = Emisor
info_no_portlet_content_defined = El portlet se ha definido sin contenido para esta p\u00E1gina.
error_select_correct_user = Por favor, seleccione un usuario v\u00E1lido.
exception_constraint_violation = {0}: {1}
no = No
code = C\u00F3digo
freetext = Campos abiertos
ui_invalid_file = Tipo de archivo incorrecto
sort_by = Ordenar por
user_edit = Editar usuario
delete = Eliminar
move_bottom = Mover hasta abajo
ui_converter = Problema de conversi\u00F3n
deny_message = Indique los motivos de su negativa.
hour = Hora
channel_deleted = Canal eliminado
ok = OK
error_removing_portlet = Error al borrar el portlet ({0}).
ks_entry_fingerprintmd5 = Huella digital (MD5)
creation_mail_message_html = Hola, {0}:<br/><br/>Por favor, encuentre a continuaci\u00F3n su informaci\u00F3n de inicio de sesi\u00F3n para {1} ({2}): <br/><br/> <li>Usuario: {3} </li><li>Enlace de renovaci\u00F3n de contrase\u00F1a: {4}</li><br/><br/>\u00A1Hasta pronto!
exception_instance_not_found = El usuario {0} no tiene un entorno configurado para el dominio {1}
ks_error_could_not_write_certificate_file = Imposible escribir un archivo asociado para este certificado.
account_creation = Crear mi cuenta
folder_in = Carpeta de entrada
ks_keystoredetailstab = Detalles del keystore
exception_unknown_layout_type = Tipo de trazado desconocido
button_edit = Editar
show_triggers = Ver desencadenantes
entry_answer = Respuesta
select_one = Elija una opci\u00F3n
rigth = Derecha
milestone = Fecha provisional de finalizaci\u00F3n
error_saving_portal = Error al guardar el portal ({0}).
info_partner_saved = Socio {0} guardado
partner_import_file = Importar un archivo de socios (*.xls, *.xlsx)
writer = Redactor
exception_task_type_not_found = No se puede encontrar el tipo de tarea {0}
entry_text = Texto
account_creation_confirm = Su cuenta se ha creado correctamente. En breve recibir\u00E1 un correo electr\u00F3nico de confirmaci\u00F3n en la direcci\u00F3n:
mark_as_read = Marcar como le\u00EDdo
report_no_data = SIN DATOS
about_short = Qui\u00E9nes somos
ks_publickeytab = Certificado de clave p\u00FAblica
partner_name = Nombre del socio
about = Acerca de TradeXpress Evolution
information_system = Informaci\u00F3n del sistema
warn_language_mandatory = La configuraci\u00F3n del idioma es obligatoria.
edit_mail = Editar correo electr\u00F3nico
error_removing_row = Error al borrar la l\u00EDnea ({0}).
exception_message = Se ha producido un error. \n Por favor, int\u00E9ntelo de nuevo m\u00E1s tarde o p\u00F3ngase en contacto con su administrador.\n ({0})
companies = Clientes
creation_mail_subject = Creaci\u00F3n de su cuenta de usuario para el sitio {0}
delete_select = \u00BFDesea eliminar definitivamente los elementos seleccionados?
error_removing_page = Error al borrar la p\u00E1gina ({0}).
identification = Identificaci\u00F3n
ks_entry_alias_optional = Opcional a menos que el alias no pueda determinarse autom\u00E1ticamente.
readonly = Solo lectura
doc_uploaded = Documento importado: {0}
exception_failing_ACE_instanciation = Se ha producido un error al instanciar ACEs para: {0}
number_reviewed_messages = N\u00FAmero de mensajes revisados
partner_view = Socio {1} ({0})
state = Estado
exception_user_not_partner = El usuario {0} del grupo {1} no es ni socio ni cliente.
FATAL_icon = fa fa-bomb
events = Desencadenantes
tva = IVA
create_notifications = Crear un anuncio
deny = Rechazar
edit = Edici\u00F3n
edit_record = Editar
exception_import_portal_not_empty = El portal no est\u00E1 vac\u00EDo para el entorno {0}.
hosts = Anfitriones
bad_account_or_password = Nombre de usuario desconocido o contrase\u00F1a incorrecta.
error_getting_portlet_content = Portlet {0}, Contenido: {1}: {2}
entry_checkbox = Casilla de verificaci\u00F3n
monitoring = Actividad
move_top = Mover hasta arriba
execute = Ejecutar
accept = Aceptar
labels = Etiquetados
menu_campaigns = Incorporaci\u00F3n
exception_more_than_one_instance = El usuario {0} asociado al cliente {1} tiene m\u00E1s de un entorno configurado para el dominio {2} ({3}).
info_host_removed = Dominio {0} eliminado
rendered = Mostrar
WARN_color = #ffb752
duns = RCS-RCM
date_last_authentication = \u00DAltima conexi\u00F3n
actions = Acciones
end_date = Fecha de fin
contact_admin = P\u00F3ngase en contacto con su administrador.
task_edit = Editar una tarea
other_variable = Variable libre
document = Documento
contact_mode = Medios
view_all = Ver todo
expand_all = Desplegar todo
info_user_removed = Usuario {0} eliminado
lang_french = Franc\u00E9s
ERROR_label = ERROR
menu_security_companies = Clientes
ks_remove_linked_partner_integrity = Operaci\u00F3n bloqueada: el certificado sigue vinculado a estos grupos: {0}.
default = por defecto
menu_security_partners = Socios
import_cert_pkcs11 = PKCS#11 (RGS-2*)...
import_cert_pem_cer_crt=PEM/CER/CRT
import_cert_pkcs7=PKCS#7
reminder_number = N\u00FAmero de recordatorios
property_not_integer = debe ser un n\u00FAmero entero.
ks_type = Tipo
disabled = Desactivado
edit_notifications = Editar anuncio
exception_invalid_gson = Archivo Gson no v\u00E1lido
report_partners_status = ESTADO DE LOS SOCIOS
warn_only_parent_empty = Solo una p\u00E1gina principal puede estar vac\u00EDa.
email = Correo electr\u00F3nico
ui_invalid_size = Tama\u00F1o de archivo incorrecto
menu_process = Proceso
process_msg = Procesar
processing = Estado
unprocesses_message = mensaje(s) no procesado(s)
validate = Validar
creation_mail_message_text = Hola, {0}:\n\nPor favor, encuentre a continuaci\u00F3n sus datos de acceso para el sitio {1} ({2}): \n\n- Usuario: {3}\n- Enlace de renovaci\u00F3n de contrase\u00F1a: {4}\n\n\u00A1Hasta pronto!
variables = Variables
languages = Idiomas
warn_select_role = Seleccione una funci\u00F3n.
ks_modulus_length = Longitud del m\u00F3dulo (bits)
doc_upload_duplicate = Duplicado: {0}
refresh = Actualizar
started = Inicio
partner_save_error = Error al guardar el socio: {0}.
ks_unrecognized = Los datos importados no se pueden cargar como keystore PKCS11, PKCS12 o JKS.\n - Compruebe la contrase\u00F1a.\n - Compruebe el tipo de archivo. \n - Intente cambiar los archivos \"Unlimited Strength Jurisdiction Policy Files\" de su JRE.
tout = Todos
number_connexion = N\u00FAmero de conexiones
about_contact_email = Correo electr\u00F3nico de contacto
account_confirmation = Confirmaci\u00F3n de creaci\u00F3n de cuenta
channel_create_error = Error al crear el canal.
confirm = Confirmar
warn_no_type_selected = Seleccione el tipo de tarea.
is_expired = ha expirado.
ks_upload_select = Keystore (*.jks, *.p12, *.pfx)
partners = Socios
entry_select_excel = Elegir un archivo Excel (*.xls, *.xlsx)
keyword_$url = URL de la aplicaci\u00F3n
ks_entry_version = Versi\u00F3n
campaign_name_short = Nombre corto
country = Pa\u00EDs
partner_edit = Editar socio {1} ({0})
documents = Documentos
administration = Administraci\u00F3n
accounting = Contacto cliente
ks_entry_notafter = V\u00E1lido hasta el
ks_upload = Importar un keystore
menu_security = Seguridad
APPROVED_color = #87b87f
channel_type_mandatory = Debe elegir un tipo de canal antes de hacer clic en A\u00F1adir.
doc_edit = Editar un documento
view = Ver
exception_duplicate_login = El inicio de sesi\u00F3n ya existe en AIO. No se puede crear.
domain_edit = Modificar un dominio
details = Detalles
test_upload = Carga de un archivo de prueba.
confirmation_mail_subject = Confirmaci\u00F3n de creaci\u00F3n de cuenta.
empty_page = P\u00E1gina vac\u00EDa
keyword_$campaignvariable = Variable libre
error_removing_template = Error al borrar plantilla ({0}).
version_technical = Referencia t\u00E9cnica
error_file_not_found = Archivo no encontrado: {0}.
menu_statistics = Generaci\u00F3n de informes
query = Filtrar
user_logged = Ya conectado
exception_user_not_specified = No se especifica el usuario.\nexception_backoffice_user={0} es un usuario de back-office.

no_data = Sin datos
channel_modify = Modificar un canal
duplicate = Duplicar
error_user_without_primary_group = No se ha encontrado ning\u00FAn grupo principal para el usuario.
modification = Fecha de modificaci\u00F3n
date_read = Fecha de lectura
APPROVED_label = OK
info_portlet_saved = Se ha guardado el contenido del portlet.
channels = Canales
entry_remove = Eliminar
switch_off = Conectado como
add_left = A\u00F1adir a la izquierda
warn_template_with_associated_host = Determinados dominios est\u00E1n asociados a una plantilla.
partner_add = A\u00F1adir socio
name = Apellido
partner_field_configuration = Campos de entrada libre de socios
campaign = Campa\u00F1a
page = P\u00E1gina
installation_detail = Programas instalados
task_types_revision = Ver la definici\u00F3n de la tarea
error_exporting_partner = Error al exportar socios ({0}).
preview = Vista previa
configuration_portlet = Configuraci\u00F3n del portlet
partner_address = Direcci\u00F3n postal
import = Importar
domain_create = Crear un dominio
keystores = Keystores
sooner_shipment_date = Fecha de env\u00EDo m\u00E1s temprana
error_duplicate_template = La plantilla {0} ya existe.
description = Descripci\u00F3n
managed_by_order_issuer = Gestionado por ordenante
error_editing_portlet = Error al editar el contenido del portlet ({0}).
to_edit=Editar
campaign_description = Descripci\u00F3n
task_create = Crear una tarea
exception_multiple_principal_groups_user = El usuario {0} tiene m\u00E1s de un grupo principal ({1}).
complementary_info = Informaci\u00F3n adicional
select_instance = Seleccionar un entorno
ks_error_could_not_find_nor_create_parent_for_friendly_name = Imposible encontrar o crear un padre para el apellido {0}.
day_of_week = D\u00EDas de la semana
severity = Gravedad
exception_removed_instance = El entorno {0} ha sido eliminado.
hours = Horas
info_partner_removed = Socio {0} eliminado
warn_locked_file = Archivo bloqueado
yes = S\u00ED
start = Iniciar
keyword_$campaigncontactemail = Correo electr\u00F3nico de contacto de la campa\u00F1a
layout = Dise\u00F1o
confirm_instance_disable = Se desactivar\u00E1n todos los servicios del entorno (integraci\u00F3n, inducci\u00F3n, etc.) \u00BFEst\u00E1 seguro de que desea desactivar este entorno?
OK_color = #87b87f
exception_json_export_error = Error de exportaci\u00F3n JSON: {0}
ks_cannot_determine_alias = No se puede determinar qu\u00E9 alias utilizar. Alias encontrado, con separador ;: {0}
recipients = Destinatarios
comment = Comentario
fullname = Nombre y apellidos
channel_add_type = Tipo de canal a a\u00F1adir
exception_task_change_parent = Error en el algoritmo changeParent.
confirmation_mail_message_text = Hola, {0}:\n\nTu cuenta se ha creado correctamente. Copia y pega esta direcci\u00F3n en tu navegador preferido para confirmar la creaci\u00F3n de tu cuenta: {1}.\n\n\u00A1Hasta pronto!=======
exception_user_no_associated_company = El usuario {0} del socio {1} no est\u00E1 vinculado a ning\u00FAn cliente.
ks_library = Librer\u00EDa
usage = Utilizaci\u00F3n
ks_cannot_load_keystore = No se puede cargar el keystore. Compruebe la configuraci\u00F3n. Consulte a un administrador.
none = Ninguno
doc_upload = Cargar
ks_entry_subject = Entregado en
repository = Repositorio
type = Tipo
required = Obligatorio
user_new = Crear usuario
active_partners = SOCIOS ACTIVOS
docs_selected = Documentos seleccionados
logout = Desconexi\u00F3n
seconds = Segundos
client_order = N.\u00BA pedido
doc_name = Nombre del documento
enable = Activar
permissions = Permisos
info_partner_saved_detail = Socio {0} guardado: {1}
remindpassword = Recordatorio de contrase\u00F1a
switch_user = Suplantar identidad
sort_order_ascending = Ascendente
fax = Fax
ks_no_slot_index_selected = Debe seleccionarse una ranura de \u00EDndice.
dashboard = Tablero de control
entry_new = Nueva entrada
error_switching_user = Error durante la suplantaci\u00F3n de identidad ({0}).
channel_save_error_null = Error al guardar el canal (el canal es nulo).
warn_app_template_deleting=La plantilla {0} no se puede borrar.
partner_comment = Comentario
role_min = rol
later_shipment_date = \u00DAltima fecha de env\u00EDo
linked_certificates = Certificados vinculados
exception_task_import_parents = Problema al importar taskParent para {0}.
error_removing_host = Error al borrar el dominio ({0}).
menu_security_order_issuers = Ordenantes
ks_config_require_usage = El uso del keystore es obligatorio.
exception_file_upload_unknown_request = El par\u00E1metro de consulta es desconocido: {0}
phone = Tel\u00E9fono
menu_system = Sistema
reset = Restablecer
exception_task_import = Problema al importar tareas para {0}.
ks_entry_issuer = Entregado por
style = Estilo
channel_add = A\u00F1adir un canal
error_removing_company = Error al borrar cliente ({0}).
ks_certificationpath = Ruta de certificaci\u00F3n
ks_provide_friendly_name = Debe rellenarse el apellido.
NONE_label = NINGUNO
ks_error_multiple_parent_for_friendly_name = Estado inconsistente: varios padres encontrados para el apellido {0}.
info_file_removed = Archivo eliminado
roadmap = Roadmap
contact_us = CONTACTO
permissions_dialog_campaign_title = A\u00F1adir socios a la campa\u00F1a
error_importing_partner = Error al importar socios ({0}).
exception_portlet_cloning = Problema clonando portlet {0}.
name_executable = Nombre del ejecutable
enabled = Activado
weak = d\u00E9bil
invalid_file_size = Tama\u00F1o de archivo no v\u00E1lido
exception_duplicate_role = Rol duplicado: {0}.
theme = Tema
copy = Copiar
error_saving_permission = Error al guardar el permiso ({0}).
start_date = Fecha de inicio
channel_name = Nombre del canal
editor = Editor
report_partners_completion_campaigns = ONBOARDING
ks_alias_not_found = Alias no encontrado: {0}. Alias encontrado, con separador ;: {1}
indexing = Volver a indexar
menu_instances = Entornos
display = Mostrar
export_portal = Portal de exportaci\u00F3n
warn_portlet_content_not_found = No se ha encontrado el contenido del portlet con id. {0}.
entry_answers = Respuestas
exception_partner_associated = {0} socio(s) relacionado(s)
info_instance_saved = El entorno {0} ha sido guardado.
channel_desc = Descripci\u00F3n
warn_host_not_removed = {0} NO ha sido eliminado ({1} entorno(s) asociado(s): CODE= {2}).
filter = Filtro
help = Ayuda
task_types = Tipo de tarea
description_short = Breve descripci\u00F3n
exception_unavailable_instance = El sitio {0} no est\u00E1 disponible temporalmente.
identification_validator = La identificaci\u00F3n debe contener caracteres alfanum\u00E9ricos o \"_\".
menu_dashboard = Tablero de control
task_to_complete = Tarea a realizar
partner_saved = Guardar el socio: {0}.
rename = Renombrar
chat = Chat
exception_import_error_during_cloning = Error al duplicar el portal
iframe_error_message = No se puede mostrar el sitio porque no lo permite.
AllUsers = Todos los usuarios de un socio
error_removing_file = Error al borrar el archivo ({0}).
doc_uploaded_update = Documento actualizado
error_creating_template_archive = Error al crear plantilla ({0}).
statistics = Estad\u00EDsticas
date = Fecha
warn_user_missing = No se encuentra el usuario (compruebe el par\u00E1metro uuid).
entry_edit_question = Editar una pregunta
keyword_$contact = Contacto
error_saving_portal_portlet_missing = Falta la configuraci\u00F3n de un portlet.
partner_identification = Identificaci\u00F3n
partner_send_mail = Enviar informaci\u00F3n de conexi\u00F3n
upload_file = Carga del archivo
channel_ftp_port = Puerto
notification = Notificaci\u00F3n
template_edit = Editar la plantilla
menu_process_reports = Informes
page_noselection = Ninguna p\u00E1gina seleccionada
create = Crear
day = D\u00EDa
campaign_general = General
ks_uploaddate = Fecha de creaci\u00F3n
user_company = Usuarios cliente
FATAL_color = #cc1e00
ks_slotIndex = \u00CDndice de ranuras
ks_label=Etiqueta
tasks_noselection = Ninguna tarea seleccionada
minutes = Minutos
error_saving_host = Error al realizar el guardado del dominio ({0}).
format = Formato
task_last_completed = \u00DAltima tarea realizada
recent_business = Negocios recientes
archive = Archivo
menu_domain = Dominios
channel_duplicate_ok = Canal duplicado
home_message = Acelere sus implantaciones EDI y B2B al tiempo que reduce sus costes con GCI Community Management
move_rigth = Mover a la derecha
partner_new = Nuevo socio
add_certificate = A\u00F1adir un certificado
reminder_content = Cuerpo del recordatorio
document_children_policy = Gestionar los enlaces padres-hijos
report_completed = Completado
FATAL_label = CR\u00CDTICO
error_profile_name_mandatory = El nombre del perfil es obligatorio.
send = Enviar
exception_role_has_pages = El rol {0} a {1} tiene p\u00E1gina(s) asociada(s).
deploy_process = Desplegar el proceso
deployment=Desplegar
week = Semana
about_contact = P\u00F3ngase en contacto con nosotros
configuration = Configuraci\u00F3n
instances = Entornos
ui_file_limit = N\u00FAmero m\u00E1ximo de archivos alcanzado
exception_sending_mail_partner = Se ha producido un error al enviar correos electr\u00F3nicos a los socios: {0}.
insert = Insertar
numeric = Digital
site_optimized_for_ie9 = Este sitio est\u00E1 optimizado para Internet Explorer 9.
info_no_portlet_defined = El portlet no ha sido definido para esta p\u00E1gina.
error = Error
error_exporting_instance = Error al exportar el entorno: {0}.
campaign_name = Apellido
general = General
security = Seguridad
test_send_mail = Enviarse un correo electr\u00F3nico
move_up = Mover hacia arriba
channel_type = Tipo
entry_new_answer = Opci\u00F3n
ks_entry_notbefore = V\u00E1lido desde el
campaign_date_end = Fecha de fin
value = Valor
channel_type_select = Tipo de canal...
authentication = Autenticaci\u00F3n
PENDING_icon = fa fa-cog fa-spin
keyword_$company = Nombre de la empresa que lanza la campa\u00F1a
clear = Vaciar
info_user_saved = Usuario {0} guardado
import_portal = Importar portal
backoffice = Back Office
error_saving_organization = Error: el elemento ya existe.
move_down = Mover hacia abajo
invoice = N.\u00BA de factura
ks_no_key_found = No se ha encontrado ninguna clave en el keystore.
user = Usuario
account = Mi cuenta
confirm_task_result_reset = Los resultados de esta tarea se borrar\u00E1n. \u00BFDesea continuar?
doc_upload_disabled = No puede realizar esta acci\u00F3n porque la tarea est\u00E1 bloqueada (puede que haya una campa\u00F1a en curso).
add_child_page = A\u00F1adir p\u00E1gina hija
trf_generic = formato gen\u00E9rico
cut = Cortar
creation = Fecha de creaci\u00F3n
trf_required_template_uri = El URI de la plantilla es obligatorio.
trf_required_document_type = El tipo de documento o su colecci\u00F3n es obligatorio.
menu_message = Correo electr\u00F3nico
task_adv_search_simple = Simple
error_save_user_no_env = Debe seleccionarse el entorno para notificar al usuario.
users = Usuarios
port = Puerto
roles = Roles
paste = Pegar
channel_other = Otros
upload_invalid_file = Formato de archivo no v\u00E1lido
task_adv_search_avancee = Avanzada
boolean_true = Verdadero
regenerate_all = Regenerar todo
channel_test = Probar
regenerate_all_password = Regenerar todas las contrase\u00F1as
template_saved = Plantilla guardada
permission_required = Selecci\u00F3n obligatoria
menu_notifications = Anuncios
boolean_false = Falso
upload_file_limit = N\u00FAmero m\u00E1ximo de archivos alcanzado
label_search = Especifique su b\u00FAsqueda
template_import = Importar la plantilla
secure = Seguro (HTTP/S)
user_search = Afinar su b\u00FAsqueda introduciendo el nombre de la persona que realiz\u00F3 los cambios.
parameters = Par\u00E1metros
parameter=Par\u00E1metro
keystore = Keystore
channel_ftp_username = Nombre de usuario
allow_user_managing_tab = Permitir a los usuarios gestionar esta pesta\u00F1a
from_address = Emisor
ks_remove_timestamp_server_integrity = Operaci\u00F3n bloqueada: certificado a\u00FAn referenciado en un servidor de marcado temporal.
upload_invalid_size = Tama\u00F1o de archivo no v\u00E1lido
preview_not_available = Vista previa no disponible
menu_notification = Anuncio
task_adv_search = B\u00FAsqueda por defecto
generate_new_password = Generar un correo electr\u00F3nico de restablecimiento de contrase\u00F1a
error_saving_host_not_unique = Ya existe un dominio con el mismo nombre
timeout = Tiempo de espera
alphanumeric_underscore = Solo se admiten caracteres alfanum\u00E9ricos y gui\u00F3n bajo
url = Url
warn_instance_code_already_used = El c\u00F3digo del entorno ya est\u00E1 en uso.
boolean_select = --Seleccione--
channel_ftp_hostname = Nombre de host del servidor
ks_usage_mismatch = El uso {0} debe corresponder al uso padre {1}.
contacts = Contactos
purge = Purga
exception_access_denied = Acceso denegado
exception_admin_url_portal = Introduzca un nombre de usuario diferente o p\u00F3ngase en contacto con su administrador para obtener la URL de acceso a su portal.
info_company_created = Cliente {0} creado
COMPLETED_icon=fa fa-check-circle-o
COMPLETED_color=#87b87f
COMPLETED_label=COMPLETADO
STARTING_icon=fa fa-times-circle-o
STARTING_color=#bebfbb
STARTING_label=INICIO
STARTED_icon=fa fa-times-circle-o
STARTED_color=#bebfbb
STARTED_label=COMENZADO
STOPPING_icon=fa fa-exclamation-circle
STOPPING_color=#cc1e00
STOPPING_label=PARADA
STOPPED_icon=fa fa-exclamation-circle
STOPPED_color=#cc1e00
STOPPED_label=DETENIDO
FAILED_icon=fa fa-exclamation-circle
FAILED_color=#cc1e00
FAILED_label=FALLIDO
ABANDONED_icon=fa fa-exclamation-circle
ABANDONED_color=#cc1e00
ABANDONED_label=RETIRADO
UNKNOWN_icon=fa fa-exclamation-circle
UNKNOWN_color=#d4d4d4
UNKNOWN_label=DESCONOCIDO
linked_document_import_selection = Seleccionar e importar uno o varios archivos
linked_document_import = Importar archivo adjunto

# Organization
organization_code=Identificador
organization_collaborativeId=Id. GCN (gid)
organization_subscriberId=Identificador de facturaci\u00F3n Generix
organization_vat=N\u00FAmero de IVA intracomunitario
organization_registration=N.\u00BA SIREN
organization_duns=RCS-RCM
organization_phone=_phone
organization_fax=_fax
organization_email=_email
organization_web=_web
organization_orderContact=Contacto
organization_orderPhone=Tel\u00E9fono
organization_orderFax=Fax
organization_orderEmail=Correo electr\u00F3nico
organization_client=_client
organization_profile=Perfil
organization_gcnSubscriber=Cliente Generix
organization_shareCapital=Capital social
organization_registerName=Nombre legal de la empresa
organization_legalStructure=Estructura jur\u00EDdica

organization_id=_id
organization_name=C\u00F3digo
organization_fullname=Apellido
organization_creation=_creation
organization_modification=_modification
organization_comment=Descripci\u00F3n
organization_description=_description
organization_userGroupAssociations=_userGroupAssociations
organization_location=_location
organization_parent=_parent
organization_children=_children
organization_logoSmall=_logoSmall
organization_logoMedium=_logoMedium
organization_logoLarge=_logoLarge
organization_freeText01=_freeText01
organization_freeText02=_freeText02
organization_freeText03=_freeText03
organization_freeText04=_freeText04
organization_freeText05=_freeText05
organization_freeText06=_freeText06
organization_freeText07=_freeText07
organization_freeText08=_freeText08
organization_freeText09=_freeText09
organization_freeLongText01=_freeLongText01
organization_freeLongText02=_freeLongText02
organization_freeBoolean01=_freeBoolean01
organization_freeBoolean02=_freeBoolean02
organization_freeDouble01=_freeDouble01
organization_freeDouble02=_freeDouble02
organization_freeDate01=_freeDate01
organization_freeDate02=_freeDate02
organization_freeViewConfiguration=Campos abiertos
organization_freeViewProfile=Perfil

organization_address_address=_address.address
organization_address_addressComplement=Complemento de direcci\u00F3n
organization_address_country_country=C\u00F3digo de pa\u00EDs ISO-2
organization_address_country_displayCountry = Pa\u00EDs
organization_address_country_iSO3Country =C\u00F3digo de pa\u00EDs ISO-3
organization_address_addressLine=Direcci\u00F3n
organization_address_streetName=Direcci\u00F3n
organization_address_postalCode=C\u00F3digo postal
organization_address_city=Ciudad
organization_address_country=Pa\u00EDs

gcnSubscriberIdentification_error = El identificador de facturaci\u00F3n Generix debe contener un m\u00E1ximo de 64 caracteres alfanum\u00E9ricos
gcn_identification_group = Generix Collaborative Network
generate_new_password_confirm = \u00BFConfirma la generaci\u00F3n de un correo electr\u00F3nico de restablecimiento de contrase\u00F1a?
invalid_generix_billing_id = El identificador de facturaci\u00F3n Generix introducido no es v\u00E1lido
generate_gcn_subscriber = Generar identificador
instance_code_validator = El c\u00F3digo debe constar de tres d\u00EDgitos o letras may\u00FAsculas.
history = Historial
gcnSubscriberIdentification = Identificador de facturaci\u00F3n Generix
exception_gcn_subscriber_id_duplication = El identificador de facturaci\u00F3n de Generix ya existe: {0}
max_size_of_linked_document = Tama\u00F1o m\u00E1ximo de los archivos adjuntos

#Statuts
NONE=No le\u00EDdo
PENDING=Borrador
APPROVED=Aprobado
ACCEPTED=Confirmado
ACCEPTED_WITH_AMENDMENT=Confirmado con modificaciones
REFUSED=Rechazado
SENT=Expedido
ACQUITTED=Reconocido
ARCHIVED=Archivado
REMOVED=Eliminado
ERROR=Error
WARNING=Warning
DUPLICATE=Duplicado
TO_VALIDATE=Por validar
UPDATED=Actualizado
READ=Le\u00EDdo
CANCEL=Cancelado
FATAL=Error del sistema
SENT_PARTIALLY=Expedido parcialmente
INVOICED=Facturado
ANSWERED=Respondido
TIMEOUT=Caducado
TO_REMOVE=Por suprimir
CLOSED=Cerrado
DELIVERED=Entregado

#Stage
Stage_CORRECT=Dematerializado
Stage_UNDEFINED=No dematerializada
Stage_ERROR=Error
Stage_UNKNOWN = Desconocido
folder = Carpeta
exit = Salir
document_type = Tipo de documento
edition = Edici\u00F3n
logistic = Log\u00EDstica
enabled_linked_document_import = Activar la importaci\u00F3n de documentos adjuntos
error_saving = Error de guardado ({0})
find = Buscar
error_save_user_no_partner = La selecci\u00F3n de socio es obligatoria.
exception_gcn_id_duplication = El identificador GCN ya existe: {0}
delete_select_single = \u00BFDesea eliminar definitivamente el elemento seleccionado?
Cascade=Depurar documentos hijos
Detach=Separar los documentos hijos
Ignore=Ignorar los documentos padres
doc_purge_children_policy=V\u00EDnculos padres-hijos

# Portlet Categories
EDOCUMENT=INVOICING SERVICES
RTE=SERVICIOS EDI
GCN=COLLABORATIVE NETWORK
SMARTPDF=SUPPLIER PORTALS
DOC=KPI

java_lang_Boolean = Booleano
themes = Temas
portlet_invoice_refused_manually = FACTURA: Rechazar manualmente
portlet_asn_ship = ASN: Remitir
ANSWERED_ICON = fa fa-undo fa-fw fa-rotate-90
portlet_referentiel_produits_general_description = Repositorio/Productos/General/Descripci\u00F3n
DELIVERED_STYLE = status_green
agreement = CGU
autoGenerationOfSSCC = SSCC generado
bank_account_bic_error_required = El c\u00F3digo BIC es obligatorio
organization_autoGenerationOfSSCC = SSCC generado
logistic_sscc = SSCC
ARCHIVED_STYLE = status_green
contextual_validation_user_scope_user = El usuario {0} no existe
REFUSED_MANUALLY_STYLE = defecto
R = Receptor
S = Emisor
agreement_file_not_found = No se puede encontrar las CGU {0} para el idioma {1}
portlet_referentiel_taxes = Repositorio/Impuestos
SENT_ICON = fa fa-paper-plane-o
correct = Corregir
Noname = \u00A1SIN NOMBRE!
imenu_integration_recent_business = Procesos recientes
java_util_Date = Fecha
users_email = Usuarios/correo electr\u00F3nico
organization_gs1 = C\u00F3digo de empresa GS1
contextual_validation_user_scope_partner_id = El n\u00FAmero de identificaci\u00F3n del socio {0} y del per\u00EDmetro {1} debe ser diferente para el usuario {2}
organization_end = Fecha de fin
root = Ra\u00EDz
INVOICED_STYLE = status_green
UNDEFINED_STYLE = defecto
portlet_pack = Paquete
organization_start = Fecha de inicio
imenu_security = Socios
bank_account_bic = BIC
portal_general = General
contextual_validation_user_scope_partner = El socio {0} no pertenece a {1}
logistic_sscc_auto = SSCC autom\u00E1tico
TO_REMOVE_ICON = fa fa-archive
demat_partner_dialog_title = Historial de modificaciones
organization_serialReference = N\u00FAmero secuencial
BLOCKED_STYLE = status_orange
imenu_repository_documents = Documentos
banking_partner = Socio bancario
new = Nuevo
logistic_extension_required = El car\u00E1cter de extensi\u00F3n es obligatorio porque est\u00E1 activada la generaci\u00F3n autom\u00E1tica de SSCC.
OK_STYLE = status_green
RESOLVED = Resuelto
REFUSED_MANUALLY = Rechazado manualmente
OK = OK
UNKNOWN = Desconocido
bank_accounts = Cuentas bancarias
imenu_integration = Proceso
freetext_details = Campos libres/Detalles
portlet_invoice_remove = FACTURA: Suprimir
REMOVED_STYLE = defecto
dictionary = Diccionarios empresariales
ERROR_STYLE_STAGE = status_red
logistic_gs1_error_format_message = El c\u00F3digo nacional unificado de proveedor (GS1) debe tener entre 7 y 10 d\u00EDgitos.
channel_auth_userSearchBase = User search base
bank_account_delete_confirm = \u00BFEst\u00E1 seguro de que desea eliminar el IBAN de la moneda?
UNKNOWN_ICON = fa fa-remove
APPROVED_STYLE = info
ERROR_STYLE = status_red
UPDATED_STYLE = info
rte = RTE
INTEGRATED_STYLE = status_green
xPath = xPath
portlet_referentiel_produits_other = Repositorio/Productos/Otros
imenu_portal_einvoice = Factura electr\u00F3nica
new_file = Nuevo archivo
imenu_repository_templates = Plantillas
logistic_gs1_company_prefix = C\u00F3digo de empresa GS1 (CNUF)
INTEGRATED = Integrado
gcnIdentification_error = El id. GCN debe contener un m\u00E1ximo de 64 caracteres alfanum\u00E9ricos
REFUSED_STYLE = status_red
TO_VALIDATE_ICON = fa fa fa-pause fa-fw
PAID = Pagado
DEMAT_ERR_STYLE = status_red
UNDEFINED_STYLE_STAGE = defecto
confirmScopeSelection = Perder\u00E1 su selecci\u00F3n perimetral de socios
logistic_serial_reference_required = El n\u00FAmero secuencial es obligatorio porque est\u00E1 activada la generaci\u00F3n autom\u00E1tica de SSCC.
TO_CORRECT = Por corregir
CLOSED_STYLE = defecto
Development = Desarrollo
TO_PAY = Por pagar
TIMEOUT_ICON = fa fa-remove fa-fw
javax_xml_datatype_XMLGregorianCalendar = Fecha XML
TO_CORRECT_STYLE = status_yellow
NONE_STYLE = status_yellow
logistic_gs1_company_not_provided = Introduzca el C\u00F3digo Nacional Unificado de Proveedor.
IN_DISPUTE = En litigio
confirm_file_delete = \u00BFRealmente desea borrar {0}?
contextual_validation_user_scope_user_cpy = El usuario {0} no pertenece a {1}
bank_account_iban_error_notValid = El IBAN no es v\u00E1lido
IN_DISPUTE_STYLE = status_yellow
TIMEOUT_STYLE = status_red
portlet_referentiel_produits_general_net_price = Repositorio/Productos/General/Precios netos
CANCEL_ICON = fa fa-times
replace = Sustituir
READ_STYLE = info
Production = Producci\u00F3n
UNKNOWN_STYLE = status_yellow
ks_error_could_not_extract_certificate = No se han podido extraer los certificados contenidos en el archivo.
portlet_referentiel_global_allowances_charges = Repositorio/Descuentos y gastos globales
Acceptance = Receta
imenu_repository = Repositorio
bank_account_bic_error_notValid = El c\u00F3digo BIC consta de 8 u 11 caracteres
imenu_general = General
banking = Banca
SENT_PARTIALLY_STYLE = status_yellow
organization_bankAccount_date = Fecha de creaci\u00F3n
agreement_default_required = El idioma por defecto {0} (pesta\u00F1a CGU) debe estar asociado a las CGU.
upload_with_conflict_title = Importar un archivo
SYNTAX_ERR_STYLE = status_red
SENT_PARTIALLY_ICON = fa fa-paper-plane
WARNING_STYLE = status_yellow
SHIPPED_STYLE = status_green
channel_toggle_linked_chanel = Error al modificar el canal. Todav\u00EDa hay socios con un alias en este canal:<br/>{0}
BLOCKED = Bloqueado
organization_bankAccount_iban = IBAN
organization_bankAccount_currency = Moneda
portlet_invoice_diagnostic = FACTURA: Diagnosticar
ACCEPTED_WITH_AMENDMENT_STYLE = info
ERROR_ICON = fa fa-remove
integrationnotification = Recibir el informe de integraci\u00F3n de procesos
java_lang_Integer = Entero
portlet_invoice_import = FACTURA: Importar
organization_bankAccount_bic = BIC
REFUSED_ICON = fa fa-exclamation-triangle
logistic_missing_mandatories_message = Por favor, rellene toda la informaci\u00F3n requerida, ya que la generaci\u00F3n autom\u00E1tica de SSCC est\u00E1 activada.
SYSTEMATIC = Generaci\u00F3n sistem\u00E1tica
FATAL_STYLE = status_red
imenu_campaigns = Incorporaci\u00F3n
SHIPPED = Enviado
warn_locked_folder = Carpeta bloqueada
ignored = Ignorados
logistic_gs1_company_required = El c\u00F3digo GS1 es obligatorio porque se activa la generaci\u00F3n autom\u00E1tica de SSCC.
imenu_messaging = Mensajer\u00EDa
#Portlets Names
Calendar=CalendarDeprecated
CarrefourInvoiceEdition=CarrefourInvoiceDeprecated
CarrefourInvoice=CarrefourInvoice
DocumentBarChart=BarChart
DocumentBirt=Birt
DocumentCalendar=DocumentCalendarDeprecated
DocumentCounter=Counter
DocumentLineChart=LineChart
archiveimv3 = Archivo IMV3
Documentation=DocumentationDeprecated
DocumentPieChart=PieChart
EDocuments=EdocumentsDeprecated
Factor=FactorDeprecated
FeedReader=FeedReaderDeprecated
Monitoring=MonitoringDeprecated
OrderLine=OrderLineDeprecated
Penalty=FranprixPenaltyDeprecated
PlanningSchedule=PlanningScheduleDeprecated
SafranInvoice=SafranInvoiceDeprecated
Survey=SurveyDeprecated
CompleteEdition=CompleteEditionDeprecated

bank_account_currency = Moneda
NO_GENERATION_IF_EXISTS = No se genera si el archivo existe
agreement_instance_required = Entorno CGU obligatorio
portlet_invoice_export_list = FACTURA: Exportar lista
logistic_sscc_fieldset = N\u00FAmero SSCC
DEMAT_ERR = Err. demat.
ks_error_during_file_reading = Se ha producido un error al leer el archivo.
agreement_lang_required = El idioma de las CGU es obligatorio
READ_ICON = fa fa-eye
TO_REMOVE_STYLE = status_yellow
error_duplicate_configuration = La configuraci\u00F3n ya existe
logistic_serial_reference = N\u00FAmero secuencial
imenu_portal_o2c = Order to cash
portlet_indexdata_export = INDEXDATA: Exportar
channel_delete_linked_chanel = Error al borrar el canal. Todav\u00EDa hay socios con un alias en este canal:<br/>{0}
SENT_STYLE = info
exception_exchange_associated = {0} intercambios(s) autorizado(s) relacionado(s)
PENDING_STYLE = status_yellow
ACCEPTED_STYLE = info
FORCED = Forzado
ACQUITTED_STYLE = status_green
java_lang_String = Texto
warn_creation_fail = Creaci\u00F3n fallida
ANSWERED_STYLE = info
classificationPlan = Plan de clasificaci\u00F3n
portlet_referentiel_produits_remise_charge = Repositorio/Productos/Descuento y cargo
logistic_missing_sscc_message = Rellene todos los campos obligatorios
ks_use_start_date = Puede utilizarse desde
REFUSED_STYLE_STAGE = status_red
line_comment = Comentario sobre la l\u00EDnea
portlet_referentiel_carrier = Repositorio/Transportador
my_bank_accounts = Mis cuentas bancarias
NONE_ICON = fa fa-envelope-o
java_lang_Class = Clase
logistic_extension = Car\u00E1cter de extensi\u00F3n
ks_use_end_date = Puede utilizarse hasta
PAID_STYLE = status_green
REPLACE_IF_EXISTS = Generaci\u00F3n con sustituci\u00F3n
channel_auth_groupSearchBase = Group search base
imenu_repository_dictionary = Diccionario empresarial
new_folder = Nueva carpeta
processed = Procesado
UNKNOWN_STYLE_STAGE = status_yellow
DUPLICATE_STYLE = status_yellow
History = RoquetteHistory
Unknown = \u00A1DESCONOCIDO!
bank_account_iban = IBAN
channel_auth_groupSearchFilter = Group search filter
error_editing_default_host = El dominio por defecto no se puede cambiar
portlet_invoice_add = FACTURA: A\u00F1adir
com_byzaneo_xtrade_api_DocumentStage = Estado
bank_accounts_dlg_header = A\u00F1adir datos bancarios
ks_use_period = Periodo de utilizaci\u00F3n
portlet_invoice_print = FACTURA: Imprimir
indexClassName = Clase de \u00EDndice
portlet_invoice_export = FACTURA: Exportar
channel_auth_userDnPattern = User DN pattern
gcn_subscriber_active = Cliente Generix
ks_error_no_certificate_found = No se ha encontrado ning\u00FAn certificado en el archivo.
RESOLVED_STYLE = status_green
portlet_referentiel_produits = Repositorio/Productos
eDocument = eDocument
bank_account_deleting_error = Error al eliminar la cuenta bancaria {0}
CORRECT_STYLE_STAGE = status_green
channel_auth_userSearchFilter = User search filter
portlet_asn_import = ASN: Importar
ACCEPTED_WITH_AMENDMENT_ICON = fa fa-check-circle-o
IN_SUBMISSION_STYLE = status_yellow
bank_account_iban_error_required = El IBAN es obligatorio
imenu_portal_p2p = Purchase to pay
imenu_portal=Colaboraci\u00F3n
organization_bankAccount_partner = Socio
TO_PAY_STYLE = status_green
edocument = Documento electr\u00F3nico
TO_VALIDATE_STYLE = status_yellow
com_byzaneo_xtrade_api_DocumentStatus = Estado
gcnIdentification = Id. GCN (gid)
FORCED_STYLE = defecto
company_code_validator = El c\u00F3digo debe constar de tres letras may\u00FAsculas.
disagree = Rechazar
RECIPIENT = Receptor
organization_bankAccount_user = Creado por
imenu_general_instance = Medioambiente
logistic_serial_reference_and_cnuf_error_format_message = La longitud del CNUF y del n\u00FAmero secuencial debe ser de 16 caracteres.
java_math_BigDecimal = Decimal
portlet_invoice_view_attachment = FACTURA: Ver archivo adjunto
agree = Aceptar
imenu_portal = Colaborativo
logistic_serial_reference_error_format_message = El n\u00FAmero secuencial debe tener entre 6 y 9 d\u00EDgitos.
ACCEPTED_ICON = fa fa-check
CANCEL_STYLE = defecto
Contract = RoquetteContract
organization_extension = Car\u00E1cter de extensi\u00F3n
add_description = A\u00F1adir una descripci\u00F3n
imenu_report = Actividad
SYNTAX_ERR = Err. sintaxis
SUBMITTED_STYLE = status_green
CAMPAIGN = ONBOARDING
ERR_XLEG = Error ej. leg.
ERR_XLEG_STYLE = status_red
agreement_version_required = La versi\u00F3n de las CGU es obligatoria
SENDER = Emisor
contextual_validation_user_scope_role = El usuario {0} no tiene el rol {1}
bank_account_iban_error_exist = Lo sentimos, ya hay un IBAN asociado a esta moneda. Por favor, elimine el IBAN antes de introducir otro.
Preproduction = Preproducci\u00F3n
PENDING_ICON = fa fa-eur
IN_SUBMISSION = En proceso de pago
channel_AuthenticationEndpointConfiguration = Autenticaci\u00F3n
portlet_uploadpdf=UploadPdf
portlet_uploadpdfclient=UploadPdfClient
IMPORT_CORRECTION = Correcci\u00F3n de importaciones
environment_error_max_characters_allowed = M\u00E1ximo 64 caracteres permitidos
NO_ROUTE_STYLE = status_yellow
NO_ROUTE = Ruta ausente
bank_account_name_required = El nombre es obligatorio
author = Autor
IMPORT_CORRECTION_STYLE = info
selection_or_enter = Elija o introduzca un valor
locale_not_defined = El idioma de su formulario de socio no est\u00E1 definido, por favor rell\u00E9nelo.
portlet_files = ARCHIVOS
portlet_carousel=Carrusel
portlet_invoice_view_history = FACTURA: Realizar auditor\u00EDa
from_address_validator = La direcci\u00F3n de correo electr\u00F3nico del remitente no es v\u00E1lida. Solo se puede introducir una direcci\u00F3n.
error_export_not_empty_portlet = No puede exportar: el portlet no est\u00E1 vac\u00EDo.
localisation = Ubicaci\u00F3n del portal
quantityValue_validator = Valor de cantidad no v\u00E1lido
error_importing_page = Error al importar la p\u00E1gina
error_export_empty_portlet = No puede exportar un portal vac\u00EDo;
upload_with_conflict_conflict_message = Se sobrescribir\u00E1n los siguientes archivos:
notification_new_contact = Notificaci\u00F3n por correo electr\u00F3nico de nuevo contacto
portlet_documentation = DOCUMENTACI\u00D3N
portlet_collection = Colecci\u00F3n
portlet_order=Pedido
error_export_empty_page = No puede exportar una p\u00E1gina vac\u00EDa;
rte_file_or_folder_invalid_name = El nombre solo debe contener caracteres alfanum\u00E9ricos sin espacios. Los caracteres '.', '_' y '-' est\u00E1n permitidos.
factor = Factor
portlet_switch_user = Suplantar identidad
partner_code_validator = El c\u00F3digo debe estar formado por el c\u00F3digo del cliente (representado por tres letras may\u00FAsculas) seguido de \"-\" y su identificaci\u00F3n.
error_exporting_page = Error al exportar la p\u00E1gina.
contact_validator = La direcci\u00F3n de correo electr\u00F3nico de contacto no es v\u00E1lida. Se pueden introducir varias direcciones, separadas por el car\u00E1cter ';'.
permission_missing = No tiene permisos suficientes para acceder a esta funcionalidad
portlet_invoice_open = FACTURA: Abrir
return = Volver
error_export_portlet_rte_collection = No puede exportar la colecci\u00F3n portlet.
progress = En curso...
notifications = Notificaciones
error_importing_Languages = Se ha producido un error al importar el archivo del portal de localizaci\u00F3n
error_portlet_localization_lang_not_supported = {0} lenguaje no soportado por el entorno
extensions=Extensiones
file_name=Nombre del archivo:
last_modification=\u00DAltima modificaci\u00F3n
extension_label=N.\u00BA de registro
export_extension_file=Exportar archivo
export_extension_all_files=Exportar archivos
no_extensions_found = No se ha encontrado ning\u00FAn archivo de extensi\u00F3n para esta base de datos
extension_name=Nombre de la extensi\u00F3n
displayed_extension_name=Nombre que aparece para la extensi\u00F3n
display_extension=Mostrar extensi\u00F3n

menu_user_information = Mi cuenta
menu_security_user = Mi contrase\u00F1a
company_society = Empresa
menu_company_information = Datos de mi empresa
profile_title = Mi cuenta
portlet_invoice_forced = FACTURA: Forzar
error_saving_company = Error al guardar al cliente.
CUSTOMERS = CUSTOMER PORTALS
SUBMITTED = Entregado
action_line = Acci\u00F3n en la l\u00EDnea
date_to = a
error_password_generation = Se ha producido un error al generar la contrase\u00F1a.
menu = Men\u00FA derecho
menu_rights_users = Usuarios
action_global = Acci\u00F3n global
portlet_faq = PREGUNTAS FRECUENTES
portlet_freetext= FreeText
menu_rights_roles = Roles
menu_rights_clients = Clientes
menu_basket=Cesta

self_register_login = Reg\u00EDstrese
to_validate = En proceso de validaci\u00F3n
PARTIALLY_SHIPPED = Enviado parcialmente
bql_filter_title = Filtros
partner_address_1 = Direcci\u00F3n 1
partner_address_2 = Direcci\u00F3n 2
customer_partner_address_city = Ciudad
partner_city = Ciudad
clients_type_CUSTOMER = clientes
partner_country = Pa\u00EDs
customer_partner_name = Apellido
user_lastname = Apellido
partner_user_send_pass_yes = S\u00ED, quiero enviar
users_search_placeholder = Buscar un usuario por usuario, nombre o apellido
customer_partner_create = A\u00F1adir un ${client}
client_type_EDI = socio
instance_type_SPECIFIC = Espec\u00EDfico
error_removing_client = Error al borrar cliente ({0}).
BEING_SENT_CLIENT_STYLE = status_orange
perimeter_edit = Editar per\u00EDmetro para
check_site_conditions = Acepto las condiciones de uso del sitio
portlet_invoice_join = FACTURA: Adjuntar
deploy = Desplegar
PARTIALLY_ACCEPTED_WITH_AMENDMENTS = Confirmado parcialmente con modificaciones
update_user_parameters = Actualizar mis par\u00E1metros
cart_checkout = VALIDAR
a_day_past = menos de un d\u00EDa
filterSuccessfullySaved = El filtro se ha guardado
portlet_deadpool = Deadpool
cart_view = VER CESTA
clients_type_EDI = socios
partner_user_send_new_pass_header = Restablecer contrase\u00F1a de usuario
REFERENTIAL_OK = Repositorio OK
menu_process_execution = Proceso: Ejecuciones
PARTIALLY_ACCEPTED = Confirmado parcialmente
related_process = B\u00FAsqueda de procesos relacionados
IN_VALIDATION = En proceso de validaci\u00F3n
REFERENTIAL_KO = Repositorio KO
clear_query = Borrar
METADATA_KO_STYLE = status_red
info_client_saved = Cliente {0} guardado
date_last_authentication_no_date = Fecha de la \u00FAltima conexi\u00F3n: Informaci\u00F3n no disponible
UNARCHIVABLE_STYLE = status_red
instance_type_INVOICE = Factura
error_password_link_generation = Se ha producido un error al generar el enlace de renovaci\u00F3n de contrase\u00F1a.
cart_totalAmount = Total:
rte_input_file_must_be_unique = El archivo de entrada debe ser \u00FAnico
error_exporting_client = Error al exportar clientes ({0}).
info_client_removed = Cliente {0} eliminado
cart_numberOfArticles = art\u00EDculo(s) en la cesta
order_history_title = MI HISTORIAL DE PEDIDOS N.\u00BA:
rte_status_not_deployed = El script RTE no est\u00E1 desplegado
BEING_SENT_CLIENT = Env\u00EDo al cliente en curso
fileName = Nombre del archivo
identical_new_passoword = La nueva contrase\u00F1a es id\u00E9ntica a la antigua
user_creation_date = Fecha de creaci\u00F3n
customer_clientUsers = Mis usuarios
contact_recipients = Destinatarios del correo electr\u00F3nico de contacto
IN_DELIVERY_STYLE = status_orderlist_yellow
template_invalid_pathname = El archivo contiene archivos con nombres incorrectos
not_valid = No v\u00E1lido
partner_client_name = Nombre ${client}
no_records_found_loreal_order = No hay art\u00EDculos que coincidan con su b\u00FAsqueda. <br/> Por favor, modifique su b\u00FAsqueda.
AWAITING_VALIDATION_STYLE = status_orange
rte_status_deployed = El script RTE se ha desplegado correctamente
IN_PREPARATION_STYLE = status_orderlist_yellow
customer_partner_edit =  ${client}
info_import_processes = Se han importado los procesos
customer_partner_export = Exportar un archivo ${client}
rte_base_not_supported = Las colecciones RTE se excluyen del dominio de prueba (por el momento)
client_type_SPECIFIC = socio
user_password_dialog_header = A\u00F1adir una contrase\u00F1a
REFERENTIAL_KO_STYLE = status_red
introduction_placeholder = Texto introductorio, ej.: \n La documentaci\u00F3n disponible est\u00E1 clasificada por categor\u00EDas. No dude en hacer clic en una categor\u00EDa para descubrir los documentos de que disponemos. Si falta alg\u00FAn documento, comun\u00EDquenoslo a trav\u00E9s de la p\u00E1gina de contacto.
self_register_placeholder_2_placeholder = Campo de n.\u00BA de conexi\u00F3n \u00FAnico en la p\u00E1gina de autorregistro
general_edi_service = Generix EDI Services
total_carts = Total de cestas:
menu_process_trigger = Procesos: Desencadenantes
confirmOverrideFilter = El filtro ya existe. \u00BFDesea sobrescribirlo?
customer_partner_edit_button = Actualizar ${client}
customer_partner_add_user = A\u00F1adir un usuario
exception_duplicate_perimeter = Per\u00EDmetro duplicado: {0}.
user_message_delete = El usuario ser\u00E1 eliminado permanentemente.
IN_PREPARATION = En proceso de preparaci\u00F3n
menu_user_parameters = Mi configuraci\u00F3n
menu_process_deployment = Proceso: Despliegue
PARTIALLY_ACCEPTED_STYLE = status_partially_accepted
client_number = N.\u00BA ${client}
to_prepare = En proceso de preparaci\u00F3n
rte_status_deployed_but_changed = El script RTE ha sido modificado desde su despliegue
CONTROL_OK_STYLE = status_green
error_saving_perimeter = Error al guardar el per\u00EDmetro ({0}).
portlet_invoice_actions_10 = FACTURA: Acci\u00F3n 10
general_invoices_service = Generix Invoices Services
partner_user_roles_save = Modificar
estimated_delivery_date = Fecha estimada de entrega:
info_category = Para ordenar las categor\u00EDas, debe arrastrarlas desde abajo hacia arriba.
CONTROL_TOTAL_KO = Controles cantidad KO
self_register_placeholder_1 = Autorregistro marcador de posici\u00F3n 1
self_register_placeholder_2 = Autorregistro marcador de posici\u00F3n 2
Category.name = Categor\u00EDa
clients_type_INVOICE = socios
user_number_connexion = Inicio(s) de sesi\u00F3n
warn_existing_resource = El recurso ya existe
perimeters = Per\u00EDmetros
confirmationTitle = Confirmaci\u00F3n
user_email = Direcci\u00F3n de correo electr\u00F3nico
bql_filter_details = Filtrado de consultas BQL Portlet de factura
portlet_order_actions_10 = PEDIDO: Acci\u00F3n 10
customer_partner_delete_yes = S\u00ED, lo borro.
portlet_order_confirm_with_modification = PEDIDO: Confirmar con modificaci\u00F3n
user_phone = Tel\u00E9fono
error_invalid_date = {0}: El contenido introducido no es una fecha: {1}
customer_partner_import_button = Importar el archivo
warm_change_into_empty_when_other_exists = No se puede cambiar esta disposici\u00F3n por una disposici\u00F3n vac\u00EDa mientras existan otras disposiciones
customer_partner_add_user_dialog_header = A\u00F1adir un usuario a ${client}
warn_delete_fail = Borrado fallido
user_login_validator = El identificador de usuario debe ser una direcci\u00F3n de correo electr\u00F3nico.
cookie = Cookie
no_processed = No procesado
warm_add_empty_when_other_exits = No puede a\u00F1adir una disposici\u00F3n vac\u00EDa si ya existe una.
instance_type = Tipo de portal
portlet_invoice_actions_3 = FACTURA: Acci\u00F3n 3
portlet_invoice_actions_4 = FACTURA: Acci\u00F3n 4
portlet_invoice_actions_1 = FACTURA: Acci\u00F3n 1
portlet_invoice_actions_2 = FACTURA: Acci\u00F3n 2
portlet_invoice_actions_7 = FACTURA: Acci\u00F3n 7
portlet_invoice_actions_8 = FACTURA: Acci\u00F3n 8
portlet_invoice_actions_5 = FACTURA: Acci\u00F3n 5
triggername_ACTION_10 = Acci\u00F3n 10
portlet_invoice_actions_6 = FACTURA: Acci\u00F3n 6
UNARCHIVABLE = No archivable
menu_company_information_client = Mi informaci\u00F3n ${client}
export_list = Exportar lista
user_firstname_placeholder = Su nombre
IN_DELIVERY = Entrega en curso
portlet_invoice_actions_9 = FACTURA: Acci\u00F3n 9
edit_library = Editar una biblioteca
partner_user_delete = Borrar usuario
partner_user_message_send_new_pass_msg = Se enviar\u00E1 un correo electr\u00F3nico al usuario con un enlace que le permitir\u00E1 cambiar su contrase\u00F1a.
customer_partner_import_header = Importar un archivo ${client} (*.xls, *.xlsx)
library_created_success = Se ha creado la documentaci\u00F3n
portlet_orderresponse_import = RESPUESTA DEL PEDIDO: Importar
END_PROCESS = Fin del tratamiento
contextual_validation_partner_role = El rol debe existir
clientUsers = Mis usuarios
company_number = N.\u00BA ${client}
rte_test_properties_placeholder = Propiedades:
in_delivery = Entrega en curso
rte_forbidden_value = init.tst: la propiedad {0} no se ha rellenado correctamente
customer_partner_create_button = A\u00F1adir el ${client}
uploaded_since = En l\u00EDnea desde
error_localizations_import_bad_structure = La estructura del archivo no se ajusta al formato esperado
customer_partner_message_delete = El ${client} ser\u00E1 eliminado permanentemente.
partner_user_search_placeholder = Buscar un usuario por n\u00FAmero, nombre ${client}, usuario, nombre o apellido
info_import_portal = El portal ha sido importado
scope_customer = Per\u00EDmetro restringido
active_carts = cesta(s) activa(s)
general_customers_service = Generix Customers Services
category_field_empty = El campo categor\u00EDa debe rellenarse.
partner_company = Empresa
info_user_role_duplicated = El rol {0} se ha duplicado
customer_partner_userNumber = N\u00FAmero de usuarios
no_processes_were_found = El RTE no participa en ning\u00FAn proceso
info_perimeter_saved = Per\u00EDmetro {0} protegido
rte_status_unknown = Por determinar
save_register =  Confirmo mi registro
error_saving_client = Error al guardar al cliente ({0}).
clientPartners = Mis ${clients}
contact_subject = Asunto del correo electr\u00F3nico de contacto
user_email_placeholder = Direcci\u00F3n de correo electr\u00F3nico utilizada para iniciar sesi\u00F3n
SIGNATURE_KO_STYLE = status_red
customer_clientPartners = Mis ${clients}
triggername_ACTION_2 = Acci\u00F3n 2
triggername_ACTION_1 = Acci\u00F3n 1
triggername_ACTION_4 = Acci\u00F3n 4
triggername_ACTION_3 = Acci\u00F3n 3
triggername_ACTION_6 = Acci\u00F3n 6
triggername_ACTION_5 = Acci\u00F3n 5
triggername_ACTION_8 = Acci\u00F3n 8
triggername_ACTION_7 = Acci\u00F3n 7
triggername_ACTION_9 = Acci\u00F3n 9
imenu.portal = Colaboraci\u00F3n
in_validation = En proceso de validaci\u00F3n
portlet_library = Biblioteca
rte_collection = Colecci\u00F3n
invalid_password = Contrase\u00F1a inv\u00E1lida. Por favor, introduzca una nueva contrase\u00F1a.
validated = Validada
customer_partner_delete_no = No, lo pensar\u00E9.
partner_user_roles = Rol del usuario
scope_partner = Per\u00EDmetro ampliado
SMTP_ERROR_STYLE = status_red
exception_import_page_null = La p\u00E1gina de exportaci\u00F3n es nula.
partner_user_send_new_pass = Restablecer contrase\u00F1a
users_number_exceeded = Ha superado el n\u00FAmero de usuarios para un ${client}. Elimine los usuarios inactivos o p\u00F3ngase en contacto con el servicio de asistencia
client_type_SUPPLIER = proveedor
requested_delivery_date = Fecha de entrega solicitada:
rte_test_results_header = Resultados de las pruebas Rte
user_email_address = Correo electr\u00F3nico
METADATA_KO = Metadatos KB
IN_VALIDATION_STYLE = status_orderlist_blue
PARTIALLY_ACCEPTED_WITH_AMENDMENTS_STYLE = status_deep_blue
rte_test_fail_message = Prueba Rte ejecutada con fallo
REFERENTIAL_OK_STYLE = status_green
SIGNATURE_OK_STYLE = status_green
clients_type_SPECIFIC = socios
order_number = N.\u00BA de pedido
partner_postal_code = C\u00F3digo postal
info_perimeter_removed = Per\u00EDmetro {0} eliminado
warn_deleting_own_user = No es posible eliminar el usuario {0}. Se trata de su propio usuario.
customer_partner_number = N.\u00BA ${client}
CONTROL_TOTAL_KO_STYLE = status_red
invalid_file_accent_char = El archivo no puede contener caracteres acentuados
customer_partner_connectionCode = N\u00FAmero \u00FAnico de conexi\u00F3n
self_register_placeholder_1_placeholder = N.\u00BA de campo ${client} en la p\u00E1gina de autorregistro
comment_upload_file = A\u00F1adir un comentario a su archivo
info_parameters_saved = Par\u00E1metros de usuario guardados
connection_code = N\u00FAmero \u00FAnico de conexi\u00F3n
portlet_taxes_allowances = Descuentos y e impuestos parafiscales
organization_role = Rol
add_user_mail_button = A\u00F1adir usuario + Link par restablecer contrase\u00F1a
exception_perimeter_has_users = El per\u00EDmetro {0} a {1} tiene usuario(s) asociado(s).
instance_type_EDI = EDI
customer_partner_import = Importar un archivo ${client}
self_register = Autorregistro
error_invalid_row_length = [{0},{1}] N\u00FAmero de campos incorrectos esperados ({2}), encontrados ({3})
error_invalid_number = {0}: El contenido de entrada no es un n\u00FAmero: {1}
info_partner_role_saved = Rol del socio guardado
exception_duplicate_email = Esta direcci\u00F3n de correo electr\u00F3nico ya ha sido creada
error_input_content_not_valid = El contenido introducido no es v\u00E1lido: {0}
VALIDATED = Validado
client_type_CUSTOMER = cliente
PARTIALLY_SHIPPED_ICON = fa fa-paper-plane
canNotSaveFilter = No puede guardar este filtro porque la ubicaci\u00F3n de almacenamiento est\u00E1 llena
run_rte = Ejecutar
general_supplier_service = Generix Suppliers Services
AWAITING_VALIDATION = A la espera de validaci\u00F3n
CONTROL_OK = Controles OK
SMTP_ERROR = Error smtp
client_type_INVOICE = socio
user_mobile = Tel\u00E9fono m\u00F3vil
user_login = Usuario
portlet_orderresponse_print = RESPUESTA DEL PEDIDO: Imprimir
customer_partner_name_for_user = Nombre ${client}
field_missing = Falta el campo \"{0}\".
add_library = A\u00F1adir una biblioteca
partner_user_create = A\u00F1adir un usuario
invalid_file_special_char = El archivo no puede contener caracteres especiales
library_document_introduction = La documentaci\u00F3n disponible est\u00E1 clasificada por categor\u00EDas. No dude en hacer clic en una categor\u00EDa para descubrir los documentos que hemos puesto a su disposici\u00F3n.
warn_creation_succes = El recurso se ha creado correctamente
SIGNATURE_KO = Firma KO
customer_partner_delete = Borrar el ${client}
user_last_authentication = \u00DAltima conexi\u00F3n
error_removing_perimeter = Error al borrar el per\u00EDmetro ({0}).
END_PROCESS_STYLE = status_green
instance_type_CUSTOMER = Cliente
SIGNATURE_OK = Firma OK
generate_reset_link_expired = El enlace de renovaci\u00F3n de contrase\u00F1a ha caducado.
user_firstname = Nombre
one_hour_past = menos de una hora
control_ean = Comprobaci\u00F3n de la longitud del campo de c\u00F3digo EAN
exception_export_null = No se ha encontrado ninguna exportaci\u00F3n en el archivo
quick_search_loreal_order = B\u00FAsqueda r\u00E1pida por marca, art\u00EDculo, c\u00F3digo de marca, EAN
default_test_rte = Prueba por defecto
import_role_header = Importar un archivo de roles (*.xls, *.xlsx)
info_perimeter_duplicated = El per\u00EDmetro {0} ha sido duplicado
generate_reset_link_invalid = Enlace de renovaci\u00F3n de contrase\u00F1a no v\u00E1lido. P\u00F3ngase en contacto con su administrador.
partner_user_add_role = Asignar un rol
self_register_ok = Se le ha enviado por correo electr\u00F3nico un enlace de renovaci\u00F3n de contrase\u00F1a.
clients_type_SUPPLIER = proveedores
partner_user = Usuario
instance_type_SUPPLIER = Proveedor
add_user_button = A\u00F1adir usuario
user_password_confirm = Confirmar contrase\u00F1a
self_register_ko = Hemos detectado un problema al registrar su cuenta. Por favor, int\u00E9ntelo de nuevo m\u00E1s tarde.
warn_portlet_localization_lang_not_supported = Idioma {0} no soportado por el entorno
perimeter = Per\u00EDmetro
site_conditions_link = Condiciones de uso del sitio
user_lastname_placeholder = Su apellido
subtype = Subtipo
individual_tests_rte = Prueba individual
control = Comprobar
partner_with_code_missing = El n\u00FAmero ${client} y/o el n\u00FAmero de registro no existen
bql = BQL
library_edited_succes = Se ha modificado la documentaci\u00F3n
info_portlet_localization_import = La importaci\u00F3n del portal de localizaci\u00F3n ha finalizado.{0} mensaje(s) a\u00F1adido(s) y {1} modificado(s)
invalid_file_csv_type = El archivo debe ser de tipo CSV
rte_property_unknown = init.tst: la propiedad {0} es desconocida
portlet_contact_us = P\u00F3ngase en contacto con nosotros
exception_perimeter_has_partners = El per\u00EDmetro {0} a {1} tiene socio(s) asociado(s).
user_empty_client = Elegir un ${client}...
by_date = el {0}
save_success = Se ha hecho una copia de seguridad de toda la biblioteca
warn_import_pt_collection = Si el portal utiliza portlets de \"colecci\u00F3n\", ser\u00E1 necesario configurar estos portlets
customer_partner_show_users = Usuarios asociados al cliente
portlet_orderresponse_export = RESPUESTA DEL PEDIDO: Exportar
validity_date = Fecha de caducidad:
VALIDATED_STYLE = status_orderlist_green
portlet_order_mark_as_unread = PEDIDO: Marcar como no le\u00EDdo
return_to = Volver a
customer_partner_search_placeholder = Buscar un ${client} por n\u00FAmero, nombre o ciudad
portlet_order_actions_9 = PEDIDO: Acci\u00F3n 9
portlet_order_actions_8 = PEDIDO: Acci\u00F3n 8
RESENT_STYLE = status_green
advSearch = B\u00FAsqueda avanzada
PARTIALLY_SHIPPED_STYLE = status_yellow
warn_localizations_import_portlet_not_found = Portlet {0} no encontrado en la definici\u00F3n del portal
RESENT = Reenviado
portlet_order_actions_1 = PEDIDO: Acci\u00F3n 1
portlet_order_actions_3 = PEDIDO: Acci\u00F3n 3
portlet_order_actions_2 = PEDIDO: Acci\u00F3n 2
portlet_order_actions_5 = PEDIDO: Acci\u00F3n 5
portlet_order_confirm = PEDIDO: Confirmar
portlet_order_actions_4 = PEDIDO: Acci\u00F3n 4
portlet_order_actions_7 = PEDIDO: Acci\u00F3n 7
portlet_order_actions_6 = PEDIDO: Acci\u00F3n 6
exception_task_properties_not_found = No se puede importar la tarea {0}, es necesario actualizar la versi\u00F3n
edit_user_button = Actualizar usuario
self_register_bottom_msg = Una vez registrado, recibir\u00E1 un correo electr\u00F3nico con su contrase\u00F1a temporal. Deber\u00E1 cambiar esta contrase\u00F1a la primera vez que se conecte.
library_deleted_success = Se ha eliminado la documentaci\u00F3n
rte_no_init_test_file = No se ha encontrado el archivo por defecto init.tst
rte_test_success_message = Prueba Rte completada con \u00E9xito
warn_partner_missing = No se encuentra el socio (compruebe el c\u00F3digo del par\u00E1metro).
partner_user_send_pass_no = No, lo pensar\u00E9.
category = Categor\u00EDa
info_ordering_removed = Ordenante {0} eliminado
portlet_standardorderlist_actions_2 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 2
portlet_standardorderlist_actions_1 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 1
portlet_standardorderlist_actions_6 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 6
portlet_standardorderlist_actions_5 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 5
portlet_standardorderlist_actions_4 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 4
portlet_standardorderlist_actions_3 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 3
reconciliation_success = Conciliaci\u00F3n completada con \u00E9xito.
portlet_standardorderlist_remove = LISTA DE PEDIDOS EST\u00C1NDAR: Suprimir
EP_ELIGIBLE = Elegible
info_partners = Seleccionar aqu\u00ED el nombre o el c\u00F3digo de una entidad.
manage_rules = Gesti\u00F3n de reglas
portlet_standardorderlist_actions_9 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 9
portlet_standardorderlist_actions_8 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 8
portlet_standardorderlist_actions_7 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 7
portlet_standardorderlist_export = LISTA DE PEDIDOS EST\u00C1NDAR: Exportar
workflow_notifications = Gestionar mis notificaciones de flujo de trabajo
add_ordering_party = A\u00F1adir un cliente
counter_management_other_page_configuration = Utilizar la configuraci\u00F3n de otra p\u00E1gina
element_order = Pedido
portlet_referentiel_produits_logistics_info = Repositorio/Productos/Informaci\u00F3n log\u00EDstica
sap_configuration_network = Red
ordering_identifier_exists = Ya existe un ordenante con el identificador {0}
checkbox_banner = Activar el banner
aws_batch = Lote AWS
pageIconPlaceholder = ex: fa fa-bar-chart
cannot_assign_role = Tiene derecho a asignar un rol pero no hay ning\u00FAn rol configurado
workflow_checkbox = Permitir que las notificaciones de flujo de trabajo sean establecidas por los usuarios
placeholder_database_name = El nombre de la base de datos de la instancia
reconciliation_empty_seller_party = Imposible conciliar: falta el c\u00F3digo del proveedor.
assignable_roles = Funciones atribuibles
portlet_referentiel_addresses = Repositorio/Direcciones
mark_doc_as_unread_warn = Solo puede restablecerse un documento en estado \"Le\u00EDdo\". No se realiza ninguna acci\u00F3n.
file_exceeded_number = No se pueden importar m\u00E1s de 10 documentos a la vez
consultation_status = No cambiar el estado de consulta del documento a \"Le\u00EDdo\"
document_statuses_added = Se ha(n) a\u00F1adido {0} nuevo(s) estado(s)
dialog_message_sequence_edit = Secuencia de mensajes - Modificar
general_notifications = Mis notificaciones generales
archive_storage_option = Almacenamiento de archivos
menu_user_customer_supplier_env_bank = Datos bancarios de mis proveedores
sap_configuration_serialization_format = Formato de serializaci\u00F3n
placeholder_host_name = El nombre de host o la direcci\u00F3n IP del sistema donde se ejecuta la instancia
add_new_status = A\u00F1adir un estado a los documentos
notify_for_new_action_to_perform = Recibir una notificaci\u00F3n cada vez que tenga que actuar sobre una factura
counter_management_own_configuration = Definir ajustes espec\u00EDficos para esta p\u00E1gina
document_statuses_updated = {0} estado(s) se ha(n) actualizado
reminderNb = N\u00FAmero de d\u00EDas antes del recordatorio
ie_recognition = Reconocimiento de Internet Explorer
warn_importing_multiLevel_iconPages = La importaci\u00F3n no puede realizarse porque una p\u00E1gina \"\u00EDcono\" solo puede tener un nivel
portlet_standardorderlist_export_list = LISTA DE PEDIDOS EST\u00C1NDAR: Exportar lista
sap_configuration_host = Host
banner = Banner
host_name = Dominio
menu_user_customer_other_env_bank = Datos bancarios de mis socios
EP_ACCEPTED = Solicitud aceptada
ordering_party_delete_last_element = \u00BFDesea eliminar el \u00FAltimo elemento?
delete_counter_error_message = Tenga en cuenta que no es posible eliminar este contador, ya que se utiliza en los contadores en miniatura.
menu_legal_information = Informaci\u00F3n jur\u00EDdica
banner_text = Texto a mostrar
counter_management_title = Seleccione los contadores que desea visualizar
document_status = Estado del documento
receptionItem = ReceptionItemCodeField
portlet_demat_partner_file = DematPartnerFile
counter_management = Gesti\u00F3n de contadores en miniatura
element_contextmenu_config_form = Configurar formulario
menu_password_user = Mi contrase\u00F1a
button_message_sequence = Secuencia de mensajes
listbox_message_sequence_available_messages = Mensajes disponibles
filter_by_type = Filtrar por tipo
file_docs_uploaded = Los documentos se han importado
portlet_standardorderlist_actions_10 = LISTA DE PEDIDOS EST\u00C1NDAR: Acci\u00F3n 10
kpis = Indicadores KPI
url_tracking = Activar el seguimiento de URL para cada usuario conectado
document_status_updated = El estado del documento {0} ha sido actualizado
element_order_response = Rep. pedido
EP_REFUSED = Solicitud denegada
delete_button_message_sequence = Eliminar
regex = Regex
ordering_name = Apellido
sap_configuration = SAP
not_notified_if_absent_and_have_replacement = No ser notificado si estoy ausente y otra persona me reemplaza
mark_docs_as_unread_warn = Algunos documentos no tienen el estado \"Le\u00EDdo\". No se ha realizado ninguna acci\u00F3n en estos pedidos.
database_name = Nombre de la base de datos
portlet_payment_actions_10 = PAGO: Acci\u00F3n 10
edit_status = Editar el estado del documento
com_byzaneo_xtrade_api_DocumentConsultStatus = Estado de consulta
menu_user_addresses = Agenda de direcciones de mi empresa
doc_status_code = C\u00F3digo de estado
error_export_shadow_portlet = No puede exportar esta p\u00E1gina porque contiene al menos un portlet \"Sombra\".
status_import_file = Importar un archivo de estado de documento (*.xls, *.xlsx)
orderItem = OrderItemCodeField
menu_user_notifications = Mis notificaciones
info_addressSearch = No es posible buscar por campo de pa\u00EDs
counter_management_pages = P\u00E1ginas
ordering_partners = Socios
ok_switch = Validar
CORRECT = Desmaterializado
portlet_payment_accept_reject_early_payment = Pago anticipado del vendedor
info_import_page = Importar p\u00E1gina OK
portlet_standardorderlist_duplicate = LISTA DE PEDIDOS EST\u00C1NDAR: Duplicar
reconciliation = Conciliaci\u00F3n
reconciliation_info = Configuraci\u00F3n de la clave de b\u00FAsqueda utilizada para conciliar las filas
counter_management_counter = Contador
no_files_choosen = No hay archivos seleccionados
rte_studio = Estudio RTE
portlet_standardorderlist_refuse = LISTA DE PEDIDOS EST\u00C1NDAR: Rechazar manualmente
portlet_invoice_correct = FACTURA: Corregir
reconciliation_gap_found = Conciliaci\u00F3n completada, discrepancias encontradas.
UNDEFINED = Indefinido
select_rte_label = Fuente RTE de controles
select_rte_source = Elegir
add_page = A\u00F1adir una p\u00E1gina
update_user_notifications = Actualizar mis notificaciones
element_arrow_optional = Opcional
element_shipping_advice = Aviso de env\u00EDo
sap_configuration_expiration_check = Per\u00EDodo de comprobaci\u00F3n de expiraci\u00F3n
ordering_party_exit_without_saving = \u00BFDesea salir sin guardar el diagrama?
portlet_payment_suggest_early_payment = Pago anticipado comprador
add_sso_role_mapping = A\u00F1adir una asignaci\u00F3n de funciones
page_display_option = No mostrar en el men\u00FA
sap_configuration_repository_optimization = Optimizaci\u00F3n de los viajes de ida y vuelta al repositorio
sap_configuration_user = Usuario
reconciliation_empty_order_number = Imposible conciliar: falta el n\u00FAmero de pedido.
sap_configuration_client = Cliente
file_process_will_be_executed = El proceso se ejecutar\u00E1
menu_rights_perimeters = Per\u00EDmetros
DEPRECATED = OBSOLETO
portlet_invoice_archive_delete = ARCHIVO DE FACTURAS: Borrar
portlet_invoice_modify = FACTURA: Modificar
arn = ARN para la funci\u00F3n IAM
menu_ordering_party = Ordenantes
add_button_message_sequence = A\u00F1adir
sap_configuration_expiration_time = Hora de caducidad
BEING_PROCESSED_STYLE = status_orange
sso_role_mapping = Asignaci\u00F3n de roles
absence = Ausencia
portlet_reception_remove = RECADV: Suprimir
quicksight = QuickSight
menu_user_partner_bank = Datos bancarios de mi empresa
ordering_number_of_partners = N\u00FAmero de socios
info_perimeters = Seleccionar aqu\u00ED el nombre de un grupo de entidades.
menu_company_addresses = Direcciones de empresa
partner_user_roles_perimeters = Rol y per\u00EDmetro del usuario
placeholder_tcp = Puerto TCP en el que escucha la instancia
document_status_removed = El estado del documento {0} ha sido eliminado
cron_expression = Expresi\u00F3n Cron
no_message_display = No volver a mostrar este mensaje
connections = Conexiones
save_button_message_sequence = Guardar
portlet_payment_actions_8 = PAGO: Acci\u00F3n 8
portlet_payment_actions_9 = PAGO: Acci\u00F3n 9
portlet_payment_actions_2 = PAGO: Acci\u00F3n 2
portlet_payment_actions_3 = PAGO: Acci\u00F3n 3
portlet_payment_actions_1 = PAGO: Acci\u00F3n 1
use_ordering_party_configuration = Utilizar la configuraci\u00F3n del ordenante
portlet_payment_actions_6 = PAGO: Acci\u00F3n 6
portlet_payment_actions_7 = PAGO: Acci\u00F3n 7
portlet_payment_actions_4 = PAGO: Acci\u00F3n 4
portlet_payment_actions_5 = PAGO: Acci\u00F3n 5
document_status_added = Se ha a\u00F1adido el estado de documento {0}
duplicate_mapping = Esta asignaci\u00F3n ya existe.
element_not_removed = No se puede eliminar el primer elemento
document_status_exists = El estado del documento {0} ya existe
portlet_invoice_archive_bulk_export = ARCHIVO DE FACTURAS: Exportaci\u00F3n masiva
info_limited_extended_scope = Indicar en estos campos las entidades a las que puede tener acceso este usuario. Puede introducir el nombre de una o varias entidades o el nombre de un grupo de entidades.
partners_ordering_party = Socios ordenante
portlet_standardorderlist_validate = LISTA DE PEDIDOS EST\u00C1NDAR: Validar manualmente
warn_kpi_invalid = No se puede iniciar el servicio KPI. Int\u00E9ntelo de nuevo m\u00E1s tarde o p\u00F3ngase en contacto con su administrador.
exception_role_has_sso = El rol {0} tiene asignaciones de rol sso.
sap_configuration_language = Lang
ordering_identifier = Identificador
edit_ordering_party = Editar ordenante
no_order_from_asn = No se ha encontrado ning\u00FAn pedido del aviso de env\u00EDo anticipado seleccionado
activate_notifications = Activar mis notificaciones
doc_status_style = Estilo de estado
roles_bo = Roles de back office
ordering_description = Descripci\u00F3n
sap_configuration_password = Passwd
EP_PERFORMED = Pago efectuado
company_information = Informaci\u00F3n sobre la empresa
tcp_port = Puerto TCP
menu_user_customer_client_env_bank = Datos bancarios de mis clientes
BEING_PROCESSED = En proceso
info_ordering_saved = Ordenante {0} guardado
page_submenu_warning = El uso de un men\u00FA de 3 niveles puede causar errores de visualizaci\u00F3n. Recuerda comprobar la representaci\u00F3n del men\u00FA en el front office.
error_importing_portlet = La importaci\u00F3n json solo es posible en una p\u00E1gina en blanco.
info_notifications_saved = Notificaciones de usuarios guardadas
reconciliation_start = Comienza la conciliaci\u00F3n.
EP_INELIGIBLE = No eligible
EP_SUBMITTED = Solicitud en curso
users_creation = Usuarios/creaci\u00F3n mdp (sin enlace)
database_kpi = Base de datos KPI
sap_configuration_sysnr = Sysnr
element_checkbox_title_mandatory = Obligatorio
info_ordering_model_saved = Modelo de socios protegidos
imenu_portlet = Portlets
add_icon_page = A\u00F1adir una p\u00E1gina de \u00EDconos
sap_configuration_pool_size = Capacidad de la piscina
workflow_checkBox_help = Si la casilla est\u00E1 marcada, el usuario podr\u00E1 elegir, desde su perfil, si desea que se le notifique cada nuevo documento.
reconciliation_order_not_found = Imposible conciliar: n.\u00BA de pedido {0} no encontrado.
ordering_messages_sequence = Secuencia de mensajes
taxOriginal = Definido el archivo EDI como original fiscal.
cancel_button_message_sequence = Abandonar
element_invoice = Factura
workflow_template = Plantilla
dialog_message_sequence_add = Secuencia de mensajes - A\u00F1adir
connection_id = N\u00FAmero \u00FAnico de identificaci\u00F3n
invoiceItem = InvoiceItemCodeField
templateUri = Plantilla de recordatorio
organization_optionReporting = Opciones de eReporting
AAP_MISSING_AN_REF_STYLE = status_red
authentication_reconfiguration = Reconfigurar
REJECTED_STYLE = status_red
accessTokenV1_username = Nombre de usuario
bql_instructions_title = T\u00EDtulo de las instrucciones BQL
RCL_READY = Listo para inf.
AAP_IN_PROGRESS_STYLE = status_yellow
accessTokenV1_access_fail = No se puede acceder a este recurso: {0}.
authentication_partners_pairs = Configuraci\u00F3n de socio
APPROVED_PARTIALLY_STYLE_STAGE = status_yellow
RCL_SUCCESS_STYLE = status_green
ROSSUM = ROSSUM
accounting_posting_management = Gesti\u00F3n
RCL_DISCREPANCY = Discrep. de inf.
translation = Traducci\u00F3n
accounting_posting_referentials_tax = Impuestos
eReporting_collection = Selecci\u00F3n de la colecci\u00F3n
AAP_READY_STYLE = defecto
angular_url = URL Angular
error_files_too_large = El tama\u00F1o del archivo supera el tama\u00F1o m\u00E1ximo autorizado de 1,7 Gb.
AAP_NA_STYLE = info
PAYMENT_SENT_STYLE_STAGE = status_green
clientId_secretKey = Clave secreta ClientId
AAP_MANUAL_MODE = Imput. modo manual
angular_template = Plantilla Angular
WKF_REFUSED_STYLE = status_red
Stage_SENT = Emitida por la plataforma
authentication_togetherConfiguration = Configuraci\u00F3n conjunta
AVAILABLE_STYLE_STAGE = status_green
compliance_life_cycle_factor_confidential_permissions = Factoring confidencial
RECEIVED_STYLE_STAGE = status_green
workflow_tab = Flujos de trabajo
AAP_MISSING_REF_STYLE = status_red
WKF_IN_PROGRESS_STYLE = status_yellow
organization_vatRegime = Opci\u00F3n de r\u00E9gimen de IVA:
RCL_NA_STYLE = info
APPROVED_B2G_STYLE_STAGE = status_green
compliance_life_cycle_partially_approve_permissions = Aprobar parcialmente
OTHER = OTROS
compliance_life_cycle_cash_in_permissions = Cobrar
template_type = Tipo de plantilla
accounting_posting_referentials = Repositorios
Stage_APPROVED = Aprobada
Stage_DISPUTED = En litigio
authentication_preDomain = Predominio
compliance_life_cycle_factor_permissions = Factoring
accessTokenV1_tokenExpirationDate = Fecha de caducidad del token
authenticationServer_tab = Servidor de autenticaci\u00F3n
AAP_ERROR_STYLE = status_red
compliance_life_cycle_suspend_permissions = Suspender
compliance_life_cycle_refuse_permissions = Rechazar
manual_analytical_ventilation = Desglose anal\u00EDtico manual
accounting_posting_management_scenario = Escenarios
RCL_PRICES_DISCR = Discrep. de inf. precios
invidx_wkf_reinit = Restablecer la informaci\u00F3n del flujo de trabajo en el \u00EDndice de facturas
WKF_IN_PROGRESS = Wkf en curso
RCL_INTRUDER_PROD_STYLE = status_orange
DISPUTED_STYLE_STAGE = status_yellow
AAP_COMPLETED_AN_STYLE = status_green
angular_version = Versi\u00F3n Angular
authenticationServer_configuration = Configuraci\u00F3n del servidor de autenticaci\u00F3n
api_permissions_roles = Roles
error_existing_clientId = Este clientId ya existe para este reino, por favor, elija otro clientId
select_validation_process = Proceso posterior a la validaci\u00F3n
authenticationServer_btn = Configurar el servidor de autenticaci\u00F3n
importPageWarningText = El portlet referenciado no existe, por lo que no se crear\u00E1 el enlace al portlet. \u00BFA\u00FAn desea importar?
manage_periodic_reports = Gesti\u00F3n de informes peri\u00F3dicos
exception_remove_technicalUser_associated = No se han podido eliminar los usuarios t\u00E9cnicos asociados a {0}.
authentication_defaultClientId = ClientId por defecto
exception_technicalUsers_perimeterAssociation = El per\u00EDmetro {0} a {1} tiene usuario(s) t\u00E9cnico(s) asociado(s)
AAP_COMPLETED = Imput. completa
authentication_partners = Socio
api_permissions_users = Usuarios
AAP_MISSING_PO_STYLE = status_red
technical_user_partner = Socio
ROLE_TECHNICAL = Funci\u00F3n t\u00E9cnica
Stage_UPLOADED = Archivado en
life_cycle = Ciclo de vida
outputFile_working_copy = Hacer una copia [de trabajo] [del archivo de salida]
secret_api_key = SecretAPIKey
accounting_posting_entries = Asientos contables
edit_technical_user = Modificar el usuario t\u00E9cnico
Stage_APPROVED_B2G = Validada
AAP_EXPORTED = Imput. exportada
workflow_functions = Funciones
organization_VR1 = Empresa sujeta al r\u00E9gimen real mensual normal
organization_VR3 = Empresa sujeta al r\u00E9gimen simplificado del IVA
organization_VR2 = Empresa que opta por el r\u00E9gimen real trimestral normal
organization_VR4 = Empresa beneficiaria del r\u00E9gimen b\u00E1sico de exenci\u00F3n del IVA
error_creating_partnerConfig = Error al crear la configuraci\u00F3n de autenticaci\u00F3n para el socio {0}
invoice_posting_manual_entry = Registro manual de cuentas
select_duplicate_check = Comprobaci\u00F3n de duplicados
invoice_posting = Imputaci\u00F3n de facturas
compliance_life_cycle_approve_permissions = Aprobar
Stage_APPROVED_PARTIALLY = Aprobada parcialmente
COMPLETED_STYLE_STAGE = status_yellow
workflow_delegations = Delegaciones
RCL_QTY_DISCR = Discrep. de inf. cantidad
role_type = Tipo
Accounting_Active = Activo
idp_alias_for_usurpation = Alias de IDP para suplantaci\u00F3n de identidad
RCL_READY_STYLE = defecto
invidx_wkf_replayed = Flujos de trabajo reproducidos: {0}
api_security = Seguridad
api_permissions_get_perimeters = Per\u00EDmetros
periodic_reports = Informes peri\u00F3dicos
MISSING_PROCESSINGWAY = processingWay ausente
save_and_send_renewal_link = Guardar y enviar el enlace de renovaci\u00F3n
authentication_classicConfiguration = Configuraci\u00F3n cl\u00E1sica
accounting_posting_referentials_product = Productos
AAP_EXPORTED_STYLE = status_deep_blue
Stage_PAYMENT_RECEIVED = Cobrada
AAP_TRANSMITTED_STYLE = status_green
jsf_templates = Plantillas Jsf
exception_technicalRole_association = La funci\u00F3n {0} est\u00E1 asociada a usuarios usuario {1} t\u00E9cnicos
WKF_READY_STYLE = defecto
Stage_REFUSED = Negada
template_name = Nombre de la plantilla
AAP_NA = Imput. no aplica
AAP_MISSING_RCP = Imput. - recep. ausente. Imput. - recep. ausente
AAP_ERROR = Imput. en error
authentication_defaultClientIdJSF = ClientIdJSF por defecto
RCL_IMPOSSIBLE_STYLE = status_red
AAP_MISSING_REF = Imput. - refer. ausente
Stage_SUSPENDED = Suspendida
invalid_role = No tiene derechos para acceder al recurso.
Stage_OPENED = Cubierta
error_deletingCompanyWithAuthConfig = Esta empresa est\u00E1 vinculada a la configuraci\u00F3n del servidor de autenticaci\u00F3n. Elimine la configuraci\u00F3n de esta empresa antes de eliminarla
AAP_TRANSMITTED = Imput. transmitida
info_technicalUser_role_saved = Rol de usuario t\u00E9cnico registrado
Stage_PAYMENT_SENT = Pago transmitido
filter_sending = Emisi\u00F3n
technical_user_selectPartner = Seleccione un socio
AAP_MISSING_PO = Imput. - c\u00F3d. ausente
error_existingAuth_clientId = El clientId {0} ya existe en el dominio, por favor, elija otro clientId
SENT_STYLE_STAGE = status_green
error_saving_invoice_entry_preferences = Error al guardar las preferencias de facturaci\u00F3n
sso_error = Se ha producido un error durante el intento de autenticaci\u00F3n.
Stage_RECEIVED = Recibida por la plataforma
authentication_defaultClientIdAngular = ClientIdAngular por defecto
authentication_clientIdJSF = ClientIdJSF
compliance_life_cycle_payment_sent_permissions = Pago transmitido
APAUTOMATION = AUTOMATIZACI\u00D3N AP
error_remove_partnerConfig = Error al borrar la configuraci\u00F3n de autenticaci\u00F3n para el socio {0}.
authentication_selectPartner = Seleccione un socio
menu_user_invoice_entry_preferences = Preferencias de facturaci\u00F3n de mi empresa
RCL_DISCREPANCY_STYLE = status_orange
error_updating_user_keycloak = Error en la actualizaci\u00F3n del usuario de Keycloak {0}
AAP_MANUAL_MODE_STYLE = status_yellow
WKF_NA_STYLE = info
authentication_addPartnerConfiguration = A\u00F1adir una configuraci\u00F3n de socio
jsf_template = Plantilla Jsf
select_legal_controls_label = Aplicaci\u00F3n de controles legales
api_permissions_get_partners = Socios
portlet_referentiel_exchange_rates = Repositorio/Tipo de cambio
Stage_AVAILABLE = Disponible
organization_eReporting = Opci\u00F3n de eReporting:
error_creating_companyConfig = Error al crear la configuraci\u00F3n de autenticaci\u00F3n para la empresa {0}
instance_type_INVOICE_PDP = Factura - PDP
Stage_NOT_APPROVED_B2G = No validada
organization_referenceCurrency = Moneda de referencia
ROLE = Funci\u00F3n portal
imputation_status_unprocessed = Estado si no se aplica ning\u00FAn escenario de imputaci\u00F3n
APPROVED_STYLE_STAGE = status_green
AAP_READY = Listo para imputaci\u00F3n
bql_instructions_documents = Documento
WKF_READY = Listo para wkf
RCL_IMPOSSIBLE = Inf. imposible
ap_automation_aged_balance = Saldo envejecido
workflow_monitoring = Supervisi\u00F3n
WKF_NA = Wkf no aplica
accessTokenV1_password = Contrase\u00F1a
customer_technical_users = Clientes usuarios t\u00E9cnicos
AAP_IN_PROGRESS_AN_STYLE = status_yellow
ap_automation_tab = Automatizaci\u00F3n AP
error_switching_user_no_idp = Para poder suplantar, el alias IDP para la suplantaci\u00F3n debe estar configurado en el grupo principal del usuario.
error_creating_user_keycloak = El inicio de sesi\u00F3n ya existe en KC. No se puede crear.
authentication_clientId = ClientId
Accounting_Inactive = Inactivo
REFERENTIAL_ERROR = Error de directorio
AAP_COMPLETED_STYLE = status_green
period_specific_to_february = Periodicidad espec\u00EDfica de febrero
angular_templates = Plantillas Angular
AAP_MISSING_AN_REF = Imput. an. -refer. ausente
accessTokenV1_token = Token
portlet_invoice_archive_extend_archiving_duration = ARCHIVO DE FACTURAS: Ampliaci\u00F3n del tiempo de archivado
AAP_EXPORTED_TIMELINE = Imputaci\u00F3n contable: exportaci\u00F3n de asientos
exception_not_existing_partner = Socio {0} no existe
accounting_posting_tab = Imputaci\u00F3n contable
error_creating_clientId = Error al crear clientId {0}
select_legal_controls_source = Elegir
error_view_secret_key = Error al mostrar la clave secreta
accounting_posting_referentials_supplier = Proveedores
compliance_life_cycle_in_dispute_permissions = En litigio
organization_ER2 = Solicito a Generix que no extraiga ning\u00FAn dato de facturaci\u00F3n, prefiero proporcionar a Generix todos los datos de eReporting en un flujo separado.
organization_ER1 = Solicito a Generix que extraiga los datos de facturaci\u00F3n B2B internacional y se comprometa a proporcionar en un flujo separado los datos de facturaci\u00F3n de las facturas que no hayan pasado por el PDP.
info_technicalUser_saved = Usuario t\u00E9cnico {0} registrado
accounting_posting_management_setting = Configuraci\u00F3n
jsf_url = Url JSF
period_except_february = Periodicidad excepto febrero
client_api_id = ClientAPIId
PAYMENT_RECEIVED_STYLE_STAGE = status_green
filter_direction = Direcci\u00F3n del flujo del portlet
RCL_INTRUDER_PROD = Inf. - Producto intruso
error_creating_authConfig = Error al crear la configuraci\u00F3n de autenticaci\u00F3n del servidor
RCL_NA = Inf. no aplica
error_remove_companyConfig = Error al borrar la configuraci\u00F3n de autenticaci\u00F3n de la empresa
error_saving_technicalUser = Error al registrar el usuario t\u00E9cnico {0}
AAP_MISSING_RCP_STYLE = status_red
OPENED_STYLE_STAGE = status_green
RCL_QTY_DISCR_STYLE = status_orange
error_token_exchange_disabled = La funci\u00F3n de suplantaci\u00F3n no est\u00E1 disponible
ereporting = Ereporting
accessTokenV1_url = URL
WKF_VALIDATED = Wkf validado
accessTokenV1_use = Utilizaci\u00F3n
workflow_status_unprocessed = Estado si no hay flujo de trabajo aplicable
Stage_COMPLETED = Completada
workflow_management = Gesti\u00F3n
technical_user_clientId = ClientId
eReporting_frequency = Frecuencia de activaci\u00F3n autom\u00E1tica de las transmisiones eReporting al gobierno franc\u00E9s
error_creating_authConfig_missing_config = Para crear la configuraci\u00F3n de autenticaci\u00F3n, se deben definir los par\u00E1metros de autenticaci\u00F3n del grupo (Realm name, ClientAPIId, SecretAPIKey y Alias IDP para suplantaci\u00F3n).
UPLOADED_STYLE_STAGE = status_green
info_invoice_entry_preferences_saved = Actualizar mis preferencias de facturaci\u00F3n
text_to_double_message = debe ser un n\u00FAmero con coma decimal.
transactions = Transacci\u00F3n
api_permissions_get_invoice = Facturas
filter_receiving = Recepci\u00F3n
error_deletingPartnerWithAuthConfig = Este socio est\u00E1 vinculado a la configuraci\u00F3n del servidor de autenticaci\u00F3n del entorno. No puede suprimirse sin borrar previamente la configuraci\u00F3n que le concierne en el servidor de autenticaci\u00F3n
NOT_APPROVED_B2G_STYLE_STAGE = status_red
payments = Pagos
angular_page = P\u00E1gina Angular
WKF_REFUSED = Wkf rechazado
exception_not_match_companyCode = {0} no corresponde a ninguna sociedad
ap_automation_aged_balance_setting = Mostrar
REFERENTIAL_ERROR_STYLE = status_red
info_technicalUser_removed = Usuario t\u00E9cnico {0} eliminado
invidx_wkf_reinit_btn = Restablecer
menu_invoice_entry_preferences = Preferencias de entrada de facturas
Stage_REJECTED = Rechazada
contextual_validation_partner_e_reporting = eReporting debe ser ER1, ER2 o nulo
portlet_invoice_ocr_verify = FACTURA: Digitalizar en video
MISSING_PROCESSINGWAY_STYLE = status_red
existing_templates = Las siguientes plantillas ya existen en el entorno; por favor, seleccione las que desea sobrescribir con la nueva versi\u00F3n contenida en este archivo. Si no desea sobrescribir las plantillas existentes, no seleccione ninguna plantilla y valide la importaci\u00F3n para importar el proceso.
AAP_COMPLETED_AN = Imput. an. completa
REJECTED = Rechazada
partner_technical_users = Contactos t\u00E9cnicos
REJECTED_STYLE_STAGE = status_red
menu_security_technical_users = Usuarios del back office t\u00E9cnico
add_technical_user = Crear un usuario t\u00E9cnico
error_file_too_large = El tama\u00F1o del archivo supera el tama\u00F1o m\u00E1ximo autorizado de 2 Gb
AAP_IN_PROGRESS_AN = Imput. a\u00F1o en curso
eReporting_tab = e-Reporting
RCL_PRICES_DISCR_STYLE = status_orange
imenu_integration_recent_mapping = Mapeo reciente
general_invoice_pdp_service = Generix Invoice Services
compliance_life_cycle_subrogate_permissions = Subrogar
realm_name = Nombre del dominio
dedicated_instance = Dedicado
workflow_absences = Ausencias
keycloak = Keycloak
SUSPENDED_STYLE_STAGE = status_yellow
technical_roles = Funci\u00F3n t\u00E9cnica
reconciliation_status_unprocessed = Estado si no procede la conciliaci\u00F3n
view_secretKey = Ver la clave secreta
RCL_SUCCESS = Inf. positivo
authentication_configurationType = Tipo de configuraci\u00F3n
compliance_life_cycle_cdv_complete_permissions = CDV: Completar
error_remove_technicalUser = Error al borrar el usuario t\u00E9cnico
authentication_clientIdAngular = ClientIdAngular
tools = Herramientas
accessTokenV1_getTokenBtn = Obtener un token
contextual_validation_partner_vat_regime = R\u00E9gimen de IVA debe ser VR1, VR2, VR3, VR4 o nulo
api_messages = Mensajes
portlet_checklistsmanagement = Gesti\u00F3n de listas de control
AAP_IN_PROGRESS = Imput. en curso
filter_indifferent = Indiferente
WKF_VALIDATED_STYLE = status_green
idp_invalid_tokenUrl_error = La propiedad \u00ABToken URL\u00BB no es v\u00E1lida. Debe seguir el patr\u00F3n: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/token<br/>
adminClient_authorizationEnabled_disabled_error = El cliente con clientId {0} no tiene habilitada la propiedad 'Authorization Enabled'.
search_by_project = Buscar un proyecto
idp_linkingOnly_disabled_error = La propiedad \u00ABSolo vinculaci\u00F3n de cuentas\u00BB no est\u00E1 habilitada.<br/>
idp_invalid_firstLoginFlow_error = La propiedad \u00ABFirst Login Flow\u00BB debe tener el valor \u00AB{0}\u00BB<br/>
error_switching_user_no_authConfig = La funcionalidad de cambio de usuario no est\u00E1 disponible: No se ha a\u00F1adido ninguna configuraci\u00F3n de autenticaci\u00F3n.
keycloak_realm_required = Se requiere el dominio de Keycloak.
double_filter_error = No es posible utilizar ambos filtros al mismo tiempo.
keycloak_client_id_required = Se requiere el ID de cliente de Keycloak.
switchUser_enabled_info = Se han agregado las configuraciones necesarias para habilitar la funcionalidad de cambio de usuario para el cliente {0} en el dominio {1}
idp_invalid_syncMode_error = La propiedad \u201CSync Mode\u201D debe tener el valor \u201C{0}\u201D<br/>
channel_coded_message = Mensaje codificado
idp_configurations_error = Errores de configuraci\u00F3n de IDP:<br/>
error_reconfigureClientsForSwitching = Error al configurar los clientes para el cambio
keycloak_client_secret_required = Se requiere el secreto de cliente de Keycloak.
serviceAccountUser_tokenExchangePermission_error = El cliente con clientId {0} no tiene los derechos necesarios para comprobar si se ha habilitado el permiso de intercambio de tokens. Aseg\u00FArese de que tiene el rol {1} establecido en Roles de la cuenta de servicio
idp_permissions_disabled_error = Para habilitar la configuraci\u00F3n de la funcionalidad de cambio de usuario, deben activarse los permisos para el IDP con el alias {0}.
no_idp_configured_for_usurpation_error = No hay ning\u00FAn IDP con el alias {0} configurado para usurpaci\u00F3n en el dominio {1}
invalid_adminClient_error = No hay ning\u00FAn cliente con clientId {0} en el dominio {1}
idp_missingConfigOnPartner_warn = El IDP necesario para configurar la funcionalidad de cambio de usuario no se ha a\u00F1adido a las propiedades de autenticaci\u00F3n del socio {0}. Si confirma la adici\u00F3n de la configuraci\u00F3n de autenticaci\u00F3n para este socio, no se incluir\u00E1n las configuraciones necesarias para la funcionalidad de cambio de usuario.
idp_missingConfigOnCustomer_warn = El IDP necesario para configurar la funcionalidad de cambio de usuario no se ha a\u00F1adido a las propiedades de autenticaci\u00F3n del cliente en este entorno. Si confirma la adici\u00F3n de la configuraci\u00F3n de autenticaci\u00F3n, no se incluir\u00E1n las configuraciones necesarias para la funcionalidad de cambio de usuario.
idp_invalid_issuerUrl_error = La propiedad \u00ABIssuer\u00BB no es v\u00E1lida. Debe seguir el patr\u00F3n: {auth_base_url}/realms/{bo_realm}<br/>
idp_invalid_mapper_error = Para habilitar la configuraci\u00F3n de la funcionalidad de cambio de usuario, el mapeador definido en el IDP con el alias {0} debe tener \u00ABSync Mode Override\u00BB establecido en \u00ABinherit\u00BB, \u00ABMapper Type\u00BB establecido en \u00ABHardcoded Role\u00BB y \u00ABRole\u00BB establecido en \u00ABimpersonation\u00BB
impersonate_disabled_error = Para habilitar la configuraci\u00F3n de la funcionalidad de cambio de usuario, se deben activar los permisos para los usuarios en el dominio {0}
no_idp_onGroupAuthConfig = No hay ning\u00FAn alias IDP configurado en las configuraciones de autenticaci\u00F3n del grupo (socio/empresa)
no_impersonation_role_error = Para habilitar la configuraci\u00F3n de la funcionalidad de cambio de usuario, debe haber un rol llamado \u00ABsuplantaci\u00F3n\u00BB en el dominio {0}
discard = Descartar
duplicate_clientId_error = No es posible crear dos clientes con el mismo clientId {0}. Aseg\u00FArese de utilizar valores diferentes.
idp_hideOnLoginPage_disabled_error = La propiedad \u00ABOcultar en la p\u00E1gina de inicio de sesi\u00F3n\u00BB no est\u00E1 habilitada<br/>
send_renewal_link = Enviar enlace de renovaci\u00F3n de contrase\u00F1a
no_impersonationRolePolicy_error = Para habilitar la configuraci\u00F3n de la funcionalidad de cambio de usuario, debe haber una pol\u00EDtica de tipo de rol con el rol de \u00ABsuplantaci\u00F3n\u00BB marcado como requerido, a\u00F1adido al permiso de \u00ABsuplantar\u00BB en Usuarios
serviceAccountUser_noAdminRole_error = El usuario de la cuenta de servicio del cliente {0} no tiene los derechos necesarios para realizar acciones en el dominio {1} (gestionar otros clientes, usuarios, etc.). Aseg\u00FArese de que tiene el rol {2} establecido en Roles de la cuenta de servicio
channel_MDN = MDN
idp_no_mapper_error = Para habilitar la configuraci\u00F3n de la funcionalidad de cambio de usuario, debe haber un mapeador con el rol de \u00ABsuplantaci\u00F3n\u00BB definido en el IDP con el alias {0}.
idp_invalid_userInfoUrl_error = La propiedad \u00ABUser Info URL\u00BB no es v\u00E1lida. Debe seguir el patr\u00F3n: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/userinfo<br/>
tokenExchange_disabled_error = La funci\u00F3n de intercambio de tokens no est\u00E1 habilitada en el dominio {0}. Para redirigir p\u00E1ginas de Angular a JSF y tambi\u00E9n para utilizar la funcionalidad de cambio de usuario, este permiso debe estar habilitado.
idp_invalid_clientAuthMethod_error = La propiedad \u00ABClient Authentication\u00BB debe tener el valor \u00ABClient secret sent as post\u00BB<br/>
trigger_already_exists_warning = El desencadenante {0} ya existe en ese entorno. No se importar\u00E1 con este proceso.
idp_invalid_authorizationUrl_error = La propiedad \u00ABURL de autorizaci\u00F3n\u00BB no es v\u00E1lida. Debe seguir el patr\u00F3n: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/auth<br/>
search_by_process = Buscar un proceso
refresh_button = Restablecer filtro
impersonationRolePolicy_error = Para habilitar la configuraci\u00F3n de la funcionalidad de cambio de usuario, la pol\u00EDtica de tipo de rol \u00AB{0}\u00BB debe tener asignado el rol de \u00ABsuplantaci\u00F3n\u00BB y marcado como obligatorio
portlet_demat_partner_file_daily = DematPartnerFileDaily
auth_reconfigureClientsForUsurpation = Reconfigurar clientes para usurpaci\u00F3n
ACCOUNTINGPOSTING = CONTABILIZACI\u00D3N
idp_disabled_error = El IDP con alias {0} no est\u00E1 habilitado<br/>
idp_invalid_clientId_error = El ID de cliente no es v\u00E1lido. Comprueba la propiedad auth.server.backoffice.clientId