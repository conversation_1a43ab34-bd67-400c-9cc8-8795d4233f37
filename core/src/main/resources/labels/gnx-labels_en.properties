# locale: en
about=About TradeXpress Evolution
about_short=About
absence=Absence
agree=Agree
agreement=Service Agreement
agreement_lang_required=Service Agreement language is required
agreement_version_required=Service Agreement version is required
agreement_file_not_found={0} agreement file not found for {1} language
agreement_instance_required=Service Agreement instance is required
agreement_default_required=Service Agreement terms must be associated to the default language {0} (Service Agreement tab)
alpha=Alpha
alphanumeric=Alphanumeric
alphanumeric_underscore=Only alphanumeric and underscore are supported
accept=Accept
account=My Account
account_confirmation=Account confirmation creation
account_creation=Account creation
account_creation_confirm=Your account has been successfully created. You are going to receive a confirmation email to the address:
accounting=Customer Contact
action=Action
actions=Actions
action_global=Global action
action_line=Action on line
add=Add
add_above=Add above
add_below=Add below
add_certificate=Add certificate
add_child_page=Add child page
add_page=Add a page
add_icon_page=Add an icon page
pageIconPlaceholder=ex: fa fa-bar-chart
add_left=Add left
add_right=Add right
add_description=Add a description
administration=Administration
advSearch=Advanced search
analyze=Analyze
all=All
archive=Archive
archiving=Archiving
authentication=Authentication
authentication_mail_template=New contact notification email
author=Author
back=Back
backoffice=Back Office
backup=Backup
bad_account_or_password=Bad account or password.
banking=Banking
banking_partner=Banking partner
begin=Begin
boolean_false=False
boolean_select=--Select--
boolean_true=True
bql=BQL
button_edit=Edit
cancel=Cancel
control=Check
control_ean=Check length of EAN code field
correct=Correct
chat=Chat
choose=Choose
classificationPlan=Classification plan
clear=Clear
clear_page_confirm=Are you sure to clear this page content?
clear_portlet_confirm=Are you sure to clear this portlet content?
client=Client
clientPartners=My ${clients}
clientUsers=My users
close=Close
code=Code
company_code_validator=Code should respect the three capitalized letters format
instance_code_validator=Code should respect the three digits or capitalized letters format
partner_code_validator=Partner code should respect his customer three capitalized letters code format followed by his identification
collapse_all=Collapse All
comment=Comment
comment_upload_file=Please add a comment link to your attachment
communication=Communication
angular_version=Angular version
companies=Customers
company=Customer
company_edit= Edit customer {1} ({0})
company_add=Add customer
company_view=Customer {1} ({0})
company_number=${client} n\u00B0
company_society=Customer
complementary_info=Complementary Information
completion=Completion
completion_bounded=Completion Bounded
configuration=Configuration
configuration_page=Page configuration
configuration_portal=Portal configuration
configuration_portlet=Portlet configuration
confirm=Confirm
discard=Discard
confirm_task_result_reset=The task\\'s results will be deleted. Do you want to continue?
connect=Connect
contact=Contact
contacts=Contacts
contact_admin=Please contact your administrator.
contact_subject=Subject of the contact email
contact_recipients=Recipients of the contact email
contact_us=CONTACT
contact_validator=Contact email address is not valid. Multiple addresses are allowed, separated by ';'.
content=Content
Contract=RoquetteContract
copy=Copier
count=Count
country=Country
create=Create
create_mail=Create mail
create_notifications=Create notification
creation=Creation Date
cut=Couper
dashboard=Dashboard
date=Date
date_last_authentication=Last connection
date_last_authentication_no_date= Last connection date : Information unavailable
date_to=at
date_read=Read Date
day=Day
day_of_month=Day of month
day_of_week=Day of week
decimal_separator=Decimal Separator
default=default
delete=Delete
delete_select=Would you like to permanently delete the selected items ?
delete_select_single=Would you like to permanently delete the selected item ?
deny=Deny
deny_message=Please, describe your denial
deploy_process=Deploy process
deploy=Deploy
deployment=Deployment
search_by_process=Search for a process
search_by_project=Search for a project
refresh_button=Reset filter
double_filter_error=It is not possible to use both filters in the same time
description=Description
description_short=Short Description
details=Details
disable=Disable
disabled=Disabled
disagree=Disagree
display=Display
display_extension=Display extension
displayed_extension_name=Displayed extension name
document_children_policy=Children document policy
document_type=Document type
document=Document
documents=Documents
doc_edit=edit a document
domain=Domain
download=Download
duns=Reference
duplicate=Duplicate
duration=Duration
edit=Edit
edit_record=Edit
edit_mail=Edit mail
edit_notifications=Edit notification
editor=Editor
edocument=E-Document
eDocument=eDocument
domain_edit=Domain Edition
domain_modif=Edit domain
domain_create=Add domain
email=E-mail
emitter=Emitter
empty_page=Empty Page
enable=Enable
enabled=Enabled
enabled_linked_document_import=Enable import of document linked
encoding=Encoding
end=Fin
end_date=End date
error=Error
error_input_content_not_valid=The entered content is not valid : {0}
events=Events
execute=Execute
expand_all=Expand All
export=Export
export_extension_file=Export file
export_extension_all_files=Export files
export_list=Export list
export_portal=Exporter portal
extensions=Extensions
extension_label=Recording n\u00B0
extension_name=Extension Name
family=Family
field=field
field_missing=The field \"{0}\" is missing.
file=File
file_name=File name:
filter=Filter
freetext=Freetext
freetext_details=FreeText/Details
folder=Folder
folder_in=Input Folder
folder_out=Output Folder
taxOriginal=Set EDI file as tax original
format=Format
from_address=From address
from_address_validator=From address email is not valid. Only one address can be filled.
fullname=Fullname
gcn_identification_group=Generix Collaborative Network
gcn_subscriber_active=Generix client
gcnIdentification=GCN Id (gid)
gcnIdentification_error=GCN Id must contains at most 64 characters alphanumeric
gcnSubscriberIdentification=Generix Billing Id
gcnSubscriberIdentification_error=Generix Billing Id must contains at most 64 characters alphanumeric
general=General
general_edi_service=Generix EDI Services
general_invoices_service=Generix Invoices Services
general_supplier_service=Generix Suppliers Services
general_customers_service=Generix Customers Services
general_invoice_pdp_service=Generix Invoice Services
generate_gcn_subscriber=Generate ID
generate_new_password=Generate a password renewal link
generate_new_password_confirm=Are you sure you want to generate a password renewal link ?
generate_password=Generate password renewal link
identical_new_passoword=The new password is identical to the old password
generate_reset_link_expired=Password renewal link has expired.
generate_reset_link_invalid=Invalid password renewal link. Please contact your administrator.
good=good
help=Help
history=History
History=RoquetteHistory
home=Home
home_message=Accelerate your EDI/B2B roll-outs while reducing your costs with GCI Community Management
host=Host
hosts=Hosts
hour=Hour
hours=Hours
identification=Identification
identification_validator=Identification should contains only alphanumeric or '_' characters
iframe_error_message=Refused to display document because display forbidden by X-Frame-Options
ignored=Ignored
information=Information
information_system=Information System
import=Import
import_cert_jks_pkcs12=JKS/PKCS#12...
import_cert_pkcs11=PKCS#11...
import_cert_pem_cer_crt=PEM/CER/CRT
import_cert_pkcs7=PKCS#7
import_portal=Import portal
importInstance=Import (*.json)
importPageWarningText=The referenced portlet does not exist and the link will not be created. Do you want to import anyway?
indexClassName=Index Class
indexing=Indexing
infinite=Infinite
integration=Process: Activity
integrationnotification=Receive reporting for process integrations
dictionary=Business dictionaries
instance=Environment
instances=Environments
io=IO
insert=Insert
installation_detail=Installed Software
invalid_file_type=Invalid file type
invalid_file_csv_type=The extension file should be CSV
invalid_file_size=Invalid file size
invalid_file_special_char=The file can not contain special characters
invalid_file_accent_char=The file can not contain accented characters
invalid_generix_billing_id=Invalid Generix Billing ID
invalid_password=Invalid password. Please enter a new password.
is_locked=is locked
is_expired=is expired
invalid_role=You don't have the rights to access the resource.
sso_error=An error occurred while trying to authenticate.
keystores=Keystores
label_search=Enter your search here
label=Label
labels=Labels
lang_english=English
lang_french=French
language=Language
languages=Languages
last_modification=Last modification
locale_not_defined=Your language is not defined in your partners profile, please fill it
localisation=Portal Localisation
layout=Layout
left=Left
length=Length
length_max=Maximum Length
length_min=Minimum Length
library=Library
linked_certificates=Linked certificates
linked_document_import=Import attachment
linked_document_import_selection=Select one or more files
login=Login
login_validator=Login should contains at least 3 alphanumeric, '_', '-', '@' and '.' characters
logout=Sign Out
managed_by=Managed by
managed_by_order_issuer=Managed by OI
mark_as_read=Mark as read
max_size_of_linked_document=Max size of linked files
menurights = Menu Rights
companyinformation= My company informations
message=Message
messages=Messages
messaging=Messaging
milestone=Planned completion date
minutes=Minutes
modification=Modification date
modif_contact_partner= Contact partner modification
modify=Modify
monitoring=Activity
tools=Tools
invidx_wkf_reinit=Re-initialization of information about workflows in Invoice Index
invidx_wkf_reinit_btn=Re-initialization
invidx_wkf_replayed=Workflows replayed : {0}
month=Month
move_bottom=Move bottom
move_down=Move down
move_left=Move left
move_rigth=Move right
move_top=Move top
move_up=Move up
my_bank_accounts=My bank accounts
bank_accounts=Bank accounts
name=Name
jsf_url=JSF Url
name_executable=Name of executable
new_message=New message
number_connexion=Number of connections
number_reviewed_messages=Number of messages reviewed
numeric=Numeric
no=No
no_data=No data
no_processed=Not processed
no_processes_were_found=The RTE is not called by any process
none=None
notification=Notification
notifications=Notifications
notification_new_contact=Email notifications new contact
notify=Notify
occurence=Occurrences
ok=OK
order_issuer=Order Issuer
other=Other
page=Page
pages=Pages
page_new=New Page
page_noselection=No Selected Page
parameters=Parameters
parameter=Parameter
partner=Partner
partner_add= Add partner
partner_edit=Edit Partner {1} ({0})
partner_field_configuration=Partner's Fields Configuration
partner_field=Partner's Field
partner_view= Partner {1} ({0})
paste=Coller
period=Period
period_to=to
permission_missing=You have not enough permission to access this functionnality
permission_edit=Edit rights for
permission_required=Selection is required
permissions=Permissions
permissions_dialog_campaign_title=Add partners in the campaign
permissions_dialog_page_title=Define access rights to the page
phone=Phone
portal=Portal
portlet=Portlets
portlet_collection= Collection
portlet_checklistsmanagement=Checklists management
portlet_order=Order
portlet_referentiel_produits=Referential/Products
portlet_taxes_allowances = Allowances and para-fiscal Taxes
portlet_referentiel_taxes=Referential/Taxes
portlet_referentiel_global_allowances_charges=Referential/Global allowances & charges
portlet_referentiel_produits_remise_charge=Referential/Products/Allowances & charges
portlet_referentiel_produits_logistics_info=Referential/Products/Logistics info
portlet_referentiel_addresses=Referential/Addresses
portlet_referentiel_exchange_rates=Referential/Exchange rates
portlet_referentiel_produits_other=Referential/Products/Others
portlet_referentiel_produits_general_description=Referential/Products/General/Description
portlet_referentiel_produits_general_net_price=Referential/Products/General/Net Price
portlet_asn_ship=ASN: Ship
portlet_asn_import=ASN: Import
portlet_contact_us=Contact us
portlet_documentation=DOCUMENTATION
portlet_demat_partner_file=DematPartnerFile
portlet_demat_partner_file_daily=DematPartnerFileDaily
portlet_deadpool=Deadpool
portlet_faq=FAQ
portlet_freetext= FreeText
portlet_files=FILES
portlet_carousel=Carousel
portlet_indexdata_export=INDEXDATA: Export
portlet_order_confirm=ORDER: Confirm
portlet_order_confirm_with_modification=ORDER: Confirm with modification
portlet_order_mark_as_unread=ORDER: Mark as unread
portlet_order_actions_1=ORDER: Action 1
portlet_order_actions_2=ORDER: Action 2
portlet_order_actions_3=ORDER: Action 3
portlet_order_actions_4=ORDER: Action 4
portlet_order_actions_5=ORDER: Action 5
portlet_order_actions_6=ORDER: Action 6
portlet_order_actions_7=ORDER: Action 7
portlet_order_actions_8=ORDER: Action 8
portlet_order_actions_9=ORDER: Action 9
portlet_order_actions_10=ORDER: Action 10
filter_direction = Portlet flow direction
filter_indifferent = Indifferent
filter_sending = Sending
filter_receiving = Receiving


portlet_standardorderlist_export=STANDARD ORDER LIST: Export
portlet_standardorderlist_export_list=STANDARD ORDER LIST: Export List
portlet_standardorderlist_duplicate=STANDARD ORDER LIST: Duplicate
portlet_standardorderlist_remove=STANDARD ORDER LIST: Remove
portlet_standardorderlist_validate=STANDARD ORDER LIST: Validate manually
portlet_standardorderlist_refuse=STANDARD ORDER LIST: Refuse manually
portlet_standardorderlist_actions_1=STANDARD ORDER LIST: Action 1
portlet_standardorderlist_actions_2=STANDARD ORDER LIST: Action 2
portlet_standardorderlist_actions_3=STANDARD ORDER LIST: Action 3
portlet_standardorderlist_actions_4=STANDARD ORDER LIST: Action 4
portlet_standardorderlist_actions_5=STANDARD ORDER LIST: Action 5
portlet_standardorderlist_actions_6=STANDARD ORDER LIST: Action 6
portlet_standardorderlist_actions_7=STANDARD ORDER LIST: Action 7
portlet_standardorderlist_actions_8=STANDARD ORDER LIST: Action 8
portlet_standardorderlist_actions_9=STANDARD ORDER LIST: Action 9
portlet_standardorderlist_actions_10=STANDARD ORDER LIST: Action 10
portlet_invoice_join=INVOICE: Attach
portlet_invoice_open=INVOICE: Open
portlet_invoice_print=INVOICE: Print
portlet_invoice_diagnostic=INVOICE: Diagnostic
portlet_invoice_refused_manually=INVOICE: Refuse manually
portlet_invoice_forced=INVOICE: Force
portlet_invoice_add=INVOICE: Add
portlet_invoice_modify=INVOICE: Modify
portlet_invoice_import=INVOICE: Import
portlet_invoice_export=INVOICE: Export
portlet_invoice_export_list=INVOICE: Export list
portlet_invoice_view_attachment=INVOICE: View Attachment
portlet_invoice_view_history=INVOICE: Audit
portlet_invoice_remove=INVOICE: Remove
portlet_invoice_correct=INVOICE: Correct
portlet_invoice_actions_1=INVOICE: Action 1
portlet_invoice_actions_2=INVOICE: Action 2
portlet_invoice_actions_3=INVOICE: Action 3
portlet_invoice_actions_4=INVOICE: Action 4
portlet_invoice_actions_5=INVOICE: Action 5
portlet_invoice_actions_6=INVOICE: Action 6
portlet_invoice_actions_7=INVOICE: Action 7
portlet_invoice_actions_8=INVOICE: Action 8
portlet_invoice_actions_9=INVOICE: Action 9
portlet_invoice_actions_10=INVOICE: Action 10
portlet_invoice_ocr_verify=INVOICE: OCR Verify
portlet_invoice_archive_bulk_export=INVOICE ARCHIVE: Bulk export
portlet_invoice_archive_delete=INVOICE ARCHIVE: Delete
portlet_invoice_archive_extend_archiving_duration=INVOICE ARCHIVE: Extend archiving duration
portlet_payment_actions_1=PAYMENT: Action 1
portlet_payment_actions_2=PAYMENT: Action 2
portlet_payment_actions_3=PAYMENT: Action 3
portlet_payment_actions_4=PAYMENT: Action 4
portlet_payment_actions_5=PAYMENT: Action 5
portlet_payment_actions_6=PAYMENT: Action 6
portlet_payment_actions_7=PAYMENT: Action 7
portlet_payment_actions_8=PAYMENT: Action 8
portlet_payment_actions_9=PAYMENT: Action 9
portlet_payment_actions_10=PAYMENT: Action 10
portlet_payment_suggest_early_payment=Buyer Early payment
portlet_payment_accept_reject_early_payment=Seller Early payment
portlet_pack=Pack
portlet_uploadpdf=UploadPdf
portlet_uploadpdfclient=UploadPdfClient
portlet_referentiel_carrier=Referential/Carrier
portlet_switch_user=Switch user
portlet_reception_remove=RECADV: Remove

api_messages=Messages
api_permissions_get_invoice=Invoices
api_security=Security
api_permissions_get_partners=Partners
api_permissions_users=Users
api_permissions_roles=Roles
api_permissions_get_perimeters=Perimeters

life_cycle= Life cycle
compliance_life_cycle_approve_permissions=Approve
compliance_life_cycle_refuse_permissions=Refuse
compliance_life_cycle_subrogate_permissions=Subrogate
compliance_life_cycle_payment_sent_permissions=Payment sent
compliance_life_cycle_cdv_complete_permissions=CDV: Complete
compliance_life_cycle_cash_in_permissions=Cash in
compliance_life_cycle_factor_permissions=Factor
compliance_life_cycle_factor_confidential_permissions=Factor confidential
compliance_life_cycle_suspend_permissions=Suspend
compliance_life_cycle_in_dispute_permissions=In dispute
compliance_life_cycle_partially_approve_permissions=Partially approve

accounting_posting_tab=Accounting posting
accounting_posting_management= Management
accounting_posting_management_setting =Settings
accounting_posting_management_scenario=Scenarios
accounting_posting_referentials= Referentials
accounting_posting_referentials_supplier=Suppliers
accounting_posting_referentials_product=Products
accounting_posting_referentials_tax=Taxes
invoice_posting=Invoice Posting
invoice_posting_manual_entry=Manual account entry
manual_analytical_ventilation=Manual analytical ventilation
accounting_posting_entries=Accounting entries

consultation_status=Do not change the consultation status of the document to\" Read \"
preferences=Preferences
preview=Preview
preview_not_available=Preview not available
print=Print
profile=Profile
profile_title=My account
progress=In progress..
purge=Purge
prompt=Enter the password
quantityValue_validator=The quantity value is not valid
query=Filter
clear_query=Clear
quick_search=Quick Search
quick_search_loreal_order=Quick search on Brand, article, sign code, EAN
quicksight=QuickSight
readonly=Read-only
recent_business=Recent business
recent_mapping=Recent mapping
recipients=Recipients
refresh=Refresh
refuse=Refuse
regenerate_all=Regenerate all
regenerate_all_password=Regenerate all password
related_process=Search related processes
remindpassword=Remind Password
remove=Remove
removed=Removed
rename=Rename
rendered=Rendered
repository=Repository
reset=Reset
result=Results
required=Required
return=Return
return_to=Return to
rigth=Rigth
rte=RTE
rte_studio=RTE Studio
rte_collection=Collection
rte_file_or_folder_invalid_name=Name should be an alpahnumerical string without blank. '.', '_' et '-' are accepted.
roadmap=Roadmap
role=Role
roles_bo=Back Office Roles
roles=Roles
role_add= add
role_edit=Edit role
role_min=role
root=Root
save=Save
send_renewal_link=Send renewal link for password
schedule=Schedule
schema=Schema
scope=Scope
search=Search
seconds=Seconds
security=Security
select=Select
select_instance=Select an environment
selection=Selection
selection_or_enter=Choose or enter a value
select_one=Select One
send=Send
severity=Severity
show_triggers=Show triggers
trigger_already_exists_warning=The trigger {0} already exists in that environment. It will not be imported with this process.
site_optimized_for_ie9=Site optimized for Internet Explorer 9.
siret=Registration
size=Size
sort_by=Sort by
sort_order=Sort order
sort_order_ascending=Ascending
sort_order_descending=Descending
sources=Sources
state=State
statistics=Reporting
status=Status
start=Start
started=Started
start_date=Start date
stop=Stop
strong=strong
style=Style
styleClass=Style Classes
subject=Subject
switch_off=Connect as
switch_user=Switch user
task=Task
task_adv_search=Advanced search
task_adv_search_simple=Simple
task_adv_search_avancee=Advanced
task_create=Create task
task_edit=Edit task
task_last_completed=Last task completed
task_to_complete=Task to complete
task_types_revision=Visualization task definition
task_types=Task Types
tasks=Tasks
tasks_campaign=Tasks of workflow
tasks_noselection=No selected task
template=Template
jsf_template=Template Jsf
angular_template=Template Angular
angular_url=Angular url
template_edit=Editing the template
template_import=Import template
template_invalid_name=Name must contain between 3 and 18 lowercase characters and/or digits
template_invalid_pathname=Archive contains invalid characters in pathname
template_new=Create template
template_revision=Import email template
template_saved=Template saved
templates=Templates
jsf_templates=Templates Jsf
angular_templates=Angular Templates
test_send_mail=Mail auto-sending
to_edit=Edit
processing=Status
processed=Processed
process_msg=Process
theme=Theme
title=Title
tout=All
trf_required_document_type=Document type or collection is required in a document transformation configuration
trf_required_template_uri=Document transformation template URI is required
trf_generic=generic format
tva=VAT
type=Type
subtype=Subtype
undefine=Not dematerialized
unread_message=unread message(s)
unprocesses_message=unprocessed message(s)
upload_file=Upload File
upload_files=Upload Files
upload_file_limit=File limit reached
upload_invalid_file=Invalid file format
upload_invalid_size=Invalid file size
upload_with_conflict_conflict_message=Following files will be overwritten :
upload_with_conflict_title=Import archive
usage=Usage
user=User
users=Users
users_email=Users/email
users_creation=Users/password creation (no link)
user_company=Customer users
user_blocked=Your user account has been blocked. Please contact your administrator.
user_edit=Edit user
user_new=Create user
user_search=Refine your results by typing the name of the user that made the changes
ok_switch=Ok
validate=Validate
validator_email=Invalid email.
validator_password=Invalid password (at least 3 characters).
version=Version
version_technical=Technical reference
value=Value
value_default=Default value
variables=Variables
view=View
view_all=View All
weak=weak
week=Week
workflow=Workflow
writer=Writer
active_partners=MOST ACTIVE PARTNERS
year=Year
yes=Yes
allow_user_managing_tab=Allow users to manage this tab
connections=Connections
manage_rules=Manage rules

# self-register
check_site_conditions=I agree to the Terms and Conditions
client_number=${client} number
connection_code=Unique connection number
partner_with_code_missing=${client} number and/or registration number don't exist
save_register=I agree to register
self_register=Self-registration
self_register_bottom_msg=Once the registration done, you will receive an email with your temporary password. This password should be modified when you first access the account.
self_register_login=Register
self_register_ko=A problem was encountered during the registration, thank you for trying later
self_register_ok=Registration successfully done!<br>A password renewal link has been sent to you by email.
self_register_placeholder_1=Self-registration Placeholder 1
self_register_placeholder_1_placeholder=${client} number field from the self-registration page
self_register_placeholder_2=Self-registration Placeholder 2
self_register_placeholder_2_placeholder=Unique connection number field from the self-registration page
site_conditions_link=Terms and Conditions
user_firstname=First Name
user_firstname_placeholder=Your first name
user_lastname=Last Name
user_lastname_placeholder=Your last name
user_email=Email address
user_email_placeholder=Email address that will be used to access the account
users_number_exceeded=Number of users for a ${client} was exceeded. Please remove inactive users or contact support

# ADMIN INSTANCE SIDE MENU
imenu_general=General
imenu_general_instance=Environment
imenu_messaging=Messaging
imenu_security=Partners
imenu_repository=Repository
imenu_repository_documents=Documents
imenu_repository_dictionary=Business dictionary
imenu_repository_templates=Templates
imenu_portal=Collaborative
imenu_portal_p2p=Purchase to pay
imenu_portal_o2c=Order to cash
imenu_portal_einvoice=e-Invoice
imenu.portal=Collaborative
imenu_integration=Process
imenu_integration_recent_business=Recent process
imenu_integration_recent_mapping=Recent mapping
imenu_campaigns=Onboarding
imenu_report=Activity
imenu_portlet=Portlets

# LOGISTIC TAB
logistic=Logistics
logistic_gs1_company_prefix=GS1 company code
logistic_gs1_error_format_message=Make sure you have between 7 to 10 digits for GS1 company code
logistic_gs1_company_not_provided=Please fill in the GS1 company prefix in the Logistic Tab of your company
logistic_gs1_company_required=GS1 is mandatory because at least one partner use the automatic generation
logistic_extension=Extension
logistic_serial_reference=Serial Reference
logistic_serial_reference_error_format_message=Make sure you have between 6 to 9 digits for Serial reference
logistic_sscc=SSCC
logistic_sscc_fieldset=SSCC number
logistic_missing_sscc_message=Please fill in the required datas
logistic_sscc_auto=SSCC generation
logistic_extension_required=Extension character is mandatory because SSCC generation has been activated
logistic_serial_reference_required=Serial number is mandatory because SSCC generation has been activated.
logistic_serial_reference_and_cnuf_error_format_message=GS1 code and serial number lenght must be 16 characters.
logistic_missing_mandatories_message=Please fill all the required fields  because SSCC generation has been activated.


# ACCOUNT CREATION
confirmation_mail_subject=Account creation confirmation.
confirmation_mail_message_html=Hello {0},<br/><br/>Your account has been successfully created. Please confirm your email by clicking on the following link : <a href=\"{1}\">Email confirmation</a>.<br/></br>Alternatively, you can cut and paste the following address in your favorite browser: {1}.<br/><br/>See you soon!
confirmation_mail_message_text=Hello {0},\n\nYour account has been successfully created. Please cut and paste the following address in your favorite browser to confirm your account creation: {1}.\n\nSee you soon!=======

# MENU
menu=Menu rights
menu_company_information=My company information
company_information=Company information
menu_legal_information=Legal information
menu_company_addresses=Company addresses
menu_company_information_client=My ${client} information
menu_invoice_entry_preferences=Invoice entry preferences
menu_password_user=My password
menu_security_user=My password
menu_user_information=My account
menu_user_parameters=My settings
menu_admin=Administration
menu_audit=Audit
menu_campaigns=Onboarding
menu_config=Configuration
menu_dashboard=Dashboard
menu_domain=Domains
menu_instances=Environments
menu_message=Mail
menu_messages=Mails
menu_notification=Notification
menu_notifications=Notifications
menu_monitoring=Activity
menu_partners=Partners
menu_process=Process
menu_process_deployment=Process: Deployments
menu_process_execution=Process: Executions
menu_process_manage=Process Management
menu_process_reports=Process Reports
menu_process_trigger=Process: Triggers
translation=Translation
menu_rights_clients=Clients
menu_rights_users=Users
menu_rights_roles=Roles
menu_rights_perimeters=Perimeters
menu_search=Search
menu_security=Security
menu_security_companies=Customers
menu_security_groups=Groups
menu_security_order_issuers=Order Issuers
menu_security_partners=Partners
menu_security_users=Back Office Users
menu_security_technical_users=Back Office Technical Users
menu_statistics=Reporting
menu_system=System
menu_basket=Basket

# CAMPAIGN
CAMPAIGN=ONBOARDING
campaign=Campaign
campaigns=Onboarding
campaigns_new=New Campaign
campaign_name=Name
campaign_name_short=Short name
campaign_description=Description
campaign_date_creation=Creation Date
campaign_date_start=Started Date
campaign_date_end=Stopped Date
campaign_general=General
campaign_documents=Documents

# PARTNER
partners=Partners
partner_new=New partner
partner_name=Partner name
partner_identification=Identification
partner_contacts=Contacts
partner_parent=Parent
partner_comment=Comment
partner_address=Address
partner_imported=Partner imported : {0}.
partner_import_file=Partners file import (*.xls, *.xlsx)
partner_import_error=Error importing partners : {0}
partner_saved=Partner saved : {0}.
partner_save_error=Error saving partner : {0}.
partner_send_mail=Send account information
partner_deleted=Partner deleted: {0}.
partner_delete_error=Error deleting partner : {0}.
partner_delete_error_children_exist=Could not delete partner with sub-partners: {0}.
partner_user_add=Add contact
partner_import_already_existing_users= user(s) already exist(s)
partner_import_already_existing_partners= partner(s) already exist(s)
partner_import_no_user=Partner {0} has no user
contextual_validation_user_scope_partner=Partner code {0} does not belong to {1}
contextual_validation_user_scope_user=User {0} does not exist
contextual_validation_user_scope_user_cpy=User {0} does not belong to {1}
contextual_validation_user_scope_role=User {0} does not have the role {1}
contextual_validation_user_scope_partner_id=Partner code {0} and perimeter ID {1} should be different for the user {2}
contextual_validation_partner_role=Role has to exist
contextual_validation_partner_vat_regime=Vat Regime should be VR1, VR2, VR3, VR4 or null
contextual_validation_partner_e_reporting=eReporting should be ER1, ER2 or null

# FAQ/SURVEY
entry_question=Question
entry_answer=Answer
entry_new_answer=Option
entry_answers=Answers
entry_type=Type
entry_remove=Remove
entry_new=New entry
entry_add=Add
entry_add_question=Add a question
entry_add_title=Add Title
entry_delete_question=Delete a question
entry_edit_question=Edit a question
entry_import_faq=Import FAQ
entry_select_excel=Select an Excel file (*.xls, *.xlsx)
entry_text=Text
entry_radio=Radio
entry_checkbox=Checkbox
entry_title=Titre

# MAIL
contact_user=Contact user
other_variable=Other variable
reminder=Reminder
reminder_number=Reminder number
reminder_delay=Reminder delay
reminder_subject=Reminder subject
reminder_content=Reminder content
mail_content=Content
user_logged=Already logged
mail_sent=Sent
keyword_$login=User's login
keyword_$url=Application url
keyword_$password=Users's password
keyword_$contact=Contact
keyword_$company=Customer
keyword_$currentDate=Current date
keyword_$campaigncontactname=Campaign contact name
keyword_$campaigncontactphone=Campaign contact phone
keyword_$campaigncontactemail=Campaign contact email
keyword_$campaignvariable=Campaign free variable

# DOCUMENTS
docs_list=Documents list
doc_name=Document's name
doc_select=Select
doc_update=Update
doc_update_information=Update my information
doc_upload=Upload
doc_cancel=Cancel
doc_new_name=New name
doc_uploaded=Document uploaded: {0}
doc_uploaded_success=Document uploaded: {0}/{1}
doc_uploaded_update=updated document
doc_upload_duplicate=Duplicate: {0}
doc_upload_error=Error uploading document: {0} ({1})
doc_upload_disabled=You can't perform this action because the task is locked (a campaign may be in progress)
doc_rename_error=Document existing
doc_remove_error=Error removing document
doc_deleted=Document deleted
doc_downloads=Document downloads
docs_available=Documents available
docs_selected=Documents selected
file_docs_uploaded=The documents have been uploaded
file_process_will_be_executed=Process will be launched
file_exceeded_number=You cannot upload more than 10 documents simultaneously
no_files_choosen=No files choosen

#SSL Files
ks_alias_not_found=Alias not found : {0}. Found aliases, with ; separator : {1}
ks_cannot_determine_alias=Cannot determine alias to use. Found aliases, with ; separator : {0}
ks_cannot_load_keystore=Keystore not loadable. Check parameters. Contact an administrator.
ks_certificationpath=Chain of trust
ks_comment=Description
ks_config_require_name=The KeyStore friendly name is required
ks_config_require_usage=The KeyStore usage is required
ks_config_require_instance=The KeyStore instance is required
ks_entrydetailstab=Certificate
ks_entry_subject=Delivered to
ks_entry_notbefore=Valid from
ks_entry_notafter=Valid until
ks_entry_alias=Alias
ks_entry_alias_optional=Optional unless alias cannot be automatically determined
ks_entry_version=Version
ks_entry_issuer=Issuer
ks_entry_serialnumber=Serial number
ks_entry_signaturealgoname=Signature algorithm name
ks_entry_fingerprintmd5=Fingerprint (MD5)
ks_entry_fingerprintsha1=Fingerprint (SHA-1)
ks_error_could_not_find_nor_create_parent_for_friendly_name=Could not find nor create parent for friendly name {0}
ks_error_could_not_write_certificate_file=Could not write the document file for this certificate
ks_error_could_not_extract_certificate=Could not extract the certificate(s) contained in the file
ks_error_no_certificate_found=No certificate(s) found in the file
ks_error_multiple_parent_for_friendly_name=Inconsistent state : multiple parents found for friendly name {0}
ks_error_no_friendly_name_found=Friendly name not found in parameters and cannot be determined
ks_error_no_keystore_folder=Error - the keystore folder could not be retrieved
ks_filename=File name
ks_friendlyname=Friendly name
ks_hasprivatekey=With private key
ks_hasRSApublickey=RSA
ks_keystoredetailstab=Keystore details
ks_library=Library
ks_modulus_length=Modulus length (bits)
ks_morethanoneentry=Your keystore contains more than one entry. We can't load this sort of keystore.
ks_no_file_selected=A keystore file must be selected
ks_error_during_file_reading= An error occurred while reading the file
ks_no_key_found=No key found in keystore
ks_no_slot_index_selected=A slot index must be selected
ks_password=Password
ks_password_pkcs11=Password (PIN code)
ks_provide_friendly_name=Friendly name must be set
ks_provide_library=Library must be set
ks_publickeytab=Public key certificate
ks_remove_error=Error removing file : {0}
ks_remove_linked_partner_integrity=Operation blocked : certificate still linked to these groups : {0}
ks_remove_timestamp_server_integrity=Operation blocked : certificate still referenced in timestamp server
ks_slotIndex=Slot index
ks_label=Label
ks_type=Type
ks_unexpected_error_determing_alias=Unexpected error determining the alias
ks_unexpected_multiple_keys=Unexpected multiple keys in keystore
ks_unrecognized=Uploaded data can not be loaded as PKCS11, PKCS12 or JKS keystore.\n - Check the password.\n - Check your file. \n - Try to change the \"Unlimited Strength Jurisdiction Policy Files\" of your JRE.
ks_upload=Upload a keystore
ks_uploaddate=Creation Date
ks_uploaded=Keystore uploaded : {0}
ks_upload_select=Keystore (*.jks, *.p12, *.pfx)
ks_upload_error=Error uploading keystore : {0}
ks_usage_mismatch=Usage {0} must match parent usage {1}
ks_use_end_date=End of use
ks_use_period=Use period
ks_use_start_date=Start of use

#TEST
test_upload=Upload a test file

#ABOUT US
about_contact=Contact us
about_contact_email=Contact's email

#TEXT
text=Text

# ENUMERATES
# CompletionDefinition
AllUsers=For all the partner's users
AtLeastOneUser=At least one partner's user

# ContactMode
contact_mode=Media
fax=Fax

#REPORTS
report_partners_completion=PARTNERS COMPLETION
report_partners_completion_campaigns=PARTNERS COMPLETION
report_partners_status_by_month=PARTNERS STATUS EVOLUTION
report_partners_status=PARTNERS STATUS
report_no_data=NO DATA
report_not_completed=Not completed
report_completed=Completed
report_informations=Informations

#ORDERS
client_order=Client order
invoice=Invoice number
later_shipment_date=At the latest shipment date
order=Order
shipment_date=Shipment date
sooner_shipment_date=As soon as possible date

# USER ACCOUNT MAILS
creation_mail_subject=Your account has been created for the site {0}
creation_mail_message_html=Hello {0},<br/><br/>Please find below your connection informations to the {1} website ({2}): <br/><br/> <li>Login: {3} </li><li>Password renewal link: {4}</li><br/><br/>See you soon!
creation_mail_message_text=Hello {0},\n\nPlease find below your connection informations to the {1} website ({2}): \n\n- Login: {3}\n- Password renewal link: {4}\n\nSee you soon !

#CHANNELS
channels=Channels
channel=Channel
channel_add=Add a channel
channel_modify=Modify a channel
channel_add_type=Adding channel of type
channel_name=Channel name
channel_type_mandatory=You have to choose a channel type before clicking on add button.
channel_type_select=Channel type...
channel_type=Type
channel_desc=Description
channel_create_error=Error creating new channel
channel_save_error_null=Error while saving channel (channel to be saved == null).
channel_add_error_duplicatename=A channel already exists with this name. This channel cannot be created.
channel_save_ok=Channel saved.
channel_not_selected=No channel selected
channel_deleted=Channel deleted
channel_duplicate_error=Error while copying channel
channel_duplicate_ok=Channel duplicated
channel_delete_linked_chanel=Error while deleting channel. Existing alias to this channel:<br/>{0}
channel_toggle_linked_chanel=Error while modifying channel. Existing alias to this channel:<br/>{0}



channel_ftp_port=Port
channel_ftp_hostname=Server hostname
channel_ftp_username=User name

#CHANNEL AS2
channel_other=Other
channel_test=Test


url=Url
port=Port
timeout=Timeout
secure=Secure (HTTP/S)
keystore=Keystore

#CHANNEL AUTHENTICATION
channel_AuthenticationEndpointConfiguration=Authentication
channel_auth_userDnPattern=User DN pattern
channel_auth_userSearchBase=User search base
channel_auth_userSearchFilter=User search filter
channel_auth_groupSearchBase=Group search base
channel_auth_groupSearchFilter=Group search filter

#PROPERTY
property_not_integer=should be an integer.
property_required=is required.

#MESSAGES
#EXCEPTIONS
exception_message=An error occurred. \nPlease try again or contact your administrator\n ({0})
exception_backoffice_user={0} is a back-office user
exception_code_duplication=Duplicate code: {0}
exception_constraint_violation={0} : {1}
exception_duplicate_email=This email address already exists
exception_duplicate_login=Login already exists in AIO. It can not be created.
exception_duplicate_role=Duplicate role code: {0}
exception_failing_ACE_instanciation=Failed to instanciate ACE from type: {0}
exception_file_upload_unknown_request=Unknown request parameter: {0}
exception_id_duplication=Duplicate identification: {0}
exception_gcn_id_duplication=Duplicate GCN identification: {0}
exception_gcn_subscriber_id_duplication=Duplicate Generix Billing Id: {0}
exception_import_error_during_cloning=Error in clone Portal
exception_import_export_null=Export is null
exception_import_instance_code_null=Environment code is null
exception_import_instance_null=Environment in export file is null
exception_export_null=No Export find in file
exception_import_page_null= Page in export file is null
exception_import_portal_not_empty=Portal is not empty for environment {0}
exception_instance_associated={0} environment(s) associated
exception_instance_not_found=User {0} has no environment configured for domain {2}
exception_invalid_gson=Invalid file Gson
exception_json_export_error=Error during JSon export: {0}
exception_more_than_one_instance=User {0} associated to the customer {1} has more than one environment configured for domains {2} ({3})
exception_multiple_principal_groups_user=User {0} has more than one principal group ({1})
exception_no_backoffice_access={0} is not part of a partner or a customer and is not granted to access the back-office
exception_partner_associated={0} partner(s) associated
exception_exchange_associated={0} exchange(s) allowed associated
exception_partner_subgroups=Partner {0} has {1} sub-group(s)
exception_portlet_cloning=Problem during portlet cloning {0}
exception_removed_instance=Environment {0} has been removed
exception_role_has_users=Role {0} has {1} user(s) associated
exception_role_has_pages=Role {0} has {1} page(s) associated
exception_role_has_sso=Role {0} has sso role mapping(s) associated
exception_sending_mail_partner=Error sending mail to partner(s): {0}
exception_task_change_parent=Error of changeParent algorithm
exception_task_import=Problem of import tasks for name {0}
exception_task_import_parents=Problem of import tasksParent for name {0}
exception_task_type_not_found=Impossible to find task type {0}
exception_task_properties_not_found=Impossible to import task {0}, a rise of version is necessary
exception_unavailable_instance= {0} site is momentarily unavailable
exception_unknown_layout_type=Layout type unknown
exception_user_associated={0} user(s) associated
exception_user_no_associated_company=User {0} partner group {1} is not associated with any customer
exception_user_not_partner=User {0} principal group {1} is not a Partner or Customer group
exception_user_not_specified=User is not specified
exception_user_more_companies=User {0} partner group {1} has more than one customer associated ({2})
exception_access_denied=Access denied
exception_admin_url_portal=Please type a different username or contact your administrator to get your access portal URL
exception_user_not_part_of_app=This user is not part of the application, try another one.
exception_go_to_login=Go back to login

#ERRORS
error_duplicate_template=Template {0} already exists
error_file_too_large=Max file size is exceeded (2 Gb)
error_files_too_large=Max file size is exceeded (1.7 Gb)
error_changing_layout=Error changing page layout ({0})
error_creating_template_archive=Error creating template archive ({0})
error_editing_portlet=Error editing portlet ({0})
error_editing_default_host=Default host is not editable
error_exporting_instance=Export failed : {0}
error_exporting_page=Export failed
error_exporting_partner=Error exporting partners ({0})
error_exporting_client=Error exporting clients ({0})
error_file_not_found=File not found {0}
error_getting_portlet_content=Portlet {0}, Content {1}: {2}
error_importing_portlet=Page is not empty, you can not import json here
error_importing_instance=Error while import: {0}
error_importing_page= Error while import
error_importing_partner=Error importing partners ({0})
error_no_company_specified=No customer specified for partner edition: {0}
error_no_user_found=No user found with identifier: {0}
error_password_generation=An error occurred while generating the password
error_password_link_generation=An error occurred while generating the password renewal link
error_profile_name_mandatory=Profile name is mandatory
error_removing_company=Error removing customer ({0})
error_removing_file=Error removing file ({0})
error_removing_host=Error removing domain ({0})
error_removing_page=Error removing page ({0})
error_removing_partner=Error removing partner ({0})
error_removing_client=Error removing client ({0})
error_removing_portlet=Error removing portlet content ({0})
error_removing_portlet_content=Error deleting the contents of the portlet ({0})
error_removing_role=Error removing role ({0})
error_removing_row=Error removing row ({0})
error_removing_template=Error removing template ({0})
error_removing_user=Error removing user ({0})
error_saving_company=Error saving company.
error_saving_file=Error saving file ({0})
error_saving_host=Error saving domain ({0})
error_saving_host_not_unique=A domain already exists for this name
error_saving_instance=Error saving environment ({0})
error_saving_organization=Error, the item already exists
error_saving_partner=Error saving partner ({0})
error_saving_client=Error saving client ({0})
error_saving_permission=Error saving permission ({0})
error_saving_portal=Error saving portal ({0})
error_saving_portal_portlet_missing=Missing portlet configuration
error_saving_role=Error saving role ({0})
error_saving_user=Error saving user ({0})
error_creating_user_keycloak=Login already exists in KC. It can not be created.
error_updating_user_keycloak=Failed to update keycloak user {0}
error_saving_invoice_entry_preferences=Error saving invoice entry preferences
error_select_correct_user=Please, select a correct user
error_sending_new_password=An error occurred while sending password renewal link to {0} ({1})
error_switching_user=Error switching user ({0})
error_uploding_logo=Error loading logo {0}
error_uploding_template=Error loading template ({0})
error_user_without_primary_group=No primary group found for user duplication
error_save_user_no_env=Environment selection is required to notify the user
error_save_user_no_partner=Partner selection is required
error_saving=Error during saving ({0})
error_duplicate_configuration=Configuration already exists
error_export_empty_portlet=You cannot export an empty portlet;
error_export_shadow_portlet=You can not export this page because it contains at least one \"Shadow\" portlet
error_export_empty_page=You cannot export an empty page;
error_export_portlet_rte_collection= You cannot export the portlet Collection.
error_export_not_empty_portlet = You cannot export : the portlet is not empty
error_importing_Languages= An error occurred while importing portlets localisation file
error_invalid_row_length=[{0},{1}] Invalid row length expected ({2}) found ({3})
error_localizations_import_bad_structure=The file structure is not compliant regarding the expected format
error_invalid_number={0} : The entered content is not a number : {1}
error_invalid_date={0} : the entered content is not a date : {1}
error_switching_user_no_idp=The switch user functionality is unavailable: No IDP has been set up for switching
error_switching_user_no_authConfig=The switch user functionality is unavailable: No authentication configuration has been added

#WARNS
warn_app_template_deleting={0} template cannot be deleted.
warn_existing_resource=This resource already exists
warn_creation_succes=The creation succeeded
warn_creation_fail=The creation failed
warn_delete_fail=The suppression failed
warn_host_already_used=This domain is already used
warn_host_not_removed={0} has NOT been removed ({1} associated environment(s):CODE= {2})
warn_instance_code_already_used=This code is already used
warn_language_mandatory=Language configuration is mandatory
warn_locked_file=Locked file
warn_locked_folder=Locked folder
warn_no_type_selected=Please, select a type of task
warn_only_parent_empty=Only parent page should be defined as empty
warm_add_empty_when_other_exits=You can not add an empty disposition if there already exists a disposition
warm_change_into_empty_when_other_exists=You can not change this layout into empty one until in this page exists other layouts
warn_partner_profile_deleted=Partner profile should not be deleted
warn_portlet_content_not_found=Portlet's content with id {0} not found
warn_profile_already_exists=Profile already exists
warn_select_partner_notification=Please, select the partners to notify
warn_select_role=Please, select a role
cannot_assign_role=You have the permission to give a role but no role is configured.
warn_template_with_associated_host=Some domains are associated with the template
warn_user_missing=User is missing (see uid parameter)
warn_partner_missing=User is missing (see pcode parameter)
warn_deleting_own_user=It is not possible to delete the user {0}. This is your own user.
warn_import_pt_collection=If the portal use \"collection\" portlets, a configuration of those portlets will be mandatory
warn_portlet_localization_lang_not_supported={0} language not supported by the environment
warn_localizations_import_portlet_not_found=Portlet {0} not found in the portal definition
warn_importing_multiLevel_iconPages=The import cannot be done because an \"icon\" page can only have one level

#INFOS
info_company_removed=Customer {0} removed
info_company_saved=Customer {0} saved
info_company_created=Customer {0} created
info_file_removed=File removed
info_file_saved=File saved
info_host_removed=Domain {0} removed
info_host_saved=Domain {0} saved
info_instance_saved={0} environment saved
info_instance_toggled={0} environment toggled
info_import_portal=The portal has been imported
info_import_processes=The processes have been imported
info_logo_uploaded=Logo loaded
info_no_portlet_content_defined=Portlet has been set to this page without content
info_no_portlet_defined=Portlet has not been set for this page
info_partner_file_import={0} imported
info_partner_removed=Partner {0} removed
info_client_removed=Client {0} removed
info_partner_saved=Partner {0} saved
info_partner_saved_detail=Partner {0} saved : {1}
info_client_saved=Client {0} saved
info_portal_saved=Portal saved
info_portlet_removed=Portlet content removed
info_portlet_saved=Portlet content saved
info_role_removed=Role {0} removed
info_role_saved=Role {0} saved
info_user_new_password=A password renewal link has been sent to {0}
info_user_removed=User {0} removed
info_user_saved=User {0} saved
info_parameters_saved=User parameters saved
info_user_role_saved=User role saved
info_user_role_duplicated=The role {0} was duplicated
info_partner_role_saved=Partner role saved
info_portlet_localization_import=The portal localisation finished successfully. {0} message(s) added and  {1} modified
import_role_header= Roles file import (*.xls, *.xlsx)
info_import_page=Import page ok

#CONFIRM
confirm_instance_disable=All the environment\\'s services will be disabled (process, onboarding...).\\n\\nAre you sure you want to disable this environment?
confirm_instance_enable=All the environment\\'s services will be enabled (process, onboarding...).\\n\\nAre you sure you want to enable this environment?
confirm_file_delete=Are you sure you want to remove {0}
confirmScopeSelection=You will lose your selection of partner scopes

# PrimeFaces
no_records_found=No records found
no_records_found_loreal_order=No articles match your search. <br/> We invite you to modify this one.
no_extensions_found = No extensions found for this database
ui_converter=Value conversion error
ui_file_limit=Maximum number of files exceeded
ui_invalid_file=Invalid file type
ui_invalid_size=Invalid file size
ui_required=Value is required
ui_validator=Value is not valid

# Report
OK_icon=fa fa-check-circle-o
OK_color=#87b87f
OK_label=OK
APPROVED_icon=fa fa-check-circle-o
APPROVED_color=#87b87f
APPROVED_label=OK
WARN_icon=fa fa-exclamation-circle
WARN_color=#ffb752
WARN_label=WARNING
WARNING_icon=fa fa-exclamation-circle
WARNING_color=#ffb752
WARNING_label=WARNING
ERROR_icon=fa fa-times-circle-o
ERROR_color=#d15b47
ERROR_label=ERROR
FATAL_icon=fa fa-bomb
FATAL_color=#cc1e00
FATAL_label=FATAL
DISABLED_icon=fa fa-minus-circle
DISABLED_color=#d4d4d4
DISABLED_label=DISABLED
NONE_icon=fa fa-minus-circle
NONE_color=#d4d4d4
NONE_label=NONE
PENDING_icon=fa fa-cog fa-spin
PENDING_color=#bebfbb
PENDING_label=REPORT IN PROGRESS...
COMPLETED_icon=fa fa-check-circle-o
COMPLETED_color=#87b87f
COMPLETED_label=COMPLETED
STARTING_icon=fa fa-times-circle-o
STARTING_color=#bebfbb
STARTING_label=STARTING
STARTED_icon=fa fa-times-circle-o
STARTED_color=#bebfbb
STARTED_label=STARTED
STOPPING_icon=fa fa-exclamation-circle
STOPPING_color=#cc1e00
STOPPING_label=STOPPING
STOPPED_icon=fa fa-exclamation-circle
STOPPED_color=#cc1e00
STOPPED_label=STOPPED
FAILED_icon=fa fa-exclamation-circle
FAILED_color=#cc1e00
FAILED_label=FAILED
ABANDONED_icon=fa fa-exclamation-circle
ABANDONED_color=#cc1e00
ABANDONED_label=ABANDONED
UNKNOWN_icon=fa fa-exclamation-circle
UNKNOWN_color=#d4d4d4
UNKNOWN_label=UNKNOWN
edition=Edition
new=New
new_file=New file
new_folder=New folder
exit=Exit
themes=Themes
find=Find
replace=Replace
line_comment=Line comment

#Portlets Names
Calendar=CalendarDeprecated
CarrefourInvoiceEdition=CarrefourInvoiceDeprecated
CarrefourInvoice=CarrefourInvoice
DocumentBarChart=BarChart
DocumentBirt=Birt
DocumentCalendar=DocumentCalendarDeprecated
DocumentCounter=Counter
DocumentLineChart=LineChart
Documentation=DocumentationDeprecated
DocumentPieChart=PieChart
EDocuments=EdocumentsDeprecated
Factor=FactorDeprecated
FeedReader=FeedReaderDeprecated
Monitoring=MonitoringDeprecated
OrderLine=OrderLineDeprecated
Penalty=FranprixPenaltyDeprecated
PlanningSchedule=PlanningScheduleDeprecated
SafranInvoice=SafranInvoiceDeprecated
Survey=SurveyDeprecated
archiveimv3=IMV3 archive
CompleteEdition=CompleteEditionDeprecated
no_order_from_asn=No order found from the selected advance shipment notice

# Organization
organization_code=Identifier
organization_collaborativeId=GCN Id (gid)
organization_subscriberId=Generix Billing Id
connection_id= Unique identification number
organization_vat=VAT Code
organization_registration=Government reference
organization_duns=Reference
organization_phone=_phone
organization_fax=_fax
organization_email=_email
organization_web=_web
organization_orderContact=Contact
organization_orderPhone=Phone
organization_orderFax=Fax
organization_orderEmail=Email
organization_client=_client
organization_profile=Profile
organization_gcnSubscriber=Generix client
organization_shareCapital=Legal capital
organization_registerName=Register name
organization_legalStructure=Legal structure
organization_referenceCurrency=Reference currency
organization_optionReporting=Options for eReporting
organization_vatRegime=Option for the VAT regime:
organization_VR1=Company subject to the normal monthly real regime
organization_VR2=Company having opted for the real normal quarterly regime
organization_VR3=Company subject to the simplified VAT tax regime
organization_VR4=Company benefiting from the franchise regime based on VAT
organization_eReporting=Option for the eReporting:
organization_ER1=I ask Generix to extract the international B2B invoicing data and undertake to provide in a separate flow the invoicing data of invoices that have not passed through the PDP.
organization_ER2=I ask Generix not to extract any invoicing data, I prefer to provide Generix with all eReporting data in a separate feed.

organization_id=_id
organization_name=Code
organization_fullname=Name
organization_creation=_creation
organization_modification=_modification
organization_comment=Description
organization_description=_description
organization_userGroupAssociations=_userGroupAssociations
organization_location=_location
organization_parent=_parent
organization_children=_children
organization_logoSmall=_logoSmall
organization_logoMedium=_logoMedium
organization_logoLarge=_logoLarge
organization_freeText01=_freeText01
organization_freeText02=_freeText02
organization_freeText03=_freeText03
organization_freeText04=_freeText04
organization_freeText05=_freeText05
organization_freeText06=_freeText06
organization_freeText07=_freeText07
organization_freeText08=_freeText08
organization_freeText09=_freeText09
organization_freeLongText01=_freeLongText01
organization_freeLongText02=_freeLongText02
organization_freeBoolean01=_freeBoolean01
organization_freeBoolean02=_freeBoolean02
organization_freeDouble01=_freeDouble01
organization_freeDouble02=_freeDouble02
organization_freeDate01=_freeDate01
organization_freeDate02=_freeDate02
organization_freeViewConfiguration=Free fields
organization_freeViewProfile=Profile

organization_address_address=_address.address
organization_address_addressComplement=Addition to address
organization_address_country_country= ISO-2 country code
organization_address_country_displayCountry = Country
organization_address_country_iSO3Country = ISO-3 country code
organization_address_addressLine=Address
organization_address_streetName=Address
organization_address_postalCode=Postal Code
organization_address_city=City
organization_address_country=Country
organization_start=Start date
organization_end=End date
organization_role=Role

organization_autoGenerationOfSSCC=SSCC generation
organization_extension=Extension
organization_gs1=GS1 company code
organization_serialReference=Serial Reference

organization_bankAccount_currency=Currency
organization_bankAccount_iban=IBAN
organization_bankAccount_bic=BIC
organization_bankAccount_user=Created by
organization_bankAccount_partner=Partner
organization_bankAccount_date=Creation date


xPath=xPath

#Statuts
NONE=Unread
NONE_ICON=fa fa-envelope-o
NONE_STYLE=status_yellow
PENDING=Draft
PENDING_ICON=fa fa-eur
PENDING_STYLE=status_yellow
APPROVED=Approved
APPROVED_STYLE=info
ACCEPTED=Confirmed
ACCEPTED_ICON=fa fa-check
ACCEPTED_STYLE=info
ACCEPTED_WITH_AMENDMENT=Confirmed with amendment
ACCEPTED_WITH_AMENDMENT_ICON=fa fa-check-circle-o
ACCEPTED_WITH_AMENDMENT_STYLE=info
REFUSED=Refused
REFUSED_ICON=fa fa-exclamation-triangle
REFUSED_STYLE=status_red
SENT=Shipped
SENT_ICON=fa fa-paper-plane-o
SENT_STYLE=info
ACQUITTED=Acknowledged
ACQUITTED_STYLE=status_green
ARCHIVED=Archived
UNARCHIVABLE=Unarchivable
IN_SUBMISSION=In submission
SUBMITTED=Submitted
REMOVED=Removed
REMOVED_STYLE=default
ERROR=Error
ERROR_ICON=fa fa-remove
ERROR_STYLE=status_red
WARNING=Warning
WARNING_STYLE=status_yellow
DUPLICATE=Duplicated
DUPLICATE_STYLE=status_yellow
TO_VALIDATE=To Validate
TO_VALIDATE_ICON=fa fa fa-pause fa-fw
TO_VALIDATE_STYLE=status_yellow
UPDATED=Updated
UPDATED_STYLE=info
READ=Read
READ_ICON=fa fa-eye
READ_STYLE=info
CANCEL=Canceled
CANCEL_ICON=fa fa-times
CANCEL_STYLE=default
FATAL=System Error
FATAL_STYLE=status_red
SENT_PARTIALLY=Partially sent
SENT_PARTIALLY_ICON=fa fa-paper-plane
SENT_PARTIALLY_STYLE=status_yellow
INVOICED=Invoiced
INVOICED_STYLE=status_green
ANSWERED=Answered
ANSWERED_ICON=fa fa-undo fa-fw fa-rotate-90
ANSWERED_STYLE=info
PARTIALLY_ACCEPTED=Partially confirmed
PARTIALLY_ACCEPTED_STYLE=status_partially_accepted
PARTIALLY_ACCEPTED_WITH_AMENDMENTS=Partially confirmed with modifications
PARTIALLY_ACCEPTED_WITH_AMENDMENTS_STYLE=status_deep_blue
PARTIALLY_SHIPPED=Partially shipped
PARTIALLY_SHIPPED_ICON=fa fa-paper-plane
PARTIALLY_SHIPPED_STYLE=status_yellow
TIMEOUT=Timeout
TIMEOUT_ICON=fa fa-remove fa-fw
TIMEOUT_STYLE=status_red
TO_REMOVE=To Remove
TO_REMOVE_ICON=fa fa-archive
TO_REMOVE_STYLE=status_yellow
CLOSED=Closed
CLOSED_STYLE=default
DELIVERED=Delivered
DELIVERED_STYLE=status_green
SYNTAX_ERR=Syntax Err.
SYNTAX_ERR_STYLE=status_red
DEMAT_ERR=Demat Err.
DEMAT_ERR_STYLE=status_red
TO_CORRECT=To correct
TO_CORRECT_STYLE=status_yellow
OK=Ok
OK_STYLE=status_green
RESOLVED=Resolved
RESOLVED_STYLE=status_green
SHIPPED=Sent
SHIPPED_STYLE=status_green
ERR_XLEG=Ex. Leg. Err.
ERR_XLEG_STYLE=status_red
INTEGRATED=Integrated
INTEGRATED_STYLE=status_green
IN_DISPUTE=In dispute
IN_DISPUTE_STYLE=status_yellow
BLOCKED=Blocked
BLOCKED_STYLE=status_orange
TO_PAY=To pay
TO_PAY_STYLE=status_green
PAID=Paid
PAID_STYLE=status_green
NO_ROUTE=No Route
NO_ROUTE_STYLE=status_yellow
IMPORT_CORRECTION=Import correction
IMPORT_CORRECTION_STYLE=info
UNKNOWN=Unknown
UNKNOWN_STYLE=status_yellow
UNKNOWN_ICON=fa fa-remove

IN_PREPARATION=To prepare
IN_PREPARATION_STYLE=status_orderlist_yellow

IN_DELIVERY=In delivery
IN_DELIVERY_STYLE=status_orderlist_yellow

VALIDATED=Validated
VALIDATED_STYLE=status_orderlist_green

IN_VALIDATION=In validation
IN_VALIDATION_STYLE=status_orderlist_blue

REJECTED=Rejected
REJECTED_STYLE=status_red


#Workflow statuses
WKF_READY=Ready for wkf
WKF_READY_STYLE=default
WKF_IN_PROGRESS=Wkf in progress
WKF_IN_PROGRESS_STYLE=status_yellow
WKF_VALIDATED=Wkf validated
WKF_VALIDATED_STYLE=status_green
WKF_REFUSED=Wkf refused
WKF_REFUSED_STYLE=status_red
WKF_NA=Wkf not applicable
WKF_NA_STYLE=info

#Reconciliation statuses
RCL_READY=Ready for rcl
RCL_READY_STYLE=default
RCL_IMPOSSIBLE=Rcl not possible
RCL_IMPOSSIBLE_STYLE=status_red
RCL_INTRUDER_PROD=Rcl - Intruder product
RCL_INTRUDER_PROD_STYLE=status_orange
RCL_DISCREPANCY=Rcl discrepancy
RCL_DISCREPANCY_STYLE=status_orange
RCL_QTY_DISCR=Rcl qty discr.
RCL_QTY_DISCR_STYLE=status_orange
RCL_PRICES_DISCR=Rcl price discr.
RCL_PRICES_DISCR_STYLE=status_orange
RCL_SUCCESS=Successful rcl
RCL_SUCCESS_STYLE=status_green
RCL_NA=Rcl not applicable
RCL_NA_STYLE=info

#DB Schenker
REFERENTIAL_OK=Referential ok
REFERENTIAL_OK_STYLE=status_green
REFERENTIAL_KO=Referential ko
REFERENTIAL_KO_STYLE=status_red
CONTROL_OK=Controls ok
CONTROL_OK_STYLE=status_green
METADATA_KO=Metadata ko
METADATA_KO_STYLE=status_red
SIGNATURE_OK=Signature ok
SIGNATURE_OK_STYLE=status_green
SIGNATURE_KO=Signature ko
SIGNATURE_KO_STYLE=status_red
BEING_SENT_CLIENT=Being sent to client
BEING_SENT_CLIENT_STYLE=status_orange
AWAITING_VALIDATION=Awaiting validation
AWAITING_VALIDATION_STYLE=status_orange
SMTP_ERROR=Smtp error
SMTP_ERROR_STYLE=status_red
CONTROL_TOTAL_KO=Amount controls ko
CONTROL_TOTAL_KO_STYLE=status_red
END_PROCESS=End process
END_PROCESS_STYLE=status_green
RESENT=Resent
RESENT_STYLE=status_green
BEING_PROCESSED=Being processed
BEING_PROCESSED_STYLE=status_orange
REFERENTIAL_ERROR=Directory error
REFERENTIAL_ERROR_STYLE=status_red

#Stage
Stage_UNKNOWN=Unknown
Stage_UNDEFINED=Not dematerialized
Stage_CORRECT=Dematerialized
Stage_UPLOADED=Uploaded
Stage_RECEIVED=Received by the platform
Stage_REJECTED=Rejected
Stage_AVAILABLE=Available
Stage_OPENED=Opened
Stage_SUSPENDED=On hold
Stage_DISPUTED=Disputed
Stage_COMPLETED=Completed
Stage_APPROVED_PARTIALLY=Partially approved
Stage_PAYMENT_SENT=Payment issued
Stage_PAYMENT_RECEIVED=Payment received
Accounting_Active = Active
Accounting_Inactive = Inactive
Stage_ERROR=Error
Stage_REFUSED=Refused
Stage_SENT=Issued by the platform
Stage_APPROVED=Approved
Stage_APPROVED_B2G=Approved (B2G)
Stage_NOT_APPROVED_B2G=Not approved (B2G)
UNDEFINED_STYLE_STAGE=default
CORRECT_STYLE_STAGE=status_green
REFUSED_STYLE_STAGE=status_red
ERROR_STYLE_STAGE=status_red
UNKNOWN_STYLE_STAGE=status_yellow
UPLOADED_STYLE_STAGE=status_green
SENT_STYLE_STAGE=status_green
RECEIVED_STYLE_STAGE=status_green
REJECTED_STYLE_STAGE=status_red
AVAILABLE_STYLE_STAGE=status_green
OPENED_STYLE_STAGE=status_green
SUSPENDED_STYLE_STAGE=status_yellow
DISPUTED_STYLE_STAGE=status_yellow
COMPLETED_STYLE_STAGE=status_yellow
APPROVED_STYLE_STAGE=status_green
APPROVED_PARTIALLY_STYLE_STAGE=status_yellow
PAYMENT_SENT_STYLE_STAGE=status_green
PAYMENT_RECEIVED_STYLE_STAGE=status_green
APPROVED_B2G_STYLE_STAGE=status_green
NOT_APPROVED_B2G_STYLE_STAGE=status_red

#Archive
UNDEFINED_STYLE=default
ARCHIVED_STYLE=status_green
SUBMITTED_STYLE=status_green
IN_SUBMISSION_STYLE=status_yellow
UNARCHIVABLE_STYLE = status_red

autoGenerationOfSSCC = Generated SSCC

# column types
com_byzaneo_xtrade_api_DocumentStatus=Status
java_lang_Boolean=Boolean
java_lang_Class=Class
java_lang_Integer=Integer
com_byzaneo_xtrade_api_DocumentStage=State
java_lang_String=Text
java_math_BigDecimal=Decimal
java_util_Date=Date
javax_xml_datatype_XMLGregorianCalendar=XML Date
com_byzaneo_xtrade_api_DocumentConsultStatus=Consult Status

# Archiving policy
Cascade=Purge child documents
Detach=Detach child documents
Ignore=Ignore parent documents
doc_purge_children_policy=Linked documents

S=Sender
R=Receiver
SENDER=Sender
RECIPIENT=Receiver

# Demat partner Dialog
demat_partner_dialog_title=Update history
REFUSED_MANUALLY=Refused manually
REFUSED_MANUALLY_STYLE=default
FORCED=Forced
FORCED_STYLE=default

# OrderResponse Rights Portlet
portlet_orderresponse_export= ORDER RESPONSE: Export
portlet_orderresponse_print= ORDER RESPONSE: Print
portlet_orderresponse_import= ORDER RESPONSE: Import

# Portlet Categories
EDOCUMENT=INVOICING SERVICES
RTE=EDI SERVICES
GCN=COLLABORATIVE NETWORK
SMARTPDF=SUPPLIER PORTALS
CUSTOMERS=CUSTOMER PORTALS
DOC=KPI
DEPRECATED=DEPRECATED

# Application type
Development=Development
Acceptance=Acceptance
Preproduction=Pre-production
Production=Production
Unknown=UNKNOWN !

SYSTEMATIC= Always create the file
REPLACE_IF_EXISTS= Replace older files
NO_GENERATION_IF_EXISTS= No file if it exists already

#Factor
factor=Factor

# Application name
Noname=NO NAME !

#Bank Accounts
bank_accounts_dlg_header=Add bank account
bank_account_currency=Currency
bank_account_iban=IBAN
bank_account_bic=BIC

bank_account_name_required=The name is mandatory
bank_account_iban_error_exist=Sorry but there is already an IBAN associated to the selected currency. Please remove the IBAN before adding a new one
bank_account_iban_error_notValid=The IBAN is not valid
bank_account_iban_error_required=The IBAN is mandatory
bank_account_bic_error_required=The BIC code is mandatory
bank_account_bic_error_notValid=The BIC code must contain 8 to 11 characters
bank_account_delete_confirm=Are you sure you want to delete the IBAN for the currency
bank_account_deleting_error=Error deleting bank account {0}

#Environment Constraints
environment_error_max_characters_allowed = Maximum 64 characters are allowed

#Order Tracking 
to_validate = To validate
in_validation = In validation
validated = Validated
to_prepare = In preparation
in_delivery = In delivery
order_history_title = HISTORY OF MY ORDER N\u00B0 :
estimated_delivery_date = Estimated delivery date :
requested_delivery_date = Requested delivery date :
order_number = Order n\u00B0

#customer
customer_clientPartners=My ${clients}
customer_clientUsers=My users
customer_partner_number=${client} n\u00B0
customer_partner_name=Name
customer_partner_address_city=City
customer_partner_connectionCode=Unique connection N\u00B0
customer_partner_userNumber=Number of users
customer_partner_create=Add ${client}
customer_partner_create_button=Add ${client}
customer_partner_edit=${client}
customer_partner_edit_button=Update ${client}
customer_partner_delete=Delete ${client}
customer_partner_message_delete=The ${client} will be permanently deleted.
customer_partner_delete_yes=Yes I delete it
customer_partner_delete_no=No, I'm thinking
customer_partner_import=Import ${client} file
customer_partner_import_header=Import ${client} file (*.xls, *.xlsx)
customer_partner_import_button=Import the file
customer_partner_export=Export ${client} file
customer_partner_add_user=Add user
customer_partner_add_user_dialog_header=Add user to ${client}
customer_partner_name_for_user=${client} name
customer_partner_search_placeholder=Search ${client} by n\u00B0, name or city
partner_user_add_role=Give a role
partner_company=Customer
partner_client_name=${client} name
partner_address_1=Address 1
partner_address_2=Address 2
partner_postal_code=Postal Code
partner_city=City
partner_country=Country
partner_user=User
partner_user_create=Add user
partner_user_delete=Delete user
partner_user_send_new_pass=Reset password
partner_user_send_new_pass_header=Reset password for user
partner_user_message_send_new_pass_msg=An email containing a link will be sent to the user, which will allow him to change his password.
partner_user_send_pass_yes=Yes I'm sending it
partner_user_send_pass_no=No, I'm thinking
partner_user_roles=Role for user
partner_user_roles_perimeters=Role and perimeter for user
info_limited_extended_scope=Indicate in these fields the entities to which this user can have access. You can fill in the name of an or several entities or the name of a group of entities.
info_partners=Select here the name or the code of an entity.
info_perimeters=Select here the name of a group of entities.
partner_user_roles_save=Modify
partner_user_search_placeholder=Search user by n\u00B0, ${client} name, user, first name or last name
user_login=User
user_login_validator=User's id must be an email address.
user_email_address=Email
user_phone=Phone
user_mobile=Mobile
user_last_authentication=Last connection
user_number_connexion=Connection(s)
user_creation_date=Creation date
user_password_dialog_header=Add password
user_password_confirm=Confirm password
user_message_delete=The user will be permanently deleted.
user_empty_client=Choose a ${client}...
add_user_button=Add user
edit_user_button=Update user
add_user_mail_button=Add user + Reset password link
customer_partner_show_users=Users associated to client
users_search_placeholder=Search user by user, first name or last name

#Library Portlet
category = Category
Category.name = Category
category_field_empty = the category field have to be filled.
fileName = File name
introduction_placeholder = Introduction text Ex : \n Available documentation is categorized. Feel free to click on a category to discover the documents that we put at your disposal. If a document is missing you can report it via the contact page.
library_created_success=The documentation was created
library_edited_succes= The documentation was edited
library_deleted_success=The documentation was deleted
library_document_introduction = The available documentation is categorized. Feel free to click on a category to discover the documents that we put at your disposal.
portlet_library = Library
save_success=The whole library was saved.
info_category=To order the categories, it is necessary to make a drag / drop by going up the category from bottom to top
uploaded_since = Uploaded on
one_hour_past = less than an hour
add_library=Add a library
edit_library=Edit a library
a_day_past = less than a day
by_date = {0}
info_addressSearch=It is not possible to search by country field

# Shopping Cart
cart_view=VIEW CART
cart_checkout=CHECKOUT
cart_numberOfArticles=article(s) in the cart
cart_totalAmount=Total :
active_carts=cart(s) active(s)
total_carts=Total baskets :
validity_date=Validity date:
not_valid=Not valid

#RTE Status
rte_status_deployed = The RTE script is properly deployed
rte_status_deployed_but_changed = The RTE script has been modified since its deployment
rte_status_not_deployed = The RTE script is not deployed
rte_status_unknown = The file status could not be determined

# RTE Test Results
run_rte=Run
default_test_rte=Default test
individual_tests_rte=One test
rte_no_init_test_file=No init.tst default file found
rte_base_not_supported=RTE collections are outside the testing scope (at the moment)
rte_forbidden_value=init.tst: improper value for {0} property
rte_test_success_message=Rte test executed successfully
rte_test_fail_message=Rte test executed with failure
rte_test_properties_placeholder=Properties:
rte_test_results_header=Rte Test Results
rte_input_file_must_be_unique=Not a unique input file
rte_property_unknown=init.tst: unknown {0} property

#Save filters in local storage 
confirmationTitle=Confirmation
confirmOverrideFilter=This filter already exists. Do you want to replace it?
canNotSaveFilter =You can not save that filter because local storage is full
filterSuccessfullySaved=The filter has been saved

# Instance Types
instance_type=Portal type
instance_type_CUSTOMER=Customer
instance_type_EDI=EDI
instance_type_INVOICE=Invoice
instance_type_SPECIFIC=Specific
instance_type_SUPPLIER=Supplier
instance_type_INVOICE_PDP=Invoice - PDP

client_type_SPECIFIC=partner
client_type_EDI=partner
client_type_SUPPLIER=supplier
client_type_CUSTOMER=customer
client_type_INVOICE=partner
clients_type_SPECIFIC=partners
clients_type_EDI=partners
clients_type_SUPPLIER=suppliers
clients_type_CUSTOMER=customers
clients_type_INVOICE=partners

#edit role dialog
bql_filter_title=Filters
bql_filter_details=BQL query Invoice portlet filtering

#assignable roles
assignable_roles=Assignable roles

update_user_parameters=Update my parameters

triggername_ACTION_1=Action 1
triggername_ACTION_2=Action 2
triggername_ACTION_3=Action 3
triggername_ACTION_4=Action 4
triggername_ACTION_5=Action 5
triggername_ACTION_6=Action 6
triggername_ACTION_7=Action 7
triggername_ACTION_8=Action 8
triggername_ACTION_9=Action 9
triggername_ACTION_10=Action 10

#perimeter
perimeter=Perimeter
perimeters=Perimeters
error_saving_perimeter=Error saving perimeter ({0})
info_perimeter_saved=Perimeter {0} saved
info_perimeter_removed=Perimeter {0} removed
error_removing_perimeter=Error removing perimeter ({0})
exception_duplicate_perimeter=Duplicate perimeter name: {0}
exception_not_match_companyCode={0} did not match any Company's code
exception_not_existing_partner=Partner {0} does not exist
info_perimeter_duplicated=The perimeter {0} was duplicated
perimeter_edit=Edit perimeter for
exception_perimeter_has_partners=Perimieter {0} has {1} partner(s) associated
exception_perimeter_has_users=Perimieter {0} has {1} user(s) associated
scope_partner=Extended scope
scope_customer=Limited scope

#announcement
no_message_display=Do not show this message again

#cookie
cookie=Cookie

#SSO Authentication
regex=Regex
duplicate_mapping=This mapping already exists.
sso_role_mapping=Role Mapping
add_sso_role_mapping=Add a role mapping

#AccessTokenV1 Authentication
accessTokenV1_use=Use
accessTokenV1_url=URL
accessTokenV1_username=Username
accessTokenV1_password=Password
accessTokenV1_getTokenBtn=Get a token
accessTokenV1_token=Token
accessTokenV1_tokenExpirationDate=Token expiration date
accessTokenV1_access_fail=Cannot access this resource : {0}.
ROSSUM=ROSSUM
OTHER=OTHER

#document status
document_status=Document Status
add_new_status=Add a new document status
edit_status=Edit a document status
status_import_file= Document status import(*.xlsx)
document_status_added=The document status {0} was added
document_status_updated=The document status {0} was updated
document_status_exists=The document status {0} already exists
document_status_removed=The document status {0} was removed
document_statuses_updated={0} status(es) was\\were updated
document_statuses_added={0} new status(es) was\\were added
doc_status_code=Status code
doc_status_style=Status style

#Notifications/Workflow
workflow_checkbox=Authorize the configuration of workflow notifications by users
workflow_checkBox_help=If the box is checked, the user will be able to choose, from his profile, if he want to be notified of each new document
workflow_template=Template
workflow_status_unprocessed= Status if no applicable workflow
menu_user_notifications=My notifications
menu_user_partner_bank=Bank details of my company
menu_user_addresses=Addresses book of my company
menu_user_invoice_entry_preferences=My company's invoice entry preferences
menu_user_customer_client_env_bank=Bank details of my clients
menu_user_customer_supplier_env_bank=Bank details of my suppliers
menu_user_customer_other_env_bank=Bank details of my partners
general_notifications=My general notifications
activate_notifications=Activate my notifications
workflow_notifications=Manage my workflow notifications
update_user_notifications=Update my notifications
info_notifications_saved=User notifications saved
not_notified_if_absent_and_have_replacement=Not to be notified if I am absent and someone else is replacing me
notify_for_new_action_to_perform=Be notified each time I have an action to perform on an invoice
info_invoice_entry_preferences_saved=Invoice entry preferences saved

#IE banner
banner=Banner
checkbox_banner=Activate banner
banner_text=Text to display
ie_recognition=Internet Explorer Recognition

#Counter management
counter_management=Miniature counter management
counter_management_title=Counters to display
counter_management_other_page_configuration=Use setting from other page
counter_management_own_configuration=Define specific setting for this page
counter_management_counter=Counter
counter_management_pages=Pages
delete_counter_error_message=Warning, it is not possible to delete this counter because, it is used in mini-counters.

#instance KPI
database_kpi=KPI database
aws_batch=AWS Batch
arn=IAM role ARN
host_name=Host name
tcp_port=TCP port
database_name=Database name
cron_expression=Cron expression
placeholder_host_name=The host name or IP address of the system where the instance is running
placeholder_database_name=The name of the instance database
placeholder_tcp=The TCP port on which the instance listens
url_tracking=Enable URL tracking for every logged used
warn_kpi_invalid=The KPI service cannot be started. Please try again later or contact your administrator
kpis=KPIs

#Page configuration
page_display_option=Don't display in the menu
page_submenu_warning=Using a 3-level menu can cause display bugs. Remember to check the rendering of the menu on the front office.
angular_page=Angular page

#archive storage
archive_storage_option=Archive storage

#Payment status
EP_ELIGIBLE = Eligible
EP_INELIGIBLE = Ineligible
EP_SUBMITTED = Request in progress
EP_ACCEPTED = Request accepted
EP_REFUSED = Request refused
EP_PERFORMED = Performed payment

#ReconciliationEngine
reconciliation=Reconciliation
invoiceItem=InvoiceItemCodeField
orderItem=OrderItemCodeField
receptionItem=ReceptionItemCodeField
reconciliation_info=Configuration of the search key used to perform row reconciliation
reconciliation_status_unprocessed=Status if no applicable reconciliation
imputation_status_unprocessed=Status if no applicable imputation scenario

#Reconciliation
reconciliation_empty_seller_party=Reconciliation not possible : supplier code missing.
reconciliation_empty_order_number=Reconciliation not possible : order number missing.
reconciliation_order_not_found=Reconciliation not possible : order number {0} not found.
reconciliation_start=Reconciliation started.
reconciliation_success=Reconciliation done successful.
reconciliation_gap_found=Reconciliation done, differences found.

#e-Reporting
eReporting_tab=e-Reporting
period_except_february=Period except february
period_specific_to_february=Period specific to february
eReporting_frequency=French eReporting frequency
eReporting_collection=Select the collection

#Authentication server
authenticationServer_tab=Authentication server
authenticationServer_btn=Configure authentication server
auth_reconfigureClientsForUsurpation=Reconfigure clients for usurpation
authenticationServer_configuration=Authentication server configuration
authentication_configurationType=Configuration type
authentication_classicConfiguration=Classic configuration
authentication_togetherConfiguration=Together configuration
authentication_clientIdJSF=ClientIdJSF
authentication_clientIdAngular=ClientIdAngular
authentication_defaultClientIdJSF=Default clientIdJSF
authentication_defaultClientIdAngular=Default clientIdAngular
authentication_clientId=ClientId
authentication_defaultClientId=Default clientId
authentication_addPartnerConfiguration=Add partner configuration
authentication_partners_pairs=Partner configuration
authentication_preDomain=Pre-domain
authentication_partners=Partner
authentication_selectPartner=Select a partner
authentication_reconfiguration=Reconfigure
error_creating_clientId=Error creating clientId {0}
error_creating_authConfig=Error creating server authentication configuration
error_existingAuth_clientId=The clientId {0} already exists in the realm, please choose another clientId
error_creating_partnerConfig=Error creating authentication configuration for partner {0}
error_creating_companyConfig=Error creating authentication configuration for company {0}
error_deletingPartnerWithAuthConfig=This partner is linked to the authentication server configuration. Please first delete the configuration concerning this partner before deleting it
error_deletingCompanyWithAuthConfig=This company is linked to the authentication server configuration. Please first delete the configuration concerning this company before deleting it
error_remove_partnerConfig=Failed to remove authentication configuration for partner {0}
error_remove_companyConfig=Failed to remove authentication configuration for company {0}
error_creating_authConfig_missing_config=In order to create the authentication configuration, the group's authentication parameters must be set (Realm name, ClientAPIId, SecretAPIKey)
error_reconfigureClientsForSwitching=Error while configuring clients for switching

#Ordering Party
ordering_identifier=Identifier
ordering_name=Name
ordering_description=Description
ordering_number_of_partners=Number of partners
ordering_partners=Partners
ordering_messages_sequence=Messages sequence
info_ordering_removed=Ordering party {0} removed
info_ordering_saved=Ordering party {0} saved
ordering_identifier_exists=An ordering party with the identifier {0} already exists
add_ordering_party=Add an Ordering Party
edit_ordering_party=Ordering Party Edit
partners_ordering_party=Ordering Party\u2019s Partners
menu_ordering_party=Ordering Party
#ordering party diagram
button_message_sequence=Messages sequence
dialog_message_sequence_add=Messages sequence Add
dialog_message_sequence_edit=Messages sequence Edit
add_button_message_sequence=Add
delete_button_message_sequence=Delete
save_button_message_sequence=Save
cancel_button_message_sequence=Cancel
listbox_message_sequence_available_messages=Available Messages
info_ordering_model_saved=Ordering party model Saved
element_not_removed=First Element could not be deleted
element_order=Order
element_order_response=Order response
element_shipping_advice=Shipping advice
element_invoice=Invoice
element_arrow_optional=Optional
ordering_party_exit_without_saving=Do you want to exit without saving the diagram?
ordering_party_delete_last_element=Do you want to delete last Element?
element_checkbox_title_mandatory=Mandatory
element_contextmenu_config_form=Configure the form
use_ordering_party_configuration=Use ordering party's configuration
filter_by_type=Filter by type

mark_doc_as_unread_warn=Only document with a status of \"Read\" can be reset. No action taken.
mark_docs_as_unread_warn=Some documents do not have the status \"Read\". No action taken on these orders.

select_legal_controls_source=Select
select_legal_controls_label=Apply legal controls in legislation
select_duplicate_check=Duplicate check

select_rte_source=Select
select_rte_label=RTE source of controls
select_validation_process=Post-validation process
outputFile_working_copy=Create a [working] copy [of the output file]

text_to_double_message=must be a number consisting of one or more digits.

#SAP CONFIG
sap_configuration=SAP
sap_configuration_host=Host
sap_configuration_sysnr=Sysnr
sap_configuration_client=Client
sap_configuration_user=User
sap_configuration_password=Passwd
sap_configuration_language=Lang
sap_configuration_repository_optimization=Repository Roundtrip Optimization
sap_configuration_serialization_format=Serialization Format
sap_configuration_network=Network
sap_configuration_pool_size=Pool capacity
sap_configuration_expiration_time=Expiration time
sap_configuration_expiration_check=Expiration check period

#Import Archive BP
existing_templates=Those templates already exist in the environment, please select the templates you want to overwrite with the new version in this process archive. If you do not wish to overwrite the existing templates, do not select any template and validate the import to import the process.
template_name=Template name
template_type=Template type

#Workflow Rights
workflow_tab=Workflows
workflow_management=Management
workflow_functions=Functions
workflow_absences=Absences
workflow_delegations=Delegations
workflow_monitoring=Monitoring

#Instance
dedicated_instance=Dedicated

#Keycloak Authentication
keycloak=Keycloak
realm_name=Realm name
client_api_id=ClientAPIId
secret_api_key=SecretAPIKey
idp_alias_for_usurpation=IDP alias for usurpation
keycloak_client_id_required=Keycloak client ID is required
keycloak_client_secret_required=Keycloak client secret is required
keycloak_realm_required=Keycloak realm is required

#Technical users
customer_technical_users=Technical customer users
technical_user_clientId=ClientId
add_technical_user=Create technical user
edit_technical_user=Edit technical user
view_secretKey=View secret key
clientId_secretKey=ClientId secret key
error_existing_clientId=The clientId already exists in the realm, please choose another clientId
error_saving_technicalUser=Error saving technical user {0}
error_view_secret_key=Error viewing secret key
error_remove_technicalUser=Failed to remove technical user
info_technicalUser_saved=Technical user {0} saved
info_technicalUser_removed=Technical user {0} removed
technical_roles=Technical role

#Technical roles
role_type=Type
ROLE=Portal role
ROLE_TECHNICAL=Technical role
info_technicalUser_role_saved=Technical user role saved
exception_technicalRole_association=Role {0} has {1} technical user(s) associated
exception_technicalUsers_perimeterAssociation=Perimeter {0} has {1} technical user(s) associated

partner_technical_users=Technical contacts
technical_user_partner=Partner
technical_user_selectPartner=Select a partner
exception_remove_technicalUser_associated=The technical user(s) associated with {0} could not be deleted

#E-reporting
ereporting= Ereporting
transactions= Transaction


#BQL Instructions
bql_instructions_title=Title of the BQL instructions
bql_instructions_documents=Document

ACCOUNTINGPOSTING=ACCOUNTING POSTING
APAUTOMATION=AP AUTOMATION
manage_periodic_reports=Management of Periodic Reports
payments=Payments
periodic_reports =Periodic Reports

#KeycloakConfigurationValidator
serviceAccountUser_noAdminRole_error=The service account user of client {0} does not have the necessary rights to do actions on the realm {1} (manage other clients, users, etc). Please make sure it has the {2} role set in Service Account Roles
serviceAccountUser_tokenExchangePermission_error=The client with clientId {0} does not have the necessary rights to check if the 'token-exchange' permission has been enabled. Make sure it has the role {1} set in Service Account Roles
tokenExchange_disabled_error=The token-exchange feature is not enabled on realm {0}. In order to redirect pages from angular to JSF and also to use the switch user functionality, this permission must be enabled
adminClient_authorizationEnabled_disabled_error=The client with clientId {0} does not have the 'Authorization Enabled' property enabled
no_idp_configured_for_usurpation_error=No IDP with the alias {0} is configured for usurpation in realm {1}
no_idp_onGroupAuthConfig=There is no IDP alias set in the authentication configurations of group (partner/company)
idp_configurations_error=IDP configuration errors:<br/>
idp_disabled_error=The IDP with alias {0} is not enabled<br/>
idp_linkingOnly_disabled_error=The \"Account Linking Only\" property is not enabled<br/>
idp_hideOnLoginPage_disabled_error=The \"Hide on Login Page\" property is not enabled<br/>
idp_invalid_firstLoginFlow_error=The \"First Login Flow\" property must have the value \"{0}\"<br/>
idp_invalid_syncMode_error=The \"Sync Mode\" property must have the value \"{0}\"<br/>
idp_invalid_authorizationUrl_error=The \"Authorization URL\" property is not valid. It must follow the pattern: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/auth<br/>
idp_invalid_tokenUrl_error=The \"Token URL\" property is not valid. It must follow the pattern: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/token<br/>
idp_invalid_issuerUrl_error=The \"Issuer\" property is not valid. It must follow the pattern: {auth_base_url}/realms/{bo_realm}<br/>
idp_invalid_userInfoUrl_error=The \"User Info URL\" property is not valid. It must follow the pattern: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/userinfo<br/>
idp_invalid_clientAuthMethod_error=The \"Client Authentication\" property must have the value \"Client secret sent as post\"<br/>
idp_invalid_clientId_error=Client ID is not valid. Please check the auth.server.backoffice.clientId property
idp_permissions_disabled_error=To enable the configuration of the switch user functionality, the permissions for the IDP with alias {0} must be activated
idp_invalid_mapper_error=To enable the configuration of the switch user functionality, the mapper defined on the IDP with alias {0} must have \"Sync Mode Override\" set to \"inherit\", \"Mapper Type\" set to \"Hardcoded Role\" and \"Role\" set to \"impersonation\"
idp_no_mapper_error=To enable the configuration of the switch user functionality, there must be a mapper with the \"impersonation\" role defined on the IDP with the alias {0}
no_impersonation_role_error=To enable the configuration of the switch user functionality, there must be a role called \"impersonation\" in the realm {0}
impersonate_disabled_error=To enable the configuration of the switch user functionality, the permissions for users in the realm {0} must be activated
no_impersonationRolePolicy_error=To enable the configuration of the switch user functionality, there must be a role-type policy with the \"impersonation\" role marked as required, added to the \"impersonate\" permission on Users
impersonationRolePolicy_error=To enable the configuration of the switch user functionality, the role-type policy \"{0}\" must have the \"impersonation\" role assigned and marked as required
idp_missingConfigOnPartner_warn=The IDP required for configuring the switch user functionality has not been added to the authentication properties of partner {0}. If you confirm adding the authentication configuration for this partner, the necessary configurations for the switch user functionality will not be included
idp_missingConfigOnCustomer_warn=The IDP required for configuring the switch user functionality has not been added to the customer's authentication properties on this environment. If you confirm adding the authentication configuration, the necessary configurations for the switch user functionality will not be included
switchUser_enabled_info=The necessary configurations to enable the switch user functionality have been added for client {0} in realm {1}
duplicate_clientId_error=It is not possible to create two clients with the same clientId {0}. Please ensure you use different values
invalid_adminClient_error=There is no client with clientId {0} on realm {1}

#APP_POSTING
AAP_READY_STYLE=default
AAP_READY=Ready for A/c Posting
AAP_MISSING_PO_STYLE=status_red
AAP_MISSING_PO=A/c Posting - missing PO
AAP_UNBALANCED_STYLE=status_red
AAP_UNBALANCED=A/c Posting - unbal. inv.
AAP_MISSING_RCP_STYLE=status_red
AAP_MISSING_RCP=A/c Posting - missing Rcpt
AAP_MISSING_REF_STYLE=status_red
AAP_MISSING_REF=A/c Posting - missing Refer
AAP_IN_PROGRESS_STYLE=status_yellow
AAP_IN_PROGRESS=A/c Posting in progress
AAP_MANUAL_MODE_STYLE=status_yellow
AAP_MANUAL_MODE=A/c Posting in manual mode
AAP_COMPLETED_STYLE=status_green
AAP_COMPLETED=A/c Posting completed
AAP_TRANSMITTED_STYLE=status_green
AAP_TRANSMITTED=A/c Posting transmitted
AAP_ERROR_STYLE=status_red
AAP_ERROR=A/c Posting in error
AAP_NA_STYLE=info
AAP_NA=A/c Posting not applicable
AAP_MISSING_AN_REF_STYLE=status_red
AAP_IN_PROGRESS_AN_STYLE=status_yellow
AAP_COMPLETED_AN_STYLE=status_green
AAP_MISSING_AN_REF=A/n Posting missing Refer
AAP_IN_PROGRESS_AN=A/n Posting in progress
AAP_COMPLETED_AN=A/n Posting completed
AAP_EXPORTED_STYLE=status_deep_blue
AAP_EXPORTED=A/c Posting exported

AAP_EXPORTED_TIMELINE=Accounting posting : exportation of entries

MISSING_PROCESSINGWAY_STYLE=status_red
MISSING_PROCESSINGWAY=Missing processingWay

ap_automation_tab=AP automation
ap_automation_aged_balance=Aged balance
ap_automation_aged_balance_setting=Display
ap_automation_reconciliation_management=Reconciliation
ap_automation_reconciliation_management_scenario=Management / Scenarios

reminderNb = Number of day before reminder
channel_coded_message = Encrypted message
CORRECT = Dematerialized
portal_general = General
UNDEFINED = Not dematerialized
channel_MDN = MDN
templateUri = Mail template

OCR_TO_VERIFY=To videocode
OCR_TO_VERIFY_STYLE=default
OCR_POSTPONED=Suspended Videocoding
OCR_POSTPONED_STYLE=status_orange
OCR_VERIFYING=Videocoding
OCR_VERIFYING_STYLE=status_yellow