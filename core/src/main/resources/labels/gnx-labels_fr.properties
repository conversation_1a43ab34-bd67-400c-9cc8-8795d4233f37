# locale: fr
about=A propos de TradeXpress Evolution
about_short=A propos
absence=Absence
agree=Accepter
agreement=CGU
agreement_lang_required=La langue du CGU est obligatoire
agreement_version_required=La version du CGU est obligatoire
agreement_file_not_found=Le CGU {0} est introuvable pour la langue {1}
agreement_instance_required=Environnement du CGU obligatoire
agreement_default_required=Il est obligatoire d''associer des CGU \u00E0 la langue par d\u00E9faut {0} (onglet CGU)
alpha=Alpha
alphanumeric=Alphanumerique
alphanumeric_underscore=Seuls les caract\u00E8res alphanum\u00E9riques et l'underscore sont autoris\u00E9s
accept=Accepter
account=Mon compte
account_confirmation=Confirmation de cr\u00E9ation de compte
account_creation=Cr\u00E9er mon compte
account_creation_confirm=Votre compte a \u00E9t\u00E9 cr\u00E9\u00E9 avec succ\u00E8s. Vous allez recevoir sous peu un email de confirmation \u00E0 l'adresse\u00A0:
accounting=Contact Client
action=Action
actions=Actions
action_global=Action globale
action_line=Action sur la ligne
add=Ajouter
add_above=Ajouter au-dessus
add_below=Ajouter en-dessous
add_certificate=Ajouter un certificat
add_child_page=Ajouter page fille
add_page=Ajouter une page
add_icon_page=Ajouter une page ic\u00F4ne
pageIconPlaceholder=ex: fa fa-bar-chart
add_left=Ajouter \u00E0 gauche
add_right=Ajouter \u00E0 droite
add_description=Ajouter une description
administration=Administration
advSearch=Recherche avanc\u00E9e
analyze=Analyser
all=Tous
archive=Archive
archiving=Archivage
authentication=Authentification
authentication_mail_template=Email de notification nouveau contact
author=Auteur
back=Retour
backoffice=Back Office
backup=Sauvegarder
bad_account_or_password=Nom d'utilisateur inconnu ou mot de passe incorrect.
banking=Bancaire
banking_partner=Partenaire Bancaire
begin=D\u00E9but
boolean_false=Faux
boolean_select=--S\u00E9lectionner--
boolean_true=Vrai
bql=BQL
button_edit=\u00C9diter
cancel=Annuler
correct=Corriger
chat=Chat
choose=Choisir
classificationPlan=Plan de classement
clear=Vider
clear_page_confirm=\u00CAtes-vous s\u00FBr de vouloir supprimer le contenu de cette page\u00A0?
clear_portlet_confirm=\u00CAtes-vous s\u00FBr de vouloir vider le contenu de cette portlet\u00A0?
client=Client
clientPartners=Mes ${clients}
clientUsers=Mes utilisateurs
close=Fermer
code=Code
company_code_validator=Le code doit \u00EAtre form\u00E9 de trois lettres en majuscule.
instance_code_validator=Le code doit \u00EAtre form\u00E9 de trois chiffres ou lettres en majuscule.
partner_code_validator=Le code doit \u00EAtre form\u00E9 du code client (repr\u00E9sent\u00E9 de trois lettres en majuscule) suivi par \"-\" et son identification.
collapse_all=Tout r\u00E9duire
comment=Commentaire
comment_upload_file=Ajouter un commentaire \u00E0 votre fichier
communication=Communication
angular_version=Version Angular
companies=Clients
company=Client
company_edit=\u00C9diter client {1} ({0})
company_add=Ajouter client
company_view=Client {1} ({0})
company_number=${client} n\u00B0
company_society=Soci\u00E9t\u00E9
complementary_info=Informations Compl\u00E9mentaires
completion=Avancement
completion_bounded=Avancement D\u00E9l\u00E9mit\u00E9
configuration=Configuration
configuration_page=Configuration de la page
configuration_portal=Configuration du portail
configuration_portlet=Configuration du portlet
confirm=Confirmer
discard=Abandonner
confirm_task_result_reset=Les r\u00E9sultats de cette t\u00E2che seront supprim\u00E9s. Souhaitez-vous continuer\u00A0?
connect=Connecter
contact=Contact
contacts=Contacts
contact_admin=Veuillez contacter votre administrateur.
contact_subject=Sujet de l'email de contact
contact_recipients=Destinataires de l'email de contact
contact_us=CONTACT
contact_validator=L'adresse mail de contact n'est pas valide. Plusieurs adresses peuvent \u00EAtre remplies, s\u00E9par\u00E9es par le caract\u00E8re ';'.
content=Contenu
Contract=RoquetteContract
control=Contr\u00F4ler
control_ean=Contr\u00F4le longueur champ code EAN
copy=Copier
count=Total
country=Pays
create=Cr\u00E9er
create_mail=Cr\u00E9er un courrier
create_notifications=Cr\u00E9er une annonce
creation=Date de cr\u00E9ation
cut=Couper
dashboard=Tableau de bord
date=Date
date_last_authentication= Derni\u00E8re connexion
date_last_authentication_no_date= Date de derni\u00E8re Connexion : Information Indisponible
date_to=\u00E0
date_read=Date Lecture
day=Jour
day_of_month=Jours du mois
day_of_week=Jours de la semaine
decimal_separator=S\u00E9parateur d\u00E9cimal
default=d\u00E9faut
delete=Supprimer
delete_select=Souhaitez-vous supprimer d\u00E9finitivement les \u00E9l\u00E9ments s\u00E9lectionn\u00E9s\u00A0?
delete_select_single=Souhaitez-vous supprimer d\u00E9finitivement l'\u00E9l\u00E9ment s\u00E9lectionn\u00E9\u00A0?
deny=Refuser
deny_message=Veuillez motiver votre refus.
deploy_process=D\u00E9ployer le processus
deploy=D\u00E9ployer
deployment=D\u00E9ploiement
search_by_process=Rechercher un processus
search_by_project=Rechercher un projet
refresh_button=R\u00E9initialiser le filtre
double_filter_error=Il n''est pas possible d''utiliser les deux filtres en m\u00EAme temps

description=Description
description_short=Courte description
details=D\u00E9tails
disable=D\u00E9sactiver
disabled=D\u00E9sactiv\u00E9
disagree=Refuser
display=Affichage
display_extension=Afficher extension
displayed_extension_name=Nom affich\u00E9 pour l'extension
document_children_policy=Gestion des liens parents-enfants
document_type=Type de document
document=Document
documents=Documents
doc_edit=\u00C9diter un document
domain=Domaine
domain_edit= Modifier un domaine
domain_modif=Modifier un domaine
domain_create=Cr\u00E9er un domaine
download=T\u00E9l\u00E9charger
duns=RCS-RCM
duplicate=Dupliquer
duration=Dur\u00E9e
edit=\u00C9dition
edit_record=\u00C9diter
edit_mail=\u00C9diter le mail
edit_notifications=\u00C9diter l'annonce
editor=\u00C9diteur
edocument=E-Document
eDocument=eDocument
email=E-mail
emitter=\u00C9metteur
empty_page=Page Vide
enable=Activer
enabled=Activ\u00E9
enabled_linked_document_import=Activer l'import de document attach\u00E9
encoding=Encodage
end=Fin
end_date=Date de fin
error=Erreur
error_input_content_not_valid=Le contenu entr\u00E9e n''est pas valide : {0}
events=D\u00E9clencheurs
execute=Ex\u00E9cuter
expand_all=Tout d\u00E9ployer
export=Exporter
export_extension_file=Exporter le fichier
export_extension_all_files=Exporter les fichiers
export_list=Exporter la liste
export_portal=Exporter portail
extensions=Extensions
extension_label=Enregistrement n\u00B0
extension_name=Nom de l'extension
family=Famille
field=un champ
field_missing=Le champ \"{0}\" est manquant.
file=Fichier
file_name=Nom du fichier:
filter=Filtre
freetext=Champs libres
freetext_details=Champs libres/D\u00E9tails
folder=Dossier
folder_in=Dossier d'entr\u00E9e
folder_out=Dossier de sortie
taxOriginal=D\u00E9fini le fichier EDI comme original fiscal.
format=Format
from_address=\u00C9metteur
from_address_validator=L'adresse mail d'\u00E9metteur n'est pas valide. Une seule adresse peut \u00EAtre remplie.
fullname=Nom Complet
gcn_identification_group=Generix Collaborative Network
gcn_subscriber_active=Client Generix
gcnIdentification=GCN Id (gid)
gcnIdentification_error=Le GCN Id doit contenir au maximum 64 caract\u00E8res alphanum\u00E9riques
gcnSubscriberIdentification=Identifiant de facturation Generix
gcnSubscriberIdentification_error=L'identifiant de facturation Generix doit contenir au maximum 64 caract\u00E8res alphanum\u00E9riques
general=G\u00E9n\u00E9ral
general_edi_service=Generix EDI Services
general_invoices_service=Generix Invoices Services
general_supplier_service=Generix Suppliers Services
general_customers_service=Generix Customers Services
general_invoice_pdp_service=Generix Invoice Services
generate_gcn_subscriber=G\u00E9n\u00E9rer l'identifiant
generate_new_password=G\u00E9n\u00E9rer un email de r\u00E9initialisation du mot de passe
generate_new_password_confirm=Confirmez-vous la g\u00E9n\u00E9ration d\\'un email de r\u00E9initialisation du mot de passe ?
generate_password=G\u00E9n\u00E9rer le lien de renouvellement de mot de passe
identical_new_passoword=Le nouveau mot de passe est identique \u00E0 l\u2019ancien mot de passe
generate_reset_link_expired=Le lien de renouvellement de mot de passe a expir\u00E9.
generate_reset_link_invalid=Lien de renouvellement de mot de passe non valide. Veuillez contacter votre administrateur.
good=bien
help=Aide
history=Historique
History=RoquetteHistory
home=Accueil
home_message=Acc\u00E9l\u00E9rez vos d\u00E9ploiements EDI et B2B tout en r\u00E9duisant vos co\u00FBts avec GCI Community Management
host=H\u00F4te
hosts=H\u00F4tes
hour=Heure
hours=Heures
identification=Identification
identification_validator=Identification doit contenir des caract\u00E8res alphanum\u00E9riques ou '_'.
iframe_error_message=Impossible d'afficher le site car il ne le permet pas.
ignored=Ignor\u00E9s
information=Information
information_system=Information Syst\u00E8me
import=Importer
import_cert_jks_pkcs12=JKS/PKCS#12...
import_cert_pkcs11=PKCS#11 (RGS-2*)...
import_cert_pem_cer_crt=PEM/CER/CRT
import_cert_pkcs7=PKCS#7
import_portal=Importer portail
importInstance=Importation (*.json)
importPageWarningText=La portlet r\u00E9f\u00E9renc\u00E9e n'existe pas, le lien vers cette portlet ne sera pas cr\u00E9\u00E9, souhaitez-vous quand m\u00EAme importer ?
indexClassName=Classe de l'index
indexing=R\u00E9indexation
infinite=Infinie
integration=Processus: Activit\u00E9
integrationnotification=Recevoir rapport d'int\u00E9gration processus
dictionary=Dictionnaires m\u00E9tier
instance=Environnement
instances=Environnements
io=DO
insert=Ins\u00E9rer
installation_detail=Logiciels install\u00E9s
invalid_file_type=Type de fichier invalide
invalid_file_csv_type=Le fichier doit \u00EAtre de type CSV
invalid_file_size=Taille du fichier invalide
invalid_file_special_char=Le fichier ne peut contenir de caract\u00E8res sp\u00E9ciaux
invalid_file_accent_char=Le fichier ne peut contenir de caract\u00E8res accentu\u00E9s
invalid_generix_billing_id=L'identifiant de facturation Generix saisi n'est pas valide
invalid_password=Mot de passe non valide. Merci de saisir un nouveau mot de passe.
is_locked=est v\u00E9rrouill\u00E9.
is_expired=est expir\u00E9.
invalid_role=Vous n'avez pas les droits pour acc\u00E9der \u00E0 la ressource.
sso_error=Une erreur s'est produite lors de la tentative d'authentification.
keystores=Keystores
label_search=Pr\u00E9ciser votre recherche
label=Libell\u00E9
labels=Libell\u00E9s
lang_english=Anglais
lang_french=Fran\u00E7ais
language=Langue
languages=Langues
locale_not_defined=La langue de votre fiche partenaire n'est pas d\u00E9finie, veuillez le remplir
localisation=Localisation portail
last_modification=Derni\u00E8re modification
layout=Disposition
left=Gauche
length=Longueur
length_max=Longueur maximale
length_min=Longueur minimale
library=Librairie
linked_certificates=Certificats li\u00E9s
linked_document_import=Importer pi\u00E8ce jointe
linked_document_import_selection=S\u00E9lectionner et importer un ou plusieurs fichiers
login=Login
login_validator=Login doit contenir au moins 3 caract\u00E8res alphanumeriques, '_', '-', '@' ou '.'.
logout=D\u00E9connexion
managed_by=G\u00E9r\u00E9 par
managed_by_order_issuer=G\u00E9r\u00E9 par DO
mark_as_read=Marquer comme lu
max_size_of_linked_document=Taille maximum des fichiers attach\u00E9s
message=Email
menurights = Menu droit
companyinformation= Mes informations soci\u00E9t\u00E9
messages=Emails
messaging=Messagerie
milestone=Date pr\u00E9visionnelle de r\u00E9alisation
minutes=Minutes
modification=Date de modification
modif_contact_partner=Modification contact partenaire
modify=Modifier
monitoring=Activit\u00E9
tools=Outils
invidx_wkf_reinit=R\u00E9initialisation des informations sur les workflows dans l'Invoice Index
invidx_wkf_reinit_btn=R\u00E9initialisation
invidx_wkf_replayed=Workflows rejou\u00E9s : {0}
month=Mois
move_bottom=D\u00E9placer tout en bas
move_down=D\u00E9placer vers le bas
move_left=D\u00E9placer vers la gauche
move_rigth=D\u00E9placer vers la droite
move_top=D\u00E9placer tout en haut
move_up=D\u00E9placer vers le haut
my_bank_accounts=Mes comptes bancaire
bank_accounts=Comptes bancaire
name=Nom
jsf_url=Url JSF
name_executable=Nom de l'ex\u00E9cutable
new_message=Nouveau message
number_connexion=Nombre de connexions
number_reviewed_messages=Nombre de messages revus
numeric=Num\u00E9rique
no=Non
no_data=Pas de donn\u00E9es
no_processed=Not processed
no_processes_were_found=Le RTE n'est appel\u00E9 dans aucun processus
none=Aucun
notification=Notification
notifications=Notifications
notification_new_contact=Email notification nouveau contact
notify=Notifier
occurence=Nombre d'occurences
ok=OK
order_issuer=Donneur d'Ordres
other=Autre
page=Page
pages=Pages
page_new=Nouvelle Page
page_noselection=Pas de page selectionn\u00E9e
parameters=Param\u00E8tres
parameter=Param\u00E8tre
partner=Partenaire
partner_add=Ajouter Partenaire
partner_edit=\u00C9diter Partenaire {1} ({0})
partner_field_configuration=Champs de saisie libre partenaires
partner_field=Champs de saisie
partner_view= Partenaire {1} ({0})
paste=Coller
period=P\u00E9riode
period_to=au
permission_missing=Vous n'avez pas les permissions suffisantes pour acc\u00E9der \u00E0 cette fonctionnalit\u00E9
permission_edit=\u00C9diter permissions pour
permission_required=S\u00E9lection obligatoire
permissions=Permissions
permissions_dialog_campaign_title=Ajouter des partenaires \u00E0 la campagne
permissions_dialog_page_title=D\u00E9terminer les droits d'acc\u00E8s \u00E0 la page
phone=T\u00E9l\u00E9phone
portal=Portail
portlet=Portlets
filter_direction = Sens du flux de la portlet
filter_indifferent = Indiff\u00E9rent
filter_sending = Emission
filter_receiving = R\u00E9ception

portlet_collection= Collection
portlet_checklistsmanagement=Gestion des listes de contr\u00F4le
portlet_order=Commande
portlet_referentiel_produits=R\u00E9f\u00E9rentiel/Produits
portlet_taxes_allowances = Remises et Taxes para-fiscale
portlet_referentiel_taxes=R\u00E9f\u00E9rentiel/Taxes
portlet_referentiel_global_allowances_charges=R\u00E9f\u00E9rentiel/Remises et charges globales
portlet_referentiel_produits_remise_charge=R\u00E9f\u00E9rentiel/Produits/Remise et Charge
portlet_referentiel_produits_logistics_info=R\u00E9f\u00E9rentiel/Produits/Infos logistiques
portlet_referentiel_addresses=R\u00E9f\u00E9rentiel/Adresses
portlet_referentiel_exchange_rates=R\u00E9f\u00E9rentiel/Taux de change
portlet_referentiel_produits_other=R\u00E9f\u00E9rentiel/Produits/Autres
portlet_referentiel_produits_general_description=R\u00E9f\u00E9rentiel/Produits/G\u00E9n\u00E9ral/Description
portlet_referentiel_produits_general_net_price=R\u00E9f\u00E9rentiel/Produits/G\u00E9n\u00E9ral/Prix Net
portlet_referentiel_carrier=R\u00E9f\u00E9rentiel/Transporteur
portlet_asn_ship=ASN: Exp\u00E9dier
portlet_asn_import=ASN: Importer
portlet_contact_us=Contactez-nous
portlet_documentation=DOCUMENTATION
portlet_demat_partner_file=DematPartnerFile
portlet_demat_partner_file_daily=DematPartnerFileDaily
portlet_deadpool=Deadpool
portlet_faq=FAQ
portlet_freetext= FreeText
portlet_files=FILES
portlet_carousel=Carrousel
portlet_indexdata_export=INDEXDATA: Exporter
portlet_invoice_join=INVOICE: Joindre
portlet_invoice_open=INVOICE: Ouvir
portlet_invoice_print=INVOICE: Imprimer
portlet_invoice_diagnostic=INVOICE: Diagnostiquer
portlet_pack=Pack
portlet_switch_user=Usurper identit\u00E9
portlet_uploadpdf=UploadPdf
portlet_uploadpdfclient=UploadPdfClient

api_messages=Messages
api_permissions_get_invoice=Factures
api_security=S\u00E9curit\u00E9
api_permissions_get_partners=Partenaires
api_permissions_users=Utilisateurs
api_permissions_roles=R\u00F4les
api_permissions_get_perimeters=P\u00E9rim\u00E8tres

life_cycle= Cycle de vie
compliance_life_cycle_approve_permissions=Approuver
compliance_life_cycle_refuse_permissions=Refuser
compliance_life_cycle_subrogate_permissions=Subroger
compliance_life_cycle_payment_sent_permissions=Paiement transmis
compliance_life_cycle_cdv_complete_permissions=CDV : Compl\u00E9ter
compliance_life_cycle_cash_in_permissions=Encaisser
compliance_life_cycle_factor_permissions=Affacturer
compliance_life_cycle_factor_confidential_permissions=Affacturer confidentiel
compliance_life_cycle_suspend_permissions=Suspendre
compliance_life_cycle_in_dispute_permissions=En litige
compliance_life_cycle_partially_approve_permissions=Approuver partiellement

accounting_posting_tab=Imputation comptable
accounting_posting_management= Gestion
accounting_posting_management_setting =Param\u00E9trage
accounting_posting_management_scenario=Sc\u00E9narios
accounting_posting_referentials= R\u00E9f\u00E9rentiels
accounting_posting_referentials_supplier=Fournisseurs
accounting_posting_referentials_product=Produits
accounting_posting_referentials_tax=Taxes
invoice_posting=Imputation de facture
invoice_posting_manual_entry=Saisie manuelle des comptes
manual_analytical_ventilation=Ventilation analytique manuelle
accounting_posting_entries=Ecritures comptables

consultation_status=Ne pas changer l'\u00E9tat de consultation du document \u00E0 \"Lu\"
preferences=Pr\u00E9f\u00E9rences
preview=Aper\u00E7u
preview_not_available=Aper\u00E7u non disponible
print=Imprimer
profile=Profil
profile_title=Mon compte
progress=En cours..
purge=Purge
prompt=Entrez le mot de passe
quantityValue_validator=La valeur de la quantit\u00E9 n'est pas valide
query=Filtrer
clear_query=Effacer
quick_search=Recherche rapide
quick_search_loreal_order=Recherche rapide sur Marque, article, code enseigne, EAN
quicksight=QuickSight
readonly=Lecture seule
recent_business=Business r\u00E9cents
recent_mapping=Mapping r\u00E9cents
recipients=Destinataires
refresh=Rafraichir
refuse=Refuser
regenerate_all=Reg\u00E9n\u00E9rer tous
regenerate_all_password=Reg\u00E9n\u00E9rer tous les mots de passe
related_process=Chercher les processus li\u00E9s
remindpassword=Rappel Mot de Passe
remove=Supprimer
removed=Supprim\u00E9
rename=Renommer
rendered=Afficher
repository=R\u00E9f\u00E9rentiel
reset=R\u00E9initialiser
result=Resultats
required=Obligatoire
return=Retour
return_to=Retour \u00E0
rigth=Droite
rte=RTE
rte_studio=Studio RTE
rte_collection=Collection
rte_file_or_folder_invalid_name=Le nom ne doit contenir que des caract\u00E8res alphanum\u00E9riques sans espace. Les caract\u00E8res '.', '_' et '-' sont admis.
roadmap=Roadmap
role=R\u00F4le
roles_bo=R\u00F4les Back Office
roles=R\u00F4les
role_add=Ajouter un
role_edit=\u00C9diter le r\u00F4le
role_min=r\u00F4le
root=Racine
save=Enregistrer
send_renewal_link=Envoyer un lien de renouvellement de mot de passe
schedule=Fr\u00E9quence
schema=Schema
scope=P\u00E9rim\u00E8tre
search=Rechercher
seconds=Secondes
security=S\u00E9curit\u00E9
select=S\u00E9lection
select_instance=S\u00E9lectionner un environnement
selection=S\u00E9lectionner
selection_or_enter=Choisir ou saisir une valeur
select_one=Choisissez une option
send=Envoyer
severity=S\u00E9v\u00E9rit\u00E9
show_triggers=Visualiser les d\u00E9clencheurs
trigger_already_exists_warning=Le trigger {0} existe d\u00E9j\u00E0 dans l''environnement. Il ne sera pas import\u00E9 avec ce process.
site_optimized_for_ie9=Ce site est optimis\u00E9 pour Internet Explorer 9.
siret=SIREN
size=Taille
sort_by=Tri par
sort_order=Sens du tri
sort_order_ascending=Croissant
sort_order_descending=D\u00E9croissant
sources=Sources
state=\u00C9tat
statistics=Statistiques
status=Statut
start=D\u00E9marrer
started=D\u00E9marrage
start_date=Date de d\u00E9but
stop=Arr\u00EAter
strong=fort
style=Style
styleClass=Classes de style
subject=Sujet
switch_off=Connect\u00E9 en tant que
switch_user=Usurper identit\u00E9
task=T\u00E2che
task_adv_search=Recherche par d\u00E9faut
task_adv_search_simple=Simple
task_adv_search_avancee=Avanc\u00E9e
task_create=Cr\u00E9er une t\u00E2che
task_edit=\u00C9diter une t\u00E2che
task_last_completed=Derni\u00E8re t\u00E2che achev\u00E9e
task_to_complete=T\u00E2che \u00E0 effectuer
task_types_revision=Visualisation d\u00E9finition t\u00E2che
task_types=Type de T\u00E2ches
tasks=T\u00E2ches
tasks_campaign=Workflow de t\u00E2ches
tasks_noselection=Aucune T\u00E2che s\u00E9lectionn\u00E9e
template=Template
jsf_template=Template Jsf
angular_template=Template Angular
angular_url=Url angular
template_edit=\u00C9dition du template
template_import=Importer template
template_invalid_name=Le nom du template doit contenir entre 3 et 18 lettres minuscules et/ou chiffres.
template_invalid_pathname=L'archive contient des fichiers dont le nom est malform\u00E9
template_new=Cr\u00E9er un template
template_revision=Importer un template de mail
template_saved=Template sauvegard\u00E9
templates=Templates
jsf_templates=Templates Jsf
angular_templates= Templates Angular
test_send_mail=S'envoyer l'email
to_edit=\u00C9diter
processing=Statut
processed=Trait\u00E9
process_msg=Traiter
theme=Th\u00E8me
title=Titre
tout=Tout
trf_required_document_type=Le type de document ou sa collection est obligatoire.
trf_required_template_uri=L'URI du template est obligatoire.
trf_generic=format g\u00E9n\u00E9rique
tva=TVA
type=Type
subtype=Sous-type
undefine=Non d\u00E9mat\u00E9rialis\u00E9e
unread_message=message(s) non lu(s)
unprocesses_message=message(s) non trait\u00E9(s)
upload_file=Chargement du fichier
upload_files=Chargement des fichiers
upload_file_limit=Nombre maximum de fichiers atteints
upload_invalid_file=Format de fichier non valide
upload_invalid_size=Taille de fichier non valide
upload_with_conflict_conflict_message=Les fichiers suivants vont \u00EAtre \u00E9cras\u00E9s :
upload_with_conflict_title=Importer une archive
usage=Usage
user=Utilisateur
users=Utilisateurs
users_email=Utilisateurs/email
users_creation=Utilisateurs/cr\u00E9ation mdp (pas de lien)
user_company=Utilisateurs client
user_blocked=Votre compte a \u00E9t\u00E9 d\u00E9sactiv\u00E9. Veuillez contacter votre administrateur.
user_edit=\u00C9diter l'utilisateur
user_new=Cr\u00E9er l'utilisateur
user_search=Affiner votre recherche en tapant le nom de l'auteur des modifications.
ok_switch=Valider
validate=Valider
validator_email=Email incorrect.
validator_password=Mot de passse incorrect (minimum 3 caract\u00E8res).
version=Version
version_technical=R\u00E9f\u00E9rence technique
value=Valeur
value_default=Valeur par d\u00E9faut
variables=Variables
view=Voir
view_all=Tout voir
weak=faible
week=Semaine
workflow=Workflow
writer=R\u00E9dacteur
active_partners=PARTENAIRES ACTIFS
year=Ann\u00E9e
yes=Oui
allow_user_managing_tab=Permettre aux utilisateurs de g\u00E9rer cet onglet
connections=Connections
manage_rules=G\u00E9rer les r\u00E8gles
# self-register
check_site_conditions=Je valide les conditions d'utilisation du site
client_number=N\u00B0 ${client}
connection_code=N\u00B0 unique de connexion
partner_with_code_missing=Le num\u00E9ro ${client} et/ou le num\u00E9ro d'enregistrement n'existent pas
save_register=Je valide pour m'enregistrer
self_register=Auto-enregistrement
self_register_bottom_msg=Une fois l'enregistrement termin\u00E9, vous allez recevoir un email contenant votre mot de passe temporaire. Ce mot de passe devra \u00EAtre modifi\u00E9 \u00E0 la premi\u00E8re connexion.
self_register_login=Enregistrez-vous
self_register_ko=Nous d\u00E9tections un probl\u00E8me sur l'enregistrement de votre compte, merci d'essayer ult\u00E9rieurement
self_register_ok=L'enregistrement a \u00E9t\u00E9 fait avec succ\u00E8s!<br/>Un lien de renouvellement de mot de passe vous a \u00E9t\u00E9 envoy\u00E9 par email.
self_register_placeholder_1=Auto-enregistrement Placeholder 1
self_register_placeholder_1_placeholder=Champ N\u00B0 ${client} de la page d'auto-enregistrement
self_register_placeholder_2=Auto-enregistrement Placeholder 2
self_register_placeholder_2_placeholder=Champ N\u00B0 unique de connexion de la page d'auto-enregistrement
site_conditions_link=Conditions d'utilisation du site
user_firstname=Pr\u00E9nom
user_firstname_placeholder=Votre pr\u00E9nom
user_lastname=Nom
user_lastname_placeholder=Votre nom
user_email=Adresse email
user_email_placeholder=Adresse email qui servira \u00E0 la connexion
users_number_exceeded=Vous d\u00E9passez le nombre d'utilisateur pour un ${client}. Merci de supprimer les utilisateurs inactifs ou de contacter le support

# ADMIN INSTANCE SIDE MENU
imenu_general=G\u00E9n\u00E9ral
imenu_general_instance=Environnement
imenu_messaging=Messagerie
imenu_security=Partenaires
menu_user_parameters=Mes param\u00E8tres
imenu_repository=R\u00E9f\u00E9rentiel
imenu_repository_documents=Documents
imenu_repository_dictionary=Dictionnaire m\u00E9tier
imenu_repository_templates=Templates
imenu_portal=Collaboratif
imenu_portal_p2p=Purchase to pay
imenu_portal_o2c=Order to cash
imenu_portal_einvoice=e-Invoice
imenu.portal=Collaboratif
imenu_integration=Processus
imenu_integration_recent_business=Process r\u00E9cents
imenu_integration_recent_mapping=Mapping r\u00E9cents
imenu_campaigns=Onboarding
imenu_report=Activit\u00E9
imenu_portlet=Portlets
translation=Translation
# LOGISTIC TAB
logistic=Logistique
logistic_gs1_company_prefix=Code Entreprise GS1 ( CNUF )
logistic_gs1_error_format_message=Le code national unifi\u00E9 Fournisseur (GS1) doit \u00EAtre compos\u00E9 de 7 \u00E0 10 chiffres
logistic_gs1_company_not_provided= Merci de renseigner le Code National Unifi\u00E9 Fournisseur.
logistic_gs1_company_required=Le code GS1 est obligatoire car la g\u00E9n\u00E9ration automatique de SSCC est activ\u00E9e.
logistic_extension=Caract\u00E8re d'extension
logistic_extension_required=Le Caract\u00E8re d'extension est obligatoire car la g\u00E9n\u00E9ration automatique de SSCC est activ\u00E9e.
logistic_serial_reference=Num\u00E9ro S\u00E9quentiel
logistic_serial_reference_required=Le Num\u00E9ro S\u00E9quentiel est obligatoire car la g\u00E9n\u00E9ration automatique de SSCC est activ\u00E9e.
logistic_serial_reference_error_format_message=Le num\u00E9ro s\u00E9quentiel doit \u00EAtre compos\u00E9 de 6 \u00E0 9 chiffres
logistic_serial_reference_and_cnuf_error_format_message=La longueur du CNUF et du num\u00E9ro s\u00E9quentiel doit \u00EAtre de 16 caract\u00E8res.
logistic_sscc=SSCC
logistic_sscc_fieldset=Num\u00E9ro SSCC
logistic_missing_sscc_message=Veuillez renseigner l'ensemble des informations requises
logistic_missing_mandatories_message=Veuillez renseigner l'ensemble des informations requises car la g\u00E9n\u00E9ration automatique de SSCC est activ\u00E9e
logistic_sscc_auto=SSCC automatique

# ACCOUNT CREATION
confirmation_mail_subject=Account creation confirmation.
confirmation_mail_message_html=Hello {0},<br/><br/>Your account has been successfully created. Please confirm your email by clicking on the following link: <a href=\"{1}\">Email confirmation</a>.<br/></br>Alternatively, you can cut and paste the following address in your favorite browser: {1}.<br/><br/>See you soon!
confirmation_mail_message_text=Hello {0},\n\nYour account has been successfully created. Please cut and paste the following address in your favorite browser to confirm your account creation: {1}.\n\nSee you soon!=======

# MENU
menu=Menu droit
menu_company_information=Mes informations soci\u00E9t\u00E9
company_information=Informations soci\u00E9t\u00E9
menu_legal_information=Informations l\u00E9gales
menu_company_addresses=Adresses soci\u00E9t\u00E9
menu_company_information_client=Mes informations ${client}
menu_invoice_entry_preferences=Pr\u00E9f\u00E9rences de saisie de facture
menu_security_user=Mon mot de passe
menu_password_user=Mon mot de passe
menu_user_information=Mon compte
menu_admin=Administration
menu_audit=Audit
menu_campaigns=Onboarding
menu_config=Configuration
menu_dashboard=Tableau de Bord
menu_domain=Domaines
menu_instances=Environnements
menu_message=Email
menu_messages=Emails
menu_notification=Annonce
menu_notifications=Annonces
menu_monitoring=Activit\u00E9
menu_partners=Partenaires
menu_process=Processus
menu_process_deployment=Processus: D\u00E9ploiement
menu_process_execution=Processus: Ex\u00E9cutions
menu_process_manage=Gestion
menu_process_reports=Rapports
menu_process_trigger=Processus: D\u00E9clencheurs
menu_rights_clients=Clients
menu_rights_users=Utilisateurs
menu_rights_roles=R\u00F4les
menu_rights_perimeters=P\u00E9rim\u00E8tres
menu_search=Rechercher
menu_security=S\u00E9curit\u00E9
menu_security_companies=Clients
menu_security_groups=Groupes
menu_security_order_issuers=Donneurs d'Ordre
menu_security_partners=Partenaires
menu_security_users=Utilisateurs Back Office
menu_security_technical_users=Utilisateurs Technique Back Office
menu_statistics=Reporting
menu_system=Syst\u00E8me
menu_basket=Panier

# CAMPAIGN
CAMPAIGN=ONBOARDING
campaign=Campagne
campaigns=Onboarding
campaigns_new=Nouvelle campagne
campaign_name=Nom
campaign_name_short=Nom court
campaign_description=Description
campaign_date_creation=Date cr\u00E9ation
campaign_date_start=Date d\u00E9but
campaign_date_end=Date fin
campaign_general=G\u00E9n\u00E9ral
campaign_documents=Documents

# PARTNER
partners=Partenaires
partner_new=Nouveau partenaire
partner_name=Nom Partenaire
partner_identification=Identification
partner_contacts=Contacts
partner_parent=Parent
partner_comment=Commentaire
partner_address=Adresse postale
partner_imported=Partenaires import\u00E9s\u00A0: {0}.
partner_import_file=Import d'un fichier de partenaires (*.xls, *.xlsx)
partner_import_error=Erreur lors de l'import de partenaires\u00A0: {0}
partner_saved=Sauvegarde du partenaire\u00A0: {0}.
partner_save_error=Erreur de la sauvegarde du partenaire\u00A0: {0}.
partner_send_mail=Envoyer les informations de connexion
partner_deleted=Suppression des partenaires\u00A0: {0}.
partner_delete_error=Erreur de suppression de partenaire\u00A0: {0}.
partner_delete_error_children_exist=Impossible de supprimer un partenaire tant qu''il a des sous-partenaires\u00A0: {0}.
partner_user_add=Ajouter un contact
partner_import_already_existing_users= utilisateur(s) d\u00E9j\u00E0 existant(s)
partner_import_already_existing_partners= partenaire(s) d\u00E9j\u00E0 existant(s)
partner_import_no_user=Le partenaire {0} n''a pas d''utilisateur
contextual_validation_user_scope_partner=Le partenaire {0} n''appartient pas \u00E0 {1}
contextual_validation_user_scope_user=L''utilisateur {0} n''existe pas
contextual_validation_user_scope_user_cpy=L''utilisateur {0} n''appartient pas \u00E0 {1}
contextual_validation_user_scope_role=L''utilisateur {0} n''a pas le r\u00F4le {1}
contextual_validation_user_scope_partner_id=Le num\u00E9ro d'identification du partenaire {0} et du p\u00E9rim\u00E8tre {1} doivent \u00EAtre diff\u00E9rents pour l'utilisateur {2}
contextual_validation_partner_role=Le r\u00F4le doit exister
contextual_validation_partner_vat_regime=Vat Regime doit \u00EAtre VR1, VR2, VR3, VR4 ou null
contextual_validation_partner_e_reporting=eReporting doit \u00EAtre ER1, ER2 ou null

# FAQ/SURVEY
entry_question=Question
entry_answer=R\u00E9ponse
entry_new_answer=Option
entry_answers=R\u00E9ponses
entry_type=Type
entry_remove=Supprimer
entry_new=Nouvelle entr\u00E9e
entry_add=Ajouter
entry_add_question=Ajouter une question
entry_add_title=Ajouter un titre
entry_delete_question=Supprimer une question
entry_edit_question=\u00C9diter une question
entry_import_faq=Importer une FAQ
entry_select_excel=Choisir un fichier Excel (*.xls, *.xlsx)
entry_text=Texte
entry_radio=Radio
entry_checkbox=Case \u00E0 cocher
entry_title=Titre

# MAIL
contact_user=Contact
other_variable=Variable libre
reminder=Rappel
reminder_number=Nombre de rappels
reminder_delay=Intervalle de temps
reminder_subject=Objet du rappel
reminder_content=Corps du rappel
mail_content=Corps
user_logged=D\u00E9j\u00E0 connect\u00E9
mail_sent=Envoy\u00E9
keyword_$login=Login de l'utilisateur
keyword_$url=URL de l'application
keyword_$password=Mot de passe de l'utilisateur
keyword_$contact=Contact
keyword_$company=Nom de l'entreprise lan\u00E7ant la campagne
keyword_$currentDate=Date du jour
keyword_$campaigncontactname=Nom du contact pour la campagne
keyword_$campaigncontactphone=Num\u00E9ro de t\u00E9l\u00E9phone du contact pour la campagne
keyword_$campaigncontactemail=Email du contact pour la campagne
keyword_$campaignvariable=Variable libre

# DOCUMENTS
docs_list=Liste des documents
doc_name=Nom du document
doc_select=S\u00E9lectionner
doc_update=Mettre \u00E0 jour
doc_update_information=Mettre \u00E0 jour mes informations
doc_upload=Charger
doc_cancel=D\u00E9s\u00E9lectionner
doc_new_name=Nouveau nom
doc_uploaded=Document import\u00E9\u00A0: {0}
doc_uploaded_success=Document(s) import\u00E9(s)\u00A0: {0}/{1}
doc_uploaded_update=Document mis \u00E0 jour
doc_upload_duplicate=Dupliqu\u00E9\u00A0: {0}
doc_upload_error=Erreur d''import du document\u00A0: {0} ({1})
doc_upload_disabled=Vous ne pouvez pas effectuer cette action car la t\u00E2che est v\u00E9rouill\u00E9e (une campagne est peut-\u00EAtre en cours).
doc_rename_error=Document d\u00E9j\u00E0 existant
doc_remove_error=Erreur lors de la suppression du document
doc_deleted=Document supprim\u00E9
doc_downloads=Nombre de t\u00E9l\u00E9chargements
docs_available=Documents disponibles
docs_selected=Documents selectionn\u00E9s
file_docs_uploaded=Les documents ont \u00E9t\u00E9 import\u00E9s
file_process_will_be_executed=Le process sera ex\u00E9cut\u00E9
file_exceeded_number=Vous ne pouvez pas importer plus de 10 documents simultan\u00E9ment
no_files_choosen=Aucun fichier choisi

#SSL Files
ks_alias_not_found=Alias non trouv\u00E9\u00A0: {0}. Alias trouv\u00E9s, avec s\u00E9parateur ;\u00A0: {1}
ks_cannot_determine_alias=Ne peut d\u00E9terminer l''alias \u00E0 utiliser. Alias trouv\u00E9s, avec s\u00E9parateur ;\u00A0: {0}
ks_cannot_load_keystore=Keystore non chargeable. V\u00E9rifiez les param\u00E8tres. Consultez un administrateur.
ks_certificationpath=Chemin d'acc\u00E8s de certification
ks_comment=Description
ks_config_require_name=Le nom du KeyStore est obligatoire.
ks_config_require_usage=L'usage du KeyStore est obligatoire.
ks_config_require_instance=L'environnement du KeyStore est obligatoire.
ks_entrydetailstab=Certificat
ks_entry_subject=Livr\u00E9 \u00E0
ks_entry_notbefore=Valide depuis
ks_entry_notafter=Valide jusqu'\u00E0
ks_entry_alias=Alias
ks_entry_alias_optional=Optionnel sauf si l'alias ne peut \u00EAtre d\u00E9termin\u00E9 automatiquement.
ks_entry_version=Version
ks_entry_issuer=Livr\u00E9 par
ks_entry_serialnumber=Num\u00E9ro de s\u00E9rie
ks_entry_signaturealgoname=Nom de l'algorithme de signature
ks_entry_fingerprintmd5=Empreinte num\u00E9rique (MD5)
ks_entry_fingerprintsha1=Empreinte num\u00E9rique (SHA-1)
ks_error_could_not_find_nor_create_parent_for_friendly_name=Impossible de trouver ou cr\u00E9er un parent pour le nom d''usage {0}.
ks_error_could_not_write_certificate_file=Impossible d'\u00E9crire de fichier associ\u00E9 pour ce certificat.
ks_error_could_not_extract_certificate=Impossible d'extraire le ou les certificats contenus dans le fichier.
ks_error_no_certificate_found=Aucun certificat trouv\u00E9 dans le fichier.
ks_error_multiple_parent_for_friendly_name=\u00C9tat incoh\u00E9rent\u00A0: plusieurs parents trouv\u00E9s pour le nom d''usage {0}.
ks_error_no_friendly_name_found=Le nom d'usage n'est pas trouv\u00E9 dans les param\u00E8tres et ne peut \u00EAtre d\u00E9termin\u00E9.
ks_error_no_keystore_folder=Error - le dossier keystore n'a pas pu \u00EAtre d\u00E9termin\u00E9.
ks_filename=Nom du certificat
ks_friendlyname=Nom d'usage
ks_hasprivatekey=Avec cl\u00E9 priv\u00E9e
ks_hasRSApublickey=RSA
ks_keystoredetailstab=D\u00E9tails du KeyStore
ks_library=Librairie
ks_modulus_length=Longueur du modulo (bits)
ks_morethanoneentry=Votre keystore contient plus d'un certificat. Il n'est pas pr\u00E9vu de g\u00E9rer ce type de keystore.
ks_no_file_selected=Un certificat doit \u00EAtre s\u00E9lectionn\u00E9.
ks_error_during_file_reading= Une erreur s'est produite lors de la lecture du fichier.
ks_no_key_found=Pas de cl\u00E9 trouv\u00E9e dans le KeyStore.
ks_no_slot_index_selected=Un slot index doit \u00EAtre s\u00E9lectionn\u00E9.
ks_password=Mot de passe
ks_password_pkcs11=Mot de passe (code PIN)
ks_provide_friendly_name=Nom d'usage doit \u00EAtre renseign\u00E9.
ks_provide_library=La librairie \u00EAtre renseign\u00E9e.
ks_publickeytab=Cl\u00E9 publique certificat
ks_remove_error=Erreur lors de la suppression du fichier\u00A0: {0}.
ks_remove_linked_partner_integrity=Op\u00E9ration bloqu\u00E9e\u00A0: certificat encore li\u00E9 \u00E0 ces groupes\u00A0: {0}.
ks_remove_timestamp_server_integrity=Op\u00E9ration bloqu\u00E9e\u00A0: certificat encore r\u00E9f\u00E9renc\u00E9 dans un serveur d'horodatage.
ks_slotIndex=Slot index
ks_label = Label
ks_type=Type
ks_unexpected_error_determing_alias=Erreur inattendue dans la d\u00E9termination de l'alias.
ks_unexpected_multiple_keys=Plusieurs cl\u00E9s dans le keystore, cas inattendu.
ks_unrecognized=Les donn\u00E9es import\u00E9es ne peuvent \u00EAtre charg\u00E9es comme un KeyStore PKCS11, PKCS12 ou JKS.\n - V\u00E9rifiez le mot de passe.\n - V\u00E9rifiez le type du fichier. \n - Essayer de changer les fichiers \"Unlimited Strength Jurisdiction Policy Files\" de votre JRE.
ks_upload=Importer un keystore
ks_uploaddate=Date de cr\u00E9ation
ks_uploaded=keystore import\u00E9\u00A0: {0}
ks_upload_select=Keystore (*.jks, *.p12, *.pfx)
ks_upload_error=Erreur d''import du fichier\u00A0: {0}
ks_usage_mismatch=L''usage {0} doit correspondre \u00E0 l''usage parent {1}
ks_use_end_date=Utilisable jusqu'\u00E0
ks_use_period=P\u00E9riode d'utilisation
ks_use_start_date=Utilisable depuis

#TEST
test_upload=Chargement d'un fichier de test.

#ABOUT US
about_contact=Contactez-nous
about_contact_email=Email du contact

#TEXT
text=Texte

# ENUMERATES
# CompletionDefinition
AllUsers=Tous les utilisateurs d'un partenaire
AtLeastOneUser=Au moins un utilisateur d'un partenaire

# ContactMode
contact_mode=M\u00E9dia
fax=Fax

#REPORTS
report_partners_completion=STATUTS DE LA CAMPAGNE
report_partners_completion_campaigns=ONBOARDING
report_partners_status_by_month=\u00C9VOLUTION DU STATUT DES PARTENAIRES
report_partners_status=STATUS DES PARTENAIRES
report_no_data=PAS DE DONN\u00C9ES
report_not_completed=Non compl\u00E9t\u00E9
report_completed=Compl\u00E9t\u00E9
report_informations=Informations

#ORDERS
client_order=N\u00B0 commande
invoice=N\u00B0 facture
later_shipment_date=Date d'exp\u00E9dition au plus tard
order=N\u00B0 de commande
shipment_date=Date d'exp\u00E9dition
sooner_shipment_date=Date d'exp\u00E9dition au plus t\u00F4t

# USER ACCOUNT MAILS
creation_mail_subject=Cr\u00E9ation de votre compte utilisateur pour le site de {0}
creation_mail_message_html=Bonjour {0},<br/><br/>Veuillez trouver ci-dessous vos informations de connexion au site de {1} ({2})\u00A0: <br/><br/> <li>Utilisateur: {3} </li><li> Lien de renouvellement de mot de passe\u00A0: {4}</li><br/><br/>A bient\u00F4t !
creation_mail_message_text=Bonjour {0},\n\nVeuillez trouver ci-dessous vos informations de connexion au site de {1} ({2})\u00A0: \n\n- Utilisateur\u00A0: {3}\n- Lien de renouvellement de mot de passe\u00A0: {4}\n\nA bient\u00F4t !

#CHANNELS
channels=Canaux
channel=Canal
channel_add=Ajouter un canal
channel_modify=Modifier un canal
channel_add_type=Type du canal \u00E0 ajouter
channel_name=Nom du canal
channel_type_mandatory=Vous devez choisir un type de canal avant de cliquer sur Ajouter.
channel_type_select=Type de canal...
channel_type=Type
channel_desc=Description
channel_create_error=Erreur lors de la cr\u00E9ation du canal.
channel_save_error_null=Erreur lors de la sauvegarde du canal (le canal est null).
channel_add_error_duplicatename=Un canal portant ce nom existe d\u00E9j\u00E0. Ce canal ne peut \u00EAtre cr\u00E9\u00E9.
channel_save_ok=Canal sauvegard\u00E9.
channel_not_selected=Pas de canal s\u00E9lectionn\u00E9
channel_deleted=Canal supprim\u00E9
channel_duplicate_error=Erreur lors de la copie du canal
channel_duplicate_ok=Canal dupliqu\u00E9
channel_delete_linked_chanel=Erreur lors de la suppression du canal. Il existe encore des partenaires avec un alias sur ce canal :<br/>{0}
channel_toggle_linked_chanel=Erreur lors de la modification du canal. Il existe encore des partenaires avec un alias sur ce canal :<br/>{0}

channel_ftp_port=Port
channel_ftp_hostname=Hostname du serveur
channel_ftp_username=Username

#CHANNEL AS2
channel_other=Autre
channel_test=Tester

url=Url
port=Port
timeout=Timeout
secure=S\u00E9curis\u00E9 (HTTP/S)
keystore=Keystore

#CHANNEL AUTHENTICATION
channel_AuthenticationEndpointConfiguration=Authentification
channel_auth_userDnPattern=User DN pattern
channel_auth_userSearchBase=User search base
channel_auth_userSearchFilter=User search filter
channel_auth_groupSearchBase=Group search base
channel_auth_groupSearchFilter=Group search filter

#PROPERTY
property_not_integer=doit \u00EAtre un nombre entier.
property_required=est requis.

#MESSAGES
#EXCEPTIONS
exception_message=Une erreur est survenue. \n Veuillez r\u00E9essayer plus tard ou contacter votre administrateur.\n ({0})
exception_backoffice_user={0} est un utilisateur du back-office.
exception_code_duplication=Le code existe d\u00E9j\u00E0: {0}.
exception_constraint_violation={0}\u00A0: {1}
exception_duplicate_email=Cette adresse email a d\u00E9j\u00E0 \u00E9t\u00E9 cr\u00E9\u00E9e
exception_duplicate_login= Le login existe d\u00E9j\u00E0 dans AIO, le user ne peut pas \u00E8tre cr\u00E9\u00E9
exception_duplicate_role=R\u00F4le dupliqu\u00E9\u00A0: {0}.
exception_failing_ACE_instanciation=Une erreur est survenue lors de l''instanciation des ACE pour\u00A0: {0}
exception_file_upload_unknown_request=Le param\u00E8tre de la requ\u00EAte est inconnu\u00A0: {0}
exception_id_duplication=L''identifiant existe d\u00E9j\u00E0: {0}
exception_gcn_id_duplication=L''identifiant GCN existe d\u00E9j\u00E0: {0}
exception_gcn_subscriber_id_duplication=L''identifiant de facturation Generix existe d\u00E9j\u00E0: {0}
exception_import_error_during_cloning=Erreur lors de la duplication du portail
exception_import_export_null=Export est null.
exception_import_instance_code_null=Le code de l'environnement est null.
exception_import_instance_null=L'environnement du fichier d'export est null.
exception_export_null= Pas d''export trouv\u00E9 dans le fichier
exception_import_page_null= La page d''export est nulle.
exception_import_portal_not_empty=Le portail n''est pas vide pour l'environnement {0}.
exception_instance_associated={0} environnement(s) li\u00E9(s)
exception_instance_not_found=L''utilisateur {0} n''a pas d''environnement configur\u00E9 pour le domaine {1}
exception_invalid_gson=Fichier Gson invalide
exception_json_export_error=Erreur lors de l''export JSON\u00A0: {0}
exception_more_than_one_instance=L''utilisateur {0} associ\u00E9 au client {1} a plus d''un environnement configur\u00E9 pour le domaine {2} ({3}).
exception_multiple_principal_groups_user=L''utilisateur {0} a plus d''un groupe principal ({1}).
exception_no_backoffice_access={0} n''appartient ni \u00E0 un partenaire ni \u00E0 un client et n''a pas d''acc\u00E8s au back-office.
exception_partner_associated={0} partenaire(s) li\u00E9(s)
exception_exchange_associated={0} \u00E9change(s) autoris\u00E9(s) li\u00E9(s)
exception_partner_subgroups=Le partenaire {0} a {1} sous-groupe(s).
exception_portlet_cloning=Probl\u00E8me lors du clonage de portlet {0}.
exception_removed_instance=L''environnement {0} a \u00E9t\u00E9 supprim\u00E9.
exception_role_has_users=Le r\u00F4le {0} a {1} utilisateur(s) associ\u00E9(s).
exception_role_has_pages=Le r\u00F4le {0} a {1} page(s) associ\u00E9e(s).
exception_role_has_sso=Le r\u00F4le {0} a des sso mappages de r\u00F4les.
exception_sending_mail_partner=Une erreur est survenue lors de l''envoi de mail au(x) partenaire(s)\u00A0: {0}.
exception_task_change_parent=Erreur dans l'algorithme changeParent.
exception_task_import=Probl\u00E8me d''import des t\u00E2ches pour {0}.
exception_task_import_parents=Probl\u00E8me d''import taskParent pour {0}.
exception_task_type_not_found=Impossible de trouver le type de t\u00E2che {0}
exception_task_properties_not_found=Import de la t\u00E2che {0} impossible, une mont\u00E9e de version est n\u00E9cessaire
exception_unavailable_instance=Le site {0} est temporairement indisponible.
exception_unknown_layout_type=Type du layout inconnu
exception_user_associated={0} utilisateur(s) li\u00E9(s)
exception_user_no_associated_company=L''utilisateur {0} du partenaire {1} n''est li\u00E9e \u00E0 aucun client.
exception_user_not_partner=L''utilisateur {0} du groupe {1} n''est ni un partenaire ni un client.
exception_user_not_specified=L''utilisateur n''est pas sp\u00E9cifi\u00E9.\nexception_backoffice_user={0} est un utilisateur back-office.
exception_user_more_companies=L''utilisateur {0} du partenaire {1} a plus d''un client li\u00E9e ({2}).
exception_access_denied=Acc\u00E8s refus\u00E9
exception_admin_url_portal=Veuillez saisir un autre nom d'utilisateur ou contactez votre administrateur pour obtenir l'URL d'acc\u00E8s \u00E0 votre portail.
exception_user_not_part_of_app=Cet utilisateur ne fait pas partie de l''application, essayez-en un autre.
exception_go_to_login=Retourner \u00E0 la connexion

#ERRORS
error_duplicate_template=Le template {0} existe d\u00E9j\u00E0.
error_changing_layout=Erreur lors du changement de layout ({0}).
error_creating_template_archive=Erreur lors de la cr\u00E9ation du template ({0}).
error_editing_portlet=Erreur lors de l''\u00E9dition du contenu de la portlet ({0}).
error_editing_default_host=Le domaine par d\u00E9faut n'est pas modifiable
error_exporting_instance=Erreur lors de l''export de l''environnement\u00A0: {0}.
error_exporting_page=Erreur lors de l'export de la page.
error_exporting_partner=Erreur lors de l''export des partenaires ({0}).
error_exporting_client=Erreur lors de l''export des clients ({0}).
error_file_not_found=Fichier non trouv\u00E9\u00A0: {0}.
error_getting_portlet_content=Portlet {0}, Contenu {1}\u00A0: {2}
error_importing_portlet=L'import json est possible seulement sur une page vierge.
error_importing_instance=Erreur lors de l''import de l''environnement\u00A0: {0}.
error_importing_page= Erreur lors de l'import de la page
error_importing_partner=Erreur lors de l''import des partenaires ({0}).
error_no_company_specified=Aucun client sp\u00E9cifi\u00E9 pour l''\u00E9dition du partenaire {0}.
error_no_user_found=Aucun utilisateur trouv\u00E9 avec l''identifiant\u00A0: {0}.
error_password_generation=Une erreur est survenue lors de la g\u00E9n\u00E9ration du mot de passe.
error_password_link_generation=Une erreur est survenue lors de la g\u00E9n\u00E9ration du lien de renouvellement de mot de passe.
error_profile_name_mandatory=Le nom du profil est obligatoire.
error_removing_company=Erreur lors de la suppression du client ({0}).
error_removing_file=Erreur lors de la suppression du fichier ({0}).
error_removing_host=Erreur lors de la suppression du domaine ({0}).
error_removing_page=Erreur lors de la suppression de la page ({0}).
error_removing_partner=Erreur lors de la suppression du partenaire ({0}).
error_removing_client=Erreur lors de la suppression du client ({0}).
error_removing_portlet=Erreur lors de la suppression du portlet ({0}).
error_removing_portlet_content=Erreur lors de la suppression du contenu de la portlet ({0}).
error_removing_role=Erreur lors de la suppression du r\u00F4le ({0}).
error_removing_row=Erreur lors de la suppression de la ligne ({0}).
error_removing_template=Erreur lors de la suppression du template ({0}).
error_removing_user=Erreur lors de la suppression de l''utilisateur ({0}).
error_saving_company=Erreur lors de la sauvegarde du client.
error_saving_file=Erreur lors de la sauvegarde du fichier ({0}).
error_saving_host=Erreur lors de la sauvegarde du domaine ({0}).
error_saving_host_not_unique=Un domaine du m\u00EAme nom existe d\u00E9j\u00E0
error_saving_instance=Erreur lors de la sauvegarde de l''environnement ({0}).
error_saving_organization=Erreur: l''\u00E9l\u00E9ment existe d\u00E9j\u00E0.
error_saving_partner=Erreur lors de la sauvegarde du partenaire ({0}).
error_saving_client=Erreur lors de la sauvegarde du client ({0}).
error_saving_permission=Erreur lors de la sauvegarde de la permission ({0}).
error_saving_portal=Erreur lors de la sauvegarde du portail ({0}).
error_saving_portal_portlet_missing=La configuration d'une portlet est manquante.
error_saving_role=Erreur lors de la sauvegarde du r\u00F4le ({0}).
error_saving_user=Erreur lors de la sauvegarde de l''utilisateur ({0}).
error_creating_user_keycloak=Le login existe d\u00E9j\u00E0 dans KC, le user ne peut pas \u00E8tre cr\u00E9\u00E9
error_updating_user_keycloak=\u00C9chec de la mise \u00E0 jour de l''utilisateur keycloak {0}
error_saving_invoice_entry_preferences=Erreur lors de la sauvegarde des pr\u00E9f\u00E9rences de facturation
error_select_correct_user=Veuillez s\u00E9lectionner un utilisateur valide.
error_sending_new_password=Une erreur est survenue lors de l''envoi du lien de renouvellement de mot de passe \u00E0 {0} ({1}).
error_switching_user=Erreur lors de l''usurpation d''identit\u00E9 ({0}).
error_uploding_logo=Chargement du logo {0} en \u00E9chec.
error_uploding_template=Chargement du template en \u00E9chec ({0}).
error_user_without_primary_group=Aucun groupe principal trouv\u00E9 pour l'utilisateur.
error_save_user_no_env=La s\u00E9lection de l'environnement est obligatoire pour pouvoir notifier l'utilisateur.
error_save_user_no_partner=La s\u00E9lection du partenaire est obligatoire.
error_saving=Erreur lors de la sauvegarde ({0})
error_duplicate_configuration=La configuration existe d\u00E9j\u00E0
error_export_empty_portlet=Vous ne pouvez pas exporter un portail vide;
error_export_shadow_portlet=Vous ne pouvez pas exporter cette page car celle-ci contient au moins un portlet \"Shadow\"
error_export_empty_page=Vous ne pouvez pas exporter une page vide;
error_export_portlet_rte_collection= Vous ne pouvez pas exporter la portlet Collection.
error_export_not_empty_portlet = Vous ne pouvez pas exporter : le portlet n'est pas vide.
error_importing_Languages= Une erreur est survenu lors de l'import du portail localisation fichier
error_invalid_row_length=[{0},{1}] Nombre de champs incorrects attendus ({2}), trouv\u00E9s ({3})
error_localizations_import_bad_structure=La structure du fichier n'est pas conforme au format attendu
error_file_too_large=La taille du fichier d\u00E9passe la taille maximale autoris\u00E9e de 2 Gb
error_files_too_large=La taille du fichier d\u00E9passe la taille maximale autoris\u00E9e de 1.7 Gb
error_invalid_number={0} : Le contenu entr\u00E9e n''est pas un nombre : {1}
error_invalid_date={0} : le contenu entr\u00E9e n''est pas une date : {1}
error_switching_user_no_idp=La fonctionnalit\u00E9 d'usurpation d'utilisateur n'est pas disponible: Aucun IDP n'a \u00E9t\u00E9 configur\u00E9 pour l'usurpation
error_switching_user_no_authConfig=La fonctionnalit\u00E9 d'usurpation d'utilisateur n'est pas disponible: Aucun configuration d'authentification n'a \u00E9t\u00E9 ajout\u00E9e

#WARNS
warn_app_template_deleting=Le template {0} ne peut pas \u00EAtre supprim\u00E9.
warn_existing_resource=La ressource existe d\u00E9j\u00E0
warn_creation_succes=La ressource a \u00E9t\u00E9 cr\u00E9\u00E9e avec succ\u00E8s
warn_creation_fail=La cr\u00E9ation a \u00E9chou\u00E9
warn_delete_fail=La suppression a \u00E9chou\u00E9
warn_host_already_used=Le domaine est d\u00E9j\u00E0 utilis\u00E9.
warn_host_not_removed={0} n''a PAS \u00E9t\u00E9 supprim\u00E9 ({1} environnement(s) associ\u00E9(s)\u00A0: CODE= {2}).
warn_instance_code_already_used=Le code de l'environnement est d\u00E9j\u00E0 utilis\u00E9.
warn_language_mandatory=La configuration des langues est obligatoire.
warn_locked_file=Fichier verrouill\u00E9
warn_locked_folder=Dossier verrouill\u00E9
warn_no_type_selected=Veuillez s\u00E9lectionner le type de la t\u00E2che.
warn_only_parent_empty=Seule une page parent peut \u00EAtre vide.
warm_add_empty_when_other_exits=Vous ne pouvez pas ajouter une disposition vide si une disposition existe d\u00E9j\u00E0
warm_change_into_empty_when_other_exists=Vous ne pouvez pas changer cette disposition en une disposition vide tant qu'il existe d'autres dispositions
warn_partner_profile_deleted=Le profil d'un partenaire ne doit pas \u00EAtre supprim\u00E9.
warn_portlet_content_not_found=Le contenu de la portlet ayant l''id {0} n''a pas \u00E9t\u00E9 trouv\u00E9.
warn_profile_already_exists=Ce profil existe d\u00E9j\u00E0.
warn_select_partner_notification=Veuillez s\u00E9lectionner les partenaires \u00E0 notifier.
warn_select_role=Veuillez s\u00E9lectionner un r\u00F4le.
cannot_assign_role=Vous avez le droit d\u2019attribuer un r\u00F4le mais aucun r\u00F4le n\u2019est configur\u00E9
warn_template_with_associated_host=Certains domaines sont associ\u00E9s \u00E0 un template.
warn_user_missing=L'utilisateur est introuvable (v\u00E9rifier l'uuid du param\u00E9tre).
warn_partner_missing=Le partenaire est introuvable (v\u00E9rifier le pcode du param\u00E9tre).
warn_deleting_own_user=Il n''est pas possible de supprimer l''utilisateur {0}. Il s''agit de votre propre utilisateur.
warn_import_pt_collection=Si le portail utilise des portlets \"collection\", une configuration de ces portlets sera n\u00E9cessaire
warn_portlet_localization_lang_not_supported=Langue {0} non support\u00E9e par l'environnement
warn_localizations_import_portlet_not_found=Portlet {0} non trouv\u00E9 dans la d\u00E9finition du portail
warn_importing_multiLevel_iconPages=L'importation ne peut pas \u00EAtre effectu\u00E9e car une page \"ic\u00F4ne\" ne peut avoir qu'un seul niveau

#INFOS
info_company_removed=Client {0} supprim\u00E9
info_company_saved=Client {0} sauvegard\u00E9
info_company_created=Client {0} cr\u00E9\u00E9
info_file_removed=Fichier supprim\u00E9
info_file_saved=Fichier sauvegard\u00E9
info_host_removed=Domaine {0} supprim\u00E9
info_host_saved=Domaine {0} sauvegard\u00E9
info_instance_saved=L''environnement {0} a \u00E9t\u00E9 sauvegard\u00E9.
info_instance_toggled=L''environnement {0} a \u00E9t\u00E9 commut\u00E9.
info_import_portal=Le portail a \u00E9t\u00E9 import\u00E9
info_import_processes=Les processus ont \u00E9t\u00E9 import\u00E9s
info_logo_uploaded=Logo charg\u00E9
info_no_portlet_content_defined=La portlet a \u00E9t\u00E9 d\u00E9finie sans contenu pour cette page.
info_no_portlet_defined=La portlet n'a pas \u00E9t\u00E9 d\u00E9finie pour cette page.
info_partner_file_import={0} import\u00E9
info_partner_removed=Partenaire {0} supprim\u00E9
info_client_removed=Client {0} supprim\u00E9
info_partner_saved=Partenaire {0} sauvegard\u00E9
info_client_saved=Client {0} sauvegard\u00E9
info_partner_saved_detail=Partenaire {0} sauvegard\u00E9\u00A0: {1}
info_portal_saved=Le portail a \u00E9t\u00E9 sauvegard\u00E9.
info_portlet_removed=Le contenu de la portlet a \u00E9t\u00E9 supprim\u00E9.
info_portlet_saved=Le contenu de la portlet a \u00E9t\u00E9 sauvegard\u00E9.
info_role_removed=R\u00F4le {0} supprim\u00E9
info_role_saved=R\u00F4le {0} sauvegard\u00E9
info_user_new_password=Un lien de renouvellement de mot de passe a \u00E9t\u00E9 envoy\u00E9 \u00E0 {0}.
info_user_removed=Utilisateur {0} supprim\u00E9
info_user_saved=Utilisateur {0} sauvegard\u00E9
info_parameters_saved=Param\u00E8tres utilisateur sauvegard\u00E9s
info_user_role_saved=R\u00F4le de l'utilisateur sauvegard\u00E9
info_user_role_duplicated=Le r\u00F4le {0} \u00E9t\u00E9  dupliqu\u00E9
info_partner_role_saved=R\u00F4le du partenaire sauvegard\u00E9
info_portlet_localization_import=L''import du portail localisation termine\u00E9.{0} message(s) ajout\u00E9(s) et {1} modifi\u00E9(s)
import_role_header =Import d'un fichier de r\u00F4les (*.xls, *.xlsx)
info_import_page=Importer la page ok

#CONFIRM
confirm_instance_disable=Tous les services de l'environnement seront d\u00E9sactiv\u00E9s (int\u00E9gration, onboarding...).\\n\\n\u00CAtes-vous s\u00FBr de vouloir d\u00E9sactiver cet environnement ?
confirm_instance_enable=Tous les services de l'environnement seront activ\u00E9s (int\u00E9gration, onboarding...).\\n\\n\u00CAtes-vous s\u00FBr de vouloir activer cet environnement ?
confirm_file_delete=Voulez-vous vraiment supprimer {0}
confirmScopeSelection=Vous allez perdre votre s\u00E9lection de p\u00E9rim\u00E8tre partenaire

# PrimeFaces
no_records_found=Pas d'enregistrement trouv\u00E9
no_records_found_loreal_order=Aucun article ne correspond \u00E0 votre recherche. <br/> Nous vous invitons \u00E0 modifier celle-ci.
no_extensions_found = Pas de fichier d'extension trouv\u00E9 pour cette base de donn\u00E9es
ui_converter=Probl\u00E8 de conversion
ui_file_limit=Nombre maximum de fichiers atteint
ui_invalid_file=Type du fichier incorrect
ui_invalid_size=Taille du fichier incorrecte
ui_required=Donn\u00E9e obligatoire
ui_validator=Donn\u00E9e invalide

# Report
OK_icon=fa fa-check-circle-o
OK_color=#87b87f
OK_label=OK
APPROVED_icon=fa fa-check-circle-o
APPROVED_color=#87b87f
APPROVED_label=OK
WARN_icon=fa fa-exclamation-circle
WARN_color=#ffb752
WARN_label=AVERTISSEMENT
WARNING_icon=fa fa-exclamation-circle
WARNING_color=#ffb752
WARNING_label=AVERTISSEMENT
ERROR_icon=fa fa-times-circle-o
ERROR_color=#d15b47
ERROR_label=ERREUR
FATAL_icon=fa fa-bomb
FATAL_color=#cc1e00
FATAL_label=CRITIQUE
DISABLED_icon=fa fa-minus-circle
DISABLED_color=#d4d4d4
DISABLED_label=D\u00C9SACTIV\u00C9
NONE_icon=fa fa-minus-circle
NONE_color=#d4d4d4
NONE_label=AUCUN
PENDING_icon=fa fa-cog fa-spin
PENDING_color=#bebfbb
PENDING_label=RAPPORT EN COURS...
COMPLETED_icon=fa fa-check-circle-o
COMPLETED_color=#87b87f
COMPLETED_label=TERMIN\u00C9
STARTING_icon=fa fa-times-circle-o
STARTING_color=#bebfbb
STARTING_label=D\u00C9PART
STARTED_icon=fa fa-times-circle-o
STARTED_color=#bebfbb
STARTED_label=COMMENC\u00C9
STOPPING_icon=fa fa-exclamation-circle
STOPPING_color=#cc1e00
STOPPING_label=ARR\u00CAT
STOPPED_icon=fa fa-exclamation-circle
STOPPED_color=#cc1e00
STOPPED_label=ARR\u00CAT\u00C9
FAILED_icon=fa fa-exclamation-circle
FAILED_color=#cc1e00
FAILED_label=\u00C9CHOU\u00C9
ABANDONED_icon=fa fa-exclamation-circle
ABANDONED_color=#cc1e00
ABANDONED_label=ABANDONN\u00C9
UNKNOWN_icon=fa fa-exclamation-circle
UNKNOWN_color=#d4d4d4
UNKNOWN_label=INCONNU
edition=Edition
new=Nouveau
new_file=Nouveau fichier
new_folder=Nouveau dossier
exit=Quitter
themes=Th\u00E8mes
find=Rechercher
replace=Remplacer
line_comment=Commenter la ligne

#Portlets Names
Calendar=CalendarDeprecated
CarrefourInvoiceEdition=CarrefourInvoiceDeprecated
CarrefourInvoice=CarrefourInvoice
DocumentBarChart=BarChart
DocumentBirt=Birt
DocumentCalendar=DocumentCalendarDeprecated
DocumentCounter=Counter
DocumentLineChart=LineChart
archiveimv3=Archive IMV3
Documentation=DocumentationDeprecated
DocumentPieChart=PieChart
EDocuments=EdocumentsDeprecated
Factor=FactorDeprecated
FeedReader=FeedReaderDeprecated
Monitoring=MonitoringDeprecated
OrderLine=OrderLineDeprecated
Penalty=FranprixPenaltyDeprecated
PlanningSchedule=PlanningScheduleDeprecated
SafranInvoice=SafranInvoiceDeprecated
Survey=SurveyDeprecated
CompleteEdition=CompleteEditionDeprecated
no_order_from_asn=Aucune commande trouv\u00E9e \u00E0 partir de l'avis d'exp\u00E9dition pr\u00E9alable s\u00E9lectionn\u00E9

# Organization
organization_code=Identifiant
organization_collaborativeId=GCN Id (gid)
organization_subscriberId=Identifiant de facturation Generix
connection_id=Num\u00E9ro d''identification unique
organization_vat=N\u00B0 TVA intracommunautaire
organization_registration=SIREN
organization_duns=RCS-RCM
organization_phone=_phone
organization_fax=_fax
organization_email=_email
organization_web=_web
organization_orderContact=Contact
organization_orderPhone=T\u00E9l\u00E9phone
organization_orderFax=Fax
organization_orderEmail=Email
organization_client=_client
organization_profile=Profil
organization_gcnSubscriber=Client Generix
organization_shareCapital=Capital social
organization_registerName=Raison sociale
organization_legalStructure=Structure juridique
organization_referenceCurrency=Devise de r\u00E9f\u00E9rence
organization_optionReporting= Options d'eReporting
organization_vatRegime=Option pour le r\u00E9gime de TVA :
organization_VR1=Entreprise soumise au r\u00E9gime r\u00E9el normal mensuel
organization_VR2=Entreprise ayant opt\u00E9 pour le r\u00E9gime r\u00E9el normal trimestriel
organization_VR3=Entreprise soumise au r\u00E9gime simplifi\u00E9 d'imposition TVA
organization_VR4=Entreprise b\u00E9n\u00E9ficiant du r\u00E9gime de franchise en base de TVA
organization_eReporting=Option pour le eReporting :
organization_ER1=Je demande \u00E0 Generix d'extraire les donn\u00E9es de facturation B2B international et m'engage \u00E0 fournir dans un flux s\u00E9par\u00E9 les donn\u00E9es de facturation des factures qui n'ont pas transit\u00E9 par la PDP.
organization_ER2=Je demande \u00E0 Generix de n'extraire aucune donn\u00E9e de facturation, je pr\u00E9f\u00E8re fournir \u00E0 Generix toutes les donn\u00E9es d'eReporting dans un flux s\u00E9par\u00E9.

organization_id=_id
organization_name=Code
organization_fullname=Nom
organization_creation=_creation
organization_modification=_modification
organization_comment=Description
organization_description=_description
organization_userGroupAssociations=_userGroupAssociations
organization_location=_location
organization_parent=_parent
organization_children=_children
organization_logoSmall=_logoSmall
organization_logoMedium=_logoMedium
organization_logoLarge=_logoLarge
organization_freeText01=_freeText01
organization_freeText02=_freeText02
organization_freeText03=_freeText03
organization_freeText04=_freeText04
organization_freeText05=_freeText05
organization_freeText06=_freeText06
organization_freeText07=_freeText07
organization_freeText08=_freeText08
organization_freeText09=_freeText09
organization_freeLongText01=_freeLongText01
organization_freeLongText02=_freeLongText02
organization_freeBoolean01=_freeBoolean01
organization_freeBoolean02=_freeBoolean02
organization_freeDouble01=_freeDouble01
organization_freeDouble02=_freeDouble02
organization_freeDate01=_freeDate01
organization_freeDate02=_freeDate02
organization_freeViewConfiguration=Champs libres
organization_freeViewProfile=Profil

organization_address_address=_address.address
organization_address_addressComplement=Compl\u00E9ment d'adresse
organization_address_country_country= Code ISO-2 du pays
organization_address_country_displayCountry = Pays
organization_address_country_iSO3Country = Code ISO-3 du pays
organization_address_addressLine=Adresse
organization_address_streetName=Adresse
organization_address_postalCode=Code Postal
organization_address_city=Ville
organization_address_country=Pays
organization_start=Date de d\u00E9but
organization_end=Date de fin
organization_role=R\u00F4le

organization_autoGenerationOfSSCC=SSCC g\u00E9n\u00E9r\u00E9
organization_extension=Caract\u00E8re d'extension
organization_gs1=Code Entreprise GS1
organization_serialReference=Num\u00E9ro S\u00E9quentiel

organization_bankAccount_currency=Devise
organization_bankAccount_iban=IBAN
organization_bankAccount_bic=BIC
organization_bankAccount_user=Cr\u00E9e par
organization_bankAccount_partner=Partenaire
organization_bankAccount_date=Date cr\u00E9ation

xPath=xPath

#Statuts
NONE=Non lu
PENDING=Brouillon
APPROVED=Approuv\u00E9
ACCEPTED=Confirm\u00E9
ACCEPTED_WITH_AMENDMENT=Confirm\u00E9 avec modifications
REFUSED=Refus\u00E9
SENT=Exp\u00E9di\u00E9
ACQUITTED=Acquitt\u00E9
ARCHIVED=Archiv\u00E9
UNARCHIVABLE=Non archivable
IN_SUBMISSION=En cours de versement
SUBMITTED=Vers\u00E9
REMOVED=Supprim\u00E9
ERROR=Erreur
WARNING=Warning
DUPLICATE=Dupliqu\u00E9
TO_VALIDATE=A valider
UPDATED=Mis \u00E0 jour
READ=Lu
CANCEL=Annul\u00E9
FATAL=Erreur syst\u00E8me
SENT_PARTIALLY=Exp\u00E9di\u00E9 partiellement
INVOICED=Factur\u00E9
PARTIALLY_ACCEPTED=Confirm\u00E9 partiellement
PARTIALLY_ACCEPTED_STYLE=status_partially_accepted
PARTIALLY_ACCEPTED_WITH_AMENDMENTS=Confirm\u00E9 partiellement avec modifications
PARTIALLY_ACCEPTED_WITH_AMENDMENTS_STYLE=status_deep_blue
PARTIALLY_SHIPPED = Envoy\u00E9 partiellement
PARTIALLY_SHIPPED_ICON=fa fa-paper-plane
PARTIALLY_SHIPPED_STYLE=status_yellow
ANSWERED=R\u00E9pondu
TIMEOUT=Expir\u00E9
TO_REMOVE=A supprimer
CLOSED=Ferm\u00E9
DELIVERED=Livr\u00E9
NONE_ICON=fa fa-envelope-o
NONE_STYLE=status_yellow
PENDING_ICON=fa fa-eur
PENDING_STYLE=status_yellow
APPROVED_STYLE=info
ACCEPTED_ICON=fa fa-check
ACCEPTED_STYLE=info
ACCEPTED_WITH_AMENDMENT_ICON=fa fa-check-circle-o
ACCEPTED_WITH_AMENDMENT_STYLE=info
REFUSED_ICON=fa fa-exclamation-triangle
REFUSED_STYLE=status_red
SENT_ICON=fa fa-paper-plane-o
SENT_STYLE=info
ACQUITTED_STYLE=status_green
REMOVED_STYLE=default
ERROR_ICON=fa fa-remove
ERROR_STYLE=status_red
WARNING_STYLE=status_yellow
DUPLICATE_STYLE=status_yellow
TO_VALIDATE_ICON=fa fa fa-pause fa-fw
TO_VALIDATE_STYLE=status_yellow
UPDATED_STYLE=info
READ_ICON=fa fa-eye
READ_STYLE=info
CANCEL_ICON=fa fa-times
CANCEL_STYLE=default
FATAL_STYLE=status_red
SENT_PARTIALLY_ICON=fa fa-paper-plane
SENT_PARTIALLY_STYLE=status_yellow
INVOICED_STYLE=status_green
ANSWERED_ICON=fa fa-undo fa-fw fa-rotate-90
ANSWERED_STYLE=info
TIMEOUT_ICON=fa fa-remove fa-fw
TIMEOUT_STYLE=status_red
TO_REMOVE_ICON=fa fa-archive
TO_REMOVE_STYLE=status_yellow
CLOSED_STYLE=default
DELIVERED_STYLE=status_green
SYNTAX_ERR=Err. Syntaxe
SYNTAX_ERR_STYLE=status_red
DEMAT_ERR=Err. D\u00E9mat
DEMAT_ERR_STYLE=status_red
TO_CORRECT=A corriger
TO_CORRECT_STYLE=status_yellow
OK=Ok
OK_STYLE=status_green
RESOLVED=R\u00E9solu
RESOLVED_STYLE=status_green
SHIPPED=Envoy\u00E9
SHIPPED_STYLE=status_green
ERR_XLEG=Erreur Ex. L\u00E9g.
ERR_XLEG_STYLE=status_red
REFUSED_MANUALLY=Refus\u00E9 manuellement
REFUSED_MANUALLY_STYLE=default
FORCED=Forc\u00E9
FORCED_STYLE=default
INTEGRATED=Int\u00E9gr\u00E9
INTEGRATED_STYLE=status_green
IN_DISPUTE=En litige
IN_DISPUTE_STYLE=status_yellow
BLOCKED=Bloqu\u00E9
BLOCKED_STYLE=status_orange
TO_PAY=A payer
TO_PAY_STYLE=status_green
PAID=Pay\u00E9
PAID_STYLE=status_green
NO_ROUTE=Route absente
NO_ROUTE_STYLE=status_yellow
IMPORT_CORRECTION=Correction import
IMPORT_CORRECTION_STYLE=info
UNKNOWN=Inconnu
UNKNOWN_STYLE=status_yellow
UNKNOWN_ICON=fa fa-remove

IN_PREPARATION=En cours de pr\u00E9paration
IN_PREPARATION_STYLE=status_orderlist_yellow

IN_DELIVERY=En cours de livraison
IN_DELIVERY_STYLE=status_orderlist_yellow

VALIDATED=Valid\u00E9
VALIDATED_STYLE=status_orderlist_green

IN_VALIDATION=En cours de validation
IN_VALIDATION_STYLE=status_orderlist_blue

REJECTED=Rejet\u00E9e
REJECTED_STYLE=status_red

#Workflow statuses
WKF_READY=Pr\u00EAt pour wkf
WKF_READY_STYLE=default
WKF_IN_PROGRESS=Wkf en cours
WKF_IN_PROGRESS_STYLE=status_yellow
WKF_VALIDATED=Wkf valid\u00E9
WKF_VALIDATED_STYLE=status_green
WKF_REFUSED=Wkf refus\u00E9
WKF_REFUSED_STYLE=status_red
WKF_NA=Wkf sans objet
WKF_NA_STYLE=info

#Reconciliation statuses
RCL_READY=Pr\u00EAt pour rap.
RCL_READY_STYLE=default
RCL_IMPOSSIBLE=Rap. impossible
RCL_IMPOSSIBLE_STYLE=status_red
RCL_INTRUDER_PROD=Rap. - Produit intrus
RCL_INTRUDER_PROD_STYLE=status_orange
RCL_DISCREPANCY=Ecart de rap.
RCL_DISCREPANCY_STYLE=status_orange
RCL_QTY_DISCR=Ecart de rap. qt\u00E9
RCL_QTY_DISCR_STYLE=status_orange
RCL_PRICES_DISCR=Ecart de rap. prix
RCL_PRICES_DISCR_STYLE=status_orange
RCL_SUCCESS=Rap. positif
RCL_SUCCESS_STYLE=status_green
RCL_NA=Rap. sans objet
RCL_NA_STYLE=info

#DB Schenker
REFERENTIAL_OK=R\u00E9f\u00E9rentiel ok
REFERENTIAL_OK_STYLE=status_green
REFERENTIAL_KO=R\u00E9f\u00E9rentiel ko
REFERENTIAL_KO_STYLE=status_red
CONTROL_OK=Contr\u00F4les ok
CONTROL_OK_STYLE=status_green
METADATA_KO=M\u00E9tadonn\u00E9es ko
METADATA_KO_STYLE=status_red
SIGNATURE_OK=Signature ok
SIGNATURE_OK_STYLE=status_green
SIGNATURE_KO=Signature ko
SIGNATURE_KO_STYLE=status_red
BEING_SENT_CLIENT=En cours envoi client
BEING_SENT_CLIENT_STYLE=status_orange
AWAITING_VALIDATION=Attente validation
AWAITING_VALIDATION_STYLE=status_orange
SMTP_ERROR=Erreur smtp
SMTP_ERROR_STYLE=status_red
CONTROL_TOTAL_KO=Contr\u00F4les montant ko
CONTROL_TOTAL_KO_STYLE=status_red
END_PROCESS=Fin traitement
END_PROCESS_STYLE=status_green
RESENT=Renvoy\u00E9
RESENT_STYLE=status_green
BEING_PROCESSED=En cours de traitement
BEING_PROCESSED_STYLE=status_orange
REFERENTIAL_ERROR=Erreur annuaire
REFERENTIAL_ERROR_STYLE=status_red
#Stage
Stage_UNKNOWN=Inconnu
Stage_UNDEFINED=Non d\u00E9mat\u00E9rialis\u00E9e
Stage_CORRECT=D\u00E9mat\u00E9rialis\u00E9e
Stage_ERROR=Erreur
Stage_REFUSED=Refus\u00E9e
Stage_UPLOADED=D\u00E9pos\u00E9e
Stage_SENT=Emise par la plateforme
Stage_RECEIVED=Re\u00E7ue par la plateforme
Stage_REJECTED=Rejet\u00E9e
Stage_AVAILABLE=Mise \u00E0 disposition
Stage_OPENED=Prise en charge
Stage_SUSPENDED=Suspendue
Stage_DISPUTED=En litige
Stage_COMPLETED=Compl\u00E9t\u00E9e
Stage_APPROVED=Approuv\u00E9e
Stage_APPROVED_PARTIALLY=Approuv\u00E9e partiellement
Stage_PAYMENT_SENT=Paiement transmis
Stage_PAYMENT_RECEIVED=Encaiss\u00E9e
Stage_APPROVED_B2G=Vis\u00E9e
Stage_NOT_APPROVED_B2G=Non vis\u00E9e
Accounting_Active = Actif
Accounting_Inactive = Inactif
UNDEFINED_STYLE_STAGE=default
CORRECT_STYLE_STAGE=status_green
REFUSED_STYLE_STAGE=status_red
ERROR_STYLE_STAGE=status_red
UNKNOWN_STYLE_STAGE=status_yellow
UPLOADED_STYLE_STAGE=status_green
SENT_STYLE_STAGE=status_green
RECEIVED_STYLE_STAGE=status_green
REJECTED_STYLE_STAGE=status_red
AVAILABLE_STYLE_STAGE=status_green
OPENED_STYLE_STAGE=status_green
SUSPENDED_STYLE_STAGE=status_yellow
DISPUTED_STYLE_STAGE=status_yellow
COMPLETED_STYLE_STAGE=status_yellow
APPROVED_STYLE_STAGE=status_green
APPROVED_PARTIALLY_STYLE_STAGE=status_yellow
PAYMENT_SENT_STYLE_STAGE=status_green
PAYMENT_RECEIVED_STYLE_STAGE=status_green
APPROVED_B2G_STYLE_STAGE=status_green
NOT_APPROVED_B2G_STYLE_STAGE=status_red

#Archive
UNDEFINED_STYLE=default
ARCHIVED_STYLE=status_green
SUBMITTED_STYLE=status_green
IN_SUBMISSION_STYLE=status_yellow
UNARCHIVABLE_STYLE = status_red

autoGenerationOfSSCC = SSCC g\u00E9n\u00E9r\u00E9

# column types
com_byzaneo_xtrade_api_DocumentStatus=Statut
java_lang_Boolean=Bool\u00E9en
java_lang_Class=Classe
java_lang_Integer=Entier
com_byzaneo_xtrade_api_DocumentStage=Etat
java_lang_String=Texte
java_math_BigDecimal=D\u00E9cimal
java_util_Date=Date
javax_xml_datatype_XMLGregorianCalendar=Date XML
com_byzaneo_xtrade_api_DocumentConsultStatus=Etat de consultation

# Archiving policy
Cascade=Purger les documents enfants
Detach=D\u00E9tacher les documents enfants
Ignore=Ignorer les documents parents
doc_purge_children_policy=Liens parents-enfants

S=Emetteur
R=R\u00E9cepteur
SENDER=Emetteur
RECIPIENT=R\u00E9cepteur

# Demat partner Dialog
demat_partner_dialog_title=Historique des modifications

# Order Rights Portlet
portlet_order_confirm=ORDER: Confirmer
portlet_order_confirm_with_modification=ORDER: Confirmer avec modification
portlet_order_mark_as_unread=ORDER: Marquer comme non lu
portlet_order_actions_1=ORDER: Action 1
portlet_order_actions_2=ORDER: Action 2
portlet_order_actions_3=ORDER: Action 3
portlet_order_actions_4=ORDER: Action 4
portlet_order_actions_5=ORDER: Action 5
portlet_order_actions_6=ORDER: Action 6
portlet_order_actions_7=ORDER: Action 7
portlet_order_actions_8=ORDER: Action 8
portlet_order_actions_9=ORDER: Action 9
portlet_order_actions_10=ORDER: Action 10

#Standard Order List
portlet_standardorderlist_export=STANDARD ORDER LIST: Exporter
portlet_standardorderlist_export_list=STANDARD ORDER LIST: Exporter liste
portlet_standardorderlist_duplicate=STANDARD ORDER LIST: Dupliquer
portlet_standardorderlist_remove=STANDARD ORDER LIST: Supprimer
portlet_standardorderlist_validate=STANDARD ORDER LIST: Valider manuellement
portlet_standardorderlist_refuse=STANDARD ORDER LIST: Refuser manuellement
portlet_standardorderlist_actions_1=STANDARD ORDER LIST: Action 1
portlet_standardorderlist_actions_2=STANDARD ORDER LIST: Action 2
portlet_standardorderlist_actions_3=STANDARD ORDER LIST: Action 3
portlet_standardorderlist_actions_4=STANDARD ORDER LIST: Action 4
portlet_standardorderlist_actions_5=STANDARD ORDER LIST: Action 5
portlet_standardorderlist_actions_6=STANDARD ORDER LIST: Action 6
portlet_standardorderlist_actions_7=STANDARD ORDER LIST: Action 7
portlet_standardorderlist_actions_8=STANDARD ORDER LIST: Action 8
portlet_standardorderlist_actions_9=STANDARD ORDER LIST: Action 9
portlet_standardorderlist_actions_10=STANDARD ORDER LIST: Action 10

# OrderResponse Rights Portlet
portlet_orderresponse_export= ORDER RESPONSE: Exporter
portlet_orderresponse_print= ORDER RESPONSE: Imprimer
portlet_orderresponse_import= ORDER RESPONSE: Importer

# Invoice Right Portlet
portlet_invoice_refused_manually=INVOICE: Refuser manuellement
portlet_invoice_forced=INVOICE: Forcer
portlet_invoice_add=INVOICE: Ajouter
portlet_invoice_modify=INVOICE: Modifier
portlet_invoice_import=INVOICE: Importer
portlet_invoice_export=INVOICE: Exporter
portlet_invoice_export_list=INVOICE: Exporter liste
portlet_invoice_view_attachment=INVOICE: Visualiser pi\u00E8ce jointe
portlet_invoice_view_history=INVOICE: Auditer
portlet_invoice_remove=INVOICE: Supprimer
portlet_invoice_correct=INVOICE: Corriger
portlet_invoice_actions_1=INVOICE: Action 1
portlet_invoice_actions_2=INVOICE: Action 2
portlet_invoice_actions_3=INVOICE: Action 3
portlet_invoice_actions_4=INVOICE: Action 4
portlet_invoice_actions_5=INVOICE: Action 5
portlet_invoice_actions_6=INVOICE: Action 6
portlet_invoice_actions_7=INVOICE: Action 7
portlet_invoice_actions_8=INVOICE: Action 8
portlet_invoice_actions_9=INVOICE: Action 9
portlet_invoice_actions_10=INVOICE: Action 10
portlet_invoice_ocr_verify=INVOICE: Vid\u00E9ocoder
portlet_invoice_archive_bulk_export=INVOICE ARCHIVE: Export en masse
portlet_invoice_archive_delete=INVOICE ARCHIVE: Supprimer
portlet_invoice_archive_extend_archiving_duration=INVOICE ARCHIVE: Prolonger la dur\u00E9e d'archivage
portlet_payment_actions_1=PAYMENT: Action 1
portlet_payment_actions_2=PAYMENT: Action 2
portlet_payment_actions_3=PAYMENT: Action 3
portlet_payment_actions_4=PAYMENT: Action 4
portlet_payment_actions_5=PAYMENT: Action 5
portlet_payment_actions_6=PAYMENT: Action 6
portlet_payment_actions_7=PAYMENT: Action 7
portlet_payment_actions_8=PAYMENT: Action 8
portlet_payment_actions_9=PAYMENT: Action 9
portlet_payment_actions_10=PAYMENT: Action 10
portlet_payment_suggest_early_payment=Paiement anticip\u00E9 acheteur
portlet_payment_accept_reject_early_payment=Paiement anticip\u00E9 vendeur
portlet_reception_remove=RECADV: Supprimer
# Portlet Categories
EDOCUMENT=INVOICING SERVICES
RTE=EDI SERVICES
GCN=COLLABORATIVE NETWORK
SMARTPDF=SUPPLIER PORTALS
CUSTOMERS=CUSTOMER PORTALS
DOC=KPI
DEPRECATED=D\u00C9PR\u00C9CI\u00C9E

SYSTEMATIC=G\u00E9n\u00E9ration syst\u00E9matique
REPLACE_IF_EXISTS=G\u00E9n\u00E9ration avec remplacement
NO_GENERATION_IF_EXISTS=Pas de g\u00E9n\u00E9ration si le fichier existe

# Application type
Development=D\u00E9veloppement
Acceptance=Recette
Preproduction=Pr\u00E9-production
Production=Production
Unknown=INCONNU !

#Factor
factor=Factor

# Application name
Noname=PAS DE NOM !

#Bank Accounts
bank_accounts_dlg_header=Ajouter des coordonn\u00E9es bancaires
bank_account_currency=Devise
bank_account_iban=IBAN
bank_account_bic=BIC

bank_account_name_required=Le nom est obligatoire
bank_account_iban_error_exist=D\u00E9sol\u00E9 un IBAN est d\u00E9ja associ\u00E9 \u00E0 cette devise, merci de supprimer l'IBAN avant d'en saisir un autre
bank_account_iban_error_notValid=L'IBAN n'est pas valide
bank_account_iban_error_required=L'IBAN est obligatoire
bank_account_bic_error_required=Le BIC code est obligatoire
bank_account_bic_error_notValid=Le BIC code est constitu\u00E9 de 8 ou 11 caract\u00E8res
bank_account_delete_confirm=Etes-vous s\u00FBr de vouloir supprimer l'IBAN pour la devise
bank_account_deleting_error=Erreur lors de la suppression du compte bancaire {0}

#Environment Constraints
environment_error_max_characters_allowed = Maximum 64 caract\u00E8res sont autoris\u00E9s

#Order Tracking 
to_validate = En cours de validation
in_validation = En cours de validation
validated = Valid\u00E9e
to_prepare = En cours de pr\u00E9paration
in_delivery = En cours de livraison
order_history_title = HISTORIQUE DE MA COMMANDE N\u00B0 :
estimated_delivery_date = Date de livraison estim\u00E9e :
requested_delivery_date = Date de livraison souhait\u00E9e :
order_number = Commande n\u00B0

#customer
customer_clientPartners=Mes ${clients}
customer_clientUsers=Mes utilisateurs
customer_partner_number=${client} n\u00B0
customer_partner_name=Nom
customer_partner_address_city=Ville
customer_partner_connectionCode=N\u00B0 unique de connexion
customer_partner_userNumber=Nombre d'utilisateurs
customer_partner_create=Ajouter un ${client}
customer_partner_create_button=Ajouter le ${client}
customer_partner_edit=${client}
customer_partner_edit_button=Mettre \u00E0 jour le ${client}
customer_partner_delete=Supprimer le ${client}
customer_partner_message_delete=Le ${client} sera supprim\u00E9 d\u00E9finitivement.
customer_partner_delete_yes=Oui je le supprime
customer_partner_delete_no=Non je vais r\u00E9fl\u00E9chir
customer_partner_import=Importer un fichier ${client}
customer_partner_import_header=Importer un fichier ${client} (*.xls, *.xlsx)
customer_partner_import_button=Importer le fichier
customer_partner_export=Exporter un fichier ${client}
customer_partner_add_user=Ajouter un utilisateur
customer_partner_add_user_dialog_header=Ajouter un utilisateur au ${client}
customer_partner_name_for_user=Nom ${client}
customer_partner_search_placeholder=Rechercher un ${client} par n\u00B0, nom ou ville
partner_company=Soci\u00E9t\u00E9
partner_client_name=Nom ${client}
partner_address_1=Adresse 1
partner_address_2=Adresse 2
partner_postal_code=Code Postal
partner_city=Ville
partner_country=Pays
partner_user=Utilisateur
partner_user_create=Ajouter un utilisateur
partner_user_delete=Supprimer l'utilisateur
partner_user_send_new_pass=R\u00E9initialiser le mot de passe
partner_user_send_new_pass_header=R\u00E9initialiser le mot de passe pour l'utilisateur
partner_user_message_send_new_pass_msg=Un email va \u00EAtre envoy\u00E9 \u00E0 l'utilisateur avec un lien qui lui permettra de changer son mot de passe.
partner_user_send_pass_yes=Oui je veux envoyer
partner_user_send_pass_no=Non je vais r\u00E9fl\u00E9chir
partner_user_roles=R\u00F4le de l'utilisateur
partner_user_roles_perimeters=R\u00F4le et p\u00E9rim\u00E8tre de l'utilisateur
info_limited_extended_scope=Indiquer dans ces champs les entit\u00E9s auxquelles cet utilisateur peut avoir acc\u00E8s. Vous pouvez renseigner le nom d'une ou plusieurs entit\u00E9s ou le nom d'un groupe d'entit\u00E9.
info_partners=S\u00E9lectionner ici le nom ou le code d'une entit\u00E9.
info_perimeters=S\u00E9lectionner ici le nom d'un groupe d'entit\u00E9s.
partner_user_roles_save=Modifier
partner_user_search_placeholder=Rechercher un utilisateur par n\u00B0, nom ${client}, utilisateur, pr\u00E9nom ou nom
partner_user_add_role=Attribuer un r\u00F4le
user_login=Utilisateur
user_login_validator=L'identifiant de l'utilisateur doit \u00EAtre une adresse email.
user_email_address=Email
user_phone=T\u00E9l\u00E9phone
user_mobile=Mobile
user_last_authentication=Derni\u00E8re connexion
user_number_connexion=Connexion(s)
user_creation_date=Date de cr\u00E9ation
user_password_dialog_header=Ajouter un mot de passe
user_password_confirm=Confirmer le mot de passe
user_message_delete=L'utilisateur sera supprim\u00E9 d\u00E9finitivement.
user_empty_client=Choisir un ${client}...
add_user_button=Ajouter l'utilisateur
edit_user_button=Mettre \u00E0 jour l'utilisateur
add_user_mail_button=Ajouter l'utilisateur + Reset password link
customer_partner_show_users=Utilisateurs associ\u00E9s au client
users_search_placeholder=Rechercher un utilisateur par utilisateur, pr\u00E9nom ou nom

#Library Portlet
category = Cat\u00E9gorie
Category.name = Cat\u00E9gorie
category_field_empty = Le champs cat\u00E9gorie doit \u00EAtre renseign\u00E9.
fileName = Nom du fichier
introduction_placeholder = Texte d'introduction Ex : \n La documentation disponible est class\u00E9e par cat\u00E9gorie. N'h\u00E9sitez pas \u00E0 cliquer sur une cat\u00E9gorie pour d\u00E9couvrir les documents que nous mettons \u00E0 votre disposition. Si un document est manquant vous pouvez nous le signaler via la page de contact.
library_created_success=La documentation a \u00E9t\u00E9 cr\u00E9\u00E9e
library_edited_succes= La documentation a \u00E9t\u00E9 modifi\u00E9e
library_deleted_success=La documentation a \u00E9t\u00E9 supprim\u00E9e
library_document_introduction = La documentation disponible est class\u00E9e par cat\u00E9gorie. N'h\u00E9sitez pas \u00E0 cliquer sur une cat\u00E9gorie pour d\u00E9couvrir les documents que nous vous mettons \u00E0 disposition.
portlet_library = Biblioth\u00E8que
save_success=L'ensemble de la biblioth\u00E8que a \u00E9t\u00E9 sauvegard\u00E9
info_category=Pour ordonner les cat\u00E9gories, il est n\u00E9cessaire de faire un gliss\u00E9 / d\u00E9pos\u00E9 en remontant la cat\u00E9gorie du bas vers le haut
uploaded_since = Mise en ligne depuis
one_hour_past = moins d'une heure
a_day_past = moins d'un jour
add_library=Ajouter une biblioth\u00E8que
edit_library=Editer une biblioth\u00E8que
by_date = le {0}
info_addressSearch=Il n'est pas possible de rechercher par champ de pays

# Shopping Cart
cart_view=VOIR LE PANIER
cart_checkout=VALIDER
cart_numberOfArticles=article(s) dans le panier
cart_totalAmount=Total :
active_carts=panier(s) actif(s)
total_carts=Total des paniers :
validity_date=Date d'expiration:
not_valid=Pas valid

# RTE Status
rte_status_deployed = Le script RTE a bien \u00E9t\u00E9 d\u00E9ploy\u00E9
rte_status_deployed_but_changed = Le script RTE a \u00E9t\u00E9 modifi\u00E9 depuis son d\u00E9ploiement
rte_status_not_deployed = Le script RTE n'est pas d\u00E9ploy\u00E9
rte_status_unknown =  ?EAtre d\u00E9termin\u00E9

# RTE Test Results
run_rte=Ex\u00E9cuter
default_test_rte=Test par d\u00E9faut
individual_tests_rte=Test individuel
rte_no_init_test_file=Aucun fichier init.tst par d\u00E9faut n'a \u00E9t\u00E9 trouv\u00E9
rte_base_not_supported=Les collections RTE sont exclues du domaine de test (pour l'instant)
rte_forbidden_value=init.tst: la propri\u00E9t\u00E9 {0} n''est pas correctement renseign\u00E9e
rte_test_success_message=Rte test ex\u00E9cut\u00E9 avec succ\u00E8s
rte_test_fail_message=Rte test ex\u00E9cut\u00E9 avec \u00E9chec
rte_test_properties_placeholder=Propri\u00E9t\u00E9s:
rte_test_results_header=R\u00E9sultats du Test Rte
rte_input_file_must_be_unique=Le fichier d'entr\u00E9e doit \u00EAtre unique
rte_property_unknown=init.tst: la propri\u00E9t\u00E9 {0} est inconnue

#Save filters in local storage
confirmationTitle=Confirmation
confirmOverrideFilter=Le filtre existe d\u00E9j\u00E0. Voulez-vous l'\u00E9craser ?
canNotSaveFilter =Vous ne pouvez pas sauvegarder ce filtre car l\u2019emplacement de stockage est plein
filterSuccessfullySaved=Le filtre a \u00E9t\u00E9 enregistr\u00E9

# Instance Types
instance_type=Type de portail
instance_type_CUSTOMER=Client
instance_type_EDI=EDI
instance_type_INVOICE=Facture
instance_type_SPECIFIC=Sp\u00E9cifique
instance_type_SUPPLIER=Fournisseur
instance_type_INVOICE_PDP=Facture - PDP

client_type_SPECIFIC=partenaire
client_type_EDI=partenaire
client_type_SUPPLIER =fournisseur
client_type_CUSTOMER =client
client_type_INVOICE=partenaire
clients_type_SPECIFIC=partenaires
clients_type_EDI=partenaires
clients_type_SUPPLIER=fournisseurs
clients_type_CUSTOMER=clients
clients_type_INVOICE=partenaires

#edit role dialog
bql_filter_title=Filtres
bql_filter_details=Requ\u00EAte BQL filtrage portlet Invoice

#assignable roles
assignable_roles=R\u00F4les attribuables

update_user_parameters=Mettre \u00E0 jour mes param\u00E8tres

triggername_ACTION_1=Action 1
triggername_ACTION_2=Action 2
triggername_ACTION_3=Action 3
triggername_ACTION_4=Action 4
triggername_ACTION_5=Action 5
triggername_ACTION_6=Action 6
triggername_ACTION_7=Action 7
triggername_ACTION_8=Action 8
triggername_ACTION_9=Action 9
triggername_ACTION_10=Action 10

#perimeter
perimeter=P\u00E9rim\u00E8tre
perimeters=P\u00E9rim\u00E8tres
error_saving_perimeter=Erreur lors de la sauvegarde du p\u00E9rim\u00E8tre ({0}).
info_perimeter_saved=P\u00E9rim\u00E8tre {0} sauvegard\u00E9
info_perimeter_removed=P\u00E9rim\u00E8tre {0} supprim\u00E9
error_removing_perimeter=Erreur lors de la suppression du p\u00E9rim\u00E8tre ({0}).
exception_duplicate_perimeter=P\u00E9rim\u00E8tre dupliqu\u00E9\u00A0: {0}.
exception_not_match_companyCode={0} ne correspond \u00E0 aucun code d'entreprise
exception_not_existing_partner=Le partenaire {0} n'existe pas
info_perimeter_duplicated=Le p\u00E9rim\u00E8tre {0} \u00E9t\u00E9  dupliqu\u00E9
perimeter_edit=\u00C9diter p\u00E9rim\u00E8tre pour
exception_perimeter_has_partners=Le p\u00E9rim\u00E8tre {0} a {1} partenaire(s) associ\u00E9(s).
exception_perimeter_has_users=Le p\u00E9rim\u00E8tre {0} a {1} utilisateur(s) associ\u00E9(s).
scope_partner=P\u00E9rim\u00E8tre \u00E9tendu
scope_customer=P\u00E9rim\u00E8tre restreint

#announcement
no_message_display=Ne plus afficher ce message

#cookie
cookie=Cookie

#SSO Authentication
regex=Regex
duplicate_mapping=Ce mappage existe d\u00E9j\u00E0.
sso_role_mapping=Mapping des r\u00F4les
add_sso_role_mapping=Ajouter un mapping de r\u00F4le

#AccessTokenV1 Authentication
accessTokenV1_use=Utilisation
accessTokenV1_url=URL
accessTokenV1_username=Nom d'utilisateur
accessTokenV1_password=Mot de passe
accessTokenV1_getTokenBtn=Obtenir un token
accessTokenV1_token=Token
accessTokenV1_tokenExpirationDate=Date d'expiration du token
accessTokenV1_access_fail=Impossible d'acc\u00E9der \u00E0 cette ressource : {0}.
ROSSUM=ROSSUM
OTHER=AUTRE

#document status
document_status=Statuts des documents
add_new_status=Ajouter un statut pour les documents
edit_status=Editer un statut pour les documents
status_import_file=Import d'un fichier statuts des documents (*.xls, *.xlsx)
document_status_added=Le statut du document {0} a \u00E9t\u00E9 ajout\u00E9
document_status_updated=Le statut du document {0} a \u00E9t\u00E9 mis \u00E0 jour
document_status_removed=Le statut du document {0} a \u00E9t\u00E9 supprim\u00E9
document_status_exists=Le statut du document {0} existe d\u00E9j\u00E0
document_statuses_updated={0} statut(s) a\\ont \u00E9t\u00E9 mis \u00E0 jour
document_statuses_added={0} nouveau(x) statut(s) a\\ont \u00E9t\u00E9 ajout\u00E9(s)
doc_status_code=Code statut
doc_status_style=Style statut

#Notifications/Workflow
workflow_checkbox=Autoriser le param\u00E9trage des notifications de workflow par les utilisateurs
workflow_checkBox_help=Si la case est coch\u00E9e, l'utilisateur aura la possibilit\u00E9 de choisir, depuis son profil, si il souhaite \u00EAtre notifi\u00E9 \u00E0 chaque nouveau document
workflow_template=Template
workflow_status_unprocessed=Statut si aucun workflow applicable
menu_user_notifications=Mes notifications
menu_user_partner_bank=Coordonn\u00E9es bancaires de ma soci\u00E9t\u00E9
menu_user_addresses=Carnet d'adresses de ma soci\u00E9t\u00E9
menu_user_invoice_entry_preferences=Pr\u00E9f\u00E9rences de facturation de ma soci\u00E9t\u00E9
menu_user_customer_client_env_bank=Coordonn\u00E9es bancaires de mes clients
menu_user_customer_supplier_env_bank=Coordonn\u00E9es bancaires de mes fournisseurs
menu_user_customer_other_env_bank=Coordonn\u00E9es bancaires de mes partenaires
general_notifications=Mes notifications g\u00E9n\u00E9rales
activate_notifications=Activer mes notifications
workflow_notifications=G\u00E9rer mes notifications de workflow
update_user_notifications=Mettre \u00E0 jour mes notifications
info_notifications_saved=Notifications utilisateur sauvegard\u00E9es
not_notified_if_absent_and_have_replacement=Ne pas \u00EAtre notifi\u00E9 si je suis absent et qu'une autre personne me remplace
notify_for_new_action_to_perform=Etre notifi\u00E9 \u00E0 chaque fois que j'ai une action \u00E0 r\u00E9aliser sur une facture
info_invoice_entry_preferences_saved=Mettre \u00E0 jour mes pr\u00E9f\u00E9rences de facturation

#IE banner
banner=Banni\u00E8re
checkbox_banner=Activer la banni\u00E8re
banner_text=Texte \u00E0 afficher
ie_recognition=Reconnaissance Internet Explorer

#Counter management
counter_management=Gestion compteurs miniatures
counter_management_title=S\u00E9lectionner les compteurs \u00E0 afficher
counter_management_other_page_configuration=Utiliser le param\u00E9trage d'une autre page
counter_management_own_configuration=D\u00E9finir un param\u00E9trage sp\u00E9cifique pour cette page
counter_management_counter=Compteur
counter_management_pages=Pages
delete_counter_error_message=Attention, il n'est pas possible de supprimer ce compteur car il est utilis\u00E9 dans les compteurs miniatures.

#instance KPI
database_kpi=Base de donn\u00E9es KPI
aws_batch=Batch AWS
arn=ARN du r\u00F4le IAM
host_name=Domaine
tcp_port=Port TCP
database_name=Nom de la base
cron_expression=Expression Cron
placeholder_host_name=Le nom d'h\u00F4te ou l'adresse IP du syst\u00E8me o\u00F9 fonctionne l'instance
placeholder_database_name=Le nom de la base de donn\u00E9es de l'instance
placeholder_tcp=Le port TCP sur lequel \u00E9coute l'instance
url_tracking=Activer le suivi des URL pour chaque utilisateur connect\u00E9
warn_kpi_invalid=La service KPI ne peut pas \u00EAtre d\u00E9marr\u00E9. Veuillez r\u00E9essayer plus tard ou contacter votre administrateur
kpis=KPIs

#Page configuration
page_display_option=Ne pas afficher dans le menu
page_submenu_warning=L'utilisation d'un menu \u00E0 3 niveaux peut provoquer des bugs d'affichage. Pensez \u00E0 v\u00E9rifier le rendu du menu sur le front office.
angular_page=Page angular
#archive storage
archive_storage_option=Stockage d'archives

#Payment status
EP_ELIGIBLE = Eligible
EP_INELIGIBLE = In\u00E9ligible
EP_SUBMITTED = Demande en cours
EP_ACCEPTED = Demande accept\u00E9e
EP_REFUSED = Demande refus\u00E9e
EP_PERFORMED = Paiement effectu\u00E9

#ReconciliationEngine
reconciliation=R\u00E9conciliation
invoiceItem=InvoiceItemCodeField
orderItem=OrderItemCodeField
receptionItem=ReceptionItemCodeField
reconciliation_info=Configuration de la cl\u00E9 de recherche qui permet d'effectuer la r\u00E9conciliation des lignes
reconciliation_status_unprocessed= Statut si aucune r\u00E9conciliation applicable
imputation_status_unprocessed=Statut si aucun sc\u00E9nario d'imputation applicable

#Reconciliation
reconciliation_empty_seller_party=R\u00E9conciliation impossible : code fournisseur absent.
reconciliation_empty_order_number=R\u00E9conciliation impossible : n\u00B0 de commande absent.
reconciliation_order_not_found=R\u00E9conciliation impossible : commande n\u00B0{0} introuvable.
reconciliation_start=D\u00E9but de la r\u00E9conciliation.
reconciliation_success=R\u00E9conciliation termin\u00E9e avec succ\u00E8s.
reconciliation_gap_found=R\u00E9conciliation termin\u00E9e, des \u00E9carts ont \u00E9t\u00E9 rencontr\u00E9s.

#e-Reporting
eReporting_tab=e-Reporting
period_except_february=P\u00E9riodicit\u00E9 hors f\u00E9vrier
period_specific_to_february=P\u00E9riodicit\u00E9 sp\u00E9cifique \u00E0 f\u00E9vrier
eReporting_frequency=P\u00E9riodicit\u00E9 du d\u00E9clenchement automatique des transmission d'eReporting vers le gouvernement Fran\u00E7ais
eReporting_collection=S\u00E9lection de la collection

#Authentication server
authenticationServer_tab=Serveur d'authentification
authenticationServer_btn=Configurer le serveur d'authentification
auth_reconfigureClientsForUsurpation=Reconfigurer les clients pour l'usurpation
authenticationServer_configuration=Configuration du serveur d'authentification
authentication_configurationType=Type de configuration
authentication_classicConfiguration=Configuration classique
authentication_togetherConfiguration=Configuration together
authentication_clientIdJSF=ClientIdJSF
authentication_clientIdAngular=ClientIdAngular
authentication_defaultClientIdJSF=ClientIdJSF par d\u00E9faut
authentication_defaultClientIdAngular=ClientIdAngular par d\u00E9faut
authentication_clientId=ClientId
authentication_defaultClientId=ClientId par d\u00E9faut
authentication_addPartnerConfiguration=Ajouter une configuration de partenaire
authentication_partners_pairs=Configuration du partenaire
authentication_preDomain=Pr\u00E9-domain
authentication_partners=Partenaire
authentication_selectPartner=S\u00E9lectionnez un partenaire
authentication_reconfiguration=Reconfigurer
error_creating_clientId=Erreur lors de la cr\u00E9ation de clientId {0}
error_creating_authConfig=Erreur lors de la cr\u00E9ation de la configuration de l'authentification du serveur
error_existingAuth_clientId=Le clientId {0} existe d\u00E9j\u00E0 dans le domaine, veuillez en choisir un autre clientId
error_creating_partnerConfig=Erreur lors de la cr\u00E9ation de la configuration d'authentification pour le partenaire {0}
error_creating_companyConfig=Erreur lors de la cr\u00E9ation de la configuration d'authentification pour la soci\u00E9t\u00E9 {0}
error_deletingPartnerWithAuthConfig=Ce partenaire est li\u00E9 \u00E0 la configuration du serveur d'authentification de l'environnement, il n'est pas possible de le supprimer sans supprimer au pr\u00E9alable la configuration le concernant dans le serveur d'authentification
error_deletingCompanyWithAuthConfig=Cette soci\u00E9t\u00E9 est li\u00E9e \u00E0 la configuration du serveur d'authentification. Merci de supprimer au pr\u00E9alable la configuration concernant cette soci\u00E9t\u00E9 avant de la supprimer
error_remove_partnerConfig=\u00C9chec de la suppression de la configuration d'authentification pour le partenaire {0}
error_remove_companyConfig=\u00C9chec de la suppression de la configuration d'authentification pour la soci\u00E9t\u00E9
error_creating_authConfig_missing_config=Afin de cr\u00E9er la configuration d'authentification, les param\u00E8tres d'authentification du groupe doivent \u00EAtre d\u00E9finis (Realm name, ClientAPIId, SecretAPIKey)
error_reconfigureClientsForSwitching=Erreur lors de la configuration des clients pour l'usurpation

#Ordering Party
ordering_identifier=Identifiant
ordering_name=Nom
ordering_description=Description
ordering_number_of_partners=Nombre de partenaires
ordering_partners=Partenaires
ordering_messages_sequence=Enchainement messages
info_ordering_removed=Donneur d'ordres {0} supprim\u00E9
info_ordering_saved=Donneur d'ordres {0} sauvegard\u00E9
ordering_identifier_exists=Un donneur d'ordre ayant l'identifiant {0} existe d\u00E9j\u00E0
add_ordering_party=Ajouter un donneur d\u2019ordres
edit_ordering_party=Editer donneur d\u2019ordres
partners_ordering_party=Partenaires donneur d\u2019ordres
#ordering party diagram
button_message_sequence=S\u00E9quence de messages
dialog_message_sequence_add=S\u00E9quence de messages - Ajouter
dialog_message_sequence_edit=S\u00E9quence de messages - Modifier
add_button_message_sequence=Ajouter
delete_button_message_sequence=Supprimer
save_button_message_sequence=Sauvgarder
cancel_button_message_sequence=Abandonner
listbox_message_sequence_available_messages=Messages disponibles
info_ordering_model_saved=Mod\u00E8le de partenaires sauvgard\u00E9
element_not_removed=Le premier \u00E9l\u00E9ment ne peut pas \u00EAtre supprim\u00E9
element_order=Commande
element_order_response=R\u00E9p. Commande
element_shipping_advice=Avis d'exp\u00E9dition
element_invoice=Facture
element_arrow_optional=Optionnel
menu_ordering_party=Donneurs d\u2019ordres
ordering_party_exit_without_saving=Voulez-vous quitter sans enregistrer le diagramme?
ordering_party_delete_last_element=Voulez-vous supprimer le dernier \u00E9l\u00E9ment?
element_checkbox_title_mandatory=Obligatoire
element_contextmenu_config_form=Configure form
use_ordering_party_configuration=Utiliser le param\u00E9trage du donneur d\u2019ordres
filter_by_type=Filtrer par type

mark_doc_as_unread_warn=Seule une document au statut \"Lu\" peut \u00EAtre r\u00E9initialis\u00E9e . Aucune action effectu\u00E9e.
mark_docs_as_unread_warn=Certaines documents n'ont pas le statut \"Lu\". Aucune action effectu\u00E9e sur ces commandes.

select_legal_controls_source=Choisir
select_legal_controls_label=Appliquer les contr\u00F4les l\u00E9gaux de la l\u00E9gislation
select_duplicate_check=V\u00E9rification des doublons

select_rte_source=Choisir
select_rte_label=Source RTE de contr\u00F4les
select_validation_process=Processus post-validation
outputFile_working_copy=Effectuer une copie [de travail] [du fichier de sortie]

text_to_double_message=doit \u00EAtre un nombre \u00E0 virgule.

#SAP CONFIG
sap_configuration=SAP
sap_configuration_host=Host
sap_configuration_sysnr=Sysnr
sap_configuration_client=Client
sap_configuration_user=User
sap_configuration_password=Passwd
sap_configuration_language=Lang
sap_configuration_repository_optimization=Repository Roundtrip Optimization
sap_configuration_serialization_format=Serialization Format
sap_configuration_network=Network
sap_configuration_pool_size=Pool capacity
sap_configuration_expiration_time=Expiration time
sap_configuration_expiration_check=Expiration check period

#Import Archive BP
existing_templates=Les templates suivants existent d\u00E9j\u00E0 dans l'environnement, veuillez s\u00E9lectionner ceux que vous souhaitez \u00E9craser avec la nouvelle version contenue dans cette archive. Si vous ne souhaitez pas \u00E9craser les templates existants, ne s\u00E9lectionnez aucun template et validez l'import pour importer le process.
template_name=Nom du template
template_type=Type de template

#Workflow Rights
workflow_tab=Workflows
workflow_management=Gestion
workflow_functions=Fonctions
workflow_absences=Absences
workflow_delegations=D\u00E9l\u00E9gations
workflow_monitoring=Monitoring

#Instance
dedicated_instance=D\u00E9di\u00E9

#Keycloak Authentication
keycloak=Keycloak
realm_name=Realm name
client_api_id=ClientAPIId
secret_api_key=SecretAPIKey
idp_alias_for_usurpation=Alias IDP pour usurpation
keycloak_client_id_required=L'identifiant du client keycloak est obligatoire
keycloak_client_secret_required=Le secret du client keycloak est obligatoire
keycloak_realm_required=Le realm keycloak est obligatoire

#Technical users
customer_technical_users=Utilisateurs technique client
technical_user_clientId=ClientId
add_technical_user=Cr\u00E9er un utilisateur technique
edit_technical_user=Modifier l'utilisateur technique
view_secretKey=Visualiser la cl\u00E9 secr\u00E8te
clientId_secretKey=Cl\u00E9 secr\u00E8te ClientId
error_existing_clientId=Ce clientId existe d\u00E9j\u00E0 pour ce royaume, merci de choisir un autre clientId
error_saving_technicalUser=Erreur lors de l''enregistrement de l''utilisateur technique {0}
error_view_secret_key=Erreur lors de l'affichage de la cl\u00E9 secr\u00E8te
error_remove_technicalUser=\u00C9chec de la suppression de l'utilisateur technique
info_technicalUser_saved=Utilisateur technique {0} enregistr\u00E9
info_technicalUser_removed=Utilisateur technique {0} supprim\u00E9
technical_roles=R\u00F4le technique

#Technical roles
role_type=Type
ROLE=Role portail
ROLE_TECHNICAL=Role technique
info_technicalUser_role_saved=R\u00F4le d'utilisateur technique enregistr\u00E9
exception_technicalRole_association=Le r\u00F4le {0} est associ\u00E9 \u00E0 {1} utilisateur(s) technique(s)
exception_technicalUsers_perimeterAssociation=Le p\u00E9rim\u00E8tre {0} a {1} utilisateur(s) technique(s) associ\u00E9(s)

partner_technical_users=Contacts techniques
technical_user_partner=Partenaire
technical_user_selectPartner=S\u00E9lectionnez un partenaire
exception_remove_technicalUser_associated=Les utilisateur(s) technique(s) associ\u00E9s \u00E0 {0} n'ont pas pu \u00EAtre supprim\u00E9s.

#E-reporting
ereporting= Ereporting
transactions= Transaction


#BQL Instructions
bql_instructions_title=Titre des instructions BQL
bql_instructions_documents=Document

ACCOUNTINGPOSTING=ACCOUNTING POSTING
APAUTOMATION=AP AUTOMATION
manage_periodic_reports=Gestion des Rapports p\u00E9riodiques
payments=Paiements
periodic_reports = Rapports p\u00E9riodiques

#KeycloakConfigurationValidator
serviceAccountUser_noAdminRole_error=L''utilisateur du compte de service du client {0} ne dispose pas des droits n\u00E9cessaires pour pour effectuer des actions sur le realm {1} (g\u00E9rer d''autres clients, utilisateurs, etc). Veuillez vous assurer qu''il dispose du r\u00F4le {2} d\u00E9fini dans les Service Account Roles.
serviceAccountUser_tokenExchangePermission_error=Le client avec l'ID client {0} ne dispose pas des droits n\u00E9cessaires pour v\u00E9rifier si l'autorisation 'token-exchange' a \u00E9t\u00E9 activ\u00E9e. Assurez-vous qu'il dispose du r\u00F4le {1} d\u00E9fini dans les Service Account Roles
tokenExchange_disabled_error=La fonctionnalit\u00E9 token-exchange n''est pas activ\u00E9e sur le realm {0}. Pour rediriger les pages d''Angular vers JSF et \u00E9galement pour utiliser la fonctionnalit\u00E9 d''usurpation d''utilisateur, cette autorisation doit \u00EAtre activ\u00E9e
adminClient_authorizationEnabled_disabled_error=Le client avec clientId {0} n''a pas la propri\u00E9t\u00E9 'Authorization Enabled' activ\u00E9e
no_idp_configured_for_usurpation_error=Il n''y a pas d''IDP avec l''alias {0} configur\u00E9 pour l''usurpation sur le realm {1}
no_idp_onGroupAuthConfig=Il n'y a pas d'alias IDP d\u00E9fini dans les configurations d'authentification du groupe (partenaire/client)
idp_configurations_error=Erreurs de configuration IDP:<br/>
idp_disabled_error=L''IDP avec l''alias {0} n''est pas activ\u00E9<br/>
idp_linkingOnly_disabled_error=La propri\u00E9t\u00E9 \"Account Linking Only\" n'est pas activ\u00E9e<br/>
idp_hideOnLoginPage_disabled_error=La propri\u00E9t\u00E9 \"Hide on Login Page\" n'est pas activ\u00E9e<br/>
idp_invalid_firstLoginFlow_error=La propri\u00E9t\u00E9 \"First Login Flow\" doit avoir la valeur \"{0}\"<br/>
idp_invalid_syncMode_error=La propri\u00E9t\u00E9 \"Sync Mode\" doit avoir la valeur \"{0}\"<br/>
idp_invalid_authorizationUrl_error=La propri\u00E9t\u00E9 \"Authorization URL\" n'est pas valide. Elle doit suivre le mod\u00E8le suivant: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/auth<br/>
idp_invalid_tokenUrl_error=La propri\u00E9t\u00E9 \"Token URL\" n'est pas valide. Elle doit suivre le mod\u00E8le suivant: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/token<br/>
idp_invalid_issuerUrl_error=La propri\u00E9t\u00E9 \"Issuer\" n'est pas valide. Elle doit suivre le mod\u00E8le suivant: {auth_base_url}/realms/{bo_realm}<br/>
idp_invalid_userInfoUrl_error=La propri\u00E9t\u00E9 \"User Info URL\" n'est pas valide. Elle doit suivre le mod\u00E8le suivant: {auth_base_url}/realms/{bo_realm}/protocol/openid-connect/userinfo<br/>
idp_invalid_clientAuthMethod_error=La propri\u00E9t\u00E9 \"Client Authentication\" doit avoir la valeur \"Client secret sent as post\"<br/>
idp_invalid_clientId_error=Client ID n'est pas valide. Veuillez v\u00E9rifier la propri\u00E9t\u00E9 auth.server.backoffice.clientId
idp_permissions_disabled_error=Pour activer la configuration de la fonctionnalit\u00E9 d''usurpation d''utilisateur, les autorisations pour l''IDP avec l''alias {0} doivent \u00EAtre activ\u00E9es.
idp_invalid_mapper_error=Pour activer la configuration de la fonctionnalit\u00E9 d''usurpation d''utilisateur, le mappeur d\u00E9fini sur l''IDP avec l''alias {0} doit avoir \"Sync Mode Override\" d\u00E9fini sur \"inherit\", \"Mapper Type\" d\u00E9fini sur \"Hardcoded Role\" et \"Role\" d\u00E9fini sur \"impersonation\"
idp_no_mapper_error=Pour activer la configuration de la fonctionnalit\u00E9 d''usurpation d''utilisateur, il doit y avoir un mappeur avec le r\u00F4le \"impersonation\" d\u00E9fini sur l''IDP avec l''alias {0}
no_impersonation_role_error=Pour activer la configuration de la fonctionnalit\u00E9 d''usurpation d''utilisateur, il doit y avoir un r\u00F4le nomm\u00E9 \"impersonation\" dans le realm {0}
impersonate_disabled_error=Pour activer la configuration de la fonctionnalit\u00E9 d''usurpation d''utilisateur, les permissions pour les utilisateurs dans le realm {0} doivent \u00EAtre activ\u00E9es
no_impersonationRolePolicy_error=Pour activer la configuration de la fonctionnalit\u00E9 d'usurpation d'utilisateur, il doit y avoir une politique de type r\u00F4le avec le r\u00F4le \"impersonation\" marqu\u00E9 comme requis ajout\u00E9 \u00E0 la permission \"impersonate\" sur les utilisateurs
impersonationRolePolicy_error=Pour activer la configuration de la fonctionnalit\u00E9 d''usurpation d''utilisateur, la politique de type r\u00F4le \"{0}\" doit avoir le r\u00F4le \"impersonation\" attribu\u00E9 et marqu\u00E9 comme requis
idp_missingConfigOnPartner_warn=L''IDP requis pour configurer la fonctionnalit\u00E9 d''usurpation d''utilisateur n''a pas \u00E9t\u00E9 ajout\u00E9 aux propri\u00E9t\u00E9s d''authentification du partenaire {0}. Si vous confirmez l''ajout de la configuration d''authentification pour ce partenaire, les configurations requises pour la fonctionnalit\u00E9 d''usurpation d''utilisateur ne seront pas ajout\u00E9es.
idp_missingConfigOnCustomer_warn=L'IDP requis pour configurer la fonctionnalit\u00E9 d'usurpation d'utilisateur n'a pas \u00E9t\u00E9 ajout\u00E9 aux propri\u00E9t\u00E9s d'authentification du client dans cet environnement. Si vous confirmez l'ajout de la configuration d'authentification, les configurations requises pour la fonctionnalit\u00E9 d'usurpation d'utilisateur ne seront pas ajout\u00E9es.
switchUser_enabled_info=Les configurations requises pour utiliser la fonctionnalit\u00E9 d''usurpation d''utilisateur ont \u00E9t\u00E9 ajout\u00E9es pour le client \"{0}\" dans le realm {1}
duplicate_clientId_error=Il n''est pas possible de cr\u00E9er deux clients avec le m\u00EAme clientId {0}. Veuillez vous assurer d''utiliser des valeurs diff\u00E9rentes
invalid_adminClient_error=Il n''y a aucun client avec le clientId {0} sur le realm {1}

#APP_POSTING
AAP_READY_STYLE=default
AAP_READY=Pret pour imputation
AAP_MISSING_PO_STYLE=status_red
AAP_MISSING_PO=Imput. - cde manq
AAP_UNBALANCED_STYLE=status_red
AAP_UNBALANCED=Imput. - fact. d\u00E9s\u00E9quil.
AAP_MISSING_RCP_STYLE=status_red
AAP_MISSING_RCP=Imput. - r\u00E9cep. manq.Imput. - r\u00E9cep. manq.
AAP_MISSING_REF_STYLE=status_red
AAP_MISSING_REF=Imput. - r\u00E9f\u00E9r. manq
AAP_IN_PROGRESS_STYLE=status_yellow
AAP_IN_PROGRESS=Imput. en cours
AAP_MANUAL_MODE_STYLE=status_yellow
AAP_MANUAL_MODE=Imput. mode manuel
AAP_COMPLETED_STYLE=status_green
AAP_COMPLETED=Imput. compl\u00E9te
AAP_TRANSMITTED_STYLE=status_green
AAP_TRANSMITTED=Imput. transmise
AAP_ERROR_STYLE=status_red
AAP_ERROR=Imput. en erreur
AAP_NA_STYLE=info
AAP_NA=Imput. sans objet
AAP_MISSING_AN_REF_STYLE=status_red
AAP_IN_PROGRESS_AN_STYLE=status_yellow
AAP_COMPLETED_AN_STYLE=status_green
AAP_MISSING_AN_REF=Imput. an. -refer. manq
AAP_IN_PROGRESS_AN=Imput. an. en cours
AAP_COMPLETED_AN=Imput. an. complete
AAP_EXPORTED_STYLE=status_deep_blue
AAP_EXPORTED=Imput. export\u00E9e

AAP_EXPORTED_TIMELINE=Imputation comptable : exportation des \u00E9critures

MISSING_PROCESSINGWAY_STYLE=status_red
MISSING_PROCESSINGWAY=processingWay absent

ap_automation_tab=AP automation
ap_automation_aged_balance=Balance \u00E2g\u00E9e
ap_automation_aged_balance_setting=Affichage
ap_automation_reconciliation_management=Rapprochement
ap_automation_reconciliation_management_scenario=Gestion / Sc\u00E9narios

reminderNb = Nombre de jours avant relance
channel_coded_message = Message crypt\u00E9
CORRECT = D\u00E9mat\u00E9rialis\u00E9e
portal_general = G\u00E9n\u00E9ral
UNDEFINED = Non d\u00E9mat\u00E9rialis\u00E9e
channel_MDN = MDN
templateUri = Template de mail

OCR_TO_VERIFY=\u00C0 vid\u00E9ocoder
OCR_TO_VERIFY_STYLE=default
OCR_POSTPONED=Vid\u00E9ocodage suspendu
OCR_POSTPONED_STYLE=status_orange
OCR_VERIFYING=En cours de vid\u00E9ocodage
OCR_VERIFYING_STYLE=status_yellow