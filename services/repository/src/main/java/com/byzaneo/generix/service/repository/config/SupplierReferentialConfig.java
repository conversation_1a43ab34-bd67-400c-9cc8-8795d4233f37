/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.service.repository.config;

import com.byzaneo.generix.service.repository.bean.aap.enums.SupplierReferentialOrigin;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for supplier referential settings
 */
@Configuration
public class SupplierReferentialConfig {

    @Value("${supplier.referential.origin:PARTNER_DIRECTORY}")
    private String supplierReferentialOriginValue;

    /**
     * Get the configured supplier referential origin
     * 
     * @return The supplier referential origin enum value
     */
    public SupplierReferentialOrigin getSupplierReferentialOrigin() {
        try {
            return SupplierReferentialOrigin.valueOf(supplierReferentialOriginValue);
        } catch (IllegalArgumentException e) {
            // Default to partner directory if invalid value
            return SupplierReferentialOrigin.PARTNER_DIRECTORY;
        }
    }
}
