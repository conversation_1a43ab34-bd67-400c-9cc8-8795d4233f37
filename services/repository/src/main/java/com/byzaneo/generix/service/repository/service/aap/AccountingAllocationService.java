/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.service.repository.service.aap;

import com.byzaneo.generix.service.repository.bean.aap.AapRefSupplier;
import com.byzaneo.generix.service.repository.bean.aap.enums.SupplierReferentialOrigin;

/**
 * Service for performing accounting allocations
 */
public interface AccountingAllocationService {

    String SERVICE_NAME = "gnxAccountingAllocationService";

    /**
     * Find supplier accounting reference based on invoice data
     * 
     * @param supplierCode The supplier code (invoice.sellerPartyID)
     * @param recipientCode The recipient code (invoice.to)
     * @param clientCode The client environment code (owner)
     * @param origin The supplier referential origin
     * @return The supplier accounting reference or null if not found
     */
    AapRefSupplier findSupplierAccountingReference(
            String supplierCode, 
            String recipientCode, 
            String clientCode, 
            SupplierReferentialOrigin origin);
}
