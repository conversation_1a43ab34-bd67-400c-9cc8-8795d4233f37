/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.service.repository.repository;

import com.byzaneo.generix.service.repository.bean.aap.AapRefSupplier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface AapRefSupplierRepository extends JpaRepository<AapRefSupplier, Long> {
    
    /**
     * Find supplier reference by address ID
     * @param addressId The address ID from Address collection
     * @return The supplier accounting reference or null if not found
     */
    AapRefSupplier findByAddressId(String addressId);
    
    /**
     * Find supplier reference by legal entity ID and demat partner ID
     * @param legalEntityId The legal entity ID
     * @param dematPartnerId The demat partner ID
     * @return The supplier accounting reference or null if not found
     */
    @Query("SELECT s FROM AapRefSupplier s WHERE s.legalEntityId = :legalEntityId AND s.dematPartnerId = :dematPartnerId")
    AapRefSupplier findByLegalEntityIdAndDematPartnerId(
            @Param("legalEntityId") String legalEntityId, 
            @Param("dematPartnerId") String dematPartnerId);
}