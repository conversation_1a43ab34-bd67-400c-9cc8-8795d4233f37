/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.service.repository.service.address;

import com.byzaneo.generix.service.repository.bean.address.Address;
import com.byzaneo.generix.service.repository.bean.address.TypeAddressEnum;

/**
 * Service for searching addresses based on various criteria
 */
public interface AddressSearchService {

    String SERVICE_NAME = "gnxAddressSearchService";

    /**
     * Find address by identification, owner, type and partner code
     * 
     * @param identification The supplier identification code
     * @param owner The environment client code
     * @param typeAddress The type of address
     * @param partnerCode The partner code (recipient of invoice)
     * @return The matching address or null if not found
     */
    Address findAddressByIdentificationAndOwnerAndTypeAndPartnerCode(
            String identification, 
            String owner, 
            TypeAddressEnum typeAddress, 
            String partnerCode);
}
