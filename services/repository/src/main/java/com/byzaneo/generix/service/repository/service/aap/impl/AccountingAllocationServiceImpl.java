/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.service.repository.service.aap.impl;

import com.byzaneo.generix.service.repository.bean.aap.AapRefSupplier;
import com.byzaneo.generix.service.repository.bean.aap.enums.SupplierReferentialOrigin;
import com.byzaneo.generix.service.repository.bean.address.Address;
import com.byzaneo.generix.service.repository.bean.address.TypeAddressEnum;
import com.byzaneo.generix.service.repository.repository.AapRefSupplierRepository;
import com.byzaneo.generix.service.repository.service.aap.AccountingAllocationService;
import com.byzaneo.generix.service.repository.service.address.AddressSearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

@Service(AccountingAllocationService.SERVICE_NAME)
public class AccountingAllocationServiceImpl implements AccountingAllocationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountingAllocationServiceImpl.class);

    @Inject
    private AddressSearchService addressSearchService;

    @Inject
    private AapRefSupplierRepository aapRefSupplierRepository;

    @Override
    public AapRefSupplier findSupplierAccountingReference(
            String supplierCode, 
            String recipientCode, 
            String clientCode, 
            SupplierReferentialOrigin origin) {
        
        if (origin == SupplierReferentialOrigin.ADDRESS_REPOSITORY) {
            return findSupplierAccountingReferenceFromAddressRepository(
                    supplierCode, recipientCode, clientCode);
        } else {
            // Default behavior (current implementation using partner directory)
            return findSupplierAccountingReferenceFromPartnerDirectory(
                    supplierCode, recipientCode, clientCode);
        }
    }

    /**
     * Find supplier accounting reference using the address repository
     */
    private AapRefSupplier findSupplierAccountingReferenceFromAddressRepository(
            String supplierCode, String recipientCode, String clientCode) {
        
        LOGGER.debug("Finding supplier accounting reference from address repository for supplier: {}, recipient: {}, client: {}", 
                supplierCode, recipientCode, clientCode);
        
        // Find address by identification (supplierCode), owner (clientCode), 
        // type (SELLER) and partnerCode (recipientCode)
        Address address = addressSearchService.findAddressByIdentificationAndOwnerAndTypeAndPartnerCode(
                supplierCode, clientCode, TypeAddressEnum.SELLER, recipientCode);
        
        if (address == null) {
            LOGGER.debug("No matching address found for supplier: {}", supplierCode);
            return null;
        }
        
        // Use the address ID to find the corresponding supplier reference
        AapRefSupplier supplier = aapRefSupplierRepository.findByAddressId(address.getId());
        
        if (supplier == null) {
            LOGGER.debug("No supplier accounting reference found for address ID: {}", address.getId());
        } else {
            LOGGER.debug("Found supplier accounting reference: {}", supplier.getId());
        }
        
        return supplier;
    }

    /**
     * Find supplier accounting reference using the partner directory (existing approach)
     * This is a placeholder for the current implementation
     */
    private AapRefSupplier findSupplierAccountingReferenceFromPartnerDirectory(
            String supplierCode, String recipientCode, String clientCode) {
        
        LOGGER.debug("Finding supplier accounting reference from partner directory for supplier: {}, recipient: {}, client: {}", 
                supplierCode, recipientCode, clientCode);
        
        // Current implementation would use legalEntityId and dematPartnerId to search
        // This would typically involve a query that's specific to the current implementation
        // Placeholder implementation
        
        return null; // Replace with actual implementation
    }
}
