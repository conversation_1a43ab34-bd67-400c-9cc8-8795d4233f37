package com.byzaneo.generix.service.repository.service.aap;

import com.byzaneo.angular.service.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.repository.bean.Product;
import com.byzaneo.generix.service.repository.bean.aap.*;
import com.byzaneo.generix.service.repository.bean.aap.enums.AutomaticCounterpartStatus;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.security.util.PrincipalHelper;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.IndexableDocument;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.*;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.datatools.connectivity.oda.design.SortDirectionType;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.dao.mongo.PageRequest.of;
import static com.byzaneo.generix.service.repository.bean.aap.AapInvActPosting.IAPStatus.AAP_COMPLETED;
import static com.byzaneo.generix.service.repository.bean.aap.AapInvActPosting.IAPStatus.AAP_IN_PROGRESS;
import static com.byzaneo.generix.service.repository.bean.aap.AapInvActPosting.IAPStatus.AAP_TO_BE_VERIFIED;
import static com.byzaneo.generix.service.repository.bean.aap.AapInvActPosting.ItemType.L;
import static com.byzaneo.generix.service.repository.bean.aap.AapInvActPosting.ItemType.T;
import static com.byzaneo.generix.service.repository.bean.aap.AapInvActPosting.ItemType.V;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.notEqual;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.query.util.QueryHelper.toQuery;
import static com.byzaneo.xtrade.api.DocumentStatus.AAP_UNBALANCED;
import static com.byzaneo.xtrade.api.IndexableDocument.FIELD_CREATION_DATE;
import static com.byzaneo.xtrade.api.IndexableDocument.FIELD_OWNERS;
import static com.byzaneo.xtrade.api.IndexableDocument.FIELD_STATUS;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.AAP_ALREADY_COMPLETED;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.AAP_COMPLETED_AUTOMATICALLY;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.AAP_MANUAL_MODE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.AAP_MISSING_PO;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.AAP_MISSING_RCP;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.AAP_MISSING_REF;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.AAP_PARTIAL;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.AAP_TRANSMITTED;
import static com.byzaneo.xtrade.bean.DocumentTimeline.USER_SYSTEM;
import static java.util.Comparator.comparing;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsLast;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.capitalize;
import static org.apache.logging.log4j.util.Strings.isBlank;
import static org.slf4j.LoggerFactory.getLogger;

@Service(AapService.SERVICE_NAME)
public class AapServiceImpl implements AapService {

    public static final String PRODUCT = "PRODUCT";

    private static final Logger logger = getLogger(AapService.class);

    @Autowired
    @Qualifier(AccountService.SERVICE_NAME)
    private AccountService accountService;


    @Autowired
    @Qualifier(DocumentService.SERVICE_NAME)
    private DocumentService documentService;

    @Autowired
    @Qualifier(SecurityService.SERVICE_NAME)
    private SecurityService securityService;

    @Autowired
    private AapInvActPostingService aapInvActPostingService;

    @Autowired
    private AccountingPostingService accountingPostingService;

    @Autowired
    private AccountingPostingScenarioService accountingPostingScenarioService;

    @Autowired
    private AapRefSupplierService aapRefSupplierService;

    @Autowired
    private AapReferentialService aapReferentialService;

    @Autowired
    private AapRefProductService  aapRefProductService;

    @Autowired
    private AapRefTaxService  aapRefTaxService;

    @Autowired
    private AapAccountingEntriesService aapAccountingEntriesService;

    @Autowired
    @Qualifier(InstanceService.SERVICE_NAME)
    private InstanceService instanceService;



    @Override
    @Transactional
    public Page<Product> getProducts(String login, String companyCode, String legalEntity, Map<String, String> sortedLegalEntities, String bql, String flowDirection, String sortBy, Sort.Direction order, int offset, int limit, boolean isCountEnabled, TechnicalUser technicalUser) {
        final QueryBuilder qbuilder = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, StringUtils.isEmpty(legalEntity) ? sortedLegalEntities.keySet() : List.of(legalEntity), parse(bql),
                IndexableDocument.FIELD_FROM,
                IndexableDocument.FIELD_FROM, IndexableDocument.FIELD_TO, IndexableDocument.FIELD_THIRD_PARTY, flowDirection, sortBy, order != null ? order.name() : Sort.Direction.ASC.name());

        User user = accountService.getUserByLoginOrOpenID(login);
        if (user != null)
            addUserAndRoleInvoiceBqlQueryToTheSearch(qbuilder, user);
        else
            qbuilder.append(securityService.createQueryFromTechnicalUserRole(technicalUser));
        final com.byzaneo.query.Query qb = qbuilder.query();
        final PageRequest pageable = getPageRequest(offset, limit, isCountEnabled, sortBy, order);
        return this.documentService.searchIndexables(Product.class, qb, pageable);
    }

    @Transactional
    @Override
    public Page<AccountingPostingScenario> getAccountingPostingScenarios(String login, String legalEntity,
                                                                         Map<String, String> sortedLegalEntities, String bql, String flowDirection, String sortBy, Sort.Direction order, int offset, int limit,
                                                                         boolean isCountEnabled, TechnicalUser technicalUser) {
        final Query qb = getQuery(login, legalEntity, sortedLegalEntities, bql, flowDirection, technicalUser);

        final PageRequest pageable = getPageRequest(offset, limit, isCountEnabled, sortBy, order);

        return accountingPostingScenarioService.getAccountingPostingScenarios(qb, pageable);
    }

    @Override
    @Transactional
    public Page<AccountingPostingScenario> getAccountingPostingScenarios(Query query, Pageable pageable) {
        return accountingPostingScenarioService.getAccountingPostingScenarios(query, pageable);
    }

    @Override
    @Transactional
    public List<AccountingPostingScenario> getAccountingPostingScenarios(Query query) {
        return accountingPostingScenarioService.getAccountingPostingScenarios(query);
    }

    private Query getQuery(String login, String legalEntity, Map<String, String> sortedLegalEntities, String bql, String flowDirection, TechnicalUser technicalUser) {
        User user = accountService.getUserByLoginOrOpenID(login);

        String owner = PrincipalHelper.isPartnerUser(user) ? user.getPrimaryGroup().getParent().getCode()
                : user.getPrimaryGroup().getCode();
        final QueryBuilder qbuilder = OrganizationHelper.resolveUserOrganizationsQueryForSql(owner, StringUtils.isEmpty(
                        legalEntity) ? sortedLegalEntities.keySet() : List.of(legalEntity), parse(bql),
                "owner",
                "from", "to", flowDirection);

        if (user != null)
            addUserAndRoleInvoiceBqlQueryToTheSearch(qbuilder, user);
        else
            qbuilder.append(securityService.createQueryFromTechnicalUserRole(technicalUser));
        final Query qb = qbuilder.query();
        return qb;
    }


    // TODO to discuss because this is not related to aap
    public void addUserAndRoleInvoiceBqlQueryToTheSearch(QueryBuilder qb, User user) {
        qb.append(securityService.createQueryFromUserRoles(user));
        qb.append(user.getBqlInvQuery());
    }
    // TODO to discuss because this is not related to aap
    public Instance getInstance(HttpServletRequest request) {
        Host host = this.instanceService.resolveHost(request.getServerName());
        if (Objects.isNull(host)) throw new RestJwtException("Host not found", ExceptionType.NOT_FOUND);
        Instance instance = this.instanceService.resolveInstance(host);
        return instance;
    }

    @Override
    @Transactional
    public Page<AapAccountingEntry> getAccountingEntries(Query qb, Pageable pageable) {
       return aapAccountingEntriesService.getAccountingEntries(qb, pageable);
    }

    @Override
    @Transactional
    public List<AapAccountingEntry> getAccountingEntries(Query query) {
        return aapAccountingEntriesService.getAccountingEntries(query);
    }

    @Override
    @Transactional
    public long countAccountingEntries(Query query) {
        return aapAccountingEntriesService.countAccountingEntries(query);
    }

    @Override
    @Transactional
    public AapAccountingEntry saveAccountingEntry(AapAccountingEntry entry) {
        return aapAccountingEntriesService.saveAccountingEntry(entry);
    }

    // TODO to discuss because this is not related to aap
    private static PageRequest getPageRequest(int offset, int limit, boolean isCountEnabled, String sortBy, Sort.Direction order) {
        final PageRequest pageable = sortBy != null && order != null ? of(
                Math.max(offset, 0),
                limit < 1 ? -1 : limit, Sort.by(order, sortBy))
            :of(
                Math.max(offset, 0),
                limit < 1 ? -1 : limit);

        pageable.setCount(isCountEnabled);
        pageable.setFromAngular(true);
        return pageable;
    }

    @Override
    @Transactional
    public AapInvActPosting v1getAapInvActPostingByUuid(String uuid) {
        return aapInvActPostingService.v1getAapInvActPostingByUuid(uuid);
    }

    @Override
    @Transactional
    public AapInvActPosting searchAapInvActPostingSingleResult(Query query) {
        return aapInvActPostingService.searchSingleResult(query);
    }

    @Override
    @Transactional
    public AccountingPostingScenario v1GetAccountingPostingScenarioById(Long id) {
        return accountingPostingScenarioService.getAccountingPostingScenarioById(id);
    }

    @Override
    @Transactional
    public long v1GetAccountingPostingScenarioCount(String login, String legalEntity, Map<String, String> sortedLegalEntities, String bql,
                                                    String flowDirection,
                                                    TechnicalUser technicalUser) {
        final Query qb = getQuery(login, legalEntity, sortedLegalEntities, bql, flowDirection, technicalUser);
        return accountingPostingScenarioService.countAccountingPostingScenarios(qb);
    }

    @Override
    @Transactional
    public AccountingPosting v1getAccountingPosting(Query query) {
        return accountingPostingService.getAccountingPosting(query);
    }

    @Override
    @Transactional
    public AccountingPosting v1getAccountingPostingById(long id) {
        return accountingPostingService.getAccountingPostingById(id);
    }

    @Override
    @Transactional
    public AccountingPosting getAccountingPostingByPartnerId(String partnerId) {
        return accountingPostingService.getAccountingPostingByPartnerId(partnerId);
    }

    @Override
    public Map<String, AccountingPosting> fetchAccountingPostings(List<String> partnerIds){
        return accountingPostingService.fetchAccountingPostings(partnerIds);
    }


    @Override
    @Transactional
    public AapReferential getAapReferentialById(long id) {
        Query query = QueryBuilder.createBuilder().and(equal("id", id)).query();
        return aapReferentialService.getAapReferential(query);
    }

    @Override
    @Transactional
    public AapReferential getAapReferential(Query query) {
        return aapReferentialService.getAapReferential(query);

    }

    @Override
    @Transactional
    public Page<AapReferential> getReferentialPage(Query query, Pageable pageable) {
        return aapReferentialService.search(query, pageable);
    }

    @Override
    @Transactional
    public Page<AapRefSupplier> getAapRefSuppliers(Map<String, Object> criteriaMap, Pageable pageable) {
        QueryBuilder qb = QueryBuilder.createBuilder();
        for (Map.Entry<String, Object> entry : criteriaMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            qb.and(equal(key, value));
        }
        return aapRefSupplierService.getAapRefSuppliers(qb.query(), pageable);
    }


    @Override
    @Transactional
    public AapInvActPosting saveAapInvActPosting(AapInvActPosting aapInvActPosting) {
        return aapInvActPostingService.saveAapInvActPosting(aapInvActPosting);
    }

    @Override
    @Transactional
    public AapInvActPosting updateApInvActPosting(AapInvActPosting aapInvActPosting) {
        return aapInvActPostingService.updateApInvActPosting(aapInvActPosting);
    }

    @Override
    public AccountingPostingScenario updateAccountingPostingScenario(AccountingPostingScenario accountingPostingScenario) {
        return accountingPostingScenarioService.updateAccountingPostingScenario(accountingPostingScenario);
    }

    @Override
    public AccountingPosting saveAccountingPosting(AccountingPosting accountingPosting) {
        return accountingPostingService.saveAccountingPosting(accountingPosting);
    }

    @Override
    @Transactional
    public AccountingPostingScenario saveAccountingPostingScenario(AccountingPostingScenario accountingPostingScenario) {
        return accountingPostingScenarioService.saveAccountingPostingScenario(accountingPostingScenario);
    }

    @Override
    @Transactional
    public List<AccountingPostingScenario> saveAccountingPostingScenarios(List<AccountingPostingScenario> accountingPostingScenarios) {
        return accountingPostingScenarioService.saveAccountingPostingScenarios(accountingPostingScenarios);
    }

    @Override
    @Transactional
    public void deleteAccountingPostingScenario(Long id) {
         accountingPostingScenarioService.deleteAccountingPostingScenario(id);
    }

    @Override
    @Transactional
    public AapRefProduct getAapRefProduct(Query query) {
        return aapRefProductService.getAapRefProduct(query);
    }

    @Override
    @Transactional
    public AapRefTax getAapRefTAx(Query query) {
        return aapRefTaxService.getAapRefTAx(query);
    }

    private static void addSort(String sortBy, String order, QueryBuilder qb) {
        if (order != null && sortBy != null) {
            if(SortDirectionType.ASCENDING.name().startsWith(order)){
                qb.asc(sortBy);
            } else {
                qb.desc(sortBy);
            }
        }
    }


    @Override
    @Transactional
    public AapRefProduct saveAapRefProduct(AapRefProduct aapRefProduct) {
        return aapRefProductService.saveAapRefProduct(aapRefProduct);
    }

    @Override
    @Transactional
    public AapRefSupplier saveAapRefSupplier(AapRefSupplier aapRefSupplier) {
        return aapRefSupplierService.saveAapRefSupplier(aapRefSupplier);
    }

    @Override
    @Transactional
    public AapRefTax saveAapRefTax(AapRefTax aapRefTax) {
        return aapRefTaxService.saveAapRefTax(aapRefTax);
    }

    @Override
    @Transactional
    public List<AapReferential> getReferentialList(Query query) {
        return aapReferentialService.search(query);
    }

    @Override
    @Transactional
    public List<AapRefSupplier> getAapRefSuppliers(Query query) {
        return aapRefSupplierService.getAapRefSuppliers(query);
    }

    @Override
    @Transactional
    public Page<AapRefSupplier> getAapRefSuppliers(Query query, Pageable pageable) {
        return aapRefSupplierService.getAapRefSuppliers(query, pageable);
    }

    @Override
    @Transactional
    public AapRefSupplier getAapRefSupplier(Query query) {
        return (AapRefSupplier) aapRefSupplierService.getAapRefSupplier(query);
    }

    @Autowired
    @Qualifier(ReconciliationService.SERVICE_NAME)
    protected transient ReconciliationService reconciliationService;



    @Autowired
    @Qualifier(AapService.SERVICE_NAME)
    protected transient AapService aapService;


    @Override
    public AccountingPostingScenario findAppropriateScenario(InvoiceIndex invoice, List<AccountingPostingScenario> scenarios) {
        List<AccountingPostingScenario> sortedScenarios = new ArrayList<>(scenarios);
        sortedScenarios.sort(comparing(AccountingPostingScenario::getTo, nullsLast(naturalOrder())));
        for (AccountingPostingScenario scenario : sortedScenarios) {
            try {
                String partnerCode = scenario.getTo();
                if (partnerCode == null || partnerCode.equals(invoice.getTo())) {
                    Query query = toQuery(scenario.getBqlFilterQuery());
                    List<InvoiceIndex> invoiceList = reconciliationService.searchIndexes(query, InvoiceIndex.class, List.of(invoice));
                    if (isNotEmpty(invoiceList)) {
                        logger.info("Found a scenario for the invoice number {} : bqlFilter ({}), to ({}), name ({})", invoice.getInvoiceNumber(),
                                scenario.getBqlFilterQuery(), partnerCode, scenario.getName());
                        return scenario;
                    }
                }
            } catch (Exception e) {
                logger.error("Invalid bql for the scenario {}. Error: {}", scenario.getName(), e);
            }
        }
        return null;
    }

    @Override
    public List<AccountingPostingScenario> findScenariosByOwner(String owner) {
        if (isBlank(owner)) {
            return null;
        }
        QueryBuilder qb = createBuilder();
        qb.and(equal("owner", owner));
        qb.and(equal("enable", true));
        qb.asc(FIELD_CREATION_DATE);
        return aapService.getAccountingPostingScenarios(qb.query());
    }

    @Override
    public void imputeDocument(Document document, InvoiceIndex invoice, AccountingPostingScenario scenario, List<DocumentTimeline> timeline,
                               String invoiceItemCodedField) {
        AapInvActPosting aapInvActPosting = getAapInvActPosting(document);
        AccountingPosting accountingPosting = aapService.getAccountingPostingByPartnerId(invoice.getTo());
        if (accountingPosting == null) {
            updateDocumentAndTimeline(document,
                new DocumentStatusEntity(scenario.getMissingAccountingReferential()), AAP_MISSING_REF, timeline,
                "accounting referential not found", invoice.getInvoiceNumber());
            return;
        }
        aapInvActPosting.setJournal(accountingPosting.getPurchaseJournalCode());
        aapInvActPosting.setOtherLabel(getEntryOtherLabel(invoice.getInvoiceTypeCoded()
            .name(), invoice.getSellerPartyName()));
        if (!AAP_IN_PROGRESS.equals(aapInvActPosting.getIapStatus())) {
            updateDocumentAndTimeline(document, null, AAP_ALREADY_COMPLETED, timeline, "imputation already completed",
                invoice.getInvoiceNumber());
            return;
        }
        AccountingPostingScenario.AccountingPostingSenarioParameter aapScenarioParameter = scenario.getAccountingPostingSenarioParameter();
        if (AccountingPostingScenario.AccountingPostingSenarioParameter.WITHOUT_ORDER.equals(aapScenarioParameter)) {
            if (scenario.isCompleteWithAccountingRef()) {
                useAutomaticImputation(document, scenario, timeline, invoice, aapInvActPosting, invoiceItemCodedField);
            }
            else {
                updateDocumentAndTimeline(document,
                    new DocumentStatusEntity(scenario.getImputationInManualModeStatus()), AAP_MANUAL_MODE, timeline,
                    "forced accounting posting in manual mode", invoice.getInvoiceNumber());
            }
        } else {
            List<DocumentStatus> excludeOrdersStatus = scenario.getExcludedorders();
            OrderIndex order = searchIndexable(OrderIndex.class, invoice.getBuyerOrderNumber(), invoice.getSellerPartyID(), invoice.getOwners(),
                    excludeOrdersStatus);
            if (order != null) {
                if (AccountingPostingScenario.AccountingPostingSenarioParameter.ORDER_AND_RECEPTION.equals(aapScenarioParameter)) {
                    List<DocumentStatus> excludeReceptionsStatus = scenario.getExcludedreceptions();
                    ReceptionIndex reception = searchIndexable(ReceptionIndex.class, invoice.getBuyerOrderNumber(), invoice.getSellerPartyID(),
                            invoice.getOwners(), excludeReceptionsStatus);
                    if (reception != null) {
                        useAutomaticImputation(document, scenario, timeline, invoice, aapInvActPosting, invoiceItemCodedField);
                    } else {
                        updateDocumentAndTimeline(document, new DocumentStatusEntity(scenario.getMissingReceptionStatus()), AAP_MISSING_RCP,
                            timeline, "reception not found",
                            invoice.getInvoiceNumber());
                    }
                } else {
                    useAutomaticImputation(document, scenario, timeline, invoice, aapInvActPosting, invoiceItemCodedField);
                }
            } else {
                updateDocumentAndTimeline(document, new DocumentStatusEntity(scenario.getMissingOrderStatus()), AAP_MISSING_PO, timeline,
                    "order not found",
                    invoice.getInvoiceNumber());
            }
        }
    }

    public String getEntryOtherLabel(String invoiceTypeCoded, String sellerPartyName) {
        if (List.of("CREDIT_INVOICE", "CREDIT_MEMO", "CREDIT_NOTE_FINANCIAL_ADJUSTMENT", "CREDIT_NOTE_GOODS_AND_SERVICES", "FACTORED_CREDIT_NOTE").contains(invoiceTypeCoded)) {
            return "Avoir fournisseur ".concat(sellerPartyName);
        } else {
            return "Facture fournisseur ".concat(sellerPartyName);
        }
    }

    @Override
    public void updateDocumentAndTimeline(Document document, DocumentStatusEntityInterface status, DocumentTimeline.TimelineAction action,
                                          List<DocumentTimeline> timeline, String message, String invoiceNumber) {
        document.setStatus(Optional.ofNullable(status)
                .orElse(document.getStatus()));
        if (!isBlank(invoiceNumber)) {
            message = String.format("Invoice number %s: %s", invoiceNumber, message);
        }
        logger.info(message);
        timeline.add(new DocumentTimeline(document, new Date(), action, USER_SYSTEM));
    }

    private <I extends Indexable> I searchIndexable(Class<I> indexable, String buyerOrderNumber, String sellerPartyId, String owner,
                                                    List<DocumentStatus> excluded) {
        if (isBlank(buyerOrderNumber) || isBlank(sellerPartyId) || isBlank(owner)) {
            return null;
        }
        QueryBuilder qb = createBuilder();
        qb.and(equal("buyerOrderNumber", buyerOrderNumber))
                .and(equal("sellerPartyID", sellerPartyId))
                .and(equal(FIELD_OWNERS, owner));
        if (excluded != null) {
            excluded.forEach(e -> qb.and(notEqual(FIELD_STATUS, e)));
        }

        return documentService.searchIndexable(indexable, qb.query());
    }

    private void useAutomaticImputation(Document document, AccountingPostingScenario scenario, List<DocumentTimeline> timeline,
                                        InvoiceIndex invoice, AapInvActPosting aapInvActPosting, String invoiceItemCodedField) {
        if (scenario.isCompleteWithAccountingRef()) {
            Partner partner = securityService.getOrganizationByCode(document.getTo(), document.getOwners());
            String accountPostingId = Optional.ofNullable(partner)
                    .map(Partner::getAccountPostingId)
                    .orElse(null);

            if (accountPostingId == null || aapService.v1getAccountingPostingById(Long.valueOf(accountPostingId)) == null) {
                updateDocumentAndTimeline(document, new DocumentStatusEntity(scenario.getMissingAccountingReferential()), AAP_MISSING_REF,
                    timeline,
                    "accounting referential not found", invoice.getInvoiceNumber());
                return;
            }
        }
        List<AapItem> items = new ArrayList<>(aapInvActPosting.getItems());
        boolean invoiceToBeVerified = false;
        boolean invoiceUnbalanced = false;
        boolean invoiceCompleted = parseDetailLines(invoice, items, scenario.isCompleteWithAccountingRef(),
                invoiceItemCodedField, aapInvActPosting);
        invoiceCompleted = parseVatBreakdown(invoice, items, scenario.isCompleteWithAccountingRef(), aapInvActPosting) && invoiceCompleted;
        invoiceCompleted = parseTotalIncVat(invoice, items, scenario.isCompleteWithAccountingRef(), aapInvActPosting) && invoiceCompleted;
        invoiceCompleted = checkBalancedInvoice(invoice,items) && invoiceCompleted;
        invoiceToBeVerified = DocumentStatus.AAP_TO_BE_VERIFIED.equals(invoice.getStatusAsEnumValue());
        invoiceUnbalanced = AAP_UNBALANCED.equals(invoice.getStatusAsEnumValue());
        if(invoiceUnbalanced){
            logger.info("setting timeline status to UNBALANCED");
            updateDocumentAndTimeline(document, new DocumentStatusEntity(AAP_UNBALANCED),
                DocumentTimeline.TimelineAction.AAP_UNBALANCED, timeline,
                "accounting posting unbalanced", invoice.getInvoiceNumber());
            return;
        }
        else if (invoiceCompleted && !invoiceToBeVerified) {
            aapInvActPosting.setIapStatus(AAP_COMPLETED);
            aapInvActPosting.setUserId(USER_SYSTEM);
            if (isNotEmpty(items)) {
                //save only if it has at least one item
                aapInvActPosting.setItems(items);
            }
            aapInvActPosting = aapService.saveAapInvActPosting(aapInvActPosting);
            updateDocumentAndTimeline(document, new DocumentStatusEntity(scenario.getCompletedImputationStatus()),
                AAP_COMPLETED_AUTOMATICALLY, timeline,
                "accounting posting completed", invoice.getInvoiceNumber());
            if (DocumentStatus.AAP_COMPLETED.equals(document.getStatusAsEnumValue())) {
                aapAccountingEntriesService.saveAapAccountingEntries(USER_SYSTEM, aapInvActPosting, invoice);
                updateDocumentAndTimeline(document, null, AAP_TRANSMITTED, timeline,
                        "accounting posting transmitted", invoice.getInvoiceNumber());
            }
            return;
        }else if(invoiceToBeVerified){
            aapInvActPosting.setIapStatus(AAP_TO_BE_VERIFIED);
            if (isNotEmpty(items)) {
                aapInvActPosting.setItems(items);
            }
            aapInvActPosting = aapService.saveAapInvActPosting(aapInvActPosting);
            logger.info("setting IapStatus to APP_TO_BE_VERIFIED");
            updateDocumentAndTimeline(document, new DocumentStatusEntity(DocumentStatus.AAP_TO_BE_VERIFIED),
                AAP_MANUAL_MODE, timeline,
                "accounting posting to be verified", invoice.getInvoiceNumber());
            return;
        }

        if (isNotEmpty(items)) {
            //save only if it has at least one item
            aapInvActPosting.setItems(items);
            aapService.saveAapInvActPosting(aapInvActPosting);
        }

        updateDocumentAndTimeline(document, new DocumentStatusEntity(scenario.getPartialImputationStatus()), AAP_PARTIAL, timeline,
            "accounting posting partially completed", invoice.getInvoiceNumber());
    }

    private boolean parseTotalIncVat(InvoiceIndex invoice, List<AapItem> items, boolean completeWithAccountingRef, AapInvActPosting aapInvActPosting) {
        AapItem totalIncVat = items.stream()
                .filter(item -> T.equals(item.getItemType()) && item.getItemSubId() == 1)
                .findFirst()
                .orElse(null);
        if (totalIncVat == null) {
            if (!completeWithAccountingRef)
                return false;
            Company company = securityService.getCompanyByCode(invoice.getOwners());
            if (company == null)
                return false;
            Partner legalEntity = securityService.getPartner(invoice.getTo(), company);
            Partner dematPartner = securityService.getPartner(invoice.getSellerPartyID(), company);
            if (legalEntity == null || dematPartner == null)
                return false;
            Query query = new QueryBuilder().and(equal("legalEntityId", legalEntity.getId()), equal("owner", invoice.getOwners()),
                            equal("dematPartnerId", dematPartner.getId()))
                    .query();

            AapRefSupplier aapRefSuppliers = aapService.getAapRefSupplier(query);
            if (aapRefSuppliers != null) {
                totalIncVat = new AapItem();
                totalIncVat.setItemType(T);
                totalIncVat.setItemId("no-item-id");
                totalIncVat.setItemSubId(1);
                totalIncVat.setAmount(invoice.getInvoiceTotalTAC() == null ? invoice.getInvoiceTotal() : invoice.getInvoiceTotalTAC());
                totalIncVat.setAapReferential(aapRefSuppliers.getAapReferential());
                totalIncVat.setAapInvActPosting(aapInvActPosting);
                //totalIncVat.setAapReferentialLineId(aapRefSupplier.getAapReferentialLineId().toString());
                    items.add(totalIncVat);
            }
        }
        return totalIncVat != null && totalIncVat.getAapReferential() != null;
    }

    private boolean parseVatBreakdown(InvoiceIndex invoice, List<AapItem> items, boolean completeWithAccountingRef, AapInvActPosting aapInvActPosting) {
        List<AapItem> vatBreakdowns = items.stream()
                .filter(item -> V.equals(item.getItemType()) && item.getItemSubId() == 1)
                .collect(Collectors.toList());
        boolean allItemsPresent = true;
        for (ValueAddedTax valueAddedTax : invoice.getSummaryBreakdownValueAddedTax()) {
            AapItem item = vatBreakdowns.stream()
                    .filter(i -> i.getItemId()
                            .equals(valueAddedTax.getRate()))
                    .findFirst()
                    .orElse(null);
            if (item == null) {
                if (!completeWithAccountingRef)
                    return false;
                Query query = new QueryBuilder().and(equal("to", invoice.getTo()), equal("owner", invoice.getOwners()),
                                equal("taxTypeCoded", "VAT" + String.valueOf(Double.parseDouble(valueAddedTax.getRate()))
                                        .replace(".", "")))
                        .query();
                AapRefTax aapRefTax = aapService.getAapRefTAx(query);
                if (aapRefTax != null) {
                    item = new AapItem();
                    item.setItemType(V);
                    //item.setId(Long.valueOf(valueAddedTax.getRate()));
                    //item.setId(Double.valueOf(valueAddedTax.getRate()).longValue());
                    item.setItemId(valueAddedTax.getRate());
                    item.setItemSubId(1);
                    item.setAmount(valueAddedTax.getTaxAmountTAC() == null ? valueAddedTax.getTaxAmount() : valueAddedTax.getTaxAmountTAC());
                    item.setAapReferential(aapRefTax.getAapReferential());
                    item.setAapInvActPosting(aapInvActPosting);
                    // find duplicates
                    vatBreakdowns.add(item);
                    items.add(item);
                }
            }
            if (item == null || item.getAapReferential() == null)
                allItemsPresent = false;

        }
        return allItemsPresent;
    }

    private boolean parseDetailLines(InvoiceIndex invoice, List<AapItem> items, boolean completeWithAccountingRef,
        String invoiceItemCodedField, AapInvActPosting aapInvActPosting) {
        List<AapItem> itemDetails = items.stream()
            .filter(item -> L.equals(item.getItemType()) && item.getItemSubId() == 1)
            .collect(Collectors.toList());
        boolean allItemsPresent = true;
        for (InvoiceDetailIndex detail : invoice.getDetails()) {
            AapItem item = itemDetails.stream()
                .filter(i -> i.getItemId()
                    .equals(Integer.toString(detail.getItemLineNumber())))
                .findFirst()
                .orElse(null);
            if (item == null) {
                if (!completeWithAccountingRef)
                    return false;
                item = fillAapItem(item, detail, aapInvActPosting);
                Query query = new QueryBuilder().and(equal("to", invoice.getTo()), equal("owner", invoice.getOwners()),
                        equal("reference", getItemCodeValue(detail, invoiceItemCodedField)))
                    .query();
                AapRefProduct aapRefProduct = aapService.getAapRefProduct(query);
                if (aapRefProduct != null) {
                    //item.setId(Long.valueOf(detail.getItemLineNumber()));
                    item.setAapReferential(aapRefProduct.getAapReferential());
                    // find duplicates
                    itemDetails.add(item);
                    items.add(item);
                }
                else {
                    Company company = securityService.getCompanyByCode(invoice.getOwners());
                    if (company == null)
                        return false;
                    Partner legalEntity = securityService.getPartner(invoice.getTo(), company);
                    Partner dematPartner = securityService.getPartner(invoice.getSellerPartyID(), company);
                    if (legalEntity == null || dematPartner == null)
                        return false;
                    query = new QueryBuilder().and(equal("legalEntityId", legalEntity.getId()), equal("owner", invoice.getOwners()),
                            equal("dematPartnerId", dematPartner.getId()))
                        .query();

                    AapRefSupplier aapRefSuppliers = aapService.getAapRefSupplier(query);
                    if (aapRefSuppliers != null && aapRefSuppliers.getCounterpartAccountId() != null && !AutomaticCounterpartStatus.SUSPENDED.toString().equals(aapRefSuppliers.getAutomaticCounterpart())) {
                        logger.info("using aapRefSuppliers CounterpartAccountId ", aapRefSuppliers.getCounterpartAccountId());
                        AapReferential aapReferential = getAapReferentialById(aapRefSuppliers.getCounterpartAccountId());
                        item.setAapReferential(aapReferential);
                        itemDetails.add(item);
                        items.add(item);
                        if (AutomaticCounterpartStatus.WITH.toString().equals(aapRefSuppliers.getAutomaticCounterpart())) {
                            DocumentStatusEntityInterface documentStatusEntityInterface = new DocumentStatusEntity();
                            documentStatusEntityInterface.setStatusCode(AAP_TO_BE_VERIFIED.toString());
                            invoice.setStatus(documentStatusEntityInterface);
                            this.documentService.saveIndexable(invoice);
                            logger.info("setting document status to AAP_TO_BE_VERIFED");
                        }
                    }
                }
            }
            if (item == null || item.getAapReferential() == null)
                allItemsPresent = false;
        }
        return allItemsPresent;
    }

    private Boolean checkBalancedInvoice(InvoiceIndex invoice,List<AapItem> items){
        TotalAmountItems result = items.stream()
            .collect(
                Collectors.teeing(
                    Collectors.filtering(item -> T.equals(item.getItemType()) && item.getItemSubId() == 1, Collectors.toList()),
                    Collectors.filtering(item -> L.equals(item.getItemType()) && item.getItemSubId() == 1,
                        Collectors.mapping(AapItem::getAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))),
                    (totalItems, totalLignesAmount) -> {
                        Optional<AapItem> totalDebitAmount = totalItems.stream().findFirst();
                        return new TotalAmountItems(totalDebitAmount, totalLignesAmount);
                    }
                )
            );

        BigDecimal invoiceTotalAmount= result.totalDebitAmount.isPresent()? result.totalDebitAmount.get()
            .getAmount() : (invoice.getInvoiceTotalTAC()!= null? invoice.getInvoiceTotalTAC() : invoice.getInvoiceTotal());
        BigDecimal totalLignesWithTaxableAmount = result.totalLignesItems.add(invoice.getTotalTaxAmountTAC()!= null ? invoice.getTotalTaxAmountTAC() : invoice.getTotalTaxAmount());

               if (invoiceTotalAmount != null) {
            //AIO-21138 here to compare both total in case not equal stop process and send AAP_UNBALANCED
            if(invoiceTotalAmount.compareTo(totalLignesWithTaxableAmount)!=0){
                DocumentStatusEntityInterface documentStatusEntityInterface = new DocumentStatusEntity();
                documentStatusEntityInterface.setStatusCode(AAP_UNBALANCED.toString());
                invoice.setStatus(documentStatusEntityInterface);
                this.documentService.saveIndexable(invoice);
                logger.info("setting document status to AAP_UNBALANCED");
                return false;
            }
        }
        return true;
    }

    private AapItem fillAapItem(AapItem item, InvoiceDetailIndex detail, AapInvActPosting aapInvActPosting) {
        item = new AapItem();
        item.setItemType(L);
        item.setItemId(String.valueOf(detail.getItemLineNumber()));
        item.setItemSubId(1);
        item.setAmount(detail.getItemTotalTAC() == null ? detail.getItemTotal() : detail.getItemTotalTAC());
        item.setAapInvActPosting(aapInvActPosting);
        return item;
    }

    private AapInvActPosting getAapInvActPosting(Document document) {

        AapInvActPosting aapInvActPosting = aapService.v1getAapInvActPostingByUuid(document.getUuid());
        if (aapInvActPosting == null) {
            aapInvActPosting = new AapInvActPosting();
            aapInvActPosting.setInvoiceId(document.getUuid());
            aapInvActPosting.setItems(new ArrayList<>());
            aapInvActPosting.setIapStatus(AAP_IN_PROGRESS);
        }
        return aapInvActPosting;
    }

    private Object getItemCodeValue(InvoiceDetailIndex detail, String invoiceItemCodedField) {
        try {
            if (invoiceItemCodedField != null)
                return detail.getClass()
                        .getDeclaredMethod("get" + capitalize(invoiceItemCodedField))
                        .invoke(detail);
        } catch (Exception e) {
            MessageHelper.error("Error when searching the matching between invoice and order or reception", e.getMessage());
        }
        return EMPTY;
    }
    static class TotalAmountItems {
        Optional<AapItem> totalDebitAmount;
        BigDecimal totalLignesItems;

        TotalAmountItems(Optional<AapItem> totalDebitAmount, BigDecimal totalLignesItems) {
            this.totalDebitAmount = totalDebitAmount;
            this.totalLignesItems = totalLignesItems;
        }
    }
}
