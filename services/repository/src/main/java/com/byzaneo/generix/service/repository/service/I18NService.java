package com.byzaneo.generix.service.repository.service;

import com.byzaneo.angular.bean.*;
import com.byzaneo.generix.service.repository.service.translation.*;
import com.byzaneo.security.bean.User;

import java.util.*;

public interface I18NService {
  public static final String BEAN_NAME = "I18NService";
  public List<I18NModule> findAllI18NModules();
  public I18NModule findI18NModuleById(Long id);
  public List<I18NTranslation> findAllI18NTranslations();
  public I18NTranslation findI18NTranslationById(Long id);
  public SearchResult<I18NTranslationDto> findI18NTranslationWithFilter(CriteriaDto criteria);
  public List<I18NTranslation> findI18NTranslationsByLocale(Locale locale);

  String findByCodeAndLocale(String code, Locale locale, final Object... args);

  I18NTranslation editTranslation(UpdateTranslationDto i18NTranslation, User user, Long id);
  public List<Locale> findLanguages();
  public Optional<I18NModule> findI18NModuleByName(String name);
  public List<I18NTranslationDto> getTranslations();
  public List<Locale> getLocales();
}
