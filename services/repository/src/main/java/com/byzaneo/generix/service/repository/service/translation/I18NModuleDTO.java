/*
 * Copyright (c) 2023.
 * created by <PERSON><PERSON>
 */

package com.byzaneo.generix.service.repository.service.translation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement( name = "I18NModule")
public class I18NModuleDTO implements Serializable {
  private Long id;
  private String name;
  private Set<I18NTranslationDto> i18NTranslations;
}