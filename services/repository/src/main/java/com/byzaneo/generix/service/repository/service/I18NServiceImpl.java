package com.byzaneo.generix.service.repository.service;

import com.byzaneo.angular.bean.I18NModule;
import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.angular.dao.I18nModuleDAO;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.angular.service.ExceptionType;
import com.byzaneo.angular.service.RestJwtException;
import com.byzaneo.generix.service.repository.service.translation.*;
import com.byzaneo.generix.service.repository.util.SortOrderDto;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.query.clause.Clause;
import com.byzaneo.query.clause.ClausePrecedence;
import com.byzaneo.query.clause.OrClause;
import com.byzaneo.security.bean.User;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.function.TriFunction;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.*;
import java.util.function.BiFunction;

import static org.apache.commons.lang3.ArrayUtils.isEmpty;

@Service(I18NService.BEAN_NAME)
public class I18NServiceImpl implements I18NService {

  public static final int MAX_PAGE_SIZE = 10000;

  public static final Set I18TRANSLATION_DATE_COLUMNS = Set.of("newValueChangedAt", "defaultValueChangedAt");
  @Autowired
  private I18nModuleDAO i18nModuleDAO;

  @Autowired
  private I18nTranslationDAO i18nTranslationDAO;

  @Autowired
  private I18NService i18NService;

  @Override
  @Transactional
  public List<I18NModule> findAllI18NModules() {
    return i18nModuleDAO.findAll();
  }

  @Override
  @Transactional
  public I18NModule findI18NModuleById(Long id) {
    return i18nModuleDAO.findById(id);
  }

  @Override
  @Transactional
  public List<I18NTranslation> findAllI18NTranslations() {
    return i18nTranslationDAO.findAll();
  }

  @Override
  @Transactional
  public I18NTranslation findI18NTranslationById(Long id) {
    return i18nTranslationDAO.findById(id);
  }

  @Autowired
  private ModelMapper modelMapper;

  @Override
  public SearchResult<I18NTranslationDto> findI18NTranslationWithFilter(CriteriaDto criteria) {
    List<Clause> clauses = new ArrayList<>();
    criteria.getFilters()
        .forEach(filterDto -> {
          List<Clause> innerClauses = new ArrayList<>();
          filterDto.getClauses()
              .forEach(clauseDto -> {
                String field = clauseDto.getField().getName();
                Operand operand = clauseDto.getOperand();
                TriFunction<String, Object, Object, Clause> triOperand = operand.getCustomOperand();
                BiFunction<String, Object, Clause> biOperand = operand.getOperand();
                if (I18TRANSLATION_DATE_COLUMNS.contains(field)) {
                  if (Operand.BETWEEN.equals(operand)) {
                    String values = clauseDto.getValue();
                    String[] limits = values.split(",");
                    innerClauses.add(triOperand
                        .apply(field, limits[0], limits[1]));
                  }
                  else {
                    innerClauses.add(biOperand
                        .apply(field, clauseDto.getValue()));
                  }
                }
                else if (Operand.IN.equals(operand) || Operand.NOT_IN.equals(operand)) {
                  String values = clauseDto.getValue();
                  String[] limits = values.split(",");
                  innerClauses.add(biOperand
                      .apply(field, Arrays.asList(limits)));
                }
                else if ((Operand.EQUAL.equals(operand) || Operand.LIKE.equals(operand) || Operand.NOT_LIKE.equals(
                    operand) || Operand.NOT_EQUAL.equals(operand)) && !clauseDto.isCaseSensitive()) {
                  // convert value to search to uppercase
                  String value = clauseDto.getValue()
                      .toUpperCase();
                  // add UPPER keyword to filed name
                  field = "UPPER(".concat(field)
                      .concat(")");
                  innerClauses.add(biOperand
                      .apply(field, value));
                }
                else {
                  innerClauses.add(biOperand
                      .apply(field, clauseDto.getValue()));
                }
              });
          clauses.add(getClauses(filterDto.getClausePrecedence(), innerClauses));
        });
      Query q = getQuery(criteria, clauses);
    Integer pageNumber = 0;
    Integer pageSize = MAX_PAGE_SIZE;
    if (!Objects.isNull(criteria.getPageNumber()) && !Objects.isNull(criteria.getPageSize())) {
      pageNumber = criteria.getPageNumber();
      pageSize = criteria.getPageSize();
    }
    Page<I18NTranslation> i18NTranslations = criteria.getSort()
        .isEmpty() ? i18nTranslationDAO.search(q, PageRequest.of(pageNumber, pageSize), criteria.getOnlyConflicts()) :
        i18nTranslationDAO.search(q, PageRequest.of(pageNumber, pageSize, getSort(criteria)), criteria.getOnlyConflicts());
    Long totalElements = i18NTranslations.getTotalElements();
    Integer totalPages = i18NTranslations.getTotalPages();
    Map<String, Object> metadata = new HashMap<>();
    metadata.put("totalElements", totalElements);
    metadata.put("totalPages", totalPages);
    metadata.put("totalConflicts", i18nTranslationDAO.count(I18NTranslation.class, new Query(), true));
    SearchResult<I18NTranslationDto> i18NTranslationSearchResult = new SearchResult<>();
    i18NTranslationSearchResult.setMetadata(metadata);
    List<I18NTranslationDto> dtos = new ArrayList<>();
    i18NTranslations.getContent().forEach(i18NTranslation -> dtos.add(modelMapper.map(i18NTranslation, I18NTranslationDto.class)));
    i18NTranslationSearchResult.setResult(dtos);
    return i18NTranslationSearchResult;
  }

  private Query getQuery(CriteriaDto criteria, List<Clause> clauses) {
    return clauses.isEmpty() ? new Query() : new Query(getClauses(criteria.getFilterPrecedence(), clauses));
  }

  private static Sort getSort(CriteriaDto criteria) {
    if (criteria.getSort()
        .isEmpty()) return null;
    Sort sort = Sort.by(Collections.emptyList());
    for (SortColumn s : criteria.getSort()) {
      Sort additionalSort = s
          .getOrderType()
          .equals(SortOrderDto.DESC) ? Sort.by(s.getColumn()
              .getName())
          .ascending() : Sort.by(s.getColumn()
              .getName())
          .descending();

      sort = sort.and(additionalSort);
    }
    return sort;
  }

  private Clause getClauses(ClausePrecedence clausePrecedence, List<Clause> clauses) {
    if (clausePrecedence == null) {
      return new AndClause(clauses);
    }
    else {
      switch (clausePrecedence) {
      case OR: {
        return new OrClause(clauses);
      }
      default: {
        return new AndClause(clauses);
      }
      }
    }
  }

  @Override
  @Transactional
  public List<I18NTranslation> findI18NTranslationsByLocale(Locale locale) {
    Query q = new Query(new AndClause(Clauses.equal("locale", locale)));
    return i18nTranslationDAO.search(q);
  }

  @Override
  @Transactional
  public String findByCodeAndLocale(String code, Locale locale, Object... args) {
    Query q = new Query(new AndClause(Clauses.equal("code", code), Clauses.equal("locale", locale)));
    I18NTranslation i18NTranslation = i18nTranslationDAO.searchSingleResult(q, true);
    if(Objects.isNull(i18NTranslation)) throw new RestJwtException("translation not found", ExceptionType.NOT_FOUND);
    String currentValue = !Objects.isNull(i18NTranslation.getNewValue())
        ? i18NTranslation.getNewValue()
        : i18NTranslation.getDefaultValue();
    return isEmpty(args) ? currentValue : MessageFormat.format(currentValue.replace("'","''"), args);
  }

  @Override
  public I18NTranslation editTranslation(UpdateTranslationDto i18NTranslation, User user, Long id) {
    Query q = new Query(Clauses.equal("id", id));
    I18NTranslation existingTranslation = i18nTranslationDAO.searchSingleResult(q, true);
    if(Objects.isNull(existingTranslation)) throw new RestJwtException("translation not found", ExceptionType.NOT_FOUND);
    existingTranslation.setNewValue(i18NTranslation.getNewValue());
    if(!StringUtils.isEmpty(i18NTranslation.getNewValue())) {
      existingTranslation.setNewValueChangedBy(user);
      existingTranslation.setNewValueChangedAt(new Date());
    } else {
      existingTranslation.setNewValueChangedBy(null);
      existingTranslation.setNewValueChangedAt(null);
    }
    return i18nTranslationDAO.store(existingTranslation);
  }

  @Override
  public List<Locale> findLanguages() {
    Page<Locale> languages = i18nTranslationDAO.searchAllLocale(I18NTranslation.class, "select DISTINCT(itr.locale) from I18NTranslation itr", PageRequest.of(0, MAX_PAGE_SIZE));
    return languages.getContent();
  }

  @Override
  @Transactional
  public Optional<I18NModule> findI18NModuleByName(String name) {
    return i18nModuleDAO.search(new Query(Clauses.equal("name", name))).stream().findFirst();
  }

  @Override
  @Transactional
  public List<I18NTranslationDto> getTranslations(){
    //In case we wont to get csv with criteria
    //SearchResult<I18NTranslationDto> i18NTranslationsWithFilter = i18NService.findI18NTranslationWithFilter(criteria);
    //List<I18NTranslationDto> translations = i18NTranslationsWithFilter.result;
    //For all translation as mentioned in the story AIO-14744
    List<I18NTranslationDto> translations = new ArrayList<>();
    i18NService.findAllI18NTranslations().forEach(i18NTranslation -> translations.add(modelMapper.map(i18NTranslation, I18NTranslationDto.class)));
    return translations;
  }

  @Override
  @Transactional
  public List<Locale> getLocales(){
    List<Locale> locales = i18NService.findLanguages();
    return locales;
  }

}
