/*
 * Copyright (c) 2024.
 * Created by Mansour SRIDI
 */

package com.byzaneo.generix.service.repository.bean.aap;

import com.byzaneo.commons.bean.Persistent;
import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "aap_RefSupplier")
public class AapRefSupplier implements Persistent<Long> {

    private static final long serialVersionUID = -980652711815395664L;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "RefSupplier_ID")
    protected Long id;

    @Column(name = "owner", nullable = false)
    private String owner;

    @Column(name = "creation_Date", nullable = false)
    private Date creationDate;

    @Column(name = "modification_Date", nullable = false)
    private Date modificationDate;
    //ExchangeId
    /**TODO see how to link with Exchagne rate. it will be from com.byzaneo.security.bean.Group*/
    @Column(name = "legal_Entity_Id", nullable = false)
    private  String legalEntityId;

    @Column(name = "demat_Partner_Id", nullable = false)
    private String dematPartnerId;

    @Column(name = "counterpart_Account_Id", nullable = false)
    private Integer counterpartAccountId;

    @Column(name = "automatic_counterpart", nullable = false)
    private String automaticCounterpart;

    @ManyToOne
    @JoinColumn(name = "REFERENTIAL_ID")
    private AapReferential aapReferential;
}
