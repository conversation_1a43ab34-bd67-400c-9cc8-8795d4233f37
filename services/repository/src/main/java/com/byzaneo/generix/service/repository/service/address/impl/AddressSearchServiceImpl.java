/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.service.repository.service.address.impl;

import com.byzaneo.generix.service.repository.bean.address.Address;
import com.byzaneo.generix.service.repository.bean.address.TypeAddressEnum;
import com.byzaneo.generix.service.repository.dao.AddressDao;
import com.byzaneo.generix.service.repository.service.address.AddressSearchService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

@Service(AddressSearchService.SERVICE_NAME)
public class AddressSearchServiceImpl implements AddressSearchService {

    @Inject
    private MongoTemplate mongoTemplate;

    @Inject
    private AddressDao addressDao;

    @Override
    public Address findAddressByIdentificationAndOwnerAndTypeAndPartnerCode(
            String identification, 
            String owner, 
            TypeAddressEnum typeAddress, 
            String partnerCode) {
        
        Query query = new Query();
        query.addCriteria(Criteria.where("identification").is(identification));
        query.addCriteria(Criteria.where("owner").is(owner));
        query.addCriteria(Criteria.where(Address.FIELD_TYPE).is(typeAddress));
        query.addCriteria(Criteria.where(Address.FIELD_PARTNER_CODE).is(partnerCode));

        return mongoTemplate.findOne(query, Address.class, Address.COLLECTION_NAME);
    }
}
