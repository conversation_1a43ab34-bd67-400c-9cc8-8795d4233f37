package com.byzaneo.generix.service.repository.service.translation;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DictionaryImportResponseTest {

    @Test
    void builder_ShouldCreateValidResponse_WithAllFields() {
        // Given
        int totalImported = 10;
        List<ImportError> errors = Arrays.asList(
            ImportError.builder()
                .lineNumber(1)
                .errorMessage("Test error 1")
                .build(),
            ImportError.builder()
                .lineNumber(2)
                .errorMessage("Test error 2")
                .build()
        );

        // When
        DictionaryImportResponse response = DictionaryImportResponse.builder()
            .totalImported(totalImported)
            .errors(errors)
            .build();

        // Then
        assertNotNull(response);
        assertEquals(totalImported, response.getTotalImported());
        assertEquals(errors, response.getErrors());
        assertEquals(2, response.getErrors().size());
    }

    @Test
    void builder_ShouldCreateValidResponse_WithEmptyErrors() {
        // Given
        int totalImported = 5;
        List<ImportError> errors = Collections.emptyList();

        // When
        DictionaryImportResponse response = DictionaryImportResponse.builder()
            .totalImported(totalImported)
            .errors(errors)
            .build();

        // Then
        assertNotNull(response);
        assertEquals(totalImported, response.getTotalImported());
        assertEquals(errors, response.getErrors());
        assertTrue(response.getErrors().isEmpty());
    }

    @Test
    void constructor_ShouldCreateValidResponse_WithAllArgsConstructor() {
        // Given
        int totalImported = 7;
        List<ImportError> errors = Arrays.asList(
            ImportError.builder()
                .lineNumber(3)
                .errorMessage("Test error 3")
                .build()
        );

        // When
        DictionaryImportResponse response = new DictionaryImportResponse(totalImported, errors);

        // Then
        assertNotNull(response);
        assertEquals(totalImported, response.getTotalImported());
        assertEquals(errors, response.getErrors());
        assertEquals(1, response.getErrors().size());
    }

    @Test
    void noArgsConstructor_ShouldCreateValidResponse() {
        // When
        DictionaryImportResponse response = new DictionaryImportResponse();

        // Then
        assertNotNull(response);
        assertEquals(0, response.getTotalImported());
        assertNull(response.getErrors());
    }

    @Test
    void settersAndGetters_ShouldWorkCorrectly() {
        // Given
        DictionaryImportResponse response = new DictionaryImportResponse();
        int totalImported = 12;
        List<ImportError> errors = Arrays.asList(
            ImportError.builder()
                .lineNumber(4)
                .errorMessage("Test error 4")
                .build()
        );

        // When
        response.setTotalImported(totalImported);
        response.setErrors(errors);

        // Then
        assertEquals(totalImported, response.getTotalImported());
        assertEquals(errors, response.getErrors());
    }

    @Test
    void response_ShouldHandleNullErrors() {
        // Given
        int totalImported = 3;

        // When
        DictionaryImportResponse response = DictionaryImportResponse.builder()
            .totalImported(totalImported)
            .errors(null)
            .build();

        // Then
        assertNotNull(response);
        assertEquals(totalImported, response.getTotalImported());
        assertNull(response.getErrors());
    }
}
