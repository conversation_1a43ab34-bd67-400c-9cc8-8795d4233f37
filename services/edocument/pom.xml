<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.byzaneo.generix.services</groupId>
		<artifactId>generix-services</artifactId>
		<version>6.0.0-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<packaging>jar</packaging>
	<artifactId>generix-service-edocument</artifactId>
	<name>[GNX] Generix - Service - eDocument</name>
	<dependencies>
		<!-- GENERIX -->
		<dependency>
			<groupId>com.byzaneo.generix.services</groupId>
			<artifactId>generix-service-xcbl</artifactId>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.aws</groupId>
			<artifactId>byzaneo-aws</artifactId>
		</dependency>
		<!-- TOOLS -->
		<dependency>
			<groupId>uk.com.robust-it</groupId>
			<artifactId>cloning</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.supercsv</groupId>
			<artifactId>super-csv-dozer</artifactId>
		</dependency>
		<!-- UI -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
		</dependency>
		<dependency><!-- link with sd-dss v4.3.0, need for com.sun.xml.bind.v2.model.annotation.AnnotationReader -->
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.dragon66</groupId>
			<artifactId>icafe</artifactId>
			<version>1.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.byzaneo.generix</groupId>
			<artifactId>generix-core</artifactId>
			<type>test-jar</type>
            <scope>test</scope>
		</dependency>
		<!-- lombok -->
  		<dependency>
			<groupId>org.projectlombok</groupId>
        	<artifactId>lombok</artifactId>
  		</dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
        </dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-junit-jupiter</artifactId>
		</dependency>
	</dependencies>
	<build>
		<plugins>
<!--			<plugin> -->
<!--				<groupId>com.atlassian.maven.plugins</groupId> -->
<!--				<artifactId>clover-maven-plugin</artifactId> -->
<!--				<version>${plg-clover}</version> -->
<!--				<configuration> -->
<!--					<excludes> -->
<!--						<exclude>**/schema/**</exclude> -->
<!--						<exclude>org/**</exclude> -->
<!--					</excludes> -->
<!--				</configuration> -->
<!--			</plugin> -->
<!-- 			<plugin> -->
<!-- 				<groupId>org.codehaus.mojo</groupId> -->
<!-- 				<artifactId>jaxb2-maven-plugin</artifactId> -->
<!-- 				<executions> -->
<!-- 					<execution> -->
<!-- 						<id>xjc</id> -->
<!-- 						<goals> -->
<!-- 							<goal>xjc</goal> -->
<!-- 						</goals> -->
<!-- 					</execution> -->
<!-- 				</executions> -->
<!-- 			</plugin> -->
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<appendAssemblyId>false</appendAssemblyId>
					<attach>false</attach>
				</configuration>
				<executions>
					<execution>
						<id>desadv</id>
						<phase>process-resources</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>desadv</finalName>
							<descriptors>
								<list>src/main/assembly/desadv.xml</list>
							</descriptors>
							<outputDirectory>target/classes/templates/birt/desadv</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>invoice</id>
						<phase>process-resources</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>invoice</finalName>
							<descriptors>
								<list>src/main/assembly/invoice.xml</list>
							</descriptors>
							<outputDirectory>target/classes/templates/birt/invoice</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>orders</id>
						<phase>process-resources</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>orders</finalName>
							<descriptors>
								<list>src/main/assembly/orders.xml</list>
							</descriptors>
							<outputDirectory>target/classes/templates/birt/orders</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>orders-excel</id>
						<phase>process-resources</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>orders-excel</finalName>
							<descriptors>
								<list>src/main/assembly/orders-excel.xml</list>
							</descriptors>
							<outputDirectory>target/classes/templates/birt/orders-excel</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>ordrsp</id>
						<phase>process-resources</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>ordrsp</finalName>
							<descriptors>
								<list>src/main/assembly/ordrsp.xml</list>
							</descriptors>
							<outputDirectory>target/classes/templates/birt/ordrsp</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>**/srcRte/**</exclude>
				</excludes>
			</resource>
		</resources>
	</build>
</project>
