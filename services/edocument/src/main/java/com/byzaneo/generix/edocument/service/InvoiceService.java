/**
 *
 */
package com.byzaneo.generix.edocument.service;

import java.io.IOException;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.JAXBException;
import javax.xml.stream.XMLStreamException;

import org.springframework.data.domain.*;

import com.byzaneo.commons.bean.*;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.exception.RequestException;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.User;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.xcbl.bean.*;

/**
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Generix group
 * @date 3 mai 2016
 */
public interface InvoiceService {

  String SERVICE_NAME = "gnxInvoiceService";

  boolean checkDuplicateInvoice(final Invoice invoice, String from);

  void removeFromDocumentGeneratedFiles(final Document document, List<FileType> types);

  Invoice convertToInvoiceType(Document document);

  Optional<String> resolveViewerBaseName(InvoiceIndex indexable, BeanDescriptor descriptor, String patternDate, String labelFamily);

  /**
   * Retrieves the display name of a user, considering both regular and technical users.
   *
   * @param userId The identifier of the user
   * @return The display name of the user or "Unknown User" if not found
   */
  String getDisplayNameForUser(String userId);

  String getRossumIdFromXcblIfOCRVerifiable(final InvoiceIndex indexable);

  String getRossumReferenceNumber(com.byzaneo.xtrade.api.Indexable indexable) throws XMLStreamException, JAXBException, IOException;

  void setRossumIdIfOCRVerifiable(OrganizationHelper.UserOrganizations userOrgs, List<InvoiceIndex> inv,
      String requestId);

  boolean refuseWorkflowDocuments(boolean fromAngular, com.byzaneo.xtrade.api.Indexable[] indexes, Locale locale, Query query,
      boolean allIndexesSelected, boolean isItemSelected, User user, User delegatorUserForSelectedInvoice, String sort,
      Sort.Direction order, Integer countSelected, String comment);

  void refuseDocumentAndSaveInTimeline(User user, User delegatorUserForSelectedInvoice, Document document,
      Workflow workflow, WorkflowStepRedirect workflowStRedirect, WorkflowDocumentStatusService workflowDocumentStatusService,
      DocumentService documentService, WorkflowStepRedirectService workflowStepRedirectService,
      DocumentTimelineService documentTimelineService, String comment);

  Query resolveSearchQuery(QueryBuilder qb, User user, boolean showOnlyWorkflow);

  WorkflowStep getNextWorkflowStepForIndexable(com.byzaneo.xtrade.api.Indexable indexable);

  boolean validateDocument(com.byzaneo.xtrade.api.Indexable indexable, Document document, User user, String locale, Instance instance);

  void updateDocStatusToRefusedAction(DocumentService documentService, Document document, Workflow workflow);

  String triggerActions(User user, String envCode, Long portletId, Map<String, String> actionParams);

  void onPostValidationComplianceProcess(Document document, String validationProcessId, String companyCode,
      Map<String, String> actionParams, Locale locale);

  Page<InvoiceIndex> searchInvoices(HttpServletRequest request, String bql, Integer limit,
      Integer offset, String flowDirection, String sortBy,
      String order, String localeAsString, User user,
      List<String> selectedSecurityFields, boolean showOnlyWorkflow) throws RequestException;

  Long countInvoices(HttpServletRequest request, String bql, Integer limit,
      Integer offset, String flowDirection, String sortBy,
      String order, String localeAsString, User user,
      List<String> selectedSecurityFields, boolean showOnlyWorkflow) throws RequestException;

}
