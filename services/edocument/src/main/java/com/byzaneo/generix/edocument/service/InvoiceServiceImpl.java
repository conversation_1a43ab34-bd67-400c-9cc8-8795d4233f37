/**
 *
 */
package com.byzaneo.generix.edocument.service;

import static com.byzaneo.commons.bean.FileType.XCBL;
import static com.byzaneo.commons.dao.mongo.PageRequest.of;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.generix.edocument.service.EDocumentService.EDocument.valueOfIgnoringCase;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.LABEL_FAMILY;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getXcblInvoiceFile;
import static com.byzaneo.generix.util.OrganizationHelper.resolveUserOrganizationsQuery;
import static com.byzaneo.generix.util.OrganizationHelper.resolveUserOrganizationsQueryWithBaseSecurityFields;
import static com.byzaneo.generix.util.ToolsHelper.removeWorkflowData;
import static com.byzaneo.generix.util.ToolsHelper.updateWorkflowPropertiesOnInvoiceIndex;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.END;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.REFUSE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.VALID;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.WRITE_ASYNC;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceIssueDate;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static javax.faces.application.FacesMessage.SEVERITY_WARN;
import static org.apache.commons.lang.StringUtils.isBlank;
import static org.apache.commons.lang3.LocaleUtils.toLocale;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import java.io.*;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import javax.xml.bind.JAXBException;
import javax.xml.stream.XMLStreamException;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.angular.service.*;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.ui.util.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.bean.ProcessReportExecution;
import com.byzaneo.generix.edocument.exception.RequestException;
import com.byzaneo.generix.edocument.util.*;
import com.byzaneo.generix.rtemachine.repository.VariablesOperations;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.security.bean.*;
import com.byzaneo.task.util.TaskHelper;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.ipm.bean.Deployment;
import com.byzaneo.xtrade.ipm.service.ProjectService;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.dao.OrderDAO;
import com.google.gson.Gson;

/**
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Generix group
 * @date 3 mai 2016
 */
@Service(InvoiceService.SERVICE_NAME)
public class InvoiceServiceImpl implements InvoiceService {

  private static final Logger log = getLogger(InvoiceServiceImpl.class);

  public static final String OFFICIAL_INDEX = "official_index";

  public static final String INVOIC_TYPE = "INVOIC";

  public static final Integer MAX_LIMIT = 500;
  public static final Predicate<DocumentFile> PREDICATE_INVOICE_XCBL = dof -> XCBL.equals(dof.getType()) &&
      OFFICIAL_INDEX.equals(dof.getDescription());

  public static final Integer TRIGGER_ACTION_MAX_INVOICES = 100;

  private static final List<String> KEYS_TO_REMOVE = asList("locale", "trigger-archive", "action-id", "bql", "action-name", "action-status",
      "action-selectedFormPage", "sortBy");

  private static final List<String> STATUSES_FOR_OCR_VERIFICATION = asList(DocumentStatus.OCR_TO_VERIFY.name(),
      DocumentStatus.OCR_POSTPONED.name());
  public static final List<String> ACCEPTED_FLOW_DIRECTION_VALUES = asList("INDIFFERENT", "SENDING", "RECEIVING");

  @Autowired
  private transient OrderDAO orderDAO;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  public transient SecurityService securityService;

  @Autowired
  @Qualifier(WorkflowDocumentStatusService.SERVICE_NAME)
  private transient WorkflowDocumentStatusService workflowDocumentStatusService;

  @Autowired
  @Qualifier(WorkflowStepRedirectService.SERVICE_NAME)
  private transient WorkflowStepRedirectService workflowStepRedirectService;

  @Autowired
  @Qualifier(RossumService.SERVICE_NAME)
  private transient RossumService rossumService;

  @Autowired(required = false)
  @Qualifier(VariablesOperations.OPERATIONS_NAME)
  private transient VariablesOperations variablesOperations;

  @Autowired
  @Qualifier(DocumentTimelineService.SERVICE_NAME)
  private transient DocumentTimelineService documentTimelineService;

  @Autowired
  @Qualifier(WorkflowUserBackupService.SERVICE_NAME)
  private transient WorkflowUserBackupService workflowUserBackupService;

  @Autowired
  @Qualifier(ActionModuleService.SERVICE_NAME)
  public transient ActionModuleService actionModuleService;

  @Autowired
  @Qualifier(WorkflowNotificationService.SERVICE_NAME)
  private transient WorkflowNotificationService workflowNotificationService;

  @Autowired
  @Qualifier(ProjectService.SERVICE_NAME)
  protected transient ProjectService projectService;

  @Autowired
  @Qualifier(InstanceService.SERVICE_NAME)
  private InstanceService instanceService;

  public enum AbsenceType {
    ABSENCE,
    DELEGATION
  }

  @Override
  public boolean checkDuplicateInvoice(final Invoice invoice, String from) {
    final Criteria critMatch = where("_from").is(from)
        .and("invoiceNumber")
        .is(invoice.getInvoiceHeader()
            .getInvoiceNumber()
            .getValue())
        .and("invoiceTypeCoded")
        .is(invoice.getInvoiceHeader()
            .getInvoiceType()
            .getInvoiceTypeCoded()
            .name());

    final AggregationOperation match = match(critMatch);
    final AggregationOperation project = project().and("invoiceIssueDate")
        .as("year");

    log.debug("Search duplicate invoice aggregate : Match {}, Project {}", match.toString(), project.toString());

    List<org.bson.Document> mongoDocuments = documentService.aggregate(newAggregation(InvoiceIndex.class, match, project),
        org.bson.Document.class);
    Calendar cal = Calendar.getInstance();
    cal.setTime(getInvoiceIssueDate(invoice));

    return mongoDocuments.stream()
        .map(dbo -> (Date) dbo.get("year"))
        .map(date ->
        {
          Calendar tmp = Calendar.getInstance();
          tmp.setTime(date);
          return tmp;
        })
        .anyMatch(tmp -> cal.get(Calendar.YEAR) == tmp.get(Calendar.YEAR));
  }

  @Override
  public void removeFromDocumentGeneratedFiles(final Document document, List<FileType> types) {
    types.stream()
        .map(document::getDocumentFileWithType)
        .map(dof -> removeFile(document, dof))
        .map(document::removeFile)
        .collect(toList());
  }

  @SuppressWarnings("squid:UnusedPrivateMethod")
  private DocumentFile removeFile(final Document document, final DocumentFile documentFile) {
    if (documentFile != null && documentFile.getLocale() != null) {
      FileHelper.deleteFile(documentFile.getFile());
      document.removeFile(documentFile);
    }
    return documentFile;
  }

  @Override
  public Invoice convertToInvoiceType(Document document) {
    if (document == null) return null;
    Invoice invoice;
    try {
      File invoiceFile = null;
      if (document.getFiles()
          .stream()
          .filter(PREDICATE_INVOICE_XCBL)
          .count() > 1) {
        error("Document invalid : official index is not unique");
      }
      else {
        invoiceFile = document.getFiles()
            .stream()
            .filter(PREDICATE_INVOICE_XCBL)
            .findFirst()
            .map(DocumentFile::getFile)
            .orElse(null);
      }
      invoice = JAXBHelper.unmarshal(Invoice.class, invoiceFile);
      if (document.getIndexValue() != null && invoice != null) {
        invoice.setId(document.getIndexValue()
            .getId());
      }
      return invoice;
    }
    catch (JAXBException | XMLStreamException | IOException e) {
      TaskHelper.error(this, e, e.getMessage());
    }
    return null;
  }

  @Override
  public boolean refuseWorkflowDocuments(boolean fromAngular, Indexable[] indexes, Locale locale, Query query,
      boolean allIndexesSelected, boolean isItemSelected, User user, User delegatorUserForSelectedInvoice, String sort,
      Sort.Direction order, Integer countSelected, String comment) {
    if (!isItemSelected && !allIndexesSelected) {
      if (fromAngular) {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + ".error_document_select_line", "", locale);
        throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
      }
      else {
        TaskHelper.warn(this, "gnxxcblcomlbls.error_document_select_line");
      }
    }
    else {
      boolean unauthorizedRefusal = false;
      boolean canNotRefuse = false;
      int refused = 0;
      boolean hasCustomRefuseAction = false;

      for (Indexable index : indexes) {
        // check if we can refuse
        Document document = documentService.getDocument(index);
        WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
            workflowDocumentStatusService.getWorkflowForDocument(index.getEntityId()));
        WorkflowStep wStep = workflowDocumentStatusService.getWorkflowStepForDocument(document.getId());
        if (wStep != null) {
          hasCustomRefuseAction = wStep.getCustomActions()
              .stream()
              .anyMatch(action -> CustomAction.CustomActionType.REFUSAL.equals(action.getAction()
                  .getActionType()));
        }
        else unauthorizedRefusal = true;
        WorkflowStepRedirect wStepRedirect = workflowStepRedirectService.getWorkflowStepRedirectByUserAndDocument(user.getId(),
            document.getId());
        if (wDocStatus != null) {
          if (!(wStepRedirect != null ? wStepRedirect.getRefuse() : wStep.getRefuse() || hasCustomRefuseAction))
            canNotRefuse = true;
          else if (isBlank(wDocStatus.getUserId()) || user.getId()
              .equals(wDocStatus.getUserId())) {
            refuseDocumentAndSaveInTimeline(user, delegatorUserForSelectedInvoice, document, wDocStatus.getWorkflow(),
                wStepRedirect, workflowDocumentStatusService, documentService, workflowStepRedirectService, documentTimelineService,
                comment);
            refused++;
          }
          else
            unauthorizedRefusal = true;
        }
        else
          unauthorizedRefusal = true;
      }
      return checkAndLogIfErrors(fromAngular, allIndexesSelected, refused, unauthorizedRefusal, indexes, canNotRefuse, countSelected);
    }
    return true;
  }

  @Override
  public void refuseDocumentAndSaveInTimeline(User user, User delegatorUserForSelectedInvoice, Document document,
      Workflow workflow, WorkflowStepRedirect workflowStRedirect, WorkflowDocumentStatusService workflowDocumentStatusService,
      DocumentService documentService, WorkflowStepRedirectService workflowStepRedirectService,
      DocumentTimelineService documentTimelineService, String comment) {
    if (document != null) {
      WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document, workflow);
      updateDocStatusToRefusedAction(documentService, document, workflow);
      workflowDocumentStatusService.removeWorkflowDocumentStatus(wDocStatus);
      if (workflowStRedirect != null) {
        workflowStepRedirectService.removeWorkflowStepRedirect(workflowStRedirect);
      }
      if (delegatorUserForSelectedInvoice == null) {
        // no delegator
        documentTimelineService.saveActionInTimeline(document, user, REFUSE, comment, wDocStatus.getNumStep());
        documentTimelineService.saveActionInTimeline(document, user, END, EMPTY, wDocStatus.getNumStep());
      }
      else {
        documentTimelineService.saveActionInTimeline(document, user, REFUSE, comment, wDocStatus.getNumStep(), null,
            delegatorUserForSelectedInvoice.getFullnameOrLogin());
        documentTimelineService.saveActionInTimeline(document, user, END, EMPTY, wDocStatus.getNumStep(), null,
            delegatorUserForSelectedInvoice.getFullnameOrLogin());
      }
    }
  }

  @Override
  public void updateDocStatusToRefusedAction(DocumentService documentService, Document document, Workflow workflow) {
    if (workflow.getStatusRefused() != null)
      document.setStatus(workflow.getStatusRefused());
    // also remove invoiceWorkflowIndex from the refused document
    removeWorkflowData((InvoiceIndex) document.getIndexValue());
    documentService.saveDocument(document);
  }

  private boolean checkAndLogIfErrors(boolean fromAngular, boolean allIndexesSelected, int refused, boolean unauthorizedRefusal,
      Indexable[] indexes, boolean canNotRefuse, Integer selectedCount) {
    if (unauthorizedRefusal || (!allIndexesSelected && indexes.length != selectedCount)) {
      if (fromAngular) {
        if (canNotRefuse) {
          throw new IllegalArgumentException("canNotRefuseAndunAuthorizedRefusal" + ":" + String.valueOf(refused));
        }
        else {
          throw new IllegalArgumentException("unauthorizedRefuse" + ":" + String.valueOf(refused));
        }
      }
      else {
        error("gnxxcblinvlbls.unauthorizedRefuse");
      }
    }
    if (refused != 0 && !fromAngular)
      TaskHelper.info(this, "refusedMessage", refused);
    if (canNotRefuse) {
      if (fromAngular) {
        throw new IllegalArgumentException("canNotRefuse" + ":" + String.valueOf(refused));
      }
      else {
        error("gnxxcblinvlbls.canNotRefuse");
        return false;
      }
    }
    return true;
  }

  @Override
  public WorkflowStep getNextWorkflowStepForIndexable(Indexable indexable) {
    try {
      return workflowDocumentStatusService.getNextWorkflowStepForDocument(indexable.getEntityId());
    }
    catch (EmptyResultDataAccessException e) {
      return null;
    }
  }

  @Override
  public boolean validateDocument(Indexable indexable, Document document, User user, String locale, Instance instance) {
    Workflow workflow = workflowDocumentStatusService.getWorkflowForDocument(indexable.getEntityId());
    WorkflowDocumentStatus wDocStatus = workflowDocumentStatusService.findByDocumentAndWorkflow(document,
        workflow);
    if (wDocStatus == null) {
      return false;
    }
    WorkflowStepRedirect workflowStepRedirect = workflowStepRedirectService.getWorkflowStepRedirectByUserAndDocument(user.getId(),
        document.getId());
    if (workflowStepRedirect != null) {
      workflowStepRedirectService.removeWorkflowStepRedirect(workflowStepRedirect);
    }
    InvoiceIndex invoiceIndex = (InvoiceIndex) indexable;
    WorkflowStep nextWorkflowStep = getNextWorkflowStepForIndexable(indexable);
    if (nextWorkflowStep != null) {
      wDocStatus.setUserId(null);
      wDocStatus.setNumStep(wDocStatus.getNumStep() + 1);
      wDocStatus.setNbRemind(0);
      wDocStatus.setPauseNotif(false);
      //Update workflow properties on invoiceIndex
      updateWorkflowPropertiesOnInvoiceIndex(invoiceIndex, wDocStatus, workflow, nextWorkflowStep, securityService, documentService);
      this.documentService.saveIndexable(invoiceIndex);

      wDocStatus.getWorkflowDocumentUsers()
          .removeIf(wdu -> wdu.getNumStep() < wDocStatus.getNumStep());
      workflowDocumentStatusService.save(wDocStatus);
      documentTimelineService.saveActionInTimeline(document, user, VALID, EMPTY, wDocStatus.getNumStep());
      List<User> usersToBeNotified = workflowNotificationService.getUsersToBeNotifiedForStep(workflow, wDocStatus.getNumStep(), wDocStatus);
      Map<Document, Map<Locale, DocumentRecipients>> documentRecipiants = new HashMap<Document, Map<Locale, DocumentRecipients>>();
      if (usersToBeNotified != null) {
        Map<Locale, DocumentRecipients> recipients = new HashMap<Locale, DocumentRecipients>();
        documentRecipiants.put(document, recipients);
        usersToBeNotified
            .forEach(
                user1 ->
                {
                  workflowNotificationService.addRecipient(toLocale(locale), recipients, user1,
                      DocumentRecipients::addToInternetAddress);
                });
        workflowNotificationService.notify(documentRecipiants, instance, new HashMap<String, Object>(),
            nextWorkflowStep.getTemplate());
      }
    }
    else {
      if (workflow != null && workflow.getStatusEnd() != null)
        document.setStatus(workflow.getStatusEnd());
      //end of workflow
      removeWorkflowData(invoiceIndex);
      documentService.saveDocument(document);

      workflowDocumentStatusService.removeWorkflowDocumentStatus(wDocStatus);
      documentTimelineService.saveActionInTimeline(document, user, VALID, EMPTY, wDocStatus.getNumStep());
      documentTimelineService.saveActionInTimeline(document, user, END, EMPTY, wDocStatus.getNumStep());
    }
    return true;
  }

  @Override
  public Query resolveSearchQuery(QueryBuilder qb, User user, boolean showOnlyWorkflow) {
    List<User> currentUserAndBackedupUsersByCurrentUser = initializeAndReturnBackupUsersLists(user);
    List<User> currentUserAndDelegatorUsersForCurrentUser = initializeAndReturnDelegatedUsersLists(user);
    Set<User> currentUserAndBackdupAndDelegatedUsers = new HashSet<>();
    currentUserAndBackdupAndDelegatedUsers.addAll(currentUserAndBackedupUsersByCurrentUser);
    currentUserAndBackdupAndDelegatedUsers.addAll(currentUserAndDelegatorUsersForCurrentUser);
    List<Long> workflowActionsOnInvoicesForUser = new ArrayList<Long>();
    workflowActionsOnInvoicesForUser.clear();
    workflowActionsOnInvoicesForUser = Stream
        .concat(workflowDocumentStatusService.getDocumentsOfConcernedUsers(new ArrayList<>(currentUserAndBackdupAndDelegatedUsers))
                .stream(),
            workflowStepRedirectService.getInvoicesForRedirectedUser(user.getId())
                .stream())
        .collect(toList());
    if (showOnlyWorkflow) {
      qb.and(Clauses.in("_entity_id", workflowActionsOnInvoicesForUser));
    }
    qb.append(actionModuleService.getQueryFromUserRoles(user));
    qb.append(user.getBqlInvQuery());
    return qb.query();
  }

  public List<User> initializeAndReturnBackupUsersLists(User currentUser) {
    List<User> currentUserAndBackedupUsersByCurrentUser = new ArrayList<User>();
    List<String> usersBackedupByCurrentUser = this.workflowUserBackupService.getAllUsersBackedupByCurrentUser(currentUser.getId());
    currentUserAndBackedupUsersByCurrentUser = new ArrayList<User>();
    currentUserAndBackedupUsersByCurrentUser.add(currentUser);
    currentUserAndBackedupUsersByCurrentUser.addAll(usersBackedupByCurrentUser.stream()
        .map(userId -> securityService.getUser(userId))
        .collect(toList()));
    return currentUserAndBackedupUsersByCurrentUser;
  }

  public List<User> initializeAndReturnDelegatedUsersLists(User currentUser) {
    List<User> currentUserAndDelegatorUsersForCurrentUser = new ArrayList<User>();
    List<String> usersDelegatedToCurrentUser = this.workflowUserBackupService.getAllUsersBackedupByCurrentUser(currentUser.getId(),
        AbsenceType.DELEGATION.toString());
    currentUserAndDelegatorUsersForCurrentUser = new ArrayList<User>();
    currentUserAndDelegatorUsersForCurrentUser.add(currentUser);
    currentUserAndDelegatorUsersForCurrentUser.addAll(usersDelegatedToCurrentUser.stream()
        .map(userId -> securityService.getUser(userId))
        .collect(toList()));
    return currentUserAndDelegatorUsersForCurrentUser;
  }

  public Optional<String> resolveViewerBaseName(InvoiceIndex indexable, BeanDescriptor descriptor, String patternDate, String labelFamily) {
    return ofNullable(indexable)
        .map(i -> new StringJoiner("-").add(getLabel(labelFamily, "invoice_reference_type_INVOICE", "i", null).toLowerCase())
            .add(ofNullable(descriptor.get("invoiceNumber")).map(p -> p.getDisplayValue(i))
                .orElse(i.getEntityRef()))
            .add(ofNullable(descriptor.get("invoiceIssueDate")).map(p -> p.getDisplayValue(i, null, null, patternDate, null))
                .orElse(""))
            .add(ofNullable(descriptor.get("invoiceProcessDateTime"))
                .map(p -> p.getDisplayValue(i, null, null, patternDate, null))
                .orElse(""))
            .toString());
  }

  public String getDisplayNameForUser(String userId) {
    User user = securityService.getUser(userId);
    if (user != null && user.getFullname() != null) {
      return user.getFullname();
    }

    TechnicalUser techUser = securityService.getTechnicalUserById(userId);
    if (techUser != null && techUser.getName() != null) {
      return techUser.getName();
    }

    return "Unknown User";
  }

  public Query getQuery(HttpServletRequest request, String bql, Integer limit,
      Integer offset, String flowDirection, String sortBy, String order,
      String localeAsString, User user, List<String> selectedSecurityFields,
      boolean showOnlyWorkflow) throws RequestException {
    if (limit == null)
      limit = -1;
    if (offset == null)
      offset = 0;
    log.debug("Searching {} eDocuments : {} (page={}, size={})",
        INVOIC_TYPE, bql, offset, limit);

    if (!(flowDirection == null || flowDirection.isEmpty() || ACCEPTED_FLOW_DIRECTION_VALUES.contains(flowDirection))) {
      throw new RequestException("400", Response.Status.BAD_REQUEST);
    }

    // - search for invoice -
    // get companyCode and perimeter for users
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    Collection<String> perimeter = userOrgs.getCodes();
    InvoiceIndex invoiceIndex;
    QueryBuilder qbuilder = resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql),
        null,
        null, null, null, flowDirection, sortBy,
        order);
    boolean isWorkflowMonitoringTask = false;
    Object task = null;
    Locale locale = null;
    if (request != null && request.getServerName() != null) {
      List<User> currentUserAndBackedupUsersByCurrentUser = new ArrayList<>();
      if (user != null)
        currentUserAndBackedupUsersByCurrentUser.add(user);

      //applied just for a normal user
      if (user != null && localeAsString != null) {
        localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
        locale = new Locale(localeAsString);
        qbuilder = resolveUserOrganizationsQueryWithBaseSecurityFields(companyCode, perimeter, parse(bql),
            null, selectedSecurityFields, sortBy,
            order);
        log.debug("query generated with security fields  {}", qbuilder.query()
            .toString());
        if (showOnlyWorkflow) {

          qbuilder.and(Clauses.in(com.byzaneo.xtrade.bean.Indexable.ENTITY_ID,
              Stream.concat(workflowDocumentStatusService.getDocumentsOfConcernedUsers(currentUserAndBackedupUsersByCurrentUser)
                          .stream(),
                      workflowStepRedirectService.getInvoicesForRedirectedUser(user.getId())
                          .stream())
                  .collect(toList())));
        }
        // replace the old  qbuilder value to handle the PortletFlowDirection in invoice case
      }
    }
    if (user != null) {
      qbuilder.append(securityService.createQueryFromUserRoles(user));
      qbuilder.append(user.getBqlInvQuery());
    }

    return qbuilder.query();
  }

  @Override
  public String getRossumIdFromXcblIfOCRVerifiable(final InvoiceIndex indexable) {
    if (indexable == null || !STATUSES_FOR_OCR_VERIFICATION.contains(indexable.getStatus()
        .getStatusCode()) || isNotBlank(indexable.getRossumId()))
      return null;
    try {
      return getRossumReferenceNumber(indexable);
    }
    catch (Exception e) {
      log.warn("Can't retrieve Rossum ID for invoice uuid " + indexable.getUuid() + " : " + e.getMessage(), e);
      return null;
    }
  }

  @Override
  public String getRossumReferenceNumber(Indexable indexable) throws XMLStreamException, JAXBException, IOException {
    return InvoiceXcblHelper.getRossumReferenceNumber(
        JAXBHelper.unmarshal(Invoice.class, getXcblInvoiceFile(documentService.getDocument(indexable))));
  }

  @Override
  public void setRossumIdIfOCRVerifiable(OrganizationHelper.UserOrganizations userOrgs, List<InvoiceIndex> inv,
      String requestId) {
    if (!rossumService.isRossumServiceActive() || !variablesOperations.hasAuthenticationChannelAccessTokenV1ForUse(AccessTokenV1Use.ROSSUM,
        userOrgs.getPartner(), userOrgs.getCompany())) {
      return;
    }

    for (InvoiceIndex invoiceIndex : inv) {
      try {
        String rossumId = getRossumIdFromXcblIfOCRVerifiable(invoiceIndex);
        if (rossumId != null) {
          invoiceIndex.setRossumId(rossumId);
        }
      }
      catch (Exception ex) {
        log.warn("RequestId {} : Can't retrieve Rossum ID for invoice uuid {} : {}", requestId, invoiceIndex.getUuid(), ex.getMessage());
      }
    }
  }

  @Override
  @Transactional(readOnly = true)
  public String triggerActions(User user, String envCode, Long portletId, Map<String, String> actionParams) {
    List<String> acceptedStatusesForAction = null;
    String bql = actionParams.get("bql");
    String actionId = actionParams.get("action-id");
    String actionName = actionParams.get("action-name");
    String status = actionParams.get("action-status");
    String selectedFormPage = actionParams.get("action-selectedFormPage");
    if (isNotBlank(status))
      acceptedStatusesForAction = Arrays.stream(status.split(","))
          .toList();

    KEYS_TO_REMOVE.forEach(actionParams::remove);
    Instance instance = this.instanceService.getInstanceByCode(envCode);
    if (instance == null) {
      throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
    }
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    Collection<String> perimeter = userOrgs.getCodes();
    final Query qb = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql), null,
            null, null, null)
        .query();
    int itemsCount = Long.valueOf(documentService.countIndexable(InvoiceIndex.class, qb))
        .intValue();
    if (itemsCount == 0)
      throw new TriggerActionException("ddl_no_file_warning_message", SEVERITY_WARN);

    if (itemsCount > TRIGGER_ACTION_MAX_INVOICES)
      throw new TriggerActionException("triggerActions_limitWarning", SEVERITY_WARN);

    List<com.byzaneo.xtrade.api.IndexableDocument> indexes = new ArrayList<>(
        this.documentService.searchIndexables(InvoiceIndex.class, qb, null)
            .getContent());
    com.byzaneo.xtrade.api.IndexableDocument[] indexableDocuments = new com.byzaneo.xtrade.api.IndexableDocument[indexes.size()];
    indexableDocuments = indexes.toArray(indexableDocuments);
    boolean globalAction = false;
    if (indexableDocuments.length > 1) {
      globalAction = true;
    }
    try {
      Gson gson = new Gson();
      String jsonString = actionParams.size() > 0 ? gson.toJson(actionParams) : null;
      actionModuleService.onTriggerAction(portletId.toString(), indexableDocuments, actionId, actionName, globalAction, user, instance,
          jsonString, acceptedStatusesForAction, InvoiceIndex.class, selectedFormPage);
      if (globalAction) {
        return "invoice_triggerActions_execute_info_global";
      }
      else {
        return "invoice_triggerActions_execute_info";
      }

    }
    catch (Exception e) {
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public void onPostValidationComplianceProcess(Document document, String validationProcessId, String companyCode,
      Map<String, String> actionParams, Locale locale) {
    if (validationProcessId == null)
      return;
    // only partners can run the process
    //this.resolveOrganizationCodes();
    if (companyCode == null) {
      //error(this, null, "error_only_partner_can_process");
      String exceptionMessage = JSFHelper.getLabel(LABEL_FAMILY, "error_only_partner_can_process",
          "error_only_partner_can_process", locale);
      throw new RuntimeException(exceptionMessage);
    }

    // gets the deployed process
    final Deployment deployment = this.projectService.getDeployment(validationProcessId);
    if (deployment == null) {
      //error(this, null, "error_process_not_deployed");
      String exceptionMessage = JSFHelper.getLabel(LABEL_FAMILY, "error_process_not_deployed", "error_process_not_deployed",
          locale);
      throw new RuntimeException(exceptionMessage);
    }
    documentService.detachDocuments(Arrays.asList(document));

    // processes document
    Arrays.asList(document)
        .stream()
        .map(ufile ->
        {
          String uFileName = ufile.getFirstFile()
              .getName();
          try {
            Map<String, Object> existingParams = new HashMap<String, Object>() {{
              put(DOCUMENTS.toString(), singletonList(ufile));
              put(WRITE_ASYNC.toString(), false);
              /*
               * even if the process is asynchronous we need to generate the reports synchronous to be able to obtain the report
               * status of the process
               */
            }};
            Map<String, Object> combinedParams = new HashMap<>(existingParams);
            combinedParams.putAll(actionParams);
            return new ProcessReportExecution(uFileName,
                this.projectService.startAsync(deployment, combinedParams));
          }
          catch (Exception e) {
            return new ProcessReportExecution(uFileName, e);
          }
        })
        .collect(toList());
  }

  @Override
  @Transactional
  public Long countInvoices(HttpServletRequest request, String bql, Integer limit,
      Integer offset, String flowDirection, String sortBy,
      String order, String localeAsString, User user,
      List<String> selectedSecurityFields, boolean showOnlyWorkflow) throws RequestException {
    try {
      final EDocumentService.EDocument edoc = valueOfIgnoringCase(INVOIC_TYPE);
      return this.documentService.countIndexable(edoc.getXcblDocumentClass(),
          getQuery(request, bql, limit, offset, flowDirection, sortBy, order, localeAsString, user, selectedSecurityFields,
              showOnlyWorkflow));
    }
    catch (RequestException e) {
      throw e;
    }
  }

  @Override
  @Transactional
  public Page<InvoiceIndex> searchInvoices(HttpServletRequest request, String bql, Integer limit,
      Integer offset, String flowDirection, String sortBy, String order,
      String localeAsString, User user, List<String> selectedSecurityFields,
      boolean showOnlyWorkflow) throws RequestException {
    try {
      final EDocumentService.EDocument edoc = valueOfIgnoringCase(INVOIC_TYPE);
      Query qb = getQuery(request, bql, limit, offset, flowDirection, sortBy, order, localeAsString, user, selectedSecurityFields,
          showOnlyWorkflow);

      final PageRequest pageable = of(
          offset < 0 ? 0 : offset,
          limit < 1 || limit > MAX_LIMIT ? MAX_LIMIT : limit);
      pageable.setFromAngular(true);

      log.info("API {} is searching {} edocuments: {} ({})", user.getLogin(), edoc, qb, pageable);

      log.debug("Searching for invoice with query {}", qb.toString());
      Page<InvoiceIndex> page = (Page<InvoiceIndex>) this.documentService.searchIndexables(edoc.getXcblDocumentClass(), qb, pageable);
      return page;
    }
    catch (RequestException e) {
      throw e;
    }
    catch (Exception e) {
      throw e;
    }
  }

}
