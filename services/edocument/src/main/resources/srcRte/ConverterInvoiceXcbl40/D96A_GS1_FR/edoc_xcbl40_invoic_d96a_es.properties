XCBL_ERROR_MOA_125_g48_EMPTY = El importe total sin IVA (MOA+125 g48) est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_IV_IDENT_EMPTY = El c\u00F3digo de identificaci\u00F3n del emisor de la factura (eC082.3039 de la NAD+IV) est\u00E1 vac\u00EDo.
XCBL_ERROR_NAD_LC_NAME_EMPTY = El primer elemento de la raz\u00F3n social del representante fiscal (eC080.3036.1 de la NAD+LC) est\u00E1 vac\u00EDo.
XCBL_ERROR_DATE_FORMAT_EMPTY = El formato de fecha est\u00E1 vac\u00EDo. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_C = Un segmento MOA+204 (g25) no puede seguir a un segmento ALC+C.
XCBL_ERROR_RFF_XA_RE_IDENT_EMPTY = eC506.1154 en RFF+XA para NAD+RE est\u00E1 vac\u00EDo
XCBL_ERROR_LINE_MOA_DOESNOT_MATCH_ALC_A = Un segmento MOA+23 (g25) no puede seguir a un segmento ALC+A.
XCBL_ERROR_NAD_BY_NAME_EMPTY = eC080.3036.1 de NAD+BY est\u00E1 vac\u00EDo
XCBL_ERROR_QTY47_g25_EMPTY = La cantidad facturada (eC186.6060 en el segmento QTY+47 g25) est\u00E1 vac\u00EDa
XCBL_ERROR_QTY52_g25_EMPTY = eC186.6060 segmento QTY+52 g25 seg est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_RE_IDENT_EMPTY = eC082.3039 del NAD+RE est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_VA_SE_IDENT_EMPTY = El n\u00FAmero de identificaci\u00F3n a efectos del IVA del vendedor (eC506.1154 en RFF+VA para el NAD+SE) est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_GN_BY_IDENT_EMPTY = eC506.1154 en RFF+GN para NAD+BY est\u00E1 vac\u00EDo
XCBL_ERROR_BGM_MISSING = Falta el segmento BGM
XCBL_ERROR_RFF_VA_BY_IDENT_EMPTY = eC506.1154 en RFF+VA para NAD+BY est\u00E1 vac\u00EDo
XCBL_ERROR_MOA_MORE_THAN_ONE = M\u00E1s de una reducci\u00F3n/cargo global (segmento g15 del MOA del segmento g15 del ALC, calificador 8)
XCBL_ERROR_NAD_IV_NAME_EMPTY = El primer elemento de la raz\u00F3n social del socio facturado (eC080.3036.1 de la NAD+IV) est\u00E1 vac\u00EDo.
XCBL_ERROR_PCD_g15_EMPTY = eC501.5482 en el segmento PCD g15 est\u00E1 vac\u00EDo
XCBL_ERROR_PERIOD_NOT_WELLFORMATTED = El punto no est\u00E1 formateado correctamente. Forzamos las fechas a 1970-01-01T00:00:00
XCBL_ERROR_NAD_SE_MISSING = Falta el segmento NAD+SE
XCBL_ERROR_RFF_XA_IV_IDENT_EMPTY = eC506.1154 en RFF+XA para NAD+IV est\u00E1 vac\u00EDo
XCBL_ERROR_MOA113_g48_EMPTY = El importe del anticipo (eC516.5004 en MOA+113 g48) est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_GN_RE_IDENT_EMPTY = eC506.1154 en RFF+GN para NAD+RE est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_CO_NAME_EMPTY = El primer elemento de la raz\u00F3n social del domicilio social del vendedor (eC080.3036.1 de la NAD+CO) est\u00E1 vac\u00EDo.
XCBL_ERROR_DTM137_MORE_THAN_ONE = M\u00E1s de una fecha de facturaci\u00F3n
XCBL_ERROR_RFF_GN_SE_IDENT_EMPTY = El c\u00F3digo SIREN del vendedor (eC506.1154 en RFF+GN para NAD+SE) est\u00E1 vac\u00EDo.
XCBL_ERROR_DATE_NOT_VALID = La fecha no es v\u00E1lida. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_LINE_MOA_g25_EMPTY = eC516.5004 en el segmento MOA g25 est\u00E1 vac\u00EDo
99InternalError0001 = Argumento MENSAJE no definido
XCBL_ERROR_MOA_124_g48_MORE_THAN_ONE = M\u00E1s de un importe total del IVA (segmento MOA+124 g48)
XCBL_ERROR_NAD_IV_MORE_THAN_ONE = M\u00E1s de un segmento relativo al facturado (NAD+IV)
99InternalError0002 = El archivo MENSAJE no existe
XCBL_ERROR_PRI_MISSING = Falta PRI+AAA y PRI+AAB (g25)
99InternalError0003 = El archivo MENSAJE tiene un tama\u00F1o de 0
XCBL_ERROR_RFF_GN_CO_IDENT_EMPTY = El c\u00F3digo SIREN del domicilio social del vendedor (eC506.1154 en RFF+GN para el NAD+CO) est\u00E1 vac\u00EDo
99InternalError0004 = No se puede leer el archivo MENSAJE
XCBL_ERROR_FTX_REG_MORE_THAN_ONE = M\u00E1s de un FTX+REG
XCBL_ERROR_ALC_DESC_MISSING = Falta la descripci\u00F3n del ALC (eC214.7160.1,eC214.7160.2)
XCBL_ERROR_NAD_SE_NAME_EMPTY = El primer elemento de la raz\u00F3n social del vendedor (eC080.3036.1 del NAD+SE) est\u00E1 vac\u00EDo.
XCBL_ERROR_RFF_GN_IV_IDENT_EMPTY = El c\u00F3digo SIREN del socio facturado (eC506.1154 en RFF+GN para NAD+IV) est\u00E1 vac\u00EDo.
XCBL_ERROR_NAD_RE_MORE_THAN_ONE = M\u00E1s de un segmento NAD+RE
99InternalError0009 = Solo se espera un UNH en el archivo MENSAJE
XCBL_ERROR_DTM113_MISSING = Falta la fecha del anticipo (segmento DTM+171)
XCBL_ERROR_RFF_VA_DL_IDENT_EMPTY = eC506.1154 en RFF+VA para NAD+DL est\u00E1 vac\u00EDo
XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_A = Un PCD+2 no puede seguir a un ALC+A
XCBL_ERROR_NAD_RE_NAME_EMPTY = eC080.3036.1 del NAD+RE est\u00E1 vac\u00EDo
99InternalError0005 = El nivel de registro no est\u00E1 definido (LEVEL)
XCBL_ERROR_LINE_PCD_DOESNOT_MATCH_ALC_C = Un PCD+1 no puede seguir a un ALC+C
XCBL_ERROR_NAD_BY_MORE_THAN_ONE = M\u00E1s de un segmento NAD+BY
99InternalError0008 = Intercambio EDIFACT Factura D96A requerida
XCBL_ERROR_NAD_DL_NAME_EMPTY = eC080.3036.1 de NAD+DL est\u00E1 vac\u00EDo
99InternalError0012 = Solo se espera un UNB en el archivo MENSAJE
XCBL_ERROR_MOA_124_g48_EMPTY = El importe total del IVA (MOA+124 g48) est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_LC_MORE_THAN_ONE = M\u00E1s de un segmento representativo fiscal (NAD+LC)
99InternalError0010 = Mensaje EDIFACT incorrecto
99InternalError0011 = UNB requerido en el archivo MENSAJE
XCBL_ERROR_DATE_EMPTY = La fecha est\u00E1 vac\u00EDa. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_LINE_PCD_QUALIFIER_NOT_EXPECTED = El calificador para PCD(g25) no es correcto despu\u00E9s de un ALC+A o ALC+C (g25). Se espera: PCD+1 o PCD+2
XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_C = Un PCD+1 no puede seguir a un ALC+C
XCBL_ERROR_PCD_DOESNOT_MATCH_ALC_A = Un PCD+2 no puede seguir a un ALC+A
XCBL_ERROR_MOA_g15_EMPTY = eC516.5004 en el segmento MOA g15 est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_DP_MORE_THAN_ONE = M\u00E1s de un segmento NAD+DP
XCBL_ERROR_RFF_XA_LC_IDENT_EMPTY = eC506.1154 en RFF+XA para NAD+LC est\u00E1 vac\u00EDo
XCBL_ERROR_DTM_13_g8_MISSING = Falta el segmento DTM+13 (g8)
XCBL_ERROR_PCD_MORE_THAN_ONE = M\u00E1s de un PCD de g15 encontrado tras el segmento ALC g15
XCBL_ERROR_DATE_NOT_WELLFORMATED = La fecha no est\u00E1 formateada correctamente. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_RFFPQ_g48_EMPTY = La referencia del anticipo (eC506.1154 en RFF+PQ) est\u00E1 vac\u00EDa
XCBL_ERROR_LINE_ALC_DESC_MISSING = Falta la descripci\u00F3n del segmento ALC(g25) (eC214.7160.1,eC214.7160.2)
XCBL_ERROR_RFF_GN_DL_IDENT_EMPTY = eC506.1154 en RFF+GN para NAD+DL est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_SE_MORE_THAN_ONE = M\u00E1s de un segmento en el vendedor de NAD+SE
XCBL_ERROR_MOA_124_g50_MORE_THAN_ONE = M\u00E1s de un segmento MOA+124 g50
XCBL_ERROR_RFF_XA_DL_IDENT_EMPTY = eC506.1154 en RFF+XA para NAD+DL est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_XA_CO_IDENT_EMPTY = eC506.1154 en RFF+XA para NAD+CO est\u00E1 vac\u00EDo
XCBL_ERROR_QTY47_MISSING = Falta QTY+47(g25)
XCBL_ERROR_VALIDATION_EDI = Fallo en la validaci\u00F3n del mensaje EDI
XCBL_ERROR_MOA_128_g48_MORE_THAN_ONE = M\u00E1s de un importe total IVA incluido (segmento MOA+128 g48)
XCBL_ERROR_CUX_MORE_THAN_ONE = M\u00E1s de un segmento CUX (g7)
XCBL_ERROR_ALC_g15_PCD_MOA_EMPTY = Falta MOA+8(g15) o PCD+1/2(g15)
XCBL_ERROR_QTY47_g25_MORE_THAN_ONE = M\u00E1s de una cantidad facturada (segmento CANT+47 g25)
XCBL_ERROR_NAD_DP_IDENT_EMPTY = eC082.3039 de NAD+DP est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_XA_BY_IDENT_EMPTY = eC506.1154 en RFF+XA para NAD+BY est\u00E1 vac\u00EDo
XCBL_ERROR_MOA9_g48_EMPTY = El importe adeudado (eC516.5004 en MOA+9 g48) est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_DL_MORE_THAN_ONE = M\u00E1s de un segmento NAD+DL
XCBL_ERROR_ALC_MOA204_MISSING = Falta MOA+204 del segmento ALC
XCBL_ERROR_RFF_XA_SE_IDENT_EMPTY = eC506.1154 en RFF+XA para NAD+SE est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_DL_IDENT_EMPTY = eC082.3039 de NAD+DL est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_SE_IDENT_EMPTY = El c\u00F3digo de identificaci\u00F3n del vendedor (eC082.3039 del NAD+SE) est\u00E1 vac\u00EDo.
XCBL_ERROR_LINE_PCD_MORE_THAN_ONE = Doble porcentaje de reducci\u00F3n/carga en la l\u00EDnea (PCD g25 despu\u00E9s del segmento ALC g25)
XCBL_ERROR_PCD_QUALIFIER_NOT_EXPECTED = El calificador PCD no es correcto despu\u00E9s de un ALC+A o ALC+C. Esperado: PCD+1 o PCD+2
XCBL_ERROR_RFF_VA_LC_IDENT_EMPTY = El n\u00FAmero de identificaci\u00F3n a efectos del IVA del representante fiscal (eC506.1154 en RFF+VA para el NAD+LC) est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_BY_IDENT_EMPTY = eC082.3039 del NAD+BY est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_DP_NAME_EMPTY = eC080.3036.1 de NAD+DP est\u00E1 vac\u00EDo
XCBL_ERROR_LINE_MOA_MORE_THAN_ONE = Doble importe de reducci\u00F3n/cargo en la l\u00EDnea (segmento MOA g25 despu\u00E9s del segmento ALC g25)
XCBL_ERROR_RFF_VA_RE_IDENT_EMPTY = eC506.1154 en RFF+VA para NAD+RE est\u00E1 vac\u00EDo
XCBL_ERROR_DTM_137_MISSING = Falta la fecha de facturaci\u00F3n
XCBL_ERROR_RFFPQ_MISSING = Falta la referencia del anticipo (segmento RFF+PQ)
XCBL_ERROR_RFF_GN_LC_IDENT_EMPTY = =El c\u00F3digo SIREN del representante fiscal (eC506.1154 en RFF+GN para el NAD+LC) est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_CO_MORE_THAN_ONE = M\u00E1s de un segmento relativo a la sede del vendedor (NAD+CO)
XCBL_ERROR_MOA_128_g48_EMPTY = El importe total, IVA incluido (MOA+128 g48), est\u00E1 vac\u00EDo
XCBL_ERROR_MOA_125_g48_MORE_THAN_ONE = M\u00E1s de un importe total sin IVA (segmento MOA+125 g48)
XCBL_ERROR_FORMAT_NOT_MANAGED = No se gestiona el formato de la fecha. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_RFF_VA_CO_IDENT_EMPTY = El n\u00FAmero de identificaci\u00F3n a efectos del IVA del domicilio social del vendedor (eC506.1154 en RFF+VA para NAD+CO) est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_VA_IV_IDENT_EMPTY = El n\u00FAmero de identificaci\u00F3n a efectos de IVA del emisor de la factura (eC506.1154 en RFF+VA para el NAD+IV) est\u00E1 vac\u00EDo
XCBL_ERROR_LINE_PCD_g25_EMPTY = eC501.5482 en el segmento PCD g25 est\u00E1 vac\u00EDo
XCBL_ERROR_MOA_124_g50_EMPTY = El importe fiscal del desglose del IVA (MOA+124 g50) est\u00E1 vac\u00EDo

XCBL_ERROR_RFF_XA_SE_MORE_THAN_ONE = M\u00E1s de un RCS-RCM (RFF+XA) para el vendedor (NAD+SE)
XCBL_ERROR_RFF_VA_CO_MORE_THAN_ONE = M\u00E1s de un c\u00F3digo de IVA intracomunitario (RFF+VA) para el domicilio social del vendedor (NAD+CO)
XCBL_ERROR_RFF_VA_LC_MORE_THAN_ONE = M\u00E1s de un c\u00F3digo de IVA intracomunitario (RFF+VA) para el representante fiscal (NAD+LC)
XCBL_ERROR_RFF_VA_DL_MORE_THAN_ONE = M\u00E1s de un RFF+VA para NAD+DL
XCBL_ERROR_TOD_4055 = No hay condiciones de entrega (en c\u00F3digo) a pesar de la presencia del segmento TOD
XCBL_ERROR_RFF_GN_RE_MORE_THAN_ONE = M\u00E1s de un RFF+GN para NAD+RE
XCBL_ERROR_RFF_VA_DP_IDENT_EMPTY = eC506.1154 en RFF+VA para NAD+DP est\u00E1 vac\u00EDo
XCBL_ERROR_NAD_SF_NAME_EMPTY = El 1er elemento de la raz\u00F3n social del proveedor (eC080.3036.1 de la NAD+SF) est\u00E1 vac\u00EDo.
XCBL_ERROR_RFF_GN_LC_MORE_THAN_ONE = M\u00E1s de un RFF+GN para NAD+LC
XCBL_ERROR_BGM_1004_EMPTY = El elemento BGM e1004 est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_GN_BY_MORE_THAN_ONE = M\u00E1s de un RFF+GN para NAD+BY
XCBL_ERROR_RFF_VA_IV_MORE_THAN_ONE = M\u00E1s de un c\u00F3digo de IVA intracomunitario (RFF+VA) para el facturado (NAD+IV)
XCBL_ERROR_RFF_GN_DP_IDENT_EMPTY = eC506.1154 en RFF+GN para NAD+DL est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_VA_RE_MORE_THAN_ONE = M\u00E1s de un RFF+VA para NAD+RE
XCBL_ERROR_RFF_GN_DP_MORE_THAN_ONE = M\u00E1s de un RFF+GN para NAD+DP
XCBL_ERROR_RFF_XA_DP_IDENT_EMPTY = eC506.1154 en RFF+XA para NAD+DP est\u00E1 vac\u00EDo
XCBL_ERROR_RFF_VA_BY_MORE_THAN_ONE = M\u00E1s de un RFF+VA para NAD+BY
XCBL_ERROR_TAX_7_VAT_E_NO_FTXSIN = TAX+7+VAT++++E sin FTX+SIN
XCBL_ERROR_RFF_VA_DP_MORE_THAN_ONE = M\u00E1s de un RFF+VA para NAD+DP
XCBL_ERROR_RFF_GN_SE_MORE_THAN_ONE = M\u00E1s de un RFF+GN para NAD+SE
XCBL_ERROR_RFF_GN_IV_MORE_THAN_ONE = M\u00E1s de un RFF+GN para NAD+IV
XCBL_ERROR_RFF_GN_CO_MORE_THAN_ONE = M\u00E1s de un RFF+GN para NAD+CO
XCBL_ERROR_RFF_VA_SE_MORE_THAN_ONE = M\u00E1s de un c\u00F3digo de IVA intracomunitario (RFF+VA) para el vendedor (NAD+SE)
XCBL_ERROR_RFF_GN_DL_MORE_THAN_ONE = M\u00E1s de un RFF+GN para NAD+DL
XCBL_ERROR_RFF_XA_CO_MORE_THAN_ONE = M\u00E1s de un RCS-RCM (RFF+XA) para el domicilio social del vendedor (NAD+CO)
XCBL_ERROR_BGM1225 = El c\u00F3digo de funci\u00F3n del mensaje es desconocido (segmento BGM)
XCBL_ERROR_INVOIC_FORMAT_NOT_MANAGED = Fecha de la factura: No se gestiona el formato de la fecha. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_ALC_A_REGLMT_CODE = El c\u00F3digo de regulaci\u00F3n de la reducci\u00F3n global (e4471 de ALC+A(g15)) debe ser 1 o 2.
XCBL_ERROR_ALC_C_REGLMT_CODE = El c\u00F3digo de regulaci\u00F3n de carga global (e4471 de ALC+C(g15)) debe ser 1 o 2
XCBL_ERROR_INVOIC_DATE_NOT_WELLFORMATED = Fecha de la factura: La fecha no est\u00E1 formateada correctamente. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_LINE_ALC_g25_PCD_MOA_EMPTY = Falta MOA+204(g25) o MOA+23(g25) o PCD+1(g25) o PCD+2(g25)
XCBL_ERROR_BGM_MORE_THAN_ONE = Inicio del mensaje: El segmento BGM est\u00E1 presente por duplicado
XCBL_ERROR_LINE_ALC_A_REGLMT_CODE = El c\u00F3digo de regulaci\u00F3n de reducci\u00F3n de l\u00EDnea (e4471 de ALC+A(g38)) debe ser 1 o 2
XCBL_ERROR_INVOIC_DATE_NOT_VALID = Fecha de la factura: La fecha no es v\u00E1lida. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_LINE_ALC_C_REGLMT_CODE = El c\u00F3digo de regulaci\u00F3n de carga global (e4471 de ALC+C(g38)) debe ser 1 o 2
XCBL_ERROR_NAD_SF_IDENT_EMPTY = El c\u00F3digo de identificaci\u00F3n de la entrega (eC082.3039 del NAD+SF) est\u00E1 vac\u00EDo.
XCBL_ERROR_INVOIC_DATE_EMPTY = Fecha de facturaci\u00F3n: La fecha est\u00E1 vac\u00EDa. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_INVOIC_DATE_FORMAT_EMPTY = Fecha de la factura: El formato de la fecha est\u00E1 vac\u00EDo. Forzamos la fecha a 1970-01-01T00:00:00
XCBL_ERROR_NAD_SF_MORE_THAN_ONE = M\u00E1s de un segmento suministrado por (NAD+SF)