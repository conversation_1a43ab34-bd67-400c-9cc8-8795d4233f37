99InternalError0001=MESSAGE argument is not defined
99InternalError0002=The file in argument doesn't exist
99InternalError0003=The file in argument has a size of 0
99InternalError0004=Can't read the file in argument
99InternalError0005=Trace level not defined
99InternalError0006=No UNB or UNH segment in the message
99InternalError0007=Not able to handle several messages in an interchange
D96ACodeComplementaireProduit0001=Line internal code: The article internal code is transmitted twice
D96ACommentaires0001=Comments (free text): The comments in the document header are missing
D96ACommentaires0002=Comments (free text): The seller regulatory information is missing
D96ACommentaires0003=Comments (free text): The seller regulatory information is transmitted twice
D96ACommentaires0004=Comments (free text): The registered company name of the seller is incorrect
D96ACommentaires0005=Comments (free text): The seller legal form is incorrect
D96ACommentaires0006=Comments (free text): The seller share capital and currency are incorrect
D96ACommentaires0007=Comments (free text): The text describing the discount terms is transmitted twice
D96ACommentaires0008=Comments (free text): The 1st element of the text describing the conditions of conditional discount is incorrect
D96ACommentaires0009=Comments (free text): The text describing the conditions of penalty is transmitted twice
D96ACommentaires0010=Comments (free text): The 1st element of the text describing the penalty terms is incorrect
D96ACommentaires0011=Comments (free text): There is an inconsistency between the comment describing the reason for the tax exemption and the tax exemption code in the header of the document
D96ACommentaires0012=Comments (free text): The 1st element of the text describing the reason for the tax exemption in the header of the document is incorrect
D96ACommentaires0013=Comments (free text): Penalty fee is missing
D96ACommentaires0014=Comments: The seller VAT and discount type information is missing
D96ACommentaires0015=Comments: Wrong label for the seller VAT scheme
D96ACommentaires0016=Comments: Wrong label for the seller discount term
D96ACommentaires0017=Comments: Wrong seller RCS-RCM or City
D96ACommentaires0018=Comments (free text): Penalty fee is duplicated
D96ACommentairesLigne0001=Line comments (free text): The 1st element of the text describing the reason for the tax exemption on the line item is incorrect
D96AConditionsPaiement0001=Payment Terms: The payment terms information is missing
D96AConditionsPaiement0002=Payment Terms: The due date information is missing
D96AConditionsPaiement0003=Payment Terms: The discount terms information is missing
D96AConditionsPaiement0004=Payment Terms: The penalty terms information is missing
D96AConditionsPaiement0005=Payment Terms: The discount terms are missing simultaneously in text and in coded form
D96AConditionsPaiement0006=Payment Terms: The discount terms are simultaneously transmitted in text and in coded form (only 1 expression can be used)
D96AConditionsPaiement0007=Payment Terms: The penalty terms are missing simultaneously in text and in coded form
D96AConditionsPaiement0008=Payment Terms: The penalty terms are simultaneously transmitted in text and in coded form (only 1 expression can be used)
D96AConditionsPaiement0009=Payment Terms: The penalty terms are transmitted in duplicate as code
D96AConditionsPaiement0010=Payment Terms: The discount terms are are transmitted in duplicate as code
D96ADateReference0001=Global reference dates: The date/time/period qualifier of the reference document is incorrect
D96ADateReference0002=Global reference dates: The validity period is not numerical
D96ADateReference0003=Global reference dates: The validity period format qualifier is incorrect
D96ADateReference0004=Global reference dates: The length of the contract validity period is incorrect
D96ADateReferenceLigne0001=Line reference dates: Missing date/time  for the referenced delivery note/despatch advice
D96ADateReferenceLigne0002=Line reference dates: The date/time  for the referenced delivery note/despatch advice is not a number
D96ADatesFacture0001=Document dates: The date/time/period qualifier is incorrect
D96ADatesFacture0002=Document dates: The document date/time is missing
D96ADatesFacture0003=Document dates: The document date/time is transmitted twice
D96ADatesFacture0004=Document dates: The document date/time is not numerical
D96ADatesFacture0005=Document dates: The format qualifier of the document date/time is incorrect
D96ADatesFacture0006=Document dates: The length of the document date/time is incorrect
D96ADatesFacture0007=Document dates: The despatch date/time is not numerical
D96ADatesFacture0008=Document dates: The pick-up date/time is not numerical
D96ADatesFacture0009=Document dates: The actual delivery date/time is not numerical
D96ADatesFacture0010=Document dates: The invoicing period is not numerical
D96ADatesFacture0011=Document dates: The invoicing period format qualifier is incorrect
D96ADatesFacture0012=Document dates: The invoicing period length is incorrect
D96ADatesFacture0013=Document dates: The date/time/period information of the document header is missing
D96ADatesFacture0014=Document dates: The month identification in the document date is incorrect
D96ADatesFacture0015=Document dates: The day identification in the document date is incorrect
D96ADatesFacture0016=Document dates: There is an inconsistency between the day and month identifications in the document date
D96ADatesFacture0017=Document dates: The document date is later than today's date
D96ADatesLigne0001=Line dates: The date/time/period qualifier is incorrect
D96ADatesLigne0002=Line dates: The despatch date/time is not numerical
D96ADatesLigne0003=Line dates: The pick-up date/time is not numerical
D96ADatesLigne0004=Line dates: The actual delivery date/time is not numerical
D96ADatesPaiement0001=Payment Terms: The payment terms date is transmitted twice
D96ADatesPaiement0002=Payment Terms: The payment terms date/time qualifier is incorrect
D96ADatesPaiement0003=Payment Terms: The due date is missing
D96ADatesPaiement0004=Payment Terms: The due date is not numerical
D96ADatesPaiement0005=Payment terms: Missing payment date
D96ADatesPaiement0006=Payment terms: The payment date is not a number
D96ADatesReferencesMontantsTotaux0001=Total amounts reference dates: The amount reference date/time qualifier is incorrect
D96ADatesReferencesMontantsTotaux0002=Total amounts reference dates: The prepaid amount date is missing
D96ADatesReferencesMontantsTotaux0003=Total amounts reference dates: The prepaid amount date is not numerical
D96ADescriptionObjet0001=Description: The item description information is missing
D96ADescriptionObjet0002=Description: The item description type is missing
D96ADescriptionObjet0003=Description: The item description code is missing
D96ADescriptionObjet0004=Description: The item description 1st element is missing
D96ADescriptionObjet0005=Description: The first element of the item description is wrong
D96ADevises0002=Currency: The reference currency details qualifier is incorrect
D96ADevises0003=Currency: The reference currency qualifier is incorrect
D96ADevises0004=Currency: The code of the reference currency is incorrect
D96ADevises0005=Currency: The code of the reference currency is incorrect
D96ADevises0006=Currency: The details qualifier of the target currency is incorrect
D96ADevises0007=Currency: The qualifier of the target currency is incorrect
D96ADevises0008=Currency: The code of the target currency is incorrect
D96ADevises0009=Currency: The code of the target currency is incorrect
D96ADevises0010=Currencies: Missing currency information
D96ADevises0011=Currencies: Missing reference currency
D96ADevises0012=Currencies: Wrong qualifier for the reference currency
D96ADevises0013=Currencies: Wrong code for the invoicing currency
D96ADevises0014=Currency: The details qualifier of the target currency is empty
D96AEnteteFacture0001=Interchange header: The interchange sender identification is incorrect
D96AEnteteFacture0002=Interchange header: The interchange recipient identification is incorrect
D96AEnteteFacture0003=Interchange header: The preparation date is not numerical
D96AEnteteFacture0004=Interchange header: The length of the preparation date is incorrect
D96AEnteteFacture0005=Interchange header: The year id in the preparation date is incorrect
D96AEnteteFacture0006=Interchange header: The month id in the preparation date is incorrect
D96AEnteteFacture0007=Interchange header: The day id in the preparation date is incorrect
D96AEnteteFacture0008=Interchange header: There is an inconsistency between the identifications of the day and month in the preparation date
D96AEnteteFacture0009=Interchange header: The preparation time is not numerical
D96AEnteteFacture0010=Interchange header: The length of the preparation time is incorrect
D96AEnteteFacture0011=Interchange header: The hour id in the preparation hour is incorrect
D96AEnteteFacture0012=Interchange header: The minute id in the preparation hour is incorrect
D96AEnteteFacture0013=Interchange header: The interchange control reference is incorrect
D96AEnteteFacture0014=Interchange header: The preparation time is empty
D96AEnteteFacture0014=Interchange header: The preparation date is empty
D96AFindeSection0001=End of section: The section id is incorrect
D96AIdentificationMessage0001=Beginning of message: The document code is incorrect
D96AIdentificationMessage0003=Beginning of message: The document number is incorrect
D96AIdentificationMessage0004=Beginning of message: The message function code is incorrect
D96AIntervenantsFacture0001=Addresses: The partners information is missing
D96AIntervenantsFacture0002=Addresses: The invoicee information is missing
D96AIntervenantsFacture0003=Addresses: The invoicee information is transmitted twice
D96AIntervenantsFacture0004=Addresses: The invoicee identification code is incorrect
D96AIntervenantsFacture0005=Addresses: The 1st element in the invoicee name is incorrect
D96AIntervenantsFacture0006=Addresses: The 1st element in the invoicee address is incorrect
D96AIntervenantsFacture0007=Addresses: The invoicee town name is incorrect
D96AIntervenantsFacture0008=Addresses: The invoicee postcode is incorrect
D96AIntervenantsFacture0009=Addresses: The invoicee country code is incorrect
D96AIntervenantsFacture0010=Addresses: The seller information is missing
D96AIntervenantsFacture0011=Addresses: The seller information is transmitted twice
D96AIntervenantsFacture0012=Addresses: The  seller identification code is incorrect
D96AIntervenantsFacture0013=Addresses: The 1st element in the seller name is incorrect
D96AIntervenantsFacture0014=Addresses: The 1st element in the seller address is incorrect
D96AIntervenantsFacture0015=Addresses: The seller town name is incorrect
D96AIntervenantsFacture0016=Addresses: The seller postcode is incorrect
D96AIntervenantsFacture0017=Addresses: The seller country code is incorrect
D96AIntervenantsFacture0018=Addresses: The seller's corporate office information is transmitted twice
D96AIntervenantsFacture0019=Addresses: The 1st element of the seller's corporate office corporate name is incorrect
D96AIntervenantsFacture0020=Addresses: The 1st element of the seller's corporate office address is incorrect
D96AIntervenantsFacture0021=Addresses: The seller's corporate office town name is incorrect
D96AIntervenantsFacture0022=Addresses: The seller's corporate office postal code is incorrect
D96AIntervenantsFacture0023=Addresses: The seller's corporate office country code is incorrect
D96AIntervenantsFacture0024=Addresses: The information of the party declaring the VAT are transmitted twice
D96AIntervenantsFacture0025=Addresses: The 1st element of the corporate name of the party declaring the VAT is incorrect
D96AIntervenantsFacture0026=Addresses: The 1st element of the address of the party declaring the VAT is incorrect
D96AIntervenantsFacture0027=Addresses: The town name of the party declaring the VAT is incorrect
D96AIntervenantsFacture0028=Addresses: The postal code of the party declaring the VAT is incorrect
D96AIntervenantsFacture0029=Addresses: The country code of the party declaring the VAT is incorrect
D96AIntervenantsFacture0030=Addresses: The information of the party receiving the commercial invoice remittance is missing
D96AIntervenantsFacture0031=Addresses: Wrong ID for the organization in charge of the code list (code type) used for the seller
D96AIntervenantsFacture0032=Addresses: The first element of the seller name and address line is wrong
D96AIntervenantsFacture0033=Addresses: The second element of the seller name and address line is wrong
D96AIntervenantsFacture0034=Addresses: The buyer information is missing
D96AIntervenantsFacture0035=Addresses: Wrong buyer ID code
D96AIntervenantsFacture0036=Addresses: Wrong ID for the organization in charge of the code list (code type) used for the buyer
D96AIntervenantsFacture0037=Addresses: The first element of the buyer name and address line is wrong
D96AIntervenantsFacture0038=Addresses: The second element of the buyer name and address line is wrong
D96AIntervenantsFacture0039=Addresses: The first element of the buyer party name is wrong
D96ALigne0001=Detail: The line number is not numerical
D96AMontantLigneReductionCharge0001=Line allowances/charges: The allowance amount is not numerical
D96AMontantLigneReductionCharge0002=Line allowances/charges: The charge amount is not numerical
D96AMontantLigneReductionCharge0003=Line allowances/charges: The allowance amount is transmitted twice
D96AMontantLigneReductionCharge0004=Line allowances/charges: The charge amount is transmitted twice
D96AMontantLigneReductionCharge0005=Allowance-Charge line: The discount amount is empty
D96AMontantLigneReductionCharge0006=Line allowances/charges: The charge amount is empty
D96AMontantReductionCharge0001=Global allowances/charges: The allowance amount is not numerical
D96AMontantReductionCharge0002=Global allowances/charges: The charge amount is not numerical
D96AMontantReductionCharge0003=Global allowances/charges: The allowance amount is transmitted twice
D96AMontantReductionCharge0004=Global allowances/charges: The charge amount is transmitted twice
D96AMontantReductionCharge0005=Global allowances/charges: The allowance amount is empty
D96AMontantReductionCharge0006=Global allowances/charges: The charge amount is empty
D96AMontants0002=Payment terms: The cash discount amount is not numerical
D96AMontantsTaxes0001=Parafiscal tax breakdown: The tax information amount is missing
D96AMontantsTaxes0002=Parafiscal tax breakdown: The tax amount is missing
D96AMontantsTaxes0003=Parafiscal tax breakdown: The tax amount is transmitted twice
D96AMontantsTaxes0004=Parafiscal tax breakdown: The tax amount is not numerical
D96AMontantsTotaux0001=Total amounts: The information of total amounts is missing
D96AMontantsTotaux0002=Total amounts: The total taxable amount is missing
D96AMontantsTotaux0003=Total amounts: The total taxable amount is transmitted at least in triplicate, or in duplicate without currency codes (1 currency code expected for each amount)
D96AMontantsTotaux0004=Total amounts: The total taxable amount is not numerical
D96AMontantsTotaux0005=Total amounts: The total VAT amount  is missing
D96AMontantsTotaux0006=Total amounts: The second total VAT amount expressed in the currency target is missing
D96AMontantsTotaux0007=Total amounts: The total VAT amount  is transmitted twice whereas no currency target is present
D96AMontantsTotaux0008=Total amounts: The total VAT amount  is not numerical
D96AMontantsTotaux0009=Total amounts: The total amount including taxes is missing
D96AMontantsTotaux0010=Total amounts: The total amount including taxes is transmitted at least in triplicate, or in duplicate without currency codes (1 currency code expected for each amount)
D96AMontantsTotaux0011=Total amounts: The total amount including taxes is not numerical
D96AMontantsTotaux0012=Total amounts: The deposit reference is missing for the total deposit amount
D96AMontantsTotaux0014=Total amounts: The total deposit amount is not numerical
D96AMontantsTotaux0015=Total amounts: The amount due is missing whereas the total deposit amount is transmitted
D96AMontantsTotaux0017=Total amounts: The amount due is not numerical
D96AMontantsTotaux0018=Total amounts: Missing VAT total amount
D96AMontantsTotaux0019=Total amounts: The VAT total amount is not a number
D96AMontantsTotaux0020=Total amounts: Missing total amount
D96AMontantsTotaux0021=Total Amounts: The total amount is not a number
D96AMontantsTotaux0022=Total amounts: The item line total amount is missing
D96AMontantsTotaux0023=Total amounts: The item line total amount is not a number
D96AMontantsTotaux0024=Total amounts: The discount amount is not a number
D96AMontantsTotaux0025=Total amounts: The total VAT amount  is transmitted at least in triplicate, or in duplicate without currency codes (1 currency code expected for each amount)
D96AMontantsTotaux0027=Total amounts: The total deposit amount is missing whereas the amount due is transmitted
D96AMontantsTva0001=VAT breakdown amounts: The tax information amount is missing
D96AMontantsTva0002=VAT breakdown amounts: The tax amount is missing
D96AMontantsTva0003=VAT breakdown amounts: The tax amount is transmitted in duplicate
D96AMontantsTva0004=VAT breakdown amounts: The tax amount is not numerical
D96AMontantsTva0005=VAT breakdown: One of the indicated amounts is not a VAT amount
D96AMontantsTva0006=VAT breakdown amount: Missing taxable amount
D96AMontantsTva0007=VAT breakdown amount: The taxable amount is not a number
D96AMontantsTva0008=VAT breakdown amount: Missing tax amount
D96AMontantTaxeLigne0001=Line VAT amount: The charge qualifier (ALC+C) of the charge amount (MOA+23) is missing
D96APourcentageLigneReduction0001=Line allowances/charges: The allowance percentage is not numerical
D96APourcentageLigneReduction0002=Line allowances/charges: The charge percentage is not numerical
D96APourcentageLigneReduction0003=Allowance-Charge line: The discount percentage is empty
D96APourcentageLigneReduction0004=Line allowances/charges: The charge percentage is empty
D96APourcentageReductionCharge0001=Global allowances/charges: The allowance percentage is not numerical
D96APourcentageReductionCharge0002=Global allowances/charges: The charge percentage is not numerical
D96APourcentageReductionCharge0003=Global allowances/charges: The allowance percentage is empty
D96APourcentageReductionCharge0004=Global allowances/charges: The charge percentage is empty
D96APourcentages0002=Payment terms: The cash discount percentage is not numerical
D96APourcentages0003=Payment terms: The penalty percentage is not numerical
D96APourcentages0004=Payment terms: An incoherence exists between the cash discount indicator and the percentage type
D96APourcentages0005=Payment terms: An incoherence exists between the penalty indicator and the percentage type
D96APrix0001=Price: The price information is missing
D96APrix0002=Price: The net price is missing
D96APrix0003=Price: The net price is transmitted twice
D96APrix0004=Price: The net price is not numerical
D96APrix0005=Price: The gross price is missing whereas a discount/charge of line is present
D96APrix0005bis=Price: The gross price is missing
D96APrix0006=Price: The gross price is transmitted twice
D96APrix0007=Price: The gross price is not numerical
D96APrix0008=Price: The WT net unit price basis is not a number
D96APrix0009=Price: The WT net price unit of measure is wrong
D96AQuantites0001=Line quantity: The information of quantity is missing
D96AQuantites0002=Line quantity: The quantity qualifier is incorrect
D96AQuantites0003=Line quantity: The invoiced quantity qualifier is missing
D96AQuantites0004=Line quantity: The invoiced quantity qualifier is transmitted twice
D96AQuantites0005=Line quantity: The invoiced quantity is not numerical
D96AQuantites0006=Item quantity: The invoiced quantity unit of measure is wrong
D96ARecapTaxesParafiscales0001=Parafiscal tax breakdown: The charge qualifier is incorrect
D96ARecapTaxesParafiscales0003=Parafiscal tax breakdown: The 1st element of the parafiscal tax description is incorrect
D96AReductionCharge0001=Global allowances/charges: The allowance/charge settlement code is incorrect
D96AReductionCharge0002=Global allowances/charges: The 1st element of the allowance/charge description is incorrect
D96AReductionCharge0003=Global allowances/charges: The allowance percentage and amount are missing
D96AReductionCharge0004=Global allowances/charges: The charge percentage and amount are missing
D96AReductionCharge0005=Global allowances/charges: The allowance/charge qualifier is incorrect
D96AReductionCharge0006=More than one percentage for global allowance (PCD+1)
D96AReductionCharge0007=More than one percentage for global charge (PCD+2)
D96AReductionChargeLigne0001=Line allowances/charges: The allowance/charge settlement code is incorrect
D96AReductionChargeLigne0002=Line allowances/charges: The 1st element of the allowance/charge description is incorrect
D96AReductionChargeLigne0003=Line allowances/charges: The allowance percentage and amount are missing
D96AReductionChargeLigne0004=Line allowances/charges: The charge percentage and amount are missing
D96AReductionChargeLigne0005=Line allowances/charges: The allowance/charge qualifier is incorrect
D96AReductionChargeLigne0006=More than one percentage for line allowance (PCD+1)
D96AReductionChargeLigne0007=More than one percentage for line charge (PCD+2)
D96AReductionChargeLigne0008=Surely a missing line allowance/charge data (a PCD segment is following directly a TAX segment in group 33)
D96AReductionChargeLigne0010=Allowance-Charge line: The code for the discount special service is wrong
D96AReductionChargeLigne0011=Allowance-Charge line: Missing discount percentage
D96AReductionChargeLigne0012=Allowance-Charge line: Missing discount amount
D96AReference0001=Global references: The invoice number and the period of validity/invoicing (document itself or contract) in reference in the credit note are missing
D96AReference0002=Global references: The invoice number in reference is incorrect
D96AReference0003=Global references: The contract number in reference is incorrect
D96AReferenceLigne0001=Line references: The referenced delivery note/despatch advice number is missing
D96AReferenceLigne0002=Line references: The referenced delivery note/despatch advice number is wrong
D96AReferencesIntervenant0001=Global address references: The reference qualifier is incorrect
D96AReferencesIntervenant0002=Global address references: The company registration number (RCS-RCM) of the seller is missing
D96AReferencesIntervenant0003=Global address references: The company registration number (RCS-RCM) of the seller is transmitted twice
D96AReferencesIntervenant0004=Global address references: The company registration number (RCS-RCM) of the seller is incorrect
D96AReferencesIntervenant0005=Global address references: The VAT registration number of the invoicee out of France is missing
D96AReferencesIntervenant0006=Global address references: The VAT registration number of the invoicee is transmitted twice
D96AReferencesIntervenant0007=Global address references: The VAT registration number of the invoicee is incorrect
D96AReferencesIntervenant0008=Global address references: The VAT registration number of the seller is missing
D96AReferencesIntervenant0009=Global address references: The VAT registration number of the seller is transmitted twice
D96AReferencesIntervenant0010=Global address references: The VAT registration number of the seller is incorrect
D96AReferencesIntervenant0011=Global address references: The VAT registration number of the party declaring the VAT is missing
D96AReferencesIntervenant0012=Global address references: The VAT registration number of the party declaring the VAT is transmitted twice
D96AReferencesIntervenant0013=Global address references: The VAT registration number of the party declaring the VAT is incorrect
D96AReferencesIntervenant0014=Global address references: The government reference number (SIREN) and the VAT registration number of the French invoicee are missing
D96AReferencesIntervenant0015=Global address references: The government reference number (SIREN) of the invoicee is transmitted twice
D96AReferencesIntervenant0016=Global address references: The government reference number (SIREN) of the invoicee is incorrect
D96AReferencesIntervenant0018=Global address references: The government reference number (SIREN) of the seller is transmitted twice
D96AReferencesIntervenant0019=Global address references: The government reference number (SIREN) of the seller is incorrect
D96AReferencesIntervenant0020=Global address references: The company registration number (RCS-RCM) of the seller's corporate office is missing
D96AReferencesIntervenant0021=Global address references: The company registration number (RCS-RCM) of the seller's corporate office is incorrect
D96AReferencesIntervenant0022=Global address references: The VAT registration number of the seller's corporate office is missing
D96AReferencesIntervenant0023=Global address references: The VAT registration number of the seller's corporate office is incorrect
D96AReferencesIntervenant0024=Global address references: The government reference number (SIREN) of the invoicee is missing
D96AReferencesIntervenant0025=Global address references: The government reference number (SIREN) of the seller is missing
D96AReferencesIntervenant0026=Global address references: The government reference number (SIREN) of the seller's corporate office is missing
D96AReferencesIntervenant0027=Global address references: The government reference number (SIREN) of the seller's corporate office is incorrect
D96AReferencesIntervenant0028=Global address references: The VAT registration number of the invoicee is missing
D96AReferencesMontantsTotaux0001=Total amounts references: The deposit number in reference is incorrect
D96ARegimesTva0001=Tax duty: An incoherence exists between the tax exemption code and the header comment describing the motive of the tax exemption
D96ATaxes0001=Global allowances/charges: The tax information is missing
D96ATaxes0002=Global allowances/charges: The tax function qualifier [7] is missing
D96ATaxes0003=Global allowances/charges: The type tax code [VAT] associated with the tax function qualifier [7] is missing
D96ATaxes0004=Global allowances/charges: The tax rate is not numerical
D96ATaxes0004b=Global allowances/charges: The tax rate is missing
D96ATaxes0008=Global allowances/charges: The tax rate is missing in the tax breakdown in the message foot
D96ATaxesLigne0001=Line VAT: The tax information is missing
D96ATaxesLigne0002=Line VAT: The tax function qualifier [7] is missing
D96ATaxesLigne0003=Line VAT: The tax type code [VAT] associated with the tax function qualifier [7] is missing
D96ATaxesLigne0004=Line VAT: The tax rate is not numerical
D96ATaxesLigne0004b=Line VAT: The tax rate is missing
D96ATaxesLigne0007=Line VAT: The tax information is transmitted twice
D96ATaxesLigne0008=Line VAT: The tax rate is missing in the tax breakdown in the message foot
D96ATaxesLigne0009=VAT line: Wrong code for the VAT category
D96ATaxesLigneReductionCharge0001=Line allowances/charges: The tax function qualifier [7] is missing
D96ATaxesLigneReductionCharge0002=Line allowances/charges: The type tax code [VAT] associated with the tax function qualifier [7] is missing
D96ATaxesLigneReductionCharge0003=Line allowances/charges: The tax rate is not numerical
D96ATaxesLigneReductionCharge0005=Line allowances/charges: The tax information is transmitted twice
D96ATaxesLigneReductionCharge0006=Line allowances/charges: The tax rate is missing in the tax breakdown in the message foot
D96ATypeMessage0001=Header message: The reference number of the message is incorrect
D96ATypeMessage0002=Header message: The message type identifier incorrect
D96ATypeMessage0003=Header message: The message type version number is incorrect
D96ATypeMessage0004=Header message: The message type release number is incorrect
D96ATypeMessage0005=Header message: The controlling agency is incorrect
D96ATypeMessage0006=Header message: The association assigned code is incorrect
D96AVentilationsTva0001=VAT breakdown: The tax information is missing
D96AVentilationsTva0002=VAT breakdown: The tax function qualifier [7] is missing
D96AVentilationsTva0003=VAT breakdown: The type tax code [VAT] associated with the tax function qualifier [7] is missing
D96AVentilationsTva0004=VAT breakdown: The taxable amount is not numerical
D96AVentilationsTva0004b=VAT breakdown: The taxable amount is missing
D96AVentilationsTva0005=VAT breakdown: The tax rate is not numerical
D96AVentilationsTva0006=VAT breakdown: The tax rate is missing
D96AVentilationsTva0007=VAT breakdown: The tax rate is missing in the rest of the message
D96AVentilationsTva0008=VAT breakdown: Wrong tax category code
D96AVentilationsTva0009=VAT breakdown: The tax rate is transmitted twice

D96AEnteteFacture0015 = Interchange header: The preparation date is empty