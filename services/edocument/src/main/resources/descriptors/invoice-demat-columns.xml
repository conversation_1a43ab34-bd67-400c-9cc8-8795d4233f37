<?xml version="1.0" encoding="UTF-8"?>
<descriptor name="InvoiceDemat" sortAsc="false" version="5">
    <properties>
        <property editable="true" path="reference" name="reference"
                  readonly="false" rendered="false" required="false" sortable="true"
                  type="java.lang.String">
            <label>reference</label>
        </property>
        <property name="from" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false"
                  sortable="true">
            <label>from</label>
        </property>
        <property name="to" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false"
                  sortable="true">
            <label>to</label>
        </property>
        <property name="creationDate" type="java.util.Date" rendered="false" readonly="false" editable="true"
                  required="false" sortable="true">
            <label>creationDate</label>
        </property>
        <property name="owners" type="java.lang.String" rendered="false" readonly="false" editable="true"
                  required="false" sortable="true">
            <label>owners</label>
        </property>
        <property name="status" type="com.byzaneo.xtrade.api.DocumentStatusEntityInterface" rendered="true"
                  readonly="false" editable="true" required="false" sortable="true">
            <label>status</label>
        </property>
        <property name="type" type="java.lang.String" rendered="false" readonly="false" editable="true" required="false"
                  sortable="true">
            <label>type</label>
        </property>
        <property editable="true" path="consultStatus" name="consultStatus"
                  readonly="false" render-restrict="None" rendered="false" required="false"
                  sortable="true" type="com.byzaneo.xtrade.api.DocumentConsultStatus"
                  styleClass="iconColumn">
            <label>consultStatus</label>
            <options-expression>#{cc.attrs.taskBean.getValues('consultStatus')}</options-expression>
        </property>
        <property name="acquisition" type="com.byzaneo.xtrade.bean.Document.AcquisitionType" rendered="false"
                  render-restrict="None"
                  readonly="true" path="acquisition" editable="false" required="false" sortable="true">
            <label>acquisition</label>
            <options-expression>#{cc.attrs.taskBean.getValues('acquisition')}</options-expression>
        </property>
        <property editable="true" name="processingWay" path="processingWay"
                  readonly="false" render-restrict="None" rendered="false" required="false"
                  sortable="true" type="com.byzaneo.xtrade.bean.Document.ProcessingWay">
            <label>processingWay</label>
            <options-expression>#{cc.attrs.taskBean.getValues('processingWay')}</options-expression>
        </property>
        <property name="translator" type="java.lang.String" sortable="false" rendered="false">
            <label>translator</label>
        </property>
        <property name="transport" type="java.lang.String" sortable="false" rendered="false">
            <label>transport</label>
        </property>
        <property name="modificationDate" type="java.util.Date" rendered="false" readonly="false" editable="true"
                  required="false" sortable="true">
            <label>modificationDate</label>
        </property>
		<property name="thirdParty"
				  readonly="false" rendered="false" required="false" sortable="true"
				  type="java.lang.String">
			<label>thirdParty</label>
		</property>
        <property name="nature" type="java.lang.String" sortable="true" rendered="false" required="false">
            <label>nature</label>
        </property>
        <property name="immatpdp" type="java.lang.String" sortable="true" rendered="false" required="false">
            <label>immatpdp</label>
        </property>
        <property name="b2gRules" type="java.lang.String" sortable="true" rendered="false" required="false">
            <label>b2gRules</label>
        </property>
        <property name="ereportingOption" type="java.lang.String" sortable="true" rendered="false" required="false">
            <label>ereportingOption</label>
        </property>
        <property name="countryRules1" type="java.lang.String" sortable="true" rendered="false" required="false">
            <label>countryRules1</label>
        </property>
        <property name="countryRules2" type="java.lang.String" sortable="true" rendered="false" required="false">
            <label>countryRules2</label>
        </property>
    </properties>
</descriptor>