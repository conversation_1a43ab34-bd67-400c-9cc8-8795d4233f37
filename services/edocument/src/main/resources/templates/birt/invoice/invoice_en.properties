#
#Wed Jun 17 16:18:00 CEST 2015
bill_to=Bill to:
buyer=Buyer:
seller=Seller:
ship_to=Delivered to
ship_from=Ship from
remit_to=Remit to
invoice_date=Issue date
invoicing_period=Invoicing period
due_date=Invoice due date
tax_point_date=Tax point date
devise=Currency
order_number=Order #
actual_ship_date=Ship date
actual_delivery_date=Delivery date
collection_date=Collection date
operation_date=Operation date
other_entities=Other parties
role=Role
id=Identifier
name=Name
street=Street
zip_code=Postal code
city=City
country=Country
contact_name=Contact name:
tax_id=Tax Id.:
registered_name=Company name:
billing_framework=Billing framework
header_allowances_charges=Allowances or charges
line_allowances_charges=Inline allowances or charges
type=Type
description=Description
value=Value
unit_value=Unit value
line_num=Line #
buyer_reference=Buyer Ref.
seller_reference=Seller Ref.
ean=Product code
included_in=included in
including=Including:
added_to=added to
deleted_from=deleted from
quantity=Invoiced Qty
free_quantity=Free Qty
returned_quantity=Returned Qty
number_of_units=Number of units
unit=Unit
net_unit_price=Unit price
gross_unit_price=Gross price
net_amount=Net amount
including_tax_amount=Amount
vat=VAT
list_tax_summary=VAT summary
taxable_amount=Taxable amount
tax_amount=Tax amount
list_ac_summary=Allowances or charges summary
sub_total_amount=Lines total:
allowance_total_amount=Allowance total:
charge_total_amount=Charges total:
net_total_amount=Total amount without VAT:
tax_total_amount=Total VAT amount:
total_amount=Total amount with VAT:
prepaid_amount=Paid amount:
total_rounding_amount=Rounding amount:
total_amount_payable=Amount due for payment:
references=References
number=Number
date=Date
start_date=[Start] Date
end_date=End date
none=No item
notes=Notes:
comments=Comments
payment_instructions=Payment terms
discount_terms=Discount terms
discount_date=until
payment_terms_note=Late payment
payment_mean=Payment mean
account_id=Account Id:
iban_2=IBAN:
account_name=Name of the account holder:
financial_institution_id=Financial institution Id:
financial_institution_name=Financial institution name:
card_info=Payment card information:
dated=dated
payment_title=Payments
payment_amount=Amount
payment_date=Date
payment_mean_2=Payment mean
payment_other_info=Reference
vat_management_method=VAT management method
vat_exemption_conditions=VAT exemption conditions
vat_currency=VAT currency:
invoicing_currency=Invoicing currency:
reference_currency=Reference currency:
target_currency=Target currency:
exchange_rate=Exchange rate:
exchange_rate_date=Exchange rate date:
country_of_origine=Country of origine
list_of_attachment=List of attachment
SignatureCertificatePublicKey=Signature certificate public key

#BillingType
LBPS=Double
PB=Delivery of goods
PS=Provision of services
B1=Goods invoice
S1=Service invoice
M1=Double invoice (good and service not incidental to each other)
B2=Goods invoice already paid
S2=Service invoice already paid
M2=Double invoice already paid
S3=Service invoice outsourced with direct payment
B4=Goods invoice - final (after deposit)
S4=Service invoice - final (after deposit)
M4=Double invoice - final (after deposit)
S5=Service invoice submitted by a subcontractor
S6=Service invoice submitted by a co-contractor
B7=Goods invoice subject to e-reporting (VAT already collected)
S7=Service invoice subject to e-reporting (VAT already collected)

#IndicatorCoded
Allowance=Allowance
Charge=Charge
LineItemAllowance=Allowance
LineItemCharge=Charge
Service=Tax-regulatory tax
Other=Other

ACCOUNT_NUMBER=Buyer ref.
CONTRACT=Contract
COST_ALLOCATION=Buyer accounting ref.
ORDER=Order
DELIVERY_NOTE=Delivery note
DISPATCH_ADVICE=Dispatch advice
INVOICE=Invoice
DEBIT_NOTE=Debit note

#ReferenceTypeCoded
#ReferenceTypeCodedOther
AccountNumber=Account number
AdditionalReferenceNumber=Additional ref. number
APE=APE id
AQQ=APE id
CustomerReferenceNumber=Cust. ref. number
DespatchAdviceNumber=Dispatch advice
OrderResponseNumber=Order resp. #
OriginatorReference=Originator ref.
PriceListNumber=Price list
ProjectNumber=Project #
ReceiptNumber=Receipt #
RequestforQuotationReference=Request for quotation
380=Invoice
381=Credit note

#InvoiceType
CommercialInvoice=Invoice
CommissionNote=Commission credit note
ConsolidatedInvoice=Consolidated invoice
CorrectedInvoice=Corrected invoice
CreditInvoice=Credit invoice
CreditMemo=Credit memo
CreditNoteFinancialAdjustment=Credit note for financial adjustment
CreditNoteGoodsAndServices=Credit note
DebitInvoice=Debit invoice
DebitMemo=Debit memo
DebitNoteFinancialAdjustment=Debit note for financial adjustment
DebitNoteGoodsAndServices=Debit note
DelcredereInvoice=Delcredere invoice
DetourBilling=Detour billing
FactoredCreditNote=Factored credit note
FactoredInvoice=Factored invoice
InvoicingDataSheet=Invoicing data sheet
MeteredServicesInvoice=Metered services invoice
PrepaymentInvoice=Prepayment invoice
ProformaInvoice=Pro forma invoice
SelfBilledCreditNote=Self-billed credit note
SelfBilledInvoice=Self-billed invoice
ThirdPartyConsolidatedInvoice=Third party consolidated invoice

#InvoicePurposeCoded
Original=original
Duplicate=duplicate

#PartyRole
AgentOrRepresentative=Buyer agent
Beneficiary=Beneficiary
BillTo=Bill to
BuyersAgentOrRepresentative=Buyer agent
DeclarantsAgentOrRepresentative=Declarants agent or representative
DeliveryParty=Delivery party
DocumentOrMessageIssuerOrSender=Document issuer
DocumentRecipient=Document recipient
Factor=Factor
FinalRecipient=Invoice recipient party
HeadOffice=Head office
PartyToReceiveCommercialInvoiceRemittance=Paid at
PartytoReceiveInvoiceFOrGoodsOrServices=Paid at
Payee=Payee
Payer=Invoice payer
RemitTo=Remit to party
SellersAgentOrRepresentative=Seller agent
ShipFrom=Ship from
ShipTo=Ship to
SoldTo=Sold to
TaxAuthority=Tax authority

#ContactNumberTypeCoded
#ContactNumberTypeCodedOther
EmailAddress=Email
MobileNumber=Mobile phone
PhoneNumber=Phone
TelephoneNumber=Phone
TelexNumber=T\u00E9lex

#AgencyCodedOther
CodeRoutage=Routing code
CSE=CSE
LigneAdressage=Access point
NumBankAccount=Bank Account number
RCS-RCM=RCS-RCM

#CodeListIdentifierCoded
BusinessLegalStructureType=Legal Structure
CapitaleSociale=Legal Capital
ValueAddedTaxIdentification=Tax Id.

#CodeListIdentifierCodedOther
IBAN=IBAN
LegalCapital=Legal Capital
SIREN=SIREN
SIRET=SIRET
0223=EU (outside of France) Id.
0226=Individual Id.
0227=Outside EU Id.
0228=RIDET Id.
0229=Tahiti Id.

#GeneralNote
GoodsRelatedInvoice=Goods related invoice

#TextTypeCodedOther
AAB=Terms of payments
AAI=General information
AAR=Terms of delivery
ABL=Legal information
ABX=Document name
AET=Instructions to the paying
CUS=Customs declaration information
GeneralInformation=General information
INV=Invoice instruction
ITS=Testing instructions
PMD=Payment detail/remittance information
PMT=Payment information
PRD=Product information
REG=Regulatory information
SIN=Special instructions
SupplierRemarks=Supplier remarks
Suppliersremarks=Supplier remarks
SUR=Supplier remarks
TXD=Seller

ZZZ=Mutually defined

#TermsOfDeliveryFunctionCoded
DeliveryCondition=Delivery condition
PriceandDespatchCondition=Price and despatch condition

#TransportTermsCoded
Carriage-InsurancePaidTo=Carriage and Insurance paid to destination
CarriagePaidTo=Carriage paid to destination
Cost-InsuranceAndFreight=Cost, Insurance and Freight
CostAndFreight=Cost and Freight
DeliveredAtFrontier=Delivered At Frontier
DeliveredDutyPaid=Delivered Duty Paid
DeliveredDutyUnpaid=Delivered duty unpaid
DeliveredEx-Quay=Delivered Ex Quay - Duty paid
DeliveredEx-Ship=Delivered Ex ship
Ex-Works=Ex Works
Free-Carrier=Free Carrier
FreeAlongsideShip=Free Alongside Ship
FreeOnBoard=Free On Board

#PaymentMeanCoded
#PaymentMeanCodedOther
BillDrawnByTheCreditorOnTheDebtor=Bill drawn by the creditor on the debtor
CertifiedCheque=Certified cheque
Cheque=Cheque
CreditTransfer=Credit transfer
DirectDebit=Direct debit
PaymentToBankAccount=Payment to bank account
PromissoryNote=Promissory note
RelatedDetailAccount=Related detail account
10=In cash
15=Bookentry credit
16=Bookentry debit
20=Cheque
21=Banker's draft
23=Bank cheque
25=Certified cheque
30=Credit transfer
31=Debit transfer
42=Payment to bank account
49=Direct debit
50=Payment by postgiro
60=Promissory note
70=Bill drawn by the creditor on the debtor

#MethodOfHandlingCoded
AllowanceToBeIssuedByVendor=On invoice
CalculateAndAddtoInvoice=On invoice
ChargeToBePaidByCustomer=On invoice
OffInvoice=Off invoice
OffInvoiceDeductionFromOriginalInvoice=Off invoice

#AllowOrChargeTreatmentCodedOther
FirstStepOfCalculation=Step 1
SecondStepOfCalculation=Step 2
ThirdStepOfCalculation=Step 3
FourthStepOfCalculation=Step 4
FifthStepOfCalculation=Step 5
SixthStepOfCalculation=Step 6
SeventhStepOfCalculation=Step 7
EighthStepOfCalculation=Step 8
NinthStepOfCalculation=Step 9

#ServiceCoded
#ServiceCodedOther
AdvertisingAllowance=Advertising allowance
Delivery=Delivery
FullTruckloadAllowance=Full truckload allowance
GoodsAndServicesCharge=Goods and services charge
GoodsAndServicesCreditAllowance=Goods and services credit allowance
GroupageDiscount=Groupage discount
Packaging=Packaging
PromotionalAllowance=Promotional allowance
ReturnedLoad=Returned load
ServiceChargeWithCashDiscount=Service charge with cash discount
Tax-RegulatoryTax=Tax - Regulatory tax
TradeDiscount=Trade discount

#LineItemTypeCodedOther
ReturnableContainer=Returnable container
VariableQuantityProduct=Variable quantity product

#ProductIdentifierQualifierCoded
#ProductIdentifierQualifierCodedOther
BrandOrLabel=Brand
CommodityName=Product Name
End-ItemDescription=Description
PromotionalVariantNumber=Promotional variant
SerialNumber=Serial number

#QuantityQualifierCoded
NumberOfUnits=Number of units
ChargeableNumberOfUnits=Chargeable number of units

#UOMCoded[Other]
C62=ONE
MON=MON

#PriceTypeCoded
DiscountAmountAllowed=Price discount
NetItemPrice=Net item price
UnitCostPrice=Unit cost price

#DimensionCoded
TotalGrossWeight=Total gross weight
TotalNetWeight=Total net weight

#TaxCategoryCoded
MixedTaxRate=Mixed

#TaxTreatmentCodedOther
DEB=VAT on debits
ENC=VAT on collections
EXO=VAT exemption
NDT=Net of tax
NoTaxApplies=No tax applies

#InvoiceExtraTotals
DepositTotalAmount=Deposit total

#CountryCoded
AD=ANDORRA
AT=AUSTRIA
BE=BELGIUM
CH=SWITZERLAND
CY=CYPRUS
DE=GERMANY
DK=DENMARK
ES=SPAIN
FI=FINLAND
FR=FRANCE
GB=UNITED KINGDOM
GF=FRENCH GUIANA
GP=GUADELOUPE
GR=GREECE
IE=IRELAND
IS=ICELAND
IT=ITALY
LI=LIECHTENSTEIN
LU=LUXEMBOURG
MC=MONACO
MQ=MARTINIQUE
MT=MALTA
NC=NEW CALEDONIA
NL=NETHERLANDS
NO=NORWAY
PF=FRENCH POLYNESIA
PL=POLAND
PM=SAINT PIERRE AND MIQUELON
PT=PORTUGAL
RE=REUNION
RO=ROUMANIA
RU=RUSSIAN FEDERATION
SE=SWEDEN
SM=SAN MARINO
VA=HOLY SEE
WF=WALLIS AND FUTUNA
YT=MAYOTTE
223 = EU (outside of France) Id.
226 = Individual Id.
227 = Outside EU Id.
228 = RIDET Id.
229 = Tahiti Id.