bill_to = Facturado a
seller = Vendedor:
country = Pa\u00EDs
buyer_ordnum = N.\u00BA de pedido del comprador:
role = Rol
buyer_partid = Comprador ref.
seller_partid = Ref. vendedor
city = Ciudad
transport_means_id = Matr\u00EDcula:
asn_date = Fecha BL:
zip_code = C\u00F3digo postal
buyer_tax_id = N.\u00BA IVA comprador:
seller_tax_id = N\u00FAmero de IVA del vendedor:
received_um = Unidad
Carrier = Transportista
id = Identificador
transport_mode = Modo de transporte:
difference_qty = Dif. de cant.
transport_means = Medio de transporte:
ship_from = Enviado desde
po_date = Fecha de pedido:
line_num = N.\u00BA de l\u00EDnea
received_qty = Cantidad recibida
receipt_id = N\u00FAmero de recepci\u00F3n:
ship_to_tax_id = N.\u00BA IVA lugar de entrega:
buyer = Comprador:
name = Apellido
receipt_date = Fecha de recepci\u00F3n:
difference_um = Unidad
seller_ordnum = N\u00FAmero de pedido:
asn_refnum = N.\u00BA BL:
ship_to = Lugar de entrega: