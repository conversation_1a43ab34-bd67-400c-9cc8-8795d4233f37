<?xml version="1.0" encoding="UTF-8"?>
<rules xmlns="http://www.example.org/constraints"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.example.org/constraints constraints.xsd"
    context="//InvoiceSummary" label="invoicesummary">

    <!-- INVOICE TOTALS FIELD -->
    <field label="invoicetotals" path="InvoiceTotals">
        <!-- Rule FR: G6.08 -->
        <!-- element BG-22 -->
        <constraints>
            <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
        </constraints>
    </field>

    <!-- INVOICE TOTALS SECTION -->
    <section label="invoicetotals" step="InvoiceTotals"
        description="validation of Invoice totals">
        <!-- BT-109 -->
        <!--Rule BR-13BR-CO-13-->
        <field path="TaxableValue/MonetaryAmount" description="Montant taxable"
            label="taxable_amount">
            <constraints>
                <constraint
                    legislation="EN, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SumConsistencyConstraint">
                    <arg value="//InvoiceSummary/ListOfTaxSummary/TaxSummary/TaxableAmount" />
                </constraint>
            </constraints>
        </field>

        <field path="InvoiceTotal/MonetaryAmount" description="Montant total TTC"
            label="total_amount">
            <!-- BT-112 -->
            <!--Rule BR-14BR-CO-15-->
            <constraints>
                <constraint legislation="EN, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN,FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SameContentConstraint">
                    <!--BT-121-->
                    <precondition expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxExemptCode='VATEX-EU-D' or TaxExemptCode='VATEX-EU-I' or TaxExemptCode='VATEX-EU-J' or TaxExemptCode='VATEX-EU-F']" />
                    <!--BT-116-->
                    <arg value="//InvoiceSummary/ListOfTaxSummary/TaxSummary/TaxableAmount" />
                </constraint>
            </constraints>
        </field>
        <!-- BT-110 -->
        <!--Rule BR-CO-14-->
        <field path="TotalTaxAmount/MonetaryAmount" description="Montant de taxe total en euros"
            label="monetary_amount">
            <constraints>
                <constraint legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, EU, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TotalTaxAmount" />
                </constraint>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TotalTaxAmount" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TotalTaxAmount" />
                    <arg value="2" />
                </constraint>
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                    <!--BT-121-->
                    <precondition expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxExemptCode='VATEX-FR-FRANCHISE' or TaxExemptCode='VATEX-EU-D' or TaxExemptCode='VATEX-EU-I' or TaxExemptCode='VATEX-EU-J' or TaxExemptCode='VATEX-EU-F']" />
                    <arg value="0" />
                </constraint>
            </constraints>
        </field>

        <field path="TotalAmountPayable/MonetaryAmount" description="Montant restant à payer"
            label="total_amount_payable">
            <!-- BT-115 -->
            <!--Rule BR-15BR-CO-16-->
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                    <precondition expression="//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement/RequirementReference[RefNum='B2' or RefNum='M2' or RefNum='S2']" />
                    <arg value="0" />
                </constraint>
            </constraints>
        </field>

        <!--BT-106-->
        <!--Rule BR-12BR-CO-10-->
        <field path="InvoiceSubTotal/MonetaryAmount" label="invoice_subtotal_monetary_amount"
            description="Invoice subtotal monetary amount">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <!--constraint contained in G1.59 also-->
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
                <!--constraint contained in G1.59 also-->
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SumConsistencyConstraint">
                    <!--BT-131-->
                    <arg value="//InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/LineItemTotal/MonetaryAmount" />
                    <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
                </constraint>
            </constraints>
        </field>
        <!--BT-107-->
        <!--Rule BR-CO-11-->
        <field path="AllowanceTotal/MonetaryAmount" label="allowance_total_monetary_amount"
            description="Allowance total monetary amount">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/AllowanceTotal" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/AllowanceTotal" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/AllowanceTotal" />
                    <arg value="2" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SumConsistencyConstraint">
                    <arg value="//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge[IndicatorCoded='Allowance']/TypeOfAllowanceOrCharge/MonetaryValue/MonetaryAmount" />
                </constraint>
            </constraints>
        </field>
        <!--BT-108-->
        <!--Rule BR-CO-12-->
        <field path="ChargeTotal/MonetaryAmount	" label="charge_total_monetary_amount"
            description="Charge total monetary amount">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/ChargeTotal" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/ChargeTotal" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/ChargeTotal" />
                    <arg value="2" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SumConsistencyConstraint">
                    <arg value="//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge[IndicatorCoded='Charge']/TypeOfAllowanceOrCharge/MonetaryValue/MonetaryAmount" />
                </constraint>
            </constraints>
        </field>
        <!--BT-111-->
        <!--Rule BR-53-->
        <field path="TaxableValueInTaxAccountingCurrency/MonetaryAmount" label="total_VAT_amount"
            description="Total VAT amount of the invoice expressed (accounting currency)">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TaxableValueInTaxAccountingCurrency" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TaxableValueInTaxAccountingCurrency" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TaxableValueInTaxAccountingCurrency" />
                    <arg value="2" />
                </constraint>
                <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                    <!--BT-121-->
                    <precondition expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxExemptCode='VATEX-FR-FRANCHISE' or TaxExemptCode='VATEX-EU-D' or TaxExemptCode='VATEX-EU-I' or TaxExemptCode='VATEX-EU-J' or TaxExemptCode='VATEX-EU-F']" />
                    <arg value="0" />
                </constraint>
            </constraints>
        </field>
        <!--BT-113-->
        <field path="PrepaidAmount/MonetaryAmount" label="payroll_amount" description="Payroll amount">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/PrepaidAmount" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/PrepaidAmount" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/PrepaidAmount" />
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
        <!--BT-114-->
        <field path="TotalRoundingAmount/MonetaryAmount" label="rounded_amount" description="Rounded amount">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TotalRoundingAmount" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TotalRoundingAmount" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="//InvoiceSummary/InvoiceTotals/TotalRoundingAmount" />
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
    </section>

    <!-- LIST OF TAX SUMMARY SECTION -->
    <section label="listoftaxsummary" step="ListOfTaxSummary" required="false">

        <!-- BG-23 -->
        <field path="TaxSummary" label="taxsummary_header">
            <constraints>
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <!-- BR-47 and G6.08 for BT-118 in fact -->
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.AllDescendantsNotEmptyConstraint">
                    <arg value="TaxTypeCodedOther" />
                </constraint>
            </constraints>
        </field>

        <!-- BT-118 -->
        <field path="TaxSummary[TaxTypeCoded='Other']/TaxTypeCodedOther" label="taxsummary">
            <constraints>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="S" />
                    <arg value="E" />
                    <arg value="AE" />
                    <arg value="K" />
                    <arg value="G" />
                    <arg value="O" />
                </constraint>
                <!-- G1.56 in association with BG-23 -->
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                    <arg value="E" />
                    <precondition expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxExemptCode='VATEX-EU-I' or TaxExemptCode='VATEX-EU-J' or TaxExemptCode='VATEX-EU-D' or TaxExemptCode='VATEX-EU-F' or TaxExemptCode='VATEX-FR-FRANCHISE']" />
                </constraint>
            </constraints>
        </field>

        <field path="TaxSummary/TaxExemptReason" label="tax_exempt_reason">
            <!-- BT-120 -->
            <constraints>
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <!-- G1.40 -->
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxTypeCodedOther='E']" />
                    <precondition expression="(//InvoiceHeader/InvoiceType[InvoiceTypeCoded != 'CorrectedInvoice']
                        and //InvoiceHeader/InvoiceType[InvoiceTypeCoded != 'SelfBilledCorrectedInvoice']
                        and //InvoiceHeader/InvoiceType[InvoiceTypeCoded != 'FactoredCorrectedInvoice'] 
                        and //InvoiceHeader/InvoiceType[InvoiceTypeCoded != 'SelfBilledFactoredCorrectedInvoice']
                        and number(//InvoiceSummary/InvoiceTotals/TaxableValue/MonetaryAmount) > 150)
                        or (not(//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement[RequirementTypeCoded='BillingType'][RequirementDetails='urn:cen.eu:en16931:2017#conformant#urn:ubl.eu:1p0:extended-ctc-fr'])
                        and not(//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement[RequirementTypeCoded='BillingType'][RequirementDetails='urn:cen.eu:en16931:2017#conformant#urn:cii.eu:1p0:extended-ctc-fr'])
                        and not(//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement[RequirementTypeCoded='BillingType'][RequirementDetails='urn:cen.eu:en16931:2017#conformant#urn.cpro.gouv.fr:1p0:extended-ctc-fr'])
                        ) " />
                </constraint>
                <!-- G1.56 (associated with BG-23) -->
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.EqualConstraint">
                    <arg value="TVA non applicable en application de l'article 293B du CGI" />
                    <precondition expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxExemptCode='VATEX-FR-FRANCHISE']" />
                </constraint>
            </constraints>
        </field>

        <field path="TaxSummary/TaxExemptCode" label="tax_exempt_code">
            <!-- BT-121 -->
            <constraints>
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="CEF_VATEX" />
                    <precondition expression="TaxSummary/TaxExemptCode != '' " />
                </constraint>
                <!-- G1.40 -->
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxTypeCodedOther='E']" />
                    <precondition expression="(//InvoiceHeader/InvoiceType[InvoiceTypeCoded != 'CorrectedInvoice']
                        and //InvoiceHeader/InvoiceType[InvoiceTypeCoded != 'SelfBilledCorrectedInvoice']
                        and //InvoiceHeader/InvoiceType[InvoiceTypeCoded != 'FactoredCorrectedInvoice'] 
                        and //InvoiceHeader/InvoiceType[InvoiceTypeCoded != 'SelfBilledFactoredCorrectedInvoice']
                        and number(//InvoiceSummary/InvoiceTotals/TaxableValue/MonetaryAmount) > 150)
                        or (//InvoiceSummary/ListOfTaxSummary/TaxSummary[TaxExemptReason != 'Facture rectificative ou facture de moins de 150 €']
                        and not(//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement[RequirementTypeCoded='BillingType'][RequirementDetails='urn:cen.eu:en16931:2017#conformant#urn:ubl.eu:1p0:extended-ctc-fr'])
                        and not(//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement[RequirementTypeCoded='BillingType'][RequirementDetails='urn:cen.eu:en16931:2017#conformant#urn:cii.eu:1p0:extended-ctc-fr'])
                        and not(//InvoiceHeader/CountrySpecificRequirements/OtherCountryRequirements/CountryRequirement[Country/CountryCoded='FR']/ListOfSpecificRequirement/SpecificRequirement[RequirementTypeCoded='BillingType'][RequirementDetails='urn:cen.eu:en16931:2017#conformant#urn.cpro.gouv.fr:1p0:extended-ctc-fr'])
                        ) " />
                </constraint>
            </constraints>
        </field>
    </section>

    <!-- TAX SUMMARY -->
    <section label="taxsummary" step="ListOfTaxSummary/TaxSummary[TaxCategoryCoded='Other']"
        required="false"
        description="validation of Tax Summary">
        <!--BT-119-->
        <!--Rule BR-48-->
        <field path="TaxCategoryCodedOther" description="Taux de TVA ventilé"
            label="vat_rate">
            <constraints>
                <constraint legislation="EU, FR, EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InDecimalListConstraint">
                    <arg value="VAT rate-percent" />
                </constraint>
            </constraints>
        </field>

        <field path="TaxableAmount" description="Montant taxable selon le taux ventilé"
            label="taxable_amount">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <field path="TaxAmountInTaxAccountingCurrency"
            description="Montant de taxe en euros selon le taux ventilé"
            label="tax_amount">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>
    </section>

    <!-- ACTUAL PAYMENT -->
    <section label="actualpayment" step="ListOfActualPayment"
        description="validation of Actual Payment" required="false">
        <field path="ActualPayment/PaymentAmount/InvoiceCurrencyAmt/MonetaryAmount"
            description="Montant payé d'acompte"
            label="prepaid_amount">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>

        <field path="ActualPayment/OtherPaymentInfo" description="Numéro de l’acompte"
            label="payment_number">
            <constraints>
                <constraint legislation="FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
            </constraints>
        </field>

        <field path="ActualPayment/PaymentDate" description="Date de l’acompte"
            label="payment_date">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyDateConstraint" />
            </constraints>
        </field>
    </section>

    <section step="ListOfTaxSummary/TaxSummary" label="list_of_tax_summary" required="false">
        <!--BT-116-->
        <!--Rule BR-45-->
        <field path="TaxableAmount" label="tax_base_for_VAT_type" description="Tax base for VAT type">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
        <!--BT-117-->
        <!--Rule BR-46BR-CO-17-->
        <field path="TaxAmountInTaxAccountingCurrency" label="tax_amount_in_tax_accounting_currency"
            description="Tax amount in tax accounting currency">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
        <field path="TaxAmount" label="VAT_amount" description="VAT amount">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
    </section>
</rules>