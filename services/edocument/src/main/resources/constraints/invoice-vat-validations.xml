<?xml version="1.0" encoding="UTF-8"?>
<rules xmlns="http://www.example.org/constraints"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.example.org/constraints constraints.xsd"
    context="/Invoice" label="invoice_tax">

    <!-- 12 -->
    <field path="InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge/Tax/TaxPercent"
        label="global_tax">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.ValueMatchConstraint">
                <arg value="//InvoiceSummary/ListOfTaxSummary/TaxSummary/TaxCategoryCodedOther" />
            </constraint>
        </constraints>
    </field>
    <field
        path="InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/Tax/TaxPercent"
        label="line_tax">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.ValueMatchConstraint">
                <arg value="//InvoiceSummary/ListOfTaxSummary/TaxSummary/TaxCategoryCodedOther" />
            </constraint>
        </constraints>
    </field>
    <field
        path="InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/Tax/TaxPercent"
        label="line_allowanceorcharge_tax">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.ValueMatchConstraint">
                <arg value="//InvoiceSummary/ListOfTaxSummary/TaxSummary/TaxCategoryCodedOther" />
            </constraint>
        </constraints>
    </field>
    <!--BG-25-->
    <!--Rule G6.09-->
    <field path="InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail" label="invoice_detail_invoice_line"
        description="Invoice line">
        <constraints>
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
            </constraint>
        </constraints>
    </field>
    <!-- TAX SUMMARY -->
    <section label="taxsummary"
        step="InvoiceSummary/ListOfTaxSummary[TaxSummary/TaxCategoryCoded='Other']" required="false"
        description="validation of Tax Summary">
        <field path="TaxSummary[TaxCategoryCoded='Other']/TaxCategoryCodedOther"
            description="Taux de TVA ventilé"
            label="tax">
            <constraints>
                <!-- 11 -->
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueValueConstraint" />
                <!-- 13 -->
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.ValueMatchConstraint">
                    <arg
                        value="//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge/Tax/TaxPercent" />
                    <arg
                        value="//InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/Tax/TaxPercent" />
                    <arg
                        value="//InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/Tax/TaxPercent" />
                </constraint>
            </constraints>
        </field>
    </section>
</rules>