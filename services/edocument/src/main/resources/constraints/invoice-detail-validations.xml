<?xml version="1.0" encoding="UTF-8"?>
<rules xmlns="http://www.example.org/constraints"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.example.org/constraints constraints.xsd"
    context="//InvoiceItemDetail" label="invoicedetail">

    <!-- (6) EU & FR - Ligne -->
    <field path="InvoiceBaseItemDetail" label="base_item_detail">
        <constraints>
            <constraint legislation="EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.AllDescendantsNotEmptyConstraint">
                <arg value="ItemIdentifiers/ItemDescription" />     <!-- BT-153 -->
                <arg value="InvoicedQuantity/QuantityValue" />      <!-- BT-129 -->
            </constraint>
        </constraints>
    </field>

    <!--BT-159-->
    <!--RULE G2.01 AND G6.09-->
    <field path="InvoiceBaseItemDetail/CountryOfOrigin/CountryCoded" label="base_item_detail_country_origin_code">
        <constraints>
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="InvoiceBaseItemDetail/CountryOfOrigin/CountryCoded" />
                <!--BG-31-->
                <precondition expression="InvoiceBaseItemDetail/ItemIdentifiers" />
            </constraint>
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CountryCodeISO3166-alpha2" />
                <!--BG-31-->
                <precondition expression="InvoiceBaseItemDetail/ItemIdentifiers" />
            </constraint>
        </constraints>
    </field>

    <!-- Rule: G6.01-->
    <!-- element BG-25 -->
    <!--BT-126-->
    <field path="InvoiceBaseItemDetail/LineItemNum/BuyerLineItemNum" label="buyer_line_number" description="Invoice buyer line number">
        <constraints>
            <!--RULE G6.09-->
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint"> <!-- BR-16 -->
                <precondition expression="//InvoiceItemDetail/InvoiceBaseItemDetail" />
                <!-- BR-21 -->
                <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.IntegerConstraint">
                <precondition expression="//InvoiceItemDetail/InvoiceBaseItemDetail" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                <arg value="6" />
            </constraint>
            <!--RULE G6.09-->
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="InvoiceItemDetail/InvoiceBaseItemDetail/LineItemNum/BuyerLineItemNum" />
            </constraint>
            <constraint
                legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueContentConstraint" />
        </constraints>
    </field>


    <!--BT-129-->
    <field path="InvoiceBaseItemDetail/InvoicedQuantity/QuantityValue"
        description="Quantité facturée" label="quantity_value">
        <!--Unique and mandatory-->
        <constraints>
            <!--RULE G6.09-->
            <constraint
                legislation="EN, EU, FR, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="InvoiceItemDetail/InvoiceBaseItemDetail/InvoicedQuantity/QuantityValue" />
            </constraint>
            <constraint legislation="EN, EU, FR"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                <arg value="-0" />
            </constraint>
            <!-- Rule BR-22 -->
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <!--BT-126-->
                <precondition expression="InvoiceBaseItemDetail/LineItemNum/BuyerLineItemNum" />
                <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
            </constraint>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                <arg value="19" />
            </constraint>
            <constraint
                legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                <arg value="4" />
            </constraint>
        </constraints>
    </field>

    <!--BT-130-->
    <!--RULE G6.09-->
    <field path="InvoiceBaseItemDetail/InvoicedQuantity/UnitOfMeasurement[UOMCoded='Other']/UOMCodedOther"
        label="invoiced_quantity" description="Invoiced quantity">
        <constraints>
            <constraint class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint"
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                <arg value="InvoiceItemDetail/InvoiceBaseItemDetail/InvoicedQuantity/UnitOfMeasurement/UOMCodedOther" />
                <precondition expression="InvoiceBaseItemDetail/InvoicedQuantity/UnitOfMeasurement/UOMCoded='Other'" />
            </constraint>
            <constraint class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint"
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                <precondition expression="InvoiceBaseItemDetail/InvoicedQuantity/UnitOfMeasurement/UOMCoded='Other'" />
                <!--BT-126-->
                <precondition expression="InvoiceBaseItemDetail/LineItemNum/BuyerLineItemNum" />
                <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
            </constraint>
            <constraint class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint"
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G">
                <arg value="EN16931_UNIT_OF_MEASURE" />
                <precondition expression="InvoiceBaseItemDetail/InvoicedQuantity/UnitOfMeasurement/UOMCoded='Other'" />
            </constraint>
        </constraints>
    </field>

    <section
        step="InvoiceBaseItemDetail/LineItemReferences/ListOfRelatedInvoiceRef" label="list_of_related_invoice_ref" required="false">
        <field
            path="RelatedInvoiceRef/RelatedInvoiceType/InvoiceTypeCoded"
            description="Type de facture antérieure référencée à la ligne"
            label="line_former_invoice_type">
            <!-- Rule FR: G1.01 -->
            <!-- element EXT-FR-FE-137 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition expression="RelatedInvoiceRef" />
                    <arg value="CommercialInvoice" /> <!-- CII code: 380 -->
                    <arg value="CreditNoteGoodsAndServices" /> <!-- CII code: 381 -->
                    <arg value="CorrectedInvoice" /> <!-- CII code: 384 -->
                    <arg value="PrepaymentInvoice" /> <!-- CII code: 386 -->
                    <arg value="SelfBilledInvoice" /> <!-- CII code: 389 -->
                    <arg value="FactoredInvoice" /> <!-- CII code: 393 -->
                    <arg value="Other" />
                </constraint>
            </constraints>
        </field>

        <field
            path="RelatedInvoiceRef/RelatedInvoiceType[InvoiceTypeCoded='Other']/InvoiceTypeCodedOther"
            description="Type de facture antérieure référencée à la ligne"
            label="line_former_invoice_type">
            <!-- Rule FR: G1.01 -->
            <!-- element EXT-FR-FE-137 -->
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="RelatedInvoiceRef/RelatedInvoiceType[InvoiceTypeCoded='Other']" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition
                        expression="RelatedInvoiceRef/RelatedInvoiceType[InvoiceTypeCoded='Other']" />
                    <arg value="SelfBilledCreditNote" /> <!-- CII code: 261 -->
                    <arg value="GlobalAllowances" /> <!-- CII code: 262 -->
                    <arg value="FactoredCreditNote" /> <!-- CII code: 396 -->
                    <arg value="SelfBilledPrepaymentInvoice" /> <!-- CII code: 500 -->
                    <arg value="SelfBilledFactoredInvoice" /> <!-- CII code: 501 -->
                    <arg value="SelfBilledFactoredCreditNote" /> <!-- CII code: 502 -->
                    <arg value="PrepaymentCreditNote" /> <!-- CII code: 503 -->
                    <arg value="SelfBilledCorrectedInvoice" /> <!-- CII code: to be defined 
                        (facture rectificative auto-facturée)-->
                    <arg value="FactoredCorrectedInvoice" /> <!-- CII code: to be defined 
                        (facture rectificative affacturée)-->
                    <arg value="SelfBilledFactoredCorrectedInvoice" /> <!-- CII code: to be defined
                        (facture rectificative auto-facturée affacturée)-->
                </constraint>
            </constraints>
        </field>
    </section>

    <section step="InvoicePricingDetail" label="invoicepricingdetail">
        <!-- BT-151 -->
        <!-- Rule G2.31 -->
        <field path="Tax[TaxTypeCoded='Other']/TaxTypeCodedOther"
            label="tax_tax_type_coded_other" description="VAT type code of invoiced item">
            <constraints>
                <!-- Rule BR-CO-4 -->
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="Tax/TaxTypeCoded='Other'" />
                    <!-- BT-126 -->
                    <precondition expression="InvoiceBaseItemDetail/LineItemNum/BuyerLineItemNum" />
                    <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
                </constraint>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="InvoicePricingDetail/Tax[TaxTypeCoded='Other']/TaxTypeCodedOther" />
                </constraint>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <arg value="S" />
                    <arg value="Z" />
                    <arg value="E" />
                    <arg value="AE" />
                    <arg value="K" />
                    <arg value="G" />
                    <arg value="O" />
                    <arg value="L" />
                    <arg value="M" />
                </constraint>
            </constraints>
        </field>

        <field
            path="ListOfPrice/Price[PricingType/PriceTypeCoded = 'CalculationNet']/UnitPrice/UnitPriceValue"
            description="Prix unitaire net HT de la ligne" label="net_unit_price">
            <!-- BT-146 -->
            <!-- Rule BR-26 -->
            <constraints>
                <constraint legislation="EN, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="../InvoiceBaseItemDetail" />
                </constraint>
                <!--G6.09-->
                <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="../InvoiceBaseItemDetail" />
                    <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
                </constraint>
                <!--G6.09-->
                <constraint legislation="EN, FR, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price/UnitPrice/UnitPriceValue" />
                </constraint>
                <constraint legislation="EN, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="4" />
                </constraint>
                <!-- BR-27 -->
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NegativeConstraint" />
            </constraints>
        </field>

        <field
            path="ListOfPrice/Price[PricingType/PriceTypeCoded = 'CalculationGross']/UnitPrice/UnitPriceValue"
            description="Prix unitaire brut HT de la ligne" label="gross_unit_price">
            <!-- BT-148 -->
            <constraints>
                <constraint legislation="EN, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="ItemAllowancesOrCharges/AllowOrCharge" />
                </constraint>
                <constraint
                    legislation="EN, FR, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint legislation="EN, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <!-- Rule BR-28 -->
                <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NegativeConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="4" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SumConsistencyConstraint">
                    <!--BT-146-->
                    <arg value="//InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price[PricingType/PriceTypeCoded = 'CalculationNet']/UnitPrice/UnitPriceValue" />
                    <!--BT-147-->
                    <arg value="//InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price[PricingType/PriceTypeCoded='DiscountAmountAllowed']/UnitPrice/UnitPriceValue" />
                </constraint>
            </constraints>
        </field>

        <!--BT-147-->
        <field path="ListOfPrice/Price[PricingType/PriceTypeCoded='DiscountAmountAllowed']/UnitPrice/UnitPriceValue" label="discount_on_item_price"
            description="Discount on item price">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price[PricingType/PriceTypeCoded='DiscountAmountAllowed']" />
                    <arg value="Price/UnitPrice/UnitPriceValue" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price[PricingType/PriceTypeCoded='DiscountAmountAllowed']" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="4" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price[PricingType/PriceTypeCoded='DiscountAmountAllowed']" />
                </constraint>
            </constraints>
        </field>
        <!--BT-149-->
        <field path="ListOfPrice/Price/PriceBasisQuantity/QuantityValue" label="base_quantity_of_item_price"
            description="Base Quantity of Item Price">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="Price/PriceBasisQuantity/QuantityValue" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price/PriceBasisQuantity" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price/PriceBasisQuantity" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="4" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ListOfPrice/Price/PriceBasisQuantity" />
                </constraint>
            </constraints>
        </field>

        <!-- Le noeud Tax doit être présent si aucune exemption n'est mentionnée en entête -->
        <field path="Tax" description="Taux de TVA appliqué" label="vat">
            <!-- BG-30 -->
            <constraints>
                <!--G6.09-->
                <constraint
                    legislation="EU, FR, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="InvoiceItemDetail/InvoicePricingDetail/Tax" />
                    <precondition expression="//InvoiceItemDetail/InvoicePricingDetail" />
                </constraint>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="not (//InvoiceHeader/TaxReference[TaxCategoryCoded='ExemptFromTax' and TaxTreatmentCoded='NoTaxApplies']) or not(//InvoiceHeader/TaxReference)" />
                </constraint>
                <!--G6.09-->
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceItemDetail/InvoicePricingDetail" />
                </constraint>
            </constraints>
        </field>

        <field path="Tax/TaxPercent" description="Taux de TVA appliqué" label="vat_rate">
            <!-- BT-152 -->
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition
                        expression="Tax[TaxTypeCoded='ValueAddedTax' and TaxFunctionQualifierCoded='Tax' and TaxCategoryCoded='StandardRate']" />
                </constraint>
                <!--G6.09-->
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceItemDetail/InvoicePricingDetail/Tax" />
                </constraint>
                <!--G6.09-->
                <constraint
                    legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InDecimalListConstraint">
                    <arg value="VAT rate-percent" />
                    <precondition expression="//InvoiceItemDetail/InvoicePricingDetail/Tax" />
                </constraint>
            </constraints>
        </field>

        <!-- Le pourcentage de taxe à la ligne doit être égal à zéro s'il est fait part d'une
        exemption en entete ou si la tax est de type Exemption -->
        <field path="Tax/TaxPercent" description="Taux de TVA appliqué" label="exempt_vat_rate">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.ZeroConstraint">
                    <precondition
                        expression="Tax[TaxTypeCoded='ValueAddedTax' and TaxFunctionQualifierCoded='TaxRelatedInformation' and TaxCategoryCoded='ExemptFromTax']" />
                    <precondition operator="or"
                        expression="//InvoiceHeader/TaxReference[TaxCategoryCoded='ExemptFromTax' and TaxTreatmentCoded='NoTaxApplies']" />
                </constraint>
            </constraints>
        </field>
        <!--BT-131-->
        <!-- Rule BR-24 -->
        <field path="LineItemTotal/MonetaryAmount" label="net_invoice_line_amount"
            description="Net invoice line amount">
            <constraints>
                <!--G6.09-->
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <!--BT-126-->
                    <precondition expression="../InvoiceBaseItemDetail/LineItemNum/BuyerLineItemNum" />
                    <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
                </constraint>
                <!--G6.09-->
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
    </section>

    <section step="InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge" label="invoicepricingdetail_allow_or_charge" required="false">
        <!--BT-137-->
        <field path="BasisMonetaryRange/MonetaryLimit/MonetaryLimitLineValue"
            label="base_for_invoice_line_discount" description="Base for invoice line discount">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <arg value="AllowOrCharge/BasisMonetaryRange/MonetaryLimit/MonetaryLimitValue" />
                    <precondition expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
        <!--BT-142-->
        <field path="BasisMonetaryRange/MonetaryLimit/MonetaryLimitLineValue"
            label="basis_of_fees_applicable_to_the_invoice_line"
            description="Basis of fees applicable to the invoice line">
            <constraints>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentQualifier[PercentQualifierCoded='BasePriceAmountInvoiceDetail']" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />
                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <arg value="19" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentQualifier[PercentQualifierCoded='BasePriceAmountInvoiceDetail']" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />

                </constraint>
                <constraint
                    legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <arg value="2" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentQualifier[PercentQualifierCoded='BasePriceAmountInvoiceDetail']" />
                    <precondition
                        expression="//InvoiceItemDetail/InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge/BasisMonetaryRange/MonetaryLimit" />

                </constraint>
            </constraints>
        </field>
    </section>

    <!-- Taxe parafiscale à la ligne -->
    <section
        step="InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge[IndicatorCoded = 'Service' and AllowanceOrChargeDescription/ServiceCoded = 'Tax-RegulatoryTax']"
        required="false" label="paratax">
        <field path="AllowanceOrChargeDescription/ListOfDescription"
            description="Libellé de la taxe parafiscale à la ligne" label="desc">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge"
            description="Pourcentage ou Montant de la taxe parafiscale à la ligne"
            label="type">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.HasDescendantNotEmptyConstraint">
                    <arg value="PercentageAllowanceOrCharge/Percent" />
                    <arg value="MonetaryValue/MonetaryAmount" />
                </constraint>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge/MonetaryValue/MonetaryAmount" required="false"
            description="Montant de la taxe parafiscale à la ligne différent de -0"
            label="amount">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/Percent" required="false"
            description="Pourcentage de la taxe parafiscale à la ligne différent de -0"
            label="percent">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>

        <field
            path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue/MonetaryAmount"
            required="false"
            description="Montant associé au pourcentage de la taxe parafiscale à la ligne différent de -0"
            label="amount_percent">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>
    </section>

    <section
        step="InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge[IndicatorCoded = 'Service' and AllowanceOrChargeDescription/ServiceCoded = 'Tax-RegulatoryTax']/Tax[TaxTypeCoded ='ValueAddedTax' and TaxFunctionQualifierCoded = 'Tax']"
        required="false" label="paratax">
        <field path="TaxPercent" description="Taux de TVA appliqué" label="regulatory_vat">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
            </constraints>
        </field>
    </section>

    <section
        step="InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge[IndicatorCoded = 'LineItemCharge']"
        required="false" label="charge">
        <field path="AllowanceOrChargeDescription/ListOfDescription"
            description="Libellé de la charge à la ligne" label="desc">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
            </constraints>
        </field>
        <!-- BT-144-->
        <field path="AllowanceOrChargeDescription/ListOfDescription" description="ChargeListOfDescription"
            label="list_of_description">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="IndicatorCoded='LineItemCharge'" />
                    <precondition expression="MethodOfHandlingCoded='ChargeToBePaidByCustomer'" />
                    <precondition expression="not(AllowanceOrChargeDescription/ServiceCodedOther) or AllowanceOrChargeDescription/ServiceCodedOther=''" />
                </constraint>
                <!--BR-CO-8-->
                <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.MatchPairConstraint">
                    <arg value="ServiceCodedOther" />
                    <arg value="UNTDID7161" />
                    <!-- BT-145 -->
                    <precondition expression="AllowanceOrChargeDescription[ServiceCoded='Other']/ServiceCodedOther and AllowanceOrChargeDescription[ServiceCoded='Other']/ServiceCodedOther!=''" />
                    <!-- BT-144 -->
                    <precondition expression="AllowanceOrChargeDescription/ListOfDescription and AllowanceOrChargeDescription/ListOfDescription!=''" />
                </constraint>
            </constraints>
        </field>
        <!-- BT-145 -->
        <field path="AllowanceOrChargeDescription/ServiceCodedOther" description="ChargeServiceCodedOther"
            label="service_coded_other">
            <constraints>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="IndicatorCoded='LineItemCharge'" />
                    <precondition expression="AllowanceOrChargeDescription/ServiceCoded='Other'" />
                    <precondition expression="not(AllowanceOrChargeDescription/ListOfDescription) or AllowanceOrChargeDescription/ListOfDescription=''" />
                </constraint>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition expression="IndicatorCoded='LineItemCharge'" />
                    <precondition expression="AllowanceOrChargeDescription/ServiceCoded='Other'" />
                    <arg value="UNTDID7161" />
                </constraint>
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge"
            description="Pourcentage ou Montant de la charge à la ligne"
            label="type">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.HasDescendantNotEmptyConstraint">
                    <arg value="PercentageAllowanceOrCharge/Percent" />
                    <arg value="MonetaryValue/MonetaryAmount" />
                </constraint>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge/MonetaryValue/MonetaryAmount" required="false"
            description="Montant de la charge à la ligne différent de -0"
            label="amount">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/Percent" required="false"
            description="Pourcentage de la charge à la ligne différent de -0"
            label="percent">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>

        <field
            path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue/MonetaryAmount"
            required="false"
            description="Montant associé au pourcentage de la charge à la ligne différent de -0"
            label="amount_percent">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>
        <!-- BT-141
        Rule BR-41 and BR-43 -->
        <field path="TypeOfAllowanceOrCharge/MonetaryValue/MonetaryLineAmount"
            label="amount" description="Line allowance and charge">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/MonetaryValue" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/MonetaryValue" />
                    <arg value="AllowOrCharge/TypeOfAllowanceOrCharge/MonetaryValue/MonetaryLineAmount" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/MonetaryValue" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/MonetaryValue" />
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
        <field path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue/MonetaryLineAmount"
            label="amount" description="Line allowance and charge">
            <constraints>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue" />
                    <arg value="AllowOrCharge/TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue/MonetaryLineAmount" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue" />
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
    </section>

    <section
        step="InvoicePricingDetail/ItemAllowancesOrCharges/AllowOrCharge[IndicatorCoded = 'LineItemAllowance']"
        required="false" label="allowance">
        <field path="AllowanceOrChargeDescription/ListOfDescription"
            description="Libellé de la remise à la ligne" label="desc">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyAndUniqueConstraint" />
            </constraints>
        </field>

        <field path="AllowanceOrChargeDescription/ListOfDescription" description="ListOfDescription"
            label="list_of_description">
            <constraints>
                <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="IndicatorCoded='LineItemAllowance'" />
                    <precondition expression="not(AllowanceOrChargeDescription[ServiceCoded='Other']/ServiceCodedOther) or AllowanceOrChargeDescription[ServiceCoded='Other']/ServiceCodedOther=''" />
                </constraint>
                <!-- BR-CO-7 -->
                <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.MatchPairConstraint">
                    <arg value="ServiceCodedOther" />
                    <arg value="UNTDID5189" />
                    <!-- BT-140 -->
                    <precondition expression="AllowanceOrChargeDescription[ServiceCoded='Other']/ServiceCodedOther and AllowanceOrChargeDescription[ServiceCoded='Other']/ServiceCodedOther!=''" />
                    <!-- BT-139 -->
                    <precondition expression="AllowanceOrChargeDescription/ListOfDescription and AllowanceOrChargeDescription/ListOfDescription!=''" />
                </constraint>
            </constraints>
        </field>

        <field path="AllowanceOrChargeDescription[ServiceCoded='Other']/ServiceCodedOther" description="ServiceCodedOther"
            label="service_coded_other">
            <constraints>
                <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="IndicatorCoded='LineItemAllowance'" />
                    <precondition expression="AllowanceOrChargeDescription/ServiceCoded='Other'" />
                    <precondition expression="not(AllowanceOrChargeDescription/ListOfDescription) or AllowanceOrChargeDescription/ListOfDescription=''" />
                </constraint>
                <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                    <precondition expression="IndicatorCoded='LineItemAllowance'" />
                    <precondition expression="AllowanceOrChargeDescription/ServiceCoded='Other'" />
                    <arg value="UNTDID5189" />
                </constraint>
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge"
            description="Pourcentage ou Montant de la remise à la ligne"
            label="type">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.HasDescendantNotEmptyConstraint">
                    <arg value="PercentageAllowanceOrCharge/Percent" />
                    <arg value="MonetaryValue/MonetaryAmount" />
                </constraint>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge/MonetaryValue/MonetaryAmount" required="false"
            description="Montant de la remise à la ligne différent de -0"
            label="amount">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>

        <field path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/Percent" required="false"
            description="Pourcentage de la remise à la ligne différent de -0"
            label="percent">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>

        <field
            path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue/MonetaryAmount"
            required="false"
            description="Montant associé au pourcentage de la remise à la ligne différent de -0"
            label="amount_percent">
            <constraints>
                <constraint legislation="EU, FR"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEqualConstraint">
                    <arg value="-0" />
                </constraint>
            </constraints>
        </field>
        <!-- BT-136
        Rule BR-41 and BR-43 -->
        <field path="TypeOfAllowanceOrCharge/MonetaryValue/MonetaryLineAmount"
            label="amount" description="Line allowance and charge">
            <constraints>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/MonetaryValue" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/MonetaryValue" />
                    <arg value="AllowOrCharge/TypeOfAllowanceOrCharge/MonetaryValue/MonetaryLineAmount" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/MonetaryValue" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/MonetaryValue" />
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
        <field path="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue/MonetaryLineAmount"
            label="amount" description="Line allowance and charge">
            <constraints>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue" />
                    <arg value="AllowOrCharge/TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue/MonetaryLineAmount" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.SizeConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue" />
                    <arg value="19" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.DecimalConstraint">
                    <precondition expression="TypeOfAllowanceOrCharge/PercentageAllowanceOrCharge/PercentageMonetaryValue" />
                    <arg value="2" />
                </constraint>
            </constraints>
        </field>
    </section>
    <field path="DeliveryDetail/ShipToLocation/NameAddress/Country" label="ship_to_country_code">
        <!-- EXT-FR-FE-157 -->
        <constraints>
            <!-- Rule FR: G6.11 -->
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="DeliveryDetail/ShipToLocation/NameAddress" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="CountryCodeISO3166-alpha2" />
            </constraint>
        </constraints>
    </field>
    <!--EXT-FR-FE-158-->
    <field path="InvoiceBaseItemDetail/LineItemReferences/DeliveryNoteNumber/RefDate" label="delivery_ref_date"
        description="Delivery reference date">
        <constraints>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <precondition expression="InvoiceBaseItemDetail/LineItemReferences/DeliveryNoteNumber" />
            </constraint>
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                <precondition expression="InvoiceBaseItemDetail/LineItemReferences/DeliveryNoteNumber" />
            </constraint>
        </constraints>
    </field>

    <!-- INVOICING PERIOD FIELD -->
    <!-- BG-26 -->
    <field path="LineItemDates/InvoicingPeriod" label="invoicingperiod" description="Invoicing period">
        <constraints>
            <!-- Rule FR: G6.11 -->
            <constraint
                legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint" />
        </constraints>
    </field>

    <!-- INVOICING PERIOD SECTION -->
    <section step="LineItemDates/InvoicingPeriod" label="invoicingperiod" description="Invoicing period" required="false">
        <!--BT-134-->
        <!-- Rule BR-CO-20 -->
        <field path="StartDate" label="start_date" description="Start date">
            <constraints>
                <!-- Rule FR: G6.11 -->
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceItemDetail/LineItemDates/InvoicingPeriod" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceItemDetail/LineItemDates/InvoicingPeriod" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                    <precondition expression="//InvoiceItemDetail/LineItemDates/InvoicingPeriod" />
                </constraint>
                <!--RULE BR-CO-20-->
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceItemDetail/LineItemDates/InvoicingPeriod/EndDate" />
                </constraint>
            </constraints>
        </field>
        <!-- BT-135-->
        <!-- Rule BR-30 BR-CO-20 -->
        <field path="EndDate" label="end_date" description="End date">
            <constraints>
                <!-- Rule FR: G6.11 -->
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceItemDetail/LineItemDates/InvoicingPeriod" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                    <precondition expression="//InvoiceItemDetail/LineItemDates/InvoicingPeriod" />
                </constraint>
                <constraint
                    legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.Century21DateConstraint">
                    <precondition expression="//InvoiceItemDetail/LineItemDates/InvoicingPeriod" />
                </constraint>
                <!--Rule BR-CO-20-->
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                    <precondition expression="//InvoiceItemDetail/LineItemDates/InvoicingPeriod/StartDate" />
                </constraint>
                <!--Rule BR-30-->
                <constraint
                    legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                    class="com.byzaneo.generix.edocument.validation.constraints.AfterDateConstraint">
                    <arg value="StartDate" />
                </constraint>
            </constraints>
        </field>
    </section>
    <!--EXT-FR-FE-183-->
    <!--RULE G1.52-->
    <field path="ListOfStructuredNote/StructuredNote/TextTypeCoded" label="structured_note_text_type_coded" description="Invoice note subject code">
        <constraints>
            <constraint legislation="FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="StructuredNote/TextTypeCoded" />
            </constraint>
            <constraint legislation="EN, FR_B2B2024, FR_B2G2024, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.InListConstraint">
                <arg value="UNTDID4451" />
            </constraint>
        </constraints>
    </field>

    <!--BG-31-->
    <!--RULE G6.09-->
    <field path="InvoiceBaseItemDetail/ItemIdentifiers" label="article_ident" description="Article identifier">
        <constraints>
            <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers" />
            </constraint>
            <constraint legislation="FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint" />
        </constraints>
    </field>
    <!--BT-153-->
    <!--RULE G6.09-->
    <field path="InvoiceBaseItemDetail/ItemIdentifiers/ItemDescription" label="article_name" description="Article name">
        <constraints>
            <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.UniqueConstraint">
                <arg value="InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers/ItemDescription" />
            </constraint>
            <constraint
                legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <!--BT-126-->
                <precondition expression="InvoiceBaseItemDetail/LineItemNum/BuyerLineItemNum" />
                <precondition expression="//InvoiceHeader[number(translate(substring(InvoiceIssueDate, 1, 10), '-', '')) > 20270031]" />
            </constraint>
        </constraints>
    </field>
    <!--BT-160-->
    <!--RULE BR-54-->
    <field path="ListOfNameValueSet/NameValueSet/ListOfNameValuePair/NameValuePair/Name" label="item_atribute_name_value" description="Item attribute name">
        <constraints>
            <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="//InvoiceItemDetail/ListOfNameValueSet/NameValueSet/ListOfNameValuePair/NameValuePair/Value and not (//InvoiceItemDetail/ListOfNameValueSet/NameValueSet/ListOfNameValuePair/NameValuePair/Value = '')" />
            </constraint>
        </constraints>
    </field>
    <!--BT-161-->
    <!--RULE BR-54-->
    <field path="ListOfNameValueSet/NameValueSet/ListOfNameValuePair/NameValuePair/Value" label="item_atribute_name_value" description="Item attribute value">
        <constraints>
            <constraint legislation="EN, FR_B2G_OrderReference, FR_B2G_BuyerReference, FR_B2G_BuyerAndOrderReference, FR_B2G_BuyerOrOrderReference, FR_B2B, FR_B2G"
                class="com.byzaneo.generix.edocument.validation.constraints.NotEmptyConstraint">
                <precondition expression="//InvoiceItemDetail/ListOfNameValueSet/NameValueSet/ListOfNameValuePair/NameValuePair/Name and not(//InvoiceItemDetail/ListOfNameValueSet/NameValueSet/ListOfNameValuePair/NameValuePair/Name = '')" />
            </constraint>
        </constraints>
    </field>
</rules>