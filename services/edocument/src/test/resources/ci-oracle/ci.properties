# -- D A T A B A S E --
database.flyway.disable = true
#  H2 (memory DB)
#database.type     = h2
#database.driver   = org.h2.Driver
#database.url      = jdbc:h2:mem:ci;DB_CLOSE_ON_EXIT=FALSE
#database.username =
#database.password =
#database.target   = org.hibernate.dialect.H2Dialect

#  MYSQL
#database.type       = mysql
#database.driver		= com.mysql.jdbc.Driver
#database.url		= *********************************************************************************************************************************************************************
#database.username	= ci
#database.password	= ci
#database.target		= com.byzaneo.commons.dao.hibernate.support.MySQL5Dialect
database.schema=GNX
database.type=oracle
database.driver=oracle.jdbc.OracleDriver
database.url=***********************************
database.username=GNX
database.password=GNXORACLE
database.target=com.byzaneo.commons.dao.hibernate.support.Oracle11gDialect
database.datasource=pooledDataSource

database.showSql	= false
# validate | update | create | create-drop
database.generateDdl		= true
database.generateDdl.mode	= create-drop
database.process.generateDdl = drop-create
database.generateDdl.imports=/META-INF/data/gnx-test.${database.type}.sql


index.mongo.uri=mongodb://ci:ci@ci-mongo/ci
archive.mongo.uri=${index.mongo.uri}