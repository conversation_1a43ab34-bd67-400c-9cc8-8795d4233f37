<?xml version="1.0" encoding="ISO-8859-1"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<!-- E X T E R N A L   P R O P E R T I E S -->
	<bean id="comPropertyConfigurer" class="com.byzaneo.commons.util.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:/gnx-edoc-test.properties</value>
				<value>classpath:/dev/dev.properties</value>
			</list>
		</property>
	</bean>
	
	<!-- S E R V I C E S -->
  	<import resource="classpath:/META-INF/spring/gnx-core.beans.xml" />
	<import resource="classpath:/META-INF/spring/gnx-archive-mongo.beans.xml" />
</beans>
