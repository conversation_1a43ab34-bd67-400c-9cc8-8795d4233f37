# -- D A T A B A S E --
database.flyway.disable = true
#  H2 (memory DB)
database.type     = h2
database.driver   = org.h2.Driver
database.url      = jdbc:h2:mem:ci;DB_CLOSE_ON_EXIT=FALSE
database.username =
database.password =
database.target   = org.hibernate.dialect.H2Dialect

database.showSql	= false
database.generateDdl		= true
# validate | update | create | create-drop
database.generateDdl.mode	= create-drop
database.generateDdl.imports= /META-INF/data/test.${database.type}.sql