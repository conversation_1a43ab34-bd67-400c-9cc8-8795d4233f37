<?xml version="1.0" encoding="UTF-8"?>
<Invoice>
    <InvoiceHeader>
        <InvoiceParty>
            <SellerParty>
                <ListOfIdentifier>
                    <Identifier>
                        <Agency>
                            <!-- This is a list containing all codes that should be checked. -->
                            <!-- We check against a list of parameters managed from the Test class -->
                            <CodeListIdentifierCodedOther>2000</CodeListIdentifierCodedOther>
                            <CodeListIdentifierCodedOther>2001</CodeListIdentifierCodedOther>
                            <CodeListIdentifierCodedOther>2002</CodeListIdentifierCodedOther>
                            <CodeListIdentifierCodedOther>2003</CodeListIdentifierCodedOther>
                            <CodeListIdentifierCodedOther>2004</CodeListIdentifierCodedOther>
                            <CodeListIdentifierCodedOther>2005</CodeListIdentifierCodedOther>
                        </Agency>
                    </Identifier>
                </ListOfIdentifier>
            </SellerParty>
        </InvoiceParty>
    </InvoiceHeader>
</Invoice>