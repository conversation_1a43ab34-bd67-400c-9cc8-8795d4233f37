<?xml version="1.0" encoding="UTF-8"?>
<root>
    <summary>
        <numbers>
            <number>2</number>
            <number> 2 </number>
            <number>dummy</number>
            <number>anything else</number>
            <number>  anything else</number>

            <otherNumber1>12.</otherNumber1>
            <otherNumber2>20</otherNumber2>
            <otherNumber3>dum my</otherNumber3>
            <otherNumber4>something else</otherNumber4>
            <otherNumber5>anithing else</otherNumber5>
            <otherNumber6>DUMMY</otherNumber6>
            <otherNumber7>3</otherNumber7>

            <testFile>AX</testFile>
            <testFile>FR123</testFile>
            <testFile>CD456</testFile>
            <testFileNotOK>AX123</testFileNotOK>
            <testFile2>FR65</testFile2>
            <testFile2>FRTRE</testFile2>
            <testFile2>FR6TR</testFile2>
            <testFile2>1FR</testFile2>
            <testOKRegex>1234</testOKRegex>
            <testOKRegex>456</testOKRegex>
            <testOKRegex>7890766</testOKRegex>
            <testOKRegex> 564 </testOKRegex>
            <testOKRegex> 1010101010101010 </testOKRegex>
            <testRX>1234</testRX>
            <testRX>456</testRX>
            <testRX>7890766</testRX>
            <testRX> 564 </testRX>
            <testRX> 1010101010101010 </testRX>
            <testRX>1TST</testRX>
        </numbers>
    </summary>
</root>
