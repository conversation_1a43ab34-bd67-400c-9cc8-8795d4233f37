<Invoice>
    <InvoiceHeader/>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
			<!-- test OK 1 node StartDate absolute path and relative path (take care wken selecting nodes for test) -->
            <InvoiceItemDetail>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <StartDate>2021-01-01T00:00:07</StartDate>
                        <!--BT-135-->
                        <EndDate>2025-01-01T00:00:07</EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
            </InvoiceItemDetail>

			<!-- test OK o nodes StartDate absolute path (in fact no start date)-->
			<test0pass>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <!-- <StartDate>2021-01-01T00:00:07</StartDate> -->
                        <!--BT-135-->
                        <EndDate>2025-01-01T00:00:07</EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
            </test0pass>
			
			<!-- test KO 1 node StartDate absolute path and relative path (take care wken selecting nodes for test) -->
            <test1fail>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <StartDate>2025-01-01T00:00:07</StartDate>
                        <!--BT-135-->
                        <EndDate>2021-01-01T00:00:07</EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
            </test1fail>
			
			<!-- test OK 1 node StartDate same date-->
            <test1samedate>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <StartDate>2025-01-01T00:00:07</StartDate>
                        <!--BT-135-->
                        <EndDate>2025-01-01T00:00:07</EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
            </test1samedate>
			
			<!-- test OK multiple nodes StartDate same date-->
            <testmultiok>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <StartDate>2021-01-01T00:00:07</StartDate>
						<StartDate>2022-01-01T00:00:07</StartDate>
						<StartDate>2023-01-01T00:00:07</StartDate>
                        <!--BT-135-->
                        <EndDate>2025-01-01T00:00:07</EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
            </testmultiok>
			
			<!-- test KO multiple nodes StartDate same date-->
            <testmultiko>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <StartDate>2021-01-01T00:00:07</StartDate>
						<StartDate>2022-01-01T00:00:07</StartDate>
						<StartDate>2030-01-01T00:00:07</StartDate>
                        <!--BT-135-->
                        <EndDate>2025-01-01T00:00:07</EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
            </testmultiko>
			
			<!-- test KO parse error start date-->
			<testkoparteerrorstartdate>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <StartDate>aaaa-bb-ccTzz:yy:zz</StartDate>
                        <!--BT-135-->
                        <EndDate>2025-01-01T00:00:07</EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
            </testkoparteerrorstartdate>
			
			<!-- test KO parse error end date-->
			<testkoparteerrorenddate>
                <!--BG-26-->
                <LineItemDates>
                    <InvoicingPeriod>
                        <!--BT-134-->
                        <StartDate>2021-01-01T00:00:07</StartDate>
                        <!--BT-135-->
                        <EndDate>aaaa-bb-ccTzz:yy:zz</EndDate>
                    </InvoicingPeriod>
                </LineItemDates>
            </testkoparteerrorenddate>

        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary/>
</Invoice>
