<Invoice xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd">
    <InvoiceHeader>
        <InvoiceNumber>00000019</InvoiceNumber>
        <InvoiceIssueDate>2004-03-01T00:00:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <core:BuyerOrderNumber>05052900</core:BuyerOrderNumber>
                <core:PurchaseOrderDate>2004-02-27T00:00:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>
            <ASNNumber>
                <core:RefNum>80062193</core:RefNum>
                <core:RefDate>2004-03-01T00:00:00</core:RefDate>
            </ASNNumber>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
            <InvoiceTypeCodedOther>380</InvoiceTypeCodedOther>
        </InvoiceType>
        <InvoiceCurrency>
            <core:CurrencyCoded>EUR</core:CurrencyCoded>
        </InvoiceCurrency>
        <InvoiceLanguage>
            <core:LanguageCoded>en</core:LanguageCoded>
        </InvoiceLanguage>
        <TaxReference>
            <core:TaxTypeCoded>AllTaxes</core:TaxTypeCoded>
            <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
            <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
            <core:TaxTreatmentCoded>Other</core:TaxTreatmentCoded>
        </TaxReference>
        <InvoiceDates>
            <InvoiceDueDate>2004-05-01T00:00:00</InvoiceDueDate>
            <ActualShipDate>2004-03-02T00:00:00</ActualShipDate>
            <ListOfOtherInvoiceDates>
                <core:DateCoded>
                    <core:Date>2004-03-01T00:00:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>PreparationDateTimeOfDocument</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfOtherInvoiceDates>
        </InvoiceDates>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Ident>3026990099715</core:Ident>
                </core:PartyID>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Ident>3014531200102</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>322444987</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                        <core:Ident>322432824 RCS TOURS</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>PEINTOU</core:Name1>
                    <core:Street>8 RUE DE LA CONCORDE</core:Street>
                    <core:PostalCode>37000</core:PostalCode>
                    <core:City>TOURS</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Ident>FR33322444987</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </SellerParty>
            <ShipToParty>
                <core:PartyID>
                    <core:Ident>3026990099807</core:Ident>
                </core:PartyID>
            </ShipToParty>
            <BillToParty>
                <core:PartyID>
                    <core:Ident>3026990099739</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>*********</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>DISTRIBUTOUT</core:Name1>
                    <core:Street>8 RUE CAUMARTIN</core:Street>
                    <core:PostalCode>75009</core:PostalCode>
                    <core:City>PARIS</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Ident>FR36*********</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BillToParty>
            <ListOfPartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <core:Ident>3014531200102</core:Ident>
                    </core:PartyID>
                    <core:PartyRoleCoded>PartytoReceiveInvoiceFOrGoodsOrServices</core:PartyRoleCoded>
                </core:PartyCoded>
            </ListOfPartyCoded>
        </InvoiceParty>
        <InvoicePaymentInstructions>
            <core:PaymentTerms>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>LatePayment</core:PaymentTermCoded>
                    <core:PaymentTermDescription>Penalites de 40 euros en cas de retard de paiement    </core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Discount</core:PaymentTermCoded>
                    <core:PaymentTermDescription>PenaltyTerms/Discount/0.00</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>PenaltyTerms</core:PaymentTermCoded>
                    <core:PaymentTermDescription>PenaltyTerms/Percentage/1.50</core:PaymentTermDescription>
                </core:PaymentTerm>
            </core:PaymentTerms>
        </InvoicePaymentInstructions>
        <InvoiceHeaderNote>PEINTOU SOCIETE ANONYME 250000 EUR  </InvoiceHeaderNote>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1</core:SellerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>GTIN</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>3083630026388</core:ProductIdentifier>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                        <core:ItemDescription>TABLETTES DE CHOCOLAT</core:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>2050</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>PCE</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.548</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.548</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>19.60</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>2</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>2</core:SellerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>GTIN</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>3083630026418</core:ProductIdentifier>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                        <core:ItemDescription>CHOCOLAT EN GRANULEES</core:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>470</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>PCE</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>2.250</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>2.250</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>19.60</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <InvoiceTotals>
            <InvoiceTotal>
                <core:MonetaryAmount>4612.71</core:MonetaryAmount>
            </InvoiceTotal>
            <InvoiceSubTotal>
                <core:MonetaryAmount>4612.71</core:MonetaryAmount>
            </InvoiceSubTotal>
            <TaxableValue>
                <core:MonetaryAmount>4230.90</core:MonetaryAmount>
            </TaxableValue>
            <TotalTaxAmount>
                <core:MonetaryAmount>381.81</core:MonetaryAmount>
            </TotalTaxAmount>
        </InvoiceTotals>
        <ListOfTaxSummary>
            <core:TaxSummary>
                <core:TaxTypeCoded>Total</core:TaxTypeCoded>
                <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                <core:TaxCategoryCodedOther>5.5</core:TaxCategoryCodedOther>
                <core:TaxableAmount>3173.40</core:TaxableAmount>
                <core:TaxAmount>174.54</core:TaxAmount>
                <core:TaxAmountInTaxAccountingCurrency>174.54</core:TaxAmountInTaxAccountingCurrency>
            </core:TaxSummary>
            <core:TaxSummary>
                <core:TaxTypeCoded>Total</core:TaxTypeCoded>
                <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                <core:TaxCategoryCodedOther>19.60</core:TaxCategoryCodedOther>
                <core:TaxableAmount>1057.50</core:TaxableAmount>
                <core:TaxAmount>207.27</core:TaxAmount>
                <core:TaxAmountInTaxAccountingCurrency>207.27</core:TaxAmountInTaxAccountingCurrency>
            </core:TaxSummary>
        </ListOfTaxSummary>
    </InvoiceSummary>
</Invoice>
