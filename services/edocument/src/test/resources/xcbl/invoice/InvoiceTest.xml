<Invoice xmlns:ns4="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" xmlns:ns2="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:ns3="http://www.w3.org/2000/09/xmldsig#" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd">
    <InvoiceHeader>
        <InvoiceNumber></InvoiceNumber>
        <InvoiceIssueDate>2018-07-26T10:54:21.063+03:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <ns2:BuyerOrderNumber>4500005694</ns2:BuyerOrderNumber>
                <ns2:PurchaseOrderDate>2003-01-01T00:00:01+02:00</ns2:PurchaseOrderDate>
            </PurchaseOrderReference>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
        </InvoiceType>
        <InvoiceCurrency>
            <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
        </InvoiceCurrency>
        <PaymentCurrency>
            <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
        </PaymentCurrency>
        <TaxAccountingCurrency>
            <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
        </TaxAccountingCurrency>
        <InvoiceLanguage>
            <ns2:LanguageCoded>en</ns2:LanguageCoded>
        </InvoiceLanguage>
        <InvoiceDates>
            <InvoiceDueDate>2018-08-25T10:54:21.063+03:00</InvoiceDueDate>
            <ExpectedShipDate>2001-10-20T12:00:00+03:00</ExpectedShipDate>
            <ActualShipDate>2001-10-20T12:00:00+03:00</ActualShipDate>
            <ExpectedDeliveryDate>2001-10-20T00:00:13+03:00</ExpectedDeliveryDate>
            <ActualDeliveryDate>2001-10-20T00:00:13+03:00</ActualDeliveryDate>
            <InvoicingPeriod>
                <ns2:StartDate/>
                <ns2:EndDate/>
            </InvoicingPeriod>
            <ListOfOtherInvoiceDates/>
        </InvoiceDates>
        <InvoiceParty>
            <BuyerParty xsi:type="ns2:InvoiceOCRPartyType" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <ns2:PartyID>
                    <ns2:Agency>
                        <ns2:AgencyCoded>AssignedBySellerOrSellersAgent</ns2:AgencyCoded>
                    </ns2:Agency>
                    <ns2:Ident>MAF_Id</ns2:Ident>
                </ns2:PartyID>
                <ns2:ListOfIdentifier>
                    <ns2:Identifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>RCS-RCM</ns2:AgencyCodedOther>
                            <ns2:AgencyDescription>French Trade and Companies Register</ns2:AgencyDescription>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:Identifier>
                    <ns2:Identifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>FR-INSEE</ns2:AgencyCoded>
                            <ns2:CodeListIdentifierCoded>Other</ns2:CodeListIdentifierCoded>
                            <ns2:CodeListIdentifierCodedOther>SIREN</ns2:CodeListIdentifierCodedOther>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:Identifier>
                </ns2:ListOfIdentifier>
                <ns2:NameAddress>
                    <ns2:Name1>ABC Enterprises</ns2:Name1>
                    <ns2:POBox POBoxPostalCode="249"></ns2:POBox>
                    <ns2:PostalCode>20012</ns2:PostalCode>
                    <ns2:City>Alpine</ns2:City>
                    <ns2:Region>
                        <ns2:RegionCoded>USNY</ns2:RegionCoded>
                    </ns2:Region>
                    <ns2:Country/>
                </ns2:NameAddress>
                <ns2:PrimaryContact>
                    <ns2:ContactName>Dietl,B.</ns2:ContactName>
                    <ns2:ContactFunction>
                        <ns2:ContactFunctionCoded>DepartmentOrPersonResponsibleForProcessingPurchaseOrder</ns2:ContactFunctionCoded>
                    </ns2:ContactFunction>
                    <ns2:ListOfContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                    </ns2:ListOfContactNumber>
                </ns2:PrimaryContact>
                <ns2:PartyTaxInformation>
                    <ns2:TaxIdentifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>CEC</ns2:AgencyCoded>
                            <ns2:CodeListIdentifierCoded>ValueAddedTaxIdentification</ns2:CodeListIdentifierCoded>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:TaxIdentifier>
                </ns2:PartyTaxInformation>
            </BuyerParty>
            <SellerParty xsi:type="ns2:InvoiceOCRPartyType" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <ns2:PartyID>
                    <ns2:Agency>
                        <ns2:AgencyCoded>AssignedBySellerOrSellersAgent</ns2:AgencyCoded>
                    </ns2:Agency>
                    <ns2:Ident>LRM</ns2:Ident>
                </ns2:PartyID>
                <ns2:ListOfIdentifier>
                    <ns2:Identifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>RCS-RCM</ns2:AgencyCodedOther>
                            <ns2:AgencyDescription>French Trade and Companies Register</ns2:AgencyDescription>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:Identifier>
                    <ns2:Identifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>FR-INSEE</ns2:AgencyCoded>
                            <ns2:CodeListIdentifierCoded>Other</ns2:CodeListIdentifierCoded>
                            <ns2:CodeListIdentifierCodedOther>SIREN</ns2:CodeListIdentifierCodedOther>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:Identifier>
                </ns2:ListOfIdentifier>
                <ns2:NameAddress>
                    <ns2:Name1>Dunn Manufacturing</ns2:Name1>
                    <ns2:Department>Order Department</ns2:Department>
                    <ns2:PostalCode>95006</ns2:PostalCode>
                    <ns2:City>Orange</ns2:City>
                    <ns2:Region>
                        <ns2:RegionCoded>USCA</ns2:RegionCoded>
                    </ns2:Region>
                    <ns2:Country/>
                </ns2:NameAddress>
                <ns2:PrimaryContact>
                    <ns2:ContactName/>
                    <ns2:ListOfContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue/>
                            <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                    </ns2:ListOfContactNumber>
                </ns2:PrimaryContact>
                <ns2:OtherContacts>
                    <ns2:Contact>
                        <ns2:ContactName>Ms Black</ns2:ContactName>
                        <ns2:ContactFunction>
                            <ns2:ContactFunctionCoded>AccountsReceivableContact</ns2:ContactFunctionCoded>
                        </ns2:ContactFunction>
                        <ns2:ListOfContactNumber>
                            <ns2:ContactNumber>
                                <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                                <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                            </ns2:ContactNumber>
                            <ns2:ContactNumber>
                                <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                                <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                            </ns2:ContactNumber>
                        </ns2:ListOfContactNumber>
                    </ns2:Contact>
                    <ns2:Contact>
                        <ns2:ContactName>George Walsh</ns2:ContactName>
                        <ns2:ContactFunction>
                            <ns2:ContactFunctionCoded>DeliveryContact</ns2:ContactFunctionCoded>
                        </ns2:ContactFunction>
                        <ns2:ListOfContactNumber>
                            <ns2:ContactNumber>
                                <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                                <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                            </ns2:ContactNumber>
                            <ns2:ContactNumber>
                                <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                                <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                            </ns2:ContactNumber>
                        </ns2:ListOfContactNumber>
                    </ns2:Contact>
                </ns2:OtherContacts>
                <ns2:PartyTaxInformation>
                    <ns2:TaxIdentifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>CEC</ns2:AgencyCoded>
                            <ns2:CodeListIdentifierCoded>ValueAddedTaxIdentification</ns2:CodeListIdentifierCoded>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:TaxIdentifier>
                </ns2:PartyTaxInformation>
            </SellerParty>
            <ShipToParty xsi:type="ns2:InvoiceOCRPartyType" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <ns2:PartyID>
                    <ns2:Agency>
                        <ns2:AgencyCoded>Le roy merlin city ctr</ns2:AgencyCoded>
                    </ns2:Agency>
                    <ns2:Ident>3000</ns2:Ident>
                </ns2:PartyID>
                <ns2:NameAddress>
                    <ns2:Name1>ABC Enterprises</ns2:Name1>
                    <ns2:StreetSupplement1>255 Marble Court</ns2:StreetSupplement1>
                    <ns2:StreetSupplement2>Marble Industrial Complex</ns2:StreetSupplement2>
                    <ns2:Building>Building</ns2:Building>
                    <ns2:PostalCode>20001</ns2:PostalCode>
                    <ns2:City>New York</ns2:City>
                    <ns2:Region>
                        <ns2:RegionCoded>USNY</ns2:RegionCoded>
                    </ns2:Region>
                    <ns2:Country>
                        <ns2:CountryCoded>US</ns2:CountryCoded>
                    </ns2:Country>
                </ns2:NameAddress>
                <ns2:PrimaryContact>
                    <ns2:ContactName>Ms. Audra Murphy</ns2:ContactName>
                    <ns2:ListOfContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                    </ns2:ListOfContactNumber>
                </ns2:PrimaryContact>
            </ShipToParty>
            <BillToParty xsi:type="ns2:InvoiceOCRPartyType" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <ns2:PartyID>
                    <ns2:Agency>
                        <ns2:AgencyCoded>AssignedBySellerOrSellersAgent</ns2:AgencyCoded>
                    </ns2:Agency>
                    <ns2:Ident>MAF_Id</ns2:Ident>
                </ns2:PartyID>
                <ns2:ListOfIdentifier>
                    <ns2:Identifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>RCS-RCM</ns2:AgencyCodedOther>
                            <ns2:AgencyDescription>French Trade and Companies Register</ns2:AgencyDescription>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:Identifier>
                    <ns2:Identifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>FR-INSEE</ns2:AgencyCoded>
                            <ns2:CodeListIdentifierCoded>Other</ns2:CodeListIdentifierCoded>
                            <ns2:CodeListIdentifierCodedOther>SIREN</ns2:CodeListIdentifierCodedOther>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:Identifier>
                </ns2:ListOfIdentifier>
                <ns2:NameAddress>
                    <ns2:Name1>ABC Enterprises</ns2:Name1>
                    <ns2:POBox POBoxPostalCode="249"></ns2:POBox>
                    <ns2:PostalCode>20012</ns2:PostalCode>
                    <ns2:City>Alpine</ns2:City>
                    <ns2:Region>
                        <ns2:RegionCoded>USNY</ns2:RegionCoded>
                    </ns2:Region>
                    <ns2:Country/>
                </ns2:NameAddress>
                <ns2:PrimaryContact>
                    <ns2:ContactName>Dietl,B.</ns2:ContactName>
                    <ns2:ContactFunction>
                        <ns2:ContactFunctionCoded>DepartmentOrPersonResponsibleForProcessingPurchaseOrder</ns2:ContactFunctionCoded>
                    </ns2:ContactFunction>
                    <ns2:ListOfContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                    </ns2:ListOfContactNumber>
                </ns2:PrimaryContact>
                <ns2:PartyTaxInformation>
                    <ns2:TaxIdentifier>
                        <ns2:Agency>
                            <ns2:AgencyCoded>CEC</ns2:AgencyCoded>
                            <ns2:CodeListIdentifierCoded>ValueAddedTaxIdentification</ns2:CodeListIdentifierCoded>
                        </ns2:Agency>
                        <ns2:Ident/>
                    </ns2:TaxIdentifier>
                </ns2:PartyTaxInformation>
            </BillToParty>
            <ShipFromParty xsi:type="ns2:InvoiceOCRPartyType" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <ns2:PartyID>
                    <ns2:Agency>
                        <ns2:AgencyCoded>Le roy merlin city ctr</ns2:AgencyCoded>
                    </ns2:Agency>
                    <ns2:Ident>3000</ns2:Ident>
                </ns2:PartyID>
                <ns2:NameAddress>
                    <ns2:Name1>ABC Enterprises</ns2:Name1>
                    <ns2:StreetSupplement1>255 Marble Court</ns2:StreetSupplement1>
                    <ns2:StreetSupplement2>Marble Industrial Complex</ns2:StreetSupplement2>
                    <ns2:Building>Building</ns2:Building>
                    <ns2:PostalCode>20001</ns2:PostalCode>
                    <ns2:City>New York</ns2:City>
                    <ns2:Region>
                        <ns2:RegionCoded>USNY</ns2:RegionCoded>
                    </ns2:Region>
                    <ns2:Country>
                        <ns2:CountryCoded>US</ns2:CountryCoded>
                    </ns2:Country>
                </ns2:NameAddress>
                <ns2:PrimaryContact>
                    <ns2:ContactName>Ms. Audra Murphy</ns2:ContactName>
                    <ns2:ListOfContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue>************</ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>TelephoneNumber</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                        <ns2:ContactNumber>
                            <ns2:ContactNumberValue><EMAIL></ns2:ContactNumberValue>
                            <ns2:ContactNumberTypeCoded>EmailAddress</ns2:ContactNumberTypeCoded>
                        </ns2:ContactNumber>
                    </ns2:ListOfContactNumber>
                </ns2:PrimaryContact>
            </ShipFromParty>
        </InvoiceParty>
        <InvoicePaymentInstructions>
            <ns2:PaymentTerms>
            	<ns2:NetDueDate/>
                <ns2:PaymentTerm>
                    <ns2:PaymentTermCoded>LatePayment</ns2:PaymentTermCoded>
                    <ns2:PaymentTermDescription/>
                </ns2:PaymentTerm>
                <ns2:PaymentTermsNote/>
            </ns2:PaymentTerms>
        </InvoicePaymentInstructions>
        <InvoiceAllowancesOrCharges/>
        <ListOfStructuredNote>
            <ns2:StructuredNote>
                <ns2:GeneralNote>Terms Of Delivery Header manually entered</ns2:GeneralNote>
                <ns2:NoteID>Terms of delivery</ns2:NoteID>
                <ns2:Agency>
                    <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                    <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                </ns2:Agency>
            </ns2:StructuredNote>
        </ListOfStructuredNote>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <ns2:BuyerLineItemNum>10</ns2:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <ns2:PartNumbers>
                            <ns2:SellerPartNumber>
                                <ns2:PartID>LRM</ns2:PartID>
                            </ns2:SellerPartNumber>
                            <ns2:BuyerPartNumber>
                                <ns2:PartID>MAF_Id</ns2:PartID>
                            </ns2:BuyerPartNumber>
                            <ns2:StandardPartNumber>
                                <ns2:ProductIdentifierQualifierCoded>EAN2-5-5-1</ns2:ProductIdentifierQualifierCoded>
                                <ns2:ProductIdentifierQualifierCodedOther></ns2:ProductIdentifierQualifierCodedOther>
                                <ns2:ProductIdentifier>2000011163701</ns2:ProductIdentifier>
                                <ns2:ProductIdentifierExt></ns2:ProductIdentifierExt>
                            </ns2:StandardPartNumber>
                        </ns2:PartNumbers>
                        <ns2:ItemDescription>Description of product</ns2:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <ns2:QuantityValue>111</ns2:QuantityValue>
                        <ns2:UnitOfMeasurement>
                            <ns2:UOMCoded>EA</ns2:UOMCoded>
                        </ns2:UnitOfMeasurement>
                    </InvoicedQuantity>
                    <ListOfQuantityCoded>
                        <ns2:QuantityCoded>
                            <ns2:QuantityValue/>
                            <ns2:QuantityQualifierCoded>FreeQuantityNotIncluded</ns2:QuantityQualifierCoded>
                        </ns2:QuantityCoded>
                    </ListOfQuantityCoded>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <ns2:ListOfPrice>
                        <ns2:Price>
                            <ns2:UnitPrice>
                                <ns2:UnitPriceValue>10.00</ns2:UnitPriceValue>
                                <ns2:Currency>
                                    <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
                                </ns2:Currency>
                            </ns2:UnitPrice>
                            <ns2:CalculatedPriceBasisQuantity>
                                <ns2:QuantityValue>1</ns2:QuantityValue>
                                <ns2:UnitOfMeasurement>
                                    <ns2:UOMCoded>EA</ns2:UOMCoded>
                                </ns2:UnitOfMeasurement>
                            </ns2:CalculatedPriceBasisQuantity>
                        </ns2:Price>
                    </ns2:ListOfPrice>
                    <ns2:LineItemTotal>
                        <ns2:MonetaryAmount>1110.00</ns2:MonetaryAmount>
                    </ns2:LineItemTotal>
                </InvoicePricingDetail>
                <DeliveryDetail>
                    <ns2:ListOfScheduleLine>
                        <ns2:ScheduleLine>
                            <ns2:ScheduleLineID>1095</ns2:ScheduleLineID>
                            <ns2:Quantity>
                                <ns2:QuantityValue>111</ns2:QuantityValue>
                                <ns2:UnitOfMeasurement>
                                    <ns2:UOMCoded>EA</ns2:UOMCoded>
                                </ns2:UnitOfMeasurement>
                            </ns2:Quantity>
                            <ns2:RequestedDeliveryDate>2001-10-21T00:12:00+03:00</ns2:RequestedDeliveryDate>
                        </ns2:ScheduleLine>
                    </ns2:ListOfScheduleLine>
                </DeliveryDetail>
                <LineItemNote>Item text manually entered</LineItemNote>
                <ListOfStructuredNote>
                    <ns2:StructuredNote>
                        <ns2:GeneralNote>Material PO text manually entered</ns2:GeneralNote>
                        <ns2:NoteID>Material PO text</ns2:NoteID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                        </ns2:Agency>
                    </ns2:StructuredNote>
                    <ns2:StructuredNote>
                        <ns2:GeneralNote>Info Record PO Text manually entered</ns2:GeneralNote>
                        <ns2:NoteID>Info record PO text</ns2:NoteID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                        </ns2:Agency>
                    </ns2:StructuredNote>
                    <ns2:StructuredNote>
                        <ns2:GeneralNote>Delivery text manually entered</ns2:GeneralNote>
                        <ns2:NoteID>Delivery text</ns2:NoteID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                        </ns2:Agency>
                    </ns2:StructuredNote>
                </ListOfStructuredNote>
            </InvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <ns2:BuyerLineItemNum>11</ns2:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <ns2:PartNumbers>
                            <ns2:SellerPartNumber>
                                <ns2:PartID>R-3456</ns2:PartID>
                            </ns2:SellerPartNumber>
                            <ns2:BuyerPartNumber>
                                <ns2:PartID>R-3456</ns2:PartID>
                            </ns2:BuyerPartNumber>
                        </ns2:PartNumbers>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <ns2:QuantityValue>1</ns2:QuantityValue>
                        <ns2:UnitOfMeasurement>
                            <ns2:UOMCoded>EA</ns2:UOMCoded>
                        </ns2:UnitOfMeasurement>
                    </InvoicedQuantity>
                    <ListOfQuantityCoded>
                        <ns2:QuantityCoded>
                            <ns2:QuantityValue/>
                            <ns2:QuantityQualifierCoded>FreeQuantityNotIncluded</ns2:QuantityQualifierCoded>
                        </ns2:QuantityCoded>
                    </ListOfQuantityCoded>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <ns2:ListOfPrice>
                        <ns2:Price>
                            <ns2:UnitPrice>
                                <ns2:UnitPriceValue>1000.00</ns2:UnitPriceValue>
                                <ns2:Currency>
                                    <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
                                </ns2:Currency>
                            </ns2:UnitPrice>
                            <ns2:CalculatedPriceBasisQuantity>
                                <ns2:QuantityValue>1</ns2:QuantityValue>
                                <ns2:UnitOfMeasurement>
                                    <ns2:UOMCoded>EA</ns2:UOMCoded>
                                </ns2:UnitOfMeasurement>
                            </ns2:CalculatedPriceBasisQuantity>
                        </ns2:Price>
                    </ns2:ListOfPrice>
                    <ns2:LineItemTotal>
                        <ns2:MonetaryAmount>1000.00</ns2:MonetaryAmount>
                    </ns2:LineItemTotal>
                </InvoicePricingDetail>
                <DeliveryDetail>
                    <ns2:ListOfScheduleLine>
                        <ns2:ScheduleLine>
                            <ns2:Quantity>
                                <ns2:QuantityValue>1</ns2:QuantityValue>
                                <ns2:UnitOfMeasurement>
                                    <ns2:UOMCoded>EA</ns2:UOMCoded>
                                </ns2:UnitOfMeasurement>
                            </ns2:Quantity>
                            <ns2:RequestedDeliveryDate>2001-02-07T00:12:00+02:00</ns2:RequestedDeliveryDate>
                        </ns2:ScheduleLine>
                    </ns2:ListOfScheduleLine>
                </DeliveryDetail>
                <LineItemNote>Item text manually entered</LineItemNote>
                <ListOfStructuredNote>
                    <ns2:StructuredNote>
                        <ns2:GeneralNote>Material PO text manually entered</ns2:GeneralNote>
                        <ns2:NoteID>Material PO text</ns2:NoteID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                        </ns2:Agency>
                    </ns2:StructuredNote>
                    <ns2:StructuredNote>
                        <ns2:GeneralNote>Info Record PO Text manually entered</ns2:GeneralNote>
                        <ns2:NoteID>Info record PO text</ns2:NoteID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                        </ns2:Agency>
                    </ns2:StructuredNote>
                    <ns2:StructuredNote>
                        <ns2:GeneralNote>Delivery text manually entered</ns2:GeneralNote>
                        <ns2:NoteID>Delivery text</ns2:NoteID>
                        <ns2:Agency>
                            <ns2:AgencyCoded>Other</ns2:AgencyCoded>
                            <ns2:AgencyCodedOther>SAP</ns2:AgencyCodedOther>
                        </ns2:Agency>
                    </ns2:StructuredNote>
                </ListOfStructuredNote>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <InvoiceTotals>
            <InvoiceTotal>
                <ns2:MonetaryAmount>2110.00</ns2:MonetaryAmount>
                <ns2:Currency>
                    <ns2:CurrencyCoded>USD</ns2:CurrencyCoded>
                </ns2:Currency>
            </InvoiceTotal>
            <InvoiceSubTotal/>
            <TaxableValue/>
            <TotalTaxAmount/>
        </InvoiceTotals>
    </InvoiceSummary>
</Invoice>
