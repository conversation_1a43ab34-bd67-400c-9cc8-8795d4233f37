<Invoice xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd ../../schema/org/xcbl/path_delim/schemas/xcbl/v4_0/financial/v1_0/financial.xsd">
    <InvoiceHeader>
        <InvoiceNumber>INV: *********</InvoiceNumber>
        <InvoiceIssueDate>2001-02-15T09:00:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <core:BuyerOrderNumber>**********</core:BuyerOrderNumber>
                <core:AccountCode>
                    <core:RefNum>ABC-99067-3000</core:RefNum>
                </core:AccountCode>
                <core:PurchaseOrderDate>2001-02-03T12:00:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
        </InvoiceType>
        <InvoiceCurrency>
            <core:CurrencyCoded>USD</core:CurrencyCoded>
        </InvoiceCurrency>
        <InvoiceLanguage>
            <core:LanguageCoded>en</core:LanguageCoded>
        </InvoiceLanguage>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AssignedBySellerOrSellersAgent</core:AgencyCoded>
                    </core:Agency>
                    <core:Ident>3000</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>ABC Enterprises</core:Name1>
                    <core:POBox POBoxPostalCode="249"/>
                    <core:PostalCode>20012</core:PostalCode>
                    <core:City>Alpine</core:City>
                    <core:Region>
                        <core:RegionCoded>USNY</core:RegionCoded>
                    </core:Region>
                </core:NameAddress>
                <core:PrimaryContact>
                    <core:ContactName>Dietl,B.</core:ContactName>
                    <core:ContactFunction>
                        <core:ContactFunctionCoded>DepartmentOrPersonResponsibleForProcessingPurchaseOrder</core:ContactFunctionCoded>
                    </core:ContactFunction>
                </core:PrimaryContact>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>AssignedBySellerOrSellersAgent</core:AgencyCoded>
                    </core:Agency>
                    <core:Ident>BCVND</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>Dunn Manufacturing</core:Name1>
                    <core:Department>Order Department</core:Department>
                    <core:PostalCode>95006</core:PostalCode>
                    <core:City>Orange</core:City>
                    <core:Region>
                        <core:RegionCoded>USCA</core:RegionCoded>
                    </core:Region>
                </core:NameAddress>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactName>Ms Black</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>AccountsReceivableContact</core:ContactFunctionCoded>
                        </core:ContactFunction>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>************</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            </core:ContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:Contact>
                    <core:Contact>
                        <core:ContactName>George Walsh</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>DeliveryContact</core:ContactFunctionCoded>
                        </core:ContactFunction>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>************</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            </core:ContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:Contact>
                </core:OtherContacts>
            </SellerParty>
        </InvoiceParty>
        <ListOfAttachment>
            <core:Attachment>
                <AttachmentPurpose>Transport</AttachmentPurpose>
                <AttachmentDescription>Transport description</AttachmentDescription>
                <AttachmentLocation>Europe</AttachmentLocation>
                <AttachmentTitle></AttachmentTitle>
                <MIMEType>HTML</MIMEType>
                <FileName>test.XML</FileName>
            </core:Attachment>
            <core:Attachment>
                <AttachmentPurpose>Transport</AttachmentPurpose>
                <AttachmentDescription>Transport description</AttachmentDescription>
                <AttachmentLocation>Europe</AttachmentLocation>
                <AttachmentTitle>Car Transport</AttachmentTitle>
                <MIMEType>PDF</MIMEType>
                <FileName>test.PDF</FileName>
            </core:Attachment>
            <core:Attachment>
                <AttachmentPurpose>Transport</AttachmentPurpose>
                <AttachmentDescription>Transport description</AttachmentDescription>
                <AttachmentLocation>Europe</AttachmentLocation>
                <AttachmentTitle>Car</AttachmentTitle>
                <MIMEType>CSV</MIMEType>
                <FileName>test.XML</FileName>
            </core:Attachment>
        </ListOfAttachment>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>0010</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>R-3456</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>R-3456</core:PartID>
                            </core:BuyerPartNumber>
                        </core:PartNumbers>
                    </ItemIdentifiers>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>R-3456</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>R-3456</core:PartID>
                            </core:BuyerPartNumber>
                        </core:PartNumbers>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>111</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:UnitPrice>
                                <core:UnitPriceValue>10.00</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>1110.00</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>0011</core:BuyerLineItemNum>
                    </LineItemNum>
                    <InvoicedQuantity>
                        <core:QuantityValue>5</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1000.00</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>5000.00</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <NumberOfLines>2</NumberOfLines>
        <InvoiceTotals>
            <InvoiceTotal>
                <core:MonetaryAmount>6110.00</core:MonetaryAmount>
            </InvoiceTotal>
            <InvoiceSubTotal>
                <core:MonetaryAmount>6110.00</core:MonetaryAmount>
                <core:Currency>
                    <core:CurrencyCoded>USD</core:CurrencyCoded>
                </core:Currency>
            </InvoiceSubTotal>
            <TaxableValue>
                <core:MonetaryAmount>4230.90</core:MonetaryAmount>
            </TaxableValue>
            <TotalTaxAmount>
                <core:MonetaryAmount>381.81</core:MonetaryAmount>
            </TotalTaxAmount>
            <TotalAmountPayable>
                <core:MonetaryAmount>6110.00</core:MonetaryAmount>
            </TotalAmountPayable>
            <TotalDiscount>
                <core:MonetaryAmount>61.10</core:MonetaryAmount>
            </TotalDiscount>
            <TotalAmountMinusDiscount>
                <core:MonetaryAmount>6048.90</core:MonetaryAmount>
            </TotalAmountMinusDiscount>
        </InvoiceTotals>
    </InvoiceSummary>
</Invoice>
