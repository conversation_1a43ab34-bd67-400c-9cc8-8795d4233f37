<?xml version="1.0" encoding="UTF-8"?>
<root>
    <decimalNumbers>
        <validNumbers>
            <argsNumbers>
                <number>20.0</number>
                <number>+20.00</number>
                <number>020</number>
                <number>0020.00</number>
                <number> 020.00</number>
            </argsNumbers>
            <fileNumbers>
                <number> 10.0</number>
                <number>+10.000</number>
                <number>00010</number>
                <number>010.00</number>
                <number>10.5</number>
                <number>10.50</number>
                <number>010.5</number>
                <number>00010.50000</number>
            </fileNumbers>
        </validNumbers>
        <invalidNumbers>
            <argsNumbers>
                <decimalNumber>5.05</decimalNumber>
                <negativeDecimalNumber>-15</negativeDecimalNumber>
                <positiveDecimalNumber>+15</positiveDecimalNumber>
                <notDecimalNumber>I am not a decimal number</notDecimalNumber>
            </argsNumbers>
            <fileNumbers>
                <notDecimalNumber>@!#</notDecimalNumber>
                <decimalNumber>30.000</decimalNumber>
                <invalidDecimalNumber>10.$0</invalidDecimalNumber>
            </fileNumbers>
        </invalidNumbers>
    </decimalNumbers>
</root>
