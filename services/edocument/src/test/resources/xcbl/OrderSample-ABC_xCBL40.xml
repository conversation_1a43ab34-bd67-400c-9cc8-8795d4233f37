<Order xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd ../../schema/org/xcbl/path_delim/schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd">
    <OrderHeader>
        <OrderNumber>
            <BuyerOrderNumber>**********</BuyerOrderNumber>
        </OrderNumber>
        <OrderIssueDate>2003-01-01T00:00:01</OrderIssueDate>
        <OrderReferences>
            <AccountCode>
                <core:RefNum>3001</core:RefNum>
            </AccountCode>
            <ContractReferences>
                <core:Contract>
                    <core:ContractID>
                        <core:Agency>
                            <core:AgencyCoded>AssignedByPartyOriginatingTheMessage</core:AgencyCoded>
                        </core:Agency>
                        <core:Ident>CONTRACT:002-91</core:Ident>
                    </core:ContractID>
                    <core:TypeOfContract>
                        <core:ContractTypeCoded>FixedPriceIncentive</core:ContractTypeCoded>
                     </core:TypeOfContract>
                    <core:ValidityDates>
                        <core:StartDate>2000-06-04T00:00:03</core:StartDate>
                        <core:EndDate>2000-06-15T00:00:04</core:EndDate>
                    </core:ValidityDates>
                    <core:SystemID>Order/OrderHeader/OrderReferences/ContractReferences/core:Contract/core:SystemID</core:SystemID>
                    <core:ContractItemNumber>Order/OrderHeader/OrderReferences/ContractReferences/core:Contract/core:ContractItemNumber</core:ContractItemNumber>
                </core:Contract>
            </ContractReferences>
            <OtherOrderReferences>
                <core:ReferenceCoded>
                    <core:ReferenceTypeCoded>RequestNumber</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>REF-002-99-0-3000</core:RefNum>
                        <core:RefDate>2001-01-04T00:00:09</core:RefDate>
                    </core:PrimaryReference>
                    <core:ReferenceDescription>Buyers OrderRequest Number</core:ReferenceDescription>
                </core:ReferenceCoded>
            </OtherOrderReferences>
        </OrderReferences>
        <Purpose>
            <core:PurposeCoded>Original</core:PurposeCoded>
        </Purpose>
        <RequestedResponse>
            <core:RequestedResponseCoded>ResponseExpected</core:RequestedResponseCoded>
        </RequestedResponse>
            <OrderCurrency>
            <core:CurrencyCoded>USD</core:CurrencyCoded>
        </OrderCurrency>
        <OrderLanguage>
            <core:LanguageCoded>en</core:LanguageCoded>
        </OrderLanguage>
        <OrderDates>
            <RequestedShipByDate>2001-10-20T12:00:00</RequestedShipByDate>
            <RequestedDeliverByDate>2001-10-20T00:00:13</RequestedDeliverByDate>
        </OrderDates>
        <OrderParty>
        <BuyerParty>
            <core:PartyID>
                <core:Agency>
                    <core:AgencyCoded>AssignedBySellerOrSellersAgent</core:AgencyCoded>
               </core:Agency>
                <core:Ident>MAF_Id</core:Ident>
            </core:PartyID>
            <core:NameAddress>
                <core:Name1>ABC Enterprises</core:Name1>
                <core:POBox POBoxPostalCode="249"></core:POBox>
                <core:PostalCode>20012</core:PostalCode>
                <core:City>Alpine</core:City>
                <core:Region>
                    <core:RegionCoded>USNY</core:RegionCoded>
                </core:Region>
            </core:NameAddress>
            <core:PrimaryContact>
                <core:ContactName>Dietl,B.</core:ContactName>
                <core:ContactFunction>
                    <core:ContactFunctionCoded>DepartmentOrPersonResponsibleForProcessingPurchaseOrder</core:ContactFunctionCoded>
                </core:ContactFunction>
                <core:ListOfContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue>************</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                </core:ListOfContactNumber>
            </core:PrimaryContact>
        </BuyerParty>
        <SellerParty>
            <core:PartyID>
                <core:Agency>
                    <core:AgencyCoded>AssignedBySellerOrSellersAgent</core:AgencyCoded>
                </core:Agency>
                <core:Ident>LRM</core:Ident>
            </core:PartyID>
            <core:NameAddress>
                <core:Name1>Dunn Manufacturing</core:Name1>
                <core:Department>Order Department</core:Department>
                <core:PostalCode>95006</core:PostalCode>
                <core:City>Orange</core:City>
                <core:Region>
                    <core:RegionCoded>USCA</core:RegionCoded>
                </core:Region>
            </core:NameAddress>
            <core:OtherContacts>
            <core:Contact>
                <core:ContactName>Ms Black</core:ContactName>
                <core:ContactFunction>
                    <core:ContactFunctionCoded>AccountsReceivableContact</core:ContactFunctionCoded>
                </core:ContactFunction>
                <core:ListOfContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue>************</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                </core:ListOfContactNumber>
            </core:Contact>
                <core:Contact>
                <core:ContactName>George Walsh</core:ContactName>
                <core:ContactFunction>
                    <core:ContactFunctionCoded>DeliveryContact</core:ContactFunctionCoded>
                </core:ContactFunction>
                <core:ListOfContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue>************</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                </core:ListOfContactNumber>
                </core:Contact>
            </core:OtherContacts>
        </SellerParty>
        <ShipToParty>
            <core:PartyID>
                <core:Agency>
                    <core:AgencyCoded>Le roy merlin city ctr</core:AgencyCoded>
                </core:Agency>
                <core:Ident>3000</core:Ident>
            </core:PartyID>
            <core:NameAddress>
                <core:Name1>ABC Enterprises</core:Name1>
                <core:StreetSupplement1>255 Marble Court</core:StreetSupplement1>
                <core:StreetSupplement2>Marble Industrial Complex</core:StreetSupplement2>
                <core:Building>Building</core:Building>
                <core:PostalCode>20001</core:PostalCode>
                <core:City>New York</core:City>
                <core:Region>
                    <core:RegionCoded>USNY</core:RegionCoded>
                </core:Region>
                <core:Country>
                    <core:CountryCoded>US</core:CountryCoded>
                </core:Country>
            </core:NameAddress>
            <core:PrimaryContact>
                <core:ContactName>Ms. Audra Murphy</core:ContactName>
                <core:ListOfContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue>************</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                </core:ListOfContactNumber>
            </core:PrimaryContact>
        </ShipToParty>
        <ShipFromParty>
            <core:PartyID>
                <core:Agency>
                    <core:AgencyCoded>Le roy merlin city ctr</core:AgencyCoded>
                </core:Agency>
                <core:Ident>3000</core:Ident>
            </core:PartyID>
            <core:NameAddress>
                <core:Name1>ABC Enterprises</core:Name1>
                <core:StreetSupplement1>255 Marble Court</core:StreetSupplement1>
                <core:StreetSupplement2>Marble Industrial Complex</core:StreetSupplement2>
                <core:Building>Building</core:Building>
                <core:PostalCode>20001</core:PostalCode>
                <core:City>New York</core:City>
                <core:Region>
                    <core:RegionCoded>USNY</core:RegionCoded>
                </core:Region>
                <core:Country>
                    <core:CountryCoded>US</core:CountryCoded>
                </core:Country>
            </core:NameAddress>
            <core:PrimaryContact>
                <core:ContactName>Ms. Audra Murphy</core:ContactName>
                <core:ListOfContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue>************</core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                    <core:ContactNumber>
                        <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                        <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                    </core:ContactNumber>
                </core:ListOfContactNumber>
            </core:PrimaryContact>
        </ShipFromParty>
        </OrderParty>
        <OrderTermsOfDelivery>
            <core:TermsOfDeliveryFunctionCoded>DeliveryCondition</core:TermsOfDeliveryFunctionCoded>
            <core:ShipmentMethodOfPaymentCoded>PaidBySupplierOrSeller</core:ShipmentMethodOfPaymentCoded>
            <core:TermsOfDeliveryDescription>New York City, NY</core:TermsOfDeliveryDescription>
        </OrderTermsOfDelivery>
        <OrderHeaderNote>Header Text manually entered</OrderHeaderNote>
        <ListOfStructuredNote>
            <core:StructuredNote>
                <core:GeneralNote>Terms Of Delivery Header manually entered</core:GeneralNote>
                <core:NoteID>Terms of delivery</core:NoteID>
                <core:Agency>
                    <core:AgencyCoded>Other</core:AgencyCoded>
                    <core:AgencyCodedOther>SAP</core:AgencyCodedOther>
                </core:Agency>
            </core:StructuredNote>
        </ListOfStructuredNote>
    </OrderHeader>
    <OrderDetail>
        <ListOfItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>00010</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>LRM</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>MAF_Id</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>EAN2-5-5-1</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifierQualifierCodedOther></core:ProductIdentifierQualifierCodedOther>
                                <core:ProductIdentifier>2000011163701</core:ProductIdentifier>
                                <core:ProductIdentifierExt></core:ProductIdentifierExt>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                        <core:ItemDescription>Description of product</core:ItemDescription>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>111</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                           </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:UnitPrice>
                                <core:UnitPriceValue>10.00</core:UnitPriceValue>
                                <core:Currency>
                                    <core:CurrencyCoded>USD</core:CurrencyCoded>
                                </core:Currency>
                            </core:UnitPrice>
                            <core:CalculatedPriceBasisQuantity>
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>EA</core:UOMCoded>
                                </core:UnitOfMeasurement>
                            </core:CalculatedPriceBasisQuantity>
                        </core:Price>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                            <core:UOMCodedOther></core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>1110.00</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:ScheduleLineID>1095</core:ScheduleLineID>
                            <core:Quantity>
                                <core:QuantityValue>111</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>EA</core:UOMCoded>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2001-10-21T00:12:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
                <LineItemNote>Item text manually entered</LineItemNote>
                <ListOfStructuredNote>
                    <core:StructuredNote>
                        <core:GeneralNote>Material PO text manually entered</core:GeneralNote>
                        <core:NoteID>Material PO text</core:NoteID>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>SAP</core:AgencyCodedOther>
                        </core:Agency>
                    </core:StructuredNote>
                     <core:StructuredNote>
                        <core:GeneralNote>Info Record PO Text manually entered</core:GeneralNote>
                        <core:NoteID>Info record PO text</core:NoteID>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>SAP</core:AgencyCodedOther>
                        </core:Agency>
                    </core:StructuredNote>
                      <core:StructuredNote>
                        <core:GeneralNote>Delivery text manually entered</core:GeneralNote>
                        <core:NoteID>Delivery text</core:NoteID>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>SAP</core:AgencyCodedOther>
                        </core:Agency>
                    </core:StructuredNote>
              </ListOfStructuredNote>
            </ItemDetail>
            <ItemDetail>
                <BaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>00011</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>R-3456</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>R-3456</core:PartID>
                            </core:BuyerPartNumber>
                        </core:PartNumbers>
                    </ItemIdentifiers>
                    <TotalQuantity>
                        <core:QuantityValue>1</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                           </core:UnitOfMeasurement>
                    </TotalQuantity>
                </BaseItemDetail>
                <PricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1000.00</core:UnitPriceValue>
                                <core:Currency>
                                    <core:CurrencyCoded>USD</core:CurrencyCoded>
                                </core:Currency>
                            </core:UnitPrice>
                            <core:CalculatedPriceBasisQuantity>
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>EA</core:UOMCoded>
                                </core:UnitOfMeasurement>
                            </core:CalculatedPriceBasisQuantity>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>1000.00</core:MonetaryAmount>
                    </core:LineItemTotal>
                </PricingDetail>
                <DeliveryDetail>
                    <core:ListOfScheduleLine>
                        <core:ScheduleLine>
                            <core:Quantity>
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>EA</core:UOMCoded>
                                </core:UnitOfMeasurement>
                            </core:Quantity>
                            <core:RequestedDeliveryDate>2001-02-07T00:12:00</core:RequestedDeliveryDate>
                        </core:ScheduleLine>
                    </core:ListOfScheduleLine>
                </DeliveryDetail>
                <LineItemNote>Item text manually entered</LineItemNote>
                <ListOfStructuredNote>
                    <core:StructuredNote>
                        <core:GeneralNote>Material PO text manually entered</core:GeneralNote>
                        <core:NoteID>Material PO text</core:NoteID>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>SAP</core:AgencyCodedOther>
                        </core:Agency>
                    </core:StructuredNote>
                     <core:StructuredNote>
                        <core:GeneralNote>Info Record PO Text manually entered</core:GeneralNote>
                        <core:NoteID>Info record PO text</core:NoteID>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>SAP</core:AgencyCodedOther>
                        </core:Agency>
                    </core:StructuredNote>
                      <core:StructuredNote>
                        <core:GeneralNote>Delivery text manually entered</core:GeneralNote>
                        <core:NoteID>Delivery text</core:NoteID>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>SAP</core:AgencyCodedOther>
                        </core:Agency>
                    </core:StructuredNote>
              </ListOfStructuredNote>
            </ItemDetail>
        </ListOfItemDetail>
    </OrderDetail>
    <OrderSummary>
        <OrderTotal>
            <core:MonetaryAmount>2110.00</core:MonetaryAmount>
            <core:Currency>
                <core:CurrencyCoded>USD</core:CurrencyCoded>
            </core:Currency>
        </OrderTotal>
    </OrderSummary>
</Order>