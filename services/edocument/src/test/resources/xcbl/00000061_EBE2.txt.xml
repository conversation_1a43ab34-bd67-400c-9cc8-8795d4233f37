<Invoice xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd">
    <InvoiceHeader>
        <InvoiceNumber>00000061</InvoiceNumber>
        <InvoiceIssueDate>2004-03-01T00:00:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <core:BuyerOrderNumber>05052900</core:BuyerOrderNumber>
                <core:PurchaseOrderDate>2004-02-27T00:00:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>
            <ASNNumber>
                <core:RefNum>80062193</core:RefNum>
                <core:RefDate>2004-03-01T00:00:00</core:RefDate>
            </ASNNumber>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
        </InvoiceType>
        <InvoiceCurrency>
            <core:CurrencyCoded>EUR</core:CurrencyCoded>
        </InvoiceCurrency>
        <InvoiceLanguage>
            <core:LanguageCoded>en</core:LanguageCoded>
        </InvoiceLanguage>
        <TaxReference>
            <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
            <core:TaxFunctionQualifierCoded>TaxRelatedInformation</core:TaxFunctionQualifierCoded>
            <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
            <core:TaxCategoryCodedOther>NotApplicable</core:TaxCategoryCodedOther>
            <core:TaxTreatmentCoded>Other</core:TaxTreatmentCoded>
            <core:TaxTreatmentCodedOther>NDT</core:TaxTreatmentCodedOther>
        </TaxReference>
        <InvoiceDates>
            <InvoiceDueDate>2004-05-01T00:00:00</InvoiceDueDate>
            <ActualShipDate>2004-03-02T00:00:00</ActualShipDate>
            <ListOfOtherInvoiceDates>
                <core:DateCoded>
                    <core:Date>2004-03-01T12:32:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>PreparationDateTimeOfDocument</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfOtherInvoiceDates>
        </InvoiceDates>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Ident>3026990099715</core:Ident>
                </core:PartyID>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Ident>3014531200102</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AssignedByNationalTradeAgency</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>BusinessLegalStructureType</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>SOCIETE ANONYME</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>LegalCapital</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>250000 EUR</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>322444987</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                        <core:Ident>322432824 RCS TOURS</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>PEINTOU</core:Name1>
                    <core:Street>8 RUE DE LA CONCORDE</core:Street>
                    <core:PostalCode>37000</core:PostalCode>
                    <core:City>TOURS</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR33322444987</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>PEINTOU</core:RegisteredName>
                </core:PartyTaxInformation>
            </SellerParty>
            <ShipToParty>
                <core:PartyID>
                    <core:Ident>3026990099807</core:Ident>
                </core:PartyID>
            </ShipToParty>
            <BillToParty>
                <core:PartyID>
                    <core:Ident>3026990099739</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>*********</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>DISTRIBUTOUT</core:Name1>
                    <core:Street>8 RUE CAUMARTIN</core:Street>
                    <core:PostalCode>75009</core:PostalCode>
                    <core:City>PARIS</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR36*********</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BillToParty>
            <ListOfPartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <core:Ident>3014531200102</core:Ident>
                    </core:PartyID>
                    <core:PartyRoleCoded>PartytoReceiveInvoiceFOrGoodsOrServices</core:PartyRoleCoded>
                </core:PartyCoded>
            </ListOfPartyCoded>
        </InvoiceParty>
        <InvoicePaymentInstructions>
        </InvoicePaymentInstructions>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Item</core:LineItemTypeCoded>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>GTIN</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>3083630026319</core:ProductIdentifier>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                        <core:ItemDescription>BOITE DE PETITS POIS 1 KG</core:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>2050</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>PCE</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                    <ListOfQuantityCoded>
                        <core:QuantityCoded>
                            <core:QuantityValue>6</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>Other</core:UOMCoded>
                                <core:UOMCodedOther>PCE</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                            <core:QuantityQualifierCoded>NumberOfUnits</core:QuantityQualifierCoded>
                        </core:QuantityCoded>
                    </ListOfQuantityCoded>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.548</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.548</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>5.50</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>3173.40</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>2</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>2</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Item</core:LineItemTypeCoded>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>GTIN</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>3083630026333</core:ProductIdentifier>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                        <core:ItemDescription>BOITE DE CHOUX DE BRUXELLES 1 KG</core:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>470</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>PCE</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                    <ListOfQuantityCoded>
                        <core:QuantityCoded>
                            <core:QuantityValue>3</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>Other</core:UOMCoded>
                                <core:UOMCodedOther>PCE</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                            <core:QuantityQualifierCoded>NumberOfUnits</core:QuantityQualifierCoded>
                        </core:QuantityCoded>
                    </ListOfQuantityCoded>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>2.250</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>2.250</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>5.50</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>1057.50</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <InvoiceTotals>
            <InvoiceTotal>
                <core:MonetaryAmount>4463.60</core:MonetaryAmount>
            </InvoiceTotal>
            <InvoiceSubTotal>
                <core:MonetaryAmount>4463.60</core:MonetaryAmount>
            </InvoiceSubTotal>
            <TaxableValue>
                <core:MonetaryAmount>4230.90</core:MonetaryAmount>
            </TaxableValue>
            <TotalTaxAmount>
                <core:MonetaryAmount>232.70</core:MonetaryAmount>
            </TotalTaxAmount>
        </InvoiceTotals>
        <ListOfTaxSummary>
            <core:TaxSummary>
                <core:TaxTypeCoded>Total</core:TaxTypeCoded>
                <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                <core:TaxCategoryCodedOther>5.50</core:TaxCategoryCodedOther>
                <core:TaxableAmount>4230.90</core:TaxableAmount>
                <core:TaxAmount>232.70</core:TaxAmount>
                <core:TaxAmountInTaxAccountingCurrency>232.70</core:TaxAmountInTaxAccountingCurrency>
            </core:TaxSummary>
        </ListOfTaxSummary>
    </InvoiceSummary>
</Invoice>
