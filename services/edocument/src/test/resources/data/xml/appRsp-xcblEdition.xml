<ApplicationResponse xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/messagemanagement/v1_0/messagemanagement.xsd"
                     xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd"
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/messagemanagement/v1_0/messagemanagement.xsd ../../../../Desktop/Ressources%20All-in-one/xcbl40/schema/org/xcbl/path_delim/schemas/xcbl/v4_0/messagemanagement/v1_0/messagemanagement.xsd">
    <ApplicationResponseHeader>
        <ApplicationResponseIssueDate>2017-08-30T14:45:06</ApplicationResponseIssueDate>
        <ApplicationResponseTypeCoded>Error</ApplicationResponseTypeCoded>
        <ApplicationResponseSender>
            <core:PartyID>
                <core:Ident>3021081134658</core:Ident>
            </core:PartyID>
        </ApplicationResponseSender>
        <ApplicationResponseReceiver>
            <core:PartyID>
                <core:Ident>3760177210012</core:Ident>
            </core:PartyID>
        </ApplicationResponseReceiver>
        <BusinessDocumentTypeCoded>InvoiceResponse</BusinessDocumentTypeCoded>
        <DocumentReference>
            <core:RefNum>GNX3B8589</core:RefNum>
        </DocumentReference>
        <DocumentStatusCoded>WarningError</DocumentStatusCoded>
    </ApplicationResponseHeader>
    <ListOfApplicationResponseDetail>
        <ApplicationResponseDetail>
            <ErrorTypeCoded>BusinessError</ErrorTypeCoded>
            <ErrorInfo>
                <core:CompletionText>Invoice Number</core:CompletionText>
                <core:CompletionMsg>
                    <core:LangString>Format non valide</core:LangString>
                    <core:Language>
                        <core:LanguageCoded>fr</core:LanguageCoded>
                    </core:Language>
                </core:CompletionMsg>
                <core:Severity>
                    <core:SeverityCoded>Warning</core:SeverityCoded>
                </core:Severity>
                <core:ListOfParameter>
                    <core:Parameter>/Invoice/InvoiceHeader/InvoiceNumber</core:Parameter>
                </core:ListOfParameter>
            </ErrorInfo>
        </ApplicationResponseDetail>
        <ApplicationResponseDetail>
            <ErrorTypeCoded>BusinessError</ErrorTypeCoded>
            <ErrorInfo>
                <core:CompletionText>Invoice Number</core:CompletionText>
                <core:CompletionMsg>
                    <core:LangString>Invalid format</core:LangString>
                    <core:Language>
                        <core:LanguageCoded>en</core:LanguageCoded>
                    </core:Language>
                </core:CompletionMsg>
                <core:Severity>
                    <core:SeverityCoded>Warning</core:SeverityCoded>
                </core:Severity>
                <core:ListOfParameter>
                    <core:Parameter>/Invoice/InvoiceHeader/InvoiceNumber</core:Parameter>
                </core:ListOfParameter>
            </ErrorInfo>
        </ApplicationResponseDetail>
    </ListOfApplicationResponseDetail>
</ApplicationResponse>