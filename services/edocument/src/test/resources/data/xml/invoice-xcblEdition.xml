<?xml version="1.0"?>
<Invoice xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <InvoiceHeader>
        <InvoiceNumber>Test</InvoiceNumber>
        <InvoiceIssueDate>2017-09-05T00:00:00+02:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <core:BuyerOrderNumber>01</core:BuyerOrderNumber>
                <core:PurchaseOrderDate>2017-09-05T00:00:00+02:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>
            <ASNNumber>
                <core:RefNum>02</core:RefNum>
                <core:RefDate>2017-09-05T00:00:00+02:00</core:RefDate>
            </ASNNumber>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
        </InvoiceType>
        <InvoiceCurrency>
            <core:CurrencyCoded>EUR</core:CurrencyCoded>
        </InvoiceCurrency>
        <InvoiceLanguage>
            <core:LanguageCoded>fr</core:LanguageCoded>
        </InvoiceLanguage>
        <InvoiceMedium>
            <InvoiceMediumCoded>Other</InvoiceMediumCoded>
            <InvoiceMediumCodedOther>Portal</InvoiceMediumCodedOther>
        </InvoiceMedium>
        <InvoiceDates>
            <InvoiceDueDate>2017-10-05T00:00:00+02:00</InvoiceDueDate>
            <ActualDeliveryDate>2017-09-06T00:00:00+02:00</ActualDeliveryDate>
            <ListOfOtherInvoiceDates>
                <core:DateCoded>
                    <core:Date>2017-09-05T09:39:49.034+02:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>DocumentReceivedDateTime</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfOtherInvoiceDates>
        </InvoiceDates>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>EAN</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>LocationCode</core:CodeListIdentifierCoded>
                    </core:Agency>
                    <core:Ident>3020180005883</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier/>
                <core:NameAddress>
                    <core:Name1>CARREFOUR MARKET AYTRE</core:Name1>
                    <core:Street>RUE NICOLAS GARCOT</core:Street>
                    <core:PostalCode>17440</core:PostalCode>
                    <core:City>AYTRE</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BuyerParty>
            <SellerParty xsi:type="core:PartyCodedType">
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>EAN</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>LocationCode</core:CodeListIdentifierCoded>
                    </core:Agency>
                    <core:Ident>FR4731509319500016</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                        <core:Ident>BYZRef</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>BYZSiren</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AssignedByNationalTradeAgency</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>BusinessLegalStructureType</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>BYZJuridique</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>LegalCapital</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>BYZSociale</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>Byzaneo</core:Name1>
                    <core:Street>1 rue Guillaume Puy</core:Street>
                    <core:PostalCode>84000</core:PostalCode>
                    <core:City>Avignon</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>10</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>BYZRaison</core:RegisteredName>
                </core:PartyTaxInformation>
            </SellerParty>
            <ShipToParty xsi:type="core:PartyCodedType">
                <core:PartyID>
                    <core:Ident>3020180005883</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier/>
                <core:NameAddress>
                    <core:Name1>CARREFOUR MARKET AYTRE</core:Name1>
                    <core:Street>RUE NICOLAS GARCOT</core:Street>
                    <core:PostalCode>17440</core:PostalCode>
                    <core:City>AYTRE</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PrimaryContact/>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </ShipToParty>
            <BillToParty xsi:type="core:PartyCodedType">
                <core:PartyID>
                    <core:Ident>FR37440283752</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>BYZSiren</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>C.S.F.</core:Name1>
                    <core:Street>ZI ROUTE DE PARIS</core:Street>
                    <core:PostalCode>14120</core:PostalCode>
                    <core:City>MONDEVILLE</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PrimaryContact/>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR37440283752</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BillToParty>
        </InvoiceParty>
        <InvoicePaymentInstructions>
            <core:PaymentTerms>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Discount</core:PaymentTermCoded>
                    <core:PaymentTermDescription>Pas d'escompte pour paiement anticipé</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>LatePayment</core:PaymentTermCoded>
                    <core:PaymentTermDescription>Pénalité de 40 EUROS appliquée  pour  non paiement à échéance- art 441.6CC</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>PenaltyTerms</core:PaymentTermCoded>
                    <core:PaymentTermDescription>1,5 FOIS LE TAUX D INTERET LEGAL SI REGLEMENT POSTERIEUR A L ECHEANCE</core:PaymentTermDescription>
                </core:PaymentTerm>
            </core:PaymentTerms>
            <core:PaymentMethod>
                <core:PaymentMeanCoded>Cheque</core:PaymentMeanCoded>
            </core:PaymentMethod>
        </InvoicePaymentInstructions>
        <InvoiceAllowancesOrCharges/>
        <ListOfNameValueSet>
            <core:NameValueSet>
                <core:SetName>KPI</core:SetName>
                <core:ListOfNameValuePair>
                    <core:NameValuePair>
                        <core:Name>Format In</core:Name>
                        <core:Value>User</core:Value>
                        <core:Datatype>Text</core:Datatype>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>Format Out</core:Name>
                        <core:Value>D96A</core:Value>
                        <core:Datatype>Text</core:Datatype>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>Nature</core:Name>
                        <core:Value>Goods</core:Value>
                        <core:Datatype>Text</core:Datatype>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>Sens</core:Name>
                        <core:Value>Entrant</core:Value>
                        <core:Datatype>Text</core:Datatype>
                    </core:NameValuePair>
                </core:ListOfNameValuePair>
            </core:NameValueSet>
        </ListOfNameValueSet>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber/>
                            <core:BuyerPartNumber/>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Other</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifierQualifierCodedOther>Reference</core:ProductIdentifierQualifierCodedOther>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>1</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>PCE</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>0</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>0.00</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>0.00</core:TaxPercent>
                        <core:TaxableAmount>0.00</core:TaxableAmount>
                        <core:TaxAmount>0.00</core:TaxAmount>
                    </core:Tax>
                    <core:LineItemSubTotal>
                        <core:MonetaryAmount>0.00</core:MonetaryAmount>
                    </core:LineItemSubTotal>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>0.00</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <InvoiceTotals>
            <InvoiceTotal>
                <core:MonetaryAmount>0.00</core:MonetaryAmount>
            </InvoiceTotal>
            <InvoiceSubTotal>
                <core:MonetaryAmount>0.00</core:MonetaryAmount>
            </InvoiceSubTotal>
            <TaxableValue>
                <core:MonetaryAmount>0.00</core:MonetaryAmount>
            </TaxableValue>
            <TotalTaxAmount>
                <core:MonetaryAmount>0.00</core:MonetaryAmount>
            </TotalTaxAmount>
        </InvoiceTotals>
        <ListOfTaxSummary>
            <core:TaxSummary>
                <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                <core:TaxCategoryCodedOther>0.00</core:TaxCategoryCodedOther>
                <core:TaxableAmount>0.00</core:TaxableAmount>
                <core:TaxAmount>0.00</core:TaxAmount>
                <core:TaxAmountInTaxAccountingCurrency>0.00</core:TaxAmountInTaxAccountingCurrency>
            </core:TaxSummary>
        </ListOfTaxSummary>
    </InvoiceSummary>
</Invoice>