<?xml version="1.0" encoding="ISO-8859-1"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- E X T E R N A L   P R O P E R T I E S -->
	<bean id="comPropertyConfigurer" class="com.byzaneo.commons.util.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>/gnx.properties</value>
				<value>classpath:/ci/ci.properties</value>
			</list>
		</property>
	</bean>
	
	<!-- S E R V I C E S -->
  	<import resource="classpath:/META-INF/spring/gnx-core.beans.xml" />
	<import resource="classpath:/META-INF/spring/gnx-archive-mongo.beans.xml" />
</beans>
