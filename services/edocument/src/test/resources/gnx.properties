# -----------------------------------------------------------------------------
# P R O P E R T I E S
# Targeted at system administrators, to avoid touching the context XML files.
# -----------------------------------------------------------------------------
# Application owner
app.owner.vat=FR00123456789
version=version-${timestamp}-r${buildNumber}
timezone=Europe/Paris

# -- DIRECTORIES LAYOUT --
webapp.dir = target/webapp
data.dir   = target/test-classes/data
bin.dir   = ${data.dir}/bin
temp.dir   = ${data.dir}/tmp
resources.dir = ${data.dir}/resources
work.dir   = ${data.dir}/work
backup.dir = ${data.dir}/backup
input.dir  = ${data.dir}/in
output.dir = ${data.dir}/out

# -- GNX SafeboxProvider --
safebox.gnx.archive=target/archive
safebox.gnx.archive.local=target/archive/local
safebox.gnx.log=target/log.txt

# -- DATABASE --
# - Connection -
# POSTGRES
database.schema=gnx
database.type=postgres
database.driver=org.postgresql.Driver
database.url=****************************************************
database.username=cipg
database.password=cipg
database.target=org.hibernate.dialect.PostgreSQLDialect
database.datasource=pooledDataSource
# MYSQL
#database.schema		= ci
#database.datasource = pooledDataSource
#database.type       = mysql
#database.driver     = com.mysql.jdbc.Driver
#database.url        = **********************/${database.schema}?characterEncoding=utf8&useCompression=true&zeroDateTimeBehavior=convertToNull&autoReconnect=true&jdbcCompliantTruncation=false
#database.username   = cidbuser
#database.password   = cidbpwd
#database.target     = com.byzaneo.commons.dao.hibernate.support.MySQL5Dialect
# ORACLE
#database.schema		= gnx
#database.datasource = pooledDataSource
#database.monitored.datasource = javaMelodyDataSource
#database.type       = oracle
#database.driver     = oracle.jdbc.OracleDriver
#database.url        = ***********************************
#database.username   = ${database.schema}
#database.password   = gnx
#database.target     = com.byzaneo.commons.dao.hibernate.support.Oracle11gDialect
# - Configuration -
# if database.showSql is set to 'true', hibernate sql is output in the console
database.showSql	= false
# DDL
database.generateDdl		= true
# validate | update | create | create-drop
database.generateDdl.mode	= create-drop
# Comma-separated names of the optional files containing
# SQL DML statements executed if 'database.generateDdl.mode'
# is set to create or create-drop.
# File order matters, the statements of a give file are executed
# before the statements of the following files.
database.generateDdl.imports=/META-INF/data/security.${database.type}.sql,/META-INF/data/gnx.${database.type}.sql,/META-INF/data/gnx-test.${database.type}.sql

database.flyway.disable = true

# -- ARCHIVE BASE --
# -- INDEXATION --
query.mongo.uri=mongodb://ci:ci@ci-mongo/ci
index.mongo.uri=mongodb://ci:ci@ci-mongo/ci
event.mongo.uri=mongodb://ci:ci@ci-mongo/ci

# -- EVENTS --
event.persist=false


# -- MAIL SERVER --
mail.debug          = true
mail.from.name      = Generix CM
mail.from.address   = <EMAIL>
mail.incoming.folder = INBOX
# INCOMING
mail.incoming.protocol = imaps
mail.incoming.host  = imap.generixgroup.com
mail.incoming.port  = 993
mail.incoming.user  = <EMAIL>
mail.incoming.password = #####
mail.incoming.ssl  = true
# OUTGOING
mail.outgoing.protocol = smtp
mail.outgoing.host  = smtp.generixgroup.com
mail.outgoing.port  = 587
mail.outgoing.user  = <EMAIL>
mail.outgoing.password = #####
mail.outgoing.starttls.enable = true
mail.outgoing.auth  = true

# -- TRANSFORM --
# BIRT home directory
transform.birt.engine.dir = /C:/dev/util/birt-runtime/ReportEngine
# Translator home directory
transform.translator.engine.dir = /c:/dev/util/translator

# --RTE --
#rte.home = ${data.dir}/RTE_HOME

# -- WEB SERVER --
webapp.contextname = gnx
webapp.host.url = http://localhost:8080

# -- EXECUTOR --
executor.autostart=false
# the minimum number of threads to keep in the pool
executor.min.pool.size=0
# the maximum number of threads to allow in the pool (negative
# means not bound)
executor.max.pool.size=-1
# keepAliveTime when the number of threads is greater than
# the core, this is the maximum time that excess idle threads
# will wait for new tasks before terminating (in milliseconds).
executor.keepalive=60000
# -1 permet d'utiliser l'executor principal 
report.executor.min.pool.size=${executor.min.pool.size}
report.executor.max.pool.size=${executor.max.pool.size}
report.executor.keepalive=${executor.keepalive}

# -- LOCATION --
# URL registered to http://localhost:8080/
google.api.key = ABQIAAAAexV7NnYlmJgWZ3E5c8qAaBTwM0brOpm-All5BF6PoaKBxRWWERQLq3MpNTP_wRkzmj0ShZMaoxuosQ

# -- BUNDLES --
labels.locale.default=en

# Audit configuration
database.audit.enabled = false


#-- LOGS --
# log4j threshold for logging in the console. Default is ALL
# values : ALL | TRACE | DEBUG | INFO | WARN | ERROR | FATAL | OFF
consoleThreshold=INFO

#GNX FILE SIGNER
#signer.class is any class implementing com.byzaneo.generix.signature.SignatureHandler - can be a mock handler.
#   reference class : com.byzaneo.generix.signature.GnxSignatureHandler
#signer.libraryPath is the path to the library provided with the cryptographic device
#   e.g : /path/to/libCnfpkcs11_Portable.so
#signer.password is the optional pincode of the cryptographic device
#signer.serviceUrl can be left empty in first versions
signer.class=com.byzaneo.generix.signature.GnxSignatureHandler
signer.libraryPath=
signer.password=
signer.serviceUrl=

# -- PERFORMANCES --
log.performance.portlet.task.enable=false

# -- E-DOCUMENT LISTENERS --
edocument.listeners.enabled=true
