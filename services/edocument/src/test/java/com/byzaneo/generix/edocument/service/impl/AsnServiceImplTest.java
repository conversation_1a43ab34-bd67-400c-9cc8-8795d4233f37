package com.byzaneo.generix.edocument.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.generix.edocument.service.OrderService;
import com.byzaneo.generix.edocument.util.AsnXcblHelper;
import com.byzaneo.generix.edocument.util.XcblHelper;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.Indexable;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice;
import com.byzaneo.xtrade.xcbl.bean.Order;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.QuantityType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.QuantityValueType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNBaseItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNOrderNumberType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNQuantitiesType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ListOfASNItemDetailType;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang.mutable.MutableBoolean;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR> Boutour
 * @date 29/10/2015
 */
public class AsnServiceImplTest {

  private MockedStatic<AsnXcblHelper> mockedAsnXcblHelper;
  private MockedStatic<XcblHelper> mockedXcblHelper;
  @InjectMocks
  private AsnServiceImpl asnService;

  @Mock
  private OrderService orderService;

  @Mock
  private DocumentService documentService;

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    setUpStaticMocks();
  }

  void setUpStaticMocks() {
    mockedAsnXcblHelper = Mockito.mockStatic(AsnXcblHelper.class);
    mockedXcblHelper = Mockito.mockStatic(XcblHelper.class);
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedXcblHelper.closeOnDemand();
    mockedAsnXcblHelper.closeOnDemand();
  }

  @Test
  public void createAndSaveAdvanceShipmentNotice_null_exception() {
    assertThrows(IllegalArgumentException.class, () ->
        asnService.createDocumentProcessAndSave(null, null, false, null));
  }

  @Test
  public void createAndSaveAdvanceShipmentNotice_nullDocument_exception() {
    assertThrows(IllegalArgumentException.class, () ->
        asnService.createDocumentProcessAndSave(new AdvanceShipmentNotice(), null, false, null));
  }

  @Test
  public void updateAndSaveAdvanceShipmentNotice_nullDocument_exception() {
    assertThrows(IllegalArgumentException.class, () ->
        asnService.updateDocumentStatusProcessAndSave(new AdvanceShipmentNotice(), null, null,
            false, null));
  }

  @Test
  public void createAndSaveAdvanceShipmentNotice_failedCreation_exception() {
    Document document = new Document();
    AdvanceShipmentNotice advanceShipmentNotice = new AdvanceShipmentNotice();
    assertThrows(ServiceException.class, () -> {
      mockedAsnXcblHelper.when(
              () -> AsnXcblHelper.createASNDocument(eq(document), eq(advanceShipmentNotice), eq(null),
                  eq(null), eq(null), eq(false), eq(false)))
          .thenReturn(Optional.<Document>empty());

      try {
        asnService.createDocumentProcessAndSave(advanceShipmentNotice, document, false, null);
      } catch (final Exception e) {
        throw e;
      } finally {
        mockedAsnXcblHelper.verify(
            () -> AsnXcblHelper.createASNDocument(eq(document), eq(advanceShipmentNotice), eq(null),
                eq(null), eq(null), eq(false), eq(false)), times(1));
      }
    });
  }

  @Test
  public void createAndSaveAdvanceShipmentNotice_noCallback_exception() throws Exception {
    ArgumentCaptor<AdvanceShipmentNotice> asnCaptor = ArgumentCaptor.forClass(
        AdvanceShipmentNotice.class);
    mockedAsnXcblHelper.when(
            () -> AsnXcblHelper.createASNDocument(any(Document.class), asnCaptor.capture(), eq(null),
                eq(null), eq(null), eq(false), eq(false)))
        .thenAnswer(invoc -> Optional.of(new Document() {
          private static final long serialVersionUID = -2382208630460613948L;

          {
            setIndexValue(asnCaptor.getValue());
          }
        }));
    when(documentService.getDocument(any(Indexable.class))).thenReturn(new Document());
    when(documentService.saveDocument(any(Document.class))).thenReturn(new Document());
    when(orderService.getOrdersFromBuyerOrderNumberAndOwner(anyString(), eq("JUNIT"))).thenReturn(
        Optional.of(new Order()));

    final ASNOrderNumberType asnOrderNumberType = new ASNOrderNumberType();
    asnOrderNumberType.setBuyerOrderNumber("JUNIT");
    final ASNHeaderType asnHeaderType = new ASNHeaderType();
    asnHeaderType.getASNOrderNumber()
        .add(asnOrderNumberType);
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNHeader(asnHeaderType);

    assertNotNull(asnService.createDocumentProcessAndSave(asn, new Document(), false, null));
  }

  @Test
  public void createAndSaveAdvanceShipmentNotice_callback_exception() throws Exception {
    ArgumentCaptor<AdvanceShipmentNotice> asnCaptor = ArgumentCaptor.forClass(
        AdvanceShipmentNotice.class);
    mockedAsnXcblHelper.when(
            () -> AsnXcblHelper.createASNDocument(any(Document.class), asnCaptor.capture(), eq(null),
                eq(null), eq(null), eq(false), eq(false)))
        .thenAnswer(invoc -> Optional.of(new Document() {
          private static final long serialVersionUID = -4071997680789915970L;

          {
            setIndexValue(asnCaptor.getValue());
          }
        }));
    when(documentService.getDocument(any(Indexable.class))).thenReturn(new Document());
    when(documentService.saveDocument(any(Document.class))).thenReturn(new Document());
    when(orderService.getOrdersFromBuyerOrderNumberAndOwner(anyString(), eq("JUNIT"))).thenReturn(
        Optional.of(new Order()));

    final ASNOrderNumberType asnOrderNumberType = new ASNOrderNumberType();
    asnOrderNumberType.setBuyerOrderNumber("JUNIT");
    final ASNHeaderType asnHeaderType = new ASNHeaderType();
    asnHeaderType.getASNOrderNumber()
        .add(asnOrderNumberType);
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNHeader(asnHeaderType);

    final MutableBoolean check = new MutableBoolean(false);
    assertNotNull(asnService.createDocumentProcessAndSave(asn, new Document(), false,
        document -> check.setValue(true)));
    assertTrue(check.booleanValue());
  }

  @Test
  public void getOrders_null_emptyMap() {
    final Map<String, Optional<Order>> result = asnService.getOrdersByBuyerOrderNumber(null);

    assertEquals(0, result.size());
  }

  @Test
  public void getOrders_noHeader_emptyMap() {
    final Map<String, Optional<Order>> result = asnService.getOrdersByBuyerOrderNumber(
        new AdvanceShipmentNotice());

    assertEquals(0, result.size());
  }

  @Test
  public void getOrders_emptyNumber_emptyMap() {
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNHeader(new ASNHeaderType());

    final Map<String, Optional<Order>> result = asnService.getOrdersByBuyerOrderNumber(asn);

    assertEquals(0, result.size());
  }

  @Test
  public void getOrders_nullNumber_emptyMap() {
    final ASNHeaderType asnHeader = new ASNHeaderType();
    asnHeader.getASNOrderNumber()
        .add(null);
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNHeader(asnHeader);

    final Map<String, Optional<Order>> result = asnService.getOrdersByBuyerOrderNumber(asn);

    assertEquals(0, result.size());
  }

  @Test
  public void getOrders_duplicateNumber_emptyMap() {
    //updated Matchers with mockito
    when(orderService.getOrderFromBuyerOrderNumberAndOwnerAndFromAndTo(
        anyString(), anyString(), anyString(), anyString(),
        any(DocumentStatus.class)))
        .thenReturn(Optional.empty());
    when(orderService.convertToOrderType(Optional.empty(), false)).thenReturn(Optional.empty());

    final ASNOrderNumberType asnOrderNumber = new ASNOrderNumberType();
    asnOrderNumber.setBuyerOrderNumber("Junit");
    final ASNHeaderType asnHeader = new ASNHeaderType();
    asnHeader.getASNOrderNumber()
        .add(asnOrderNumber);
    asnHeader.getASNOrderNumber()
        .add(new ASNOrderNumberType());
    asnHeader.getASNOrderNumber()
        .add(asnOrderNumber);
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNHeader(asnHeader);

    final Map<String, Optional<Order>> result = asnService.getOrdersByBuyerOrderNumber(asn);

    assertEquals(1, result.size());
  }

  @Test
  public void testIsNotPartialWithNoItem() {
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNDetail(new ASNDetailType());
    asn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());

    assertFalse(asnService.isPartial(asn));
  }

  @Test
  public void testIsPartialWithOneItemInteger() {
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNDetail(new ASNDetailType());
    asn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());
    ASNItemDetailType item = new ASNItemDetailType();
    item.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("100"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item);

    assertTrue(asnService.isPartial(asn));
  }

  @Test
  public void testIsNotPartialWithOneItemInteger() {
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNDetail(new ASNDetailType());
    asn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());
    ASNItemDetailType item = new ASNItemDetailType();
    item.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("0"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item);

    assertFalse(asnService.isPartial(asn));
  }

  @Test
  public void testIsPartialWithManyItemsInteger() {
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNDetail(new ASNDetailType());
    asn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());

    ASNItemDetailType item = new ASNItemDetailType();
    item.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("100"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item);

    ASNItemDetailType item1 = new ASNItemDetailType();
    item1.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item1.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("0"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item1);

    assertTrue(asnService.isPartial(asn));
  }

  @Test
  public void testIsNotPartialWithManyItemsInteger() {
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNDetail(new ASNDetailType());
    asn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());

    ASNItemDetailType item = new ASNItemDetailType();
    item.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("0"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item);

    ASNItemDetailType item1 = new ASNItemDetailType();
    item1.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item1.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("0.00"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item1);

    assertFalse(asnService.isPartial(asn));
  }

  @Test
  public void testIsPartialWithOneItemDouble() {
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNDetail(new ASNDetailType());
    asn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());
    ASNItemDetailType item = new ASNItemDetailType();
    item.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("0.1"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item);

    assertTrue(asnService.isPartial(asn));
  }

  @Test
  public void testIsPartialWithManyItemsDouble() {
    final AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNDetail(new ASNDetailType());
    asn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());

    ASNItemDetailType item1 = new ASNItemDetailType();
    item1.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item1.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item1.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("0.01"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item1);

    ASNItemDetailType item2 = new ASNItemDetailType();
    item2.setASNBaseItemDetail(new ASNBaseItemDetailType());
    item2.getASNBaseItemDetail()
        .setASNQuantities(new ASNQuantitiesType());
    item2.getASNBaseItemDetail()
        .getASNQuantities()
        .setBackOrderedQuantity(new QuantityType());
    item2.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .setQuantityValue(new QuantityValueType());
    item2.getASNBaseItemDetail()
        .getASNQuantities()
        .getBackOrderedQuantity()
        .getQuantityValue()
        .setValue(new BigDecimal("7.30"));
    asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .add(item2);

    assertTrue(asnService.isPartial(asn));
  }
}
