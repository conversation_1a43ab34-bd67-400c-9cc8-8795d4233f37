package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.XcblHelper.nextDay;
import static com.byzaneo.xtrade.api.DocumentStatus.PENDING;
import static com.byzaneo.xtrade.api.DocumentStatus.SENT;
import static com.byzaneo.xtrade.api.DocumentStatus.SENT_PARTIALLY;
import static com.byzaneo.xtrade.api.DocumentType.DESADV;
import static org.junit.jupiter.api.Assertions.*;

import java.io.InputStream;
import java.util.*;

import javax.xml.bind.*;
import javax.xml.transform.stream.StreamSource;

import org.apache.commons.lang3.time.DateUtils;

import com.byzaneo.generix.service.repository.bean.Carrier;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TransportRoutingType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.*;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

public class AsnXcblHelperTest {
  private static Order order;

  @BeforeAll
  public static void setUp() throws Exception {
    final JAXBContext jc = JAXBContext.newInstance(Order.class);
    final Unmarshaller unmarshaller = jc.createUnmarshaller();
    final InputStream orderStream = InvoiceXcblHelperTest.class.getResourceAsStream("/xcbl/OrderSample-ABC_xCBL40.xml");
    order = unmarshaller.unmarshal(new StreamSource(orderStream), Order.class)
        .getValue();
  }

  @Test
  public void testCreateAdvanceShipmentNoticeParty() {
    ASNPartyType party = AsnXcblHelper.createAdvanceShipmentNoticeParty(order);
    OrderPartyType orderParty = order.getOrderHeader()
        .getOrderParty();

    XcblHelper.checkCountry(orderParty.getBuyerParty());
    XcblHelper.checkCountry(orderParty.getSellerParty());
    XcblHelper.checkCountry(orderParty.getShipToParty());
    XcblHelper.checkCountry(orderParty.getShipFromParty());

    assertEquals(party.getBuyerParty(), orderParty.getBuyerParty());
    assertEquals(party.getSellerParty(), orderParty.getSellerParty());
    assertEquals(party.getShipToParty(), orderParty.getShipToParty());
    assertEquals(party.getBillToParty(), orderParty.getBillToParty());
    assertEquals(party.getRemitToParty(), orderParty.getRemitToParty());
    assertEquals(party.getShipFromParty(), orderParty.getShipFromParty());
    assertEquals(party.getWarehouseParty(), orderParty.getWarehouseParty());
    assertEquals(party.getSoldToParty(), orderParty.getSoldToParty());
    assertEquals(party.getManufacturingParty(), orderParty.getManufacturingParty());
    assertEquals(party.getListOfPartyCoded(), orderParty.getListOfPartyCoded());
  }

  @Test
  public void testCreateAdvanceShipmentNoticeDetail() {
    ItemDetailType item1 = order.getOrderDetail()
        .getListOfItemDetail()
        .getItemDetail()
        .get(0);
    ItemDetailType item2 = order.getOrderDetail()
        .getListOfItemDetail()
        .getItemDetail()
        .get(1);
    ASNItemDetailType asnDetail = AsnXcblHelper.createAdvanceShipmentNoticeDetail("1234567", item1, true);
    assertEquals(item1.getLineItemNote(), asnDetail.getLineItemNote());
    assertEquals(item1.getListOfStructuredNote()
        .getStructuredNote()
        .size(), asnDetail.getListOfStructuredNote()
            .getStructuredNote()
            .size());
    asnDetail.getListOfStructuredNote()
        .getStructuredNote()
        .forEach(struct ->
          item1.getListOfStructuredNote()
              .getStructuredNote()
              .contains(struct));
    assertEquals(item1.getBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getBuyerPartNumber()
        .getPartID(), asnDetail.getASNBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .getBuyerPartNumber()
            .getPartID());
  }

  @Test
  public void testUpdateASNTransportRouting() {
    String carrierName = "UPS";
    String carrierId = "123000";
    TransportRoutingType transportRoutingType = new TransportRoutingType();
    Carrier carrier = new Carrier();
    carrier.setName(carrierName);
    carrier.setCarrierId(carrierId);
    carrier.setCarrierType(Carrier.CarrierType.TRUCK);
    carrier.setCarrierMode(Carrier.CarrierMode.ROAD_TRANSPORT);
    AsnXcblHelper.updateASNTransportRouting(transportRoutingType, carrier);
    assertEquals(carrierId, transportRoutingType.getCarrierID()
        .getIdent());
    assertEquals(carrierName, transportRoutingType.getCarrierName());
    assertEquals(Carrier.CarrierMode.ROAD_TRANSPORT.name(), transportRoutingType.getTransportMode()
        .getTransportModeCoded()
        .name());
    assertEquals(Carrier.CarrierType.TRUCK.getValue(), transportRoutingType.getTransportMeans()
        .getTransportMeansCoded());
  }

  @Test
  public void testPrepareASNDocumentBeforeCreated() {
    Document document = new Document();
    document.setOwners("Gnx");
    Document desadv = new Document();
    AdvanceShipmentNotice asn = initASN();
    AsnXcblHelper.prepareASNDocumentBeforeCreated(document, asn, desadv, true);

    assertEquals("Gnx", desadv.getOwners());
    assertEquals(DESADV.name(), desadv.getType());
    assertEquals(PENDING, desadv.getStatusAsEnumValue());

    ASNStatusType asnStatusType = new ASNStatusType();
    asnStatusType.setASNStatusCoded(ASNStatusCodeType.COMPLETE_ORDER);
    asn.getASNHeader()
        .setASNStatus(asnStatusType);
    AsnXcblHelper.prepareASNDocumentBeforeCreated(document, asn, desadv, false);
    assertEquals(SENT, desadv.getStatusAsEnumValue());

    asnStatusType.setASNStatusCoded(ASNStatusCodeType.PARTIAL_ORDER);
    AsnXcblHelper.prepareASNDocumentBeforeCreated(document, asn, desadv, false);
    assertEquals(SENT_PARTIALLY, desadv.getStatusAsEnumValue());
  }

  @Test
  public void testCreateASNPartyWithOrdRsp() {
    ASNPartyType party = AsnXcblHelper.createAdvanceShipmentNoticeParty(order);
    OrderResponseType orderResponseType = new OrderResponseType();
    OrderResponseHeaderType orderResponseHeaderType = new OrderResponseHeaderType();
    orderResponseHeaderType.setBuyerParty(party.getBuyerParty());
    orderResponseHeaderType.setSellerParty(party.getSellerParty());
    orderResponseType.setOrderResponseHeader(orderResponseHeaderType);
    ASNPartyType partyWithOrdRsp = AsnXcblHelper.createASNPartyWithOrdRsp(orderResponseType);
    assertEquals(party.getBuyerParty(), partyWithOrdRsp.getBuyerParty());
    assertEquals(party.getSellerParty(), partyWithOrdRsp.getSellerParty());
    assertNotNull(partyWithOrdRsp.getShipToParty());
    assertNotNull(partyWithOrdRsp.getShipToParty()
        .getNameAddress());
    assertNotNull(partyWithOrdRsp.getShipToParty()
        .getNameAddress()
        .getCountry());
  }

  @Test
  public void testInitASNWithOrderDetailsWithNoOrder() {
    assertThrows(IllegalArgumentException.class, () -> {
      AdvanceShipmentNotice asn = AsnXcblHelper.initASNWithOrderDetails(null, "Gnx");
    });
  }

  @Test
  public void testInitASNWithOrderDetails() {
    AdvanceShipmentNotice asn = AsnXcblHelper.initASNWithOrderDetails(Arrays.asList(order), "Gnx");
    assertNotNull(asn);
    assertNotNull(asn.getASNHeader());
    assertNotNull(asn.getASNDetail());
    assertNotNull(asn.getASNSummary());
    assertEquals("Gnx", asn.getOwners());
    assertEquals(order.getTo(), asn.getTo());
    assertEquals(order.getFrom(), asn.getFrom());
  }

  @Test
  public void testInitASNWithOrderResponse() {
    OrderResponse orderResponse = new OrderResponse();
    ASNPartyType party = AsnXcblHelper.createAdvanceShipmentNoticeParty(order);
    ListOfOrderResponseItemDetailType list = new ListOfOrderResponseItemDetailType();
    OrderResponseDetailType orderResponseDetailType = new OrderResponseDetailType();
    orderResponseDetailType.setListOfOrderResponseItemDetail(list);
    OrderResponseHeaderType orderResponseHeaderType = new OrderResponseHeaderType();
    orderResponseHeaderType.setBuyerParty(party.getBuyerParty());
    orderResponseHeaderType.setSellerParty(party.getSellerParty());
    orderResponse.setOrderResponseHeader(orderResponseHeaderType);
    orderResponse.setOrderResponseDetail(orderResponseDetailType);
    Date date = new Date(1000000000000L);

    AdvanceShipmentNotice desadv = AsnXcblHelper.initASNWithOrderResponse(null, date);
    AdvanceShipmentNotice asn = AsnXcblHelper.initASNWithOrderResponse(orderResponse, date);

    assertNotNull(desadv);
    assertEquals(null, desadv.getFrom());
    assertEquals(null, desadv.getTo());
    assertNotNull(asn);
    assertNotNull(asn.getASNHeader());
    assertNotNull(asn.getASNDetail());
    assertNotNull(asn.getASNSummary());
    assertEquals(orderResponse.getFrom(), asn.getFrom());
    assertEquals(orderResponse.getTo(), asn.getTo());
  }

  private AdvanceShipmentNotice initASN() {
    Date date = new Date(1000000000000L);
    ASNPartyType party = AsnXcblHelper.createAdvanceShipmentNoticeParty(order);
    AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    ASNHeaderType asnHeaderType = new ASNHeaderType();
    ASNTypeType asnTypeType = new ASNTypeType();
    asnTypeType.setASNTypeCoded(ASNTypeCodeType.OTHER);
    asnHeaderType.setASNNumber("1234");
    asnHeaderType.setASNIssueDate(date);
    asnHeaderType.setASNType(asnTypeType);
    asnHeaderType.setASNParty(party);
    asn.setASNHeader(asnHeaderType);
    return asn;
  }

  /**
   * If order delivery date is missing, the ASN delivery date will be tomorrow If order delivery date <= today, the ASN delivery date will
   * be tomorrow If order delivery date > today, the ASN delivery date will be the same as order delivery date
   */
  @Test
  public void testComputeASNDeliveryDate() {
    // order delivery date null
    Date asnDeliveryDate = AsnXcblHelper.computeASNDeliveryDate(null);
    assertEquals(DateUtils.truncate(nextDay(new Date(), 1), Calendar.DAY_OF_MONTH),
        DateUtils.truncate(asnDeliveryDate, Calendar.DAY_OF_MONTH));

    // order delivery date in the past
    Calendar orderDeliveryDate = Calendar.getInstance();
    orderDeliveryDate.setTime(new Date());
    orderDeliveryDate.add(Calendar.DAY_OF_MONTH, -3);
    asnDeliveryDate = AsnXcblHelper.computeASNDeliveryDate(orderDeliveryDate.getTime());
    assertEquals(DateUtils.truncate(nextDay(new Date(), 1), Calendar.DAY_OF_MONTH),
        DateUtils.truncate(asnDeliveryDate, Calendar.DAY_OF_MONTH));

    // order delivery date today (now)
    orderDeliveryDate = Calendar.getInstance();
    orderDeliveryDate.setTime(new Date());
    asnDeliveryDate = AsnXcblHelper.computeASNDeliveryDate(orderDeliveryDate.getTime());
    assertEquals(DateUtils.truncate(nextDay(new Date(), 1), Calendar.DAY_OF_MONTH),
        DateUtils.truncate(asnDeliveryDate, Calendar.DAY_OF_MONTH));

    // order delivery date today (3 hours later)
    orderDeliveryDate = Calendar.getInstance();
    orderDeliveryDate.setTime(new Date());
    orderDeliveryDate.add(Calendar.HOUR_OF_DAY, 3);
    asnDeliveryDate = AsnXcblHelper.computeASNDeliveryDate(orderDeliveryDate.getTime());
    assertEquals(DateUtils.truncate(nextDay(new Date(), 1), Calendar.DAY_OF_MONTH),
        DateUtils.truncate(asnDeliveryDate, Calendar.DAY_OF_MONTH));

    // order delivery date in the future
    orderDeliveryDate = Calendar.getInstance();
    orderDeliveryDate.setTime(new Date());
    orderDeliveryDate.add(Calendar.DAY_OF_MONTH, 3);
    asnDeliveryDate = AsnXcblHelper.computeASNDeliveryDate(orderDeliveryDate.getTime());
    assertEquals(DateUtils.truncate(orderDeliveryDate.getTime(), Calendar.DAY_OF_MONTH),
        DateUtils.truncate(asnDeliveryDate, Calendar.DAY_OF_MONTH));
  }
}