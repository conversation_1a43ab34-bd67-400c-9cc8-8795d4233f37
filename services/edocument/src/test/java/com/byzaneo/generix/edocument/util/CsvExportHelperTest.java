package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.CsvExportHelper.exportAsCSV;
import static com.byzaneo.generix.edocument.util.CsvExportHelper.formatDateFieldForCsv;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.supercsv.prefs.CsvPreference.EXCEL_NORTH_EUROPE_PREFERENCE;

import java.io.*;
import java.nio.file.Files;
import java.text.*;
import java.util.*;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.supercsv.cellprocessor.ift.CellProcessor;
import org.supercsv.io.*;

import com.byzaneo.commons.bean.*;
import com.byzaneo.generix.edocument.bean.RecapListItem;
import com.byzaneo.generix.util.DocumentCompoundTypeHelper;
import com.byzaneo.xtrade.api.*;

public class CsvExportHelperTest {

  private List<PropertyDescriptor> pDescList;

  private CellProcessor[] processors;

  private String[] headers;

  File csvFile = new File(FileUtils.getTempDirectory(), "recap-list.csv");

  private int size = 3;

  private Logger logger = Logger.getLogger(CsvExportHelperTest.class);

  @BeforeEach
  public void before() {

  }

  @Test
  public void testFormatDateFieldWithHour() {
    List<String> fieldNames = Arrays.asList("creationDate", "documentPreparationDate", "documentDate");
    pDescList = new ArrayList<>();
    fieldNames.forEach(fieldName -> pDescList.add(new PropertyDescriptor(fieldName)));

    processors = new CellProcessor[size];
    headers = new String[size];

    for (int i = 0; i < size; i++) {
      headers[i] = fieldNames.get(i);
    }

    for (int i = 0; i < size; i++) {
      formatDateFieldForCsv(processors, i, pDescList.get(i));
    }
    writeCsv();
    readCsvAndCheckDateFormat();

    try {
      Files.delete(csvFile.toPath());
    }
    catch (IOException e) {
      logger.warn("Failed to delete csv file ", e);
    }
  }

  @Test
  public void testExportAsCsv() throws IOException {
    BeanDescriptor bd = new BeanDescriptor("beanDescriptor");
    List<Object> items = new ArrayList<>();
    List<String> fieldNames = Arrays.asList("creationDate", "qty", "buyerCountry", "docStatus", "docStage", "archiveStatus", "dematPartner",
        "messageType", "gnxTypeConverter", "documentTypeConverter");
    pDescList = new ArrayList<>();
    fieldNames.forEach(fieldName -> pDescList.add(new PropertyDescriptor(fieldName)));
    pDescList.forEach(propertyDescriptor -> bd.addProperty(propertyDescriptor));
    assignType();
    FileOutputStream fos = new FileOutputStream(csvFile);

    exportAsCSV(items, bd, fos, Locale.ENGLISH, null);
    assertTrue(csvFile.exists());

    try {
      Files.delete(csvFile.toPath());
    }
    catch (IOException e) {
      logger.warn("Failed to delete csv file ", e);
    }
  }

  private void assignType() {
    pDescList.forEach(pd -> {
      switch (pd.getName()) {
      case "creationDate":
        pd.setType(Date.class);
        break;
      case "qty":
        pd.setType(Number.class);
        break;
      case "docStatus":
        pd.setType(DocumentStatusEntityInterface.class);
        break;
      case "docStage":
        pd.setType(DocumentStage.class);
        break;
      case "archiveStatus":
        pd.setType(ArchiveStatus.class);
        break;
      case "dematPartner":
        pd.setType(RecapListItem.DematPartnerQuality.class);
        break;
      default:
        pd.setType(String.class);
        break;
      }
    });
  }

  private void writeCsv() {
    try (final ICsvBeanWriter writer = new CsvBeanWriter(new FileWriter(csvFile),
        EXCEL_NORTH_EUROPE_PREFERENCE);) {
      RecapListItem item = new RecapListItem();
      item.setCreationDate(new Date());
      item.setDocumentPreparationDate(new Date());
      item.setDocumentDate(new Date());
      writer.write(item, headers, processors);
    }
    catch (IOException e) {
      e.printStackTrace();
    }
  }

  private void readCsvAndCheckDateFormat() {
    SimpleDateFormat formatWithTime = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    SimpleDateFormat formatWithoutTime = new SimpleDateFormat("dd/MM/yyyy");
    Date creationDate = null;
    Date documentPreparationDate = null;
    Date documentDate = null;
    try (BufferedReader csvReader = new BufferedReader(new FileReader(csvFile));) {
      String csvRow;
      String[] csvDateValues = null;
      while ((csvRow = csvReader.readLine()) != null) {
        csvDateValues = csvRow.split(";");
      }
      checkDateFormat(formatWithTime, csvDateValues[0], creationDate);
      checkDateFormat(formatWithTime, csvDateValues[1], documentPreparationDate);
      checkDateFormat(formatWithoutTime, csvDateValues[2], documentDate);

    }
    catch (IOException | ParseException e) {
      e.printStackTrace();
    }
  }

  private void checkDateFormat(SimpleDateFormat formatWithTime, String dateValue, Date date) throws ParseException {
    date = formatWithTime.parse(dateValue);
    assertEquals(dateValue, formatWithTime.format(date));
  }

  @Test
  public void shouldExtractNormFromJSON() {
    String json = "{\"kind\":\"INVOIC\",\"norm\":\"D96A\"}";
    String result = StringUtils.isEmpty(json) ? "" : DocumentCompoundTypeHelper.fromJson(json)
        .getNorm();
    Assertions.assertTrue("D96A".equals(result));
  }

  @AfterEach
  public void after() {

  }

}
