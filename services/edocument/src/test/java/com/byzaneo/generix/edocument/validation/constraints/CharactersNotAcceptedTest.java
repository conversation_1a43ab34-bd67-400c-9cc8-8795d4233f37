package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class CharactersNotAcceptedTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/CharactersNotAcceptedTest.xml"), false);

  /**
   * Test of isValid method, of class CharactersNotAccepted.
   */
  @Test
  public void testValid() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productValid/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testInvalid1() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid1/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid2() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid2/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid3() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid3/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid4() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid4/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid5() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid5/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid6() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid6/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid7() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid7/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid8() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid8/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid9() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid9/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid11() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid11/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid12() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid12/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid13() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid13/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid14() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid14/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid15() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid15/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid16() {
    CharactersNotAccepted instance = new CharactersNotAccepted();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid16/test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }
}
