package com.byzaneo.generix.edocument.validation.validator;

import com.byzaneo.generix.edocument.validation.bean.Validator;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.xml.bind.JAXBException;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Set;

import static com.byzaneo.generix.edocument.validation.ConstraintsParser.getInstance;
import static com.byzaneo.generix.edocument.validation.bean.ConstraintType.FR;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class XCBLValidatorTest {
  private final static String XCBL_PATH = "./src/test/resources/xcbl/InvoiceExample.xml";

  private final static String XCBL_PATH_TVA_OK = "./src/test/resources/xcbl/2taux_ok.xml";

  private final static String XCBL_PATH_TVA_NOK1 = "./src/test/resources/xcbl/2taux_nok_1.edi.xml";

  private final static String XCBL_PATH_TVA_NOK2 = "./src/test/resources/xcbl/2taux_nok_2.edi.xml";

  private final static String XCBL_PATH_TEST3 = "./src/test/resources/xcbl/test3_1.edi.xml";

  private final static String XCBL_PATH_TEST4 = "./src/test/resources/xcbl/test4_1.edi.xml";

  private final static String XCBL_PATH_KO1 = "./src/test/resources/xcbl/00000051_KO1.txt.xml";

  private final static String XCBL_PATH_KO2 = "./src/test/resources/xcbl/00000051_KO2.txt.xml";

  private final static String XCBL_PATH_EBE1 = "./src/test/resources/xcbl/00000061_EBE1.txt.xml";

  private final static String XCBL_PATH_EBE2 = "./src/test/resources/xcbl/00000061_EBE2.txt.xml";

  private final static String XCBL_PATH_ILLEGALVALUE = "./src/test/resources/xcbl/xcbl_illegalvalue.xml";

  private final static String XCBL_PATH_BIGFILE = "./src/test/resources/xcbl/bigfile.xml";

  private final static String XCBL_PATH_VAT_EMPTY_TAX_PERCENT = "./src/test/resources/xcbl/00000076_1.xml";

  private static final Validator delegate = getInstance().loadValidator(FR);

  private static final Validator delegateTva = new Validator(FR);

  private static final Validator delegateHeader = new Validator(FR);

  private static final Validator delegateDetail = new Validator(FR);

  private static final Validator delegateSummary = new Validator(FR);

  @BeforeAll
  public static void init() throws JAXBException, IOException {
    try (InputStream stream = XCBLValidatorTest.class.getResourceAsStream("/constraints/invoice-vat-validations.xml")) {
      delegateTva.unmarshall(stream);
    }
    try (InputStream stream = XCBLValidatorTest.class.getResourceAsStream("/constraints/invoice-header-validations.xml")) {
      delegateHeader.unmarshall(stream);
    }
    try (InputStream stream = XCBLValidatorTest.class.getResourceAsStream("/constraints/invoice-detail-validations.xml")) {
      delegateDetail.unmarshall(stream);
    }
    try (InputStream stream = XCBLValidatorTest.class.getResourceAsStream("/constraints/invoice-summary-validations.xml")) {
      delegateSummary.unmarshall(stream);
    }
  }

  @Test
  public void testValidateDocumentFile() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH), FR);
    assertTrue(CollectionUtils.isNotEmpty(errors));
  }

  @Test
  public void testValidateTvaOk() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegateTva);
    Set<String> errors = validator.validate(new File(XCBL_PATH_TVA_OK), FR);
    assertTrue(CollectionUtils.isEmpty(errors));
  }

  @Test
  public void testValidateTvaNok1() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegateTva);
    Set<String> errors = validator.validate(new File(XCBL_PATH_TVA_NOK1), FR);
    assertEquals(1, errors.size());
    assertTrue(errors.iterator()
        .next()
        .endsWith("invoice_tax_line_tax_unmatched"));
  }

  @Test
  public void testValidateTvaNok2() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegateTva);
    Set<String> errors = validator.validate(new File(XCBL_PATH_TVA_NOK2), FR);
    assertEquals(1, errors.size());
    assertTrue(errors.iterator()
        .next()
        .equals("invoice_tax_taxsummary_tax_unmatched"));
  }

  @Test
  public void testValidateTest3() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH_TEST3), FR);
    assertEquals(1, errors.size());
    assertTrue(errors.iterator()
        .next()
        .equals("invoice_tax_taxsummary_tax_notuniquevalue"));
  }

  @Test
  public void testValidateTest4() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH_TEST4), FR);
    assertEquals(1, errors.size());
    assertTrue(errors.iterator()
        .next()
        .equals("invoice_tax_taxsummary_tax_notuniquevalue"));
  }

  @Test
  public void testValidateKo1() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH_KO1), FR);
    assertEquals(3, errors.size());
    for (String error : errors) {
      assertTrue(error.contains("invoiceheader_sellerparty_address"));
      assertTrue(error.endsWith("_empty"));
    }
  }

  @Test
  public void testValidateKo2() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH_KO2), FR);
    assertEquals(3, errors.size());
    for (String error : errors) {
      assertTrue(error.contains("invoiceheader_sellerparty_address"));
      assertTrue(error.endsWith("_empty"));
    }
  }

  @Test
  public void testValidateEbe1() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH_EBE1), FR);
    assertEquals(1, errors.size());
    assertTrue(errors.iterator()
        .next()
        .equals("invoiceheader_paymentterms_missing"));
  }

  @Test
  public void testValidateEbe2() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH_EBE2), FR);
    assertEquals(1, errors.size());
    assertTrue(errors.iterator()
        .next()
        .equals("invoiceheader_paymentterms_missing"));
  }

  @Test
  public void testValidateIllegalValue() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH_ILLEGALVALUE), FR);
    assertEquals(1, errors.size());
    assertTrue(errors.iterator()
        .next()
        .equals("invoicedetail_quantity_value_illegalvalue"));
  }

  @Test
  public void testEmptyVatTaxPercent() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File(XCBL_PATH_VAT_EMPTY_TAX_PERCENT), FR);
    assertEquals(1, errors.size());
    assertTrue(errors.iterator()
        .next()
        .endsWith("vat_rate_empty"));
  }

  @Test
  public void testTaxPercent() throws Exception {
    XCBLValidator validator = new XCBLValidator(delegate);
    Set<String> errors = validator.validate(new File("./src/test/resources/xcbl/00000006_1.xml"), FR);
    assertEquals(0, errors.size());
  }

  @Test
  @Disabled // Ignored because of the duration
  public void testValidateBigFile() throws Exception {
    // header validation duration
    long start = System.currentTimeMillis();
    XCBLValidator validator = new XCBLValidator(delegateHeader);
    Set<String> errors = validator.validate(new File(XCBL_PATH_BIGFILE), FR);
    long end = System.currentTimeMillis();
    System.out.println("Header : Duration=" + (end - start) / 1000 + "s");
    assertTrue(CollectionUtils.isEmpty(errors));

    // summary validation duration
    start = System.currentTimeMillis();
    validator = new XCBLValidator(delegateSummary);
    errors = validator.validate(new File(XCBL_PATH_BIGFILE), FR);
    end = System.currentTimeMillis();
    System.out.println("Summary : Duration=" + (end - start) / 1000 + "s");
    assertTrue(CollectionUtils.isEmpty(errors));

    // vat validation duration
    start = System.currentTimeMillis();
    validator = new XCBLValidator(delegateTva);
    errors = validator.validate(new File(XCBL_PATH_BIGFILE), FR);
    end = System.currentTimeMillis();
    System.out.println("Vat : Duration=" + (end - start) / 1000 + "s");
    assertTrue(CollectionUtils.isEmpty(errors));

    // detail validation duration
    start = System.currentTimeMillis();
    validator = new XCBLValidator(delegateDetail);
    errors = validator.validate(new File(XCBL_PATH_BIGFILE), FR);
    end = System.currentTimeMillis();
    System.out.println("Detail : Duration=" + (end - start) / 1000 + "s");
    assertTrue(CollectionUtils.isEmpty(errors));

    //
    //
    // //global validation duration
    // start = System.currentTimeMillis();
    // validator = new XCBLValidator(delegate);
    // errors = validator.validate(new File(XCBL_PATH_BIGFILE));
    // end = System.currentTimeMillis();
    // System.out.println("Global : Duration="+(end-start)/1000+"s");
    // assertTrue(CollectionUtils.isEmpty(errors));
  }
}
