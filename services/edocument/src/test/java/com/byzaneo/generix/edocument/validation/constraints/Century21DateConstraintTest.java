package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class Century21DateConstraintTest {

    private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/Century21DateExample.xml"), false);

    @Test
    public void validTest() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/validDate");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertTrue(result);
    }

    @Test
    public void invalidTest1() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate1");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }

    @Test
    public void invalidTest2() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate2");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }

    @Test
    public void invalidTest3() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate3");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }

    @Test
    public void invalidTest4() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate4");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }

    @Test
    public void invalidTest5() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate5");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }

    @Test
    public void invalidTest6() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate6");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }

    @Test
    public void invalidTest7() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate7");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }

    @Test
    public void invalidTest8() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate8");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }

    @Test
    public void invalidTest9() {
        Century21DateConstraint instance = new Century21DateConstraint();
        instance.setXQuery(new XQuery());
        List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//invoices/invoice/invalidDate9");
        instance.setNodes(nodes);
        boolean result = instance.isValid();
        assertFalse(result);
    }
}
