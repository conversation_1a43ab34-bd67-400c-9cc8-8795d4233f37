package com.byzaneo.generix.edocument.util;

import java.util.Locale;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.supercsv.exception.SuperCsvCellProcessorException;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryCodeType;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class CountryProcessorTest {
  private CountryProcessor processorEN;
  private CountryProcessor processorFR;

  @BeforeEach
  public void setUp() throws Exception {
    this.processorEN = new CountryProcessor(Locale.ENGLISH);
    this.processorFR = new CountryProcessor(Locale.FRENCH);
  }

  @Test
  public void testExecuteCountryProcessorWithNullValue() {
    assertThrows(SuperCsvCellProcessorException.class, () ->
      processorEN.execute(null, null));
  }

  @Test
  public void testExecuteCountryProcessorWithCountryCodeTypeEN() {
    assertEquals("UNITED STATES", processorEN.execute(CountryCodeType.US, null));
    assertEquals("NIGERIA", processorEN.execute(CountryCodeType.NG, null));
    assertEquals("BRAZIL", processorEN.execute(CountryCodeType.BR, null));
    assertEquals("FRANCE", processorEN.execute(CountryCodeType.FR, null));
    assertEquals("GERMANY", processorEN.execute(CountryCodeType.DE, null));
  }

  @Test
  public void testExecuteCountryProcessorWithStringEN() {
    assertEquals("", processorEN.execute("", null));
    assertEquals("TUNISIA", processorEN.execute("_Tunisia", null));
    assertEquals("FRANCE", processorEN.execute("_France", null));
    assertEquals("CHINA", processorEN.execute("_China", null));
    assertEquals("CANADA", processorEN.execute("_Canada", null));
  }

  @Test
  public void testExecuteContryProcessorWithStringNoUnderscoreEN() {
    assertEquals("FRANCE", processorEN.execute("France", null));
    assertEquals("CHINA", processorEN.execute("China", null));
    assertEquals("GERMANY", processorEN.execute("Germany", null));
    assertEquals("BRAZIL", processorEN.execute("Brazil", null));
  }

  @Test
  public void testExecuteCountryProcessorWithCountryCodeTypeFR() {
    assertEquals("ÉTATS-UNIS", processorFR.execute(CountryCodeType.US, null));
    assertEquals("ISLANDE", processorFR.execute(CountryCodeType.IS, null));
    assertEquals("ITALIE", processorFR.execute(CountryCodeType.IT, null));
    assertEquals("ALLEMAGNE", processorFR.execute(CountryCodeType.DE, null));
  }

  @Test
  public void testExecuteCountryProcessorWithStringFR() {
    assertEquals("BELGIQUE", processorFR.execute("_Belgique", null));
    assertEquals("FRANCE", processorFR.execute("_France", null));
    assertEquals("CHINE", processorFR.execute("_Chine", null));
    assertEquals("CANADA", processorFR.execute("_Canada", null));
  }

  @Test
  public void testExecuteContryProcessorWithStringNoUnderscoreFR() {
    assertEquals("FRANCE", processorFR.execute("France", null));
    assertEquals("JAPON", processorFR.execute("Japon", null));
    assertEquals("HONGRIE", processorFR.execute("Hongrie", null));
  }

}