package com.byzaneo.generix.edocument.validation.constraints;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;

import static org.junit.jupiter.api.Assertions.*;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;

import java.io.File;
import java.util.List;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

/**
 * <AUTHOR>
 */
public class ValueMatchConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/SimpleExample.xml"), false);

  /**
   * Test of isValid method, of class ValueMatchConstraint.
   */
  @Test
  public void testValid() {
    ValueMatchConstraint instance = new ValueMatchConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/number");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("//detail/products/product/number", "//detail/products/product/anotherNumber"));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  /**
   * Test of isValid method, of class ValueMatchConstraint.
   */
  @Test
  public void testInvalid() {
    ValueMatchConstraint instance = new ValueMatchConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("//detail/products/product/number", "//detail/products/product/anotherNumber"));
    boolean result = instance.isValid();
    assertFalse(result);
  }
}
