package com.byzaneo.generix.edocument.repository;

import static com.byzaneo.generix.edocument.bean.DocumentLink.Field.source;
import static com.byzaneo.generix.edocument.bean.DocumentLink.Field.target;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.generix.edocument.bean.DocumentLink;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Dec 11, 2015
 * @since 1.0
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {
    "classpath:/gnx-edoc-test.beans.xml"}, loader = SystemPropertyContextLoader.class)
public class DocumentLinkOperationsITCase {

  @Autowired
  private DocumentLinkOperations operations;

  @Test
  public final void testStore() {
    List<DocumentLink> links = new ArrayList<>(10);
    for (Long i = 2L; i < 11L; i++) {
      links.add(new DocumentLink(
          String.valueOf(i - 1),
          String.valueOf(i)));
    }

    this.operations.saveAll(links);
    // compound index should avoid duplicate couple source/target
    this.operations.saveAll(links);

    List<DocumentLink> result = this.operations.findByQuery(createBuilder()
                .or(equal(source.toString(), "3"), equal(target.toString(), "3"))
                .query(),
            null)
        .getContent();
    assertEquals(2, result.size());
  }

}
