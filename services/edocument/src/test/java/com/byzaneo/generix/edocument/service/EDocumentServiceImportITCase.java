package com.byzaneo.generix.edocument.service;

import static com.byzaneo.generix.edocument.util.AsnXcblHelper.STANDARD_DESPATCH_ADVICE;
import static com.byzaneo.generix.edocument.util.XcblHelper.CROSS_DOCKING_DESPATCH_ADVICE;
import static com.byzaneo.query.builder.Clauses.equal;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.fail;

import com.byzaneo.commons.test.SpringJUnitClassListener;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.generix.edocument.util.XcblHelper;
import com.byzaneo.location.bean.Address;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.Location;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.security.dao.GroupDAO;
import com.byzaneo.xtrade.bean.Document_;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.DateCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PackageMarkCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNPurposeCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNTypeCodeType;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {
    "classpath:/gnx-edoc-test.beans.xml"}, loader = SystemPropertyContextLoader.class)
@Disabled
// TODO Fix me
public class EDocumentServiceImportITCase {

  @Autowired
  @Qualifier(EDocumentService.SERVICE_NAME)
  private static EDocumentService service;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  @Autowired
  protected static GroupDAO groupDAO;

  private static Partner partnerBuyer;

  private static Partner partnerSeller;

  private static Company company;

  private QueryBuilder qb;

  @BeforeAll
  public static void beforeClass() throws IOException {
    company = new Company("Generix group");
    company.setFullname("Generix group");
    company.setCode("codeGroupe");
    Address mainC = new Address("15", "rue", "Beaubourg", null, "VilleCompany", null, null, "75003",
        null, Locale.FRANCE, null, null, null);
    company.setLocation(new Location("mainC", mainC, true));

    partnerBuyer = new Partner("partner Buyer");
    partnerBuyer.setFullname("partner Buyer");
    partnerBuyer.setCode("codeBuyer");
    Address mainB = new Address("1", "rue", "Buyer", null, "VilleBuyer", null, null, "75011", null,
        Locale.FRANCE, null, null, null);
    partnerBuyer.setLocation(new Location("mainB", mainB, true));
    partnerBuyer.setParent(company);

    partnerSeller = new Partner("partner Seller");
    partnerSeller.setFullname("partner Seller");
    partnerSeller.setCode("codeSeller");
    Address mainS = new Address("1", "rue", "Seller", null, "VilleSeller", null, null, "75011",
        null, Locale.FRANCE, null, null, null);
    partnerSeller.setLocation(new Location("mainS", mainS, true));
    partnerSeller.setParent(company);

    HashSet<Group> partners = new HashSet<>();
    partners.add(partnerBuyer);
    partners.add(partnerSeller);
    company.setChildren(partners);

    // save company and partners
    groupDAO.store(company);
    groupDAO.storeAll(company.getChildren());
    // list of files

    List<InputStream> list = new ArrayList<>();
    try (FileInputStream file1 = new FileInputStream(
        new File("src/test/resources/xcbl/asn/asn-test.csv"));
        FileInputStream file2 = new FileInputStream(
            new File("src/test/resources/xcbl/asn/asn-test-2.csv"));
        FileInputStream file3 = new FileInputStream(
            new File("src/test/resources/xcbl/asn/asn-test-3.csv"));
        FileInputStream file4 = new FileInputStream(
            new File("src/test/resources/xcbl/asn/asn-check-nadBYandSE.csv"));
        FileInputStream file5 = new FileInputStream(
            new File("src/test/resources/xcbl/asn/asn-header-missingAAU.csv"));
        FileInputStream file6 = new FileInputStream(
            new File("src/test/resources/xcbl/asn/asn-header-missingSH.csv"));
        FileInputStream file7 = new FileInputStream(
            new File("src/test/resources/xcbl/asn/asn-header-incorrect-line.csv"));
        FileInputStream file8 = new FileInputStream(
            new File("src/test/resources/xcbl/asn/asn-header-incorrect-date.csv"));
        FileInputStream file9 = new FileInputStream(
            new File("src/test/resources/xcbl/asn/asn-unknown-package.csv"));) {
      list.add(file1);
      list.add(file2);
      list.add(file3);
      list.add(file4);
      list.add(file5);
      list.add(file6);
      list.add(file7);
      list.add(file8);
      list.add(file9);
      service.importAdvanceShipmentNotices(company, partnerSeller, list, true, "xcblAsnHeader");
    }
  }

  /**
   * Test asn-test.csv
   */
  @Test
  public void check_asn_test() throws FileNotFoundException {
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerSeller.getCode()),
        equal(Document_.to.getName(), partnerBuyer.getCode()),
        equal("asnHeader.asnType.asnTypeCodedOther", CROSS_DOCKING_DESPATCH_ADVICE),
        equal("asnHeader.asnNumber", "8300000130"));

    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    AdvanceShipmentNotice asn = documents.get(0);

    assertEquals("8300000130", asn.getASNHeader()
        .getASNNumber());
    assertEquals(ASNPurposeCodeType.ORIGINAL, asn.getASNHeader()
        .getASNPurpose()
        .getASNPurposeCoded());
    assertEquals(ASNTypeCodeType.OTHER, asn.getASNHeader()
        .getASNType()
        .getASNTypeCoded());
    assertEquals(XcblHelper.CROSS_DOCKING_DESPATCH_ADVICE, asn.getASNHeader()
        .getASNType()
        .getASNTypeCodedOther());
    assertEquals("900140", asn.getASNHeader()
        .getShippingReferences()
        .getShipmentIdentifier()
        .getRefNum());
    assertEquals(partnerSeller.getCode(), asn.getASNHeader()
        .getASNParty()
        .getSellerParty()
        .getPartyID()
        .getIdent());
    assertEquals(partnerBuyer.getCode(), asn.getASNHeader()
        .getASNParty()
        .getShipToParty()
        .getPartyID()
        .getIdent());
    assertEquals("3019911700000", asn.getASNHeader()
        .getASNParty()
        .getShipFromParty()
        .getPartyID()
        .getIdent());
    assertEquals(partnerSeller.getCode(), asn.getASNHeader()
        .getASNParty()
        .getWarehouseParty()
        .getPartyID()
        .getIdent());
    assertEquals("3020400191900", asn.getASNHeader()
        .getASNParty()
        .getListOfPartyCoded()
        .getPartyCoded()
        .get(0)
        .getPartyID()
        .getIdent());
    assertEquals("UltimateCustomer", asn.getASNHeader()
        .getASNParty()
        .getListOfPartyCoded()
        .getPartyCoded()
        .get(0)
        .getPartyRoleCoded());
    assertEquals(3, asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .size());
    for (int i = 1; i <= asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .size(); i++) {
      assertEquals(i, asn.getASNDetail()
          .getListOfASNItemDetail()
          .getASNItemDetail()
          .get(i - 1)
          .getASNBaseItemDetail()
          .getLineItemNum()
          .getBuyerLineItemNum());
      // assertNull(asn.getASNDetail().getListOfASNItemDetail().getASNItemDetail().get(i-1).getASNBaseItemDetail().getASNItemDates());
    }
    assertEquals("661187", asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getBuyerPartNumber()
        .getPartID());
    assertEquals("3700253730170", asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getStandardPartNumber()
        .getProductIdentifier());
    assertEquals(new BigDecimal("28.000"), asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getASNQuantities()
        .getShippedQuantity()
        .getQuantityValue()
        .getValue());
    assertEquals(1, asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .size());
    assertEquals(4, asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getNumberOfPackages());
    assertEquals("Other", asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getPackageType()
        .getPackageTypeCoded());
    assertEquals("X1", asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getPackageType()
        .getPackageTypeCodedOther());
    assertEquals(3, asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getListOfPackage()
        .getPackage()
        .size());
    assertEquals(4, asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getListOfPackage()
        .getPackage()
        .get(2)
        .getPackageID());
    assertEquals(2, asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getListOfPackage()
        .getPackage()
        .get(2)
        .getListOfItemReference()
        .getPackageItemReference()
        .size());
    assertEquals(PackageMarkCodeType.SSCC_18, asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getListOfPackage()
        .getPackage()
        .get(2)
        .getListOfPackageMark()
        .getPackageMark()
        .get(0)
        .getPackageMarkCoded());
    assertEquals("134821000000000113", asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getListOfPackage()
        .getPackage()
        .get(2)
        .getPackageDetail()
        .get(0)
        .getListOfPackage()
        .getPackage()
        .get(0)
        .getListOfPackageMark()
        .getPackageMark()
        .get(0)
        .getPackageMarkValue());

  }

  /**
   * Test asn-test2.csv
   */
  @Test
  public void check_asn_test_2() {
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerSeller.getCode()),
        equal(Document_.to.getName(), partnerBuyer.getCode()),
        equal("asnHeader.asnType.asnTypeCodedOther", STANDARD_DESPATCH_ADVICE),
        equal("asnHeader.asnNumber", "8300000135"));

    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    assertEquals(1, documents.size());

    AdvanceShipmentNotice asn = documents.get(0);
    assertEquals("8300000135", asn.getASNHeader()
        .getASNNumber());
    assertEquals(ASNTypeCodeType.OTHER, asn.getASNHeader()
        .getASNType()
        .getASNTypeCoded());
    assertEquals(STANDARD_DESPATCH_ADVICE, asn.getASNHeader()
        .getASNType()
        .getASNTypeCodedOther());
    assertEquals("900141", asn.getASNHeader()
        .getShippingReferences()
        .getShipmentIdentifier()
        .getRefNum());
    assertEquals(partnerSeller.getCode(), asn.getASNHeader()
        .getASNParty()
        .getSellerParty()
        .getPartyID()
        .getIdent());
    assertEquals(partnerBuyer.getCode(), asn.getASNHeader()
        .getASNParty()
        .getBuyerParty()
        .getPartyID()
        .getIdent());
    assertEquals(partnerBuyer.getCode(), asn.getASNHeader()
        .getASNParty()
        .getShipToParty()
        .getPartyID()
        .getIdent());
    assertEquals("7019911700001", asn.getASNHeader()
        .getASNParty()
        .getShipFromParty()
        .getPartyID()
        .getIdent());
    assertEquals(partnerSeller.getCode(), asn.getASNHeader()
        .getASNParty()
        .getWarehouseParty()
        .getPartyID()
        .getIdent());
    assertNull(asn.getASNHeader()
        .getASNParty()
        .getListOfPartyCoded());
    assertEquals(4, asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .size());
    assertEquals("771187", asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getSellerPartNumber()
        .getPartID());
    assertEquals("661187", asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getBuyerPartNumber()
        .getPartID());
    for (ASNItemDetailType detail : asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()) {

      String productNumber = detail.getASNBaseItemDetail()
          .getItemIdentifiers()
          .getPartNumbers()
          .getStandardPartNumber()
          .getProductIdentifier();
      String orderId = detail.getASNBaseItemDetail()
          .getASNLineItemReferences()
          .getPurchaseOrderReference()
          .getBuyerOrderNumber();

      switch (productNumber) {
        case "9700253730170":
          assertEquals(new BigDecimal("51.000"), detail.getASNBaseItemDetail()
              .getASNQuantities()
              .getShippedQuantity()
              .getQuantityValue()
              .getValue());
          break;
        case "9700253730279":
          if ("65000103".equals(orderId)) {
            assertEquals(new BigDecimal("5.000"), detail.getASNBaseItemDetail()
                .getASNQuantities()
                .getShippedQuantity()
                .getQuantityValue()
                .getValue());
          } else if ("65000104".equals(orderId)) {
            assertEquals(new BigDecimal("29.000"), detail.getASNBaseItemDetail()
                .getASNQuantities()
                .getShippedQuantity()
                .getQuantityValue()
                .getValue());
          }
          break;
        case "9700253730286":
          assertEquals(new BigDecimal("15.000"), detail.getASNBaseItemDetail()
              .getASNQuantities()
              .getShippedQuantity()
              .getQuantityValue()
              .getValue());
          break;
        default:
          fail("Missing case ?");
      }
    }
  }

  /**
   * Test asn-test3.csv
   *
   * @throws ParseException
   */
  @Test
  public void check_asn_test_3() throws ParseException {
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerSeller.getCode()),
        equal(Document_.to.getName(), partnerBuyer.getCode()),
        equal("asnHeader.asnType.asnTypeCodedOther", CROSS_DOCKING_DESPATCH_ADVICE),
        equal("asnHeader.asnNumber", "8300000140"));

    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    assertEquals(1, documents.size());
    AdvanceShipmentNotice asn = documents.get(0);

    assertEquals("8300000140", asn.getASNHeader()
        .getASNNumber());
    assertEquals(ASNTypeCodeType.OTHER, asn.getASNHeader()
        .getASNType()
        .getASNTypeCoded());
    assertEquals(CROSS_DOCKING_DESPATCH_ADVICE, asn.getASNHeader()
        .getASNType()
        .getASNTypeCodedOther());
    assertEquals("Multiple", asn.getASNHeader()
        .getASNOrderNumber()
        .get(0)
        .getBuyerOrderNumber());
    assertEquals("900730", asn.getASNHeader()
        .getShippingReferences()
        .getShipmentIdentifier()
        .getRefNum());
    assertEquals(partnerSeller.getCode(), asn.getASNHeader()
        .getASNParty()
        .getSellerParty()
        .getPartyID()
        .getIdent());
    assertEquals(partnerBuyer.getCode(), asn.getASNHeader()
        .getASNParty()
        .getBuyerParty()
        .getPartyID()
        .getIdent());
    assertEquals(partnerBuyer.getCode(), asn.getASNHeader()
        .getASNParty()
        .getShipToParty()
        .getPartyID()
        .getIdent());
    assertEquals("6559911700000", asn.getASNHeader()
        .getASNParty()
        .getShipFromParty()
        .getPartyID()
        .getIdent());
    assertEquals(partnerSeller.getCode(), asn.getASNHeader()
        .getASNParty()
        .getWarehouseParty()
        .getPartyID()
        .getIdent());
    assertEquals("8310400191900", asn.getASNHeader()
        .getASNParty()
        .getListOfPartyCoded()
        .getPartyCoded()
        .get(0)
        .getPartyID()
        .getIdent());
    assertEquals(5, asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .size());
    List<DateCodedType> dates = asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail()
        .getASNItemDates()
        .getListOfDateCoded()
        .getDateCoded();
    assertEquals(2, dates.size());
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    for (DateCodedType date : dates) {
      switch (date.getDateQualifier()
          .getDateQualifierCoded()) {
        case "Expiration":
          assertEquals(sdf.parse("2010-02-05"), date.getDate());
          break;
        case "ShelfLifeExpiration":
          assertEquals(sdf.parse("2010-01-05"), date.getDate());
          break;

      }
    }

    for (ASNItemDetailType detail : asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()) {

      String productNumber = detail.getASNBaseItemDetail()
          .getItemIdentifiers()
          .getPartNumbers()
          .getStandardPartNumber()
          .getProductIdentifier();
      String orderId = detail.getASNBaseItemDetail()
          .getASNLineItemReferences()
          .getPurchaseOrderReference()
          .getBuyerOrderNumber();

      switch (productNumber) {
        case "3700253730170":
          assertEquals(new BigDecimal("42.000"), detail.getASNBaseItemDetail()
              .getASNQuantities()
              .getShippedQuantity()
              .getQuantityValue()
              .getValue());
          break;
        case "3700253730279":
          if ("65000103".equals(orderId)) {
            assertEquals(new BigDecimal("14.000"), detail.getASNBaseItemDetail()
                .getASNQuantities()
                .getShippedQuantity()
                .getQuantityValue()
                .getValue());
          } else if ("65000104".equals(orderId)) {
            assertEquals(new BigDecimal("28.000"), detail.getASNBaseItemDetail()
                .getASNQuantities()
                .getShippedQuantity()
                .getQuantityValue()
                .getValue());
          }
          break;
        case "3700253730286":
          if ("65000103".equals(orderId)) {
            assertEquals(new BigDecimal("14.000"), detail.getASNBaseItemDetail()
                .getASNQuantities()
                .getShippedQuantity()
                .getQuantityValue()
                .getValue());
          } else if ("65000104".equals(orderId)) {
            assertEquals(new BigDecimal("14.000"), detail.getASNBaseItemDetail()
                .getASNQuantities()
                .getShippedQuantity()
                .getQuantityValue()
                .getValue());
          }
          break;
        default:
          fail("Missing case ?");
      }
    }
  }

  /**
   * Test asn-check-nadBYandSE.csv
   */
  @Test
  public void check_asn_check_nadBYandSE() {
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerSeller.getCode()),
        equal(Document_.to.getName(), partnerBuyer.getCode()),
        equal("asnHeader.asnType.asnTypeCodedOther", CROSS_DOCKING_DESPATCH_ADVICE),
        equal("asnHeader.asnNumber", "8300000188"));
    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    assertEquals(1, documents.size());
    AdvanceShipmentNotice asn = documents.get(0);

    assertEquals(CurrencyCodeType.EUR, asn.getASNHeader()
        .getASNCurrency()
        .getCurrencyCoded());
    assertEquals(LanguageCodeType.FR, asn.getASNHeader()
        .getASNLanguage()
        .getLanguageCoded());

    assertEquals(partnerBuyer.getCode(), asn.getASNHeader()
        .getASNParty()
        .getBuyerParty()
        .getPartyID()
        .getIdent());
    assertEquals("buyername", asn.getASNHeader()
        .getASNParty()
        .getBuyerParty()
        .getNameAddress()
        .getName1());
    assertEquals("buyerstreet", asn.getASNHeader()
        .getASNParty()
        .getBuyerParty()
        .getNameAddress()
        .getStreet());
    assertEquals("buyercp", asn.getASNHeader()
        .getASNParty()
        .getBuyerParty()
        .getNameAddress()
        .getPostalCode());
    assertEquals("buyercity", asn.getASNHeader()
        .getASNParty()
        .getBuyerParty()
        .getNameAddress()
        .getCity());
    assertEquals(CountryCodeType.FR, asn.getASNHeader()
        .getASNParty()
        .getBuyerParty()
        .getNameAddress()
        .getCountry()
        .getCountryCoded());

    assertEquals(partnerSeller.getCode(), asn.getASNHeader()
        .getASNParty()
        .getSellerParty()
        .getPartyID()
        .getIdent());
    assertEquals("shiptocode", asn.getASNHeader()
        .getASNParty()
        .getShipToParty()
        .getPartyID()
        .getIdent());
    assertEquals("shipfromcode", asn.getASNHeader()
        .getASNParty()
        .getShipFromParty()
        .getPartyID()
        .getIdent());
    assertEquals("warehousecode", asn.getASNHeader()
        .getASNParty()
        .getWarehouseParty()
        .getPartyID()
        .getIdent());

  }

  /**
   * Test asn-header-missingAAU.csv
   */
  @Test
  public void check_asn_header_missingAAU() {
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerSeller.getCode()),
        equal(Document_.to.getName(), partnerBuyer.getCode()),
        equal("asnHeader.asnType.asnTypeCodedOther", "CrossDockingDespatchAdvice"),
        equal("asnHeader.asnNumber", "8300000131"));
    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    assertEquals(0, documents.size());
  }

  /**
   * Test asn-header-missingSH.csv
   */
  @Test
  public void check_asn_header_missingSH() {
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerSeller.getCode()),
        equal(Document_.to.getName(), partnerBuyer.getCode()),
        equal("asnHeader.asnType.asnTypeCodedOther", "CrossDockingDespatchAdvice"),
        equal("asnHeader.asnNumber", "8300000132"));
    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    assertEquals(0, documents.size());
  }

  /**
   * Test asn-header-incorrect-line.csv
   */
  @Test
  public void check_asn_header_incorrect_line() {
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerSeller.getCode()),
        equal(Document_.to.getName(), partnerBuyer.getCode()),
        equal("asnHeader.asnType.asnTypeCodedOther", "CrossDockingDespatchAdvice"),
        equal("asnHeader.asnNumber", "8300000133"));
    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    assertEquals(0, documents.size());
  }

  /**
   * Test asn-header-incorrect-date.csv
   */
  @Test
  public void check_asn_header_incorrect_date() {
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerSeller.getCode()),
        equal(Document_.to.getName(), partnerBuyer.getCode()),
        equal("asnHeader.asnType.asnTypeCodedOther", "CrossDockingDespatchAdvice"),
        equal("asnHeader.asnNumber", "8300000258"));
    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    assertEquals(0, documents.size());
  }

  /**
   * Test asn-unknown-package.csv
   */
  @Test
  public void check_asn_unknown_package() {
    // with an incorrect package id
    qb = QueryBuilder.createBuilder();
    qb.and(equal(Document_.from.getName(), partnerBuyer.getCode()),
        equal(Document_.to.getName(), company.getCode()),
        equal("asnHeader.asnNumber", "8300000175"));
    List<AdvanceShipmentNotice> documents = this.documentService.searchIndexables(
            AdvanceShipmentNotice.class, qb.query(), null)
        .getContent();
    assertEquals(0, documents.size());

  }
}
