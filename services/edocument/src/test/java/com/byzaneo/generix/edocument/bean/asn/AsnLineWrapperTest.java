package com.byzaneo.generix.edocument.bean.asn;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.bean.PropertyDescriptor;
import com.byzaneo.commons.io.BeanImportContext;
import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.edocument.api.asn.AsnLineWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.AsnCsvLine;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PackageDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNOrderNumberType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ListOfASNItemDetailType;
import java.util.HashMap;
import java.util.Locale;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class AsnLineWrapperTest {

  private MockedStatic<JSFHelper> mockedJSFHelper;

  private MockedStatic<CsvImportHelper> mockedCsvImportHelper;

  AsnLineWrapper asnLineWrapper;

  @BeforeEach
  public void before() {
    setUpStaticMocks();
    prepareJSFHelper();
    prepareAsnLineWrapper();
  }

  void setUpStaticMocks() {
    mockedJSFHelper = Mockito.mockStatic(JSFHelper.class);
    mockedCsvImportHelper = Mockito.mockStatic(CsvImportHelper.class);
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedCsvImportHelper.closeOnDemand();
    mockedJSFHelper.closeOnDemand();
  }

  private void prepareAsnLineWrapper() {
    PropertyDescriptor pd = new PropertyDescriptor("packingLocalId");
    pd.setValue(1);
    BeanDescriptor bd = new BeanDescriptor();
    bd.setParent(new BeanDescriptor());
    bd.getParent()
        .addProperty(pd);
    asnLineWrapper = Mockito.spy(new AsnLineWrapper());
    Mockito.when(asnLineWrapper.getWrapped())
        .thenReturn(bd);
  }

  private void prepareJSFHelper() {
    mockedJSFHelper.when(JSFHelper::getLocale)
        .thenReturn(Locale.ENGLISH);
  }

  @SuppressWarnings("serial")
  private void prepareCsvImportHelper(boolean ent) {
    mockedCsvImportHelper.when(CsvImportHelper::getContainersDetailMap)
        .thenReturn(
            new HashMap<Integer, PackageDetailType>() {
              {
                put(1, new PackageDetailType());
              }
            });
    // TODO : getImportedIndexable()
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(createASN(ent));
    mockedCsvImportHelper.when(() -> CsvImportHelper.getProductLineNumber(Mockito.anyString()))
        .thenReturn(1);
    mockedCsvImportHelper.when(CsvImportHelper::getRootContainerIndex)
        .thenReturn(0);
  }

  private AdvanceShipmentNotice createASN(boolean ent) {
    AdvanceShipmentNotice asn = new AdvanceShipmentNotice();
    asn.setASNHeader(new ASNHeaderType());
    if (ent) {
      ASNOrderNumberType orderNumber = new ASNOrderNumberType();
      orderNumber.setBuyerOrderNumber("Buyer Order Number");
      asn.getASNHeader()
          .getASNOrderNumber()
          .add(orderNumber);
    }
    asn.setASNDetail(new ASNDetailType());
    asn.getASNDetail()
        .setListOfASNItemDetail(new ListOfASNItemDetailType());
    return asn;
  }

  private AsnCsvLine createBean(boolean lin, boolean linLine) {
    AsnCsvLine bean = new AsnCsvLine();
    if (lin) {
      bean.setOrderNumber("Order Number");
    }
    if (linLine) {
      bean.setOrderLineNumber(1);
    }
    bean.setGtinCode("gtinCode");
    return bean;
  }

  /**
   * Use Case 1 : - ENT NÂ° de commande : X - LIN NÂ° de commande : 0 - LIN NÂ° de ligne de commande
   * : 0 Error
   */

  @Test
  public void testValidateEnt() {
    prepareCsvImportHelper(true);
    try {
      asnLineWrapper.validate(createBean(false, false), new BeanImportContext());
      fail("Should throw an exception");
    } catch (Exception e) {
      assertEquals(PropertiesException.class, e.getClass());
      assertTrue(((PropertiesException) e)
          .getExceptions()
          .get("orderNumberLine")
          .getMessage()
          .contains("Order number line is mandatory in the segment LIN"));
    }
  }

  /**
   * Use Case 2 : - ENT NÂ° de commande : X - LIN NÂ° de commande : 0 - LIN NÂ° de ligne de commande
   * : X Ok
   */

  @Test
  public void testValidateEntWithLine() {
    prepareCsvImportHelper(true);
    Exception ex = null;
    try {
      asnLineWrapper.validate(createBean(false, true), new BeanImportContext());
    } catch (Exception e) {
      ex = e;
    }
    assertEquals(null, ex);
  }

  /**
   * Use Case 3 : - ENT NÂ° de commande : 0 - LIN NÂ° de commande : X - LIN NÂ° de ligne de commande
   * : X Ok
   */

  @Test
  public void testValidateLinWithLine() {
    prepareCsvImportHelper(false);
    Exception ex = null;
    try {
      asnLineWrapper.validate(createBean(true, true), new BeanImportContext());
    } catch (Exception e) {
      ex = e;
    }
    assertEquals(null, ex);
  }

  /**
   * Use Case 4 : - ENT NÂ° de commande : 0 - LIN NÂ° de commande : X - LIN NÂ° de ligne de commande
   * : 0 Error
   */

  @Test
  public void testValidateLin() {
    prepareCsvImportHelper(false);
    try {
      asnLineWrapper.validate(createBean(true, false), new BeanImportContext());
      fail("Should throw an exception");
    } catch (Exception e) {
      assertEquals(PropertiesException.class, e.getClass());
      assertTrue(((PropertiesException) e)
          .getExceptions()
          .get("orderNumberLine")
          .getMessage()
          .contains("Order number line is mandatory in the segment LIN"));
    }
  }

  /**
   * Use Case 5 : - ENT NÂ° de commande : X - LIN NÂ° de commande : X - LIN NÂ° de ligne de commande
   * : X Error
   */

  @Test
  public void testValidateALL() {
    prepareCsvImportHelper(true);
    try {
      asnLineWrapper.validate(createBean(true, true), new BeanImportContext());
      fail("Should throw an exception");
    } catch (Exception e) {
      assertEquals(PropertiesException.class, e.getClass());
      assertTrue(((PropertiesException) e)
          .getExceptions()
          .get("orderNumber")
          .getMessage()
          .contains("Purchase order number can't be defined on header and line levels"));
    }
  }

  /**
   * Use Case 6 : - ENT NÂ° de commande : X - LIN NÂ° de commande : X - LIN NÂ° de ligne de commande
   * : 0 Error
   */

  // TODO: change the test to avoid depending of a label property
  @Test
  public void testValidateEntWithLin() {
    prepareCsvImportHelper(true);
    try {
      asnLineWrapper.validate(createBean(true, false), new BeanImportContext());
      fail("Should throw an exception");
    } catch (Exception e) {
      assertEquals(PropertiesException.class, e.getClass());
      assertTrue(((PropertiesException) e)
          .getExceptions()
          .get("orderNumber")
          .getMessage()
          .contains("Purchase order number can't be defined on header and line levels"));
    }
  }

  /**
   * Use Case 7 : - ENT NÂ° de commande : 0 - LIN NÂ° de commande : 0 - LIN NÂ° de ligne de commande
   * : X Error
   */

  @Test
  public void testValidateLine() {
    prepareCsvImportHelper(false);
    try {
      asnLineWrapper.validate(createBean(false, true), new BeanImportContext());
      fail("Should throw an exception");
    } catch (Exception e) {
      assertEquals(PropertiesException.class, e.getClass());
      assertTrue(((PropertiesException) e)
          .getExceptions()
          .get("orderNumberLine")
          .getMessage()
          .contains("Order reference can not be null if Order number line is present"));
    }
  }

  /**
   * Use Case 8 : - ENT NÂ° de commande : 0 - LIN NÂ° de commande : 0 - LIN NÂ° de ligne de commande
   * : 0 Ok
   */

  @Test
  public void testValidateNONE() {
    prepareCsvImportHelper(false);
    try {
      asnLineWrapper.validate(createBean(false, false), new BeanImportContext());
    } catch (Exception e) {
      assertEquals(IndexOutOfBoundsException.class, e.getClass());
    }
  }

}
