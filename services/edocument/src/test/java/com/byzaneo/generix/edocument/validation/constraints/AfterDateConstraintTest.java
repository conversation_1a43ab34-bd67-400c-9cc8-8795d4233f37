package com.byzaneo.generix.edocument.validation.constraints;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.util.List;

import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;

/**
 * Testing the constraint class
 * 
 * <AUTHOR>
 */
public class AfterDateConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/AfterDateConstraintTest.xml"), false);


//@formatter:off
  /**
   * Test OK:
   * end date after start date
   * only 1 node start date 
   * absolute path as param
   */
//@formatter:on
  @Test
  public void testValidAbsolutePath() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemDates/InvoicingPeriod/EndDate",
        "//InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemDates/InvoicingPeriod/StartDate");
    instance.getCache()
        .put("//InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemDates/InvoicingPeriod/StartDate",
            java.util.Arrays.asList("2021-01-01T00:00:07"));
    boolean result = instance.isValid();
    assertTrue(result);
  }

//@formatter:off
  /**
   * Test OK:
   * end date after start date
   * only 1 node start date 
   * relative path as param
   */
//@formatter:on
  @Test
  public void testValidRelativePath() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/LineItemDates/InvoicingPeriod/EndDate", "StartDate");
    boolean result = instance.isValid();
    assertTrue(result);
  }

//@formatter:off
  /**
   * Test OK:
   * end date exists but:
   * NO node start date 
   * relative path as param
   */
//@formatter:on
  @Test
  public void testValidNoStartDate() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/test0pass/LineItemDates/InvoicingPeriod/EndDate", "StartDate");
    boolean result = instance.isValid();
    assertTrue(result);
  }

//@formatter:off
  /**
   * Test KO:
   * end date BEFORE start date
   * only 1 node start date 
   * absolute path as param
   */
//@formatter:on
  @Test
  public void testInvalidAbsolutePath() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/test1fail/LineItemDates/InvoicingPeriod/EndDate",
        "//InvoiceDetail/ListOfInvoiceItemDetail/test1fail/LineItemDates/InvoicingPeriod/StartDate");
    instance.getCache()
        .put("//InvoiceDetail/ListOfInvoiceItemDetail/test1fail/LineItemDates/InvoicingPeriod/StartDate",
            java.util.Arrays.asList("2025-01-01T00:00:07"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

//@formatter:off
  /**
   * Test KO:
   * end date BEFORE start date
   * only 1 node start date 
   * relative path as param
   */
//@formatter:on
  @Test
  public void testInvalidRelativePath() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/test1fail/LineItemDates/InvoicingPeriod/EndDate", "StartDate");
    boolean result = instance.isValid();
    assertFalse(result);
  }

//@formatter:off
  /**
   * Test OK:
   * end date AFTER all the start dates
   * 3 nodes start date 
   * relative path as param
   */
//@formatter:on
  @Test
  public void testValidMultipleStartDates() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/testmultiok/LineItemDates/InvoicingPeriod/EndDate", "StartDate");
    boolean result = instance.isValid();
    assertTrue(result);
  }

//@formatter:off
  /**
   * Test KO:
   * end date BEFORE one of the 3 the start dates
   * 3 nodes start date 
   * relative path as param
   */
//@formatter:on
  @Test
  public void testInvalidMultipleStartDates() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/testmultiko/LineItemDates/InvoicingPeriod/EndDate", "StartDate");
    boolean result = instance.isValid();
    assertFalse(result);
  }

  /**
   * Test KO: error parsing start date
   */
  @Test
  public void testParseErrorStartDate() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/testkoparteerrorstartdate/LineItemDates/InvoicingPeriod/EndDate", "StartDate");
    boolean result = instance.isValid();
    assertFalse(result);
  }

  /**
   * Test KO: error parsing end date
   */
  @Test
  public void testParseErrorEndDate() {
    AfterDateConstraint instance = initializeInstance(
        "//InvoiceDetail/ListOfInvoiceItemDetail/testkoparteerrorenddate/LineItemDates/InvoicingPeriod/EndDate", "StartDate");
    boolean result = instance.isValid();
    assertFalse(result);
  }

  private AfterDateConstraint initializeInstance(String xpath, String theOnlyParam) {
    AfterDateConstraint instance = new AfterDateConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, xpath);
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(theOnlyParam));
    return instance;
  }

}
