package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNode;
import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class InListConstraintTest {
  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/InvoiceForInListConstraintTest.xml"),
      false);

  @Test
  public void testValid() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/number");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2", "dummy", "anything else"));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid2() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFile");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test"));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid3() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFile");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test5"));
    boolean result = instance.isValid();
    assertTrue(result);
  }
  @Test
  public void testValid4() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testOKRegex");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test7"));
    boolean result = instance.isValid();
    assertTrue(result);
  }
  @Test
  public void testIsNotValid1() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2", "dummy", "anything else"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid2() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber2");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2", "dummy", "anything else"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid3() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber3");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2", "dummy", "anything else"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid4() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber4");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2", "dummy", "anything else"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid5() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber5");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2", "dummy", "anything else"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid6() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber6");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2", "dummy", "anything else"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid7() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/number");
    nodes.add(evaluateNode(XCBL_ROOT, "//summary/numbers/otherNumber7"));
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2", "dummy", "anything else"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid8() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFile");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid9() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFile");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test3"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid10() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFile");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test4"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid11() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFile");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("testNotExisting"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid12() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFileNotOK");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid13() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFile2");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test6"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid14() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testFile2");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test7"));
    boolean result = instance.isValid();
    assertFalse(result);
  }
  @Test
  public void testValid5() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testRegexOK");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test7"));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testNotValid16() {
    InListConstraint instance = new InListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/testRX");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("test7"));
    boolean result = instance.isValid();
    assertFalse(result);
  }
}
