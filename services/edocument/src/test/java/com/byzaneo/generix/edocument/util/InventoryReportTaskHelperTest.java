package com.byzaneo.generix.edocument.util;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Locale;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.DateCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.DateQualifierType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfDateCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.InventoryReportDatesType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.InventoryReportPartyType;
import org.apache.log4j.Logger;

import com.byzaneo.generix.service.repository.bean.Product;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.xcbl.bean.InventoryReport;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PurposeCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.InventoryReportDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.InventoryReportTypeCodeType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * test ckass for invrptHelper
 * 
 * <AUTHOR>
 * @see InventoryReportTaskHelper
 */
public class InventoryReportTaskHelperTest {

  private Product product;

  private Product product2;

  InventoryReportDetailType createInvLineFromOneProduct;

  private Partner partner;

  private Company company;

  private Logger logger = Logger.getLogger(InventoryReportTaskHelper.class);

  @BeforeEach
  public void before() {
    product = mock(Product.class, RETURNS_DEEP_STUBS);
    when(product.getProductName()).thenReturn("ProductName1");
    when(product.getDescription()).thenReturn("Product1");
    when(product.getManuPartNumber()).thenReturn("ManuPartNumber");
    when(product.getUnitOfMeasurementType()).thenReturn("UnitOfMesure");

    product2 = mock(Product.class, RETURNS_DEEP_STUBS);
    when(product2.getProductName()).thenReturn("ProductName2");
    when(product2.getDescription()).thenReturn("Product2");
    when(product2.getManuPartNumber()).thenReturn("ManuPartNumber2");
    when(product2.getUnitOfMeasurementType()).thenReturn("UnitOfMesure2");

    partner = mock(Partner.class, RETURNS_DEEP_STUBS);
    when(partner.getFullname()).thenReturn("Partner");
    when(partner.getCode()).thenReturn("codep");
    when(partner.getLocation()
        .getAddress()
        .getStreetName()).thenReturn("15 rue des Mimosas");
    when(partner.getLocation()
        .getAddress()
        .getPostalCode()).thenReturn("13001");
    when(partner.getLocation()
        .getAddress()
        .getCity()).thenReturn("Marseille");
    when(partner.getLocation()
        .getAddress()
        .getCountry()).thenReturn(Locale.FRANCE);
    when(partner.getDescription()).thenReturn(Partner.DESCRIPTION);

    company = mock(Company.class, RETURNS_DEEP_STUBS);
    when(company.getFullname()).thenReturn("Generix group");
    when(partner.getCode()).thenReturn("codeg");
    when(company.getLocation()
        .getAddress()
        .getStreetName()).thenReturn("15 rue Beaubourg");
    when(company.getLocation()
        .getAddress()
        .getPostalCode()).thenReturn("75003");
    when(company.getLocation()
        .getAddress()
        .getCity()).thenReturn("Paris");
    when(company.getLocation()
        .getAddress()
        .getCountry()).thenReturn(Locale.FRANCE);
    when(company.getDescription()).thenReturn(Company.DESCRIPTION);
  }

  @AfterEach
  public void after() {
    product = null;
    product2 = null;
    createInvLineFromOneProduct = null;
    partner = null;
    company = null;
  }

  @Test
  public void testInitReportHeader() {
    InventoryReport initInventoryReport = InventoryReportTaskHelper.initInventoryReport(partner, company);

    ;
    assertEquals("Seller", initInventoryReport.getInventoryReportHeader()
        .getInventoryReportParty()
        .getSenderParty()
        .getPartyRoleCodedOther());
    assertEquals("Buyer", initInventoryReport.getInventoryReportHeader()
        .getInventoryReportParty()
        .getReceiverParty()
        .getPartyRoleCodedOther());
    assertEquals(PurposeCodeType.ORIGINAL,
        initInventoryReport.getInventoryReportHeader()
            .getInventoryReportPurpose()
            .getPurposeCoded());
    assertEquals(InventoryReportTypeCodeType.SELLER_INVENTORY_REPORT,
        initInventoryReport.getInventoryReportHeader()
            .getInventoryReportType()
            .getInventoryReportTypeCoded());
  }

  @Test
  public void testCreateLineProduct() {
    createInvLineFromOneProduct = InventoryReportTaskHelper.createInvLineFromOneProduct(product);
    assertEquals("Product1", createInvLineFromOneProduct.getInventoryItemIdentifiers()
        .getItemDescription());
    assertEquals("ManuPartNumber", createInvLineFromOneProduct.getInventoryItemIdentifiers()
        .getPartNumbers()
        .getSellerPartNumber()
        .getPartID());
    assertEquals("UnitOfMesure",
        createInvLineFromOneProduct.getTotalInventoryQuantity()
            .getUnitOfMeasurement()
            .getUOMCodedOther());
    assertEquals(BigDecimal.ZERO, createInvLineFromOneProduct.getTotalInventoryQuantity()
        .getQuantityValue()
        .getValue());

  }

  @Test
  public void testUpdateInvRpt() {
    InventoryReport invReport = new InventoryReport();
    invReport = InventoryReportTaskHelper.createInvRptLinesFromPartnerRefProduct(invReport, Arrays.asList(product));
    assertEquals("Product1", invReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getInventoryItemIdentifiers()
        .getItemDescription());
    assertEquals("ManuPartNumber", invReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getInventoryItemIdentifiers()
        .getPartNumbers()
        .getSellerPartNumber()
        .getPartID());
    assertEquals("UnitOfMesure", invReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getTotalInventoryQuantity()
        .getUnitOfMeasurement()
        .getUOMCodedOther());
    assertEquals(BigDecimal.ZERO, invReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getTotalInventoryQuantity()
        .getQuantityValue()
        .getValue());

  }

  @Test
  public void testInitHeaderAndOneLine() {
    InventoryReport initInventoryReport = InventoryReportTaskHelper.initInventoryAndCreateLinesFromProduct(
        Arrays.asList(product), partner, company);
    assertEquals(PurposeCodeType.ORIGINAL,
        initInventoryReport.getInventoryReportHeader()
            .getInventoryReportPurpose()
            .getPurposeCoded());
    assertEquals(InventoryReportTypeCodeType.SELLER_INVENTORY_REPORT,
        initInventoryReport.getInventoryReportHeader()
            .getInventoryReportType()
            .getInventoryReportTypeCoded());
    assertEquals("Product1", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getInventoryItemIdentifiers()
        .getItemDescription());
    assertEquals("ManuPartNumber", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getInventoryItemIdentifiers()
        .getPartNumbers()
        .getSellerPartNumber()
        .getPartID());
    assertEquals("UnitOfMesure", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getTotalInventoryQuantity()
        .getUnitOfMeasurement()
        .getUOMCodedOther());
    assertEquals(BigDecimal.ZERO, initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getTotalInventoryQuantity()
        .getQuantityValue()
        .getValue());
  }

  @Test
  public void testInitHeaderAnd2Lines() {
    InventoryReport initInventoryReport = InventoryReportTaskHelper.initInventoryAndCreateLinesFromProduct(
        Arrays.asList(product, product2), partner, company);
    assertEquals(PurposeCodeType.ORIGINAL,
        initInventoryReport.getInventoryReportHeader()
            .getInventoryReportPurpose()
            .getPurposeCoded());
    assertEquals(InventoryReportTypeCodeType.SELLER_INVENTORY_REPORT,
        initInventoryReport.getInventoryReportHeader()
            .getInventoryReportType()
            .getInventoryReportTypeCoded());
    assertEquals("Product1", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getInventoryItemIdentifiers()
        .getItemDescription());
    assertEquals("ManuPartNumber", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getInventoryItemIdentifiers()
        .getPartNumbers()
        .getSellerPartNumber()
        .getPartID());
    assertEquals("UnitOfMesure", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getTotalInventoryQuantity()
        .getUnitOfMeasurement()
        .getUOMCodedOther());
    assertEquals(BigDecimal.ZERO, initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(0)
        .getTotalInventoryQuantity()
        .getQuantityValue()
        .getValue());

    assertEquals("Product2", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(1)
        .getInventoryItemIdentifiers()
        .getItemDescription());
    assertEquals("ManuPartNumber2", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(1)
        .getInventoryItemIdentifiers()
        .getPartNumbers()
        .getSellerPartNumber()
        .getPartID());
    assertEquals("UnitOfMesure2", initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(1)
        .getTotalInventoryQuantity()
        .getUnitOfMeasurement()
        .getUOMCodedOther());
    assertEquals(BigDecimal.ZERO, initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .get(1)
        .getTotalInventoryQuantity()
        .getQuantityValue()
        .getValue());

    assertEquals(2, initInventoryReport.getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .size());
  }

  @Test
  public void testPrepareInvRptBuyerAndSellerParties() {
    InventoryReport inventoryReport = InventoryReportTaskHelper.initInventoryAndCreateLinesFromProduct(
        Arrays.asList(product, product2), partner, company);
    InventoryReport result = InventoryReportTaskHelper.prepareInvRptBuyerAndSellerParties(partner, company, inventoryReport);

    InventoryReportPartyType party = result.getInventoryReportHeader()
        .getInventoryReportParty();
    assertNotNull(party);
    assertNotNull(party.getSenderParty());
    assertNotNull(party.getReceiverParty());
    assertEquals("Seller", party.getSenderParty()
        .getPartyRoleCodedOther());
    assertEquals("Buyer", party.getReceiverParty()
        .getPartyRoleCodedOther());
  }

  @Test
  public void testCopyInventoryXcbl() {
    DateQualifierType dateQualifierType = new DateQualifierType();
    dateQualifierType.setDateQualifierCoded("SUBMISSION_DATE");

    DateCodedType dateCodedType = new DateCodedType();
    dateCodedType.setDateQualifier(dateQualifierType);

    ListOfDateCodedType listOfDateCodedType = new ListOfDateCodedType();
    listOfDateCodedType.getDateCoded()
        .add(dateCodedType);

    InventoryReportDatesType inventoryReportDates = new InventoryReportDatesType();
    inventoryReportDates.setListOfDateCoded(listOfDateCodedType);

    InventoryReport toDuplicate = InventoryReportTaskHelper.initInventoryAndCreateLinesFromProduct(
        Arrays.asList(product), partner, company);
    InventoryReport toCreate = InventoryReportTaskHelper.initInventoryAndCreateLinesFromProduct(Arrays.asList(), partner, company);
    toDuplicate.getInventoryReportHeader()
        .setInventoryReportDates(new InventoryReportDatesType());
    toDuplicate.getInventoryReportHeader()
        .setInventoryReportDates(inventoryReportDates);

    InventoryReportTaskHelper.copyInventoryXcbl(toDuplicate, toCreate);

    assertNotNull(toCreate.getInventoryReportHeader()
        .getInventoryReportIssueDate());
    assertEquals(toDuplicate.getInventoryReportSummary(), toCreate.getInventoryReportSummary());
    assertEquals(toDuplicate.getListOfInventoryReportDetail(), toCreate.getListOfInventoryReportDetail());
  }

  @Test
  public void testCopyInventoryDoc() {
    Document toDuplicate = new Document();
    Document toCreate = new Document();
    toDuplicate.setOwners("Gnx");
    toDuplicate.setTo("receiver");
    toDuplicate.setFrom("sender");
    InventoryReportTaskHelper.copyInventoryDoc(toDuplicate, toCreate);

    assertEquals(toDuplicate.getOwners(), toCreate.getOwners());
    assertEquals(toDuplicate.getTo(), toCreate.getTo());
    assertEquals(toDuplicate.getFrom(), toCreate.getFrom());
  }

  @Test
  public void testDeleteDraftDocumentFileBeforeSending() {
    File file = new File("src/test/resources/data/inventoryrptUuid/errors.txt");
    try {
      file.getParentFile().mkdirs();
      file.createNewFile();
    }
    catch (IOException e) {
      logger.warn("Failed to create new file!", e);
      return;
    }
    DocumentFile dof = new DocumentFile();
    dof.setFile(file);
    dof.setType(FileType.XCBL);
    Document doc = new Document();
    ArrayList<DocumentFile> list = new ArrayList<>();
    list.add(dof);
    doc.setFiles(list);

    InventoryReportTaskHelper.deleteDraftDocumentFileBeforeSending(doc);

    assertEquals(0, doc.getFiles()
        .size());
    assertFalse(file.exists());
  }
}