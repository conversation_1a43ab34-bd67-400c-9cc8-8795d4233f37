package com.byzaneo.generix.edocument.test;

import static com.byzaneo.commons.bean.FileType.EDIUNIT;
import static com.byzaneo.commons.bean.FileType.ERROR;
import static com.byzaneo.commons.bean.FileType.XCBL;
import static com.byzaneo.xtrade.util.DocumentHelper.searchFiles;
import static java.lang.String.format;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Collections.emptyList;
import static java.util.Collections.singleton;
import static org.apache.commons.io.FileUtils.writeStringToFile;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.doReturn;

import java.io.*;
import java.util.*;

import com.byzaneo.commons.bean.*;
import com.byzaneo.generix.edocument.service.EDocumentService;
import com.byzaneo.generix.edocument.util.*;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.*;

import org.activiti.engine.delegate.DelegateExecution;
import org.apache.commons.lang3.StringUtils;
import org.apache.tools.ant.util.FileUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

public class EDocumentErrorHelperTest {

  static final private String TARGET_DIR = "target";

  static final private String EDI_EXTENSION = ".edi";

  static final private String ERR_EXTENSION = ".err";

  static final private String XCBL_EXTENSION = ".xml";

  @Mock
  private DocumentErrorService documentErrorService;
  @Mock
  private DocumentErrorTranslationService documentErrorTranslationService;

  @Mock
  DelegateExecution execution;

  @BeforeEach
  public void before() {
    MockitoAnnotations.initMocks(this);
    EDocumentErrorHelper.setDocumentErrorService(documentErrorService);
    EDocumentErrorHelper.setDocumentErrorTranslationService(documentErrorTranslationService);

  }

  @AfterEach
  public void cleanTargetDir() {
    File targetDir = new File(TARGET_DIR);
    File[] toBeDeleted = targetDir.listFiles(new FilenameFilter() {

      @Override
      public boolean accept(File dir, String name) {
        return name.endsWith(EDI_EXTENSION) || name.endsWith(ERR_EXTENSION);
      }
    });
    if (toBeDeleted == null)
      return;
    for (File toDelete : toBeDeleted) {
      FileUtils.delete(toDelete);
    }
  }

  @Test
  public void testGetEDocumentError_null_document() throws Exception {
    try {
      EDocumentErrorHelper.getEDocumentError(null, null);
      fail();
    }
    catch (IllegalArgumentException e) {
      assertThat(e).hasMessage("document null");
    }
  }

  @Test
  public void testGetEDocumentError_no_error() throws Exception {
    Document document = new Document();
    doReturn(emptyList()).when(documentErrorService)
        .findErrors(document);
    // action
    EDocumentErrors result = EDocumentErrorHelper.getEDocumentError(null, document);
    // assertions
    assertThat(result).isEmpty();
  }

  @Test
  public void testEnsureOneErrorFile() {
    Document document = new Document();
    DocumentFile df = new DocumentFile(new File(TARGET_DIR, "testEnsureErrorFileOk" + XCBL_EXTENSION), XCBL,
        "testEnsureErrorFileOk", "007", document, "comment?");
    df.setDescription(EDocumentService.OFFICIAL_INDEX);
    document.addFile(df);

    EDocumentErrorHelper.addError(document, "testEnsureErrorFileOk");

    assertThat(searchFiles(singleton(document), format("type = \"%s\"", ERROR))).hasSize(1);
  }

  @Test
  public void testEnsureOneErrorFileWhenNoDocumentFile() {
    Document document = new Document();

    EDocumentErrorHelper.addError(document, "testEnsureErrorFileNoDocumentFile");

    assertThat(searchFiles(singleton(document), format("type = \"%s\"", ERROR))).hasSize(0);
  }

  @Test
  public void testEnsureErrorFileDocumentFileNullFile() {
    Document document = new Document();
    document.addFile(new DocumentFile(null, EDIUNIT, "testEnsureErrorFileDocumentFileNullFile" + EDI_EXTENSION,
        "007", document, "comment?"));

    EDocumentErrorHelper.addError(document, "testEnsureErrorFileDocumentFileNullFile");

    assertThat(searchFiles(singleton(document), format("type = \"%s\"", ERROR))).hasSize(0);
  }

  @Test
  public void testEnsureErrorFileConcat() {
    Document doc1 = new Document();
    DocumentFile df1 = new DocumentFile(new File(TARGET_DIR, "testEnsureErrorFileConcat1" + XCBL_EXTENSION), XCBL,
        "testEnsureErrorFileConcat1", "007", doc1, "comment?");
    df1.setDescription(EDocumentService.OFFICIAL_INDEX);
    doc1.addFile(df1);
    EDocumentErrorHelper.addError(doc1, "errorCode1");
    Document doc2 = new Document();
    DocumentFile df2 = new DocumentFile(new File(TARGET_DIR, "testEnsureErrorFileConcat2" + XCBL_EXTENSION), XCBL,
        "testEnsureErrorFileConcat2", "008", doc2, "comment?");
    df2.setDescription(EDocumentService.OFFICIAL_INDEX);
    doc2.addFile(df2);
    EDocumentErrorHelper.addError(doc2, "errorCode2");

    Document doc3 = new Document();
    doc3.addFile(doc1.getDocumentFileWithType(ERROR));
    doc3.addFile(doc2.getDocumentFileWithType(ERROR));

    doReturn(emptyList()).when(documentErrorService)
        .findErrors(doc3);

    EDocumentErrorHelper.addError(doc3, "errorCode3");

    assertThat(EDocumentErrorHelper.getEDocumentError(execution, doc3)).hasSize(3);

  }

  @Test
  public void testAddError() {
    Document document = new Document();
    doReturn(emptyList()).when(documentErrorService)
        .findErrors(document);
    DocumentFile df = new DocumentFile(new File(TARGET_DIR, "testAddError" + XCBL_EXTENSION), XCBL, null, null, document, null);
    df.setDescription(EDocumentService.OFFICIAL_INDEX);
    document.addFile(df);
    String errorCode = "ECode";
    Map<Locale, String> translations = new HashMap<>();
    String messageFrench = "mess_fr";
    String messageJap = "mess_jp";
    translations.put(Locale.FRENCH, messageFrench);
    translations.put(Locale.JAPANESE, messageJap);

    // action
    EDocumentErrorHelper.addError(document, errorCode, translations);
    // System.out.println(document.getError());

    // assertions
    // assertThat(document.getError()).isEqualTo("[{\"f\":\"EDEH\",\"k\":\"ECode\",\"ls\":[{\"fr\":\"mess_fr\"},{\"ja\":\"mess_jp\"},{\"en\":\"ECode\"}]}]");
    EDocumentErrors result = EDocumentErrorHelper.getEDocumentError(execution, document);
    assertThat(result).hasSize(1);
    LabelSet labelSet = result.get(0);
    assertThat(labelSet.getFamily()).isEqualTo(EDocumentErrorHelper.LABELSET_FAMILY);
    assertThat(labelSet.getKey()).isEqualTo(errorCode);
    assertThat(labelSet.getLabelValue(Locale.FRENCH)).isEqualTo(messageFrench);
    assertThat(labelSet.getLabelValue(Locale.JAPANESE)).isEqualTo(messageJap);
    assertThat(labelSet.getLabelValue(Locale.ENGLISH)).isEqualTo(errorCode);

    /** add a new error code on a document that already has a non-empty error */
    String errorCode2 = "ECode_2";
    translations = new HashMap<>();
    String messageFrench2 = "mess_fr_2";
    translations.put(Locale.FRENCH, messageFrench2);

    // action
    EDocumentErrorHelper.addError(document, errorCode2, translations);
    System.out.println(document.getError());

    // assertions
    result = EDocumentErrorHelper.getEDocumentError(execution, document);
    assertThat(result).hasSize(2);
    assertThat(testLabelSetEquality(result.get(0), labelSet)).isTrue();// first error must not be modified
    LabelSet labelSet2 = result.get(1);
    assertThat(labelSet2.getKey()).isEqualTo(errorCode2);
    assertThat(labelSet2.getLabelValue(Locale.FRENCH)).isEqualTo(messageFrench2);
    assertThat(labelSet2.getLabelValue(Locale.ENGLISH)).isEqualTo(errorCode2);
  }

  @Test
  public void test_error_file_is_not_well_formated() throws IOException {
    Document document = new Document();
    File file = new File(TARGET_DIR, "testAddError" + XCBL_EXTENSION);
    DocumentFile docFile = new DocumentFile(file, XCBL, null, null, document, null);
    docFile.setDescription(EDocumentService.OFFICIAL_INDEX);
    document.addFile(docFile);

    Map<Locale, String> translations = new HashMap<>();
    translations.put(Locale.FRENCH, "mess_fr");
    translations.put(Locale.JAPANESE, "mess_jp");

    // Creation du fichier d'erreur et changement du contenu du fichier d'erreur par un json invalide
    document = EDocumentErrorHelper.addError(document, "ECode", translations);
    docFile = document.getDocumentFileWithType(ERROR);
    String json = "\"f\":\"EDEH\",\"k\":\"ECode\",\"ls\":[{\"fr\":\"mess_fr\"},{\"en\":\"ECode\"},{\"ja\":\"mess_jp\"}]}";
    writeStringToFile(file, json, UTF_8);
    docFile.setFile(file);

    doReturn(emptyList()).when(documentErrorService)
        .findErrors(document);
    // Vérification qu'on a bien en erreur un fichier d'erreur mal formé
    EDocumentErrors result = EDocumentErrorHelper.getEDocumentError(execution, document);
    assertThat(result).hasSize(1);
    LabelSet labelSet = result.get(0);
    assertThat(labelSet.getFamily()).isEqualTo(EDocumentErrorHelper.LABELSET_FAMILY);
    assertThat(labelSet.getKey()).isEqualTo("json.format_not_correct");
  }

  @Test
  public void testCorrectTranslations_add_defaultvalue_for_mandatory_locales() throws Exception {
    Document doc = new Document();
    String errorCode = "ECode";
    Map<Locale, String> translations = new HashMap<>();

    // action
    EDocumentErrorHelper.addError(doc, errorCode, translations);

    // assertions
    assertThat(EDocumentErrorHelper.getMandatoryLocales().length).isGreaterThan(0);// otherwise no real correction
    assertThat(translations).hasSize(EDocumentErrorHelper.getMandatoryLocales().length);
    for (Locale mandatoryLocale : EDocumentErrorHelper.getMandatoryLocales()) {
      assertThat(translations).containsEntry(mandatoryLocale, errorCode);
    }
  }

  @Test
  public void testConvertStringToEdocumentErrorsWithErrors() {
    String value = "[EDEH.labels.service-edocument.exchange_requiered_to = {fr=L'interlocuteur (recipient) est obligatoire pour l'échange autorisé, en=Receiver is required in a allowed exchange}, EDEH.labels.service-edocument.recaplist_unknown_legalEntity = {fr=labels.service-edocument.recaplist_unknown_legalEntity, en=labels.service-edocument.recaplist_unknown_legalEntity}]";
    EDocumentErrors eDocErrors = EDocumentErrorHelper.convertStringToEdocumentErrors(value);
    Assertions.assertNull(eDocErrors);
  }

  @Test
  public void testConvertStringToEdocumentErrors() {
    String value = "[{\"f\":\"EDEH\",\"k\":\"labels.service-edocument.exchange_requiered_to\",\"ls\":[{\"fr\":\"L\u0027interlocuteur (recipient) est obligatoire pour l\u0027échange autorisé\"},{\"en\":\"Receiver is required in a allowed exchange\"}]},{\"f\":\"EDEH\",\"k\":\"labels.service-edocument.recaplist_unknown_legalEntity\",\"ls\":[{\"fr\":\"labels.service-edocument.recaplist_unknown_legalEntity\"},{\"en\":\"Populate RecapList : Legal entity does not exist in this environment.\"}]}]";
    EDocumentErrors eDocErrors = EDocumentErrorHelper.convertStringToEdocumentErrors(value);
    Assertions.assertTrue(eDocErrors != null && eDocErrors.size() == 2 && eDocErrors.get(0)
        .getFamily()
        .equals("EDEH"));
  }

  @Test
  public void testGetTranslationsWithCode() {
    Map<Locale, String> labels = new HashMap<>();
    labels.put(Locale.ENGLISH, "Let's test with english label");
    labels.put(Locale.FRENCH, "Let's test with french label");
    Mockito.doReturn(labels)
        .when(documentErrorTranslationService)
        .getLabelsForCode("test1");
    Map<Locale, String> translations = EDocumentErrorHelper.getDocumentErrorTranslations(new DocumentError("TEST", "test1", null));
    Assertions.assertTrue(labels.get(Locale.ENGLISH)
        .equals(translations.get(Locale.ENGLISH)));
    Assertions.assertTrue(labels.get(Locale.FRENCH)
        .equals(translations.get(Locale.FRENCH)));
  }

  @Test
  public void testGetTranslationWithDefaultLabel() {
    String defaultLabel = "Let's test with default label";
    Map<Locale, String> translations = EDocumentErrorHelper.getDocumentErrorTranslations(
        new DocumentError("TEST", null, defaultLabel));
    for (Locale locale : EDocumentErrorHelper.getMandatoryLocales())
      Assertions.assertTrue(defaultLabel.equals(translations.get(locale)));
  }

  @Test
  public void testGetTranslationWithParams() {
    Map<Locale, String> labels = new HashMap<>();
    labels.put(Locale.ENGLISH, "Let's {0} with english' {1}");
    labels.put(Locale.FRENCH, "Let's {0} with french' {1}");
    Mockito.doReturn(labels)
        .when(documentErrorTranslationService)
        .getLabelsForCode("test1");
    Map<Locale, String> translations = EDocumentErrorHelper.getDocumentErrorTranslations(
        new DocumentError("TEST", "test1", null, "[\"test\",\"label\"]"));
    Assertions.assertTrue("Let's test with english' label".equals(translations.get(Locale.ENGLISH)));
    Assertions.assertTrue("Let's test with french' label".equals(translations.get(Locale.FRENCH)));

  }

  /**
   * test utility. LabelSet.equals defaults to Object.equals if no persistent id is set and does not compare the labels
   *
   * @param one
   * @param two
   * @return
   */
  private boolean testLabelSetEquality(LabelSet one, LabelSet two) {
    if (one == null) {
      return two == null;
    }
    if (two == null) {
      return false;
    }
    return StringUtils.equals(one.getFamily(), two.getFamily()) && StringUtils.equals(one.getKey(), two.getKey()) &&
        testMapEquality(one.getLabels(), two.getLabels());
  }

  private boolean testMapEquality(Map<Locale, Label> one, Map<Locale, Label> two) {
    if (one == null) {
      return two == null;
    }
    return one.equals(two);
  }
}
