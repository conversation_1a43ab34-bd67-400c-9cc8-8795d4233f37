package com.byzaneo.generix.edocument.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.byzaneo.generix.edocument.service.EDocumentService.EDocument;
import com.byzaneo.xtrade.xcbl.bean.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class EDocumentTest {

  @Test
  public void testValueOfType() {
    assertEquals(EDocument.Invoice, EDocument.valueOf(InvoiceIndex.class));
    assertNotEquals(EDocument.Invoice, EDocument.valueOf(OrderIndex.class));
  }

  @Test
  public void testValueOfInstance() {
    assertEquals(EDocument.Invoice, EDocument.valueOf(new InvoiceIndex()));
    assertNotEquals(EDocument.Invoice, EDocument.valueOf(new OrderIndex()));
  }
}
