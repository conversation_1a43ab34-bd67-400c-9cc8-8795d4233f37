package com.byzaneo.generix.edocument.validation.constraints;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

import com.byzaneo.commons.util.DomHelper;

import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

public class SameContentConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/sameContentConstraintTestFile.xml"), false);

  @Test
  public void testValidSamePath() {
    String argumentPath = "//object1/number2";
    SameContentConstraint instance = new SameContentConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//object1/number1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValidMultiple() {
    String argumentPath = "//object1/first-name";
    SameContentConstraint instance = new SameContentConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//object1/names/name");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValidDifferentPath() {
    String argumentPath = "//object2/value1";
    SameContentConstraint instance = new SameContentConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//object1/number1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testInvalidWithPaddingOne() {
    String argumentPath = "//object2/value2";
    SameContentConstraint instance = new SameContentConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//object1/number1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalidWithPaddingTwo() {
    String argumentPath = "//object2/value3";
    SameContentConstraint instance = new SameContentConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//object1/number1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid() {
    String argumentPath = "//object2/value4";
    SameContentConstraint instance = new SameContentConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//object1/number1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalidPath() {
    String argumentPath = "//object2/notExisting";
    SameContentConstraint instance = new SameContentConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//object1/number1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNoGlobalPath() {
    String argumentPath = "object2/notExisting";
    SameContentConstraint instance = new SameContentConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//object1/number1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    boolean result = instance.isValid();
    assertFalse(result);
  }

}
