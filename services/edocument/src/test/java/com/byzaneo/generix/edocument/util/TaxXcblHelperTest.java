package com.byzaneo.generix.edocument.util;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.ReasonTaxExemptCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxReferenceType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class TaxXcblHelperTest {

  @Test
  public void testCreateTaxReferenceType() {
    TaxReferenceType ref1 = TaxXcblHelper.createTaxReferenceType();
    TaxReferenceType ref2 = TaxXcblHelper.createTaxReferenceType("NDT");

    assertNotNull(ref1);
    assertEquals(ReasonTaxExemptCodeType.OTHER, ref1.getReasonTaxExemptCoded());
    assertNotNull(ref2);
    assertEquals("NDT", ref2.getTaxTreatmentCodedOther());
  }

  @Test
  public void testCreateExoTaxReferenceType() {
    TaxReferenceType ref = TaxXcblHelper.createExoTaxReferenceType("FakeExemption");
    assertNotNull(ref);
  }

}