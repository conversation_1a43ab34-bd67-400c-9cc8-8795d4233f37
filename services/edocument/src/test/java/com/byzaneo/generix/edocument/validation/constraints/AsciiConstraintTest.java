package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class AsciiConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/AsciiConstraintTest.xml"), false);

  /**
   * Test of isValid method, of class AsciiConstraint.
   */
  @Test
  public void testValid() {
    AsciiConstraint instance = new AsciiConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productValid/test");

    instance.setXQuery(new XQuery());
    instance.setNodes(nodes);

    boolean result = instance.isValid();
    assertTrue(result);
  }

  /**
   * Test of isValid method, of class AsciiConstraint.
   */
  @Test
  public void testInvalid1() {
    AsciiConstraint instance = new AsciiConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid1/test");

    instance.setXQuery(new XQuery());
    instance.setNodes(nodes);

    boolean result = instance.isValid();
    assertFalse(result);
  }

  /**
   * Test of isValid method, of class AsciiConstraint.
   */
  @Test
  public void testInvalid2() {
    AsciiConstraint instance = new AsciiConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid2/test");

    instance.setXQuery(new XQuery());
    instance.setNodes(nodes);

    boolean result = instance.isValid();
    assertFalse(result);
  }

  /**
   * Test of isValid method, of class AsciiConstraint.
   */
  @Test
  public void testInvalid3() {
    AsciiConstraint instance = new AsciiConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid3/test");

    instance.setXQuery(new XQuery());
    instance.setNodes(nodes);

    boolean result = instance.isValid();
    assertFalse(result);
  }

  /**
   * Test of isValid method, of class AsciiConstraint.
   */
  @Test
  public void testInvalid4() {
    AsciiConstraint instance = new AsciiConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid4/test");

    instance.setXQuery(new XQuery());
    instance.setNodes(nodes);

    boolean result = instance.isValid();
    assertFalse(result);
  }

  /**
   * Test of isValid method, of class AsciiConstraint.
   */
  @Test
  public void testInvalid5() {
    AsciiConstraint instance = new AsciiConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid5/test");

    instance.setXQuery(new XQuery());
    instance.setNodes(nodes);

    boolean result = instance.isValid();
    assertFalse(result);
  }

  /**
   * Test of isValid method, of class AsciiConstraint.
   */
  @Test
  public void testInvalid6() {
    AsciiConstraint instance = new AsciiConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//detail/products/productInvalid6/test");

    instance.setXQuery(new XQuery());
    instance.setNodes(nodes);

    boolean result = instance.isValid();
    assertFalse(result);
  }
}
