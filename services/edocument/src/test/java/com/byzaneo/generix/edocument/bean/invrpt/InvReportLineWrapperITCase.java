package com.byzaneo.generix.edocument.bean.invrpt;

import java.math.BigDecimal;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.byzaneo.commons.service.PropertiesException;

import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.mockStatic;

import com.byzaneo.generix.edocument.api.invrpt.InventoryReportLineWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.CsvInvRptLine;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.InventoryReport;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;

/**
 * <AUTHOR>
 */
public class InvReportLineWrapperITCase {

  private InventoryReportLineWrapper instance;

  @BeforeEach
  public void before() {
    instance = new InventoryReportLineWrapper();
    Mockito.spy(CsvImportHelper.class);
  }

  @AfterEach
  public void after() {
    instance = null;
  }

  @Test
  public void testValidateNoHeader() {
    CsvInvRptLine line = new CsvInvRptLine();

    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(null);

      try {
        instance.validate(line, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("header");
        Assertions.assertNotNull(serviceException);
      }
    }
  }

  @Test
  public void testValidateMissingCodeVendeur() {
    CsvInvRptLine line = new CsvInvRptLine();

    InventoryReport inventoryReport = new InventoryReport();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(inventoryReport);

      try {
        instance.validate(line, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("line-sellerPartId");
        Assertions.assertNotNull(serviceException);
      }
    }
  }

  @Test
  public void testValidateMissingCodeEAN() {
    CsvInvRptLine line = new CsvInvRptLine();
    line.setSellerPartID("2");

    InventoryReport inventoryReport = new InventoryReport();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(inventoryReport);

      try {
        instance.validate(line, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("line-productIdentifier");
        Assertions.assertNotNull(serviceException);
      }
    }
  }

  @Test
  public void testValidateMissingDescrpition() {
    CsvInvRptLine line = new CsvInvRptLine();
    line.setSellerPartID("2");
    line.setProductIdentifier("ProductIdent");

    InventoryReport inventoryReport = new InventoryReport();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(inventoryReport);

      try {
        instance.validate(line, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("line-description");
        Assertions.assertNotNull(serviceException);
      }
    }
  }

  @Test
  public void testValidateMissingQuantity() {
    CsvInvRptLine line = new CsvInvRptLine();
    line.setSellerPartID("2");
    line.setProductIdentifier("ProductIdent");
    line.setItemDescription("Description");
    InventoryReport inventoryReport = new InventoryReport();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(inventoryReport);

      try {
        instance.validate(line, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("line-quantity");
        Assertions.assertNotNull(serviceException, "Exception should have a \"line-quantity\" mapping");
      }
    }
  }

  @Test
  public void testValidateMissingProductUnit() {
    CsvInvRptLine line = new CsvInvRptLine();
    line.setSellerPartID("2");
    line.setProductIdentifier("ProductIdent");
    line.setItemDescription("Description");
    line.setQuantity(BigDecimal.ZERO);

    InventoryReport inventoryReport = new InventoryReport();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(inventoryReport);

      try {
        instance.validate(line, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("line-uom");
        Assertions.assertNotNull(serviceException);
      }
    }
  }

  @Test
  public void testValidate() {
    CsvInvRptLine line = new CsvInvRptLine();
    line.setSellerPartID("123456789");
    line.setProductIdentifier("P1");
    line.setItemDescription("Produit1");
    line.setQuantity(BigDecimal.ZERO);
    line.setUom("Kgs");
    InventoryReport inventoryReport = initInventoryHeader();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(inventoryReport);

      try {
        instance.validate(line, null);
        Assertions.assertEquals("123456789", inventoryReport.getListOfInventoryReportDetail()
            .getInventoryReportDetail()
            .get(0)
            .getInventoryItemIdentifiers()
            .getPartNumbers()
            .getSellerPartNumber()
            .getPartID());
        Assertions.assertEquals("EAN2-5-5-1", inventoryReport.getListOfInventoryReportDetail()
            .getInventoryReportDetail()
            .get(0)
            .getInventoryItemIdentifiers()
            .getPartNumbers()
            .getStandardPartNumber()
            .getProductIdentifierQualifierCoded());
        Assertions.assertEquals("P1", inventoryReport.getListOfInventoryReportDetail()
            .getInventoryReportDetail()
            .get(0)
            .getInventoryItemIdentifiers()
            .getPartNumbers()
            .getStandardPartNumber()
            .getProductIdentifier());
        Assertions.assertEquals("Produit1", inventoryReport.getListOfInventoryReportDetail()
            .getInventoryReportDetail()
            .get(0)
            .getInventoryItemIdentifiers()
            .getItemDescription());
        Assertions.assertEquals(BigDecimal.ZERO, inventoryReport.getListOfInventoryReportDetail()
            .getInventoryReportDetail()
            .get(0)
            .getTotalInventoryQuantity()
            .getQuantityValue()
            .getValue());
        Assertions.assertEquals("Other", inventoryReport.getListOfInventoryReportDetail()
            .getInventoryReportDetail()
            .get(0)
            .getTotalInventoryQuantity()
            .getUnitOfMeasurement()
            .getUOMCoded());
        Assertions.assertEquals("Kgs", inventoryReport.getListOfInventoryReportDetail()
            .getInventoryReportDetail()
            .get(0)
            .getTotalInventoryQuantity()
            .getUnitOfMeasurement()
            .getUOMCodedOther());
      }
      catch (Exception e) {
        fail("expected validation exception");
      }
    }
  }

  private InventoryReport initInventoryHeader() {

    InventoryReport inventoryReport = new InventoryReport();
    InventoryReportHeaderType inventoryReportHeader = new InventoryReportHeaderType();

    PurposeType purposeType = new PurposeType();
    purposeType.setPurposeCoded(PurposeCodeType.ORIGINAL);
    inventoryReportHeader.setInventoryReportPurpose(purposeType);

    InventoryReportTypeType inventoryReportType = new InventoryReportTypeType();
    inventoryReportType.setInventoryReportTypeCoded(InventoryReportTypeCodeType.SELLER_INVENTORY_REPORT);
    inventoryReportHeader.setInventoryReportType(inventoryReportType);

    inventoryReport.setInventoryReportHeader(inventoryReportHeader);
    ListOfInventoryReportDetailType initInventoryDetail = new ListOfInventoryReportDetailType();
    inventoryReport.setListOfInventoryReportDetail(initInventoryDetail);

    return inventoryReport;

  }

}
