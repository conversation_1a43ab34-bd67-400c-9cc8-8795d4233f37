package com.byzaneo.generix.edocument.util;

import com.byzaneo.generix.edocument.bean.xcbl.XcblPath;
import com.byzaneo.xtrade.bean.DocumentFile;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Document;

import java.io.File;

import static com.byzaneo.commons.bean.FileType.XCBL;
import static com.byzaneo.commons.bean.FileType.XML;
import static com.byzaneo.commons.util.DomHelper.evaluateString;
import static com.byzaneo.commons.util.DomHelper.parseFile;
import static com.byzaneo.commons.util.FileHelper.getResourceFile;

public class XcblEditionTest {
  private File appRsp;

  private File invoice;

  @BeforeEach
  public void setUp() {
    appRsp = getResourceFile("/data/xml/appRsp-xcblEdition.xml");
    invoice = getResourceFile("/data/xml/invoice-xcblEdition.xml");
  }

  @AfterEach
  public void tearDown() {
  }

  @Test
  public void testCorrectFromXcblPath() {
    String xPath = "/Invoice/InvoiceHeader/InvoiceNumber";
    Document xdoc = parseFile(invoice, false);
    XcblPath xcblPath = new XcblPath("InvoiceNumber", "Need number", xPath, xdoc);
    xcblPath.setValue("3");
    XcblEditionBuilder.builder(xdoc, null, null, invoice.toPath(), invoice.toPath())
        .addPath(xcblPath)
        .build()
        .write();
    Document xdocModify = parseFile(invoice, false);
    String value = evaluateString(xdocModify, xPath);
    Assertions.assertEquals("3", value);
  }

  @Test
  public void testCorrectFromDocument() {
    String xPath = "/Invoice/InvoiceHeader/InvoiceNumber";
    com.byzaneo.xtrade.bean.Document doc = new com.byzaneo.xtrade.bean.Document();
    doc.addFile(new DocumentFile(invoice, XCBL, null, null, doc, null));
    doc.addFile(new DocumentFile(appRsp, XML, null, null, doc, null));
    XcblEditionBuilder.XcblEdition xcblEdition = XcblEditionBuilder.builder(doc, dof -> XCBL.equals(dof.getType()), null)
        .paths(doc, dof -> XML.equals(dof.getType()))
        .build();
    xcblEdition.getPathes()
        .stream()
        .findFirst()
        .ifPresent(path -> path.setValue("Number"));
    xcblEdition.write();
    Document xdocModify = parseFile(xcblEdition.getNewPath()
        .toFile(), false);
    String value = evaluateString(xdocModify, xPath);
    Assertions.assertEquals("Number", value);
  }

}