package com.byzaneo.generix.edocument.bean.invoice;

import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getBuyerOrderNumberFromPurchaseOrderReference;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getEndDateFromInvoiceValidityDatesType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getIdentValueFromContractID;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getPurchaseOrderDateFromPurchaseOrderReference;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getRefDateFromAsnNumber;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getRefDateFromInvoiceNumber;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getRefNumFromAsnNumber;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getRefNumFromInvoiceNumber;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getStartDateFromInvoiceValidityDatesType;
import static org.apache.commons.lang3.time.DateUtils.parseDate;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.generix.edocument.api.invoice.InvoiceHeaderReferenceWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.CsvHeaderReference;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;

public class InvoiceHeaderReferenceWrapperITCase {

  private MockedStatic<CsvImportHelper> mockedCsvImportHelper;

  private InvoiceHeaderReferenceWrapper instance;

  @BeforeEach
  public void before() {
    instance = new InvoiceHeaderReferenceWrapper();
    mockedCsvImportHelper = Mockito.mockStatic(CsvImportHelper.class);
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedCsvImportHelper.closeOnDemand();
  }

  @Test
  public void testValidateNoHeader() {
    CsvHeaderReference reference = new CsvHeaderReference();

    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(null);

    try {
      instance.validate(reference, null);
      fail("expected validation exception");
    }
    catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("header");
      Assertions.assertNotNull(serviceException);
    }
  }

  @Test
  public void testValidateEmptyReferenceDate() {
    CsvHeaderReference reference = new CsvHeaderReference();
    reference.setReferenceType("ON");

    Invoice invoice = new Invoice();
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      instance.validate(reference, null);
      fail("expected validation exception");
    }
    catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("header reference");
      Assertions.assertNotNull(serviceException);
    }
  }

  @Test
  public void testValidateEmptyReferenceDates() {
    CsvHeaderReference reference = new CsvHeaderReference();
    reference.setReferenceType("CT");

    Invoice invoice = new Invoice();
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      instance.validate(reference, null);
      fail("expected validation exception");
    }
    catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("header reference");
      Assertions.assertNotNull(serviceException);
    }
  }

  @Test
  public void testValidateUnknownType() {
    CsvHeaderReference reference = new CsvHeaderReference();
    reference.setReferenceType("ZZ");

    Invoice invoice = new Invoice();
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      reference.setDateReference(parseDate("01/12/2015", "dd/MM/yyyy"));
      instance.validate(reference, null);
      fail("expected validation exception");
    }
    catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("header reference");
      Assertions.assertNotNull(serviceException);
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }

  @Test
  public void testValidateON() {
    CsvHeaderReference reference = new CsvHeaderReference();
    reference.setReferenceType("ON");
    reference.setReferenceNumber("REF_NUM_ON");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      reference.setDateReference(parseDate("01/12/2015", "dd/MM/yyyy"));
      instance.validate(reference, null);

      // Check
      Assertions.assertEquals("REF_NUM_ON", getBuyerOrderNumberFromPurchaseOrderReference(invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)
          .getPurchaseOrderReference()));
      Assertions.assertEquals(parseDate("01/12/2015", "dd/MM/yyyy"), getPurchaseOrderDateFromPurchaseOrderReference(invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)
          .getPurchaseOrderReference()));

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }

  @Test
  public void testValidateDQ() {
    CsvHeaderReference reference = new CsvHeaderReference();
    reference.setReferenceType("DQ");
    reference.setReferenceNumber("REF_NUM_DQ");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      reference.setDateReference(parseDate("13/12/2015", "dd/MM/yyyy"));
      instance.validate(reference, null);

      // Check
      Assertions.assertEquals("REF_NUM_DQ", getRefNumFromAsnNumber(invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)));
      Assertions.assertEquals(parseDate("13/12/2015", "dd/MM/yyyy"), getRefDateFromAsnNumber(invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)));

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }

  @Test
  public void testValidateIV() {
    CsvHeaderReference reference = new CsvHeaderReference();
    reference.setReferenceType("IV");
    reference.setReferenceNumber("REF_NUM_IV");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      reference.setDateReference(parseDate("07/12/2015", "dd/MM/yyyy"));
      instance.validate(reference, null);

      // Check
      RelatedInvoiceRefType relatedInvoice = invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)
          .getListOfRelatedInvoiceRef()
          .getRelatedInvoiceRef()
          .get(0);
      assertEquals("REF_NUM_IV", getRefNumFromInvoiceNumber(relatedInvoice));
      assertEquals(parseDate("07/12/2015", "dd/MM/yyyy"), getRefDateFromInvoiceNumber(relatedInvoice));
      assertEquals(InvoiceTypeCodeType.COMMERCIAL_INVOICE, relatedInvoice.getRelatedInvoiceType()
          .getInvoiceTypeCoded());

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }

  @Test
  public void testValidateDL() {
    CsvHeaderReference reference = new CsvHeaderReference();
    reference.setReferenceType("DL");
    reference.setReferenceNumber("REF_NUM_DL");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      reference.setDateReference(parseDate("24/12/2015", "dd/MM/yyyy"));
      instance.validate(reference, null);

      // Check
      RelatedInvoiceRefType relatedInvoice = invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)
          .getListOfRelatedInvoiceRef()
          .getRelatedInvoiceRef()
          .get(0);
      assertEquals("REF_NUM_DL", getRefNumFromInvoiceNumber(relatedInvoice));
      assertEquals(parseDate("24/12/2015", "dd/MM/yyyy"), getRefDateFromInvoiceNumber(relatedInvoice));
      assertEquals(InvoiceTypeCodeType.DEBIT_NOTE_GOODS_AND_SERVICES, relatedInvoice.getRelatedInvoiceType()
          .getInvoiceTypeCoded());

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }

  @Test
  public void testValidateCT() {
    CsvHeaderReference reference = new CsvHeaderReference();
    reference.setReferenceType("CT");
    reference.setReferenceNumber("REF_NUM_CT");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      reference.setDateReferenceStart(parseDate("15/12/2015", "dd/MM/yyyy"));
      reference.setDateReferenceEnd(parseDate("25/12/2015", "dd/MM/yyyy"));
      instance.validate(reference, null);

      // Check
      Assertions.assertEquals("REF_NUM_CT", getIdentValueFromContractID(invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)));
      Assertions.assertEquals(parseDate("15/12/2015", "dd/MM/yyyy"), getStartDateFromInvoiceValidityDatesType(invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)
          .getContractReference()
          .getValidityDates()));
      Assertions.assertEquals(parseDate("25/12/2015", "dd/MM/yyyy"), getEndDateFromInvoiceValidityDatesType(invoice.getInvoiceHeader()
          .getInvoiceReferences()
          .get(0)
          .getContractReference()
          .getValidityDates()));

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }
}
