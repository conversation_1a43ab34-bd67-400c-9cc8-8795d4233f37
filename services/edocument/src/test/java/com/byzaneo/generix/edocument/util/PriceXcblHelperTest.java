package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.PriceXcblHelper.buildPriceType;
import static com.byzaneo.generix.edocument.util.PriceXcblHelper.getPricetypeCoded;
import static com.byzaneo.generix.edocument.util.PriceXcblHelper.getUnitPriceValue;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_NET;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.TEN;
import static java.util.Optional.empty;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class PriceXcblHelperTest {

  private PriceType priceType;

  @BeforeEach
  public void before() {
    priceType = buildPriceType(CALCULATION_NET, TEN, 3, 0);
  }

  @AfterEach
  public void tearDown() {
    priceType = null;
  }

  @Test
  public void testGetPriceTypeCoded() {
    assertEquals(CALCULATION_NET, getPricetypeCoded(priceType).get());

    priceType = null;
    assertEquals(empty(), getPricetypeCoded(priceType));
  }

  @Test
  public void testGetUnitPriceValue() {
    assertEquals(TEN.setScale(3, 0), getUnitPriceValue(priceType).get());

    priceType = null;
    assertEquals(empty(), getUnitPriceValue(priceType));
  }

  @Test
  public void testSetUnitPriceValue() {
    PriceXcblHelper.setUnitPriceValue(priceType, ONE);
    assertEquals(ONE, getUnitPriceValue(priceType).get());
  }

}
