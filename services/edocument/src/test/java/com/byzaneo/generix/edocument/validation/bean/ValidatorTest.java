package com.byzaneo.generix.edocument.validation.bean;

import static com.byzaneo.commons.util.DomHelper.parseFile;
import static com.byzaneo.generix.edocument.validation.bean.ConstraintType.FR;

import java.io.File;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.util.Assert;
import org.w3c.dom.Node;

import com.byzaneo.generix.edocument.validation.ConstraintsParser;
import com.byzaneo.generix.edocument.validation.constraints.AbstractConstraint;

/**
 * <AUTHOR>
 */
public class ValidatorTest {

  private final static String XCBL_PATH = "src/test/resources/xcbl/InvoiceExample.xml";

  private final Validator instance = ConstraintsParser.getInstance()
      .loadValidator(FR);

  /**
   * Test of collectLabels method, of class Validator.
   */
  @Test
  public void testCollectLabels() {
    List<String> result = instance.collectLabels();
    Assert.notEmpty(result);
  }

  /**
   * Test of collectApplicableConstraints method, of class Validator.
   */
  @Test
  public void testCollectApplicableConstraints() {
    Node document = parseFile(new File(XCBL_PATH), false);
    instance.collectApplicableConstraints(document);
    Assert.notEmpty((List<AbstractConstraint>) instance.getConstraints());
  }

}
