package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class DecimalConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/InvoiceForDecimalConstraintTest.xml"),
      false);

  @Test
  public void testValid() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/number");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testIsNotValid1() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid2() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber2");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid3() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber3");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid4() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber4");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid5() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber5");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid6() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber6");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid7() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber7");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid8() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber8");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid9() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber9");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid10() {
    DecimalConstraint instance = new DecimalConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber10");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2"));
    boolean result = instance.isValid();
    assertFalse(result);
  }
}
