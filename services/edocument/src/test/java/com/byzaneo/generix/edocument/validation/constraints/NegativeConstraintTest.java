package com.byzaneo.generix.edocument.validation.constraints;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;

public class NegativeConstraintTest {
  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/NegativeConstraintTestFile.xml"), false);

  @Test
  public void testEmptyNode() {
    NegativeConstraint instance = initializeInstance("root/empty");
    assertTrue(instance.isValid());
  }

  @Test
  public void testZeroValue() {
    NegativeConstraint instance = initializeInstance("root/zero");
    assertTrue(instance.isValid());

  }

  @Test
  public void testNotANumber() {
    NegativeConstraint instance = initializeInstance("root/notDecimal");
    assertFalse(instance.isValid());

  }

  @Test
  public void testNegativeNumber() {
    NegativeConstraint instance = initializeInstance("root/negative");
    assertFalse(instance.isValid());
  }

  @Test
  public void testNegativeDecimalNumber() {
    NegativeConstraint instance = initializeInstance("root/negativeDecimal");
    assertFalse(instance.isValid());
  }

  @Test
  public void testPositiveNumber() {
    NegativeConstraint instance = initializeInstance("root/positive");
    assertTrue(instance.isValid());

  }

  @Test
  public void testPositiveDecimalNumber() {
    NegativeConstraint instance = initializeInstance("root/positiveDecimal");
    assertTrue(instance.isValid());
  }

  @Test
  public void testNumberAndString() {
    NegativeConstraint instance = initializeInstance("root/numberAndString");
    assertFalse(instance.isValid());
  }

  private NegativeConstraint initializeInstance(String xpath) {
    NegativeConstraint instance = new NegativeConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, xpath);
    instance.setNodes(nodes);
    return instance;
  }
}
