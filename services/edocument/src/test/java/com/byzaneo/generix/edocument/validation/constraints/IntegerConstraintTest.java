package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class IntegerConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/InvoiceForIntegerConstraintTest.xml"),
      false);

  @Test
  public void testValid() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/number");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testIsNotValid1() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber1");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid2() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber2");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid3() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber3");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid4() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber4");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid5() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber5");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid6() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber6");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid7() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber7");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testIsNotValid8() {
    IntegerConstraint instance = new IntegerConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber8");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }
}