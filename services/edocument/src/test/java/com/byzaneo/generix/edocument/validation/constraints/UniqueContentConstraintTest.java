package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class UniqueContentConstraintTest {
  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/InvoiceForUniqueConstraintValidation.xml"),
      false);

  @Test
  public void testValid1() {
    UniqueContentConstraint instance = new UniqueContentConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//Invoice/InvoiceHeader/ListOfAttachment/Attachment/MIMEType");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid2() {
    UniqueContentConstraint instance = new UniqueContentConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//Invoice/InvoiceHeader/ListOfAttachment/Attachment/Test");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid3() {
    UniqueContentConstraint instance = new UniqueContentConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//Invoice/InvoiceHeader/ListOfAttachment/Attachment/AttachmentTitle");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testNotValid() {
    UniqueContentConstraint instance = new UniqueContentConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//Invoice/InvoiceHeader/ListOfAttachment/Attachment/FileName");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }
}
