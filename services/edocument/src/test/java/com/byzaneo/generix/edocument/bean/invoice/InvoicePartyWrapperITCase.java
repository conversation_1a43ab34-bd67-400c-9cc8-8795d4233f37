package com.byzaneo.generix.edocument.bean.invoice;

import static com.byzaneo.generix.edocument.util.CsvImportHelper.SIREN;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getCityValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getIdentValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getName1Value;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getPostalCodeValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getStreetValue;
import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.mockStatic;

import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.ui.MissingKeyHandler;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.edocument.api.invoice.InvoicePartyWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.CsvParty;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.util.InvoiceHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceOCRPartyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoicePartyCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoicePartyType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class InvoicePartyWrapperITCase {

  private MockedStatic<JSFHelper> mockedJSFHelper;

  private InvoicePartyWrapper instance;

  @BeforeEach
  public void before() {
    instance = new InvoicePartyWrapper();
    Mockito.spy(CsvImportHelper.class);
    setUpStaticMocks();
  }

  void setUpStaticMocks() {
    mockedJSFHelper = Mockito.mockStatic(JSFHelper.class, invocation -> {
      if (invocation.getMethod()
          .getName()
          .equals("getMissingKeyHandler")) {
        return Mockito.mock(MissingKeyHandler.class);
      }
      return invocation.callRealMethod();
    });
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedJSFHelper.closeOnDemand();
  }

  @Test
  public void testValidateNoHeader() {
    CsvParty party = new CsvParty();

    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable())
          .thenReturn(null);

      try {
        instance.validate(party, null);
        fail("expected validation exception");
      } catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
                .get("header");
        Assertions.assertEquals("Aucune entête liée à cet élément", serviceException.getMessage());
      }
    }
  }

  @Test
  public void testValidateUnknownType() {
    CsvParty party = new CsvParty();
    party.setType("ZZ");
    party.setSiren("SIR");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable())
              .thenReturn(invoice);

      try {
        instance.validate(party, null);
        fail("expected validation exception");
      } catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
                .get("party");
        assertTrue(asList(
                "Type d'intervenant inconnu : ZZ",
                "Unknown party type : ZZ")
                .contains(serviceException.getMessage()));
      }
    }
  }

  @Test
  public void testValidateMissingIdFields() {
    CsvParty party = new CsvParty();
    party.setType("CO");

    Invoice invoice = new Invoice();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable())
              .thenReturn(invoice);

      try {
        instance.validate(party, null);
        fail("expected validation exception");
      } catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
                .get("party");
        assertTrue(asList(
                "Champs obligatoires manquants pour l'intervenant CO",
                "Missing party mandatory fields for party CO")
                .contains(serviceException.getMessage()));
      }
    }
  }

  @Test
  public void testValidateCO() {
    CsvParty party = new CsvParty();
    party.setType("CO");
    party.setSiren("SIREN_CO");
    party.setName("NOM");
    party.setAddress("ADRESSE");
    party.setCity("VILLE");
    party.setZipCode("CODE POSTAL");
    party.setCountry("AS");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
            .setInvoiceParty(new InvoicePartyType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
            invocation -> {
              if (invocation.getMethod().getName().equals("getImportedIndexable")) {
                return invoice;
              }
              return invocation.callRealMethod();
            })) {

      try {
        instance.validate(party, null);

        // Check

        InvoicePartyCodedType partyCoded = invoice.getInvoiceHeader()
                .getInvoiceParty()
                .getListOfPartyCoded()
                .getPartyCoded()
                .get(0);
        assertEquals("HeadOffice", partyCoded.getPartyRoleCoded());
        assertEquals("NOM", getName1Value(partyCoded.getNameAddress()));
        assertEquals("ADRESSE", getStreetValue(partyCoded.getNameAddress()));
        assertEquals("CODE POSTAL", getPostalCodeValue(partyCoded.getNameAddress()));
        assertEquals("VILLE", InvoiceHelper.getCityValue(partyCoded.getNameAddress()));
        assertEquals(CountryCodeType.AS, partyCoded.getNameAddress()
                .getCountry()
                .getCountryCoded());
        assertEquals("SIREN_CO", getIdentValue(partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)));
        assertEquals("FR-INSEE", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyCoded());
        assertEquals("Other", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getCodeListIdentifierCoded());
        assertEquals(SIREN, partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getCodeListIdentifierCodedOther());
        assertEquals("French Trade and Companies Register", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyDescription());

      } catch (PropertiesException e) {
        fail("no validation exception expected");
      } catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }

  @Test
  public void testValidateLC() {
    CsvParty party = new CsvParty();
    party.setType("LC");
    party.setSiren("SIREN_LC");
    party.setName("NOM");
    party.setAddress("ADRESSE");
    party.setCity("VILLE");
    party.setZipCode("CODE POSTAL");
    party.setCountry("AS");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
            .setInvoiceParty(new InvoicePartyType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
            invocation -> {
              if (invocation.getMethod().getName().equals("getImportedIndexable")) {
                return invoice;
              }
              return invocation.callRealMethod();
            })) {

      try {
        instance.validate(party, null);

        // Check

        InvoicePartyCodedType partyCoded = invoice.getInvoiceHeader()
                .getInvoiceParty()
                .getListOfPartyCoded()
                .getPartyCoded()
                .get(0);
        assertEquals("DeclarantsAgentOrRepresentative", partyCoded.getPartyRoleCoded());
        assertEquals("NOM", getName1Value(partyCoded.getNameAddress()));
        assertEquals("ADRESSE", getStreetValue(partyCoded.getNameAddress()));
        assertEquals("CODE POSTAL", getPostalCodeValue(partyCoded.getNameAddress()));
        assertEquals("VILLE", getCityValue(partyCoded.getNameAddress()));
        assertEquals(CountryCodeType.AS, partyCoded.getNameAddress()
                .getCountry()
                .getCountryCoded());
        assertEquals("SIREN_LC", getIdentValue(partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)));
        assertEquals("FR-INSEE", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyCoded());
        assertEquals("Other", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getCodeListIdentifierCoded());
        assertEquals(SIREN, partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getCodeListIdentifierCodedOther());
        assertEquals("French Trade and Companies Register", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyDescription());

      } catch (PropertiesException e) {
        fail("no validation exception expected");
      } catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }

  @Test
  public void testValidateDL() {
    CsvParty party = new CsvParty();
    party.setType("DL");
    party.setRegisterNumber("REGISTER_DL");
    party.setName("NOM");
    party.setAddress("ADRESSE");
    party.setCity("VILLE");
    party.setZipCode("CODE POSTAL");
    party.setCountry("AS");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
            .setInvoiceParty(new InvoicePartyType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
            invocation -> {
              if (invocation.getMethod().getName().equals("getImportedIndexable")) {
                return invoice;
              }
              return invocation.callRealMethod();
            })) {

      try {
        instance.validate(party, null);

        // Check
        InvoicePartyCodedType partyCoded = invoice.getInvoiceHeader()
                .getInvoiceParty()
                .getListOfPartyCoded()
                .getPartyCoded()
                .get(0);
        assertEquals("Factor", partyCoded.getPartyRoleCoded());
        assertEquals("NOM", getName1Value(partyCoded.getNameAddress()));
        assertEquals("ADRESSE", getStreetValue(partyCoded.getNameAddress()));
        assertEquals("CODE POSTAL", getPostalCodeValue(partyCoded.getNameAddress()));
        assertEquals("VILLE", getCityValue(partyCoded.getNameAddress()));
        assertEquals(CountryCodeType.AS, partyCoded.getNameAddress()
                .getCountry()
                .getCountryCoded());
        assertEquals("REGISTER_DL", getIdentValue(partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)));
        assertEquals("Other", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyCoded());
        assertEquals("RCS-RCM", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyCodedOther());
        assertEquals("French Trade and Companies Register", partyCoded.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyDescription());

      } catch (PropertiesException e) {
        fail("no validation exception expected");
      } catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }

  @Test
  public void testValidateRE() {
    CsvParty party = new CsvParty();
    party.setType("RE");
    party.setRegisterNumber("REGISTER_RE");
    party.setName("NOM");
    party.setAddress("ADRESSE");
    party.setCity("VILLE");
    party.setZipCode("CODE POSTAL");
    party.setCountry("ZZ");
    party.setEanCode("EAN_CODE");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
            .setInvoiceParty(new InvoicePartyType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
            invocation -> {
              if (invocation.getMethod().getName().equals("getImportedIndexable")) {
                return invoice;
              }
              return invocation.callRealMethod();
            })) {

      try {
        instance.validate(party, null);
        // Check

        InvoiceOCRPartyType partyType = invoice.getInvoiceHeader()
                .getInvoiceParty()
                .getRemitToParty();
        assertEquals("NOM", getName1Value(partyType.getNameAddress()));
        assertEquals("ADRESSE", getStreetValue(partyType.getNameAddress()));
        assertEquals("CODE POSTAL", InvoiceHelper.getPostalCodeValue(partyType.getNameAddress()));
        assertEquals("VILLE", InvoiceHelper.getCityValue(partyType.getNameAddress()));
        assertEquals(CountryCodeType.OTHER, partyType.getNameAddress()
                .getCountry()
                .getCountryCoded());
        assertEquals("ZZ", partyType.getNameAddress()
                .getCountry()
                .getCountryCodedOther());
        assertEquals("REGISTER_RE", getIdentValue(partyType.getListOfIdentifier()
                .getIdentifier()
                .get(0)));
        assertEquals("Other", partyType.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyCoded());
        assertEquals("RCS-RCM", partyType.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyCodedOther());
        assertEquals("French Trade and Companies Register", partyType.getListOfIdentifier()
                .getIdentifier()
                .get(0)
                .getAgency()
                .getAgencyDescription());
        assertEquals("EAN", partyType.getPartyID()
                .getAgency()
                .getAgencyCoded());
        assertEquals("LocationCode", partyType.getPartyID()
                .getAgency()
                .getCodeListIdentifierCoded());
        assertEquals("EAN_CODE", getIdentValue(partyType.getPartyID()));

      } catch (PropertiesException e) {
        fail("no validation exception expected");
      } catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }

  @Test
  public void testValidateDP() {
    CsvParty party = new CsvParty();
    party.setType("DP");
    party.setVatNumber("TVA_DP");
    party.setName("NOM");
    party.setAddress("ADRESSE");
    party.setCity("VILLE");
    party.setZipCode("CODE POSTAL");
    party.setCountry("ZZ");
    party.setEanCode("EAN_CODE");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
            .setInvoiceParty(new InvoicePartyType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
            invocation -> {
              if (invocation.getMethod().getName().equals("getImportedIndexable")) {
                return invoice;
              }
              return invocation.callRealMethod();
            })) {

      try {
        instance.validate(party, null);
        // Check
        InvoiceOCRPartyType partyType = invoice.getInvoiceHeader()
                .getInvoiceParty()
                .getShipToParty();
        assertEquals("NOM", getName1Value(partyType.getNameAddress()));
        assertEquals("ADRESSE", getStreetValue(partyType.getNameAddress()));
        assertEquals("CODE POSTAL", getPostalCodeValue(partyType.getNameAddress()));
        assertEquals("VILLE", InvoiceHelper.getCityValue(partyType.getNameAddress()));
        assertEquals(CountryCodeType.OTHER, partyType.getNameAddress()
                .getCountry()
                .getCountryCoded());
        assertEquals("ZZ", partyType.getNameAddress()
                .getCountry()
                .getCountryCodedOther());
        assertEquals("TVA_DP", getIdentValue(partyType.getPartyTaxInformation()
                .getTaxIdentifier()));
        assertEquals("CEC", partyType.getPartyTaxInformation()
                .getTaxIdentifier()
                .getAgency()
                .getAgencyCoded());
        assertEquals("ValueAddedTaxIdentification", partyType.getPartyTaxInformation()
                .getTaxIdentifier()
                .getAgency()
                .getCodeListIdentifierCoded());
        assertEquals("EAN", partyType.getPartyID()
                .getAgency()
                .getAgencyCoded());
        assertEquals("LocationCode", partyType.getPartyID()
                .getAgency()
                .getCodeListIdentifierCoded());
        assertEquals("EAN_CODE", partyType.getPartyID()
                .getIdent()
                .getValue());

      } catch (PropertiesException e) {
        fail("no validation exception expected");
      } catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }

  @Test
  public void testValidateSF() {
    CsvParty party = new CsvParty();
    party.setType("SF");
    party.setVatNumber("TVA_SF");
    party.setName("NOM");
    party.setAddress("ADRESSE");
    party.setCity("VILLE");
    party.setZipCode("CODE POSTAL");
    party.setCountry("ZZ");
    party.setEanCode("EAN_CODE");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
            .setInvoiceParty(new InvoicePartyType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
            invocation -> {
              if (invocation.getMethod().getName().equals("getImportedIndexable")) {
                return invoice;
              }
              return invocation.callRealMethod();
            })) {

      try {
        instance.validate(party, null);
        // Check
        InvoiceOCRPartyType partyType = invoice.getInvoiceHeader()
                .getInvoiceParty()
                .getShipFromParty();
        assertEquals("NOM", getName1Value(partyType.getNameAddress()));
        assertEquals("ADRESSE", getStreetValue(partyType.getNameAddress()));
        assertEquals("CODE POSTAL", getPostalCodeValue(partyType.getNameAddress()));
        assertEquals("VILLE", getCityValue(partyType.getNameAddress()));
        assertEquals(CountryCodeType.OTHER, partyType.getNameAddress()
                .getCountry()
                .getCountryCoded());
        assertEquals("ZZ", partyType.getNameAddress()
                .getCountry()
                .getCountryCodedOther());
        assertEquals("TVA_SF", getIdentValue(partyType.getPartyTaxInformation()
                .getTaxIdentifier()));
        assertEquals("CEC", partyType.getPartyTaxInformation()
                .getTaxIdentifier()
                .getAgency()
                .getAgencyCoded());
        assertEquals("ValueAddedTaxIdentification", partyType.getPartyTaxInformation()
                .getTaxIdentifier()
                .getAgency()
                .getCodeListIdentifierCoded());
        assertEquals("EAN", partyType.getPartyID()
                .getAgency()
                .getAgencyCoded());
        assertEquals("LocationCode", partyType.getPartyID()
                .getAgency()
                .getCodeListIdentifierCoded());
        assertEquals("EAN_CODE", getIdentValue(partyType.getPartyID()));

      } catch (PropertiesException e) {
        fail("no validation exception expected");
      } catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }
}
