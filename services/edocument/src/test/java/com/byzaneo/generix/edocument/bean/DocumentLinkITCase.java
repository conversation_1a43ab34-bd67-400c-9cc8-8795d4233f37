package com.byzaneo.generix.edocument.bean;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Dec 11, 2015
 * @since GNX-3124
 */
public class DocumentLinkITCase {

  @Test
  public final void testEquals() {
    assertTrue(new DocumentLink("2L", "3L").equals(new DocumentLink("3L", "2L")));
    assertTrue(new DocumentLink("2L", "3L").equals(new DocumentLink("2L", "3L")));
    assertFalse(new DocumentLink("2L", "3L").equals(new DocumentLink("2L", "1L")));
    assertFalse(new DocumentLink("2L", "3L").equals(new DocumentLink("1L", "3L")));
  }

  @Test
  public final void testHashCode() {
    assertTrue(new DocumentLink("2L", "3L").hashCode() == new DocumentLink("2L", "3L").hashCode());
    assertTrue(new DocumentLink("2L", "3L").hashCode() == new DocumentLink("3L", "2L").hashCode());
    assertFalse(new DocumentLink("2L", "3L").hashCode() == new DocumentLink("2L", "1L").hashCode());
    assertFalse(new DocumentLink("2L", "3L").hashCode() == new DocumentLink("1L", "4L").hashCode());
  }

}
