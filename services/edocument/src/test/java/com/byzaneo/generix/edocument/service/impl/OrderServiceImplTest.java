package com.byzaneo.generix.edocument.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.fail;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.util.OrderHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.*;

public class OrderServiceImplTest {

  @InjectMocks
  private OrderServiceImpl orderService;

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void updateOrderDocument_null_null_throwServiceException() {
    try {
      orderService.updateOrderDocument(null, null);
      fail("ServiceException was expected");
    }
    catch (ServiceException e) {
      // expected
    }
  }

  @Test
  public void updateOrderDocument_regular_CLOSED() {
    // fixture
    Document desadv = new Document();
    Document orderDoc = new Document();
    Order order = new Order();
    OrderDetailType orderDetail = new OrderDetailType();
    ListOfItemDetailType listOfItemDetail = new ListOfItemDetailType();
    ItemDetailType itemDetail = new ItemDetailType();
    ListOfNameValueSetType listOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType valueSet = OrderHelper.getOrCreateNameValueSetType(listOfNameValueSet, OrderHelper.LINE_STATUS);
    NameValuePairType valuePair = OrderHelper.getOrCreateNameValuePairType(valueSet, OrderHelper.STATUS_KEY);
    valuePair.setValue(OrderLineStatus.CLOSED.getValue());

    orderDoc.setIndexValue(order);
    order.setOrderDetail(orderDetail);
    orderDetail.setListOfItemDetail(listOfItemDetail);
    listOfItemDetail.getItemDetail()
        .add(itemDetail);
    itemDetail.setListOfNameValueSet(listOfNameValueSet);
    listOfNameValueSet.getNameValueSet()
        .add(valueSet);
    valueSet.getListOfNameValuePair()
        .getNameValuePair()
        .add(valuePair);

    // action
    orderService.updateOrderDocument(orderDoc, desadv);

    assertThat(orderDoc.getStatusAsEnumValue()).isSameAs(DocumentStatus.SENT);
  }

  @Test
  public void updateOrderDocument_regular_CONFIRMED() {
    // fixture
    Document desadv = new Document();
    Document orderDoc = new Document();
    Order order = new Order();
    OrderDetailType orderDetail = new OrderDetailType();
    ListOfItemDetailType listOfItemDetail = new ListOfItemDetailType();
    ItemDetailType itemDetail = new ItemDetailType();
    ListOfNameValueSetType listOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType valueSet = OrderHelper.getOrCreateNameValueSetType(listOfNameValueSet, OrderHelper.LINE_STATUS);
    NameValuePairType valuePair = OrderHelper.getOrCreateNameValuePairType(valueSet, OrderHelper.STATUS_KEY);
    valuePair.setValue(OrderLineStatus.CONFIRMED.getValue());

    orderDoc.setIndexValue(order);
    order.setOrderDetail(orderDetail);
    orderDetail.setListOfItemDetail(listOfItemDetail);
    listOfItemDetail.getItemDetail()
        .add(itemDetail);
    itemDetail.setListOfNameValueSet(listOfNameValueSet);
    listOfNameValueSet.getNameValueSet()
        .add(valueSet);
    valueSet.getListOfNameValuePair()
        .getNameValuePair()
        .add(valuePair);

    // action
    orderService.updateOrderDocument(orderDoc, desadv);

    assertThat(orderDoc.getStatusAsEnumValue()).isSameAs(DocumentStatus.SENT);
  }

  @Test
  public void updateOrderDocument_regular_PARTIAL() {
    // fixture
    Document desadv = new Document();
    Document orderDoc = new Document();
    Order order = new Order();
    OrderDetailType orderDetail = new OrderDetailType();
    ListOfItemDetailType listOfItemDetail = new ListOfItemDetailType();
    ItemDetailType itemDetail = new ItemDetailType();
    ListOfNameValueSetType listOfNameValueSet = new ListOfNameValueSetType();
    NameValueSetType valueSet = OrderHelper.getOrCreateNameValueSetType(listOfNameValueSet, OrderHelper.LINE_STATUS);
    NameValuePairType valuePair = OrderHelper.getOrCreateNameValuePairType(valueSet, OrderHelper.STATUS_KEY);
    valuePair.setValue(OrderLineStatus.PARTIALLY_CONFIRMED.getValue());

    orderDoc.setIndexValue(order);
    order.setOrderDetail(orderDetail);
    orderDetail.setListOfItemDetail(listOfItemDetail);
    listOfItemDetail.getItemDetail()
        .add(itemDetail);
    itemDetail.setListOfNameValueSet(listOfNameValueSet);
    listOfNameValueSet.getNameValueSet()
        .add(valueSet);
    valueSet.getListOfNameValuePair()
        .getNameValuePair()
        .add(valuePair);

    // action
    orderService.updateOrderDocument(orderDoc, desadv);

    assertThat(orderDoc.getStatusAsEnumValue()).isSameAs(DocumentStatus.SENT_PARTIALLY);
  }

}
