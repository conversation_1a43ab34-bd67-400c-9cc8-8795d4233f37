package com.byzaneo.generix.edocument.validation.constraints;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import java.io.File;
import java.util.List;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

/**
 * Tests the OneInListContraint
 */
public class OneInListConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(
      new File("./src/test/resources/xcbl/OneInListConstraintTest.xml"));
  private static final String XPATH = "//Invoice/InvoiceHeader/InvoiceParty/SellerParty/ListOfIdentifier/Identifier/Agency/CodeListIdentifierCodedOther";

  //@formatter:off
  /**
   * Test OK:
   * Params sent: "2002", "4000", "5000"
   * Values at XPATH: "2000", "2001", "2002", "2003", "2004", "2005"
   * Exactly 1 match: "2002"
   */
  //@formatter:on
  @Test
  public void testValid() {
    OneInListConstraint instance = new OneInListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = DomHelper.evaluateNodeList(XCBL_ROOT, XPATH);
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2002", "4000", "5000"));
    boolean result = instance.isValid();
    assertTrue(result, "There should be exactly one value that matches xml/params");
  }

  //@formatter:off
  /**
   * Test NOK:
   * Params sent: no params sent
   * Values at XPATH: "2000", "2001", "2002", "2003", "2004", "2005"
   * No match, no params sent
   */
  //@formatter:on
  @Test
  public void testInvalidNoParams() {
    OneInListConstraint instance = new OneInListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = DomHelper.evaluateNodeList(XCBL_ROOT, XPATH);
    instance.setNodes(nodes);
    instance.setParams(Arrays.array());
    boolean result = instance.isValid();
    Assertions.assertFalse(result, "Without params, the result should be false");
  }

  //@formatter:off
  /**
   * Test NOK:
   * Params sent: "3000", "4000", "5000"
   * Values at XPATH: "2000", "2001", "2002", "2003", "2004", "2005"
   * Zero matches
   */
  //@formatter:on
  @Test
  public void testInvalidZeroMatches() {
    OneInListConstraint instance = new OneInListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = DomHelper.evaluateNodeList(XCBL_ROOT, XPATH);
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("3000", "4000", "5000"));
    boolean result = instance.isValid();
    Assertions.assertFalse(result, "Zero matches, the result should be false");
  }

  //@formatter:off
  /**
   * Test NOK:
   * Params sent: "2002", "2003", "5000"
   * Values at XPATH: "2000", "2001", "2002", "2003", "2004", "2005"
   * Two matches
   */
  //@formatter:on
  @Test
  public void testInvalidTwoMatches() {
    OneInListConstraint instance = new OneInListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = DomHelper.evaluateNodeList(XCBL_ROOT, XPATH);
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("2002", "2003", "5000"));
    boolean result = instance.isValid();
    Assertions.assertFalse(result, "Two matches, the result should be false");
  }

  //@formatter:off
  /**
   * Test OK:
   * Only one param, that means it is a file; it contains: "2002", "4000", "5000"
   * Values at XPATH: "2000", "2001", "2002", "2003", "2004", "2005"
   * Exactly 1 match: "2002"
   */
  //@formatter:on
  @Test
  public void testValidUsingFile() {
    OneInListConstraint instance = new OneInListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = DomHelper.evaluateNodeList(XCBL_ROOT, XPATH);
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("tests" + File.separator + "one_in_list_constraints_test1"));
    boolean result = instance.isValid();
    Assertions.assertTrue(result, "There should be exactly one value that matches xml/params");
  }

  //@formatter:off
  /**
   * Test NOK:
   * Only one param, that means it is a file; it contains:  "3000", "4000", "5000"
   * Values at XPATH: "2000", "2001", "2002", "2003", "2004", "2005"
   * Zero matches
   */
  //@formatter:on
  @Test
  public void testInvalidZeroMatchesFromFile() {
    OneInListConstraint instance = new OneInListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = DomHelper.evaluateNodeList(XCBL_ROOT, XPATH);
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("tests" + File.separator + "one_in_list_constraints_test2"));
    boolean result = instance.isValid();
    Assertions.assertFalse(result, "Zero matches, the result should be false");
  }

  //@formatter:off
  /**
   * Test NOK:
   * Only one param, that means it is a file; it contains: "2002", "2003", "5000"
   * Values at XPATH: "2000", "2001", "2002", "2003", "2004", "2005"
   * Two matches
   */
  //@formatter:on
  @Test
  public void testInvalidTwoMatchesFromFile() {
    OneInListConstraint instance = new OneInListConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = DomHelper.evaluateNodeList(XCBL_ROOT, XPATH);
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("tests" + File.separator + "one_in_list_constraints_test3"));
    boolean result = instance.isValid();
    Assertions.assertFalse(result, "Two matches, the result should be false");
  }

}
