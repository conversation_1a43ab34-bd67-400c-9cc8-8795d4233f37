package com.byzaneo.generix.edocument.test;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.bean.LabelSet;
import com.byzaneo.commons.bean.PropertyDescriptor;
import com.byzaneo.commons.util.FileHelper;
import com.byzaneo.commons.util.I18NHelper;
import com.byzaneo.commons.util.SpringContextHelper;
import com.byzaneo.generix.api.DocumentCompoundType;
import com.byzaneo.generix.edocument.EDocumentProcessException;
import com.byzaneo.generix.edocument.bean.RecapListItem;
import com.byzaneo.generix.edocument.service.EDocumentService;
import com.byzaneo.generix.edocument.util.EDocumentErrorHelper;
import com.byzaneo.generix.edocument.util.EDocumentErrors;
import com.byzaneo.generix.edocument.util.RecapListHelper;
import com.byzaneo.generix.util.DocumentCompoundTypeHelper;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Organization;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.api.DocumentStage;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.ibm.icu.util.Calendar;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.impl.SchemaOperationsProcessEngineBuild;
import org.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.activiti.engine.impl.context.Context;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.interceptor.CommandContextFactory;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.tools.ant.util.FileUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

import static com.byzaneo.commons.bean.FileType.EDIUNIT;
import static com.byzaneo.commons.bean.FileType.XCBL;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.edocument.EDocumentProcessException.Code.error_xcbl_file;
import static com.byzaneo.generix.edocument.EDocumentProcessException.Code.null_message;
import static com.byzaneo.generix.edocument.util.CsvExportHelper.exportAsCSV;
import static com.byzaneo.generix.edocument.util.EDocumentErrorHelper.addErrorCode;
import static com.byzaneo.generix.edocument.util.EDocumentErrorHelper.addErrorLabel;
import static com.byzaneo.generix.edocument.util.RecapListHelper.*;
import static com.byzaneo.xtrade.api.DocumentStage.UNDEFINED;
import static com.byzaneo.xtrade.api.DocumentStatus.NONE;
import static org.apache.commons.collections4.CollectionUtils.size;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Jan 2, 2015
 * @since 1.0
 */
@ExtendWith(MockitoExtension.class)
public class RecapListHelperTest {

  private static final int ITEM_COUNT = 10;

  private MockedStatic<EDocumentErrorHelper> mockedEDocumentErrorHelper;
  private MockedStatic<SpringContextHelper> mockedSpringContextHelper;
  private ProcessEngineConfigurationImpl bankAccountService;
  File csvFile = new File(org.apache.commons.io.FileUtils.getTempDirectory(), "recap-list.csv");

  @BeforeEach
  void setUpStaticMocks() {
    mockedEDocumentErrorHelper = Mockito.mockStatic(EDocumentErrorHelper.class);
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedEDocumentErrorHelper.closeOnDemand();
  }

  @Test
  public void testExportAsCsv() {

    try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
      List<String> fieldNames = Arrays.asList("creationDate", "documentPreparationDate", "documentDate");
      List<PropertyDescriptor> pDescList = new ArrayList<>();
      List<PropertyDescriptor> finalPDescList = pDescList;
      fieldNames.forEach(fieldName -> finalPDescList.add(new PropertyDescriptor(fieldName)));
      // System.out.println(new String(out.toByteArray()));
      BeanDescriptor bd = new BeanDescriptor("beanDescriptor");
      FileOutputStream fos = new FileOutputStream(csvFile);
      List<Object> items = new ArrayList<>();
      List<String> fieldNames1 = Arrays.asList("creationDate", "qty", "buyerCountry", "docStatus", "docStage", "archiveStatus", "dematPartner",
              "messageType", "gnxTypeConverter", "documentTypeConverter");
      pDescList = new ArrayList<>();
      List<PropertyDescriptor> finalPDescList1 = pDescList;
      fieldNames.forEach(fieldName1 -> finalPDescList1.add(new PropertyDescriptor(fieldName1)));
      pDescList.forEach(propertyDescriptor -> bd.addProperty(propertyDescriptor));

      exportAsCSV(items, bd, fos, Locale.ENGLISH, null);
    }
    catch (IOException e) {
      fail(e.getMessage());
    }
  }

  /* -- DATA -- */

  public static final RecapListItem createTestItem(int i) {
    // Building RecapList item
    RecapListItem item = new RecapListItem();
    // Retrieving sender informations
    item.setLegalEntityCode("LegalEntityCode" + i);
    item.setLegalEntityName("LegalEntityName" + i);
    item.setLegalEntityReference("LegalEntityReference" + i);
    item.setLegalEntitySiren("LegalEntitySiren" + i);
    item.setLegalEntityVATNumber("LegalEntityVATNumber" + i);
    item.setLegalEntityAddress("LegalEntityAddress" + i);
    item.setLegalEntityPostalCode("LegalEntityPostalCode" + i);
    item.setLegalEntityCity("LegalEntityCity" + i);
    item.setLegalEntityCountry("LegalEntityCountry" + i);

    // Retrieving receiver informations
    item.setDematPartnerCode("DematPartnerCode" + i);
    item.setDematPartnerName("DematPartnerName" + i);
    item.setDematPartnerReference("DematPartnerReference" + i);
    item.setDematPartnerSiren("DematPartnerSiren" + i);
    item.setDematPartnerVATNumber("DematPartnerVATNumber" + i);
    item.setDematPartnerAddress("DematPartnerAddress" + i);
    item.setDematPartnerPostalCode("DematPartnerPostalCode" + i);
    item.setDematPartnerCity("DematPartnerCity" + i);
    item.setDematPartnerCountry("DematPartnerCountry" + i);

    // Retrieving software version
    item.setSoftwareVersion("SoftwareVersion" + i);
    item.setMonetaryAmount(19.6);

    // Retrieving message informations
    item.setMessageUri("MessageUri" + i);
    item.setMessageType("MessageType" + i);

    // Retrieving informations from XCBL document
    item.setDocumentNumber("DocumentNumber" + i);
    item.setDocumentDate(new Date());
    item.setDocumentPreparationDate(new Date());
    item.setTaxableAmount(100.0);
    item.setAtiAmount(10.0);
    item.setCurrencyCode("EUR");

    return item;
  }

  @Test
  public void testPreCheckOnMessage() {
    Document doc;
    // check 1 XCBL file
    try {
      doc = createDocument(true, false);
      preCheckOnMessage(doc);
      fail();
    }
    catch (EDocumentProcessException e) {
      assertTrue(e.getCode() == error_xcbl_file);
    }

    try {
      doc = createDocument(true, true);
      DocumentFile dof1 = new DocumentFile(new File("xcbl_file2"), XCBL, "action", "processInstanceId", doc, "comment");
      dof1.setDescription(EDocumentService.OFFICIAL_INDEX);
      doc.addFile(dof1);
      preCheckOnMessage(doc);
      fail();
    }
    catch (EDocumentProcessException e) {
      assertTrue(e.getCode() == error_xcbl_file);
    }

    // // check from not null
    // try {
    // doc = createDocument(true, true);
    // doc.setFrom(null);
    // preCheckOnMessage(doc);
    // fail();
    // } catch (EDocumentProcessException e) {
    // assertTrue(e.getCode() == null_from_code);
    // }
    //
    // // check to no null
    // try {
    // doc = createDocument(true, true);
    // doc.setTo(null);
    // preCheckOnMessage(doc);
    // fail();
    // } catch (EDocumentProcessException e) {
    // assertTrue(e.getCode() == null_to_code);
    // }

    // check all ok
    try {
      doc = createDocument(true, true);
      assertTrue(preCheckOnMessage(doc));
    }
    catch (EDocumentProcessException e) {
      fail();
    }

  }

  @Test
  public void testCreateItemWithEmptyMessage() {
    // check null message
    try {
      createItem(null, null, false, null, null);
      fail();
    }
    catch (EDocumentProcessException e) {
      assertTrue(e.getCode() == null_message);
    }
  }

  @Test
  public void testCreateItemWithEdiMessage() {
    // check copyProperties
    Document doc = createDocument(true, true);
    RecapListItem item = createItem(null, doc, true, "dematPartner", "legalEntity");
    assertEquals("legalEntity", item.getFrom());
    assertEquals("owners", item.getOwners());
    assertEquals("reference", item.getReference());
    assertEquals(NONE, item.getStatusAsEnumValue());
    assertEquals(UNDEFINED, item.getStage());
    assertEquals("dematPartner", item.getTo());
    assertEquals("type", item.getType());

    // check legalEntity, to, legalEntity
    assertEquals("legalEntity", item.getLegalEntityCode());
    assertEquals("dematPartner", item.getDematPartnerCode());

    // check xcbl :
    // check document number
    assertEquals("Invoice/InvoiceHeader/InvoiceNumber", item.getDocumentNumber());

    // check document date
    Calendar cal = Calendar.getInstance();
    cal.set(2003, 0, 1, 0, 0, 1);
    assertTrue(DateUtils.truncatedEquals(cal.getTime(), item.getDocumentDate(), Calendar.SECOND));

    // check document preparation date
    cal = Calendar.getInstance();
    cal.set(2003, 0, 1, 0, 0, 35);
    assertTrue(DateUtils.truncatedEquals(cal.getTime(), item.getDocumentPreparationDate(), Calendar.SECOND));

    // check taxable amount
    assertEquals(Double.valueOf(1121), item.getTaxableAmount());

    // check ati amount
    assertEquals(Double.valueOf(1115), item.getAtiAmount());

    // check currency code
    assertEquals("AFA", item.getCurrencyCode());

    // check invoice type
    assertEquals("MeteredServicesInvoice", item.getMessageType());

    FileUtils.delete(new File("target", "testCreateItem_error_file.err"));
  }

  @Test
  public void testCreateItemWithPdfMessage() {
    final DocumentCompoundType pdfNorm = DocumentCompoundTypeHelper.createCompoundType("INVOIC", "NAMED_PDF");
    Document doc = new Document("reference", "owners", DocumentCompoundTypeHelper.toJson(pdfNorm), "legalEntity",
        "dematPartner", NONE);
    doc.addFile(new DocumentFile(new File("edi_file"), EDIUNIT, "action", "processInstanceId", doc, "comment"));
    DocumentFile dof = new DocumentFile(new File("src/test/resources/xcbl/INV-PDF-A28125805.xml"), XCBL, "action",
        "processInstanceId", doc, "comment");
    dof.setDescription(EDocumentService.OFFICIAL_INDEX);
    doc.addFile(dof);
    RecapListItem item = createItem(null, doc, true, doc.getTo(), doc.getFrom());

    Assertions.assertTrue(item != null && DocumentStage.UNDEFINED == item.getStage() && 93306.36 == item.getAtiAmount() &&
        StringUtils.isEmpty(item.getCurrencyCode()));
  }

  @Test
  public void testCreateItemWithUserRecordMessage() {
    final DocumentCompoundType pdfNorm = DocumentCompoundTypeHelper.createCompoundType("INVOIC", "USER_RECORD");
    Document doc = new Document("reference", "owners", DocumentCompoundTypeHelper.toJson(pdfNorm), "legalEntity",
        "dematPartner", NONE);
    DocumentFile dof = new DocumentFile(new File("src/test/resources/xcbl/InvoiceSample-ABC_xCBL40.xml"), XCBL, "action",
        "processInstanceId", doc, "comment");
    dof.setDescription(EDocumentService.OFFICIAL_INDEX);
    doc.addFile(dof);
    DelegateExecution execution = buildExecution();
    RecapListItem item = createItem(execution, doc, true, doc.getTo(), doc.getFrom());

    EDocumentErrors err = new EDocumentErrors();
    err.add(new LabelSet(null, "testError1"));
    mockedEDocumentErrorHelper.when(() -> EDocumentErrorHelper.getEDocumentError(execution, doc))
            .thenReturn(err);

    Assertions.assertTrue(item != null && "USD".equals(item.getCurrencyCode()) && DocumentStage.REFUSED == item.getStage() &&
        4230.90 == item.getTaxableAmount() && 6110.00 == item.getAtiAmount() && 381.81 == item.getMonetaryAmount() &&
        !EDocumentErrorHelper.getEDocumentError(execution, doc)
            .isEmpty());
    FileHelper.deleteQuietly(new File("src/test/resources/xcbl/InvoiceSample-ABC_xCBL40.err"));
  }

  @Test
  public void testConsolidateItemInformations() {
    EDocumentErrors err = new EDocumentErrors();
    err.add(new LabelSet(null, "testError1"));
    LabelSet lb = new LabelSet(null, null);
    lb.addLabel("testError2", Locale.ENGLISH);
    err.add(lb);
    err.add(new LabelSet("XCX", "XCX"));
    DelegateExecution execution = mock(DelegateExecution.class);
    Document doc = createDocument(true, true);
    Document finalDoc = doc;
    mockedEDocumentErrorHelper.when(() -> EDocumentErrorHelper.getEDocumentError(execution, finalDoc))
            .thenReturn(err);
    addErrorLabel(execution, doc, "testError1");
    addErrorCode(execution, doc, "testError2");
    RecapListItem item = createItem(execution, doc, false, doc.getTo(), doc.getFrom());
    try {
      int nbDocErrors = size(EDocumentErrorHelper.getEDocumentError(execution, doc));
      consolidateItemInformations(execution, item, doc, null, null, null, null, null);
      assertEquals(nbDocErrors, size(EDocumentErrorHelper.getEDocumentError(execution, doc)));
      assertEquals(DocumentStage.REFUSED, doc.getStage());
      assertEquals(doc.getStatus(), item.getStatus());
    }
    catch (EDocumentProcessException e) {
      fail();
    }

    try {
      int nbDocErrors = size(EDocumentErrorHelper.getEDocumentError(execution, doc));
      consolidateItemInformations(execution, item, doc, "legaleEntity", "", createOrganization("legalEntity", false, 0), null, null);
      assertEquals(nbDocErrors, size(EDocumentErrorHelper.getEDocumentError(execution, doc)));
      assertEquals(DocumentStage.REFUSED, doc.getStage());
      assertEquals(doc.getStatus(), item.getStatus());
    }
    catch (EDocumentProcessException e) {
      fail();
    }

    try {
      int nbDocErrors = size(EDocumentErrorHelper.getEDocumentError(execution, doc));
      consolidateItemInformations(execution, item, doc, "legalEntity", "to", createOrganization("legalEntity", false, 0),
          createOrganization("to", false, 0), null);
      assertEquals(nbDocErrors, size(EDocumentErrorHelper.getEDocumentError(execution, doc)));
      assertEquals(DocumentStage.REFUSED, doc.getStage());
      assertEquals(doc.getStatus(), item.getStatus());
    }
    catch (EDocumentProcessException e) {
      fail();
    }

    doc = createDocument(true, true);
    addErrorCode(execution, doc, "testError1");
    addErrorLabel(execution, doc, "testError2");
    item = createItem(execution, doc, false, doc.getTo(), doc.getFrom());
    Organization legalEntity = createOrganization("legalEntity", false, 9);
    Organization dematPartner = createOrganization("dematPartner", true, 9);

    try {
      consolidateItemInformations(execution, item, doc, "legalEntity", "dematPartner", legalEntity, dematPartner, "version");
    }
    catch (EDocumentProcessException e) {
      fail();
    }

    assertEquals("Code" + "legalEntity", item.getLegalEntityCode());
    assertEquals("legalEntity-fullname", item.getLegalEntityName());
    assertEquals("DUNS" + "legalEntity", item.getLegalEntityReference());
    assertEquals("Registration" + "legalEntity", item.getLegalEntitySiren());
    assertEquals("VAT" + "legalEntity", item.getLegalEntityVATNumber());
    assertEquals("streetName" + "legalEntity", item.getLegalEntityAddress());
    assertEquals("postalCode" + "legalEntity", item.getLegalEntityPostalCode());
    assertEquals("city" + "legalEntity", item.getLegalEntityCity());
    assertEquals("France", I18NHelper.toLocale(item.getLegalEntityCountry())
        .getDisplayCountry());

    assertEquals("Code" + "dematPartner", item.getDematPartnerCode());
    assertEquals("dematPartner-fullname", item.getDematPartnerName());
    assertEquals("DUNS" + "dematPartner", item.getDematPartnerReference());
    assertEquals("Registration" + "dematPartner", item.getDematPartnerSiren());
    assertEquals("VAT" + "dematPartner", item.getDematPartnerVATNumber());
    assertEquals("streetName" + "dematPartner", item.getDematPartnerAddress());
    assertEquals("postalCode" + "dematPartner", item.getDematPartnerPostalCode());
    assertEquals("city" + "dematPartner", item.getDematPartnerCity());
    assertEquals("France", I18NHelper.toLocale(item.getDematPartnerCountry())
        .getDisplayCountry());

    assertEquals("version", item.getSoftwareVersion());

    assertEquals(doc.getStatus(), item.getStatus());
    assertEquals(doc.getStage(), item.getStage());

    // check MessageErrors
    err.remove(2);
    item.setErrors(err);
    assertEquals(2, item.getErrors()
        .size());
    assertEquals("testError1", item.getErrors()
        .get(0)
        .getKey());
    assertEquals(null, item.getErrors()
        .get(1)
        .getKey());
    assertEquals("testError2", item.getErrors()
        .get(1)
        .getLabel(Locale.ENGLISH)
        .getValue());
  }

  @Test
  public void testCheckInvoiceNorm() {
    final DocumentCompoundType pdfNorm = DocumentCompoundTypeHelper.createCompoundType("INVOIC", "NAMED_PDF");
    Document message = new Document("reference", "owners", DocumentCompoundTypeHelper.toJson(pdfNorm), "legalEntity",
        "dematPartner", NONE);
    Assertions.assertTrue(RecapListHelper.checkInvoiceNorm(message, EDocumentService.RTE.NAMED_PDF.toString()));
  }

  /**
   * -- UTILS --
   */
  private static Document createDocument(boolean withEdiFile, boolean withXcblFile) {
    Document doc = new Document("reference", "owners", "type", "legalEntity", "dematPartner", NONE);
    if (withEdiFile) {
      doc.addFile(new DocumentFile(new File("edi_file"), EDIUNIT, "action", "processInstanceId", doc, "comment"));
    }
    if (withXcblFile) {
      DocumentFile dof = new DocumentFile(new File(
          RecapListHelperTest.class.getClassLoader()
              .getResource("xcbl/InvoiceExample.xml")
              .getFile()),
          XCBL,
          "action", "processInstanceId", doc, "comment");
      dof.setDescription(EDocumentService.OFFICIAL_INDEX);
      doc.addFile(dof);
    }
    if (new File("edi_file.err").exists()) {
      FileUtils.delete(new File("edi_file.err"));
    }
    return doc;
  }

  private static Organization createOrganization(String suffix, boolean company, int nbFields) {
    Organization org = company ? new Company() : new Partner();
    org.setLocation(null);
    if (nbFields >= 1) {
      org.setCode("Code" + suffix);
    }
    if (nbFields >= 2) {
      org.setName("Name" + suffix);
      org.setFullname(String.join("-", suffix, "fullname"));
    }
    if (nbFields == 3) {
      org.setDuns("DUNS" + suffix);
    }
    if (nbFields == 4) {
      org.setRegistration("Registration" + suffix);
    }
    if (nbFields >= 5) {
      org.setVat("VAT" + suffix);
    }
    if (nbFields >= 6) {
      org.getLocation()
          .getAddress()
          .setStreetName("streetName" + suffix);
    }
    if (nbFields >= 7) {
      org.getLocation()
          .getAddress()
          .setPostalCode("postalCode" + suffix);
    }
    if (nbFields >= 8) {
      org.getLocation()
          .getAddress()
          .setCity("city" + suffix);
    }
    if (nbFields >= 9) {
      org.getLocation()
          .getAddress()
          .setCountry(Locale.FRANCE);
      org.setRegistration("Registration" + suffix);
      org.setDuns("DUNS" + suffix);
    }
    return org;
  }

  private DelegateExecution buildExecution() {
    bankAccountService = Mockito.mock(ProcessEngineConfigurationImpl.class, Mockito.RETURNS_DEEP_STUBS);
    mockedSpringContextHelper = Mockito.mockStatic(SpringContextHelper.class);
    mockedSpringContextHelper.when(() -> SpringContextHelper.getBean(ProcessEngineConfigurationImpl.class, "processEngineConfiguration"))
            .thenReturn(bankAccountService);

    ProcessEngineConfigurationImpl processEngineConfiguration = getBean(ProcessEngineConfigurationImpl.class, "processEngineConfiguration");
    CommandContextFactory commandContextFactory = new CommandContextFactory();
    commandContextFactory.setProcessEngineConfiguration(processEngineConfiguration);
    CommandContext context = commandContextFactory.createCommandContext(new SchemaOperationsProcessEngineBuild());
    Context.setCommandContext(context);
    Context.setProcessEngineConfiguration(processEngineConfiguration);
    ExecutionEntity execution = new ExecutionEntityImpl();
    return execution;
  }
}
