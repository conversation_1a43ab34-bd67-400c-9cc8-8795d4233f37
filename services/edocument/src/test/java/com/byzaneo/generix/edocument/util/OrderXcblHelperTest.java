package com.byzaneo.generix.edocument.util;

import com.byzaneo.generix.service.repository.bean.Product;
import com.byzaneo.security.bean.Location;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.xcbl.bean.Contract;
import com.byzaneo.xtrade.xcbl.bean.Order;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderPartyType;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;

public class OrderXcblHelperTest {

  @Test
  public void initOrderWithNoContract() throws Exception {
    assertNull(OrderXcblHelper.initOrder(null, null, null));

    Contract contract = new Contract();
    Partner partner = new Partner();
    partner.setLocation(new Location("DEFAULT", new com.byzaneo.location.bean.Address(), true));
    partner.getLocation()
        .getAddress()
        .setCountry(Locale.FRANCE);
    ShippingScheduleHeaderType headerType = new ShippingScheduleHeaderType();
    NameAddressType nameAddressType = new NameAddressType();
    nameAddressType.setCity("Paris");
    IdentifierType identifierType = new IdentifierType();
    identifierType.setIdent("ident1");
    PartyType partyType = new PartyType();
    partyType.setPartyID(identifierType);
    partyType.setNameAddress(nameAddressType);
    SchedulePartyType schedulePartyType = new SchedulePartyType();
    schedulePartyType.setShipToParty(partyType);
    schedulePartyType.setShipFromParty(partyType);
    schedulePartyType.setBuyerParty(partyType);
    ScheduleReferencesType scheduleReferencesType = new ScheduleReferencesType();
    scheduleReferencesType.setOtherScheduleReferences(new ListOfReferenceCodedType());
    ListOfTransportRoutingType listOfTransportRoutingType = new ListOfTransportRoutingType();
    List<TransportRoutingType> list = listOfTransportRoutingType.getTransportRouting();
    TransportMeansType tmeans = new TransportMeansType();
    tmeans.setTransportMeansCoded("BUS");
    TransportRoutingType tRouting = new TransportRoutingType();
    tRouting.setTransportMeans(tmeans);
    list.add(tRouting);
    headerType.setScheduleID("ID1");
    headerType.setScheduleParty(schedulePartyType);
    headerType.setListOfTransportRouting(listOfTransportRoutingType);
    headerType.setScheduleReferences(scheduleReferencesType);
    ListOfMaterialGroupedShippingDetailType listOfMaterial = new ListOfMaterialGroupedShippingDetailType();
    BaseShippingDetailType baseShippingDetailType = new BaseShippingDetailType();
    ItemIdentifiersType itemIdentifiers = new ItemIdentifiersType();
    QuantityCodedType quantity = new QuantityCodedType();
    quantity.setUnitOfMeasurement(new UnitOfMeasurementType());
    PartNumbersType partNumbers = new PartNumbersType();
    PartNumType partNumType = new PartNumType();
    partNumType.setPartID("id");
    partNumbers.setSellerPartNumber(partNumType);
    itemIdentifiers.setPartNumbers(partNumbers);
    baseShippingDetailType.setItemIdentifiers(itemIdentifiers);
    baseShippingDetailType.setTotalQuantity(quantity);
    MaterialGroupedShippingDetailType materialGrouped = new MaterialGroupedShippingDetailType();
    materialGrouped.setBaseShippingDetail(baseShippingDetailType);
    listOfMaterial.getMaterialGroupedShippingDetail()
        .add(materialGrouped);
    contract.setShippingScheduleHeader(headerType);
    contract.setListOfMaterialGroupedShippingDetail(listOfMaterial);

    Product p1 = new Product();
    p1.setReference("reference");
    p1.setDescription("product 1");
    p1.setTo("To");

    Order order = OrderXcblHelper.initOrder(contract, partner, Arrays.asList(p1));
    OrderDetailType orderDetail = order.getOrderDetail();
    OrderHeaderType orderHeader = order.getOrderHeader();
    OrderPartyType orderParty = order.getOrderHeader()
        .getOrderParty();
    assertNotNull(orderDetail);
    assertNotNull(orderDetail.getListOfItemDetail());
    assertEquals(1, orderDetail.getListOfItemDetail()
        .getItemDetail()
        .size());
    assertNotNull(orderHeader);
    assertNotNull(orderHeader.getOrderHeaderPrice());
    assertEquals(1, orderHeader.getOrderReferences()
        .getContractReferences()
        .getContract()
        .size());
    assertEquals("ident1", orderParty.getBuyerParty()
        .getPartyID()
        .getIdent());
    assertEquals(1, orderHeader.getListOfTransportRouting()
        .getTransportRouting()
        .size());
  }

}