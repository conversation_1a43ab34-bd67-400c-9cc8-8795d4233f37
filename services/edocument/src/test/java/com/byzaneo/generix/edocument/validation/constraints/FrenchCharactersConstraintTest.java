package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class FrenchCharactersConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/FrenchCharactersConstraintTestFile.xml"), false);

  @Test
  public void testValidTextExpressions() {
    FrenchCharactersConstraint frenchCharactersConstraint = new FrenchCharactersConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//expressions/validExpressions/validText");
    frenchCharactersConstraint.setNodes(nodes);
    frenchCharactersConstraint.setXQuery(new XQuery());
    boolean result = frenchCharactersConstraint.isValid();
    assertTrue(result);
  }

  @Test
  public void testValidNumberExpressions() {
    FrenchCharactersConstraint frenchCharactersConstraint = new FrenchCharactersConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//expressions/validExpressions/validNumber");
    frenchCharactersConstraint.setNodes(nodes);
    frenchCharactersConstraint.setXQuery(new XQuery());
    boolean result = frenchCharactersConstraint.isValid();
    assertTrue(result);
  }

  @Test
  public void testInvalidMixedExpression() {
    FrenchCharactersConstraint frenchCharactersConstraint = new FrenchCharactersConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//expressions/validExpressions/invalidMixedExpressions");
    frenchCharactersConstraint.setNodes(nodes);
    frenchCharactersConstraint.setXQuery(new XQuery());
    boolean result = frenchCharactersConstraint.isValid();
    assertFalse(result);
  }

  @Test
  public void testValidSymbolExpressions() {
    FrenchCharactersConstraint frenchCharactersConstraint = new FrenchCharactersConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//expressions/validExpressions/validSymbolExpression");
    frenchCharactersConstraint.setXQuery(new XQuery());
    frenchCharactersConstraint.setNodes(nodes);
    boolean result = frenchCharactersConstraint.isValid();
    assertTrue(result);
  }

  @Test
  public void testInvalidTextExpressions() {
    FrenchCharactersConstraint frenchCharactersConstraint = new FrenchCharactersConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//expressions/invalidExpressions/invalidText");
    frenchCharactersConstraint.setXQuery(new XQuery());
    frenchCharactersConstraint.setNodes(nodes);
    boolean result = frenchCharactersConstraint.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalidNumberExpressions() {
    FrenchCharactersConstraint frenchCharactersConstraint = new FrenchCharactersConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//expressions/invalidExpressions/invalidNumber");
    frenchCharactersConstraint.setXQuery(new XQuery());
    frenchCharactersConstraint.setNodes(nodes);
    boolean result = frenchCharactersConstraint.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalidMixedExpression2() {
    FrenchCharactersConstraint frenchCharactersConstraint = new FrenchCharactersConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//expressions/invalidExpressions/invalidMixedExpression");
    frenchCharactersConstraint.setXQuery(new XQuery());
    frenchCharactersConstraint.setNodes(nodes);
    boolean result = frenchCharactersConstraint.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalidSymbolExpressions() {
    FrenchCharactersConstraint frenchCharactersConstraint = new FrenchCharactersConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//expressions/invalidExpressions/invalidSymbolExpression");
    frenchCharactersConstraint.setXQuery(new XQuery());
    frenchCharactersConstraint.setNodes(nodes);
    boolean result = frenchCharactersConstraint.isValid();
    assertFalse(result);
  }

}
