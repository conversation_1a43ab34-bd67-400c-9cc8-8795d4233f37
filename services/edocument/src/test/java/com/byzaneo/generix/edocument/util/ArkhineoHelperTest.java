package com.byzaneo.generix.edocument.util;

import com.byzaneo.generix.edocument.bean.ArkhineoSearchParameters;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class ArkhineoHelperTest {

  @Test
  public void testBuildLuceneExpression() {
    List<String> codes = Arrays.asList("INVOIC", "ORDERS");
    ArkhineoSearchParameters params = new ArkhineoSearchParameters();
    params.setLegalEntityCode("GNX");
    params.setDocSenderCode("01");
    params.setDocReceiverCode("AB");
    params.setArchiveName("Archive");
    params.setDocKindCodes(codes);
    params.setDocFormat("PDF");
    params.setDocDateMin(new Date(1400000000000L));
    params.setDocDateMax(new Date(1500000000000L));
    params.setDocNumbers(Arrays.asList("01", "02", "03"));

    String expected = "(business.artefact:\"document\" OR business.artefact:\"Document\") " +
        "AND business.archiveType:\"Archive unitaire\" AND business.docLegalEntityCode:\"GNX\" " +
        "AND business.docSenderCode:\"01\" AND business.docReceiverCode:\"AB\" " +
        "AND ( business.docKindCode:INVOIC OR business.docKindCode:ORDERS) AND business.docFormat:\"PDF\" " +
        "AND ( business.docNumber:01 OR business.docNumber:02 OR business.docNumber:03) " +
        "AND business.docDate:[20140513000000000 TO 20170714235959999] AND business.archiveName:\"Archive\"";
    assertEquals(expected, ArkhineoHelper.buildLuceneExpression(params));
  }

}