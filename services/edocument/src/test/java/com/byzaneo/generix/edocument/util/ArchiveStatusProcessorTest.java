package com.byzaneo.generix.edocument.util;

import java.util.Locale;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.supercsv.exception.SuperCsvCellProcessorException;

import com.byzaneo.xtrade.api.ArchiveStatus;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class ArchiveStatusProcessorTest {
  private ArchiveStatusProcessor archiveStatusProcessor;

  @BeforeEach
  public void setUp() throws Exception {
    this.archiveStatusProcessor = new ArchiveStatusProcessor(Locale.FRANCE);
  }

  @Test
  public void testInitMapValue() {
    assertEquals(ArchiveStatus.values().length, archiveStatusProcessor.initMapValue()
        .size());
  }

  @Test
  public void testExecuteWithNullValue() {
    assertThrows(SuperCsvCellProcessorException.class, () ->
      this.archiveStatusProcessor.execute(null, null));
  }

  @Test
  public void testExecute() {
    assertEquals("UNDEFINED", this.archiveStatusProcessor.execute(ArchiveStatus.UNDEFINED, null));
    assertEquals("ARCHIVED", this.archiveStatusProcessor.execute(ArchiveStatus.ARCHIVED, null));
    assertEquals("", this.archiveStatusProcessor.execute("", null));
  }

}