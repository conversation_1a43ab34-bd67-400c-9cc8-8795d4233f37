package com.byzaneo.generix.edocument.service;

import java.util.List;

import com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;

public class AsnMock extends EDocumentServiceImpl {
  @Override
  public void setAsnFillRequiredFields(AdvanceShipmentNotice asn) {
    super.setAsnFillRequiredFields(asn);
  }

  public AdvanceShipmentNotice createAsn() {
    AdvanceShipmentNotice asn = new AdvanceShipmentNotice();

    asn.setASNHeader(createASNHeader());
    asn.setASNDetail(createASNDetail());

    return asn;
  }

  private ASNHeaderType createASNHeader() {
    ASNHeaderType asnHeader = new ASNHeaderType();
    asnHeader.setListOfTransportRouting(createListOfTransportRouting());

    return asnHeader;
  }

  private ListOfTransportRoutingType createListOfTransportRouting() {
    ListOfTransportRoutingType listOfTransportRoutingType = new ListOfTransportRoutingType();

    List<TransportRoutingType> transportRoutingList = listOfTransportRoutingType.getTransportRouting();
    transportRoutingList.add(createTransportRouting(null));
    transportRoutingList.add(createTransportRouting(TransportModeCodeType.OTHER));

    return listOfTransportRoutingType;
  }

  private TransportRoutingType createTransportRouting(TransportModeCodeType transportModeCode) {
    TransportRoutingType transportRouting = new TransportRoutingType();
    transportRouting.setTransportMode(createTransportMode(transportModeCode));

    return transportRouting;
  }

  private TransportModeType createTransportMode(TransportModeCodeType transportModeCode) {
    TransportModeType transportMode = new TransportModeType();
    transportMode.setTransportModeCoded(transportModeCode);

    return transportMode;
  }

  private ASNDetailType createASNDetail() {
    ASNDetailType asnDetail = new ASNDetailType();
    asnDetail.setListOfASNItemDetail(createListOfAsnDetail());
    asnDetail.setListOfASNPackageDetail(createListOfASNPackageDetail());

    return asnDetail;
  }

  private ListOfASNItemDetailType createListOfAsnDetail() {
    ListOfASNItemDetailType listOfASNItemDetailType = new ListOfASNItemDetailType();

    List<ASNItemDetailType> asnItemDetailList = listOfASNItemDetailType.getASNItemDetail();
    asnItemDetailList.add(createAsnItemDetail());

    return listOfASNItemDetailType;
  }

  private ListOfASNPackageDetailType createListOfASNPackageDetail() {
    ListOfASNPackageDetailType listOfASNPackageDetailType = new ListOfASNPackageDetailType();
    List<PackageDetailType> asnPackageDetailList = listOfASNPackageDetailType.getASNPackageDetail();
    asnPackageDetailList.add(createAsnPackageDetail());

    return listOfASNPackageDetailType;
  }

  private PackageDetailType createAsnPackageDetail() {
    PackageDetailType packageDetail = new PackageDetailType();
    packageDetail.setListOfPackage(createListOfPackage());

    return packageDetail;
  }

  private ListOfPackageType createListOfPackage() {
    ListOfPackageType listOfPackageType = new ListOfPackageType();
    List<PackageType> packageList = listOfPackageType.getPackage();
    packageList.add(createPackage());

    return listOfPackageType;
  }

  private PackageType createPackage() {
    PackageType pck = new PackageType();
    List<PackageDetailType> packageDetailList = pck.getPackageDetail();

    pck.setListOfDimension(new ListOfDimensionType());
    pck.setListOfItemReference(createListOfItemRef());
    packageDetailList.add(createListOfPackageDetail());

    return pck;
  }

  private ListOfPackageItemReferenceType createListOfItemRef() {
    ListOfPackageItemReferenceType listOfPackageItemRef = new ListOfPackageItemReferenceType();
    List<PackageItemReferenceType> packageItemRefList = listOfPackageItemRef.getPackageItemReference();
    packageItemRefList.add(createPackageItemReference());

    return listOfPackageItemRef;
  }

  private PackageItemReferenceType createPackageItemReference() {
    PackageItemReferenceType packageItemRef = new PackageItemReferenceType();
    packageItemRef.setLineItemReference("42");
        
    return packageItemRef;
  }

  private PackageDetailType createListOfPackageDetail() {
    PackageDetailType packageDetail = new PackageDetailType();
    packageDetail.setListOfPackage(createListOfPackageForPckDetail());

    return packageDetail;
  }

  private ListOfPackageType createListOfPackageForPckDetail() {
    ListOfPackageType listOfPackageType = new ListOfPackageType();
    List<PackageType> packageList = listOfPackageType.getPackage();
    packageList.add(createPackageForPckDetailWithListOfDimension());
    packageList.add(createPackageForPckDetailWithoutListOfDimension());

    return listOfPackageType;
  }

  private PackageType createPackageForPckDetailWithListOfDimension() {
    PackageType pck = new PackageType();
    pck.setListOfDimension(createListOfDimension());
    
    return pck;
  }

  private PackageType createPackageForPckDetailWithoutListOfDimension() {
    PackageType pck = new PackageType();
    return pck;
  }

  private ListOfDimensionType createListOfDimension() {
    ListOfDimensionType listOfDimension = new ListOfDimensionType();
    List<DimensionType> dimensionList = listOfDimension.getDimension();
    dimensionList.add(createDimension());

    return listOfDimension;
  }

  private DimensionType createDimension() {
    DimensionType dimension = new DimensionType();
    dimension.setDimensionCoded(DimensionCodeType.OTHER);

    return dimension;
  }

  private ASNItemDetailType createAsnItemDetail() {
    ASNItemDetailType asnItemDetailType = new ASNItemDetailType();
    asnItemDetailType.setASNBaseItemDetail(createASNBaseItemDetail());

    return asnItemDetailType;
  }

  private ASNBaseItemDetailType createASNBaseItemDetail() {
    ASNBaseItemDetailType asnBaseItemDetailType = new ASNBaseItemDetailType();
    asnBaseItemDetailType.setLineItemNum(createLineItemNum());

    return asnBaseItemDetailType;
  }

  private LineItemNumType createLineItemNum() {
    LineItemNumType lineItemNumType = new LineItemNumType();
    lineItemNumType.setBuyerLineItemNum(42);

    return lineItemNumType;
  }
}
