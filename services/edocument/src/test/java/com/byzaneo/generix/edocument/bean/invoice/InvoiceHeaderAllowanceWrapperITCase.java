package com.byzaneo.generix.edocument.bean.invoice;

import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxAmountFromInvoiceTaxType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxCategoryCodedOtherFromInvoiceTaxType;
import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;

import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.ui.MissingKeyHandler;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.edocument.api.invoice.InvoiceHeaderAllowOrChargeWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.InvoiceCsvHeaderAllowOrCharge;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.util.InvoiceHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;

public class InvoiceHeaderAllowanceWrapperITCase {

  private static MockedStatic<JSFHelper> mockedJSFHelper;

  private static InvoiceHeaderAllowOrChargeWrapper instance;

  @BeforeAll
  static void setUpStaticMocks() {
    mockedJSFHelper = mockStatic(JSFHelper.class, invocation -> {
      if (invocation.getMethod()
          .getName()
          .equals("getMissingKeyHandler")) {
        return Mockito.mock(MissingKeyHandler.class);
      }
      return invocation.callRealMethod();
    });
    instance = new InvoiceHeaderAllowOrChargeWrapper();
    Mockito.spy(CsvImportHelper.class);
  }

  @AfterAll
  static void tearDownStaticMocks() {
    mockedJSFHelper.closeOnDemand();
  }

  @Test
  public void testValidateNoHeader() {
    InvoiceCsvHeaderAllowOrCharge allowance = new InvoiceCsvHeaderAllowOrCharge();

    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(null);

      try {
        instance.validate(allowance, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("header");
        Assertions.assertEquals("Aucune entête liée à cet élément", serviceException.getMessage());
      }
    }
  }

  @Test
  public void testValidateUnknownType() {
    InvoiceCsvHeaderAllowOrCharge allowance = new InvoiceCsvHeaderAllowOrCharge();
    allowance.setType("ZZ");
    allowance.setInformationType("3");
    allowance.setServiceCode("ZZZ");

    Invoice invoice = new Invoice();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(invoice);

      try {
        instance.validate(allowance, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        assertTrue(asList(
            "Code de service particulier pour les remises/charges d'en-tête inconnu : ZZZ",
            "Unknown type for header allowances or charges of invoice : ZZZ")
            .contains(e.getExceptions()
                .get("header allowance code")
                .getMessage()));
        Assertions.assertEquals("Type pour les remises/charges d'en-tête inconnu : ZZ", e.getExceptions()
            .get("header allowance type")
            .getMessage());
        Assertions.assertEquals("Type d'information pour les remises/charges d'en-tête inconnu : 3", e.getExceptions()
            .get("header allowance information")
            .getMessage());
      }
    }
  }

  @Test
  public void testValidateA() {
    InvoiceCsvHeaderAllowOrCharge allowance = new InvoiceCsvHeaderAllowOrCharge();
    allowance.setType("A");
    allowance.setInformationType("2");
    allowance.setServiceCode("ADS");
    allowance.setAmount(new BigDecimal("123.56"));
    allowance.setLabel("Libellé ADS");
    allowance.setPercentage(new BigDecimal("4.3"));
    allowance.setTaxAmount(new BigDecimal("65.1"));
    allowance.setTaxCode("Taxe ADS");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(invoice);

      try {
        instance.validate(allowance, null);

        // Check
        InvoiceAllowOrChargeType allowanceOrCharge = invoice.getInvoiceHeader()
            .getInvoiceAllowancesOrCharges()
            .getAllowOrCharge()
            .get(0);
        assertEquals("Taxe ADS", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getRefID());
        assertEquals("Libellé ADS", InvoiceHelper.getListOfDescriptionFromInvoiceAllowanceOrCharge(allowanceOrCharge));
        assertEquals(PercentQualifierCodeType.OTHER, allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercentQualifier()
            .getPercentQualifierCoded());
        assertEquals("NotIndicated", allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercentQualifier()
            .getPercentQualifierCodedOther());
        assertEquals(new BigDecimal("4.3"), allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercent()
            .getValue());
        assertEquals(new BigDecimal("123.56"), InvoiceHelper.getMonetaryAmountFromInvoiceAllowanceOrCharge(allowanceOrCharge));
        assertEquals("ValueAddedTax", allowanceOrCharge.getTax()
            .get(0)
            .getTaxTypeCoded());
        assertEquals(TaxFunctionQualifierCodeType.TAX, allowanceOrCharge.getTax()
            .get(0)
            .getTaxFunctionQualifierCoded());
        assertEquals(TaxCategoryCodeType.OTHER, allowanceOrCharge.getTax()
            .get(0)
            .getTaxCategoryCoded());
        assertEquals("65.1", getTaxCategoryCodedOtherFromInvoiceTaxType(allowanceOrCharge.getTax()
            .get(0)));
        assertEquals(new BigDecimal("65.1"), allowanceOrCharge.getTax()
            .get(0)
            .getTaxPercent()
            .getValue());
        assertEquals(new BigDecimal("-1"), getTaxAmountFromInvoiceTaxType(allowanceOrCharge.getTax()
            .get(0)));
        assertEquals("Other", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getServiceCoded());
        assertEquals("FullPalletOrdering", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getServiceCodedOther());

      }
      catch (PropertiesException e) {
        fail("no validation exception expected");
      }
      catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }

  }

  @Test
  public void testValidateC() {
    InvoiceCsvHeaderAllowOrCharge allowance = new InvoiceCsvHeaderAllowOrCharge();
    allowance.setType("C");
    allowance.setInformationType("1");
    allowance.setServiceCode("TAE");
    allowance.setAmount(new BigDecimal("456.78"));
    allowance.setLabel("Libellé TAE");
    allowance.setTaxAmount(new BigDecimal("12.3"));
    allowance.setTaxCode("Taxe TAE");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(invoice);

      try {
        instance.validate(allowance, null);

        // Check
        InvoiceAllowOrChargeType allowanceOrCharge = invoice.getInvoiceHeader()
            .getInvoiceAllowancesOrCharges()
            .getAllowOrCharge()
            .get(0);
        assertEquals("Taxe TAE", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getRefID());
        assertEquals("Libellé TAE", InvoiceHelper.getListOfDescriptionFromInvoiceAllowanceOrCharge(allowanceOrCharge));
        assertEquals(new BigDecimal("456.78"), allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercentageMonetaryValue()
            .getMonetaryAmount());
        assertEquals("ValueAddedTax", allowanceOrCharge.getTax()
            .get(0)
            .getTaxTypeCoded());
        assertEquals(TaxFunctionQualifierCodeType.TAX, allowanceOrCharge.getTax()
            .get(0)
            .getTaxFunctionQualifierCoded());
        assertEquals(TaxCategoryCodeType.OTHER, allowanceOrCharge.getTax()
            .get(0)
            .getTaxCategoryCoded());
        assertEquals("12.3", getTaxCategoryCodedOtherFromInvoiceTaxType(allowanceOrCharge.getTax()
            .get(0)));
        assertEquals(new BigDecimal("12.3"), allowanceOrCharge.getTax()
            .get(0)
            .getTaxPercent()
            .getValue());
        assertEquals(new BigDecimal("-1"), getTaxAmountFromInvoiceTaxType(allowanceOrCharge.getTax()
            .get(0)));
        assertEquals("FullTruckloadAllowance", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getServiceCoded());

      }
      catch (PropertiesException e) {
        fail("no validation exception expected");
      }
      catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }
}
