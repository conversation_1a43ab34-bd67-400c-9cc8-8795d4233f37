package com.byzaneo.generix.edocument.bean.invoice;

import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getPaymentTermDescriptionFromInvoice;
import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.ui.MissingKeyHandler;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.edocument.api.invoice.InvoiceCommentWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.CsvHeaderComment;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;

public class InvoiceCommentWrapperITCase {

  private static MockedStatic<JSFHelper> mockedJSFHelper;

  private static MockedStatic<CsvImportHelper> mockedCsvImportHelper;

  private static InvoiceCommentWrapper instance;

  @BeforeAll
  static void setUpStaticMocks() {
    mockedJSFHelper = Mockito.mockStatic(JSFHelper.class, invocation -> {
      if (invocation.getMethod()
          .getName()
          .equals("getMissingKeyHandler")) {
        return Mockito.mock(MissingKeyHandler.class);
      }
      return invocation.callRealMethod();
    });
    instance = new InvoiceCommentWrapper();
    mockedCsvImportHelper = Mockito.mockStatic(CsvImportHelper.class);
  }

  @AfterAll
  static void tearDownStaticMocks() {
    mockedCsvImportHelper.closeOnDemand();
    mockedJSFHelper.closeOnDemand();
  }

  @Test
  public void testValidateNoHeader() {
    CsvHeaderComment comment = new CsvHeaderComment();

    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(null);

    try {
      instance.validate(comment, null);
      fail("expected validation exception");
    }
    catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("header");
      Assertions.assertEquals("Aucune entête liée à cet élément", serviceException.getMessage());
    }
  }

  @Test
  public void testValidateUnknownType() {
    CsvHeaderComment comment = new CsvHeaderComment();
    comment.setType("ZZZ");
    Invoice invoice = new Invoice();
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      instance.validate(comment, null);
      fail("expected validation exception");
    }
    catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("comment");
      String message = serviceException.getMessage();
      assertTrue("Type de commentaire d'en-tête inconnu : ZZZ".equals(message) || "Unknown header comment type : ZZZ".equals(message));
    }
  }

  @Test
  public void testValidateSIN() {
    CsvHeaderComment comment = new CsvHeaderComment();
    comment.setType("SIN");
    comment.setComment("COMMENTAIRE POUR TYPE SIN");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      instance.validate(comment, null);

      // Check
      assertEquals(ReasonTaxExemptCodeType.OTHER, invoice.getInvoiceHeader()
          .getTaxReference()
          .get(0)
          .getReasonTaxExemptCoded());
      assertEquals("COMMENTAIRE POUR TYPE SIN", invoice.getInvoiceHeader()
          .getTaxReference()
          .get(0)
          .getReasonTaxExemptCodedOther());
      assertEquals("ValueAddedTax", invoice.getInvoiceHeader()
          .getTaxReference()
          .get(0)
          .getTaxTypeCoded());
      assertEquals(TaxCategoryCodeType.EXEMPT_FROM_TAX, invoice.getInvoiceHeader()
          .getTaxReference()
          .get(0)
          .getTaxCategoryCoded());
      assertEquals(TaxTreatmentCodeType.NO_TAX_APPLIES, invoice.getInvoiceHeader()
          .getTaxReference()
          .get(0)
          .getTaxTreatmentCoded());
      assertEquals(TaxFunctionQualifierCodeType.TAX, invoice.getInvoiceHeader()
          .getTaxReference()
          .get(0)
          .getTaxFunctionQualifierCoded());

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }

  @Test
  public void testValidateAAB() {
    CsvHeaderComment comment = new CsvHeaderComment();
    comment.setType("AAB");
    comment.setComment("COMMENTAIRE POUR TYPE AAB");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      instance.validate(comment, null);

      // Check
      assertEquals("Discount", invoice.getInvoiceHeader()
          .getInvoicePaymentInstructions()
          .getPaymentTerms()
          .getPaymentTerm()
          .get(0)
          .getPaymentTermCoded());
      assertEquals("COMMENTAIRE POUR TYPE AAB", getPaymentTermDescriptionFromInvoice(invoice));

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }

  @Test
  public void testValidatePMD() {
    CsvHeaderComment comment = new CsvHeaderComment();
    comment.setType("PMD");
    comment.setComment("COMMENTAIRE POUR TYPE PMD");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      instance.validate(comment, null);

      // Check
      assertEquals("PenaltyTerms", invoice.getInvoiceHeader()
          .getInvoicePaymentInstructions()
          .getPaymentTerms()
          .getPaymentTerm()
          .get(0)
          .getPaymentTermCoded());
      assertEquals("COMMENTAIRE POUR TYPE PMD", getPaymentTermDescriptionFromInvoice(invoice));

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }

  @Test
  public void testValidateREG() {
    CsvHeaderComment comment = new CsvHeaderComment();
    comment.setType("REG");
    comment.setComment("COMMENTAIRE POUR TYPE REG");

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      instance.validate(comment, null);

      // Check
      assertEquals("COMMENTAIRE POUR TYPE REG", invoice.getInvoiceHeader()
          .getInvoiceHeaderNote());

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }
}
