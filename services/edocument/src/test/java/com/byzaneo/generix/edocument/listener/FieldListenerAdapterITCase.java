package com.byzaneo.generix.edocument.listener;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.times;

import java.util.stream.IntStream;

import org.hibernate.event.spi.PostInsertEvent;
import org.hibernate.event.spi.PostUpdateEvent;
import org.hibernate.persister.entity.SingleTableEntityPersister;
import org.hibernate.tuple.entity.EntityMetamodel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.byzaneo.commons.event.EventBuilder;
import com.byzaneo.xtrade.bean.Index;
import com.byzaneo.xtrade.index.MongoIndexOperations;
import com.byzaneo.xtrade.xcbl.bean.Invoice;

public class FieldListenerAdapterITCase {

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testOnCreateDocument() {

    String[] propertyNames = { "ID", "NAME", "OTHER", "status" };
    Object[] state = { "ID_VALUE", "NAME_VALUE", "OTHER_PROPERTY", "STATUS_VALUE" };

    MongoIndexOperations operations = mock(MongoIndexOperations.class);
    SingleTableEntityPersister persister = mock(SingleTableEntityPersister.class);
    EntityMetamodel metaModel = mock(EntityMetamodel.class);
    when(persister.getEntityMetamodel()).thenReturn(metaModel);
    when(metaModel.getPropertyNames()).thenReturn(propertyNames);

    FieldListenerAdapter adapter = new FieldListenerAdapter(operations, "status");

    // test with no args
    Object originEvent = new PostInsertEvent(null, null, null, null, null);
    adapter.onCreateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(0))
        .saveIndex(any(Index.class));

    // test with no args
    originEvent = new PostInsertEvent(new Invoice(), null, null, null, null);
    adapter.onCreateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(0))
        .saveIndex(any(Index.class));

    // test with no args
    originEvent = new PostInsertEvent(new Invoice(), "ID", state, null, null);
    adapter.onCreateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(0))
        .saveIndex(any(Index.class));

    // test with no args
    originEvent = new PostInsertEvent(new Invoice(), "ID", state, null, null);
    adapter.onCreateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(0))
        .saveIndex(any(Index.class));

    // test success
    originEvent = new PostInsertEvent(new Invoice(), "ID", state, persister, null);
    adapter.onCreateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(1))
        .saveIndex(any(Index.class));

    adapter = new FieldListenerAdapter(operations, "status", "OTHER", "ID");
    originEvent = new PostInsertEvent(new Invoice(), "ID", state, persister, null);
    adapter.onCreateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/update")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(4))
        .saveIndex(any(Index.class));// 4 times 1 from the previous test
  }

  @Test
  public void testOnUpdateDocument() {

    String[] propertyNames = { "ID", "NAME", "OTHER", "status" };
    Object[] state = { "ID_VALUE", "NAME_VALUE", "OTHER_PROPERTY", "STATUS_VALUE" };

    MongoIndexOperations operations = mock(MongoIndexOperations.class);
    SingleTableEntityPersister persister = mock(SingleTableEntityPersister.class);
    EntityMetamodel metaModel = mock(EntityMetamodel.class);
    when(persister.getEntityMetamodel()).thenReturn(metaModel);
    when(metaModel.getPropertyNames()).thenReturn(propertyNames);

    FieldListenerAdapter adapter = new FieldListenerAdapter(operations, "status");

    // test with no args
    Object originEvent = new PostUpdateEvent(null, null, null, null, null, null, null);
    adapter.onUpdateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(0))
        .saveIndex(any(Index.class));

    // test with no args
    originEvent = new PostUpdateEvent(new Invoice(), null, null, null, null, null, null);
    adapter.onUpdateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(0))
        .saveIndex(any(Index.class));

    // test with no args
    originEvent = new PostUpdateEvent(new Invoice(), "ID", state, null, null, null, null);
    adapter.onUpdateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(0))
        .saveIndex(any(Index.class));

    // test with no args
    originEvent = new PostUpdateEvent(new Invoice(), "ID", state, null, IntStream.of(0, 1, 2)
        .toArray(), null, null);
    adapter.onUpdateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(0))
        .saveIndex(any(Index.class));

    int[] dirtyProperties = { 3 };
    // test success
    originEvent = new PostUpdateEvent(new Invoice(), "ID", state, null, dirtyProperties, persister, null);
    adapter.onUpdateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/create")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(1))
        .saveIndex(any(Index.class));

    int[] dirtyProperties2 = { 0, 2, 3 };
    adapter = new FieldListenerAdapter(operations, "status", "OTHER", "ID");
    originEvent = new PostUpdateEvent(new Invoice(), "ID", state, null, dirtyProperties2, persister, null);
    adapter.onUpdateDocument(EventBuilder.fromSource(originEvent)
        .topic("persistent/Document/update")
        .origin(originEvent)
        .build());
    Mockito.verify(operations, times(4))
        .saveIndex(any(Index.class));// 4 times 1 from the previous test
  }
}
