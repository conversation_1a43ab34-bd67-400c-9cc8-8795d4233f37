/**
 * 
 */
package com.byzaneo.generix.edocument.service;

import com.byzaneo.amazon.service.S3Service;
import com.byzaneo.commons.service.ConfigurationService;
import com.byzaneo.commons.service.ConfigurationService.ConfigurationKey;
import com.byzaneo.commons.util.FileHelper;
import com.byzaneo.generix.edocument.SafeboxException;
import com.byzaneo.generix.edocument.archive.metadata.*;
import com.byzaneo.generix.edocument.bean.Archive;
import com.byzaneo.generix.edocument.exception.*;
import com.byzaneo.generix.service.S3ResolverService;
import com.byzaneo.xtrade.bean.*;
import org.junit.jupiter.api.*;
import org.mockito.*;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR>
 * @company Generix Group
 * @date 17 avr. 2018
 */
public class SafeboxServiceImplTest {

  @Spy
  @InjectMocks
  private SafeboxServiceImpl safeBox;

  @Mock
  private S3Service amazonS3Service;

  @Mock
  private S3ResolverService s3ResolverService;

  @Mock
  private ConfigurationService configService;

  @Captor
  private ArgumentCaptor<String> captorKey;

  @Captor
  private ArgumentCaptor<String> captorArchiveZip;

  private final String archive = "src/test/resources/data/archives/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip";

  private final String fileNotPresentInsideIndexFile = "src/test/resources/data/archives/not-present-inside-index-file-c51948507.edi";

  @BeforeEach
  public void before() {
    MockitoAnnotations.initMocks(this);
    if (!new File("target/restore/archive/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01").exists()) {
      FileHelper.unzip(new File(archive),
          new File("target/restore/archive/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01"), "");
    }

    doReturn(true).when(amazonS3Service)
        .isS3ArchiveServiceAvailable();
    Mockito.doReturn(amazonS3Service)
        .when(s3ResolverService)
        .initS3Service(Mockito.any());
  }

  @AfterEach
  public void after() {
    FileHelper.deleteQuietly(new File("target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01"));
    FileHelper.deleteQuietly(new File("target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip"));
  }

  @Test
  public void testCreateDocFileFromFileTypeWithFileNotFound() {
    Throwable exception = assertThrows(ArchiveFileNotFoundException.class, () -> {

      FileType type = new FileType();
      type.setURI("fake/file");
      safeBox.createDocumentFileFromFileType(
          new File("target/restore/archiveARC_2018_47bc2c1434059b40a677823e84d677a95634fc01"), type, 0);
    });
    assertTrue(exception.getMessage().contains("Failed to find file fake/file"));
  }

  @Test
  public void testCreateDocFileFromFileTypeWithFile() throws ArchiveFileNotFoundException {
    FileType type = new FileType();
    type.setFormat("text/xml");
    type.setExtension(".xml");
    type.setURI("3946/4cf3d445dbf342dddd58dbd588b7849523ee05cedc2abb254fea1b8c454151ec.xml");
    ExtraInformationType info = new ExtraInformationType();
    info.setExtraInformationDescription("ConverterInvoiceXcbl40Task");
    info.setXMLSchema("Action name");
    type.getExtraInformation()
        .add(info);

    info = new ExtraInformationType();
    info.setExtraInformationDescription("official_index");
    info.setXMLSchema("Description");
    type.getExtraInformation()
        .add(info);

    info = new ExtraInformationType();
    info.setExtraInformationDescription("XCBL");
    info.setXMLSchema("File type");
    type.getExtraInformation()
        .add(info);

    DocumentFile dof = safeBox.createDocumentFileFromFileType(
        new File("target/restore/archive/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01"), type, 0);

    Assertions.assertTrue(dof.getFile()
        .exists() && "official_index".equals(dof.getDescription()) && dof.getType() == com.byzaneo.commons.bean.FileType.XCBL &&
        "ConverterInvoiceXcbl40Task".equals(dof.getActionName()));
  }

  @Test
  public void testGetArchiveId() {
    Assertions.assertEquals("47bc2c1434059b40a677823e84d677a95634fc01",
        safeBox.getArchiveId("ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01"));
  }

  @Test
  public void testGetArchiveIdNotMatchingPattern() {
    Assertions.assertEquals("XXX/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01",
        safeBox.getArchiveId("XXX/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01"));
  }

  @Test
  public void testGetArchiveFromS3DownloadFailed() {
    Throwable exception = assertThrows(ArchiveDownloadFailedException.class, () -> {

      Mockito.doReturn(Boolean.TRUE)
          .when(amazonS3Service)
          .downloadArchive(Mockito.isA(String.class), Mockito.isA(String.class));
      Mockito.doReturn("target/restore")
          .when(safeBox)
          .getSafeboxRetrievePath();
      Archive archive = new Archive();
      archive.setEntityId(3946);
      archive.setArchiveUid("ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip");
      safeBox.getArchiveFromS3(archive, null);
    });
    assertTrue(exception.getMessage().contains("Failed to download archive ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip"));
  }

  @Test
  public void testGetArchiveFromS3() throws IOException, ArchiveDownloadFailedException {
    Mockito.doAnswer(answer -> {
      FileHelper.copyFile(new File(archive),
          new File("target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip"));
      return true;
    })
        .when(amazonS3Service)
        .downloadArchive(Mockito.isA(String.class), Mockito.isA(String.class));
    Mockito.doReturn("target/restore")
        .when(safeBox)
        .getSafeboxRetrievePath();
    FileHelper.copyFile(new File(fileNotPresentInsideIndexFile),
        new File("target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01/not-present-inside-index-file-c51948507.edi"));
    Archive archive = new Archive();
    archive.setEntityId(3946);
    archive.setArchiveUid("ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip");
    Document rslt = safeBox.getArchiveFromS3(archive, null);

    Assertions.assertTrue(rslt != null && rslt.getFiles()
        .size() == 4);
  }

  @Test
  public void testGetArchiveFromS3WithRetry() throws IOException, ArchiveDownloadFailedException {
    FileHelper.unzip(new File(archive),
        new File("target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01"), "");
    FileHelper.deleteQuietly(new File(
        "target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01/3946/4cf3d445dbf342dddd58dbd588b7849523ee05cedc2abb254fea1b8c454151ec.xml"));
    Mockito.doAnswer(answer -> {
      FileHelper.copyFile(new File(archive),
          new File("target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip"));
      return true;
    })
        .when(amazonS3Service)
        .downloadArchive(Mockito.isA(String.class), Mockito.isA(String.class));
    Mockito.doReturn("target/restore")
        .when(safeBox)
        .getSafeboxRetrievePath();
    Archive archive = new Archive();
    archive.setEntityId(3946);
    archive.setArchiveUid("ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip");
    Document rslt = safeBox.getArchiveFromS3(archive, null);

    Document doc = new Document();
    BeanUtils.copyProperties(archive, doc);
    doc.setId(Long.valueOf(archive.getEntityId()
        .toString()));
    doc.setIndexValue(archive);

    Mockito.verify(safeBox)
        .updateArchiveDocWithIndexMetadata(Mockito.any(Document.class), Mockito.any(File.class));
    Assertions.assertTrue(rslt != null && rslt.getFiles()
        .size() == 4);
  }

  @Test
  public void testUpdateArchiveDocWithIndexMetadataFailed() {
    Throwable exception = assertThrows(SafeboxException.class, () -> {

      FileHelper.unzip(new File(archive),
          new File("target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01"), "");
      FileHelper.deleteQuietly(new File(
          "target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01/3946/4cf3d445dbf342dddd58dbd588b7849523ee05cedc2abb254fea1b8c454151ec.xml"));
      Document doc = new Document();
      BeanUtils.copyProperties(archive, doc);
      doc.setId(3946L);
      safeBox.updateArchiveDocWithIndexMetadata(doc, new File(
          "target/restore/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01/47bc2c1434059b40a677823e84d677a95634fc01.xml"));
    });
    assertTrue(exception.getMessage().contains("Failed to update archive document"));
  }

  @Test
  public void testDownloadArchiveOnS3() throws IOException, ArchiveDownloadFailedException {
    Mockito.doReturn(Boolean.TRUE)
        .when(amazonS3Service)
        .downloadArchive(Mockito.isA(String.class), Mockito.isA(String.class));
    safeBox.downloadArchiveOnS3("src/test/resources/data/archives",
        "ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip", null);

    Mockito.verify(amazonS3Service)
        .downloadArchive(captorKey.capture(), captorArchiveZip.capture());
    Assertions.assertTrue("ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip".equals(captorKey.getValue()) &&
        Paths.get("src/test/resources/data/archives/ARC_2018_47bc2c1434059b40a677823e84d677a95634fc01.zip")
            .toString()
            .equals(captorArchiveZip.getValue()));
  }

  @Test
  public void testGetSafeboxRetrievePath() {
    Mockito.doReturn(new File("target/restore"))
        .when(configService)
        .getFile(ConfigurationKey.DATA_DIR, FileHelper.getTempDirectory());
    Assertions.assertEquals(new File("target/restore/arch/retrieve").getAbsolutePath(), safeBox.getSafeboxRetrievePath());
  }
}
