package com.byzaneo.generix.edocument.bean.invoice;

import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getInvoiceTotalFromInvoiceSummary;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxableValueFromInvoiceSummary;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTotalTaxAmountFromInvoiceSummary;
import static org.junit.jupiter.api.Assertions.fail;

import com.byzaneo.commons.io.BeanImportContext;
import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.ui.MissingKeyHandler;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.edocument.api.invoice.InvoiceFooterWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.InvoiceCsvFooter;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import java.math.BigDecimal;

import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class InvoiceFooterWrapperITCase {

  private static MockedStatic<JSFHelper> mockedJSFHelper;

  private static MockedStatic<CsvImportHelper> mockedCsvImportHelper;

  private static InvoiceFooterWrapper instance;

  @BeforeAll
  static void setUpStaticMocks() {
    mockedJSFHelper = Mockito.mockStatic(JSFHelper.class, invocation -> {
      if (invocation.getMethod()
          .getName()
          .equals("getMissingKeyHandler")) {
        return Mockito.mock(MissingKeyHandler.class);
      }
      return invocation.callRealMethod();
    });
    instance = new InvoiceFooterWrapper();
    mockedCsvImportHelper = Mockito.mockStatic(CsvImportHelper.class);
  }

  @AfterAll
  static void tearDownStaticMocks() {
    mockedCsvImportHelper.closeOnDemand();
    mockedJSFHelper.closeOnDemand();
  }

  @Test
  public void testValidateNoHeader() {
    InvoiceCsvFooter footer = new InvoiceCsvFooter();

    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(null);

    try {
      instance.validate(footer, new BeanImportContext());
      fail("expected validation exception");
    }
    catch (PropertiesException e) {
      Exception serviceException = e.getExceptions()
          .get("header");
      Assertions.assertEquals("Aucune entête liée à cet élément", serviceException.getMessage());
    }
  }

  @Test
  public void testValidate() {
    InvoiceCsvFooter footer = new InvoiceCsvFooter();
    footer.setTaxableAmount(new BigDecimal("123.4534"));
    footer.setTaxAmount(new BigDecimal("459.66"));
    footer.setTotalAmount(new BigDecimal("9566"));

    Invoice invoice = new Invoice();
    mockedCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
        .thenReturn(invoice);

    try {
      instance.validate(footer, new BeanImportContext());

      // Check
      Assertions.assertEquals(new BigDecimal("9566.00"), getInvoiceTotalFromInvoiceSummary(invoice));
      Assertions.assertEquals(new BigDecimal("9566.00"), invoice.getInvoiceSummary()
          .getInvoiceTotals()
          .getInvoiceSubTotal()
          .getMonetaryAmount()
          .getValue());
      Assertions.assertEquals(new BigDecimal("123.45"), getTaxableValueFromInvoiceSummary(invoice));
      Assertions.assertEquals(new BigDecimal("459.66"), getTotalTaxAmountFromInvoiceSummary(invoice));

    }
    catch (PropertiesException e) {
      fail("no validation exception expected");
    }
    catch (Exception e) {
      fail("exception " + e.getMessage());
    }
  }
}
