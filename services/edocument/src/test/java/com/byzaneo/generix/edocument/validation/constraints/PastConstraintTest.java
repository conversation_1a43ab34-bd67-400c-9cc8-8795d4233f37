package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PastConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/DateBeforeToday.xml"), false);

  @Test
  public void testValid() {
    PastConstraint instance = new PastConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/dates/date");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testInvalid() {
    PastConstraint instance = new PastConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/dates/invalidDate");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid1() {
    PastConstraint instance = new PastConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/dates/invalidDate1");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid2() {
    PastConstraint instance = new PastConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/dates/invalidDate2");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid3() {
    PastConstraint instance = new PastConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/dates/invalidDate3");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }
}
