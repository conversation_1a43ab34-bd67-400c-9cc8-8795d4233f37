package com.byzaneo.generix.edocument.bean.ordrsp;

import static org.apache.commons.lang3.time.DateUtils.parseDate;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.mockStatic;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.bean.PropertyDescriptor;
import com.byzaneo.commons.service.BeanService;
import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.generix.edocument.api.ordrsp.OrdrspSccWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.OrdrspCsvScc;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.OrderResponse;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.ListOfOrderResponseItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.OrderResponseDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.SplitQuantityType;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class OrdrspSccWrapperTest {

  @InjectMocks
  private OrdrspSccWrapper instance;

  @Mock
  private BeanService beanService;

  @BeforeEach
  public void before() {
    Mockito.spy(CsvImportHelper.class);
    instance = new OrdrspSccWrapper();

    BeanDescriptor descriptor = new BeanDescriptor();
    PropertyDescriptor property = new PropertyDescriptor("deliveryLineNumber");
    property.setValue("0010");

    try {
      Field wrappedField = instance.getClass()
          .getSuperclass()
          .getDeclaredField("wrapped");
      wrappedField.setAccessible(true);
      wrappedField.set(instance, descriptor);
    } catch (Exception e) {
      fail("no exception expected");
    }
  }

  @AfterEach
  public void after() {
    try {
      Field wrappedField = instance.getClass()
          .getSuperclass()
          .getDeclaredField("wrapped");
      wrappedField.setAccessible(false);

    } catch (Exception e) {
      fail("no exception expected");
    }
  }

  @Test
  public void testValidateNoHeader() {
    OrdrspCsvScc bean = new OrdrspCsvScc();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable())
          .thenReturn(null);

      try {
        instance.validate(bean, null);
        // fail("expected validation exception");
      } catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("header");
        Assertions.assertEquals("Aucune entête liée à cet élément", serviceException.getMessage());
      }
    }
  }

  @Test
  public void testValidate() {
    OrdrspCsvScc bean = new OrdrspCsvScc();
    bean.setOrderLineNumberParent(10);
    bean.setConfirmedQuantity(new BigDecimal("40"));
    bean.setDeliveryLineNumber(1);

    OrderResponse response = new OrderResponse();
    response.setOrderResponseDetail(new OrderResponseDetailType());
    response.getOrderResponseDetail()
        .setListOfOrderResponseItemDetail(new ListOfOrderResponseItemDetailType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
        invocation -> {
          if (invocation.getMethod().getName().equals("getImportedIndexable")) {
            return response;
          }
          return invocation.callRealMethod();
        })) {

      try {
        bean.setDeliveryDate(parseDate("01/12/2015", "dd/MM/yyyy"));

        instance.validate(bean, null);

        SplitQuantityType qty = response.getOrderResponseDetail()
            .getListOfOrderResponseItemDetail()
            .getOrderResponseItemDetail()
            .get(0)
            .getOriginalItemDetailWithChanges()
            .getDeliveryDetail()
            .getListOfSplitQuantity()
            .getSplitQuantity()
            .get(0);
        Assertions.assertEquals(new BigDecimal("40"), qty.getSplitQuantity()
            .getQuantityValue()
            .getValue());
        Assertions.assertEquals(parseDate("01/12/2015", "dd/MM/yyyy"),
            qty.getEstimatedDeliveryDate());
      } catch (PropertiesException e) {

      } catch (ParseException e) {
        // TODO Auto-generated catch block
        e.printStackTrace();
      }
    }
  }
}
