package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.VENDOR_FREIGHT;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.addAllOrChHeader;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.addAllowOrChargeLine;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.buildAllowOrCharge;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.clearTypeOfAllowOrChargeType;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.computeAllowanceOrChargeOnSelectedDetail;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.computeTaxOnAllowanceOrCharge;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.createAllowOrCharge;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.getAllowOrChargeFromSelectedDetail;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.getAllowOrChargeValue;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.getAmountVAT;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.getFirstTax;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.removeAllOrChHeader;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.removeAllowOrChargeLine;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.setAllowOrChargeFromSelectedDetail;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.setMonetaryValue;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.setPercentValue;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.updateIndicatorCodedOfAllowOrCharge;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.buildInvoiceItemDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setQuantity;
import static com.byzaneo.generix.edocument.util.InvoiceTaxXcblHelper.createInvoiceTaxtype;
import static com.byzaneo.generix.edocument.util.TaxXcblHelper.TAX_PRECISION;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.MONETARY_AMOUNT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.PERCENT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_ALLOWANCE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_CHARGE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxCategoryCodeType.OTHER;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxCategoryCodeType.STANDARD_RATE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxFunctionQualifierCodeType.TAX;
import static java.math.BigDecimal.TEN;
import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static java.math.RoundingMode.HALF_EVEN;
import static java.util.Optional.of;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.AllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceAllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceMonetaryValueType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceTaxType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PercentType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PercentageAllowanceOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TypeOfAllowanceOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceItemDetailType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> Lemesle <<EMAIL>>
 * @company Byzaneo
 * @date 09 April, 2018
 */
public class AllowOrChargeXcblHelperTest {
  @Test
  public void computeAllowanceOrChargeOnSelectedDetailTest() {
    AllowOrChargeType allowOrCharge = buildAllowOrCharge();
    BigDecimal netPriceComputed = valueOf(24).setScale(2, HALF_EVEN);
    // Allowance
    allowOrCharge.setIndicatorCoded(LINE_ITEM_ALLOWANCE);
    allowOrCharge.setBasisCoded(MONETARY_AMOUNT);
    setMonetaryValue(allowOrCharge, TEN);
    assertEquals(valueOf(14).setScale(2, HALF_EVEN), computeAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed));
    allowOrCharge.setBasisCoded(PERCENT);
    setPercentValue(allowOrCharge, TEN);
    assertEquals(valueOf(21.6).setScale(2, HALF_EVEN), computeAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed));
    // Charge
    allowOrCharge.setIndicatorCoded(LINE_ITEM_CHARGE);
    allowOrCharge.setBasisCoded(MONETARY_AMOUNT);
    setMonetaryValue(allowOrCharge, TEN);
    assertEquals(valueOf(34).setScale(2, HALF_EVEN), computeAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed));
    allowOrCharge.setBasisCoded(PERCENT);
    setPercentValue(allowOrCharge, TEN);
    assertEquals(valueOf(26.4).setScale(2, HALF_EVEN), computeAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed));
  }

  @Test
  public void computeTaxOnAllowanceOrChargeTest() {
    AllowOrChargeType allowOrCharge = buildAllowOrCharge();
    InvoiceItemDetailType detail = buildInvoiceItemDetail();
    setQuantity(detail, valueOf(2));
    setCalculationNet(detail, valueOf(14), 6, HALF_EVEN);
    detail.getInvoicePricingDetail()
        .getTax()
        .add(createInvoiceTaxtype(getCalculationNet(detail), of(valueOf(5.50)), STANDARD_RATE));
    BigDecimal netPriceComputed = getCalculationNet(detail).orElse(ZERO);
    // Allowance Monetary
    allowOrCharge.setBasisCoded(MONETARY_AMOUNT);
    allowOrCharge.setIndicatorCoded(IndicatorCodeType.LINE_ITEM_CHARGE);
    setMonetaryValue(allowOrCharge, TEN);
    computeTaxOnAllowanceOrCharge(allowOrCharge, detail, netPriceComputed);
    assertEquals(valueOf(5.50).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxPercent()
        .getValue());
    assertEquals(valueOf(48).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxableAmount());
    assertEquals(valueOf(2.64).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxAmount());
    // Allowance Percent
    allowOrCharge.setBasisCoded(PERCENT);
    setPercentValue(allowOrCharge, TEN);
    computeTaxOnAllowanceOrCharge(allowOrCharge, detail, netPriceComputed);
    assertEquals(valueOf(5.50).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxPercent()
        .getValue());
    assertEquals(valueOf(30.8).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxableAmount());
    assertEquals(valueOf(1.694).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxAmount());

    // Allowance Monetary
    allowOrCharge.setBasisCoded(MONETARY_AMOUNT);
    allowOrCharge.setIndicatorCoded(IndicatorCodeType.LINE_ITEM_ALLOWANCE);
    setMonetaryValue(allowOrCharge, TEN);
    computeTaxOnAllowanceOrCharge(allowOrCharge, detail, netPriceComputed);
    assertEquals(valueOf(5.50).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxPercent()
        .getValue());
    assertEquals(valueOf(8).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxableAmount());
    assertEquals(valueOf(0.44).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxAmount());
    // Allowance Percent
    allowOrCharge.setBasisCoded(PERCENT);
    setPercentValue(allowOrCharge, TEN);
    computeTaxOnAllowanceOrCharge(allowOrCharge, detail, netPriceComputed);
    assertEquals(valueOf(5.50).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxPercent()
        .getValue());
    assertEquals(valueOf(25.2).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxableAmount());
    assertEquals(valueOf(1.386).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxAmount());
  }

  @Test
  public void testAllowOrChargeFromSelectedDetail() {
    InvoiceAllowOrChargeType allowOrChargeType1 = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType allowOrChargeType2 = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType allowOrChargeType3 = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType allowOrChargeType4 = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    allowOrChargeType1.setIndicatorCoded(IndicatorCodeType.OTHER);
    allowOrChargeType2.setIndicatorCoded(IndicatorCodeType.CHARGE);
    allowOrChargeType3.setIndicatorCoded(IndicatorCodeType.CHARGE);
    allowOrChargeType4.setIndicatorCoded(IndicatorCodeType.OTHER);
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(allowOrChargeType1, null, BigDecimal.TEN, BigDecimal.ZERO);
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(allowOrChargeType2, null, BigDecimal.ZERO, BigDecimal.valueOf(20));
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(allowOrChargeType3, null, BigDecimal.ZERO, BigDecimal.TEN);
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(allowOrChargeType4, null, BigDecimal.valueOf(5), BigDecimal.ZERO);

    InvoiceItemDetailType detail = buildInvoiceItemDetail();
    setAllowOrChargeFromSelectedDetail(detail, Arrays.asList(allowOrChargeType1, allowOrChargeType2));
    assertEquals(2, getAllowOrChargeFromSelectedDetail(detail).size());

    addAllowOrChargeLine(allowOrChargeType3, true, detail);
    List<InvoiceAllowOrChargeType> allowOrChargeFromSelectedDetail = getAllowOrChargeFromSelectedDetail(detail);
    assertEquals(2, allowOrChargeFromSelectedDetail.size());
    assertEquals(BigDecimal.TEN, allowOrChargeFromSelectedDetail.get(1)
        .getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercent()
        .getValue());

    addAllowOrChargeLine(allowOrChargeType4, false, detail);
    assertEquals(3, getAllowOrChargeFromSelectedDetail(detail).size());

    removeAllowOrChargeLine(allowOrChargeType4, detail);
    allowOrChargeFromSelectedDetail = getAllowOrChargeFromSelectedDetail(detail);
    assertEquals(2, allowOrChargeFromSelectedDetail.size());
    for (InvoiceAllowOrChargeType allowOrChargeType : allowOrChargeFromSelectedDetail) {
      assertNotEquals(allowOrChargeType4, allowOrChargeType);
    }
  }

  @Test
  public void testInitAllowanceChargeWithMonetaryAmount() {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    allowOrChargeType.setIndicatorCoded(IndicatorCodeType.OTHER);
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(allowOrChargeType, null, BigDecimal.TEN, BigDecimal.ZERO);
    assertEquals(BasisCodeType.MONETARY_AMOUNT, allowOrChargeType.getBasisCoded());
    InvoiceMonetaryValueType monetaryValue = allowOrChargeType.getTypeOfAllowanceOrCharge()
        .getMonetaryValue();
    assertEquals(BigDecimal.TEN, monetaryValue.getMonetaryAmount()
        .getValue());
    assertEquals(CurrencyCodeType.EUR, monetaryValue.getCurrency()
        .getCurrencyCoded());

    allowOrChargeType.getAllowanceOrChargeDescription()
        .getListOfDescription()
        .setValue("FRAIS DE LIVRAISON (Frais de transport)");
    assertEquals("FRAIS DE LIVRAISON (Frais de transport) : 10", InvoiceAllowOrChargeXcblHelper.inline(allowOrChargeType));
    allowOrChargeType.setIndicatorCoded(IndicatorCodeType.CHARGE);
    assertEquals("FRAIS DE LIVRAISON (Frais de transport) : +10", InvoiceAllowOrChargeXcblHelper.inline(allowOrChargeType));
  }

  @Test
  public void testInitAllowanceChargeWithPercentValue() {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    allowOrChargeType.setIndicatorCoded(IndicatorCodeType.OTHER);
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(allowOrChargeType, null, BigDecimal.ZERO, BigDecimal.TEN);
    PercentageAllowanceOrChargeType percentage = allowOrChargeType.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge();
    assertEquals(BasisCodeType.PERCENT, allowOrChargeType.getBasisCoded());
    assertEquals(BigDecimal.TEN, percentage.getPercent()
        .getValue());
    assertEquals(CurrencyCodeType.EUR, percentage.getPercentageMonetaryValue()
        .getCurrency()
        .getCurrencyCoded());

    allowOrChargeType.getAllowanceOrChargeDescription()
        .getListOfDescription()
        .setValue("REMISE PROMOTIONNELLE (Remise promotionnelle)");
    assertEquals("REMISE PROMOTIONNELLE (Remise promotionnelle) : 10%", InvoiceAllowOrChargeXcblHelper.inline(allowOrChargeType));
    allowOrChargeType.setIndicatorCoded(IndicatorCodeType.ALLOWANCE);
    assertEquals("REMISE PROMOTIONNELLE (Remise promotionnelle) : -10%", InvoiceAllowOrChargeXcblHelper.inline(allowOrChargeType));
  }

  @Test
  public void testGetAllowOrChargeValue() {
    AllowOrChargeType allowOrChargeType = createAllowOrCharge();
    allowOrChargeType.setBasisCoded(BasisCodeType.PERCENT);
    setPercentValue(allowOrChargeType, TEN);
    assertEquals(valueOf(120).setScale(2, HALF_EVEN), getAllowOrChargeValue(allowOrChargeType, BigDecimal.valueOf(1200), 2));

    allowOrChargeType.setBasisCoded(BasisCodeType.MONETARY_AMOUNT);
    setMonetaryValue(allowOrChargeType, BigDecimal.valueOf(150));
    assertEquals(valueOf(150).setScale(1, HALF_EVEN), getAllowOrChargeValue(allowOrChargeType, BigDecimal.valueOf(1000), 1));

    allowOrChargeType.getTax()
        .get(0)
        .setTaxCategoryCodedOther("");
    assertEquals(valueOf(150).setScale(1, HALF_EVEN), getAllowOrChargeValue(allowOrChargeType, BigDecimal.valueOf(1000), 1));
  }

  @Test
  public void testWithTva0() {
    AllowOrChargeType allowOrChargeType = createAllowOrChargeWithTva("0.0");
    setPercentValue(allowOrChargeType, TEN);

    // Execute getAllowOrChargeValue() two times because there is a wrong calculation of charge produces when execute calculation several
    // times (AIO-8884)
    BigDecimal allowOrChargeValue = getAllowOrChargeValue(allowOrChargeType, BigDecimal.valueOf(100), 2);
    assertEquals(valueOf(10).setScale(2, HALF_EVEN), allowOrChargeValue);
    allowOrChargeValue = getAllowOrChargeValue(allowOrChargeType, BigDecimal.valueOf(100), 2);
    assertEquals(valueOf(10).setScale(2, HALF_EVEN), allowOrChargeValue);
  }

  @Test
  public void testWithTva19_6() {
    AllowOrChargeType allowOrChargeType = createAllowOrChargeWithTva("19.6");
    setPercentValue(allowOrChargeType, TEN);

    // Execute getAllowOrChargeValue() two times because there is a wrong calculation of charge produces when execute calculation several
    // times (AIO-8884)
    BigDecimal allowOrChargeValue = getAllowOrChargeValue(allowOrChargeType, BigDecimal.valueOf(100), 2);
    assertEquals(valueOf(10).setScale(2, HALF_EVEN), allowOrChargeValue);
    allowOrChargeValue = getAllowOrChargeValue(allowOrChargeType, BigDecimal.valueOf(100), 2);
    assertEquals(valueOf(10).setScale(2, HALF_EVEN), allowOrChargeValue);
  }

  public static AllowOrChargeType createAllowOrChargeWithTva(String percentTva) {
    AllowOrChargeType allowOrChargeType = new AllowOrChargeType();
    allowOrChargeType.setBasisCoded(PERCENT);

    TypeOfAllowanceOrChargeType typeOfAllowanceOrCharge = new TypeOfAllowanceOrChargeType();
    typeOfAllowanceOrCharge.setPercentageAllowanceOrCharge(AllowOrChargeXcblHelper.getPercentageAllowanceOrChargeType(ZERO));
    allowOrChargeType.setTypeOfAllowanceOrCharge(typeOfAllowanceOrCharge);
    List<TaxType> tax = allowOrChargeType.getTax();
    tax.add(getTVA(percentTva));

    return allowOrChargeType;
  }

  private static TaxType getTVA(String percent) {
    TaxType tax = new TaxType();

    tax.setTaxCategoryCoded(OTHER);
    tax.setTaxCategoryCodedOther(percent.equals("0.0") ? null : percent);
    tax.setTaxFunctionQualifierCoded(TAX);
    tax.setTaxTypeCoded("ValueAddedTax");
    tax.setTaxPercent(new PercentType());

    return tax;
  }

  @Test
  public void testUpdateIndicatorCodedOfAllowOrCharge() {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    allowOrChargeType.setIndicatorCoded(IndicatorCodeType.CHARGE);

    updateIndicatorCodedOfAllowOrCharge(allowOrChargeType, "Allowance");
    assertEquals(IndicatorCodeType.ALLOWANCE, allowOrChargeType.getIndicatorCoded());
    updateIndicatorCodedOfAllowOrCharge(allowOrChargeType, "LineItemAllowance");
    assertEquals(IndicatorCodeType.LINE_ITEM_ALLOWANCE, allowOrChargeType.getIndicatorCoded());
    updateIndicatorCodedOfAllowOrCharge(allowOrChargeType, "Adjustment");
    assertEquals(IndicatorCodeType.ADJUSTMENT, allowOrChargeType.getIndicatorCoded());
    updateIndicatorCodedOfAllowOrCharge(allowOrChargeType, "Other");
    assertEquals(IndicatorCodeType.OTHER, allowOrChargeType.getIndicatorCoded());
    updateIndicatorCodedOfAllowOrCharge(allowOrChargeType, null);
    assertEquals(IndicatorCodeType.OTHER, allowOrChargeType.getIndicatorCoded());
  }

  @Test
  public void testAddAllOrChHeaderAndRemove() {
    InvoiceHeaderType invoiceHeaderType = new InvoiceHeaderType();
    InvoiceAllowOrChargeType allowOrChargeType = addAllOrChHeader(invoiceHeaderType, BigDecimal.valueOf(10), "shipping", VENDOR_FREIGHT, 2,
        true);
    List<InvoiceTaxType> taxes = allowOrChargeType.getTax();
    assertEquals(1, taxes.size());
    assertEquals(BigDecimal.TEN.setScale(2, HALF_EVEN), taxes.get(0)
        .getTaxableAmount());
    assertEquals(BigDecimal.ZERO.setScale(2, HALF_EVEN), taxes.get(0)
        .getTaxPercent()
        .getValue());

    allowOrChargeType = addAllOrChHeader(invoiceHeaderType, BigDecimal.valueOf(50), "shipping", VENDOR_FREIGHT, 2, false);
    taxes = allowOrChargeType.getTax();
    assertEquals(1, taxes.size());
    assertEquals(BigDecimal.valueOf(50)
        .setScale(2, HALF_EVEN),
        taxes.get(0)
            .getTaxableAmount());
    assertEquals(BigDecimal.valueOf(20)
        .setScale(2, HALF_EVEN),
        taxes.get(0)
            .getTaxPercent()
            .getValue());

    removeAllOrChHeader(invoiceHeaderType, VENDOR_FREIGHT);
    assertEquals(0, invoiceHeaderType.getInvoiceAllowancesOrCharges()
        .getAllowOrCharge()
        .size());
  }

  @Test
  public void testGetAmountVAT() {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    allowOrChargeType.setBasisCoded(BasisCodeType.MONETARY_AMOUNT);
    InvoiceAllowOrChargeXcblHelper.setMonetaryValue(allowOrChargeType, BigDecimal.valueOf(150));
    allowOrChargeType.getTax()
        .get(0)
        .getTaxPercent()
        .setValue(BigDecimal.valueOf(20));

    Optional<BigDecimal> amountVAT = getAmountVAT(allowOrChargeType);
    assertEquals(BigDecimal.valueOf(30), amountVAT.get());
  }

  @Test
  public void testClearTypeOfAllowOrChargeType() {
    List<AllowOrChargeType> items = new ArrayList<>();
    AllowOrChargeType item1 = createAllowOrCharge();
    AllowOrChargeType item2 = createAllowOrCharge();
    PercentageAllowanceOrChargeType percentageAllowanceOrChargeType = new PercentageAllowanceOrChargeType();
    item1.setBasisCoded(PERCENT);
    setMonetaryValue(item1, BigDecimal.valueOf(150));
    item2.setBasisCoded(MONETARY_AMOUNT);
    item2.getTypeOfAllowanceOrCharge()
        .setPercentageAllowanceOrCharge(percentageAllowanceOrChargeType);
    items.add(item1);
    items.add(item2);

    clearTypeOfAllowOrChargeType(items);
    assertTrue(items.contains(item1) && items.contains(item2));
    assertNull(item1.getTypeOfAllowanceOrCharge()
        .getMonetaryValue());
    assertNull(item2.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge());
  }
}
