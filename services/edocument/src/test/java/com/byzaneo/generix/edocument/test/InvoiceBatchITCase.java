package com.byzaneo.generix.edocument.test;

import static com.byzaneo.commons.util.GsonHelper.pretty;
import static java.lang.String.format;
import static java.lang.System.currentTimeMillis;
import static org.apache.commons.lang3.time.DurationFormatUtils.formatDurationHMS;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.test.SpringJUnitClassListener;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.generix.edocument.bean.Invoice;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.service.DocumentStatusService;
import com.byzaneo.xtrade.util.DocumentHelper;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date 23 oct. 2009
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {
    "classpath:/gnx-edoc-test.beans.xml"}, loader = SystemPropertyContextLoader.class)
public class InvoiceBatchITCase implements SpringJUnitClassListener {

  private static final int BATCH_SIZE = 100;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  @Autowired
  @Qualifier(DocumentStatusService.SERVICE_NAME)
  private DocumentStatusService statusService;

  private static List<Document> documents = new ArrayList<>(BATCH_SIZE);

  @BeforeEach
  @Override
  public void beforeClass() throws Exception {
    DocumentHelper.populateDocumentStatusEntityTable(statusService);
    long time = currentTimeMillis();
    for (int i = 1; i <= BATCH_SIZE; i++) {
      Invoice invoice = new Invoice();
      invoice.setNumber(format("%06d", i));
      invoice.setArchId(format("AID%06d", i));
      invoice.setArchName(format("Name%06d", i));
      invoice.setMsgRef(format("MR%06d", i));
      invoice.setIchgId(format("I%06d", i));
      invoice.setDate(new Date(time / ((i % 7) + 1)));
      invoice.setStoredDate(new Date(time / ((i % 3) + 1)));
      invoice.setDematState(i % 5 == 0 ? "DEMAT_CORRECTED" : "DEMAT_OK");
      invoice.setExtState(i % 3 == 0 ? "VALIDATED" : "PENDING");
      invoice.setStoredState(i % 3 == 0 ? "CDC_ARF" : "STORED");
      invoice.setTotal(i * 34d);
      invoice.setFormat(format("F%02d", i % 3));
      invoice.setVersion("3.0");
      invoice.setText01(format("Txt%03d", i % 7));
      invoice.setFloat01(i * 2d);
      invoice.setDate(new Date(time / ((i % 5) + 1)));
      invoice.setFromName(format("From%d", i % 3));
      invoice.setOwnersName(format("Owner%d", i % 5));
      invoice.setToName(format("To%d", i % 7));
      Document document = new Document(
          format("Ref%06d", i),
          format("%09d", i % 5),
          i % 2 == 0 ? "INVOIC" : "DEBADV",
          format("%09d", i % 3),
          format("%09d", i % 7),
          (i % 2 == 0 ? DocumentStatus.PENDING : DocumentStatus.APPROVED));
      if (i % 2 == 0) {
        document.addFile(
            new DocumentFile(new File("/C:/dev/tmp/data/gnx/in/imv3/META_9952667821_2.xml"),
                FileType.XML, "Test", null, document, null));
        document.addFile(
            new DocumentFile(new File("/C:/dev/tmp/data/gnx/in/imv3/ARC_9952667821_2.zip"),
                FileType.ARCHIVE, "Test", null,
                document, null));
      } else {
        document.addFile(
            new DocumentFile(new File("/C:/dev/tmp/data/gnx/in/imv3/META_NUMDOC1_1.xml"),
                FileType.XML, "Test", null, document, null));
        document.addFile(
            new DocumentFile(new File("/C:/dev/tmp/data/gnx/in/imv3/ARC_NUMDOC1_1.zip"),
                FileType.ARCHIVE, "Test", null, document, null));
      }
      document.setIndexValue(invoice);
      documents.add(document);
    }
    final long start = currentTimeMillis();
    documents = this.documentService.saveDocuments(documents);
    System.err.printf("%s invoice documents inserted in: %s\n", BATCH_SIZE,
        formatDurationHMS(currentTimeMillis() - start));
  }

  @Override
  public void afterClass() throws Exception {
    final long start = currentTimeMillis();
    this.documentService.removeDocuments(documents);
    System.err.printf("%s invoice documents removed in: %s\n", BATCH_SIZE,
        formatDurationHMS(currentTimeMillis() - start));
  }

  @Test
  public void testGetIndexableInvoice() throws Exception {
    Invoice invoice = this.documentService.getIndexable(documents.get(BATCH_SIZE / 2));
    printInvoice(invoice);
    assertNotNull(invoice);
  }

  @Test
  public void testFindIndexables() throws Exception {
    Page<Invoice> invoices = this.documentService.<Invoice>searchIndexables(Invoice.class,
        "{$and: [{from:'000000001'},{to:'000000001'}]}",
        -1, PageRequest.of(0, 5, Sort.by(Direction.DESC, "reference")));
    Assertions.assertTrue(invoices.getNumberOfElements() > 0);
    for (Invoice invoice : invoices) {
      printInvoice(invoice);
    }
  }

  private void printInvoice(Invoice invoice) throws IOException {
    pretty(invoice);
  }

}
