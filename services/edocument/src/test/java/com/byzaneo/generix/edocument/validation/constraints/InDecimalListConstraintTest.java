package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.*;

public class InDecimalListConstraintTest {
  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/InDecimalListConstraintTest.xml"),
      false);

  @Test
  public void testValidNumbersAsArgs() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/validNumbers/argsNumbers/number",
        Arrays.array("20", "20.5"));
    assertTrue(inDecimalListConstraint.isValid());
  }
  @Test
  public void testValidNumbersFromFile() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/validNumbers/fileNumbers/number",
        Arrays.array("inDecimalListTest"));
    assertTrue(inDecimalListConstraint.isValid());
  }

  @Test
  public void testInvalidNumbersAsArgs_1() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/invalidNumbers/argsNumbers/negativeDecimalNumber",
        Arrays.array("15", "15.05"));
    assertFalse(inDecimalListConstraint.isValid());
  }

  @Test
  public void testInvalidNumbersAsArgs_2() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/invalidNumbers/argsNumbers/notDecimalNumber",
        Arrays.array("13", "13.0"));
    assertFalse(inDecimalListConstraint.isValid());
  }

  @Test
  public void testInvalidNumbersAsArgs_3() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/invalidNumbers/argsNumbers/decimalNumber",
        Arrays.array("5", "5.0"));
    assertFalse(inDecimalListConstraint.isValid());
  }

  @Test
  public void testInvalidNumbersAsArgs_4() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/invalidNumbers/argsNumbers/positiveDecimalNumber",
        Arrays.array("-15", "-15.0"));
    assertFalse(inDecimalListConstraint.isValid());
  }

  @Test
  public void testInvalidNumbersFromFile_1() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/invalidNumbers/fileNumbers/decimalNumber",
        Arrays.array("inDecimalListTest"));
    assertFalse(inDecimalListConstraint.isValid());
  }

  @Test
  public void testInvalidNumbersFromFile_2() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/invalidNumbers/fileNumbers/notDecimalNumber",
        Arrays.array("inDecimalListTest"));
    assertFalse(inDecimalListConstraint.isValid());
  }

  @Test
  public void testInvalidNumbersFromFile_3() {
    InDecimalListConstraint inDecimalListConstraint = createInDecimalList("//decimalNumbers/invalidNumbers/fileNumbers/invalidDecimalNumber",
        Arrays.array("inDecimalListTest"));
    assertFalse(inDecimalListConstraint.isValid());
  }

  private InDecimalListConstraint createInDecimalList(String nodePath, String[] params) {
    InDecimalListConstraint inDecimalListConstraint = new InDecimalListConstraint();
    inDecimalListConstraint.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, nodePath);
    inDecimalListConstraint.setNodes(nodes);
    inDecimalListConstraint.setParams(params);
    return inDecimalListConstraint;
  }

}
