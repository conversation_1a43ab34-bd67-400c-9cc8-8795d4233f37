package com.byzaneo.generix.edocument.listener;

import static com.byzaneo.xtrade.api.DocumentStage.UNDEFINED;
import static com.byzaneo.xtrade.api.DocumentStatus.ACCEPTED;
import static com.byzaneo.xtrade.api.DocumentStatus.PENDING;
import static com.byzaneo.xtrade.api.DocumentStatus.SENT;
import static com.byzaneo.xtrade.api.DocumentType.INVOIC;
import static com.byzaneo.xtrade.util.DocumentBuilder.createDocument;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.List;

import com.byzaneo.xtrade.service.DocumentStatusService;
import com.byzaneo.xtrade.util.DocumentHelper;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import com.byzaneo.commons.test.*;
import com.byzaneo.generix.edocument.service.EDocumentService;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentService;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @since AIO-7255
 */
@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = { "classpath:/gnx-edoc-test.beans.xml" }, loader = SystemPropertyContextLoader.class)
@Disabled
// TODO Fix me
public class ListenerTest{

  @Autowired
  private EDocumentService eDocumentService;

  @Autowired
  private DocumentService documentService;

  private static Document document;

  @BeforeAll
  public static void beforeClass(@Autowired DocumentService documentService) {
    if (document != null)
      return;

    // creates document
    document = documentService.saveDocument(createDocument()
        .reference("ListenerTest")
        .owners("owner_code")
        .from("from_code")
        .to("to_code")
        .status(PENDING.name())
        .stage(UNDEFINED)
        .subtype("sub_type")
        .type(INVOIC.toString())
        .build());
  }


  @AfterAll
  public static void afterClass(@Autowired DocumentService documentService) throws Exception {
    documentService.removeDocument(document);
    document = null;
  }

  @Test
  public final void testFieldStatus() {
    // updates document
    document.setStatusWithEnumValue(ACCEPTED);
    document = documentService.saveDocument(document);

    document.setReference("ref_updated");
    document = documentService.saveDocument(document);

    document.setStatusWithEnumValue(SENT);
    document = documentService.saveDocument(document);

    final IndexableDocument indexable = new IndexableDocument();
    indexable.setEntityId(document.getId());
    final List<FieldEventEntry> statuses = this.eDocumentService.searchStatusEvents(indexable);
    // System.out.println(statuses);
    assertEquals(2, statuses.size());
  }

  // @Test
  // public final void testDummy() {
  // String username = "john.doe";
  // com.byzaneo.security.bean.User user = new com.byzaneo.security.bean.User(username, "pwd", "John Doe", "<EMAIL>");
  // user.setPrimaryGroup(new Partner("MyPartner"));
  // getContext().setAuthentication(new TestingAuthenticationToken(new UserDetails(user, null), username));
  //
  // Document document = this.documentService.getDocument(6675L);
  // document.setStatus(TO_CORRECT);
  // document = documentService.saveDocument(document);
  // document.setStatus(INVOICED);
  // document = documentService.saveDocument(document);
  // document.setStatus(REFUSED_MANUALLY);
  // documentService.saveDocument(document);
  //
  // getContext().setAuthentication(null);
  // }
}
