package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.applyTaxOnInvoiceItemDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.buildInvoiceItemDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getAmountHT;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getAmountVAT;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getCalculationGross;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.populateInvoiceItemDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.populateItemWithAllowanceOrCharge;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.populateItemWithTax;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationGross;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getBuyerPartID;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setBuyerPartID;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getSellerPartID;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setSellerPartID;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getBuyerOrderNumber;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setBuyerOrderNumber;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getAsnNumber;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setAsnNumber;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getItemDesription;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getStandardProductIdentifier;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setStandardProductIdentifier;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static com.byzaneo.generix.edocument.util.XcblHelper.BigDecimal_CENT;
import static com.byzaneo.generix.service.repository.util.product.TypeAttributeValue.AMOUNT;
import static java.math.RoundingMode.HALF_EVEN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.math.BigDecimal;
import java.util.*;

import com.byzaneo.generix.service.repository.bean.*;
import com.byzaneo.generix.service.repository.util.product.*;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceItemDetailType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class InvoiceItemDetailXcblHelperTest {

  private InvoiceItemDetailType itemDetail;

  private int detailPrecision = 6;

  private int amountPrecision = 2;

  private Map<String, InvoiceTaxSummaryType> taxSummaries;

  /*
   * -- COMPUTE --
   */

  @BeforeEach
  public void before() {
    itemDetail = InvoiceItemDetailXcblHelperMockTest.createItem(BigDecimal.TEN, detailPrecision);
    taxSummaries = new TreeMap<>();
  }

  @AfterEach
  public void tearDown() {
    itemDetail = null;
    taxSummaries = null;
  }
  
  @Test
  public void testGetBuyerOrderNumber() {
    assertEquals(null, getBuyerOrderNumber(itemDetail)
        .getValue());
    assertNotNull(itemDetail.getInvoiceBaseItemDetail()
        .getLineItemReferences()
        .getPurchaseOrderReference()
        .getBuyerOrderNumber());
    
    setBuyerOrderNumber("Order123", itemDetail);
    assertNotNull(getBuyerOrderNumber(itemDetail)
        .getValue());
    assertEquals("Order123", getBuyerOrderNumber(itemDetail)
        .getValue());
  }
  
  @Test
  public void testGetStandardProductIdentifier() {
    assertEquals(null, getStandardProductIdentifier(itemDetail)
        .getValue());
    assertNotNull(itemDetail.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getStandardPartNumber()
        .getProductIdentifier());

    setStandardProductIdentifier("ID123", itemDetail);
    assertNotNull(getStandardProductIdentifier(itemDetail)
        .getValue());
    assertEquals("ID123", getStandardProductIdentifier(itemDetail)
        .getValue());
  }
  
  @Test
  public void testGetAsnNumber() {
    assertEquals(null, getAsnNumber(itemDetail)
        .getValue());
    assertNotNull(itemDetail.getInvoiceBaseItemDetail()
        .getLineItemReferences()
        .getASNNumber()
        .getRefNum());
    
    setAsnNumber("Asn123", itemDetail);
    assertNotNull(getAsnNumber(itemDetail)
        .getValue());
    assertEquals("Asn123", getAsnNumber(itemDetail)
        .getValue());
  }
  
  @Test
  public void testGetItemDescription() {
    assertEquals("", InvoiceItemDetailXcblHelper.getItemDescription(itemDetail));
    ComplexStringType itemDescription = itemDetail.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getItemDescription();
    assertNotNull(itemDescription);
    itemDetail.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .setItemDescription(toComplexStringType(itemDescription, "Description of product"));
    assertNotNull(InvoiceItemDetailXcblHelper.getItemDescription(itemDetail));
    assertEquals("Description of product", InvoiceItemDetailXcblHelper.getItemDescription(itemDetail));
  }
  
  @Test
  public void testGetSellerPartID() {
    assertEquals(null, getSellerPartID(itemDetail)
        .getValue());
    assertNotNull(itemDetail.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getSellerPartNumber()
        .getPartID());
    
    setSellerPartID("SellerID3452", itemDetail);
    assertNotNull(getSellerPartID(itemDetail)
        .getValue());
    assertEquals("SellerID3452", getSellerPartID(itemDetail)
        .getValue());
  }
  
  @Test
  public void testGetBuyerPartID() {
    assertEquals(null, getBuyerPartID(itemDetail)
        .getValue());
    assertNotNull(itemDetail.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getBuyerPartNumber()
        .getPartID());
    
    setBuyerPartID("BuyerID3452", itemDetail);
    assertNotNull(getBuyerPartID(itemDetail)
        .getValue());
    assertEquals("BuyerID3452", getBuyerPartID(itemDetail)
        .getValue());
  }
  

  @Test
  public void testGetPrice() {
    InvoiceItemDetailType item = buildInvoiceItemDetail();
    assertEquals(BigDecimal.ZERO, InvoiceItemDetailXcblHelper.getCalculationGross(item)
        .get());
    assertEquals(BigDecimal.ZERO, InvoiceItemDetailXcblHelper.getCalculationNet(item)
        .get());

    setCalculationGross(item, BigDecimal.TEN, detailPrecision, HALF_EVEN);
    setCalculationNet(item, BigDecimal.valueOf(20), detailPrecision, HALF_EVEN);
    assertEquals(BigDecimal.TEN.setScale(detailPrecision, HALF_EVEN), InvoiceItemDetailXcblHelper.getCalculationGross(item)
        .get());
    assertEquals(BigDecimal.valueOf(20)
        .setScale(detailPrecision, HALF_EVEN),
        InvoiceItemDetailXcblHelper.getCalculationNet(item)
            .get());

    Product product = new Product();
    product.setCalculationGross(12D);
    product.setCalculationNet(18D);
    InvoiceItemDetailXcblHelper.populateItemDetailWithUnitPrice(item, product, amountPrecision);
    assertEquals(BigDecimal.valueOf(12D)
        .setScale(amountPrecision, HALF_EVEN),
        InvoiceItemDetailXcblHelper.getCalculationGross(item)
            .get());
    assertEquals(BigDecimal.valueOf(18D)
        .setScale(amountPrecision, HALF_EVEN),
        InvoiceItemDetailXcblHelper.getCalculationNet(item)
            .get());

    product.setCalculationGross(22D);
    InvoiceItemDetailXcblHelper.populateItemDetailWithGrossPrice(item, product, detailPrecision, HALF_EVEN);
    assertEquals(BigDecimal.valueOf(22D)
        .setScale(detailPrecision, HALF_EVEN),
        InvoiceItemDetailXcblHelper.getCalculationGross(item)
            .get());
  }

  @SuppressWarnings({ "rawtypes", "serial", "unchecked" })
  @Test
  public void testItemWithAllowanceOrCharge() {
    Product product = new Product();
    ObjectAttribute tax = new ObjectAttribute(AllowanceOrCharge.LINE_ITEM_TAX_REGULATORY_TAX.toString(), BigDecimal.valueOf(5),
        TypeAttributeValue.PERCENT.toString(), "Tax");
    ObjectAttribute amount = new ObjectAttribute(AllowanceOrCharge.CHARGE.toString(), BigDecimal.valueOf(23), AMOUNT.toString(), "Charge");
    List<ObjectAttribute> objects = new LinkedList() {
      {
        add(tax);
        add(amount);
      }
    };
    product.setObjectAttribute(objects);
    InvoiceItemDetailType item = buildInvoiceItemDetail();
    setCalculationGross(item, BigDecimal.TEN, detailPrecision, HALF_EVEN);

    // Populate
    populateItemWithAllowanceOrCharge(item, product);

    assertEquals(IndicatorCodeType.SERVICE, item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .get(0)
        .getIndicatorCoded());
    assertEquals(tax.getValue()
        .toString(),
        item.getInvoicePricingDetail()
            .getItemAllowancesOrCharges()
            .getAllowOrCharge()
            .get(0)
            .getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercent()
            .getValue()
            .toString());
    assertEquals(BasisCodeType.PERCENT, item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .get(0)
        .getBasisCoded());
    assertEquals(BigDecimal.ZERO, item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .get(0)
        .getTypeOfAllowanceOrCharge()
        .getMonetaryValue()
        .getMonetaryAmount()
        .getValue());

    assertEquals(IndicatorCodeType.CHARGE, item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .get(1)
        .getIndicatorCoded());
    assertEquals(amount.getValue()
        .toString(),
        item.getInvoicePricingDetail()
            .getItemAllowancesOrCharges()
            .getAllowOrCharge()
            .get(1)
            .getTypeOfAllowanceOrCharge()
            .getMonetaryValue()
            .getMonetaryAmount()
            .getValue()
            .toString());
    assertEquals(BasisCodeType.MONETARY_AMOUNT, item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .get(1)
        .getBasisCoded());
    assertEquals(BigDecimal.ZERO, item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .get(1)
        .getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercent()
        .getValue());

    // Apply
    InvoiceAllowOrChargeXcblHelper.applyInvoiceAllowanceOrChargeOnDetail(item, detailPrecision);
    assertEquals(BigDecimal.valueOf(34.65)
        .setScale(detailPrecision, HALF_EVEN), getCalculationNet(item).get());
  }

  @Test
  public void testItemWithTax() {
    InvoiceItemDetailType item = buildInvoiceItemDetail();
    setCalculationGross(item, BigDecimal.TEN, detailPrecision, HALF_EVEN);
    setCalculationNet(item, BigDecimal.TEN, detailPrecision, HALF_EVEN);

    // Populate
    assertEquals(BigDecimal.valueOf(2)
        .setScale(amountPrecision, HALF_EVEN), populateItemWithTax(item, getCalculationNet(item), BigDecimal.valueOf(20)));
    assertEquals(TaxFunctionQualifierCodeType.TAX, item.getInvoicePricingDetail()
        .getTax()
        .get(0)
        .getTaxFunctionQualifierCoded());
    assertEquals(getCalculationNet(item).get()
        .setScale(amountPrecision, HALF_EVEN),
        item.getInvoicePricingDetail()
            .getTax()
            .get(0)
            .getTaxableAmount());

    InvoiceItemDetailXcblHelper.computeAmountHT(item, detailPrecision, HALF_EVEN);
    // Apply
    applyTaxOnInvoiceItemDetail(item, taxSummaries, detailPrecision);
    assertEquals(BigDecimal.valueOf(12)
        .setScale(detailPrecision, HALF_EVEN),
        item.getInvoicePricingDetail()
            .getLineItemTotal()
            .getMonetaryAmount()
            .getValue());
  }

  @SuppressWarnings({ "rawtypes", "serial", "unchecked" })
  @Test
  public void testPopulateInvoiceItemDetail() {
    InvoiceItemDetailType item = buildInvoiceItemDetail();
    Product product = new Product();
    ObjectAttribute tax = new ObjectAttribute(AllowanceOrCharge.LINE_ITEM_TAX_REGULATORY_TAX.toString(), BigDecimal.valueOf(5),
        TypeAttributeValue.PERCENT.toString(), "Tax");
    ObjectAttribute amount = new ObjectAttribute(AllowanceOrCharge.CHARGE.toString(), BigDecimal.valueOf(23), AMOUNT.toString(), "Charge");
    List<ObjectAttribute> objects = new LinkedList() {
      {
        add(tax);
        add(amount);
      }
    };
    product.setProductName("Product Name");
    product.setDescription("Product Identifier");
    product.setObjectAttribute(objects);
    product.setCalculationGross(12D);
    product.setCalculationNet(18D);
    product.setUnitOfMeasurementType("EA");
    product.setReference("Reference");
    product.setBaseProductNumber("Product Number");
    product.setVat(20D);

    populateInvoiceItemDetail(item, product, detailPrecision, 6, null);
    assertEquals(product.getBaseProductNumber(), item.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getPartNumbers()
        .getBuyerPartNumber()
        .getPartID()
        .getValue());
    assertEquals(product.getDescription(), item.getInvoiceBaseItemDetail()
        .getItemIdentifiers()
        .getItemDescription()
        .getValue());
    assertEquals(product.getCalculationGross()
            .doubleValue(),
        getCalculationGross(item).get()
            .doubleValue(),
        0.001);
    assertEquals(BigDecimal.valueOf(36.75)
        .setScale(detailPrecision, HALF_EVEN), getAmountHT(item).get());
    assertEquals(BigDecimal.valueOf(44.1)
            .setScale(detailPrecision, HALF_EVEN),
        item.getInvoicePricingDetail()
            .getLineItemTotal()
            .getMonetaryAmount()
            .getValue());
  }

  @Test
  public void computeNetPriceWithoutAllOrChr() {
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(getCalculationGross(itemDetail), getCalculationNet(itemDetail));
  }

  @Test
  public void computeNetPriceWithAll() {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceItemDetailXcblHelperMockTest.addAllowance(itemDetail);

    // Amount
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    InvoiceAllowOrChargeXcblHelper.initMonetaryAllowanceOrChargeToValue(allowOrChargeType, null, BigDecimal.ONE);
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(BigDecimal.valueOf(9)
        .setScale(detailPrecision, HALF_EVEN), getCalculationNet(itemDetail).get());

    // Percent
    InvoiceAllowOrChargeXcblHelper.initPercentAllowanceOrChargeToValue(allowOrChargeType, null, BigDecimal.valueOf(50));
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(BigDecimal.valueOf(5)
        .setScale(detailPrecision, HALF_EVEN), getCalculationNet(itemDetail).get());
  }

  @Test
  public void computeNetPriceWithChr() {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceItemDetailXcblHelperMockTest.addChr(itemDetail);

    // Amount
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    InvoiceAllowOrChargeXcblHelper.initMonetaryAllowanceOrChargeToValue(allowOrChargeType, null, BigDecimal.ONE);
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(BigDecimal.valueOf(11)
        .setScale(detailPrecision, HALF_EVEN), getCalculationNet(itemDetail).get());

    // Percent
    InvoiceAllowOrChargeXcblHelper.initPercentAllowanceOrChargeToValue(allowOrChargeType, null, BigDecimal.valueOf(50));
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(BigDecimal.valueOf(15)
        .setScale(detailPrecision, HALF_EVEN), getCalculationNet(itemDetail).get());
  }

  @Test
  public void computeNetPriceWithParafiscalTax() {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceItemDetailXcblHelperMockTest.addParafiscalTax(itemDetail);

    // Amount
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    InvoiceAllowOrChargeXcblHelper.initMonetaryAllowanceOrChargeToValue(allowOrChargeType, null, BigDecimal.ONE);
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(BigDecimal.valueOf(11)
        .setScale(detailPrecision, HALF_EVEN), getCalculationNet(itemDetail).get());

    // Percent
    InvoiceAllowOrChargeXcblHelper.initPercentAllowanceOrChargeToValue(allowOrChargeType, null, BigDecimal.valueOf(50));
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(BigDecimal.valueOf(15)
        .setScale(detailPrecision, HALF_EVEN), getCalculationNet(itemDetail).get());
  }

  @Test
  public void computeAmountHT() {
    itemDetail.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .getQuantityValue()
        .setValue(BigDecimal.TEN);
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(BigDecimal_CENT.setScale(amountPrecision, HALF_EVEN), getAmountHT(itemDetail).get());
  }

  @Test
  public void computeAmountVAT() {
    itemDetail.getInvoicePricingDetail()
        .getTax()
        .get(0)
        .getTaxPercent()
        .setValue(BigDecimal.valueOf(20));
    InvoiceItemDetailXcblHelperMockTest.computeItem(itemDetail, taxSummaries, detailPrecision, amountPrecision);
    Assertions.assertEquals(BigDecimal.valueOf(2)
        .setScale(amountPrecision, HALF_EVEN), getAmountVAT(itemDetail).get());
  }

}