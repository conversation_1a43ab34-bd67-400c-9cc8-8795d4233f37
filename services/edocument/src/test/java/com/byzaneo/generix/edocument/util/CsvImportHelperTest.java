package com.byzaneo.generix.edocument.util;

import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxCategoryCodedOtherFromInvoiceTaxSummaryType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxableAmountFromInvoiceTaxSummaryType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexBigDecimalType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.Order;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CountryCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.IdentifierType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceAllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceAllowOrChgDescType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceMonetaryValueType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceTaxSummaryType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceTaxType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceTypeOfAllowanceOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfInvoiceAllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfInvoicePriceType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.MethodOfHandlingCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PackageDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PackageType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartNumType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PercentType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxCategoryCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxFunctionQualifierCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceItemDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoicePricingDetailType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceSummaryType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.ListOfInvoiceItemDetailType;
import java.math.BigDecimal;
import java.util.Locale;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class CsvImportHelperTest {

  private Partner partner;

  private Company company;

  @BeforeEach
  public void before() {

    partner = mock(Partner.class, RETURNS_DEEP_STUBS);
    when(partner.getFullname()).thenReturn("Partner");
    when(partner.getCode()).thenReturn("codep");
    when(partner.getLocation()
        .getAddress()
        .getStreetName()).thenReturn("15 rue des Mimosas");
    when(partner.getLocation()
        .getAddress()
        .getPostalCode()).thenReturn("13001");
    when(partner.getLocation()
        .getAddress()
        .getCity()).thenReturn("Marseille");
    when(partner.getLocation()
        .getAddress()
        .getCountry()).thenReturn(Locale.FRANCE);
    when(partner.getDescription()).thenReturn(Partner.DESCRIPTION);
    when(partner.getVat()).thenReturn("VAT_PARTNER");
    when(partner.getRegistration()).thenReturn("REG_PARTNER");
    when(partner.getDuns()).thenReturn("SIREN_PARTNER");

    company = mock(Company.class, RETURNS_DEEP_STUBS);
    when(company.getFullname()).thenReturn("Generix group");
    when(company.getCode()).thenReturn("codeg");
    when(company.getLocation()
        .getAddress()
        .getStreetName()).thenReturn("15 rue Beaubourg");
    when(company.getLocation()
        .getAddress()
        .getPostalCode()).thenReturn("75003");
    when(company.getLocation()
        .getAddress()
        .getCity()).thenReturn("Paris");
    when(company.getLocation()
        .getAddress()
        .getCountry()).thenReturn(Locale.FRANCE);
    when(company.getDescription()).thenReturn(Company.DESCRIPTION);
  }

  @AfterEach
  public void after() {

  }

  @Test
  public void testReinitMaps() {
    CsvImportHelper.getContainersMap()
        .put(1, new PackageType());
    CsvImportHelper.getContainersDetailMap()
        .put(1, new PackageDetailType());
    CsvImportHelper.setImportedIndexable(new Order());
    CsvImportHelper.getProductLineNumber("id1");
    CsvImportHelper.setRootContainerIndex(Integer.valueOf(10));
    CsvImportHelper.setDocumentLongVersion(true);
    CsvImportHelper.reinitMaps();

    assertEquals(0, CsvImportHelper.getContainersMap()
        .size());
    assertEquals(0, CsvImportHelper.getContainersDetailMap()
        .size());
    assertNull(CsvImportHelper.getImportedIndexable());
    assertNull(CsvImportHelper.getRootContainerIndex());
    assertFalse(CsvImportHelper.isDocumentLongVersion());
  }

  @Test
  public void testGetPartNumberTypeWithNullValue() {
    PartNumType partNumberType = CsvImportHelper.getPartNumberType(null);
    assertNotNull(partNumberType);
    assertNull(partNumberType.getPartID());
  }

  @Test
  public void testGetPartNumberType() {
    PartNumType partNumberType = CsvImportHelper.getPartNumberType("test");
    assertNotNull(partNumberType);
    assertEquals("test", partNumberType.getPartID());
  }

  @Test
  public void testGetUnitOfMeasurement() {
    assertNull(CsvImportHelper.getUnitOfMeasurement(""));
    assertEquals("EA", CsvImportHelper.getUnitOfMeasurement("PCE")
        .getUOMCoded());
    assertEquals("DTN", CsvImportHelper.getUnitOfMeasurement("DTN")
        .getUOMCoded());
    assertEquals("Other", CsvImportHelper.getUnitOfMeasurement("Autres")
        .getUOMCoded());
    assertTrue(CsvImportHelper.getUnitOfMeasurement("Autres")
        .getUOMCoded()
        .equalsIgnoreCase("Other") && CsvImportHelper.getUnitOfMeasurement("Autres")
            .getUOMCodedOther()
            .equalsIgnoreCase("Autres"));
  }

  @Test
  public void testFillGroupInformations_Company() {
    PartyType companyParty = CsvImportHelper.fillGroupInformations(company);

    assertEquals("Generix group", companyParty.getNameAddress()
        .getName1());
    assertEquals("15 rue Beaubourg", companyParty.getNameAddress()
        .getStreet());
    assertEquals("75003", companyParty.getNameAddress()
        .getPostalCode());
    assertEquals("Paris", companyParty.getNameAddress()
        .getCity());
    assertEquals(CountryCodeType.FR, companyParty.getNameAddress()
        .getCountry()
        .getCountryCoded());
    assertEquals("codeg", companyParty.getPartyID()
        .getIdent());
    Assertions.assertNull(companyParty.getListOfIdentifier());
  }

  @Test
  public void testFillGroupInformations_Partner() {
    PartyType partnerParty = CsvImportHelper.fillGroupInformations(partner);

    assertEquals("Partner", partnerParty.getNameAddress()
        .getName1());
    assertEquals("15 rue des Mimosas", partnerParty.getNameAddress()
        .getStreet());
    assertEquals("13001", partnerParty.getNameAddress()
        .getPostalCode());
    assertEquals("Marseille", partnerParty.getNameAddress()
        .getCity());
    assertEquals(CountryCodeType.FR, partnerParty.getNameAddress()
        .getCountry()
        .getCountryCoded());
    assertEquals("codep", partnerParty.getPartyID()
        .getIdent());
    Assertions.assertNotNull(partnerParty.getListOfIdentifier());

    for (IdentifierType identifier : partnerParty.getListOfIdentifier()
        .getIdentifier()) {
      switch (identifier.getAgency()
          .getAgencyCoded()) {
      case "FR-INSEE":
        assertEquals("Other", identifier.getAgency()
            .getCodeListIdentifierCoded());
        assertEquals("Siren", identifier.getAgency()
            .getCodeListIdentifierCodedOther());
        assertEquals("SIREN_PARTNER", identifier.getIdent());
        break;
      case "CEC":
        assertEquals("ValueAddedTaxIdentification", identifier.getAgency()
            .getCodeListIdentifierCoded());
        assertEquals("VAT_PARTNER", identifier.getIdent());
        break;
      case "Other":
        assertEquals("Other", identifier.getAgency()
            .getAgencyCoded());
        assertEquals("RCS-RCM", identifier.getAgency()
            .getAgencyCodedOther());
        assertEquals("French Trade and Companies Register", identifier.getAgency()
            .getAgencyDescription());
        assertEquals("REG_PARTNER", identifier.getIdent());
        break;
      default:
        Assertions.fail("Unknwon code");
        break;
      }
    }
  }

  @Test
  public void testComputeInvoiceTotal_HeaderAllowancesOnly() {
    Invoice invoice = new Invoice();

    // Fill the invoice
    // Allowances
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
        .setInvoiceAllowancesOrCharges(new ListOfInvoiceAllowOrChargeType());
    InvoiceAllowOrChargeType allowHeader1 = new InvoiceAllowOrChargeType();
    allowHeader1.setMethodOfHandlingCoded(MethodOfHandlingCodeType.BILL_BACK);
    allowHeader1.setAllowanceOrChargeDescription(new InvoiceAllowOrChgDescType());
    allowHeader1.setIndicatorCoded(IndicatorCodeType.ALLOWANCE);
    InvoiceTaxType tax1 = new InvoiceTaxType();
    tax1.setTaxCategoryCodedOther(toComplexStringType(tax1.getTaxCategoryCodedOther(), "6.7"));
    allowHeader1.setTypeOfAllowanceOrCharge(new InvoiceTypeOfAllowanceOrChargeType());
    allowHeader1.getTypeOfAllowanceOrCharge()
        .setMonetaryValue(new InvoiceMonetaryValueType());
    InvoiceMonetaryValueType monetaryValue1 = allowHeader1.getTypeOfAllowanceOrCharge()
        .getMonetaryValue();
    monetaryValue1.setMonetaryAmount(toComplexBigDecimalType(monetaryValue1.getMonetaryAmount(), new BigDecimal("1000.0")));
    allowHeader1.getTax()
        .add(tax1);
    InvoiceAllowOrChargeType allowHeader2 = new InvoiceAllowOrChargeType();
    allowHeader2.setMethodOfHandlingCoded(MethodOfHandlingCodeType.BILL_BACK);
    InvoiceTaxType tax2 = new InvoiceTaxType();
    tax2.setTaxCategoryCodedOther(toComplexStringType(tax2.getTaxCategoryCodedOther(), "15.7"));
    allowHeader2.setIndicatorCoded(IndicatorCodeType.CHARGE);
    allowHeader2.setAllowanceOrChargeDescription(new InvoiceAllowOrChgDescType());
    allowHeader2.setTypeOfAllowanceOrCharge(new InvoiceTypeOfAllowanceOrChargeType());
    allowHeader2.getTypeOfAllowanceOrCharge()
        .setMonetaryValue(new InvoiceMonetaryValueType());
    InvoiceMonetaryValueType monetaryValue2 = allowHeader2.getTypeOfAllowanceOrCharge()
        .getMonetaryValue();
    monetaryValue2.setMonetaryAmount(toComplexBigDecimalType(monetaryValue2.getMonetaryAmount(), new BigDecimal("2000.0")));
    allowHeader2.getTax()
        .add(tax2);
    invoice.getInvoiceHeader()
        .getInvoiceAllowancesOrCharges()
        .getAllowOrCharge()
        .add(allowHeader1);
    invoice.getInvoiceHeader()
        .getInvoiceAllowancesOrCharges()
        .getAllowOrCharge()
        .add(allowHeader2);

    CsvImportHelper.computeInvoiceTotal(invoice, 3, 3);

    Assertions.assertNotNull(invoice.getInvoiceSummary());

    Assertions.assertNull(invoice.getInvoiceSummary()
        .getAllowOrChargeSummary());
    InvoiceTaxSummaryType taxSummary = findTaxSummary(invoice, "6.7");
    assertEquals(new BigDecimal("-1000.000"), getTaxableAmountFromInvoiceTaxSummaryType(taxSummary));
    assertEquals(new BigDecimal("-67.000"), taxSummary.getTaxAmount());
    assertEquals(TaxCategoryCodeType.OTHER, taxSummary.getTaxCategoryCoded());
    assertEquals(getTaxCategoryCodedOtherFromInvoiceTaxSummaryType(taxSummary), "6.7");
    assertEquals("ValueAddedTax", taxSummary.getTaxTypeCoded());
    assertEquals(TaxFunctionQualifierCodeType.TAX, taxSummary.getTaxFunctionQualifierCoded());

    taxSummary = findTaxSummary(invoice, "15.7");
    assertEquals(new BigDecimal("2000.000"), getTaxableAmountFromInvoiceTaxSummaryType(taxSummary));
    assertEquals(new BigDecimal("314.000"), taxSummary.getTaxAmount());
    assertEquals(TaxCategoryCodeType.OTHER, taxSummary.getTaxCategoryCoded());
    assertEquals(getTaxCategoryCodedOtherFromInvoiceTaxSummaryType(taxSummary), "15.7");
    assertEquals("ValueAddedTax", taxSummary.getTaxTypeCoded());
    assertEquals(TaxFunctionQualifierCodeType.TAX, taxSummary.getTaxFunctionQualifierCoded());
  }

  @Test
  public void testComputeInvoiceTotal_LinesOnly() {
    Invoice invoice = new Invoice();

    // Fill the invoice
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.setInvoiceDetail(new InvoiceDetailType());
    invoice.getInvoiceDetail()
        .setListOfInvoiceItemDetail(new ListOfInvoiceItemDetailType());
    invoice.setInvoiceSummary(new InvoiceSummaryType());

    for (int i = 0; i <= 10; i++) {
      String taxCategory = i + ".8";
      InvoiceItemDetailType detail = createInvoiceDetail(BigDecimal.valueOf(1000), taxCategory, new BigDecimal(taxCategory),
          BigDecimal.valueOf(1000), 1);
      invoice.getInvoiceDetail()
          .getListOfInvoiceItemDetail()
          .getInvoiceItemDetail()
          .add(detail);
    }

    CsvImportHelper.computeInvoiceTotal(invoice, 4, 4);

    Assertions.assertNotNull(invoice.getInvoiceSummary());
    Assertions.assertNull(invoice.getInvoiceSummary()
        .getAllowOrChargeSummary());
    for (int i = 0; i <= 10; i++) {
      String taxCategory = i + ".8";
      InvoiceTaxSummaryType taxSummary = findTaxSummary(invoice, taxCategory);
      assertEquals(new BigDecimal("1000.0000"), getTaxableAmountFromInvoiceTaxSummaryType(taxSummary));
      assertEquals(new BigDecimal(i + "8.0000"), taxSummary.getTaxAmount());
      assertEquals(TaxCategoryCodeType.OTHER, taxSummary.getTaxCategoryCoded());
      assertEquals(taxCategory, getTaxCategoryCodedOtherFromInvoiceTaxSummaryType(taxSummary));
      assertEquals("ValueAddedTax", taxSummary.getTaxTypeCoded());
      assertEquals(TaxFunctionQualifierCodeType.TAX, taxSummary.getTaxFunctionQualifierCoded());
    }

  }

  private InvoiceItemDetailType createInvoiceDetail(BigDecimal ammount, String taxCategory, BigDecimal taxPercent, BigDecimal price,
      int nbItems) {
    InvoiceItemDetailType detail = InvoiceItemDetailXcblHelper.buildInvoiceItemDetail();
    detail.setInvoicePricingDetail(new InvoicePricingDetailType());
    detail.getInvoicePricingDetail()
        .setLineItemSubTotal(new InvoiceMonetaryValueType());
    detail.getInvoicePricingDetail()
        .getLineItemSubTotal()
        .setMonetaryAmount(toComplexBigDecimalType(ammount));
    detail.getInvoicePricingDetail()
        .getTax()
        .add(new InvoiceTaxType());
    InvoiceTaxType tax = detail.getInvoicePricingDetail()
        .getTax()
        .get(0);
    tax.setTaxCategoryCodedOther(toComplexStringType(tax.getTaxCategoryCodedOther(), taxCategory));
    PercentType value = new PercentType();
    value.setValue(taxPercent);
    tax.setTaxPercent(value);
    detail.getInvoicePricingDetail()
        .setItemAllowancesOrCharges(new ListOfInvoiceAllowOrChargeType());

    detail.getInvoicePricingDetail()
        .setListOfPrice(new ListOfInvoicePriceType());
    detail.getInvoicePricingDetail()
        .getListOfPrice()
        .getPrice()
        .add(InvoicePriceXcblHelper.createInvoicePriceType(PriceTypeCodeType.CALCULATION_NET, price));

    return detail;
  }

  @Test
  public void testComputeInvoiceTotal_Lines() {
    Invoice invoice = new Invoice();

    // Fill the invoice
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.setInvoiceDetail(new InvoiceDetailType());
    invoice.getInvoiceDetail()
        .setListOfInvoiceItemDetail(new ListOfInvoiceItemDetailType());
    invoice.setInvoiceSummary(new InvoiceSummaryType());

    for (int i = 0; i <= 10; i++) {
      String taxCategory = i + ".8";
      InvoiceItemDetailType detail = createInvoiceDetail(BigDecimal.valueOf(1000), taxCategory, new BigDecimal(taxCategory),
          BigDecimal.valueOf(1000), 1);
      // Line allowances
      InvoiceAllowOrChargeType allowance = new InvoiceAllowOrChargeType();
      allowance.setMethodOfHandlingCoded(MethodOfHandlingCodeType.BILL_BACK);
      allowance.setAllowanceOrChargeDescription(new InvoiceAllowOrChgDescType());
      allowance.getAllowanceOrChargeDescription()
          .setRefID("Product " + i);
      allowance.setIndicatorCoded(IndicatorCodeType.CHARGE);
      InvoiceTaxType tax1 = new InvoiceTaxType();
      tax1.setTaxCategoryCodedOther(toComplexStringType("1.8"));
      allowance.setTypeOfAllowanceOrCharge(new InvoiceTypeOfAllowanceOrChargeType());
      allowance.getTypeOfAllowanceOrCharge()
          .setMonetaryValue(new InvoiceMonetaryValueType());
      allowance.getTypeOfAllowanceOrCharge()
          .getMonetaryValue()
          .setMonetaryAmount(toComplexBigDecimalType(new BigDecimal("10.0")));
      allowance.getTax()
          .add(tax1);

      detail.getInvoicePricingDetail()
          .getItemAllowancesOrCharges()
          .getAllowOrCharge()
          .add(allowance);
      invoice.getInvoiceDetail()
          .getListOfInvoiceItemDetail()
          .getInvoiceItemDetail()
          .add(detail);
    }

    CsvImportHelper.computeInvoiceTotal(invoice, 2, 2);

    Assertions.assertNotNull(invoice.getInvoiceSummary());

    for (int i = 0; i <= 10; i++) {
      String taxCategory = i + ".8";
      InvoiceTaxSummaryType taxSummary = findTaxSummary(invoice, taxCategory);
      assertEquals(new BigDecimal("1000.00"), getTaxableAmountFromInvoiceTaxSummaryType(taxSummary));
      assertEquals(new BigDecimal(i + "8.00"), taxSummary.getTaxAmount());
      assertEquals(TaxCategoryCodeType.OTHER, taxSummary.getTaxCategoryCoded());
      assertEquals(taxCategory, getTaxCategoryCodedOtherFromInvoiceTaxSummaryType(taxSummary));
      assertEquals("ValueAddedTax", taxSummary.getTaxTypeCoded());
      assertEquals(TaxFunctionQualifierCodeType.TAX, taxSummary.getTaxFunctionQualifierCoded());
    }

  }

  /**
   * Find the taxSummary related to the tax category in the invoice
   * 
   * @param invoice
   * @param taxCategory
   * @return
   */
  private InvoiceTaxSummaryType findTaxSummary(Invoice invoice, String taxCategory) {
    for (InvoiceTaxSummaryType taxSummary : invoice.getInvoiceSummary()
        .getListOfTaxSummary()
        .getTaxSummary()) {
      if (getTaxCategoryCodedOtherFromInvoiceTaxSummaryType(taxSummary)
          .equals(taxCategory))
        return taxSummary;
    }
    return null;
  }
}
