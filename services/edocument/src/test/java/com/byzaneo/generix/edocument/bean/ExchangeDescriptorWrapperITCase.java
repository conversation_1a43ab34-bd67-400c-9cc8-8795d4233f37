package com.byzaneo.generix.edocument.bean;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.slf4j.LoggerFactory.getLogger;

import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;

import com.byzaneo.commons.io.BeanImportContext;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.bean.Exchange.Direction;

public class ExchangeDescriptorWrapperITCase {

  private static final Logger log = getLogger(ExchangeDescriptorWrapperITCase.class);

  private ExchangeDescriptorWrapper instance;

  @BeforeEach
  public void before() {
    instance = new ExchangeDescriptorWrapper();
  }

  @Test
  public void testContextValidate_noCompanyInContext() throws Exception {
    Exchange exchange = createExchange();
    BeanImportContext context = new BeanImportContext();

    // action : no validation exception is expected
    instance.validate(exchange, context);
  }

  @Test
  public void testExchangeValidation_noStartDate() {
    assertThrows(IllegalArgumentException.class, () -> {
      Exchange exchange = createExchange();
      exchange.setStart(null);
      BeanImportContext context = new BeanImportContext();
      instance.validate(exchange, context);
    });
  }

  @Test
  public void testExchangeValidation_noKind() {
    assertThrows(IllegalArgumentException.class, () -> {
      Exchange exchange = createExchange();
      exchange.getId()
          .setKind(null);
      BeanImportContext context = new BeanImportContext();
      instance.validate(exchange, context);
    });
  }

  @Test
  public void testExchangeValidation_noLegalEntity() {
    assertThrows(IllegalArgumentException.class, () -> {
      Exchange exchange = createExchange();
      exchange.getId()
          .setLegalEntity(null);
      BeanImportContext context = new BeanImportContext();
      instance.validate(exchange, context);
    });
  }

  @Test
  public void testExchangeValidation_noDematPartner() {
    assertThrows(IllegalArgumentException.class, () -> {
      Exchange exchange = createExchange();
      exchange.getId()
          .setDematPartner(null);
      BeanImportContext context = new BeanImportContext();
      instance.validate(exchange, context);
    });
  }

  @Test
  public void testExchangeValidation_noDirection() {
    assertThrows(IllegalArgumentException.class, () -> {
      Exchange exchange = createExchange();
      exchange.getId()
          .setDirection(null);
      BeanImportContext context = new BeanImportContext();
      instance.validate(exchange, context);
    });
  }

  @Test
  public void testExchangeValidation_endDateBeforeStartDate() {
    assertThrows(IllegalArgumentException.class, () -> {
      Exchange exchange = createExchange();
      exchange.setEnd(new Date(1564580300));
      exchange.setStart(new Date(1564580388));
      BeanImportContext context = new BeanImportContext();
      instance.validate(exchange, context);
    });
  }

  @Test
  public void testExchangeValidation_legalEntityEqualsWithDematPartner() {
    assertThrows(IllegalArgumentException.class, () -> {
      Exchange exchange = createExchange();
      Group sameGroup = new Group("sameGroup");
      exchange.getId()
          .setDematPartner(sameGroup);
      exchange.getId()
          .setLegalEntity(sameGroup);
      BeanImportContext context = new BeanImportContext();
      instance.validate(exchange, context);
    });
  }

  @Test
  public void testContextValidate_ContextCompanyDoesNotMatchWrappedCompany() throws Exception {
    Exchange exchange = new Exchange();
    BeanImportContext context = new BeanImportContext();
    String contextCompanyCode = "ctxCpyCde";
    context.put(SecurityService.COMPANY_CODE, contextCompanyCode);
    instance = spy(instance);
    String wrappedCompanyCode = "wrpCpyCde";
    doReturn(wrappedCompanyCode).when(instance)
        .getWrappedCompanyCode();

    // action
    try {
      instance.validate(exchange, context);
      fail("expected validation exception");
    }
    catch (Exception e) {
      log.error(e.getMessage());
    }
  }

  private Exchange createExchange() {
    Exchange exchange = new Exchange();
    ExchangeId exchangeId = new ExchangeId();
    exchangeId.setDematPartner(new Group("dematPartner"));
    exchangeId.setLegalEntity(new Group("legalEntity"));
    exchangeId.setKind("exchange");
    exchangeId.setDirection(Direction.R);
    exchangeId.setIdentifier("identifier");
    exchange.setId(exchangeId);
    exchange.setStart(new Date(1564580300));
    exchange.setEnd(new Date(1564580388));
    return exchange;
  }
}
