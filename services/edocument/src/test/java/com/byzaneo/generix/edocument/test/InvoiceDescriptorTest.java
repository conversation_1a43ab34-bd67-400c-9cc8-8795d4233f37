package com.byzaneo.generix.edocument.test;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Date;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.util.BeanDescriptorHelper;
import com.byzaneo.generix.edocument.bean.Invoice;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Apr 8, 2014
 * @since 3.0 GNX-1112
 */
public class InvoiceDescriptorTest {

  @Test
  public void testDescriptor() throws Exception {
    BeanDescriptor descriptor = BeanDescriptorHelper.newDescriptor(Invoice.class, null, true, true, "id", "class", "entityRef",
        "entityClass", "entityId");
    assertNotNull(descriptor);
    assertNull(descriptor.get("entityRef"));
    assertNotNull(descriptor.get("number"));
    assertEquals(String.class, descriptor.get("number")
        .getType());
    assertEquals(Date.class, descriptor.get("date")
        .getType());
    assertEquals(int.class, descriptor.get("link")
        .getType());
  }
}
