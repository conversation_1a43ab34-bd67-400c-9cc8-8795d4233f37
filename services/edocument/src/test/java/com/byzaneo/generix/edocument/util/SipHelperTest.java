package com.byzaneo.generix.edocument.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.byzaneo.generix.edocument.util.SipHelper.DetailsMessage;
import com.google.common.hash.Hashing;
import org.junit.jupiter.api.Test;

public class SipHelperTest {

  @Test
  public void testCreateSipReportFileNameIllegalArgument1() {
    assertThrows(IllegalArgumentException.class, () ->
      SipHelper.createSipReportFileName(null, "archiveId"));
  }

  @Test
  public void testCreateSipReportFileNameIllegalArgument2() {
    assertThrows(IllegalArgumentException.class, () ->
      SipHelper.createSipReportFileName("prefix", null));
  }

  @Test
  public void testCreateSipReportFileNameIllegalArguments() {
    assertThrows(IllegalArgumentException.class, () ->
      SipHelper.createSipReportFileName(null, null));
  }

  @Test
  public void testCreateSipReportFileNameInvalid() {
    assertEquals("SIP_report_ARC_2020_84a9a5ccc80e07a1c7fd6cc67d2876731284b0aa.xml",
        SipHelper.createSipReportFileName("ARC_2020", "84a9a5ccc80e07a1c7fd6cc67d2876731284b0aa"));
  }

  @Test
  public void testFindHashFunctionNameNullArgument() {
    assertThrows(IllegalArgumentException.class, () ->
      SipHelper.findHashFunctionName(null));
  }

  @Test
  public void testFindHashFunctionNameSHA1() {
    assertEquals("SHA1", SipHelper.findHashFunctionName(Hashing.sha1()));
  }

  @Test
  public void testFindHashFunctionNameSHA256() {
    assertEquals("SHA-256", SipHelper.findHashFunctionName(Hashing.sha256()));
  }

  @Test
  public void testFindHashFunctionNameSHA384() {
    assertEquals("SHA-384", SipHelper.findHashFunctionName(Hashing.sha384()));
  }

  @Test
  public void testFindHashFunctionNameSHA512() {
    assertEquals("SHA-512", SipHelper.findHashFunctionName(Hashing.sha512()));
  }

  @Test
  public void testFindHashFunctionNameMD5() {
    assertEquals("MD5", SipHelper.findHashFunctionName(Hashing.md5()));
  }

  @Test
  public void testCreateIndexFileNameNullArgument() {
    assertThrows(IllegalArgumentException.class, () ->
      SipHelper.createIndexFileName(null));
  }

  @Test
  public void testCreateIndexFileNameEmptyArgument() {
    assertThrows(IllegalArgumentException.class, () ->
      SipHelper.createIndexFileName(""));
  }

  @Test
  public void testCreateIndexFileName() {
    assertEquals("SIP_index_84a9a5ccc80e07a1c7fd6cc67d2876731284b0aa.xml",
        SipHelper.createIndexFileName("84a9a5ccc80e07a1c7fd6cc67d2876731284b0aa"));
  }

  @Test
  public void testTransformBooleanOK() {
    assertEquals("OK", SipHelper.transformBoolean(true));
  }

  @Test
  public void testTransformBooleanKO() {
    assertEquals("KO", SipHelper.transformBoolean(false));
  }

  @Test
  public void testCreateDetailsMessageFile() {
    assertEquals("File required : type XML, subtype FatturaPA",
        SipHelper.createControlDetailsMessage(DetailsMessage.FILE, "XML", "FatturaPA", ""));
  }

  @Test
  public void testCreateDetailsMessageFileNoSubtype() {
    assertEquals("File required : type XML",
        SipHelper.createControlDetailsMessage(DetailsMessage.FILE, "XML", "", ""));
  }

  @Test
  public void testCreateDetailsMessageHash() {
    assertEquals("Checking hash of file : type XML, subtype FatturaPA",
        SipHelper.createControlDetailsMessage(DetailsMessage.HASH, "XML", "FatturaPA", ""));
  }

  @Test
  public void testCreateDetailsMessageHashNoSubtype() {
    assertEquals("Checking hash of file : type XML",
        SipHelper.createControlDetailsMessage(DetailsMessage.HASH, "XML", "", ""));
  }

  @Test
  public void testCreateDetailsMessageSignature() {
    assertEquals("Verification for XML Signature of SIP Index",
        SipHelper.createControlDetailsMessage(DetailsMessage.SIGNATURE, "XML", "FatturaPA", ""));
  }

  @Test
  public void testCreateDetailsMessageInvalidArgument() {
    assertThrows(IllegalArgumentException.class, () ->
      assertEquals("Verification for XML Signature of SIP Index",
          SipHelper.createControlDetailsMessage(null, "XML", "FatturaPA", "")));
  }
}
