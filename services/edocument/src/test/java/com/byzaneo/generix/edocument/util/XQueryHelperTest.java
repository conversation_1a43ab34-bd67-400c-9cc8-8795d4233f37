package com.byzaneo.generix.edocument.util;

import static com.byzaneo.commons.util.DomHelper.evaluateNode;
import static com.byzaneo.generix.edocument.util.XQuery.isNotEmpty;
import static org.junit.jupiter.api.Assertions.*;

import java.io.File;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import com.byzaneo.commons.util.DomHelper;

public class XQueryHelperTest {
  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/InvoiceExample.xml"), false);

  private static final String XPATH = "//InvoiceHeader/InvoiceDates/ListOfOtherInvoiceDates/DateCoded[DateQualifier[normalize-space(DateQualifierCoded)='PreparationDateTimeOfDocument']]/Date";

  private XQuery xQuery = new XQuery();

  @Test
  public void testIsNotEmpty() {
    Node node = evaluateNode(XCBL_ROOT, XPATH);
    assertTrue(isNotEmpty(node));
  }

  @Test
  public void testIsUnique() {
    Node node = evaluateNode(XCBL_ROOT, XPATH);
    assertTrue(xQuery.isUnique(node.getParentNode(), XPATH));
  }

  @Test
  public void testIsEqualTo() {
    Node node = evaluateNode(XCBL_ROOT, XPATH);
    assertTrue(XQuery.isEqualTo(node, "2003-01-01T00:00:35"));

  }

  @Test
  public void testIsAnyDescendantNotEmpty() {

    String[] params = new String[] { "PercentageAllowanceOrCharge/Percent", "MonetaryValue/MonetaryAmount" };
    String path = "//InvoiceHeader/InvoiceAllowancesOrCharges/AllowOrCharge/TypeOfAllowanceOrCharge";
    Node node = evaluateNode(XCBL_ROOT, path);
    assertFalse(xQuery.isAnyDescendantNotEmpty(node, params));
  }

  @Test
  public void testAreAllDescendantsNotEmpty() {

    String path = "//InvoiceHeader/InvoiceDates/InvoicingPeriod";
    Node node = evaluateNode(XCBL_ROOT, path);

    // Let's assume that the StartDate does not exist in the XCBL document
    // (we can achieve this by entering a bad name of the element).

    assertFalse(xQuery.areAllDescendantsNotEmpty(node, "StartDate", "EndDateOops"));
    path = "//InvoiceHeader/InvoiceDates/InvoicingPeriod";
    node = evaluateNode(XCBL_ROOT, path);
    assertTrue(xQuery.areAllDescendantsNotEmpty(node, "StartDate", "EndDate"));

  }

  @Test
  public void tesAssertTrue() {
    String path1 = "//InvoiceHeader/InvoiceIssueDates";
    String path2 = "//InvoiceHeader/InvoiceType";

    assertFalse(xQuery.checkXExpression(XCBL_ROOT, path1));
    assertTrue(xQuery.checkXExpression(XCBL_ROOT, path2));
    assertFalse(xQuery.checkXExpression(XCBL_ROOT, path1, path2));
  }

  @Test
  public void testEvaluateNumberContent() {
    String path = "//InvoiceHeader/InvoiceReferences/AuthorizationReference/AuthorizationAssignedBy/NameAddress/Timezone/TimezoneCoded";
    Node node = evaluateNode(XCBL_ROOT, path);

    String content = node.getTextContent();
    assertEquals("12.00", content);

    Number nb = xQuery.evaluateNumberContent(node);
    assertEquals(12.0, nb);
  }

  @Test
  public void testEvaluateNumberContentWithNoNumber() {
    String path = "//InvoiceHeader/InvoiceNumber";
    Node node = evaluateNode(XCBL_ROOT, path);

    String content = node.getTextContent();
    assertEquals("Invoice/InvoiceHeader/InvoiceNumber", content);

    Number nb = xQuery.evaluateNumberContent(node);
    assertEquals(Double.NaN, nb);
  }

  @Test
  public void testHasOfficialXcblIndex() {
    Assertions.assertEquals("type = XCBL AND description = official_index", XQuery.hasOfficialXcblIndex());
  }
}
