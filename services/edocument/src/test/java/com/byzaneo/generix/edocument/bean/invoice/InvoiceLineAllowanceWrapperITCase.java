package com.byzaneo.generix.edocument.bean.invoice;

import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;

import java.lang.reflect.Field;
import java.math.BigDecimal;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.Mock;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.ui.MissingKeyHandler;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.edocument.api.invoice.InvoiceLineAllowOrChargeWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.InvoiceCsvAllowOrCharge;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;

public class InvoiceLineAllowanceWrapperITCase {

  private MockedStatic<JSFHelper> mockedJSFHelper;

  @InjectMocks
  private InvoiceLineAllowOrChargeWrapper instance;

  @Mock
  private BeanService beanService;

  @BeforeEach
  public void before() {
    Mockito.spy(CsvImportHelper.class);
    instance = new InvoiceLineAllowOrChargeWrapper();

    BeanDescriptor descriptor = new BeanDescriptor();
    BeanDescriptor parentDescriptor = new BeanDescriptor("parent");
    PropertyDescriptor property = new PropertyDescriptor("lineNumber");
    property.setValue(1);
    parentDescriptor.put("lineNumber", property);
    parentDescriptor.addChild(descriptor);

    try {
      Field wrappedField = instance.getClass()
          .getSuperclass()
          .getDeclaredField("wrapped");
      wrappedField.setAccessible(true);
      wrappedField.set(instance, descriptor);
    }
    catch (Exception e) {
      fail("no exception expected");
    }
  }

  @AfterEach
  public void after() {
    try {
      Field wrappedField = instance.getClass()
          .getSuperclass()
          .getDeclaredField("wrapped");
      wrappedField.setAccessible(false);

    }
    catch (Exception e) {
      fail("no exception expected");
    }
  }

  @BeforeEach
  void setUpStaticMocks() {
    mockedJSFHelper = mockStatic(JSFHelper.class, invocation -> {
      if (invocation.getMethod()
          .getName()
          .equals("getMissingKeyHandler")) {
        return Mockito.mock(MissingKeyHandler.class);
      }
      return invocation.callRealMethod();
    });
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedJSFHelper.closeOnDemand();
  }

  @Test
  public void testValidateNoHeader() {
    InvoiceCsvAllowOrCharge allowance = new InvoiceCsvAllowOrCharge();

    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(null);

      try {
        instance.validate(allowance, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("header");
        Assertions.assertEquals("Aucune entête liée à cet élément", serviceException.getMessage());
      }
    }
  }

  @Test
  public void testValidateUnknownAssociatedLine() {
    InvoiceCsvAllowOrCharge allowance = new InvoiceCsvAllowOrCharge();
    allowance.setLineNumber(5);

    Invoice invoice = new Invoice();
    invoice.setInvoiceDetail(new InvoiceDetailType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(invoice);

      try {

        instance.validate(allowance, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        assertTrue(asList(
            "Remise/charge liée à la ligne 1 est inconnue",
            "Unknown line for allowance or charge : 1")
            .contains(e.getExceptions()
                .get("line allow or charge")
                .getMessage()));
      }
    }
  }

  @Test
  public void testValidateUnknownTypes() {
    InvoiceCsvAllowOrCharge allowance = new InvoiceCsvAllowOrCharge();
    allowance.setLineNumber(1);
    allowance.setServiceCode("ZZZ");
    allowance.setInformationType("3");
    allowance.setType("Z");

    Invoice invoice = new Invoice();
    invoice.setInvoiceDetail(new InvoiceDetailType());
    InvoiceItemDetailType item = new InvoiceItemDetailType();
    item.setInvoiceBaseItemDetail(new InvoiceBaseItemDetailType());
    item.getInvoiceBaseItemDetail()
        .setLineItemNum(new LineItemNumType());
    item.getInvoiceBaseItemDetail()
        .getLineItemNum()
        .setBuyerLineItemNum(1);

    invoice.getInvoiceDetail()
        .setListOfInvoiceItemDetail(new ListOfInvoiceItemDetailType());
    invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail()
        .getInvoiceItemDetail()
        .add(item);

    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(invoice);

      try {

        instance.validate(allowance, null);
        fail("expected validation exception");
      }
      catch (PropertiesException e) {
        assertTrue(asList(
            "Type Z inconnu pour les remises/charges de la ligne 1",
            "Type Z for allowance or charge is unknown for line 1")
            .contains(e.getExceptions()
                .get("line aoc type")
                .getMessage()));
        assertTrue(asList(
            "Type information 3 inconnu pour les remises/charges de la ligne 1",
            "Information type 3 for allowance or charge is unknown for line 1")
            .contains(e.getExceptions()
                .get("line aoc information")
                .getMessage()));
        assertTrue(asList(
            "Code du service particulier ZZZ inconnu pour les remises/charges de la ligne 1",
            "Service code ZZZ is unknown for line allowances or charge 1")
            .contains(e.getExceptions()
                .get("line aoc service")
                .getMessage()));
      }
    }
  }

  @Test
  public void testValidateA() {
    InvoiceCsvAllowOrCharge allowance = new InvoiceCsvAllowOrCharge();
    allowance.setType("A");
    allowance.setInformationType("2");
    allowance.setServiceCode("X14");
    allowance.setTotalAmount(new BigDecimal("145"));
    allowance.setLabel("Libellé X14");
    allowance.setPercentage(new BigDecimal("4.3"));
    allowance.setTaxPercentage(new BigDecimal("45.6"));
    allowance.setTaxCode("Taxe X14");
    allowance.setLineNumber(1);

    Invoice invoice = new Invoice();
    invoice.setInvoiceDetail(new InvoiceDetailType());
    InvoiceItemDetailType item = new InvoiceItemDetailType();
    item.setInvoiceBaseItemDetail(new InvoiceBaseItemDetailType());
    item.getInvoiceBaseItemDetail()
        .setLineItemNum(new LineItemNumType());
    item.getInvoiceBaseItemDetail()
        .getLineItemNum()
        .setBuyerLineItemNum(1);

    invoice.getInvoiceDetail()
        .setListOfInvoiceItemDetail(new ListOfInvoiceItemDetailType());
    invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail()
        .getInvoiceItemDetail()
        .add(item);

    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(invoice);

      try {
        instance.validate(allowance, null);
        // Check
        InvoiceAllowOrChargeType allowanceOrCharge = invoice.getInvoiceDetail()
            .getListOfInvoiceItemDetail()
            .getInvoiceItemDetail()
            .get(0)
            .getInvoicePricingDetail()
            .getItemAllowancesOrCharges()
            .getAllowOrCharge()
            .get(0);
        assertEquals(MethodOfHandlingCodeType.BILL_BACK, allowanceOrCharge.getMethodOfHandlingCoded());
        assertEquals(IndicatorCodeType.ALLOWANCE, allowanceOrCharge.getIndicatorCoded());
        assertEquals("Taxe X14", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getRefID());
        assertEquals("Libellé X14", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getListOfDescription()
            .getValue());
        assertEquals(PercentQualifierCodeType.OTHER, allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercentQualifier()
            .getPercentQualifierCoded());
        assertEquals("NotIndicated", allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercentQualifier()
            .getPercentQualifierCodedOther());
        assertEquals(new BigDecimal("4.3"), allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercent()
            .getValue());
        assertEquals(new BigDecimal("145"), allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercentageMonetaryValue()
            .getMonetaryAmount());
        assertEquals("ValueAddedTax", allowanceOrCharge.getTax()
            .get(0)
            .getTaxTypeCoded());
        assertEquals(TaxFunctionQualifierCodeType.TAX, allowanceOrCharge.getTax()
            .get(0)
            .getTaxFunctionQualifierCoded());
        assertEquals(TaxCategoryCodeType.OTHER, allowanceOrCharge.getTax()
            .get(0)
            .getTaxCategoryCoded());
        assertEquals("45.6", allowanceOrCharge.getTax()
            .get(0)
            .getTaxCategoryCodedOther()
            .getValue());
        assertEquals(new BigDecimal("45.6"), allowanceOrCharge.getTax()
            .get(0)
            .getTaxPercent()
            .getValue());
        assertEquals(new BigDecimal("-1"), allowanceOrCharge.getTax()
            .get(0)
            .getTaxAmount()
            .getValue());
        assertEquals("Other", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getServiceCoded());
        assertEquals("SpecificSaleLocation", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getServiceCodedOther());

      }
      catch (PropertiesException e) {
        fail("no validation exception expected");
      }
      catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }

  @Test
  public void testValidateC() {
    InvoiceCsvAllowOrCharge allowance = new InvoiceCsvAllowOrCharge();
    allowance.setType("C");
    allowance.setInformationType("1");
    allowance.setServiceCode("ABL");
    allowance.setTotalAmount(new BigDecimal("145"));
    allowance.setLabel("Libellé ABL");
    allowance.setTaxPercentage(new BigDecimal("45.6"));
    allowance.setTaxCode("Taxe ABL");
    allowance.setLineNumber(1);

    Invoice invoice = new Invoice();
    invoice.setInvoiceDetail(new InvoiceDetailType());
    InvoiceItemDetailType item = new InvoiceItemDetailType();
    item.setInvoiceBaseItemDetail(new InvoiceBaseItemDetailType());
    item.getInvoiceBaseItemDetail()
        .setLineItemNum(new LineItemNumType());
    item.getInvoiceBaseItemDetail()
        .getLineItemNum()
        .setBuyerLineItemNum(1);

    invoice.getInvoiceDetail()
        .setListOfInvoiceItemDetail(new ListOfInvoiceItemDetailType());
    invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail()
        .getInvoiceItemDetail()
        .add(item);

    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable()).thenReturn(invoice);

      try {
        instance.validate(allowance, null);
        // Check
        InvoiceAllowOrChargeType allowanceOrCharge = invoice.getInvoiceDetail()
            .getListOfInvoiceItemDetail()
            .getInvoiceItemDetail()
            .get(0)
            .getInvoicePricingDetail()
            .getItemAllowancesOrCharges()
            .getAllowOrCharge()
            .get(0);

        assertEquals(MethodOfHandlingCodeType.INFORMATION_ONLY, allowanceOrCharge.getMethodOfHandlingCoded());
        assertEquals(IndicatorCodeType.CHARGE, allowanceOrCharge.getIndicatorCoded());
        assertEquals("Taxe ABL", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getRefID());
        assertEquals("Libellé ABL", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getListOfDescription()
            .getValue());
        assertNull(allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercentQualifier());
        assertNull(allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercent()
            .getValue());
        assertNull(allowanceOrCharge.getTypeOfAllowanceOrCharge()
            .getPercentageAllowanceOrCharge()
            .getPercentageMonetaryValue());
        assertEquals("ValueAddedTax", allowanceOrCharge.getTax()
            .get(0)
            .getTaxTypeCoded());
        assertEquals(TaxFunctionQualifierCodeType.TAX, allowanceOrCharge.getTax()
            .get(0)
            .getTaxFunctionQualifierCoded());
        assertEquals(TaxCategoryCodeType.OTHER, allowanceOrCharge.getTax()
            .get(0)
            .getTaxCategoryCoded());
        assertEquals("45.6", allowanceOrCharge.getTax()
            .get(0)
            .getTaxCategoryCodedOther()
            .getValue());
        assertEquals(new BigDecimal("45.6"), allowanceOrCharge.getTax()
            .get(0)
            .getTaxPercent()
            .getValue());
        assertEquals(new BigDecimal("-1"), allowanceOrCharge.getTax()
            .get(0)
            .getTaxAmount()
            .getValue());
        assertEquals("Packaging", allowanceOrCharge.getAllowanceOrChargeDescription()
            .getServiceCoded());

      }
      catch (PropertiesException e) {
        fail("no validation exception expected");
      }
      catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }

  }
}
