package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class AtLeastOnceConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/AtLeastOnceConstraintTest.xml"),
      false);

  @Test
  public void testValid() {
    AtLeastOnceConstraint instance = new AtLeastOnceConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/number");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("1"));
    boolean result = instance.isValid();
    assertTrue(result);
    instance.setParams(Arrays.array("2"));
    result = instance.isValid();
    assertTrue(result);
    instance.setParams(Arrays.array("3"));
    result = instance.isValid();
    assertTrue(result);
    instance.setParams(Arrays.array("4"));
    result = instance.isValid();
    assertTrue(result);
    instance.setParams(Arrays.array("5"));
    result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValidText() {
    AtLeastOnceConstraint instance = new AtLeastOnceConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/text");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("exists"));
    boolean result = instance.isValid();
    assertTrue(result);
    instance.setParams(Arrays.array("text"));
    result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testInvalid() {
    AtLeastOnceConstraint instance = new AtLeastOnceConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/number");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("6"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalidText() {
    AtLeastOnceConstraint instance = new AtLeastOnceConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/text");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("exist"));
    boolean result = instance.isValid();
    assertFalse(result);
    instance.setParams(Arrays.array("texts"));
    result = instance.isValid();
    assertFalse(result);
  }
}
