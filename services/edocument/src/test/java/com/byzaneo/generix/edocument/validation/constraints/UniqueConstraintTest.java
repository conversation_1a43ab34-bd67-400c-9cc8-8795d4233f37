package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class UniqueConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/InvoiceForUniqueConstraintValidation.xml"),
      false);

  @Test
  public void testValid1() {
    UniqueConstraint instance = new UniqueConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//Invoice/InvoiceHeader/InvoiceNumber");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid2() {
    UniqueConstraint instance = new UniqueConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//Invoice/InvoiceHeader/InvoiceIssueDate");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid3() {
    UniqueConstraint instance = new UniqueConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT,
        "//Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/LineItemNum/BuyerLineItemNum");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("InvoiceItemDetail/InvoiceBaseItemDetail/LineItemNum"));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testNotValid1() {
    UniqueConstraint instance = new UniqueConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT,
        "//Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail/InvoiceBaseItemDetail/ItemIdentifiers");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("InvoiceItemDetail/InvoiceBaseItemDetail"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid2() {
    UniqueConstraint instance = new UniqueConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//Invoice/InvoiceHeader/InvoicePurpose");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }
}
