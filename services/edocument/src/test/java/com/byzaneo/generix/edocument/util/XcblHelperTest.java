package com.byzaneo.generix.edocument.util;

import static com.byzaneo.commons.util.DomHelper.evaluateNode;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.commons.util.JAXBHelper;
import com.byzaneo.generix.test.XmlUtils;
import com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.Order;
import com.byzaneo.xtrade.xcbl.bean.OrderResponse;
import com.byzaneo.xtrade.xcbl.util.InvoiceHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.AllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.AllowOrChgDescType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.IdentifierType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceAllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.LanguageType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfAllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.MonetaryValueType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.NameValueSetType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TypeOfAllowanceOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNOrderNumberType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNPurposeCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNTypeCodeType;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import javax.xml.bind.JAXBException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.w3c.dom.Node;

@ExtendWith(MockitoExtension.class)
public class XcblHelperTest {

  private static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy");
  private Order order;

  @BeforeEach
  public void setUp() throws Exception {
    order = JAXBHelper.unmarshal(Order.class,
        new File("./src/test/resources/xcbl/OrderSample-ABC_xCBL40.xml"));
  }

  @AfterEach
  public void tearDown() throws Exception {
    order = null;
  }

  @Test
  public void testInitASNWithOrder() {

    PartyType party = new PartyType();
    party.setPartyID(new IdentifierType());
    party.getPartyID()
        .setIdent("bill to party");

    Order order = mock(Order.class, RETURNS_DEEP_STUBS);
    when(order.getOrderHeader()
        .getOrderNumber()
        .getBuyerOrderNumber()).thenReturn("ORDER-001");
    when(order.getOrderHeader()
        .getOrderNumber()
        .getSellerOrderNumber()).thenReturn("SELLER-001");
    when(order.getOrderHeader()
        .getOrderLanguage()
        .getLanguageCoded()).thenReturn(LanguageCodeType.ZH);
    LanguageType languageType = new LanguageType();
    languageType.setLanguageCoded(LanguageCodeType.ZH);
    //languageType.set
    when(order.getOrderHeader()
        .getOrderLanguage()).thenReturn(languageType);

    when(order.getOrderHeader()
        .getOrderCurrency()
        .getCurrencyCoded()).thenReturn(CurrencyCodeType.YUN);
    CurrencyType currencyType = new CurrencyType();
    currencyType.setCurrencyCoded(CurrencyCodeType.YUN);
    when(order.getOrderHeader()
        .getOrderCurrency()).thenReturn(currencyType);
    when(order.getOrderHeader()
        .getOrderParty()
        .getBillToParty()).thenReturn(party);
    when(order.getOrderHeader()
        .getOrderHeaderNote()).thenReturn("Notes pour la commande");
    try {
      AdvanceShipmentNotice asn = AsnXcblHelper.initASNWithOrder(order);
      assertEquals("ASN-ORDER-001", asn.getASNHeader()
          .getASNNumber());
      assertEquals("ORDER-001", asn.getASNHeader()
          .getASNOrderNumber()
          .get(0)
          .getBuyerOrderNumber());
      assertEquals("SELLER-001", asn.getASNHeader()
          .getASNOrderNumber()
          .get(0)
          .getSellerOrderNumber());
      assertEquals(ASNPurposeCodeType.ORIGINAL, asn.getASNHeader()
          .getASNPurpose()
          .getASNPurposeCoded());
      assertEquals(ASNTypeCodeType.OTHER, asn.getASNHeader()
          .getASNType()
          .getASNTypeCoded());
      assertEquals("DespatchAdvice", asn.getASNHeader()
          .getASNType()
          .getASNTypeCodedOther());
      assertEquals(LanguageCodeType.ZH, asn.getASNHeader()
          .getASNLanguage()
          .getLanguageCoded());
      assertEquals(CurrencyCodeType.YUN, asn.getASNHeader()
          .getASNCurrency()
          .getCurrencyCoded());
      assertEquals("bill to party", asn.getASNHeader()
          .getASNParty()
          .getBillToParty()
          .getPartyID()
          .getIdent());
      assertEquals("Notes pour la commande", asn.getASNHeader()
          .getASNHeaderNote());

    } catch (Exception e) {
      fail();
    }
  }

  @Test
  public void initOrderResponse() throws Exception {
    OrderResponse orderResponse = OrderResponseXcblHelper.initOrderResponse(order, null);
    assertNotNull(orderResponse);
    assertEquals(new BigDecimal("111"),
        orderResponse.getOrderResponseDetail()
            .getListOfOrderResponseItemDetail()
            .getOrderResponseItemDetail()
            .get(0)
            .getOriginalItemDetailWithChanges()
            .getDeliveryDetail()
            .getListOfSplitQuantity()
            .getSplitQuantity()
            .get(0)
            .getSplitQuantity()
            .getQuantityValue()
            .getValue());
    assertEquals("21/10/2001",
        DATE_FORMAT.format(orderResponse.getOrderResponseDetail()
            .getListOfOrderResponseItemDetail()
            .getOrderResponseItemDetail()
            .get(0)
            .getOriginalItemDetailWithChanges()
            .getDeliveryDetail()
            .getListOfSplitQuantity()
            .getSplitQuantity()
            .get(0)
            .getEstimatedDeliveryDate()));
    assertEquals("RSP" + order.getOrderHeader()
        .getOrderNumber()
        .getBuyerOrderNumber(), orderResponse.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getSellerOrderResponseNumber());
  }

  @Test
  public void initOrderResponseWithoutDeliveryDateDetail() throws Exception {
    order.getOrderDetail()
        .getListOfItemDetail()
        .getItemDetail()
        .get(0)
        .getDeliveryDetail()
        .getListOfScheduleLine()
        .getScheduleLine()
        .get(0)
        .getRequestedDeliveryDateOrListOfOtherDeliveryDate()
        .remove(0);
    OrderResponse orderResponse = OrderResponseXcblHelper.initOrderResponse(order, null);

    assertNotNull(orderResponse);
    assertEquals(new BigDecimal("111"),
        orderResponse.getOrderResponseDetail()
            .getListOfOrderResponseItemDetail()
            .getOrderResponseItemDetail()
            .get(0)
            .getOriginalItemDetailWithChanges()
            .getDeliveryDetail()
            .getListOfSplitQuantity()
            .getSplitQuantity()
            .get(0)
            .getSplitQuantity()
            .getQuantityValue()
            .getValue());
    assertEquals("20/10/2001",
        DATE_FORMAT.format(orderResponse.getOrderResponseDetail()
            .getListOfOrderResponseItemDetail()
            .getOrderResponseItemDetail()
            .get(0)
            .getOriginalItemDetailWithChanges()
            .getDeliveryDetail()
            .getListOfSplitQuantity()
            .getSplitQuantity()
            .get(0)
            .getEstimatedDeliveryDate()));

    assertEquals("RSP" + order.getOrderHeader()
        .getOrderNumber()
        .getBuyerOrderNumber(), orderResponse.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getSellerOrderResponseNumber());
  }

  @Test
  public void initOrderResponseWithoutDeliveryDate() throws Exception {
    order.getOrderHeader()
        .getOrderDates()
        .setRequestedDeliverByDate(null);
    order.getOrderDetail()
        .getListOfItemDetail()
        .getItemDetail()
        .get(0)
        .getDeliveryDetail()
        .getListOfScheduleLine()
        .getScheduleLine()
        .get(0)
        .getRequestedDeliveryDateOrListOfOtherDeliveryDate()
        .remove(0);
    OrderResponse orderResponse = OrderResponseXcblHelper.initOrderResponse(order, null);
    assertNotNull(orderResponse);
    assertEquals(new BigDecimal("111"),
        orderResponse.getOrderResponseDetail()
            .getListOfOrderResponseItemDetail()
            .getOrderResponseItemDetail()
            .get(0)
            .getOriginalItemDetailWithChanges()
            .getDeliveryDetail()
            .getListOfSplitQuantity()
            .getSplitQuantity()
            .get(0)
            .getSplitQuantity()
            .getQuantityValue()
            .getValue());
    assertEquals(DATE_FORMAT.format(DateUtils.addDays(new Date(), 1)),
        DATE_FORMAT.format(orderResponse.getOrderResponseDetail()
            .getListOfOrderResponseItemDetail()
            .getOrderResponseItemDetail()
            .get(0)
            .getOriginalItemDetailWithChanges()
            .getDeliveryDetail()
            .getListOfSplitQuantity()
            .getSplitQuantity()
            .get(0)
            .getEstimatedDeliveryDate()));
    assertEquals("RSP" + order.getOrderHeader()
        .getOrderNumber()
        .getBuyerOrderNumber(), orderResponse.getOrderResponseHeader()
        .getOrderResponseNumber()
        .getSellerOrderResponseNumber());
  }

  @Test
  public void initASNWithOrderNull() {
    AdvanceShipmentNotice desadv = AsnXcblHelper.initASNWithOrder(null);
    assertNotNull(desadv);
  }

  @Test
  public void initASNWithOrderNotNull() throws Exception {
    AdvanceShipmentNotice desadv = AsnXcblHelper.initASNWithOrder(order);
    assertEquals(order.getOrderHeader()
            .getOrderParty()
            .getBuyerParty()
            .getPartyID()
            .getIdent(),
        desadv.getASNHeader()
            .getASNParty()
            .getBuyerParty()
            .getPartyID()
            .getIdent());
  }

  @Test
  public void initInvoiceWithOrderNull() throws JAXBException {
    try {
      InvoiceXcblHelper.initInvoiceWithOrderStandard(null, 2, 2);
      fail("Should throw Illegal argument exception");
    } catch (IllegalArgumentException e) {
      assert (e.getMessage()
          .contains("Order null : Illegal arguments"));
    }
  }

  @Test
  public void initInvoiceWithOrderTest_testWithoutDates()
      throws JAXBException, FileNotFoundException, IOException {
    Invoice invoice = InvoiceXcblHelper.initInvoiceWithOrderStandard(order, 2, 2);
    InvoiceXcblHelper.cleanEmptyTagsInvoiceReferencesHeader(invoice);
    Optional.ofNullable(
            invoice.getInvoiceDetail().getListOfInvoiceItemDetail().getInvoiceItemDetail())
        .orElseGet(Collections::emptyList)
        .stream().filter(Objects::nonNull)
        .forEach(invoiceItemDetail -> invoiceItemDetail.getInvoiceBaseItemDetail()
            .setLineItemReferences(null));
    File invoiceOutFile = new File("target", "test/invoice/invoice.xml");
    invoiceOutFile.getParentFile()
        .mkdirs();
    File invoiceExpectedFile = new File("src/test/resources/xcbl/invoice/InvoiceTest.xml");
    JAXBHelper.marshal(invoice, invoiceOutFile);

    // Ignore dates
    List<String> ignoreXPath = new ArrayList<String>(Arrays.asList(
        "/Invoice/InvoiceHeader/InvoiceIssueDate",
        "/Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/PurchaseOrderDate",
        "/Invoice/InvoiceHeader/InvoiceDates/InvoiceDueDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ExpectedShipDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ActualShipDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ExpectedDeliveryDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ActualDeliveryDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ListOfOtherInvoiceDates"));
    requestedDeliveryDateXPath(invoice, ignoreXPath);
    XmlUtils.assertXmlEquals(invoiceOutFile, invoiceExpectedFile,
        ignoreXPath.toArray(new String[ignoreXPath.size()]));
  }

  @Test
  public void testConvertToListOfInvoiceAllowOrChargeType() {
    Invoice invoice = InvoiceXcblHelper.initInvoiceWithOrderStandard(order, 2, 2);

    assertEquals(0, invoice.getInvoiceHeader()
        .getInvoiceAllowancesOrCharges()
        .getAllowOrCharge()
        .size());

    ListOfAllowOrChargeType listOfAllowOrChargeType = new ListOfAllowOrChargeType();
    AllowOrChargeType allowOrChargeType = new AllowOrChargeType();
    AllowOrChgDescType description = new AllowOrChgDescType();
    description.setListOfDescription("Test list of description");
    description.setServiceCoded("serviceCode");
    description.setServiceCodedOther("serviceCodeOther");
    allowOrChargeType.setAllowanceOrChargeDescription(description);

    TypeOfAllowanceOrChargeType typeOfAllowanceOrChargeType = new TypeOfAllowanceOrChargeType();
    MonetaryValueType monetaryValue = new MonetaryValueType();
    monetaryValue.setMonetaryAmount(new BigDecimal("1000.0"));
    typeOfAllowanceOrChargeType.setMonetaryValue(monetaryValue);
    allowOrChargeType.setTypeOfAllowanceOrCharge(typeOfAllowanceOrChargeType);
    listOfAllowOrChargeType.getAllowOrCharge()
        .add(allowOrChargeType);
    order.getOrderHeader()
        .setOrderAllowancesOrCharges(listOfAllowOrChargeType);

    invoice = InvoiceXcblHelper.initInvoiceWithOrderStandard(order, 2, 2);

    InvoiceAllowOrChargeType invoiceAllowOrChargeType = invoice.getInvoiceHeader()
        .getInvoiceAllowancesOrCharges()
        .getAllowOrCharge()
        .get(0);

    assertEquals(new BigDecimal("1000.0"),
        InvoiceHelper.getMonetaryAmountFromInvoiceAllowanceOrCharge(invoiceAllowOrChargeType));
    assertEquals("Test list of description",
        InvoiceHelper.getListOfDescriptionFromInvoiceAllowanceOrCharge(invoiceAllowOrChargeType));
    assertEquals("serviceCode", invoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getServiceCoded());
    assertEquals("serviceCodeOther", invoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getServiceCodedOther());

    AllowOrChargeType allowOrChargeType2 = new AllowOrChargeType();
    AllowOrChgDescType description2 = new AllowOrChgDescType();
    description2.setListOfDescription("Second test list of description");
    description2.setServiceCoded("serviceCode2");
    description2.setServiceCodedOther("serviceCodeOther2");
    allowOrChargeType2.setAllowanceOrChargeDescription(description2);

    TypeOfAllowanceOrChargeType typeOfAllowanceOrChargeType2 = new TypeOfAllowanceOrChargeType();
    MonetaryValueType monetaryValue2 = new MonetaryValueType();
    monetaryValue2.setMonetaryAmount(new BigDecimal("2000.0"));
    typeOfAllowanceOrChargeType2.setMonetaryValue(monetaryValue2);
    allowOrChargeType2.setTypeOfAllowanceOrCharge(typeOfAllowanceOrChargeType2);
    listOfAllowOrChargeType.getAllowOrCharge()
        .add(allowOrChargeType2);
    order.getOrderHeader()
        .setOrderAllowancesOrCharges(listOfAllowOrChargeType);

    invoice = InvoiceXcblHelper.initInvoiceWithOrderStandard(order, 2, 2);

    invoiceAllowOrChargeType = invoice.getInvoiceHeader()
        .getInvoiceAllowancesOrCharges()
        .getAllowOrCharge()
        .get(0);

    assertEquals(new BigDecimal("1000.0"),
        InvoiceHelper.getMonetaryAmountFromInvoiceAllowanceOrCharge(invoiceAllowOrChargeType));
    assertEquals("Test list of description",
        InvoiceHelper.getListOfDescriptionFromInvoiceAllowanceOrCharge(invoiceAllowOrChargeType));
    assertEquals("serviceCode", invoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getServiceCoded());
    assertEquals("serviceCodeOther", invoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getServiceCodedOther());

    invoiceAllowOrChargeType = invoice.getInvoiceHeader()
        .getInvoiceAllowancesOrCharges()
        .getAllowOrCharge()
        .get(1);

    assertEquals(new BigDecimal("2000.0"),
        InvoiceHelper.getMonetaryAmountFromInvoiceAllowanceOrCharge(invoiceAllowOrChargeType));
    assertEquals("Second test list of description",
        InvoiceHelper.getListOfDescriptionFromInvoiceAllowanceOrCharge(invoiceAllowOrChargeType));
    assertEquals("serviceCode2", invoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getServiceCoded());
    assertEquals("serviceCodeOther2", invoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getServiceCodedOther());
  }

  /*
   * The timezone generated by jenkins might differ from those generated locally therefore, tests can pass locally but fail on jenkins
   */
  @Test
  public void initInvoiceWithOrderTest_testOnlyDates()
      throws JAXBException, FileNotFoundException, IOException {
    Invoice invoice = InvoiceXcblHelper.initInvoiceWithOrderStandard(order, 2, 2);

    File invoiceOutFile = new File("target", "test/invoice/invoice.xml");
    invoiceOutFile.getParentFile()
        .mkdirs();
    File invoiceExpectedFile = new File("src/test/resources/xcbl/invoice/InvoiceTest.xml");
    JAXBHelper.marshal(invoice, invoiceOutFile);

    // Compare the following dates
    List<String> testDatesXPath = new ArrayList<String>(Arrays.asList(
        "/Invoice/InvoiceHeader/InvoiceReferences/PurchaseOrderReference/PurchaseOrderDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ExpectedShipDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ActualShipDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ExpectedDeliveryDate",
        "/Invoice/InvoiceHeader/InvoiceDates/ActualDeliveryDate"));
    requestedDeliveryDateXPath(invoice, testDatesXPath);

    Node rootNodeActualInvoice = DomHelper.parseFile(invoiceOutFile, false);
    Node rootNodeExpectedInvoice = DomHelper.parseFile(invoiceExpectedFile, false);

    List<String> actualXpathDates = new ArrayList<String>();
    List<String> expectedXpathDates = new ArrayList<String>();
    final int substringStart = 0;
    final int substringEnd = 19;
    for (String xPath : testDatesXPath) {
      // Remove the timezone from dates ("YYYY-MM-DDTHH:MM:SS")
      actualXpathDates.add(
          StringUtils.substring(evaluateNode(rootNodeActualInvoice, xPath).getFirstChild()
              .getNodeValue(), substringStart, substringEnd));
      expectedXpathDates.add(
          StringUtils.substring(evaluateNode(rootNodeExpectedInvoice, xPath).getFirstChild()
              .getNodeValue(), substringStart, substringEnd));
    }

    Assertions.assertArrayEquals(
        actualXpathDates.toArray(new String[actualXpathDates.size()]),
        expectedXpathDates.toArray(new String[actualXpathDates.size()]));
  }

  @Test
  public void createNameValueSetNullTest() {
    NameValueSetType nameValueSetType = XcblHelper.createNameValueSet(null, null);
    assertNull(nameValueSetType);
  }

  @Test
  public void createNameValueSetTest() {
    Map<String, String> values = new HashMap<String, String>();
    values.put("key1", "Value 1");
    values.put("key2", "Value 2");
    NameValueSetType nameValueSetType = XcblHelper.createNameValueSet(values, "0000000000");
    assertEquals("DatiTrasmissione", nameValueSetType.getSetName());
    assertEquals(values.size() + 1, nameValueSetType.getListOfNameValuePair()
        .getNameValuePair()
        .size());
  }

  @Test
  public void testCreateASNOrderNumberException() {
    assertThrows(IllegalArgumentException.class, () ->
        AsnXcblHelper.createASNOrderNumber("", "XXX-Y", new Date()));
  }

  @Test
  public void testCreateASNOrderNumber() {
    final ASNOrderNumberType asnOrderNumber = AsnXcblHelper.createASNOrderNumber("XXX-X", "XXX-Y",
        new Date());
    Assertions.assertEquals("XXX-X", asnOrderNumber.getBuyerOrderNumber());
  }

  private void requestedDeliveryDateXPath(Invoice invoice, List<String> ignoreXPath) {
    AtomicInteger atomicInteger = new AtomicInteger(1);
    invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail()
        .getInvoiceItemDetail()
        .forEach(detail ->
        {
          int index = atomicInteger.getAndIncrement();
          ignoreXPath.add(new StringBuilder()
              .append("/Invoice/InvoiceDetail/ListOfInvoiceItemDetail/InvoiceItemDetail")
              .append(index == 1 ? "" : "[".concat(String.valueOf(index))
                  .concat("]"))
              .append("/DeliveryDetail/ListOfScheduleLine/ScheduleLine/RequestedDeliveryDate")
              .toString());
        });
  }
}
