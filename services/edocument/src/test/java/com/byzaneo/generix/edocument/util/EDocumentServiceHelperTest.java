package com.byzaneo.generix.edocument.util;

import static com.byzaneo.commons.bean.FileType.EXCEL;
import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.util.FileHelper.getResourceFile;
import static com.byzaneo.generix.edocument.util.EDocumentServiceHelper.getExistingPdfFile;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.util.*;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.edocument.bean.ResultingFileAction;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.util.DocumentBuilder;

import de.bwaldvogel.mongo.backend.Assert;
import org.junit.jupiter.api.Test;

public class EDocumentServiceHelperTest {

  @Test
  public void getExistingPdfFile_notPDF() {
    assertNull(getExistingPdfFile(document("Not PDF"), EXCEL));
  }

  @Test
  public void getExistingPdfFile_addFileToInvoice() {
    assertNull(getExistingPdfFile(document(ResultingFileAction.ADD_FILE_TO_INVOICE.getName()), PDF));
  }

  @Test
  public void getExistingPdfFile_birtGeneration() {
    assertNull(getExistingPdfFile(document("_global_/BIRT/invoice/invoice.rptdesign"), PDF)); // Template URI
  }

  @Test
  public void getExistingPdfFile_birtTaskGeneration() {
    assertNotNull(getExistingPdfFile(document(ResultingFileAction.BIRTTASK.getName()), PDF));
  }

  @Test
  public void getExistingPdfFile_attached() {
    assertNotNull(getExistingPdfFile(document("Something happen"), PDF));
  }

  @Test
  public void getBirtGeneratedFilesWithGlobalTemplate() {
    Document doc = new Document();

    DocumentFile dof1 = new DocumentFile();
    dof1.setFile(getResourceFile("/data/xml/invoice-xcblEdition.xml"));
    dof1.setType(FileType.PDF);
    dof1.setActionName("/_global_/BIRT/invoice/invoice");

    DocumentFile dof2 = new DocumentFile();
    dof2.setFile(getResourceFile("/data/xml/invoice-xcblEdition.xml"));
    dof2.setType(FileType.EXCEL);
    dof2.setActionName("/_global_/BIRT/invoice/invoice");

    DocumentFile dof3 = new DocumentFile();
    dof3.setFile(getResourceFile("/data/xml/invoice-xcblEdition.xml"));
    dof3.setType(FileType.PDF);
    dof3.setActionName("addFileToInvoice");

    DocumentFile dof4 = new DocumentFile();
    dof4.setFile(getResourceFile("/data/xml/invoice-xcblEdition.xml"));
    dof4.setType(FileType.PDF);
    dof4.setActionName("uploadpdf");

    doc.addFile(dof1);
    doc.addFile(dof2);
    doc.addFile(dof3);
    doc.addFile(dof4);

    List<DocumentFile> dofs = EDocumentServiceHelper.getBirtGeneratedFiles(doc,
        Arrays.asList(new FileType[] { FileType.PDF, FileType.EXCEL }));
    Assert.equals(dofs.size(), 2);
    Assert.equals("/_global_/BIRT/invoice/invoice", dofs.get(0)
        .getActionName());
    Assert.equals("/_global_/BIRT/invoice/invoice", dofs.get(1)
        .getActionName());
  }

  @Test
  public void getBirtGeneratedFilesWithInstanceTemplate() {
    Document doc = new Document();

    DocumentFile dof1 = new DocumentFile();
    dof1.setFile(getResourceFile("/data/xml/invoice-xcblEdition.xml"));
    dof1.setType(FileType.PDF);
    dof1.setActionName("/ENV/BIRT/invoice/invoice");

    DocumentFile dof2 = new DocumentFile();
    dof2.setFile(getResourceFile("/data/xml/invoice-xcblEdition.xml"));
    dof2.setType(FileType.PDF);
    dof2.setActionName("addFileToInvoice");

    DocumentFile dof3 = new DocumentFile();
    dof3.setFile(getResourceFile("/data/xml/invoice-xcblEdition.xml"));
    dof3.setType(FileType.PDF);
    dof3.setActionName("uploadpdf");

    doc.addFile(dof1);
    doc.addFile(dof2);
    doc.addFile(dof3);

    List<DocumentFile> dofs = EDocumentServiceHelper.getBirtGeneratedFiles(doc,
        Arrays.asList(new FileType[] { FileType.PDF, FileType.EXCEL }));
    Assert.equals(dofs.size(), 1);
    Assert.equals("/ENV/BIRT/invoice/invoice", dofs.get(0)
        .getActionName());
  }

  private static Document document(String actionName) {
    return DocumentBuilder.createDocument()
        .addFile()
        .file(getResourceFile("/data/xml/invoice-xcblEdition.xml"))
        .type(PDF)
        .actionName(actionName) // Template URI
        .document()
        .build();
  }
}
