package com.byzaneo.generix.edocument.service;

import static com.byzaneo.generix.edocument.bean.DocumentLink.Field.source;
import static com.byzaneo.generix.edocument.bean.DocumentLink.Field.target;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.util.QueryHelper.search;
import static com.byzaneo.xtrade.api.DocumentType.INTERCHANGE_EDIFACT;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptySet;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.slf4j.LoggerFactory.getLogger;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.bean.Template;
import com.byzaneo.generix.edocument.ExchangeNotAllowedException;
import com.byzaneo.generix.edocument.bean.DocumentLink;
import com.byzaneo.generix.edocument.bean.DocumentLinkModel;
import com.byzaneo.generix.edocument.bean.ResultingFileAction;
import com.byzaneo.generix.edocument.repository.DocumentLinkOperations;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.service.TransformService;
import com.byzaneo.generix.service.TransformService.Transform;
import com.byzaneo.query.Query;
import com.byzaneo.query.clause.TerminalClause;
import com.byzaneo.query.operand.MultiValueOperand;
import com.byzaneo.query.operand.SingleValueOperand;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Exchange;
import com.byzaneo.security.bean.Exchange.Direction;
import com.byzaneo.security.bean.ExchangeId;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.api.DocumentType;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.Document.ProcessingWay;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.DocumentStatusEntity;
import com.byzaneo.xtrade.bean.Document_;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.util.DocumentHelper;
import com.byzaneo.xtrade.xcbl.api.XcblDocument;
import com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import com.byzaneo.xtrade.xcbl.bean.Order;
import com.byzaneo.xtrade.xcbl.service.XcblService;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ComplexStringType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.DimensionCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PackageItemReferenceType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PackageType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TransportModeCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TransportRoutingType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeType;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.ASNBaseItemDetailType;
import com.google.common.collect.ImmutableList;
import com.mongodb.BasicDBObjectBuilder;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.persistence.metamodel.SingularAttribute;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.LimitOperation;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Dec 14, 2015
 * @since 3.9 GNX-3124
 */
public class EDocumentServiceTest {

  private MockedStatic<DocumentHelper> mockedDocumentHelper;
  private static final Logger log = getLogger(EDocumentServiceTest.class);
  private static final Integer DOCUMENT_COUNT = 20;

  @InjectMocks
  private EDocumentServiceImpl service;

  @Mock
  private final DocumentLinkOperations documentLinkOperations = Mockito.mock(
      DocumentLinkOperations.class);
  @Mock
  private final DocumentService documentService = Mockito.mock(DocumentService.class);
  @Mock
  private final XcblService xcblService = Mockito.mock(XcblService.class);
  @Mock
  private final SecurityService securityService = Mockito.mock(SecurityService.class);
  @Mock
  private final TransformService transformService = Mockito.mock(TransformService.class);
  @TempDir
  public File folder;

  private Map<Long, Document> documents;
  private List<DocumentLink> links;

  static {
    mockDocument();
  }

  @SuppressWarnings("unchecked")
  private static void mockDocument() {
    Document_.owners = mock(SingularAttribute.class);
    Document_.from = mock(SingularAttribute.class);
  }

  @BeforeEach
  @SuppressWarnings("unchecked")
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    service = spy(service);

    // Documents
    this.documents = new HashMap<>(DOCUMENT_COUNT);
    DocumentType[] types = DocumentType.values();
    for (Long i = 1L; i <= DOCUMENT_COUNT; i++) {
      DocumentStatus nullstatus = null;
      Document doc = new Document("Document " + i, null,
          types[(i.intValue() % types.length)].toString(), null, null, nullstatus);
      doc.setId(i);
      doc.getIndex()
          .setReference(String.valueOf(i));
      this.documents.put(i, doc);
    }

    // Links
    this.links = new ArrayList<>();
    for (Long i = 2L; i < DOCUMENT_COUNT; i++) {
      this.links.add(this.createLink(DOCUMENT_COUNT + i, String.valueOf(i - 1), String.valueOf(i)));
      this.links.add(
          this.createLink((DOCUMENT_COUNT * 2) + i, String.valueOf(i), String.valueOf(i + 1)));
    }

    // mock: documentService#getDocument
    Mockito.when(documentService.getDocument(any(Long.class)))
        .thenAnswer(
            invocation -> documents.get(Arrays.stream(invocation.getArguments()).toList().get(0)));

    // mock: documentLinkOperations.findByQuery
    Mockito.when(
            documentLinkOperations.findByQuery(Mockito.any(Query.class), Mockito.any(Pageable.class)))
        .thenAnswer(invocation ->
        {
          Query q = (Query) Arrays.stream(invocation.getArguments()).toList().get(0);
          // no returned link query (docid==1l)
          boolean nolink = createBuilder()
              .or(equal(source.toString(), "1"), equal(target.toString(), "1"))
              .query()
              .equals(q);
          return new PageImpl<>(
              nolink
                  ? emptyList()
                  : search(q, DocumentLink.class, links));
        });

    Mockito.when(documentLinkOperations.findByQuery(Mockito.any(Query.class), nullable(Pageable.class)))
        .thenAnswer(invocation ->
        {
          Query q = (Query) Arrays.stream(invocation.getArguments()).toList().get(0);
          // no returned link query (docid==1l)
          boolean nolink = createBuilder()
              .or(equal(source.toString(), "1"), equal(target.toString(), "1"))
              .query()
              .equals(q);
          return new PageImpl<>(
              nolink
                  ? emptyList()
                  : search(q, DocumentLink.class, links));
        });

    // mock: this.documentService.search
    Mockito.when(documentService.search(Mockito.any(Query.class), Mockito.any(Pageable.class)))
        .thenAnswer(invocation ->
        {
          TerminalClause in = (TerminalClause) ((Query) Arrays.stream(invocation.getArguments())
              .toList().get(0))
              .getWhereClause();
          List<String> docidxrefs = ((MultiValueOperand) in.getOperand())
              .getValues()
              .stream()
              .map(op -> ((SingleValueOperand) op).getStringValue())
              .collect(Collectors.toList());
          return new PageImpl<>(documents.values()
              .stream()
              .filter(d -> docidxrefs.contains(d.getIndexReference()))
              .collect(Collectors.toList()));
        });

    Mockito.when(documentService.search(Mockito.any(Query.class), nullable(Pageable.class)))
        .thenAnswer(invocation ->
        {
          TerminalClause in = (TerminalClause) ((Query) Arrays.stream(invocation.getArguments())
              .toList().get(0))
              .getWhereClause();
          List<String> docidxrefs = ((MultiValueOperand) in.getOperand())
              .getValues()
              .stream()
              .map(op -> ((SingleValueOperand) op).getStringValue())
              .collect(Collectors.toList());
          return new PageImpl<>(documents.values()
              .stream()
              .filter(d -> docidxrefs.contains(d.getIndexReference()))
              .collect(Collectors.toList()));
        });

    // mock: this.documentLinkOperations.saveAll
    Mockito.when(documentLinkOperations.saveAll(Mockito.anyCollection()))
        .thenAnswer(invocation -> Arrays.stream(invocation.getArguments()).toList().get(0));

    setUpStaticMocks();
  }

  private DocumentLink createLink(Long id, String source, String target) {
    DocumentLink link = new DocumentLink(source, target);
    link.setId(String.valueOf(id));
    return link;
  }

  void setUpStaticMocks() {
    mockedDocumentHelper = Mockito.mockStatic(DocumentHelper.class);
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedDocumentHelper.closeOnDemand();
  }

  /* -- getDocumentLinkModel -- */

  @Test
  public void testGetDocumentLinkModel_null() throws Exception {
    try {
      this.service.getDocumentLinkModel(null);
      fail();
    } catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  public void testGetDocumentLinkModel_notfound() throws Exception {
    try {
      this.service.getDocumentLinkModel(DOCUMENT_COUNT + 1L);
      fail();
    } catch (Exception e) {
      assertEquals(IllegalArgumentException.class, e.getClass());
    }
  }

  @Test
  public void testGetDocumentLinkModel_nolink() throws Exception {
    DocumentLinkModel model = this.service.getDocumentLinkModel(1L);
    // System.out.println(model);
    assertNotNull(model);
    assertEquals(1, model.getDocuments()
        .size());
    Document doc = model.getDocuments()
        .values()
        .iterator()
        .next();
    assertEquals(this.documents.get(1L), doc);
    assertEquals(doc, model.getDocument());
    assertTrue(model.getLinks()
        .isEmpty());
  }

  @Test
  public void testGetDocumentLinkModel_links() throws Exception {
    DocumentLinkModel model = this.service.getDocumentLinkModel(DOCUMENT_COUNT.longValue());
    // System.out.println(model);
    assertNotNull(model);
    assertEquals(DOCUMENT_COUNT.intValue(), model.getDocuments()
        .size());
    assertEquals((DOCUMENT_COUNT - 1), model.getLinks()
        .size());
  }

  @Test
  public void testGetDocumentLinkModel_deprecated() throws Exception {
    // adds deprecated links
    List<DocumentLink> deprecated = ImmutableList.<DocumentLink>builder()
        .add(this.createLink((DOCUMENT_COUNT * 4) + 1L,
            String.valueOf(DOCUMENT_COUNT),
            String.valueOf(DOCUMENT_COUNT * 3L)))
        .add(this.createLink((DOCUMENT_COUNT * 4) + 2L,
            String.valueOf(DOCUMENT_COUNT),
            String.valueOf(DOCUMENT_COUNT * 4L)))
        .build();
    this.links.addAll(deprecated);

    DocumentLinkModel model = this.service.getDocumentLinkModel(DOCUMENT_COUNT.longValue());
    // System.out.println(model);
    assertNotNull(model);
    assertEquals(DOCUMENT_COUNT.intValue(), model.getDocuments()
        .size());
    assertEquals((DOCUMENT_COUNT - 1), model.getLinks()
        .size());

    // removes deprecated links (restore)
    this.links.removeAll(deprecated);
  }

  /* -- createDocumentLinks -- */

  @Test
  public void testCreateDocumentLinks_null() throws Exception {
    assertEquals(0, this.service.createDocumentLinks(null)
        .size());
  }

  @Test
  public void testCreateDocumentLinks_empty() throws Exception {
    assertEquals(0, this.service.createDocumentLinks(emptyList())
        .size());
  }

  @Test
  public void testCreateDocumentLinks() throws Exception {
    doReturn(new PageImpl<>(emptyList()))
        .when(documentLinkOperations)
        .findByQuery(any(Query.class), isNull());
    Collection<DocumentLink> links = this.service.createDocumentLinks(
        Arrays.asList("1L", "2L", "3L", "4L"));

    // System.out.println(links);
    assertEquals(6, links.size());
  }

  @Test
  public void testCreateDocumentLinks_duplicate() throws Exception {
    Collection<DocumentLink> links = this.service.createDocumentLinks(
        Arrays.asList("1L", "2L", "2L", "3L", "4L", "4L"));
    assertEquals(6, links.size());
  }

  @Test
  public void testCreateDocumentLinks_nullrefs() throws Exception {
    Collection<DocumentLink> links = this.service.createDocumentLinks(
        Arrays.asList("1L", "2L", null, "3L", "4L", null));
    // System.out.println(links);
    assertEquals(6, links.size());
  }

  @Test
  public void testCreateDocumentLinks_withExistingLinks() throws Exception {
    List<DocumentLink> existingLinks = new ArrayList<>();
    existingLinks.add(new DocumentLink("1L", "2L"));
    existingLinks.add(new DocumentLink("2L", "4L"));
    doReturn(new PageImpl<>(existingLinks)).when(documentLinkOperations)
        .findByQuery(any(Query.class), isNull());
    Collection<DocumentLink> links = this.service.createDocumentLinks(
        Arrays.asList("1L", "2L", "2L", "3L", "4L", "4L"));
    assertEquals(4, links.size());
  }

  @Test
  public void testCreateDocumentLinks_withAllExistingLinks() throws Exception {
    List<DocumentLink> existingLinks = new ArrayList<>();
    existingLinks.add(new DocumentLink("1L", "2L"));
    existingLinks.add(new DocumentLink("1L", "3L"));
    existingLinks.add(new DocumentLink("1L", "4L"));
    existingLinks.add(new DocumentLink("2L", "3L"));
    existingLinks.add(new DocumentLink("2L", "4L"));
    existingLinks.add(new DocumentLink("3L", "4L"));
    doReturn(new PageImpl<>(existingLinks)).when(documentLinkOperations)
        .findByQuery(any(Query.class), isNull());
    Collection<DocumentLink> links = this.service.createDocumentLinks(
        Arrays.asList("1L", "2L", "2L", "3L", "4L", "4L"));
    assertEquals(0, links.size());
  }
  /* -- resolveDocumentLinks -- */

  @Test
  public void testResolveDocumentLinks_nulldoc() throws Exception {
    int count = this.service.resolveDocumentLinks(null);
    assertEquals(0, count);
  }

  @Test
  public void testResolveDocumentLinks_notindexed() throws Exception {
    int count = this.service.resolveDocumentLinks(new Document());
    assertEquals(0, count);
  }

  @Test
  @SuppressWarnings("serial")
  public void testResolveDocumentLinks_noindexable() throws Exception {
    Mockito.when(documentService.getIndexable(Mockito.any(Document.class)))
        .thenReturn(null);

    Document doc = new Document() {
      {
        setIndexValue(new Order());
      }
    };
    doc.getIndex()
        .setValue(null);
    int count = this.service.resolveDocumentLinks(doc);
    assertEquals(0, count);
  }

  @Test
  @SuppressWarnings("serial")
  public void testResolveDocumentLinks_noref() throws Exception {
    Mockito.when(xcblService.getReferences(Mockito.any(XcblDocument.class), Mockito.anyBoolean()))
        .thenReturn(emptySet());

    int count = this.service.resolveDocumentLinks(new Document() {
      {
        setIndexValue(new Order());
      }
    });
    assertEquals(0, count);
  }

  @Test
  @SuppressWarnings("serial")
  public void testResolveDocumentLinks_newrefs() throws Exception {
    // xrefs
    Mockito.when(xcblService.getReferences(Mockito.any(XcblDocument.class), Mockito.anyBoolean()))
        .thenReturn(Arrays.asList(
            new Order() {
              {
                setEntityRef("A");
              }
            },
            new Order() {
              {
                setEntityRef("B");
              }
            }));

    int count = this.service.resolveDocumentLinks(new Document() {
      {
        setIndexValue(new Order() {
          {
            setEntityRef("C");
          }
        });
      }
    });
    assertEquals(2, count);
  }

  @Test
  @SuppressWarnings("serial")
  public void testResolveDocumentLinks_newref() throws Exception {
    // xrefs
    Mockito.when(xcblService.getReferences(Mockito.any(XcblDocument.class), Mockito.anyBoolean()))
        .thenReturn(Arrays.asList(
            new Order() {
              {
                setEntityRef("A");
              }
            },
            new Order() {
              {
                setEntityRef("B");
              }
            }));
    Mockito.when(documentLinkOperations.findByQuery(createBuilder()
            .or(equal(source.toString(), "C"), equal(target.toString(), "C"))
            .query(), null))
        .thenReturn(new PageImpl<>(Arrays.asList(new DocumentLink("B", "C"))));

    int count = this.service.resolveDocumentLinks(new Document() {
      {
        setIndexValue(new Order() {
          {
            setEntityRef("C");
          }
        });
      }
    });
    assertEquals(1, count);
  }

  /**
   * Checks that an EDIFACT interchange is ignored by checkDocumentExchanges and returned with a
   * {@code null} mapping in the exceptions map.
   */
  @Test
  public void testCheckDocumentExchangesIgnoreInterchange() {
    Mockito.when(securityService.getGroupByName(any()))
        .thenReturn(new Company());

    Document doc = new Document("ref", "owner", INTERCHANGE_EDIFACT.toString(), "from", "to",
        DocumentStatus.NONE);

    Map<Document, ExchangeNotAllowedException> exceptions = this.service.checkDocumentExchanges(
        "some non-null company code",
        Direction.R,
        singletonList(doc),
        true, null, false, false, false, "to", "from");

    assertNotNull(exceptions);
    assertTrue(exceptions.containsKey(doc));
    assertNull(exceptions.get(doc));
  }

  @Test
  public void testCheckDocumentExchangesWithDocsProcessingWay() {
    Mockito.when(securityService.getGroupByName(any()))
        .thenReturn(new Company());

    Document doc = new Document("ref", "owner", INTERCHANGE_EDIFACT.toString(), "from", "to",
        DocumentStatus.NONE);
    doc.setProcessingWay(ProcessingWay.SENDING);

    Map<Document, ExchangeNotAllowedException> exceptions = this.service.checkDocumentExchanges(
        "some non-null company code",
        Direction.R,
        singletonList(doc),
        true, null, false, false, true, "to", "from");

    assertNotNull(exceptions);
    assertTrue(exceptions.containsKey(doc));
    assertNull(exceptions.get(doc));
  }

  @Test
  public void testCheckDocumentExchangesWithoutDocsProcessingWay() {
    Mockito.when(securityService.getGroupByName(any()))
        .thenReturn(new Company());

    Document doc = new Document("ref", "owner", INTERCHANGE_EDIFACT.toString(), "from", "to",
        DocumentStatus.NONE);

    Map<Document, ExchangeNotAllowedException> exceptions = this.service.checkDocumentExchanges(
        "some non-null company code",
        Direction.R,
        singletonList(doc),
        true, null, false, false, true, "to", "from");

    assertNotNull(exceptions);
    assertTrue(exceptions.containsKey(doc));
    assertEquals(exceptions.get(doc)
        .getCode(), ExchangeNotAllowedException.Code.processing_way_missing);
  }

  @Test
  public void testSearchExchangeFromSqlIllegalArgs() {
    Company company = Mockito.mock(Company.class);
    Partner partner = Mockito.mock(Partner.class);

    Assertions.assertTrue(service.searchExchange(null, null, null, null, null, false) == null &&
        service.searchExchange("", null, null, null, null, false) == null &&
        service.searchExchange("INVOIC", null, null, null, null, false) == null &&
        service.searchExchange("INVOIC", company, null, null, null, false) == null &&
        service.searchExchange("INVOIC", company, partner, null, null, false) == null);
  }

  @Test
  public void testSearchExchangeFromSqlEmptyExchg() {
    Company company = Mockito.mock(Company.class);
    Mockito.when(company.getLegalEntityExchanges())
        .thenReturn(new ArrayList<>());
    Partner partner = Mockito.mock(Partner.class);
    Mockito.when(partner.getLegalEntityExchanges())
        .thenReturn(new ArrayList<>());

    Assertions.assertTrue(
        service.searchExchange("INVOIC", company, partner, Direction.S, null, false) == null &&
            service.searchExchange("INVOIC", company, partner, Direction.R, null, false) == null);
  }

  @Test
  public void testSearchExchangeFromSqlUnvalidExchg() {
    Company company = Mockito.mock(Company.class);
    Exchange exchange = Mockito.mock(Exchange.class);
    ExchangeId exchangeId = Mockito.mock(ExchangeId.class);
    Partner partner = Mockito.mock(Partner.class);
    Mockito.when(exchange.getId())
        .thenReturn(exchangeId);
    Mockito.when(exchangeId.getKind())
        .thenReturn("INVOIC");
    Mockito.when(exchangeId.getDirection())
        .thenReturn(Direction.S);
    Mockito.when(exchangeId.getLegalEntity())
        .thenReturn(company);
    Mockito.when(exchangeId.getDematPartner())
        .thenReturn(partner);
    Mockito.when(exchange.getStart())
        .thenReturn(DateUtils.addDays(new Date(), 2));
    Mockito.when(company.getId())
        .thenReturn("XXXXX");
    Mockito.when(company.getLegalEntityExchanges())
        .thenReturn(Arrays.asList(exchange));
    Mockito.when(partner.getId())
        .thenReturn("YYYYY");

    Assertions.assertNull(
        service.searchExchange("INVOIC", company, partner, Direction.S, new Date(), false));
  }

  @Test
  public void testGenerateFileWithTemplateFromArchivedInvoiceWhenDocumentIsNull() {
    Assertions.assertNull(
        service.generateFileWithTemplateFromArchivedInvoice(null, Locale.FRENCH, null, null,
            FileType.PDF, false, null,
            DocumentType.INVOIC));
  }

  @Test
  public void testGenerateFileWithTemplateFromArchivedInvoiceWhenTemplateIsSetInPartner() {
    Template template = Mockito.mock(Template.class);
    Partner partner = Mockito.mock(Partner.class);
    Document document = Mockito.mock(Document.class);
    Mockito.doReturn(Optional.of(template))
        .when(service)
        .getTemplateFromGroup(Mockito.eq(document),
            Mockito.eq(partner), Mockito.eq(DocumentType.INVOIC), Mockito.eq(FileType.PDF));
    service.getTemplateFromPartnerOrCompany(document, Locale.FRENCH, Mockito.mock(Instance.class),
        partner, DocumentType.INVOIC,
        FileType.PDF);

    Mockito.verify(service)
        .getTemplateFromGroup(Mockito.eq(document), Mockito.eq(partner),
            Mockito.eq(DocumentType.INVOIC), Mockito.eq(FileType.PDF));
  }

  @Test
  public void testGenerateFileWithTemplateFromArchivedInvoiceWhenTemplateIsSetInCompany() {
    Template template = Mockito.mock(Template.class);
    Instance instance = Mockito.mock(Instance.class);
    Partner partner = Mockito.mock(Partner.class);
    Group group = Mockito.mock(Group.class);
    Document document = Mockito.mock(Document.class);
    Mockito.doReturn(group)
        .when(instance)
        .getGroup();
    Mockito.doReturn(Optional.empty())
        .when(service)
        .getTemplateFromGroup(Mockito.eq(document), Mockito.eq(partner),
            Mockito.eq(DocumentType.INVOIC), Mockito.eq(FileType.PDF));
    Mockito.doReturn(Optional.of(template))
        .when(service)
        .getTemplateFromGroup(Mockito.eq(document),
            Mockito.eq(group), Mockito.eq(DocumentType.INVOIC), Mockito.eq(FileType.PDF));
    service.getTemplateFromPartnerOrCompany(document, Locale.FRENCH, Mockito.mock(Instance.class),
        partner, DocumentType.INVOIC,
        FileType.PDF);
    Mockito.verify(service, Mockito.times(2))
        .getTemplateFromGroup(Mockito.eq(document), Mockito.any(),
            Mockito.eq(DocumentType.INVOIC), Mockito.eq(FileType.PDF));
  }

  @Test
  public void generateFileFromBirtIfTemplatePresent_more_then_one_document_file() {
    Document document = generateMockDocument_invoice_with_XML_FIC_ERR();
    Template t = generateMockTemplate_global_();
    Indexable xcblDocument = document.getIndexValue();
    File fileMock = mock(File.class);
    doReturn(true).when(fileMock)
        .isFile();
    doReturn(fileMock).when(service)
        .getXcblFile(document, false, DocumentType.INVOIC, xcblDocument);
    // i didn't create a new file mock;
    doReturn(fileMock).when(transformService)
        .executeBirt(eq(document), any(), any(), any(), any(),
            any(), any(), anyBoolean(), any());
    @SuppressWarnings("unchecked")
    ArrayList<DocumentFile> dofs = mock(ArrayList.class);
    doReturn(2).when(dofs)
        .size();
    mockedDocumentHelper.when(
            () -> DocumentHelper.searchFiles(eq(singletonList(document)), any(Query.class)))
        .thenReturn(dofs);
    service.generateFileFromBirtIfTemplatePresent(document, Locale.FRANCE, mock(Instance.class),
        DocumentType.INVOIC,
        FileType.PDF,
        false, Optional.ofNullable(t), xcblDocument);
    verify(service, times(1)).getXcblFile(document, false, DocumentType.INVOIC, xcblDocument);
    verify(transformService, times(1)).executeBirt(eq(document), any(), any(), any(), any(),
        any(), any(), anyBoolean(), any());
  }

  @Test
  public void getFileDocumentWith_XCBL_XML_ATTACHED_FILE_ERR_FIC_and_WithTemplate_With_NO_PDF_GENERATEDTest()
      throws IOException {
    Document document = generateMockDocument_invoice_with_XML_FIC_ERR();
    Template t = generateMockTemplate_global_();
    Indexable xcblDocument = document.getIndexValue();
    doReturn(Optional.ofNullable(t))
        .when(service)
        .getTemplateFromPartnerOrCompany(eq(document), any(), any(),
            any(), any(), any());
    doReturn(File.createTempFile("invoice_test_pdf_generated_file.pdf", null, folder))
        .when(service)
        .generateFileFromBirtIfTemplatePresent(eq(document), any(), any(), any(), any(),
            anyBoolean(), any(), any());
    // case when we don't have a PDF generated
    service.getFile(document, Locale.FRANCE, null, null, FileType.PDF, false, xcblDocument);
    // only once the .addFile() method is called (when the PDF is generated )
    verify(document, times(1)).addFile(any());
  }

  @Test
  public void getFileDocumentWith_XCBL_XML_ATTACHED_FILE_ERR_FIC_and_WithTemplate_With_PDF_GENERATEDTest()
      throws IOException {
    Document document = generateMockDocument_invoice_with_XML_FIC_ERR();
    Indexable xcblDocument = document.getIndexValue();
    doReturn(null).when(document)
        .getFiles();
    Template t = generateMockTemplate_global_();
    doReturn(Optional.ofNullable(t))
        .when(service)
        .getTemplateFromPartnerOrCompany(eq(document), any(), any(),
            any(), any(), any());
    doReturn(File.createTempFile("invoice_test_pdf_generated_file.pdf", null, folder))
        .when(service)
        .generateFileFromBirtIfTemplatePresent(eq(document), any(), any(), any(), any(),
            anyBoolean(), any(), any());
    // case when we do have a PDF generated
    service.getFile(document, Locale.FRANCE, null, null, FileType.PDF, false, xcblDocument);
    // the addFile() is not called
    verify(document, times(0)).addFile(any());
  }

  @Test
  public void testBuildQueryWhenPartnerIsNull() {
    try {
      Invoice invoice = Mockito.mock(Invoice.class);
      InvoiceHeaderType header = Mockito.mock(InvoiceHeaderType.class);
      InvoiceTypeType type = Mockito.mock(InvoiceTypeType.class);
      Mockito.doReturn(header)
          .when(invoice)
          .getInvoiceHeader();
      ComplexStringType invoiceNumber = Mockito.mock(ComplexStringType.class);
      Mockito.doReturn(invoiceNumber)
          .when(header)
          .getInvoiceNumber();
      Mockito.doReturn("INV-XXX")
          .when(invoiceNumber)
          .getValue();
      Mockito.doReturn(type)
          .when(header)
          .getInvoiceType();
      Mockito.doReturn(InvoiceTypeCodeType.COMMERCIAL_INVOICE)
          .when(type)
          .getInvoiceTypeCoded();
      Company cpy = Mockito.mock(Company.class);
      Mockito.doReturn("CPY")
          .when(cpy)
          .getCode();
      service.buildQuery(cpy, null, invoice, new Date(), new Date());
    } catch (Exception e) {
      log.error("Failed to build query", e);
      Assertions.fail("Should not throw Null Pointer for partner");
    }

  }

  private Template generateMockTemplate_global_() {
    Template t = mock(Template.class);
    Map<Locale, File> bundles = new LinkedHashMap<Locale, File>();
    bundles.put(Locale.FRANCE, mock(File.class));
    when(t.getFile())
        .thenReturn(mock(File.class));
    when(t.getBundles())
        .thenReturn(bundles);
    when(t.getName())
        .thenReturn("invoice");
    when(t.getOwner())
        .thenReturn("_global_");
    when(t.getType())
        .thenReturn(Transform.BIRT);
    when(t.getUri())
        .thenReturn("/_global_/BIRT/invoice/invoice");
    return t;
  }

  private Document generateMockDocument_invoice_with_XML_FIC_ERR() {
    Document d = mock(Document.class);
    when(d.getReference())
        .thenReturn("invoice_test_Edi_file.fic");
    when(d.getOwners())
        .thenReturn("owners");
    when(d.getType())
        .thenReturn(DocumentType.INVOIC.name());
    when(d.getFrom())
        .thenReturn("from");
    when(d.getTo())
        .thenReturn("to");
    when(d.getStatus())
        .thenReturn(new DocumentStatusEntity(DocumentStatus.NONE));
    when(d.getFiles())
        .thenReturn(generateMockDocumentFiles(d));
    Assertions.assertEquals(4, d.getFiles()
        .size());
    return d;
  }

  private List<DocumentFile> generateMockDocumentFiles(Document d) {
    List<DocumentFile> dofs = new ArrayList<DocumentFile>();
    dofs.add(new DocumentFile(mock(File.class), FileType.XCBL,
        "ConverterInvoiceXcbl40Task", null, d, null));
    dofs.add(new DocumentFile(mock(File.class), FileType.EDIUNIT,
        "EdifactSplitterTask", null, d, null));
    dofs.add(new DocumentFile(mock(File.class), FileType.ERROR,
        "", null, d, null));
    dofs.add(new DocumentFile(mock(File.class), FileType.CSV,
        ResultingFileAction.ADD_FILE_TO_INVOICE.getName(), null, d, null));
    return dofs;
  }

  @Test
  public void testFillAsnRequiredFields() throws Exception {
    AsnMock asnMock = new AsnMock();
    AdvanceShipmentNotice asn = asnMock.createAsn();

    List<TransportRoutingType> transportRouting = asn.getASNHeader()
        .getListOfTransportRouting()
        .getTransportRouting();
    ASNBaseItemDetailType baseItemDetail = asn.getASNDetail()
        .getListOfASNItemDetail()
        .getASNItemDetail()
        .get(0)
        .getASNBaseItemDetail();
    PackageType pck = asn.getASNDetail()
        .getListOfASNPackageDetail()
        .getASNPackageDetail()
        .get(0)
        .getListOfPackage()
        .getPackage()
        .get(0);
    PackageItemReferenceType packageItemRef = pck.getListOfItemReference()
        .getPackageItemReference()
        .get(0);
    List<PackageType> subPckList = pck.getPackageDetail()
        .get(0)
        .getListOfPackage()
        .getPackage();

    assertNull(transportRouting.get(0)
        .getTransportMode()
        .getTransportModeCoded());
    assertEquals(transportRouting.get(1)
        .getTransportMode()
        .getTransportModeCoded(), TransportModeCodeType.OTHER);
    assertEquals(baseItemDetail.getLineItemNum()
        .getBuyerLineItemNum(), 42);

    assertNotNull(pck.getListOfDimension());
    assertEquals(packageItemRef.getLineItemReference(), "42");
    assertEquals(subPckList.get(0)
        .getListOfDimension()
        .getDimension()
        .get(0)
        .getDimensionCoded(), DimensionCodeType.OTHER);
    assertNull(subPckList.get(1)
        .getListOfDimension());

    asnMock.setAsnFillRequiredFields(asn);

    assertNotNull(asn);
    assertEquals(transportRouting.get(0)
        .getTransportMode()
        .getTransportModeCoded(), TransportModeCodeType.OTHER);
    assertEquals(transportRouting.get(1)
        .getTransportMode()
        .getTransportModeCoded(), TransportModeCodeType.OTHER);
    assertEquals(baseItemDetail.getLineItemNum()
        .getBuyerLineItemNum(), 42);

    assertNull(pck.getListOfDimension());
    assertEquals(packageItemRef.getLineItemReference(), "42");
    assertEquals(subPckList.get(0)
        .getListOfDimension()
        .getDimension()
        .get(0)
        .getDimensionCoded(), DimensionCodeType.OTHER);
    assertNull(subPckList.get(1)
        .getListOfDimension());
  }

  @Test
  public void getFileModifiedDoc() throws Exception {
    Document document = generateMockDocument_invoice_with_XML_FIC_ERR();
    Indexable xcblDocument = document.getIndexValue();
    Template t = generateMockTemplate_global_();
    doReturn(Optional.ofNullable(t)).when(service)
        .getTemplateFromPartnerOrCompany(eq(document), any(), any(), any(), any(), any());
    doReturn(File.createTempFile("invoice_test_pdf_generated_file.pdf", null, folder)).when(service)
        .generateFileFromBirtIfTemplatePresent(eq(document), any(), any(), any(), any(),
            anyBoolean(), any(), any());

    service.getFile(document, Locale.FRANCE, null, null, FileType.PDF, false, xcblDocument);

    verify(document, times(1)).addFile(any());
    verify(documentService, times(1)).saveDocument(any());
  }

  @Test
  public void getFileNotModifiedDoc() throws Exception {
    Document document = mock(Document.class);
    Indexable xcblDocument = document.getIndexValue();
    List<DocumentFile> dofs = new ArrayList<DocumentFile>();
    File file = mock(File.class);
    dofs.add(new DocumentFile(file, FileType.XCBL, "action", null, document, null));

    when(document.getFiles()).thenReturn(dofs);
    when(document.getFiles()
        .get(0)
        .getUri()).thenReturn("uri");
    when(file.toURI()).thenReturn(new URI("uri"));

    Template t = generateMockTemplate_global_();
    doReturn(Optional.ofNullable(t)).when(service)
        .getTemplateFromPartnerOrCompany(eq(document), any(), any(), any(), any(), any());
    doReturn(file).when(service)
        .generateFileFromBirtIfTemplatePresent(eq(document), any(), any(), any(), any(),
            anyBoolean(), any(), any());

    service.getFile(document, Locale.FRANCE, null, null, FileType.PDF, false, xcblDocument);

    verify(document, times(0)).addFile(any());
    verify(documentService, times(0)).saveDocument(any());
  }

  @Test
  public void testFieldSuggestions() {
    List<Object> objects = Arrays.asList(BasicDBObjectBuilder.start("_id", "xxxx-1")
            .get(),
        BasicDBObjectBuilder.start("_id", "xxxx-2")
            .get());
    Mockito.when(service.canAutoComplete(Mockito.any(), Mockito.any()))
        .thenReturn(true);
    Mockito.doReturn(objects).when(service)
        .aggregate(Mockito.any(), Mockito.any(),
            Mockito.any(), Mockito.any(), Mockito.any());
    BeanDescriptor bd = new BeanDescriptor();
    Query q = new Query();
    service.getFieldSuggestions("fieldX", "", InvoiceIndex.class, bd, q);
    verify(service).aggregate(Mockito.any(), Mockito.any(),
        Mockito.any(Query.class), Mockito.any(GroupOperation.class),
        Mockito.any(LimitOperation.class));
  }
}
