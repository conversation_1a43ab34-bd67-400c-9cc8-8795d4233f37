package com.byzaneo.generix.edocument.ui.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.ui.SessionHandler;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import javax.faces.component.UIComponent;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR> <<EMAIL>>
 */
public class BigDecimalConverterTest {

  private MockedStatic<JSFHelper> mockedJSFHelper;

  UIComponent uIComponent = Mockito.mock(UIComponent.class);

  Map<String, Object> attributes = new HashMap<>();

  private BigDecimalConverter converter;

  @BeforeEach
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
    mockedJSFHelper = Mockito.mockStatic(JSFHelper.class);
    SessionHandler session = mock(SessionHandler.class);
    mockedJSFHelper.when(() -> JSFHelper.getManagedBean(eq(SessionHandler.class),
        eq(SessionHandler.MANAGED_BEAN_NAME))).thenReturn(session);
    when(session.getLocale()).thenReturn(new Locale("fr"));

    Mockito.when(uIComponent.getAttributes())
        .thenReturn(attributes);

    converter = new BigDecimalConverter();
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedJSFHelper.closeOnDemand();
  }

  @Test
  public void testGetAsString_withScale() {
    attributes.put("scale", 2);

    assertEquals("", converter.getAsString(null, uIComponent, ""));
    assertEquals("aaa", converter.getAsString(null, uIComponent, "aaa"));
    assertEquals("1 000", converter.getAsString(null, uIComponent, "1 000"));
    assertEquals("1,00", converter.getAsString(null, uIComponent, "0001"));
    assertEquals("1,00", converter.getAsString(null, uIComponent, "1"));
    assertEquals("0,50", converter.getAsString(null, uIComponent, "0.5"));
    assertEquals("55,12", converter.getAsString(null, uIComponent, "55.12345"));
    assertEquals("-5,00", converter.getAsString(null, uIComponent, "-5"));
    assertEquals("-5,22", converter.getAsString(null, uIComponent, "-5.22"));
    assertEquals("0,10", converter.getAsString(null, uIComponent, ".1"));
    assertEquals("11,00", converter.getAsString(null, uIComponent, "11."));

    assertEquals("5,00", converter.getAsString(null, uIComponent, 5));
    assertEquals("-5,00", converter.getAsString(null, uIComponent, -5));
    assertEquals("55,12", converter.getAsString(null, uIComponent, 55.12345));
    assertEquals("-55,12", converter.getAsString(null, uIComponent, -55.12345));
    assertEquals("0,50", converter.getAsString(null, uIComponent, 0.5));
    assertEquals("0,56", converter.getAsString(null, uIComponent, 0.557));
    assertEquals("0,10", converter.getAsString(null, uIComponent, .1));
    assertEquals("11,00", converter.getAsString(null, uIComponent, 11.));
  }

  @Test
  public void testGetAsString_noScale() {
    attributes.put("scale", 0);

    assertEquals("", converter.getAsString(null, uIComponent, ""));
    assertEquals("aaa", converter.getAsString(null, uIComponent, "aaa"));
    assertEquals("1 000", converter.getAsString(null, uIComponent, "1 000"));
    assertEquals("1", converter.getAsString(null, uIComponent, "0001"));
    assertEquals("1", converter.getAsString(null, uIComponent, "1"));
    assertEquals("0", converter.getAsString(null, uIComponent, "0.5"));
    assertEquals("55", converter.getAsString(null, uIComponent, "55.12345"));
    assertEquals("-5", converter.getAsString(null, uIComponent, "-5"));
    assertEquals("-5", converter.getAsString(null, uIComponent, "-5.22"));
    assertEquals("0", converter.getAsString(null, uIComponent, ".1"));
    assertEquals("11", converter.getAsString(null, uIComponent, "11."));

    assertEquals("0", converter.getAsString(null, uIComponent, 0.5));
    assertEquals("5", converter.getAsString(null, uIComponent, 5));
    assertEquals("-5", converter.getAsString(null, uIComponent, -5));
    assertEquals("55", converter.getAsString(null, uIComponent, 55.12345));
    assertEquals("-55", converter.getAsString(null, uIComponent, -55.12345));
    assertEquals("1", converter.getAsString(null, uIComponent, 0.557));
    assertEquals("0", converter.getAsString(null, uIComponent, .1));
    assertEquals("11", converter.getAsString(null, uIComponent, 11.));
  }

  @Test
  public void testGetAsObject_withScale() {
    attributes.put("scale", 2);

    assertEquals(null, converter.getAsObject(null, uIComponent, null));
    assertEquals(null, converter.getAsObject(null, uIComponent, "aaa"));
    assertEquals(null, converter.getAsObject(null, uIComponent, ""));
    assertEquals(BigDecimal.valueOf(1)
        .setScale(2) /* 1.00 */, converter.getAsObject(null, uIComponent, "0001"));
    assertEquals(BigDecimal.valueOf(1)
        .setScale(2) /* 1.00 */, converter.getAsObject(null, uIComponent, "1"));
    assertEquals(BigDecimal.valueOf(0.5)
        .setScale(2) /* 0.50 */, converter.getAsObject(null, uIComponent, "0.5"));
    assertEquals(BigDecimal.valueOf(55.12) /* 55.12 */,
        converter.getAsObject(null, uIComponent, "55.12345"));
    assertEquals(BigDecimal.valueOf(-5)
        .setScale(2) /*-5.00*/, converter.getAsObject(null, uIComponent, "-5"));
    assertEquals(BigDecimal.valueOf(-5.22) /*-5.22*/,
        converter.getAsObject(null, uIComponent, "-5.22"));
    assertEquals(BigDecimal.valueOf(0.1)
        .setScale(2) /* 0.10 */, converter.getAsObject(null, uIComponent, ".1"));
    assertEquals(BigDecimal.valueOf(11)
        .setScale(2) /* 11.00 */, converter.getAsObject(null, uIComponent, "11."));
  }

  @Test
  public void testGetAsObject_noScale() {
    attributes.put("scale", 0);

    assertEquals(null, converter.getAsObject(null, uIComponent, null));
    assertEquals(null, converter.getAsObject(null, uIComponent, "aaa"));
    assertEquals(null, converter.getAsObject(null, uIComponent, ""));
    assertEquals(BigDecimal.valueOf(1) /* 1 */, converter.getAsObject(null, uIComponent, "0001"));
    assertEquals(BigDecimal.valueOf(1) /* 1 */, converter.getAsObject(null, uIComponent, "1"));
    assertEquals(BigDecimal.valueOf(0) /* 0 */, converter.getAsObject(null, uIComponent, "0.5"));
    assertEquals(BigDecimal.valueOf(55) /* 55 */,
        converter.getAsObject(null, uIComponent, "55.12345"));
    assertEquals(BigDecimal.valueOf(-5) /*-5*/, converter.getAsObject(null, uIComponent, "-5"));
    assertEquals(BigDecimal.valueOf(-5) /*-5*/, converter.getAsObject(null, uIComponent, "-5.22"));
    assertEquals(BigDecimal.valueOf(0) /* 0 */, converter.getAsObject(null, uIComponent, ".1"));
    assertEquals(BigDecimal.valueOf(11) /* 11 */, converter.getAsObject(null, uIComponent, "11."));
  }
}
