package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class SumConsistencyConstraintTest {
  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/sumConsistencyConstraintTestFile.xml"),
      false);

  @Test
  public void testValidExact() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValidSmaller() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result2");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValiBigger() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result3");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }
  @Test
  public void testValidBigger() {
    String argumentPath1 = "//elements/element";
    String argumentPath2 = "//elements/element2";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result7");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath1,argumentPath2));
    instance.getCache()
        .put(argumentPath1, evaluateNodeList(XCBL_ROOT, argumentPath1).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    instance.getCache()
        .put(argumentPath2, evaluateNodeList(XCBL_ROOT, argumentPath2).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }
  @Test
  public void testValidSmaller2Elements() {
    String argumentPath1 = "//elements/element";
    String argumentPath2 = "//elements/element2";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result8");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath1,argumentPath2));
    instance.getCache()
        .put(argumentPath1, evaluateNodeList(XCBL_ROOT, argumentPath1).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    instance.getCache()
        .put(argumentPath2, evaluateNodeList(XCBL_ROOT, argumentPath2).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValidMoreDecimals() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result4");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid5() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result5");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid6() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result6");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValueToBig() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/resultIncorect1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalid() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/resultIncorect2");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testValueToSmall() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/resultIncorect3");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotANumber() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/resultIncorect4");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testEmptyField() {
    String argumentPath = "//elements/element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/resultIncorect5");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testInvalidPath() {
    String argumentPath = "//elements/not-element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNoGlobalPath() {
    String argumentPath = "elements/not-element";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result1");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath));
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void tesEmptyList() {
    String argumentPath = "//empty-elements/empty";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/result1");
    instance.setNodes(nodes);
    instance.getCache()
        .put(argumentPath, evaluateNodeList(XCBL_ROOT, argumentPath).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    instance.setParams(Arrays.array(argumentPath));
    boolean result = instance.isValid();
    assertFalse(result);
  }
  @Test
  public void testValidTooSmall2Elements() {
    String argumentPath1 = "//elements/element";
    String argumentPath2 = "//elements/element2";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/resultIncorect6");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath1,argumentPath2));
    instance.getCache()
        .put(argumentPath1, evaluateNodeList(XCBL_ROOT, argumentPath1).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    instance.getCache()
        .put(argumentPath2, evaluateNodeList(XCBL_ROOT, argumentPath2).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }
  @Test
  public void testValidTooBig2Elements() {
    String argumentPath1 = "//elements/element";
    String argumentPath2 = "//elements/element2";
    SumConsistencyConstraint instance = new SumConsistencyConstraint();
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//results/resultIncorect7");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array(argumentPath1,argumentPath2));
    instance.getCache()
        .put(argumentPath1, evaluateNodeList(XCBL_ROOT, argumentPath1).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    instance.getCache()
        .put(argumentPath2, evaluateNodeList(XCBL_ROOT, argumentPath2).stream()
            .map(Node::getTextContent)
            .collect(Collectors.toList()));
    boolean result = instance.isValid();
    assertFalse(result);
  }

}
