package com.byzaneo.generix.edocument.validation.constraints;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.util.List;

import org.assertj.core.util.Arrays;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;

/**
 * <AUTHOR> <lv<PERSON><PERSON>.<EMAIL>>
 */
public class SizeConstraintTest {

  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/SizeConstraintTest.xml"), false);

  @Test
  public void testValidateSizeOk() {
    SizeConstraint instance = new SizeConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//products/product/category");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("6"));
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValidateSizeNotOk() {
    SizeConstraint instance = new SizeConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//products/product/name");
    instance.setNodes(nodes);
    instance.setParams(Arrays.array("6"));
    boolean result = instance.isValid();
    assertFalse(result);
  }

}
