package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.TaxSummaryXcblHelper.createTaxSummary;
import static com.byzaneo.generix.edocument.util.TaxSummaryXcblHelper.populateInvoiceTaxSummary;
import static com.byzaneo.generix.edocument.util.TaxSummaryXcblHelper.removeTaxSummary;
import static com.byzaneo.generix.edocument.util.TaxXcblHelper.TAX_PRECISION;
import static com.byzaneo.generix.edocument.util.TaxXcblHelper.createTaxtype;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getTaxableAmountFromInvoiceTaxSummaryType;
import static java.math.BigDecimal.ROUND_HALF_EVEN;
import static java.math.BigDecimal.valueOf;
import static java.util.Optional.of;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.util.Assert.isNull;
import static org.springframework.util.Assert.notNull;

import java.math.BigDecimal;
import java.util.*;

import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceSummaryType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> Lemesle <<EMAIL>>
 * @company Byzaneo
 * @date 05 April, 2018
 */
public class TaxSummaryXcblHelperTest {

  Invoice invoice;

  @BeforeEach
  public void setUp() throws Exception {
    invoice = new Invoice();
    invoice.setInvoiceSummary(new InvoiceSummaryType());
  }

  @Test
  public void createTaxSummaryTest() {
    createTaxSummary(this.invoice);
    notNull(this.invoice.getInvoiceSummary()
        .getListOfTaxSummary());
  }

  @Test
  public void removeTaxSummaryTest() {
    this.invoice.getInvoiceSummary()
        .setListOfTaxSummary(new ListOfInvoiceTaxSummaryType());
    removeTaxSummary(this.invoice);
    isNull(this.invoice.getInvoiceSummary()
        .getListOfTaxSummary());
  }

  @Test
  public void populateInvoiceTaxSummaryTest() {
    Map<String, InvoiceTaxSummaryType> taxSummaries = new TreeMap<>();

    // Add two taxes with the same percent
    populateInvoiceTaxSummary(taxSummaries,
        createTaxtype(of(BigDecimal.TEN), of(valueOf(20)), TaxCategoryCodeType.OTHER),
        TAX_PRECISION);
    populateInvoiceTaxSummary(taxSummaries,
        createTaxtype(of(valueOf(20)), of(valueOf(20)), TaxCategoryCodeType.OTHER),
        TAX_PRECISION);
    assertEquals(1, taxSummaries.size());
    assertEquals(valueOf(30).setScale(TAX_PRECISION, ROUND_HALF_EVEN), getTaxableAmountFromInvoiceTaxSummaryType(taxSummaries.values()
        .iterator()
        .next()));
    assertEquals(valueOf(6).setScale(TAX_PRECISION, ROUND_HALF_EVEN), taxSummaries.values()
        .iterator()
        .next()
        .getTaxAmount());

    // Add one more tax compute negatively
    populateInvoiceTaxSummary(taxSummaries,
        createTaxtype(of(valueOf(20)), of(valueOf(20)), TaxCategoryCodeType.OTHER),
        TAX_PRECISION,
        IndicatorCodeType.ALLOWANCE);
    assertEquals(1, taxSummaries.size());
    assertEquals(valueOf(10).setScale(TAX_PRECISION, ROUND_HALF_EVEN), getTaxableAmountFromInvoiceTaxSummaryType(taxSummaries.values()
        .iterator()
        .next()));
    assertEquals(valueOf(2).setScale(TAX_PRECISION, ROUND_HALF_EVEN), taxSummaries.values()
        .iterator()
        .next()
        .getTaxAmount());

    // Add one more tax with a different percent
    populateInvoiceTaxSummary(taxSummaries,
        createTaxtype(of(valueOf(20)), of(valueOf(10)), TaxCategoryCodeType.OTHER),
        TAX_PRECISION);
    assertEquals(2, taxSummaries.size());
  }
}
