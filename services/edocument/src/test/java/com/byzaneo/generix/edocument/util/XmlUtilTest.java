package com.byzaneo.generix.edocument.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.dom4j.QName;
import org.dom4j.dom.*;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.w3c.dom.*;

public class XmlUtilTest {

  @Test
  public void testAsList() {
    NodeList nodeList = Mockito.mock(NodeList.class);
    Mockito.when(nodeList.getLength())
        .thenReturn(0)
        .thenReturn(1);

    assertEquals(0, XmlUtil.asList(null)
        .size());
    assertEquals(0, XmlUtil.asList(nodeList)
        .size());
    assertEquals(1, XmlUtil.asList(nodeList)
        .size());
  }

  @Test
  public void testRegisterElementById() {
    Attr attr = new DOMAttribute(new QName("Id"));
    Element element = Mockito.mock(Element.class);
    DOMAttributeNodeMap nodeMap = Mockito.mock(DOMAttributeNodeMap.class);

    Mockito.when(element.getAttributes())
        .thenReturn(nodeMap);
    Mockito.when(nodeMap.getNamedItem("Id"))
        .thenReturn(attr);
    XmlUtil.registerElementById(element);

    Mockito.verify(element, Mockito.times(1))
        .setIdAttributeNode(attr, true);
  }

  @Test
  public void testGetFilsSignedProperties() {
    Element element = Mockito.mock(Element.class);
    NodeList nodeList = Mockito.mock(NodeList.class);
    Node node = Mockito.mock(Element.class);

    Mockito.when(element.getChildNodes())
        .thenReturn(nodeList);
    Mockito.when(nodeList.getLength())
        .thenReturn(1)
        .thenReturn(0);
    Mockito.when(nodeList.item(0))
        .thenReturn(node);
    Mockito.when(node.getNodeName())
        .thenReturn("QualifyingProperties")
        .thenReturn("SignedProperties");
    Mockito.when(node.getChildNodes())
        .thenReturn(nodeList);

    assertEquals(node.hashCode(), XmlUtil.getFilsSignedProperties(element)
        .hashCode());
    assertEquals(null, XmlUtil.getFilsSignedProperties(element));
  }
}