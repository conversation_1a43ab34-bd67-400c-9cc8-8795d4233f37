package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.BILL_TO;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.BUYER;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.REMIT_TO;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.SELLER;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.SHIP_FROM;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.SHIP_TO;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getCityValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getComplexStringValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getName1Value;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.getStreetValue;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static org.junit.jupiter.api.Assertions.*;

import java.util.*;

import com.byzaneo.generix.service.repository.bean.Factor;
import com.byzaneo.generix.service.repository.bean.address.*;
import com.byzaneo.security.bean.*;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.util.InvoiceHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;
import org.junit.jupiter.api.Test;

public class PartyCodeXcblHelperTest {

  @Test
  public void testCreateEmptyParty() {
    InvoicePartyCodedType partyCodedType = PartyCodeXcblHelper.createEmptyParty("Buyer");
    assertNotNull(partyCodedType);
    assertEquals("Buyer", partyCodedType.getPartyRoleCoded());
  }

  @Test
  public void testGetAddressFromFactor() {
    Factor factor = new Factor();
    factor.setName("Factor");
    factor.setAddress("Avenue Daumesnil");
    factor.setPostalCode("75012");
    factor.setCity("Paris");
    InvoicePartyCodedType party = new InvoicePartyCodedType();

    InvoiceNameAddressType nameAddress = PartyCodeXcblHelper.getAddressFromFactor(factor, party)
        .getNameAddress();
    assertEquals(null, PartyCodeXcblHelper.getAddressFromFactor(factor, null));
    assertNotNull(nameAddress);
    assertEquals("Factor", getName1Value(nameAddress));
    assertEquals("Avenue Daumesnil", getStreetValue(nameAddress));
    assertEquals("Paris", getCityValue(nameAddress));
    assertEquals(CountryCodeType.FR, nameAddress.getCountry()
        .getCountryCoded());
  }

  @Test
  public void testConvert() {
    assertNull(PartyCodeXcblHelper.convert(null, null));
    Address address = new Address();
    address.setIdentification("ID1");
    address.setVatCode("20");
    address.setName("Location1");
    address.setCity("Paris");
    address.setStreet("Avenue Dausmenil");
    address.setPostalCode("75012");
    address.setTypeAddress(TypeAddressEnum.SHIP_TO);
    AddressContact contact = new AddressContact();
    contact.setName("Support");
    contact.setEmail("<EMAIL>");
    contact.setTel("0123456789");
    address.setContacts(Arrays.asList(contact));

    InvoicePartyCodedType pct = PartyCodeXcblHelper.convert(address, null);

    assertNotNull(pct);
    assertNotNull(pct.getPartyID());
    assertTrue(pct.getNameAddress() != null && getName1Value(pct.getNameAddress())
        .equalsIgnoreCase("Location1") && InvoiceHelper.getCityValue(pct.getNameAddress())
            .equalsIgnoreCase("Paris"));
    assertEquals("ShipTo", pct.getPartyRoleCoded());
    assertEquals("Support", getComplexStringValue(pct.getPrimaryContact()
        .getContactName()));
    assertEquals(2, pct.getPrimaryContact()
        .getListOfContactNumber()
        .getContactNumber()
        .size());
    assertEquals(2, pct.getListOfIdentifier()
        .getIdentifier()
        .size());
  }

  @Test
  public void testPopulateInvoiceParty() {
    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    invoice.getInvoiceHeader()
        .setInvoiceParty(new InvoicePartyType());

    Partner partner = new Partner();
    partner.setLocation(new Location("DEFAULT", new com.byzaneo.location.bean.Address(), true));
    partner.getLocation()
        .getAddress()
        .setCountry(Locale.FRANCE);
    ArrayList<InvoicePartyCodedType> types = new ArrayList<>();
    InvoicePartyCodedType type1 = new InvoicePartyCodedType();
    type1.setPartyRoleCoded(BILL_TO);
    InvoicePartyCodedType type2 = new InvoicePartyCodedType();
    type2.setPartyRoleCoded(BUYER);
    InvoicePartyCodedType type3 = new InvoicePartyCodedType();
    type3.setPartyRoleCoded(SELLER);
    InvoicePartyCodedType type4 = new InvoicePartyCodedType();
    type4.setPartyRoleCoded(SHIP_TO);
    InvoicePartyCodedType type5 = new InvoicePartyCodedType();
    type5.setPartyRoleCoded(SHIP_FROM);
    InvoicePartyCodedType type6 = new InvoicePartyCodedType();
    type6.setPartyRoleCoded(REMIT_TO);
    InvoicePartyCodedType type7 = new InvoicePartyCodedType();
    type7.setPartyRoleCoded("Other");
    types.add(type1);
    types.add(type2);
    types.add(type3);
    types.add(type4);
    types.add(type5);
    types.add(type6);
    types.add(type7);

    PartyCodeXcblHelper.populateInvoiceParty(invoice, types, partner);
    InvoicePartyType invoiceParty = invoice.getInvoiceHeader()
        .getInvoiceParty();
    assertEquals(BILL_TO, ((InvoicePartyCodedType) invoiceParty.getBillToParty()).getPartyRoleCoded());
    assertEquals(BUYER, ((InvoicePartyCodedType) invoiceParty.getBuyerParty()).getPartyRoleCoded());
    assertEquals(SELLER, ((InvoicePartyCodedType) invoiceParty.getSellerParty()).getPartyRoleCoded());
    assertEquals(SHIP_TO, ((InvoicePartyCodedType) invoiceParty.getShipToParty()).getPartyRoleCoded());
    assertEquals(SHIP_FROM, ((InvoicePartyCodedType) invoiceParty.getShipFromParty()).getPartyRoleCoded());
    assertEquals(REMIT_TO, ((InvoicePartyCodedType) invoiceParty.getRemitToParty()).getPartyRoleCoded());
  }

  @Test
  public void testCopyPartyProperties() {
    InvoiceIdentifierType identifierType = new InvoiceIdentifierType();
    identifierType.setIdent(toComplexStringType(identifierType.getIdent(), "ID1"));
    InvoiceNameAddressType addressType = new InvoiceNameAddressType();
    addressType.setName1(toComplexStringType(addressType.getName1(), "Address1"));
    ListOfInvoiceIdentifierType listOfIdentifierType = new ListOfInvoiceIdentifierType();
    InvoicePartyTaxInformationType partyTaxInformationType = new InvoicePartyTaxInformationType();

    InvoicePartyCodedType from = new InvoicePartyCodedType();
    InvoicePartyCodedType to = new InvoicePartyCodedType();
    from.setPartyID(identifierType);
    from.setNameAddress(addressType);
    from.setListOfIdentifier(listOfIdentifierType);
    from.setPartyTaxInformation(partyTaxInformationType);
    to.setPartyID(new InvoiceIdentifierType());

    PartyCodeXcblHelper.copyPartyProperties(from, to);

    assertEquals("ID1", InvoiceHelper.getIdentValue(to.getPartyID()));
    assertEquals("Address1", InvoiceHelper.getName1Value(to.getNameAddress()));
    assertEquals(listOfIdentifierType.getIdentifier()
        .size(), to.getListOfIdentifier()
            .getIdentifier()
            .size());
    assertTrue(to.getPartyTaxInformation()
        .equals(partyTaxInformationType));
  }

}