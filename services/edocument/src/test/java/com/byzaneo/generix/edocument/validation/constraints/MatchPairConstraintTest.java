package com.byzaneo.generix.edocument.validation.constraints;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;

public class MatchPairConstraintTest {
  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/MatchPairConstraintTestFile.xml"), false);

  @Test
  public void testCodeIncorrect() {
    MatchPairConstraint instance = initializeInstance("root/codeIncorrect/description", new String[] { "code", "UNTDID7161" });
    assertFalse(instance.isValid());
  }

  @Test
  public void testNotAPair() {
    MatchPairConstraint instance = initializeInstance("root/notPair/description", new String[] { "code", "UNTDID7161" });
    assertFalse(instance.isValid());
  }

  @Test
  public void testPairFromPath() {
    MatchPairConstraint instance = initializeInstance("root/correct/description", new String[] { "code", "UNTDID7161" });
    assertTrue(instance.isValid());
  }

  @Test
  public void testPairFromCache() {
    MatchPairConstraint instance = initializeInstance("root/correctFromCache/description",
        new String[] { "//root/correctFromCache/code", "UNTDID7161" });
    instance.getCache()
        .put("//correctFromCache/code",
            java.util.Arrays.asList("ABA"));
    assertTrue(instance.isValid());
  }

  @Test
  public void testWrongFilePath() {
    MatchPairConstraint instance = initializeInstance("root/correct/description", new String[] { "code", "No_File" });
    assertFalse(instance.isValid());
  }

  private MatchPairConstraint initializeInstance(String xpath, String[] params) {
    MatchPairConstraint instance = new MatchPairConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, xpath);
    instance.setNodes(nodes);
    instance.setParams(params);
    return instance;
  }
}
