package com.byzaneo.generix.edocument.bean.invoice;

import static com.byzaneo.generix.edocument.util.CsvImportHelper.VAT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_GROSS;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.PriceTypeCodeType.CALCULATION_NET;
import static java.util.Arrays.asList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;

import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import com.byzaneo.commons.io.BeanImportContext;
import com.byzaneo.commons.service.PropertiesException;
import com.byzaneo.commons.ui.MissingKeyHandler;
import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.edocument.api.invoice.InvoiceLineWrapper;
import com.byzaneo.generix.edocument.bean.xcbl.CsvInvoiceLine;
import com.byzaneo.generix.edocument.util.CsvImportHelper;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.*;

@ExtendWith(MockitoExtension.class)
public class InvoiceLineWrapperITCase {

  private MockedStatic<JSFHelper> mockedJSFHelper;

  private InvoiceLineWrapper instance;

  @BeforeEach
  public void before() {
    instance = new InvoiceLineWrapper();
    Mockito.spy(CsvImportHelper.class);
    setUpStaticMocks();
  }

  void setUpStaticMocks() {
    mockedJSFHelper = mockStatic(JSFHelper.class, invocation -> {
      if (invocation.getMethod()
          .getName()
          .equals("getMissingKeyHandler")) {
        return Mockito.mock(MissingKeyHandler.class);
      }
      return invocation.callRealMethod();
    });
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedJSFHelper.closeOnDemand();
  }

  @Test
  public void testValidateNoHeader() {
    CsvInvoiceLine line = new CsvInvoiceLine();

    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable())
          .thenReturn(null);

      try {
        instance.validate(line, new BeanImportContext());
        fail("expected validation exception");
      } catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("header");
        Assertions.assertEquals("Aucune entête liée à cet élément", serviceException.getMessage());
      }
    }
  }

  @Test
  public void testValidateMissingQuantity() {
    CsvInvoiceLine line = new CsvInvoiceLine();
    line.setLineNumber(5);

    Invoice invoice = new Invoice();
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class)) {
      mockCsvImportHelper.when((MockedStatic.Verification) CsvImportHelper.getImportedIndexable())
          .thenReturn(invoice);

      try {
        instance.validate(line, new BeanImportContext());
        fail("expected validation exception");
      } catch (PropertiesException e) {
        Exception serviceException = e.getExceptions()
            .get("line");
        assertTrue(asList(
            "Au moins un champ quantité doit être renseigné pour la ligne 5",
            "At least one quantity field should be filled for line 5")
            .contains(serviceException.getMessage()));
      }
    }
  }

  @Test
  public void testValidate() {
    CsvInvoiceLine line = new CsvInvoiceLine();
    line.setLineNumber(1);
    line.setBuyerCode("Code produit acheteur");
    line.setQuantity(125);
    line.setProductLabel("Libellé produit");
    line.setSellerCode("Code produit vendeur");
    line.setTaxPercentage(new BigDecimal("5.6"));
    line.setUnit("KGM");
    line.setUnitPriceNet(new BigDecimal("10"));
    line.setAmount(new BigDecimal("1250"));
    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
        Mockito.CALLS_REAL_METHODS)) {
      mockCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
          .thenReturn(invoice);

      try {
        instance.validate(line, new BeanImportContext());

        // Check
        InvoiceItemDetailType detail = invoice.getInvoiceDetail()
            .getListOfInvoiceItemDetail()
            .getInvoiceItemDetail()
            .get(0);
        assertEquals(1, detail.getInvoiceBaseItemDetail()
            .getLineItemNum()
            .getBuyerLineItemNum());
        assertEquals("Code produit acheteur", detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .getBuyerPartNumber()
            .getPartID()
            .getValue());
        assertEquals("Code produit vendeur", detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .getSellerPartNumber()
            .getPartID()
            .getValue());
        assertNull(detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .getStandardPartNumber());
        assertEquals("Libellé produit", detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getItemDescription()
            .getValue());
        assertEquals(new BigDecimal("125"), detail.getInvoiceBaseItemDetail()
            .getInvoicedQuantity()
            .getQuantityValue()
            .getValue());
        assertEquals("KGM", detail.getInvoiceBaseItemDetail()
            .getInvoicedQuantity()
            .getUnitOfMeasurement()
            .getUOMCoded());
        assertNull(detail.getInvoiceBaseItemDetail()
            .getListOfQuantityCoded());

        InvoicePricingDetailType pricingDetail = detail.getInvoicePricingDetail();
        assertEquals(1, pricingDetail.getListOfPrice()
            .getPrice()
            .size());
        assertEquals(CALCULATION_NET, pricingDetail.getListOfPrice()
            .getPrice()
            .get(0)
            .getPricingType()
            .getPriceTypeCoded());
        assertEquals("KGM", pricingDetail.getListOfPrice()
            .getPrice()
            .get(0)
            .getUnitOfMeasurement()
            .getUOMCoded());
        assertEquals(new BigDecimal("10.00"), pricingDetail.getListOfPrice()
            .getPrice()
            .get(0)
            .getUnitPrice()
            .getUnitPriceValue()
            .getValue());

        assertEquals(new BigDecimal("1250.00"), pricingDetail.getLineItemSubTotal()
            .getMonetaryAmount()
            .getValue());

        assertEquals(VAT, pricingDetail.getTax()
            .get(0)
            .getTaxTypeCoded());
        assertEquals(TaxFunctionQualifierCodeType.TAX, pricingDetail.getTax()
            .get(0)
            .getTaxFunctionQualifierCoded());
        assertEquals(new BigDecimal("1250.00"), pricingDetail.getTax()
            .get(0)
            .getTaxableAmount());
        assertEquals(TaxCategoryCodeType.OTHER, pricingDetail.getTax()
            .get(0)
            .getTaxCategoryCoded());
        assertEquals("5.6", pricingDetail.getTax()
            .get(0)
          .getTaxCategoryCodedOther()
          .getValue());
        assertEquals(new BigDecimal("5.60"), pricingDetail.getTax()
            .get(0)
            .getTaxPercent()
            .getValue());

      } catch (PropertiesException e) {
        fail("no validation exception expected");
      } catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }

  @Test
  public void testValidateFull() {
    CsvInvoiceLine line = new CsvInvoiceLine();
    line.setLineNumber(5);
    line.setBuyerCode("Code produit acheteur");
    line.setQuantity(50);
    line.setFreeQuantity(25);
    line.setProductCode("Code produit");
    line.setProductLabel("Libellé produit complet");
    line.setSellerCode("Code produit vendeur");
    line.setTaxPercentage(new BigDecimal("20.6"));
    line.setUnit("PCE");
    line.setUnitPrice(new BigDecimal("12"));
    line.setUnitPriceNet(new BigDecimal("20"));
    line.setAmount(new BigDecimal("1025.5"));

    Invoice invoice = new Invoice();
    invoice.setInvoiceHeader(new InvoiceHeaderType());
    try (MockedStatic<CsvImportHelper> mockCsvImportHelper = mockStatic(CsvImportHelper.class,
        Mockito.CALLS_REAL_METHODS)) {
      mockCsvImportHelper.when(CsvImportHelper::getImportedIndexable)
          .thenReturn(invoice);

      try {
        instance.validate(line, new BeanImportContext());

        // Check
        InvoiceItemDetailType detail = invoice.getInvoiceDetail()
            .getListOfInvoiceItemDetail()
            .getInvoiceItemDetail()
            .get(0);
        assertEquals(5, detail.getInvoiceBaseItemDetail()
            .getLineItemNum()
            .getBuyerLineItemNum());
        assertEquals("Code produit acheteur", detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .getBuyerPartNumber()
            .getPartID()
            .getValue());
        assertEquals("Code produit vendeur", detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .getSellerPartNumber()
            .getPartID()
            .getValue());
        assertEquals("Code produit", detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .getStandardPartNumber()
            .getProductIdentifier()
            .getValue());
        assertEquals("EAN2-5-5-1", detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getPartNumbers()
            .getStandardPartNumber()
            .getProductIdentifierQualifierCoded());
        assertEquals("Libellé produit complet", detail.getInvoiceBaseItemDetail()
            .getItemIdentifiers()
            .getItemDescription()
            .getValue());
        assertEquals(new BigDecimal("50"), detail.getInvoiceBaseItemDetail()
            .getInvoicedQuantity()
            .getQuantityValue()
            .getValue());
        assertEquals("EA", detail.getInvoiceBaseItemDetail()
            .getInvoicedQuantity()
            .getUnitOfMeasurement()
            .getUOMCoded());
        assertEquals(new BigDecimal("25"), detail.getInvoiceBaseItemDetail()
            .getListOfQuantityCoded()
            .getQuantityCoded()
            .get(0)
            .getQuantityValue()
            .getValue());
        assertEquals("EA", detail.getInvoiceBaseItemDetail()
            .getListOfQuantityCoded()
            .getQuantityCoded()
            .get(0)
            .getUnitOfMeasurement()
            .getUOMCoded());

        InvoicePricingDetailType pricingDetail = detail.getInvoicePricingDetail();
        assertEquals(2, pricingDetail.getListOfPrice()
            .getPrice()
            .size());
        assertEquals(CALCULATION_NET, pricingDetail.getListOfPrice()
            .getPrice()
            .get(0)
            .getPricingType()
            .getPriceTypeCoded());
        assertEquals("EA", pricingDetail.getListOfPrice()
            .getPrice()
            .get(0)
            .getUnitOfMeasurement()
            .getUOMCoded());
        assertEquals(new BigDecimal("20.00"), pricingDetail.getListOfPrice()
            .getPrice()
            .get(0)
            .getUnitPrice()
            .getUnitPriceValue()
            .getValue());

        assertEquals(CALCULATION_GROSS, pricingDetail.getListOfPrice()
            .getPrice()
            .get(1)
            .getPricingType()
            .getPriceTypeCoded());
        assertEquals("EA", pricingDetail.getListOfPrice()
            .getPrice()
            .get(1)
            .getUnitOfMeasurement()
            .getUOMCoded());
        assertEquals(new BigDecimal("12.00"), pricingDetail.getListOfPrice()
            .getPrice()
            .get(1)
            .getUnitPrice()
            .getUnitPriceValue()
            .getValue());

        assertEquals(new BigDecimal("1025.50"), pricingDetail.getLineItemSubTotal()
            .getMonetaryAmount()
            .getValue());

        assertEquals(VAT, pricingDetail.getTax()
            .get(0)
            .getTaxTypeCoded());
        assertEquals(TaxFunctionQualifierCodeType.TAX, pricingDetail.getTax()
            .get(0)
            .getTaxFunctionQualifierCoded());
        assertEquals(new BigDecimal("1000.00"), pricingDetail.getTax()
            .get(0)
            .getTaxableAmount());
        assertEquals(TaxCategoryCodeType.OTHER, pricingDetail.getTax()
            .get(0)
            .getTaxCategoryCoded());
        assertEquals("20.6", pricingDetail.getTax()
            .get(0)
          .getTaxCategoryCodedOther()
          .getValue());
        assertEquals(new BigDecimal("20.60"), pricingDetail.getTax()
            .get(0)
            .getTaxPercent()
            .getValue());

      } catch (PropertiesException e) {
        fail("no validation exception expected");
      } catch (Exception e) {
        fail("exception " + e.getMessage());
      }
    }
  }

}
