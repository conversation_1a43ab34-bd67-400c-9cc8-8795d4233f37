package com.byzaneo.generix.edocument.util;

import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.VENDOR_FREIGHT;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.addAllOrChHeader;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.addAllowOrChargeLine;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.getAllowOrChargeFromSelectedDetail;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.getAmountVAT;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.removeAllOrChHeader;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.removeAllowOrChargeLine;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.setAllowOrChargeFromSelectedDetail;
import static com.byzaneo.generix.edocument.util.AllowOrChargeXcblHelper.updateIndicatorCodedOfAllowOrCharge;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.computeInvoiceAllowanceOrChargeOnSelectedDetail;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.computeTaxOnInvoiceAllowanceOrCharge;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.createInvoiceParafiscalTax;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.getAllowOrChargeValue;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.getFirstTax;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.initAllowanceCharge;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.setMonetaryValue;
import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.setPercentValue;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.buildInvoiceItemDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.getCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setQuantity;
import static com.byzaneo.generix.edocument.util.InvoiceTaxXcblHelper.createInvoiceTaxtype;
import static com.byzaneo.generix.edocument.util.TaxXcblHelper.TAX_PRECISION;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexBigDecimalType;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexStringType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.MONETARY_AMOUNT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.PERCENT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_ALLOWANCE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_CHARGE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxCategoryCodeType.OTHER;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxCategoryCodeType.STANDARD_RATE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.TaxFunctionQualifierCodeType.TAX;
import static java.math.BigDecimal.TEN;
import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static java.math.RoundingMode.HALF_EVEN;
import static java.util.Optional.of;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.byzaneo.xtrade.xcbl.util.InvoiceHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceAllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceMonetaryValueType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceTaxType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.InvoiceTypeOfAllowanceOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PercentType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PercentageAllowanceOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TotalAllowOrChargeType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceHeaderType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceItemDetailType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> Lemesle <<EMAIL>>
 * @company Byzaneo
 * @date 09 April, 2018
 */
public class InvoiceAllowOrChargeXcblHelperTest {
  @Test
  public void computeAllowanceOrChargeOnSelectedDetailTest() {
    InvoiceAllowOrChargeType allowOrCharge = InvoiceAllowOrChargeXcblHelper.buildInvoiceAllowOrCharge();
    BigDecimal netPriceComputed = valueOf(24).setScale(2, HALF_EVEN);
    // Allowance
    allowOrCharge.setIndicatorCoded(LINE_ITEM_ALLOWANCE);
    allowOrCharge.setBasisCoded(MONETARY_AMOUNT);
    InvoiceAllowOrChargeXcblHelper.setMonetaryValue(allowOrCharge, TEN);
    assertEquals(valueOf(14).setScale(2, HALF_EVEN),
        computeInvoiceAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed));
    allowOrCharge.setBasisCoded(PERCENT);
    InvoiceAllowOrChargeXcblHelper.setPercentValue(allowOrCharge, TEN);
    assertEquals(valueOf(21.6).setScale(2, HALF_EVEN),
        computeInvoiceAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed));
    // Charge
    allowOrCharge.setIndicatorCoded(LINE_ITEM_CHARGE);
    allowOrCharge.setBasisCoded(MONETARY_AMOUNT);
    InvoiceAllowOrChargeXcblHelper.setMonetaryValue(allowOrCharge, TEN);
    assertEquals(valueOf(34).setScale(2, HALF_EVEN),
        computeInvoiceAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed));
    allowOrCharge.setBasisCoded(PERCENT);
    InvoiceAllowOrChargeXcblHelper.setPercentValue(allowOrCharge, TEN);
    assertEquals(valueOf(26.4).setScale(2, HALF_EVEN),
        computeInvoiceAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed));
  }

  @Test
  public void computeTaxOnAllowanceOrChargeTest() {
    InvoiceAllowOrChargeType allowOrCharge = InvoiceAllowOrChargeXcblHelper.buildInvoiceAllowOrCharge();
    InvoiceItemDetailType detail = buildInvoiceItemDetail();
    setQuantity(detail, valueOf(2));
    setCalculationNet(detail, valueOf(14), 6, HALF_EVEN);
    detail.getInvoicePricingDetail()
        .getTax()
        .add(createInvoiceTaxtype(getCalculationNet(detail), of(valueOf(5.50)), STANDARD_RATE));
    BigDecimal netPriceComputed = getCalculationNet(detail).orElse(ZERO);
    // Allowance Monetary
    allowOrCharge.setBasisCoded(MONETARY_AMOUNT);
    allowOrCharge.setIndicatorCoded(IndicatorCodeType.LINE_ITEM_CHARGE);
    setMonetaryValue(allowOrCharge, TEN);
    computeTaxOnInvoiceAllowanceOrCharge(allowOrCharge, detail, netPriceComputed);
    assertEquals(valueOf(5.50).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxPercent()
        .getValue());
    assertEquals(valueOf(48).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxableAmount());
    assertEquals(InvoiceHelper.toComplexBigDecimalType(valueOf(2.64).setScale(TAX_PRECISION, HALF_EVEN)),
        getFirstTax(allowOrCharge).get()
            .getTaxAmount());
    // Allowance Percent
    allowOrCharge.setBasisCoded(PERCENT);
    setPercentValue(allowOrCharge, TEN);
    computeTaxOnInvoiceAllowanceOrCharge(allowOrCharge, detail, netPriceComputed);
    assertEquals(valueOf(5.50).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxPercent()
        .getValue());
    assertEquals(valueOf(30.8).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxableAmount());
    assertEquals(toComplexBigDecimalType(valueOf(1.694).setScale(TAX_PRECISION, HALF_EVEN)),
        getFirstTax(allowOrCharge).get()
            .getTaxAmount());

    // Allowance Monetary
    allowOrCharge.setBasisCoded(MONETARY_AMOUNT);
    allowOrCharge.setIndicatorCoded(IndicatorCodeType.LINE_ITEM_ALLOWANCE);
    setMonetaryValue(allowOrCharge, TEN);
    computeTaxOnInvoiceAllowanceOrCharge(allowOrCharge, detail, netPriceComputed);
    assertEquals(valueOf(5.50).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxPercent()
        .getValue());
    assertEquals(valueOf(8).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxableAmount());
    assertEquals(toComplexBigDecimalType(valueOf(0.44).setScale(TAX_PRECISION, HALF_EVEN)), getFirstTax(allowOrCharge).get()
        .getTaxAmount());
    // Allowance Percent
    allowOrCharge.setBasisCoded(PERCENT);
    setPercentValue(allowOrCharge, TEN);
    computeTaxOnInvoiceAllowanceOrCharge(allowOrCharge, detail, netPriceComputed);
    assertEquals(valueOf(5.50).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxPercent()
        .getValue());
    assertEquals(valueOf(25.2).setScale(TAX_PRECISION, HALF_EVEN), getFirstTax(allowOrCharge).get()
        .getTaxableAmount());
    assertEquals(InvoiceHelper.toComplexBigDecimalType(valueOf(1.386).setScale(TAX_PRECISION, HALF_EVEN)),
        getFirstTax(allowOrCharge).get()
            .getTaxAmount());
  }

  @Test
  public void testAllowOrChargeFromSelectedDetail() {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType1 = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType2 = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType3 = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType4 = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType1.setIndicatorCoded(IndicatorCodeType.OTHER);
    InvoiceAllowOrChargeType2.setIndicatorCoded(IndicatorCodeType.CHARGE);
    InvoiceAllowOrChargeType3.setIndicatorCoded(IndicatorCodeType.CHARGE);
    InvoiceAllowOrChargeType4.setIndicatorCoded(IndicatorCodeType.OTHER);
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(InvoiceAllowOrChargeType1, null, BigDecimal.TEN, BigDecimal.ZERO);
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(InvoiceAllowOrChargeType2, null, BigDecimal.ZERO, BigDecimal.valueOf(20));
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(InvoiceAllowOrChargeType3, null, BigDecimal.ZERO, BigDecimal.TEN);
    InvoiceAllowOrChargeXcblHelper.initAllowanceCharge(InvoiceAllowOrChargeType4, null, BigDecimal.valueOf(5), BigDecimal.ZERO);

    InvoiceItemDetailType detail = buildInvoiceItemDetail();
    setAllowOrChargeFromSelectedDetail(detail, Arrays.asList(InvoiceAllowOrChargeType1, InvoiceAllowOrChargeType2));
    assertEquals(2, getAllowOrChargeFromSelectedDetail(detail).size());

    addAllowOrChargeLine(InvoiceAllowOrChargeType3, true, detail);
    List<InvoiceAllowOrChargeType> allowOrChargeFromSelectedDetail = getAllowOrChargeFromSelectedDetail(detail);
    assertEquals(2, allowOrChargeFromSelectedDetail.size());
    assertEquals(BigDecimal.TEN, allowOrChargeFromSelectedDetail.get(1)
        .getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercent()
        .getValue());

    addAllowOrChargeLine(InvoiceAllowOrChargeType4, false, detail);
    assertEquals(3, getAllowOrChargeFromSelectedDetail(detail).size());

    removeAllowOrChargeLine(InvoiceAllowOrChargeType4, detail);
    allowOrChargeFromSelectedDetail = getAllowOrChargeFromSelectedDetail(detail);
    assertEquals(2, allowOrChargeFromSelectedDetail.size());
    for (InvoiceAllowOrChargeType InvoiceAllowOrChargeType : allowOrChargeFromSelectedDetail) {
      assertNotEquals(InvoiceAllowOrChargeType4, InvoiceAllowOrChargeType);
    }
  }

  @Test
  public void testInitAllowanceChargeWithMonetaryAmount() {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType.setIndicatorCoded(IndicatorCodeType.OTHER);
    initAllowanceCharge(InvoiceAllowOrChargeType, null, BigDecimal.TEN, BigDecimal.ZERO);
    assertEquals(BasisCodeType.MONETARY_AMOUNT, InvoiceAllowOrChargeType.getBasisCoded());
    InvoiceMonetaryValueType monetaryValue = InvoiceAllowOrChargeType.getTypeOfAllowanceOrCharge()
        .getMonetaryValue();
    assertEquals(BigDecimal.TEN, monetaryValue.getMonetaryAmount()
        .getValue());
    assertEquals(CurrencyCodeType.EUR, monetaryValue.getCurrency()
        .getCurrencyCoded());

    InvoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getListOfDescription()
        .setValue("FRAIS DE LIVRAISON (Frais de transport)");
    assertEquals("FRAIS DE LIVRAISON (Frais de transport) : 10", InvoiceAllowOrChargeXcblHelper.inline(InvoiceAllowOrChargeType));
    InvoiceAllowOrChargeType.setIndicatorCoded(IndicatorCodeType.CHARGE);
    assertEquals("FRAIS DE LIVRAISON (Frais de transport) : +10", InvoiceAllowOrChargeXcblHelper.inline(InvoiceAllowOrChargeType));
  }

  @Test
  public void testInitAllowanceChargeWithPercentValue() {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType.setIndicatorCoded(IndicatorCodeType.OTHER);
    initAllowanceCharge(InvoiceAllowOrChargeType, null, BigDecimal.ZERO, BigDecimal.TEN);
    PercentageAllowanceOrChargeType percentage = InvoiceAllowOrChargeType.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge();
    assertEquals(BasisCodeType.PERCENT, InvoiceAllowOrChargeType.getBasisCoded());
    assertEquals(BigDecimal.TEN, percentage.getPercent()
        .getValue());
    assertEquals(CurrencyCodeType.EUR, percentage.getPercentageMonetaryValue()
        .getCurrency()
        .getCurrencyCoded());

    InvoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getListOfDescription()
        .setValue("REMISE PROMOTIONNELLE (Remise promotionnelle)");
    assertEquals("REMISE PROMOTIONNELLE (Remise promotionnelle) : 10%", InvoiceAllowOrChargeXcblHelper.inline(InvoiceAllowOrChargeType));
    InvoiceAllowOrChargeType.setIndicatorCoded(IndicatorCodeType.ALLOWANCE);
    assertEquals("REMISE PROMOTIONNELLE (Remise promotionnelle) : -10%", InvoiceAllowOrChargeXcblHelper.inline(InvoiceAllowOrChargeType));
  }

  @Test
  public void testGetAllowOrChargeValue() {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType.setBasisCoded(BasisCodeType.PERCENT);
    setPercentValue(InvoiceAllowOrChargeType, TEN);
    assertEquals(valueOf(120).setScale(2, HALF_EVEN), getAllowOrChargeValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(1200), 2));

    InvoiceAllowOrChargeType.setBasisCoded(BasisCodeType.MONETARY_AMOUNT);
    setMonetaryValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(150));
    assertEquals(valueOf(150).setScale(1, HALF_EVEN), getAllowOrChargeValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(1000), 1));

    InvoiceAllowOrChargeType.getTax()
        .get(0)
        .setTaxCategoryCodedOther(toComplexStringType(""));
    assertEquals(valueOf(150).setScale(1, HALF_EVEN), getAllowOrChargeValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(1000), 1));
  }
  
  @Test
  public void getParafiscalTaxValueWithMonetaryAmountTest() {
    Map<String, TotalAllowOrChargeType> parafiscalTaxSummaries = new TreeMap<>();
    
    InvoiceAllowOrChargeType invoiceAllowOrChargeType1 = createParafiscalTax("Forestry tax", IndicatorCodeType.SERVICE, MONETARY_AMOUNT);
    setMonetaryValue(invoiceAllowOrChargeType1, BigDecimal.valueOf(2));
    InvoiceAllowOrChargeType invoiceAllowOrChargeType2 = createParafiscalTax("Forestry tax", IndicatorCodeType.SERVICE, MONETARY_AMOUNT);
    setMonetaryValue(invoiceAllowOrChargeType2, BigDecimal.valueOf(3));
    
    List<InvoiceAllowOrChargeType> allowOrCharges = getAllowOrCharges(invoiceAllowOrChargeType1, invoiceAllowOrChargeType2);
    
    assertEquals(BigDecimal.valueOf(2), allowOrCharges.get(0)
        .getTypeOfAllowanceOrCharge()
        .getMonetaryValue()
        .getMonetaryAmount()
        .getValue());
    assertEquals(BigDecimal.valueOf(3), allowOrCharges.get(1)
        .getTypeOfAllowanceOrCharge()
        .getMonetaryValue()
        .getMonetaryAmount()
        .getValue());
    
    TaxSummaryXcblHelper.populateInvoiceParafiscalTaxSummary(parafiscalTaxSummaries, allowOrCharges, TAX_PRECISION,
        BigDecimal.valueOf(2));
    assertEquals(1, parafiscalTaxSummaries.size());
    assertEquals(BigDecimal.valueOf(10).setScale(TAX_PRECISION, HALF_EVEN), parafiscalTaxSummaries.get("Forestry tax")
        .getSummaryAllowOrCharge()
        .getMonetaryAmount());
  }
  
  @Test
  public void getParafiscalTaxValuesWithPercentTest() {
    Map<String, TotalAllowOrChargeType> parafiscalTaxSummaries = new TreeMap<>();
        
    InvoiceAllowOrChargeType invoiceAllowOrChargeType1 = createParafiscalTax("Forestry tax", IndicatorCodeType.SERVICE, PERCENT);
    setPercentValue(invoiceAllowOrChargeType1, TEN);
    InvoiceAllowOrChargeType invoiceAllowOrChargeType2 = createParafiscalTax("HBJOAT Tax", IndicatorCodeType.SERVICE, PERCENT);
    setPercentValue(invoiceAllowOrChargeType2, BigDecimal.valueOf(5));

    List<InvoiceAllowOrChargeType> allowOrCharges = getAllowOrCharges(invoiceAllowOrChargeType1, invoiceAllowOrChargeType2);
    
    assertEquals(BigDecimal.ONE, allowOrCharges.get(0)
        .getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercentageMonetaryValue()
        .getMonetaryAmount());
    assertEquals(BigDecimal.valueOf(0.55).setScale(TAX_PRECISION, HALF_EVEN), allowOrCharges.get(1)
        .getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercentageMonetaryValue()
        .getMonetaryAmount());
    
    TaxSummaryXcblHelper.populateInvoiceParafiscalTaxSummary(parafiscalTaxSummaries, allowOrCharges, TAX_PRECISION,
        BigDecimal.valueOf(2));
    assertEquals(2, parafiscalTaxSummaries.size());
    assertEquals(BigDecimal.valueOf(2), parafiscalTaxSummaries.get("Forestry tax")
        .getSummaryAllowOrCharge()
        .getMonetaryAmount());
    assertEquals(BigDecimal.valueOf(1.10).setScale(TAX_PRECISION, HALF_EVEN), parafiscalTaxSummaries.get("HBJOAT Tax")
        .getSummaryAllowOrCharge()
        .getMonetaryAmount());
  }
  
  @Test
  public void getParafiscalTaxValueWithPercentAndAllowanceTest() {
    Map<String, TotalAllowOrChargeType> parafiscalTaxSummaries = new TreeMap<>();
    
    InvoiceAllowOrChargeType invoiceAllowOrChargeType1 = createInvoiceAllowOrCharge();
    invoiceAllowOrChargeType1.setIndicatorCoded(LINE_ITEM_ALLOWANCE);
    invoiceAllowOrChargeType1.setBasisCoded(MONETARY_AMOUNT);
    setMonetaryValue(invoiceAllowOrChargeType1, BigDecimal.valueOf(3));
    
    InvoiceAllowOrChargeType invoiceAllowOrChargeType2 = createParafiscalTax("Forestry tax", IndicatorCodeType.SERVICE, PERCENT);
    setPercentValue(invoiceAllowOrChargeType2, TEN);

    List<InvoiceAllowOrChargeType> allowOrCharges = getAllowOrCharges(invoiceAllowOrChargeType1, invoiceAllowOrChargeType2);
    
    assertEquals(BigDecimal.valueOf(3), allowOrCharges.get(0)
        .getTypeOfAllowanceOrCharge()
        .getMonetaryValue()
        .getMonetaryAmount()
        .getValue());
    assertEquals(BigDecimal.valueOf(0.70).setScale(TAX_PRECISION, HALF_EVEN), allowOrCharges.get(1)
        .getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercentageMonetaryValue()
        .getMonetaryAmount());
    
    TaxSummaryXcblHelper.populateInvoiceParafiscalTaxSummary(parafiscalTaxSummaries, Arrays.asList(allowOrCharges.get(1)), TAX_PRECISION,
        BigDecimal.valueOf(2));
    assertEquals(1, parafiscalTaxSummaries.size());
    assertEquals(BigDecimal.valueOf(1.40).setScale(TAX_PRECISION, HALF_EVEN), parafiscalTaxSummaries.get("Forestry tax")
        .getSummaryAllowOrCharge()
        .getMonetaryAmount());
  }
  
  @Test
  public void getParafiscalTaxValueWithPercentAndMonetaryAmountTest() {
    Map<String, TotalAllowOrChargeType> parafiscalTaxSummaries = new TreeMap<>();
        
    InvoiceAllowOrChargeType invoiceAllowOrChargeType1 = createParafiscalTax("Forestry tax", IndicatorCodeType.SERVICE, PERCENT);
    setPercentValue(invoiceAllowOrChargeType1, TEN);
    InvoiceAllowOrChargeType invoiceAllowOrChargeType2 = createParafiscalTax("Forestry tax", IndicatorCodeType.SERVICE, MONETARY_AMOUNT);
    setMonetaryValue(invoiceAllowOrChargeType2, BigDecimal.valueOf(2));

    List<InvoiceAllowOrChargeType> allowOrCharges = getAllowOrCharges(invoiceAllowOrChargeType1, invoiceAllowOrChargeType2);
    
    assertEquals(BigDecimal.valueOf(1), allowOrCharges.get(0)
        .getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercentageMonetaryValue()
        .getMonetaryAmount());
    assertEquals(BigDecimal.valueOf(2), allowOrCharges.get(1)
        .getTypeOfAllowanceOrCharge()
        .getMonetaryValue()
        .getMonetaryAmount()
        .getValue());
    
    TaxSummaryXcblHelper.populateInvoiceParafiscalTaxSummary(parafiscalTaxSummaries, allowOrCharges, TAX_PRECISION,
        BigDecimal.valueOf(2));
    assertEquals(1, parafiscalTaxSummaries.size());
    assertEquals(BigDecimal.valueOf(6).setScale(TAX_PRECISION, HALF_EVEN), parafiscalTaxSummaries.get("Forestry tax")
        .getSummaryAllowOrCharge()
        .getMonetaryAmount());
  }


  @Test
  public void testWithTva0() {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = createInvoiceAllowOrChargeWithTva("0.0");
    setPercentValue(InvoiceAllowOrChargeType, TEN);

    BigDecimal allowOrChargeValue = getAllowOrChargeValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(100), 2);
    assertEquals(valueOf(10).setScale(2, HALF_EVEN), allowOrChargeValue);
    allowOrChargeValue = getAllowOrChargeValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(100), 2);
    assertEquals(valueOf(10).setScale(2, HALF_EVEN), allowOrChargeValue);
  }

  @Test
  public void testWithTva19_6() {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = createInvoiceAllowOrChargeWithTva("19.6");
    setPercentValue(InvoiceAllowOrChargeType, TEN);

    BigDecimal allowOrChargeValue = getAllowOrChargeValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(100), 2);
    assertEquals(valueOf(10).setScale(2, HALF_EVEN), allowOrChargeValue);
    allowOrChargeValue = getAllowOrChargeValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(100), 2);
    assertEquals(valueOf(10).setScale(2, HALF_EVEN), allowOrChargeValue);
  }

  public static InvoiceAllowOrChargeType createInvoiceAllowOrChargeWithTva(String percentTva) {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = new InvoiceAllowOrChargeType();
    InvoiceAllowOrChargeType.setBasisCoded(PERCENT);

    InvoiceTypeOfAllowanceOrChargeType typeOfAllowanceOrCharge = new InvoiceTypeOfAllowanceOrChargeType();
    typeOfAllowanceOrCharge.setPercentageAllowanceOrCharge(InvoiceAllowOrChargeXcblHelper.getPercentageAllowanceOrChargeType(ZERO));
    InvoiceAllowOrChargeType.setTypeOfAllowanceOrCharge(typeOfAllowanceOrCharge);
    List<InvoiceTaxType> tax = InvoiceAllowOrChargeType.getTax();
    tax.add(getTVA(percentTva));

    return InvoiceAllowOrChargeType;
  }

  private static InvoiceTaxType getTVA(String percent) {
    InvoiceTaxType tax = new InvoiceTaxType();

    tax.setTaxCategoryCoded(OTHER);
    tax.setTaxCategoryCodedOther(percent.equals("0.0") ? null : toComplexStringType(percent));
    tax.setTaxFunctionQualifierCoded(TAX);
    tax.setTaxTypeCoded("ValueAddedTax");
    tax.setTaxPercent(new PercentType());

    return tax;
  }

  @Test
  public void testUpdateIndicatorCodedOfAllowOrCharge() {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType.setIndicatorCoded(IndicatorCodeType.CHARGE);

    updateIndicatorCodedOfAllowOrCharge(InvoiceAllowOrChargeType, "Allowance");
    assertEquals(IndicatorCodeType.ALLOWANCE, InvoiceAllowOrChargeType.getIndicatorCoded());
    updateIndicatorCodedOfAllowOrCharge(InvoiceAllowOrChargeType, "LineItemAllowance");
    assertEquals(IndicatorCodeType.LINE_ITEM_ALLOWANCE, InvoiceAllowOrChargeType.getIndicatorCoded());
    updateIndicatorCodedOfAllowOrCharge(InvoiceAllowOrChargeType, "Adjustment");
    assertEquals(IndicatorCodeType.ADJUSTMENT, InvoiceAllowOrChargeType.getIndicatorCoded());
    updateIndicatorCodedOfAllowOrCharge(InvoiceAllowOrChargeType, "Other");
    assertEquals(IndicatorCodeType.OTHER, InvoiceAllowOrChargeType.getIndicatorCoded());
    updateIndicatorCodedOfAllowOrCharge(InvoiceAllowOrChargeType, null);
    assertEquals(IndicatorCodeType.OTHER, InvoiceAllowOrChargeType.getIndicatorCoded());
  }

  @Test
  public void testAddAllOrChHeaderAndRemove() {
    InvoiceHeaderType invoiceHeaderType = new InvoiceHeaderType();
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = addAllOrChHeader(invoiceHeaderType, BigDecimal.valueOf(10), "shipping",
        VENDOR_FREIGHT, 2,
        true);
    List<InvoiceTaxType> taxes = InvoiceAllowOrChargeType.getTax();
    assertEquals(1, taxes.size());
    assertEquals(BigDecimal.TEN.setScale(2, HALF_EVEN), taxes.get(0)
        .getTaxableAmount());
    assertEquals(BigDecimal.ZERO.setScale(2, HALF_EVEN), taxes.get(0)
        .getTaxPercent()
        .getValue());

    InvoiceAllowOrChargeType = addAllOrChHeader(invoiceHeaderType, BigDecimal.valueOf(50), "shipping", VENDOR_FREIGHT, 2, false);
    taxes = InvoiceAllowOrChargeType.getTax();
    assertEquals(1, taxes.size());
    assertEquals(BigDecimal.valueOf(50)
        .setScale(2, HALF_EVEN),
        taxes.get(0)
            .getTaxableAmount());
    assertEquals(BigDecimal.valueOf(20)
        .setScale(2, HALF_EVEN),
        taxes.get(0)
            .getTaxPercent()
            .getValue());

    removeAllOrChHeader(invoiceHeaderType, VENDOR_FREIGHT);
    assertEquals(0, invoiceHeaderType.getInvoiceAllowancesOrCharges()
        .getAllowOrCharge()
        .size());
  }

  @Test
  public void testGetAmountVAT() {
    InvoiceAllowOrChargeType InvoiceAllowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType.setBasisCoded(BasisCodeType.MONETARY_AMOUNT);
    InvoiceAllowOrChargeXcblHelper.setMonetaryValue(InvoiceAllowOrChargeType, BigDecimal.valueOf(150));
    InvoiceAllowOrChargeType.getTax()
        .get(0)
        .getTaxPercent()
        .setValue(BigDecimal.valueOf(20));

    Optional<BigDecimal> amountVAT = getAmountVAT(InvoiceAllowOrChargeType);
    assertEquals(BigDecimal.valueOf(30), amountVAT.get());
  }

  @Test
  public void testClearTypeOfInvoiceAllowOrChargeType() {
    List<InvoiceAllowOrChargeType> items = new ArrayList<>();
    InvoiceAllowOrChargeType item1 = createInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType item2 = createInvoiceAllowOrCharge();
    PercentageAllowanceOrChargeType percentageAllowanceOrChargeType = new PercentageAllowanceOrChargeType();
    item1.setBasisCoded(PERCENT);
    setMonetaryValue(item1, BigDecimal.valueOf(150));
    item2.setBasisCoded(MONETARY_AMOUNT);
    item2.getTypeOfAllowanceOrCharge()
        .setPercentageAllowanceOrCharge(percentageAllowanceOrChargeType);
    items.add(item1);
    items.add(item2);

    InvoiceAllowOrChargeXcblHelper.clearTypeOfInvoiceAllowOrChargeType(items);
    assertTrue(items.contains(item1) && items.contains(item2));
    assertNull(item1.getTypeOfAllowanceOrCharge()
        .getMonetaryValue());
    assertNull(item2.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge());
  }
  
  private List<InvoiceAllowOrChargeType> getAllowOrCharges(InvoiceAllowOrChargeType invoiceAllowOrChargeType1, InvoiceAllowOrChargeType invoiceAllowOrChargeType2) {
    InvoiceItemDetailType detail = buildInvoiceItemDetail();
    
    setAllowOrChargeFromSelectedDetail(detail, Arrays.asList(invoiceAllowOrChargeType1, invoiceAllowOrChargeType2));
    List<InvoiceAllowOrChargeType> allowOrCharges = getAllowOrChargeFromSelectedDetail(detail);
    BigDecimal netPriceComputed = TEN;
    for (InvoiceAllowOrChargeType allowOrCharge : allowOrCharges) {
      netPriceComputed = InvoiceAllowOrChargeXcblHelper.computeInvoiceAllowanceOrChargeOnSelectedDetail(allowOrCharge, netPriceComputed)
          .setScale(TAX_PRECISION, HALF_EVEN);
    }
    return allowOrCharges;
  }
  
  private InvoiceAllowOrChargeType createParafiscalTax(String description, IndicatorCodeType indicator, BasisCodeType basisCodeType) {
    InvoiceAllowOrChargeType invoiceAllowOrChargeType = createInvoiceParafiscalTax();
    invoiceAllowOrChargeType.setIndicatorCoded(indicator);
    invoiceAllowOrChargeType.getAllowanceOrChargeDescription()
        .getListOfDescription()
        .setValue(description);
    invoiceAllowOrChargeType.setBasisCoded(basisCodeType);
    return invoiceAllowOrChargeType;
  }
}
