package com.byzaneo.generix.edocument.service.documentcheck;

import static org.junit.jupiter.api.Assertions.fail;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import org.junit.jupiter.api.Test;

public class DocumentFileCheckPolicyITCase {

  @Test
  public void test_no_file() {
    Document document = new Document();
    DocumentFileCheckPolicy[] policies = DocumentFileCheckPolicy.values();
    for (DocumentFileCheckPolicy curPolicy : policies) {
      switch (curPolicy) {
      case NO_CHECK:
        try {
          curPolicy.checkDocument(document);
        }
        catch (Exception e) {
          fail("no exception should have been thrown");
        }
        break;
      case XCBL_AND_EDIUNIT:
      case XCBL_AND_PDF:
      case XCBL_AND_XML:
        try {
          curPolicy.checkDocument(document);
          fail("no file should trigger exception for " + curPolicy);
        }
        catch (Exception e) {
          // System.out.println("expected behavior for " + curPolicy);
        }
        break;
      default:
        fail("unexpected policy : " + curPolicy);
        break;

      }
    }
  }

  @Test
  public void test_XCBL_AND_PDF() {
    // fixture
    Document document = new Document();
    document.addFile(new DocumentFile(null, FileType.XCBL, null, null, document, null));
    document.addFile(new DocumentFile(null, FileType.PDF, null, null, document, null));

    DocumentFileCheckPolicy[] policies = DocumentFileCheckPolicy.values();
    for (DocumentFileCheckPolicy curPolicy : policies) {
      switch (curPolicy) {
      case NO_CHECK:
      case XCBL_AND_PDF:
        try {
          curPolicy.checkDocument(document);
        }
        catch (Exception e) {
          fail("no exception should have been thrown");
        }
        break;
      case XCBL_AND_EDIUNIT:
      case XCBL_AND_XML:
        try {
          curPolicy.checkDocument(document);
          fail("XCBL and PDF file should trigger exception for " + curPolicy);
        }
        catch (Exception e) {
          // System.out.println("expected behavior for " + curPolicy);
        }
        break;
      default:
        fail("unexpected policy : " + curPolicy);
        break;

      }
    }
  }

  @Test
  public void test_XCBL_AND_EDIUNIT() {
    // fixture
    Document document = new Document();
    document.addFile(new DocumentFile(null, FileType.XCBL, null, null, document, null));
    document.addFile(new DocumentFile(null, FileType.EDIUNIT, null, null, document, null));

    DocumentFileCheckPolicy[] policies = DocumentFileCheckPolicy.values();
    for (DocumentFileCheckPolicy curPolicy : policies) {
      switch (curPolicy) {
      case NO_CHECK:
      case XCBL_AND_EDIUNIT:
        try {
          curPolicy.checkDocument(document);
        }
        catch (Exception e) {
          fail("no exception should have been thrown");
        }
        break;
      case XCBL_AND_PDF:
      case XCBL_AND_XML:
        try {
          curPolicy.checkDocument(document);
          fail("XCBL and EDIUNIT file should trigger exception for " + curPolicy);
        }
        catch (Exception e) {
          // System.out.println("expected behavior for " + curPolicy);
        }
        break;
      default:
        fail("unexpected policy : " + curPolicy);
        break;

      }
    }
  }

  @Test
  public void test_XCBL_AND_XML() {
    // fixture
    Document document = new Document();
    document.addFile(new DocumentFile(null, FileType.XCBL, null, null, document, null));
    document.addFile(new DocumentFile(null, FileType.XML, null, null, document, null));

    DocumentFileCheckPolicy[] policies = DocumentFileCheckPolicy.values();
    for (DocumentFileCheckPolicy curPolicy : policies) {
      switch (curPolicy) {
      case NO_CHECK:
      case XCBL_AND_XML:
        try {
          curPolicy.checkDocument(document);
        }
        catch (Exception e) {
          fail("no exception should have been thrown");
        }
        break;
      case XCBL_AND_PDF:
      case XCBL_AND_EDIUNIT:
        try {
          curPolicy.checkDocument(document);
          fail("XCBL and XML file should trigger exception for " + curPolicy);
        }
        catch (Exception e) {
          // System.out.println("expected behavior for " + curPolicy);
        }
        break;
      default:
        fail("unexpected policy : " + curPolicy);
        break;

      }
    }
  }
}
