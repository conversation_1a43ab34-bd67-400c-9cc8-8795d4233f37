package com.byzaneo.generix.edocument.validation.constraints;

import com.byzaneo.commons.util.DomHelper;
import com.byzaneo.generix.edocument.util.XQuery;
import org.junit.jupiter.api.Test;
import org.w3c.dom.Node;

import java.io.File;
import java.util.List;

import static com.byzaneo.commons.util.DomHelper.evaluateNodeList;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class EmptyConstraintTest {
  private static final Node XCBL_ROOT = DomHelper.parseFile(new File("./src/test/resources/xcbl/InvoiceForEmptyConstraintTest.xml"),
      false);

  @Test
  public void testValid1() {
    EmptyConstraint instance = new EmptyConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber2");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid2() {
    EmptyConstraint instance = new EmptyConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber3");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testValid3() {
    EmptyConstraint instance = new EmptyConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/ListOfTaxSummary/TaxSummary/TaxFunctionQualifierCoded");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertTrue(result);
  }

  @Test
  public void testNotValid1() {
    EmptyConstraint instance = new EmptyConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/number");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }

  @Test
  public void testNotValid2() {
    EmptyConstraint instance = new EmptyConstraint();
    instance.setXQuery(new XQuery());
    List<Node> nodes = evaluateNodeList(XCBL_ROOT, "//summary/numbers/otherNumber1");
    instance.setNodes(nodes);
    boolean result = instance.isValid();
    assertFalse(result);
  }
}
