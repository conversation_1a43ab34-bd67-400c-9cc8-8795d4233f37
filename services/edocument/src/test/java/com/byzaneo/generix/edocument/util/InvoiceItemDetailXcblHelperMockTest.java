package com.byzaneo.generix.edocument.util;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceItemDetailType;

import java.math.BigDecimal;
import java.util.Map;

import static com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper.applyInvoiceAllowanceOrChargeOnDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.buildInvoiceItemDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.populateItemDetailWithUnitPrice;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationGross;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationNet;
import static com.byzaneo.generix.edocument.util.InvoiceTaxXcblHelper.createInvoiceTaxtype;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_ALLOWANCE;
import static java.math.RoundingMode.HALF_EVEN;

public class InvoiceItemDetailXcblHelperMockTest {

  public static InvoiceItemDetailType createItem(BigDecimal value, int precision) {
    InvoiceItemDetailType itemDetail = buildInvoiceItemDetail();
    populateItemDetailWithUnitPrice(itemDetail, null, precision);
    setCalculationGross(itemDetail, value, precision, HALF_EVEN);
    setCalculationNet(itemDetail, value, precision, HALF_EVEN);
    itemDetail.getInvoicePricingDetail()
        .getTax()
        .add(createInvoiceTaxtype());
    return itemDetail;
  }

  public static InvoiceAllowOrChargeType addAllowance(InvoiceItemDetailType item) {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    allowOrChargeType.setIndicatorCoded(LINE_ITEM_ALLOWANCE);
    item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(allowOrChargeType);
    return allowOrChargeType;
  }

  public static InvoiceAllowOrChargeType addChr(InvoiceItemDetailType item) {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceAllowOrCharge();
    allowOrChargeType.setIndicatorCoded(IndicatorCodeType.LINE_ITEM_CHARGE);
    item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(allowOrChargeType);
    return allowOrChargeType;
  }

  public static InvoiceAllowOrChargeType addParafiscalTax(InvoiceItemDetailType item) {
    InvoiceAllowOrChargeType allowOrChargeType = InvoiceAllowOrChargeXcblHelper.createInvoiceTaxLines(null,
        ParafiscalTax.PT_ALCOHOL.getCodeEAN(),
        ParafiscalTax.PT_ALCOHOL.getDefaultLabel());
    item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(allowOrChargeType);
    return allowOrChargeType;
  }

  public static void computeItem(InvoiceItemDetailType itemDetail, Map<String, InvoiceTaxSummaryType> taxSummaries, int detailPrecision,
      int amountPrecision) {
    applyInvoiceAllowanceOrChargeOnDetail(itemDetail, detailPrecision);
    InvoiceItemDetailXcblHelper.computeAmountHT(itemDetail, amountPrecision, HALF_EVEN);
    InvoiceItemDetailXcblHelper.applyTaxOnInvoiceItemDetail(itemDetail, taxSummaries, amountPrecision);
    InvoiceItemDetailXcblHelper.computeAmountTTC(itemDetail, amountPrecision, HALF_EVEN);
  }

}
