package com.byzaneo.generix.edocument.service.impl;

import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATA_DIR;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.byzaneo.commons.bean.Logs;
import com.byzaneo.commons.service.ConfigurationService;
import com.byzaneo.commons.test.SpringJUnitClassListener;
import com.byzaneo.commons.test.SystemPropertyContextLoader;
import com.byzaneo.generix.edocument.service.InventoryReportService;
import com.byzaneo.location.bean.Address;
import com.byzaneo.query.Query;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Location;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.service.DocumentStatusService;
import com.byzaneo.xtrade.util.DocumentHelper;
import com.byzaneo.xtrade.xcbl.bean.InventoryReport;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import org.apache.commons.io.FileUtils;
import org.apache.log4j.Level;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = {
    "classpath:/gnx-edoc-test.beans.xml"}, loader = SystemPropertyContextLoader.class)
public class InventoryReportServbiceITCase {

  @Autowired
  private transient InventoryReportService inventoryService;

  @Autowired
  private ConfigurationService configurationService;

  @Autowired
  private DocumentService documentService;

  @Autowired
  @Qualifier(DocumentStatusService.SERVICE_NAME)
  private static DocumentStatusService statusService;

  @BeforeAll
  public static void setup(@Autowired DocumentStatusService statusService) throws Exception {
    DocumentHelper.populateDocumentStatusEntityTable(statusService);
  }

  @Test
  public void testImportInventoryReport() throws Exception {

    Logs logs = importFile("in/invrpt/import-test.csv");
    Page<InventoryReport> invReports = documentService.searchIndexables(InventoryReport.class,
        new Query(), null);
    assertEquals(1, invReports.getContent()
        .size());

    assertEquals(17, invReports.getContent()
        .get(0)
        .getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .size());

    documentService.removeIndexables(invReports.getContent());
  }

  @Test
  public void testImportInventoryReport2() throws Exception {

    Logs logs = importFile("in/invrpt/import-test-2.csv");
    Page<InventoryReport> invReports = documentService.searchIndexables(InventoryReport.class,
        new Query(), null);
    assertEquals(1, invReports.getContent()
        .size());

    assertEquals(17, invReports.getContent()
        .get(0)
        .getListOfInventoryReportDetail()
        .getInventoryReportDetail()
        .size());

    documentService.removeIndexables(invReports.getContent());
  }

  @Test
  public void testImportInventoryReportError() throws Exception {

    Logs logs = importFile("in/invrpt/import-test-err-1.csv");
    assertEquals(2, logs.getLogEventList(Level.ERROR.toString())
        .size());
  }

  @Test
  public void testImportInventoryReportError2() throws Exception {

    Logs logs = importFile("in/invrpt/import-test-err-2.csv");
    assertEquals(2, logs.getLogEventList(Level.ERROR.toString())
        .size());
  }

  @Test
  public void testImportInventoryReportError3() throws Exception {

    Logs logs = importFile("in/invrpt/import-test-err-3.csv");
    assertEquals(2, logs.getLogEventList(Level.ERROR.toString())
        .size());
  }

  private Logs importFile(String path) throws IOException {
    Company company = new Company();
    company.setId("123");
    company.setCode("CRFID");
    company.setFullname("company fullname");
    Location location = new Location();
    Address address = new Address();
    address.setStreetName("company street name");
    address.setCountry(Locale.FRANCE);
    location.setAddress(address);
    company.setLocation(location);

    Partner partner = new Partner();
    partner.setId("999");
    partner.setCode("800199");
    partner.setFullname("partner fullname");
    location = new Location();
    address = new Address();
    address.setCity("Paris");
    address.setStreetName("partner street name");
    address.setCountry(Locale.FRANCE);
    location.setAddress(address);

    partner.setLocation(location);

    File dataDir = configurationService.getFile(DATA_DIR, null);
    File invRptCsv = dataDir.toPath()
        .resolve(path)
        .toFile();

    List<InputStream> streams = new ArrayList<>();
    streams.add(FileUtils.openInputStream(invRptCsv));
    Logs logs = inventoryService.importInventoryReport(company, partner, streams, null);
    streams.stream()
        .close();
    return logs;
  }
}
