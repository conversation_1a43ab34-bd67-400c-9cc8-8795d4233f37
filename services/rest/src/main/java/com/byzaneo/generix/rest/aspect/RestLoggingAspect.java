package com.byzaneo.generix.rest.aspect;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.byzaneo.commons.service.ConfigurationService;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.bean.TechnicalUser;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.xtrade.service.DocumentService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.ws.rs.core.Response;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static org.slf4j.LoggerFactory.getLogger;

@Aspect
public class RestLoggingAspect {

  private static final Logger log = getLogger(RestLoggingAspect.class);

  private final Environment env;

  @Autowired
  @Qualifier(AccountService.SERVICE_NAME)
  private AccountService accountService;
  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private ConfigurationService configurationService;
  @Autowired(required = false)
  protected HttpServletRequest request;
  String adminRealm="";

  @PostConstruct
  public final void init() {
    adminRealm = configurationService.getProperties().getProperty("auth.server.BO.realm", "");
  }

  public RestLoggingAspect(Environment env) {
    this.env = env;
  }

  private void logDetails(ProceedingJoinPoint joinPoint, long executionTime, int status, String statusMessage) {
    String logToShow = "";
    String endpoint = "";
    String user = "";
    String username = "";

    if (joinPoint != null && joinPoint.getSignature() != null) {
      endpoint = joinPoint.getSignature()
          .getDeclaringTypeName() + "." + joinPoint.getSignature()
          .getName();
    }

    try {
      user = RestServiceHelper.getUserFromRequest(request);
    }
    catch (Exception e) {
      user = RestServiceHelper.getJWTUsername(request);
    }

    String authorizationToken = request.getHeader("Authorization");
    if (authorizationToken != null && authorizationToken.startsWith("Bearer ")) {
        String jwt = authorizationToken.substring(7);
        //no need validation Token, should be done by TokenValidator if request come here
        DecodedJWT decodedJWT = JWT.decode(jwt);
        username = decodedJWT.getClaim("clientId")
                .asString();
    }

    User cpt = accountService.getUserByLoginOrOpenID(username);
    String method = request.getMethod();
    String tenantName = request.getHeader("X-TenantID");

    long start = System.currentTimeMillis();
    logToShow += ("\"level\" : \"" + ((log.isDebugEnabled()) ? Level.DEBUG.name() : Level.INFO.name()) + "\"");
    logToShow += ", ";
    logToShow += ("\"timestamp\" : \"" + (new Timestamp(start)) + "\"");
    logToShow += ", ";
    String systemId = Optional.ofNullable(cpt)
            .map(User::getPrimaryGroup)
            .map(Group::getParent)
            .map(Group::getCode)
            .orElse("");
    if (StringUtils.isNotBlank(systemId))
      logToShow += ("\"SystemId\" : \"" + systemId + "\", ");
    else
      logToShow += ("\"SystemId\" : \"Undefined\", ");
    logToShow += ("\"ComponentID\" : \"TBD\", ");
    if (StringUtils.isNotBlank(endpoint))
      logToShow += ("\"operationId\" : \"" + endpoint.split("\\.")[endpoint.split("\\.").length - 1] + "\", ");
    else
      logToShow += ("\"operationId\" : \"Undefined\", ");
    String requestId = null;
    String origin = null;
    if (request != null) {
      requestId = request.getParameter("requestId");
      origin = request.getHeader("Origin");
    }
    if (requestId != null)
      logToShow += ("\"CorrelationId\" : \"" + requestId + "\", ");
    else
      logToShow += ("\"CorrelationId\" : \"Undefined\", ");
    logToShow += ("\"TenantId\" : \"" + tenantName + "\", ");
    String instanceId = Optional.ofNullable(request)
        .map(HttpServletRequest::getSession)
        .map(HttpSession::getId)
        .orElse("");
    if (StringUtils.isNotBlank(instanceId))
      logToShow += ("\"instanceId\" : \"" + instanceId + "\", ");
    else
      logToShow += ("\"instanceId\" : \"Undefined\", ");
    logToShow += ("\"Starttime\" : \"" + start + "ms" + "\", ");
    logToShow += ("\"Execution time\" : \"" + executionTime + "ms" + "\", ");
    logToShow += ("\"message\" : \"" + statusMessage + "\", ");
    logToShow += ("\"http method\" : \"" + method + "\", ");
    logToShow += ("\"Status\" : \"" + status + "\", ");
    String userId = Optional.ofNullable(cpt)
        .map(User::getId)
        .orElse("");
    if (cpt != null && StringUtils.isNotBlank(userId))
      logToShow += ("\"clientId\" : \"" + userId + "\", ");
    else
      logToShow += ("\"clientId\" : \"Undefined\", ");

    if (adminRealm != null || origin != null)
      logToShow += ("\"realm\" : \"" + adminRealm != null ? adminRealm : origin != null ? origin : "Undefined" + "\", ");
    logToShow += ("\"userId\" : \"" + user + "\"");
    String impersonatorUsername = RestServiceHelper.getJWTImpersonatorUsername(request);
    if (impersonatorUsername != null) {
      logToShow += (", \"sourceUserId\" : \"" + impersonatorUsername + "\"");
    }
    log.info(logToShow);
  }

  @AfterThrowing(value = "@annotation(org.springframework.security.access.prepost.PreAuthorize)", throwing = "ex")
  public void logException(JoinPoint joinPoint, Exception ex) throws Throwable {
    long start = System.currentTimeMillis();
    log.error("Exception in executing " + joinPoint.getSignature() + " : " + ex.getMessage());
    logDetails((ProceedingJoinPoint) joinPoint, System.currentTimeMillis() - start, 500, ex.getMessage());
  }

  @Pointcut(
      "within(@org.springframework.stereotype.Repository *)" +
          " || within(@org.springframework.stereotype.Service *)" +
          " || within(@org.springframework.web.bind.annotation.RestController *)"
  )
  public void springBeanPointcut() {
    // Method is empty as this is just a Pointcut, the implementations are in the advices.
  }

  /**
   * Pointcut that matches all Spring beans in the application's main packages.
   */
  @Pointcut(
      "within(javax.ws.rs..*)" +
          " || within(com.byzaneo.generix.rest.service..*)" +
              " || within(com.byzaneo.generix.api.service..*)"
  )
  public void applicationPackagePointcut() {
    // Method is empty as this is just a Pointcut, the implementations are in the advices.
  }

  /**
   * Retrieves the {@link Logger} associated to the given {@link JoinPoint}.
   *
   * @param joinPoint join point we want the logger for.
   * @return {@link Logger} associated to the given {@link JoinPoint}.
   */
  private Logger logger(JoinPoint joinPoint) {
    return LoggerFactory.getLogger(joinPoint.getSignature()
        .getDeclaringTypeName());
  }

  /**
   * Advice that logs methods throwing exceptions.
   *
   * @param joinPoint join point for advice.
   * @param e         exception.
   */
  @AfterThrowing(pointcut = "applicationPackagePointcut() && springBeanPointcut()", throwing = "e")
  public void logAfterThrowing(JoinPoint joinPoint, Throwable e) {
    long start = System.currentTimeMillis();
    if (env.acceptsProfiles(Profiles.of(Group.OWN_VIEW_PROFILE))) {
      logger(joinPoint)
          .error(
              "Exception in {}() with cause = '{}' and exception = '{}'",
              joinPoint.getSignature()
                  .getName(),
              e.getCause() != null ? e.getCause() : "NULL",
              e.getMessage(),
              e
          );
    }
    else {
      logger(joinPoint)
          .error(
              "Exception in {}() with cause = {}",
              joinPoint.getSignature()
                  .getName(),
              e.getCause() != null ? e.getCause() : "NULL"
          );
    }
    logDetails((ProceedingJoinPoint) joinPoint, System.currentTimeMillis() - start, 500, e.getMessage());
  }

  /**
   * Advice that logs when a method is entered and exited.
   *
   * @param joinPoint join point for advice.
   * @return result.
   * @throws Throwable throws {@link IllegalArgumentException}.
   */

  @Around("applicationPackagePointcut() && springBeanPointcut()")
  @Scope(value = "session", proxyMode = ScopedProxyMode.TARGET_CLASS)
  public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
    Logger log = logger(joinPoint);
    long start = System.currentTimeMillis();
    Response response = null;
    if (joinPoint != null && joinPoint.getSignature() != null)
      log.debug("Enter: {}() with argument[s] = {}", joinPoint.getSignature()
          .getName(), Arrays.toString(joinPoint.getArgs()));
    try {

      Object result = joinPoint.proceed();
      if (result instanceof Response) {
        response = (Response) result;
      }
      if (joinPoint != null && joinPoint.getSignature() != null)
        log.debug("Exit: {}() with result = {}", joinPoint.getSignature()
            .getName(), result);
      if (response != null)
        logDetails(joinPoint, System.currentTimeMillis() - start, response.getStatusInfo()
            .getStatusCode(), response.getStatusInfo()
            .getReasonPhrase());
      return result;

    }
    catch (IllegalArgumentException e) {
      if (response != null)
        logDetails(joinPoint, System.currentTimeMillis() - start, response.getStatusInfo()
            .getStatusCode(), e.getMessage());
      log.error("Illegal argument: {} in {}()", Arrays.toString(joinPoint.getArgs()), joinPoint.getSignature()
          .getName());
      throw e;
    }
  }

}
