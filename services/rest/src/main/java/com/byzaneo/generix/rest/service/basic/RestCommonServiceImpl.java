/*
 * Copyright (c) 2023.
 * created by <PERSON><PERSON>
 */

package com.byzaneo.generix.rest.service.basic;

import static com.byzaneo.amazon.utils.AmazonArchivingHelper.getPathFromKey;
import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.bean.FileType.getType;
import static com.byzaneo.commons.dao.mongo.PageRequest.of;
import static com.byzaneo.commons.job.JobExecutionBuilder.builder;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATA_DIR;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.RECAPLIST_DIR;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.generix.bean.Message.Label.Integration;
import static com.byzaneo.generix.edocument.service.EDocumentService.EDocument.valueOfIgnoringCase;
import static com.byzaneo.generix.edocument.service.InvoiceServiceImpl.ACCEPTED_FLOW_DIRECTION_VALUES;
import static com.byzaneo.generix.edocument.service.InvoiceServiceImpl.INVOIC_TYPE;
import static com.byzaneo.generix.edocument.service.InvoiceServiceImpl.MAX_LIMIT;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.joinInvoiceNumberAndInvoiceIssueDate;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.checkCredential;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.checkTechnicalUserInfo;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.convertInputStreamToByteArray;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.getJWTUsername;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.getLocaleFromRequest;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.getResponse;
import static com.byzaneo.generix.rest.helper.RestServiceHelper.getResponseOnError;
import static com.byzaneo.generix.rest.service.basic.AsyncRestService.DELIMITER;
import static com.byzaneo.generix.rest.service.basic.AsyncRestService.RESTFOLDER;
import static com.byzaneo.generix.rest.trigger.RestTrigger.REST_TRIGGER_PARAMETERS_PREFIX;
import static com.byzaneo.generix.util.OrganizationHelper.resolveUserOrganizationsQuery;
import static com.byzaneo.generix.util.OrganizationHelper.resolveUserOrganizationsQueryWithBaseSecurityFields;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.getAttachedFiles;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.getErrorsFiles;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.getFileSize;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.getFilesByType;
import static com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper.getReportedData;
import static com.byzaneo.generix.xtrade.util.AbstractTasksHelper.isFileType;
import static com.byzaneo.generix.xtrade.util.AbstractTasksHelper.probeIsFileType;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.security.util.PrincipalHelper.getCompany;
import static com.byzaneo.xtrade.api.DocumentStage.UNDEFINED;
import static com.byzaneo.xtrade.api.DocumentStatus.NONE;
import static com.byzaneo.xtrade.bean.DocumentTimeline.TimelineAction.ATTACHFILE;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_INSTANCE;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_NAME;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_NO_FILES_TO_PROCESS;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_NO_HEADER_PARAMS_FILE;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_TIMESTAMP;
import static com.byzaneo.xtrade.service.ProcessService.PROCESS_JOB_GROUP;
import static com.byzaneo.xtrade.util.DocumentBuilder.createDocument;
import static java.lang.String.format;
import static java.lang.System.currentTimeMillis;
import static java.lang.Thread.currentThread;
import static java.util.Arrays.stream;
import static java.util.Collections.singletonList;
import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON_TYPE;
import static javax.ws.rs.core.MediaType.APPLICATION_OCTET_STREAM;
import static javax.ws.rs.core.MediaType.APPLICATION_XML;
import static javax.ws.rs.core.MediaType.TEXT_HTML;
import static javax.ws.rs.core.MediaType.TEXT_PLAIN;
import static javax.ws.rs.core.MediaType.TEXT_XML;
import static javax.ws.rs.core.Response.noContent;
import static javax.ws.rs.core.Response.ok;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.io.FileUtils.getTempDirectory;
import static org.apache.commons.io.FilenameUtils.getBaseName;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.trimToEmpty;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import java.io.File;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.*;
import java.util.stream.*;
import java.util.zip.*;

import javax.persistence.*;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.*;
import javax.ws.rs.core.Response.Status;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.*;
import org.apache.commons.io.filefilter.WildcardFileFilter;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.amazon.service.*;
import com.byzaneo.amazon.utils.AmazonArchivingHelper;
import com.byzaneo.angular.service.*;
import com.byzaneo.commons.bean.Job;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.JobDAO;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.job.JobTrigger;
import com.byzaneo.commons.job.*;
import com.byzaneo.commons.service.ExecutorService;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.portal.DocumentFileDto;
import com.byzaneo.generix.api.bean.portal.advancedrecapdaily.AdvancedRecapDailyDTO;
import com.byzaneo.generix.api.bean.portal.dematparnerfiledaily.DematPartnerFileDailyDTO;
import com.byzaneo.generix.api.util.PermissionHelper;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.demat.service.DematService;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.exception.FileAlreadyExistsException;
import com.byzaneo.generix.edocument.exception.*;
import com.byzaneo.generix.edocument.service.*;
import com.byzaneo.generix.edocument.service.EDocumentService.EDocument;
import com.byzaneo.generix.edocument.task.portal.ArchivedInvoiceTask;
import com.byzaneo.generix.edocument.util.EDocumentErrorHelper;
import com.byzaneo.generix.rest.*;
import com.byzaneo.generix.rest.converter.*;
import com.byzaneo.generix.rest.exception.DocumentNotFound;
import com.byzaneo.generix.rest.helper.*;
import com.byzaneo.generix.rest.service.jwt.RestGenericService;
import com.byzaneo.generix.rest.service.jwt.advancedrecapdaily.RestAdvancedRecapDailyServiceDelegatorImpl;
import com.byzaneo.generix.rest.service.jwt.advancedrecaplist.RestAdvancedRecapListServiceDelegatorImpl;
import com.byzaneo.generix.rest.service.jwt.dematpartnerfile.RestDematPartnerFileServiceDelegatorImpl;
import com.byzaneo.generix.rest.trigger.RestTrigger;
import com.byzaneo.generix.rtemachine.repository.VariablesOperations;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.repository.bean.aap.AapAccountingEntry;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.aap.AapService;
import com.byzaneo.generix.service.repository.util.SortOrderDto;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.generix.xcbl.portal.task.*;
import com.byzaneo.generix.xcbl.portal.task.util.InvoiceTaskHelper;
import com.byzaneo.generix.xcbl.service.StandardInvoiceEdition.StandardInvoiceEditionService;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.generix.xtrade.util.*;
import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.BqlParseException;
import com.byzaneo.query.builder.*;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.security.spring.UserDetailsService;
import com.byzaneo.security.util.PrincipalHelper;
import com.byzaneo.xtrade.api.DefinitionDescriptor;
import com.byzaneo.xtrade.api.IndexableDocument;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.Document.ProcessingWay;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.Indexable;
import com.byzaneo.xtrade.bean.Report;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.dao.mongo.MongoIndexableDocument;
import com.byzaneo.xtrade.index.MongoIndexOperations;
import com.byzaneo.xtrade.ipm.service.RestJobProcessManagement;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.util.*;
import com.byzaneo.xtrade.xcbl.api.XcblDocument;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import com.google.common.collect.ImmutableMap;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Sep 17, 2014
 * @since 2.9 GNX-1536
 */
@Service(RestCommonService.SERVICE_NAME)
public class RestCommonServiceImpl extends DistributedJobExecutor implements RestCommonService {

  private static final Logger log = getLogger(RestCommonServiceImpl.class);

  protected static final String WRONG_EXPORT_EMAIL = "/_global_/ThymeLeaf/email-wrongExport-items/email-wrongExport-items";

  protected static final String DOWNLOAD_REPORT_URI = "/_global_/ThymeLeaf/async-download-mail/async-download-mail";

  public static final String FILE_NAME_REGEX = "[a-zA-Z_\\-.0-9]*";

  protected static final String PATTERN_DATE = "YYYYMMdd";

  private static final String EXTENDED_DATE_FORMAT = ISO_8601_EXTENDED_DATE_FORMAT.getPattern();
  // - SERVICES -
  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;
  @Autowired
  @Qualifier(DocumentService.INDEX_OPERATIONS_NAME)
  MongoIndexOperations indexOperations;
  @Autowired
  private RestGenericService restGenericService;

  @Autowired
  @Qualifier(InstanceService.SERVICE_NAME)
  private InstanceService instanceService;

  @Autowired
  @Qualifier(DematService.SERVICE_NAME)
  private DematService dematService;

  @Autowired
  @Qualifier(EDocumentService.SERVICE_NAME)
  private EDocumentService eDocumentService;

  @Autowired
  @Qualifier(SafeboxService.SERVICE_NAME)
  private SafeboxService safeboxService;

  @Autowired(required = false)
  @Qualifier(AmazonS3ServiceImpl.SERVICE_NAME)
  private S3Service amazonService;

  @Autowired
  @Qualifier(RestJobProcessManagement.SERVICE_NAME)
  private RestJobProcessManagement restJobProcessManagement;

  @Autowired
  @Qualifier(AsynchronousDownloadService.SERVICE_NAME)
  private AsynchronousDownloadService asyncDownloadService;

  @Autowired
  @Qualifier(AsyncRestService.SERVICE_NAME)
  private AsyncRestService asyncRestService;

  @Autowired
  @Qualifier(RossumService.SERVICE_NAME)
  private transient RossumService rossumService;

  @Autowired(required = false)
  @Qualifier(VariablesOperations.OPERATIONS_NAME)
  private transient VariablesOperations variablesOperations;

    @Autowired
    private DocumentLockingService documentLockingService;

    @Autowired
    private PermissionHelper permissionHelper;

    protected transient ExecutorService executorService;

    @Autowired
    @Qualifier(AccountService.SERVICE_NAME)
    private AccountService accountService;

    @Autowired
    @Qualifier(DocumentTimelineService.SERVICE_NAME)
    private transient DocumentTimelineService documentTimelineService;

    @Autowired
    @Qualifier(ConfigurationService.SERVICE_NAME)
    private ConfigurationService configurationService;

    @Autowired
    @Qualifier(JobDAO.DAO_NAME)
    private JobDAO jobDAO;

    @Autowired(required = false)
    private HttpServletRequest request;

    @Autowired
    private I18NService i18NService;

  @Autowired
  StandardInvoiceEditionService standardInvoiceEditionService;

    @Autowired
    @Qualifier(DocumentViewerService.SERVICE_NAME)
    protected transient DocumentViewerService documentViewerService;

    protected transient TransformService transformService;

    protected transient MessagingService messagingService;

    @Autowired
  private InvoiceTransformService invoiceTransformService;

  @Autowired
  private ArchivedInvoiceTransformService archivedInvoiceTransformService;


  // - PROPERTIES -

    /**
     * REST triggers by instanceCode_triggerMapping
     */
    private Map<String, RestTrigger> triggers = new HashMap<>();

    /**
     * REST trigger executor activation flag
     */
    private boolean disabled = false;

    @Value("${process.node.register:}")
    private String multinode;

    @Autowired
    @Qualifier(TaskService.SERVICE_NAME)
    private TaskService taskService;

    @Autowired
    @Qualifier(WorkflowDocumentStatusService.SERVICE_NAME)
    private transient WorkflowDocumentStatusService workflowDocumentStatusService;

    @Autowired
    @Qualifier(WorkflowStepRedirectService.SERVICE_NAME)
    private transient WorkflowStepRedirectService workflowStepRedirectService;

    @Autowired
    @Qualifier(SecurityService.SERVICE_NAME)
    public transient SecurityService securityService;

    @Autowired
    @Qualifier(AapService.SERVICE_NAME)
    private AapService aapService;

    @Autowired
    private RestAdvancedRecapListServiceDelegatorImpl restAdvancedRecapListService;

  @Autowired
  private RestAdvancedRecapDailyServiceDelegatorImpl advancedRecapDailyServiceDelegator;

    @Autowired
    private RestDematPartnerFileServiceDelegatorImpl restDematPartnerFileServiceDelegator;

  public static final String USER_NOT_FOUND = "user_not_found";

  @Autowired
  private final ModelMapper modelMapper;

  public RestCommonServiceImpl(ModelMapper modelMapper) {
    this.modelMapper = modelMapper;
  }

  /*
   * -- ROOT --
   */

  /** @see RestCommonService#getRoot(HttpServletRequest) */
  @Override
  public Response getRoot(HttpServletRequest request) {
    return noContent().build();
  }

  @Override
  public Response getAsyncExportFile(HttpServletRequest request, String instanceCode, String username, String filename) {
    // check if the authenticated user is the same as the requested user
    Credential credentials = checkCredential(request, instanceCode, false);
    canAccessResource(username, credentials);
    final File file = this.asyncDownloadService.getAsyncExportFile(instanceCode, username, filename);
    if (file == null)
      throw new NotFoundException("File not found");

    return ok(file, getType(file).getDefaultMime())
        .header("Content-Disposition", "attachment; filename=" + file.getName())
        .build();
  }

  @Override
  public Response getAsyncExportFile(HttpServletRequest request, String username, String filename) {
    // check if the authenticated user is admin
    Credential credentials = checkCredential(request, null, false);
    canAccessResource(username, credentials);
    final File file = this.asyncDownloadService.getAsyncExportFile(null, username, filename);
    if (file == null)
      throw new NotFoundException("File not found");

    return ok(file, getType(file).getDefaultMime())
        .header("Content-Disposition", "attachment; filename=" + file.getName())
        .build();
  }
  /*
   * -- DEMAT --
   */

  private void canAccessResource(String username, Credential credentials) {
    ofNullable(credentials.getUser()).map(User::getLogin)
        .filter(login -> login.equals(username))
        .orElseThrow(() -> new RestSecurityException("Cannot access this page. You must be logged as '" + username + ""));
  }

  /** @see RestService#getDematInvoiceArchiveFile(HttpServletRequest, String, String, String, boolean) */
  @Override
  public Response getDematInvoiceArchiveFile(HttpServletRequest request, String instanceCode, String archiveFolder, String bql,
      boolean markAsRead) {
    log.debug("Getting invoice archive file: instance={}, arch. folder={}, query={}", instanceCode, archiveFolder, bql);

    // - security checks -
    final Credential credential = checkCredential(request, instanceCode, true);

    // - search for invoice -
    try {

      final File file = this.dematService.getInvoiceArchiveFile(
          credential.getOrganization(), bql, archiveFolder, markAsRead);
      if (file == null)
        throw new IllegalArgumentException("File not found");

      log.info("REST invoice archive file: {} (instance='{}', q='{}' and folder='{}')",
          file.getName(), instanceCode, bql, archiveFolder);

      return ok(file, getType(file).getDefaultMime())
          .header("Content-Disposition", "attachment; filename=" + file.getName())
          .build();

    }
    catch (SecurityException e) {
      log.error("REST invoice archive file error: {} (instance='{}', q='{}' and folder='{}')",
          e.getMessage(), instanceCode, bql, archiveFolder);
      throw new RestSecurityException(e.getMessage());
    }
    catch (BqlParseException | IllegalArgumentException | NoResultException | NonUniqueResultException e) {
      log.error("REST invoice archive file error: {} (instance='{}', q='{}' and folder='{}')",
          e.getMessage(), instanceCode, bql, archiveFolder);
      throw new RestException(e.getMessage());
    }
    catch (Exception e) {
      String message = getRootCauseMessage(e);
      log.error("REST invoice archive file error: {} (instance='{}', q='{}' and folder='{}')",
          message, instanceCode, bql, archiveFolder);
      throw new RestException("An unexpected error occurs: " + message);
    }
  }

  /*
   * -- XCBL --
   */

  @Override
  public Response searchEDocuments(HttpServletRequest request, String instanceCode, String eDocumentType, String bql, int page, int size) {
    log.debug("REST Searching {} eDocuments for instance {}: {} (page={}, size={})",
        eDocumentType, instanceCode, bql, page, size);

    // - sanity checks -
    if (isBlank(eDocumentType))
      throw new RestException("eDocument type is requiered");
    if (isBlank(bql))
      throw new RestException("BQL query is required");

    // - security checks -
    final Credential credential = checkCredential(request, instanceCode, true);

    // - search for invoice -
    try {
      final Query query = resolveUserOrganizationsQuery(credential.getUser(), parse(bql)).query();
      final EDocument edoc = valueOfIgnoringCase(eDocumentType);
      final PageRequest pageable = of(
          page < 0 ? 0 : page,
          size < 1 || size > MAX_LIMIT ? MAX_LIMIT : size);

      log.info("REST {} is searching {} edocuments: {} ({})", credential.getUser()
          .getLogin(), edoc, query, pageable);

      final Page<? extends XcblDocument> result = this.documentService.searchIndexables(edoc.getXcblDocumentClass(), query, pageable);
      if (result == null)
        throw new IllegalArgumentException("No result");

      log.info("REST result: {}", result);

      return ok(getGson().toJson(result)).build();

    }
    catch (SecurityException e) {
      log.error("REST eDocument search error: {} (instance='{}', q='{}', type='{}')",
          e.getMessage(), instanceCode, bql, eDocumentType);
      throw new RestSecurityException(e.getMessage());
    }
    catch (BqlParseException | IllegalArgumentException | NoResultException | NonUniqueResultException e) {
      log.error("REST eDocument search error: {} (instance='{}', q='{}', type='{}')",
          e.getMessage(), instanceCode, bql, eDocumentType);
      throw new RestException(e.getMessage());
    }
    catch (Exception e) {
      String message = getRootCauseMessage(e);
      log.error("REST eDocument search error: {} (instance='{}', q='{}', type='{}')",
          message, instanceCode, bql, eDocumentType);
      throw new RestException("An unexpected error occurs: " + message);
    }
  }


  @Override
  public Response getEDocument(HttpServletRequest request, String instanceCode, String eDocumentType, String number, String recipient,
      String format) {
    log.debug("REST getting {} eDocuments for instance {}: number={}, recipient={} (format={})",
        eDocumentType, instanceCode, number, recipient, format);

    // - sanity checks -
    if (isBlank(eDocumentType))
      throw new RestException("eDocument type is requiered");
    if (isBlank(number))
      throw new RestException("Number is required");
    // - security checks -
    final Credential credential = checkCredential(request, instanceCode, true);

    // - searching... -
    try {
      final FileType type = FileType.valueOf(format.toUpperCase());
      ArchivedInvoice archived = null;
      // log recipient if there is one
      log.info("REST {} is getting {} {} edocuments: number={}, recipient={}",
          credential, type, eDocumentType, number, recipient);

      XcblDocument xdoc;
      try {
        xdoc = searchXcblDocument(eDocumentType, number, recipient, credential.getUser());
      }
      catch (DocumentNotFound e) {
        // find in archive
        if ("invoice".equals(eDocumentType)) {
          archived = searchArchivedDocument(ArchivedInvoice.class, "invoice.invoiceNumber", number, recipient,
              credential.getUser());
          xdoc = archived.getInvoice();
        }
        else {
          throw e;
        }
      }

      log.debug("REST result: {}", xdoc);

      switch (type) {
      case JSON:
        return ok(getGson().toJson(xdoc), APPLICATION_JSON_TYPE).build();
      case PDF:
      case EXCEL:
      case HTML:
        final File file;
        if (archived != null && type.equals(PDF)) {
          file = getArchivedInvoice(archived.getArchiveUid(), archived.getEntityId()
              .toString());
          if (file == null)
            throw new NoResultException("No pdf file found in the archive");
        }
        else {
          file = this.eDocumentService.getFile(xdoc, credential.getLanguage(), credential.getInstance(), type, false);
        }

        log.info("REST eDocument file: {}", file.getName());
        return ok(file, type.getDefaultMime())
            .header("Content-Disposition",
                format("attachment; filename=%s-%s%s",
                    eDocumentType.toLowerCase(), number, type.getExtension()))
            .build();
      default:
        throw new IllegalArgumentException("Format not supported: " + type);
      }

    }
    catch (SecurityException se) {
      log(instanceCode, eDocumentType, number, recipient, se.getMessage());
      throw new RestSecurityException(se.getMessage());
    }
    catch (BqlParseException | IllegalArgumentException | NoResultException | NonUniqueResultException | DocumentNotFound de) {
      log(instanceCode, eDocumentType, number, recipient, de.getMessage());
      throw new RestException(de.getMessage());
    }
    catch (Exception e) {
      String message = getRootCauseMessage(e);
      log(instanceCode, eDocumentType, number, recipient, message);
      throw new RestException("An unexpected error occurs: " + message);
    }
  }

  /**
   * @see RestService#changeStatusEDocument(HttpServletRequest, String, java.lang.String, java.lang.String, java.lang.String,
   *      java.lang.String)
   */
  @Override
  public Response changeStatusEDocument(@Context HttpServletRequest request, String instanceCode, String eDocumentType, String number,
      String recipient, String status) {
    // - sanity checks -
    if (isBlank(eDocumentType))
      throw new RestException("eDocument type is required");
    if (isBlank(number))
      throw new RestException("Number is required");
    if (isBlank(recipient))
      throw new RestException("Recipient is required");

    // - status -
    final String targetStatus;
    if (StringUtils.isBlank(status)) {
      throw new RestException("Valid status is required");
    }
    targetStatus = status.toUpperCase();

    // - security checks -
    final Credential credential = checkCredential(request, instanceCode, true);

    // - changes the status -
    try {
      log.info("REST {} change {} edocument status: number={}, recipient={}, status={}",
          credential, eDocumentType, number, recipient, targetStatus);

      final Document document = this.eDocumentService.changeStatus(
          searchXcblDocument(eDocumentType, number, recipient, credential.getUser()), targetStatus);

      log.info("REST document result: {}", document);

      return ok().build();

    }
    catch (SecurityException exception) {
      log(instanceCode, eDocumentType, number, recipient, exception.getMessage());
      throw new RestSecurityException(exception.getMessage());
    }
    catch (BqlParseException | IllegalArgumentException | ServiceException | NoResultException | NonUniqueResultException exception) {
      log(instanceCode, eDocumentType, number, recipient, exception.getMessage());
      throw new RestException(exception.getMessage());
    }
    catch (RestException e) {
      throw e;
    }
    catch (Exception exception) {
      String message = getRootCauseMessage(exception);
      log(instanceCode, eDocumentType, number, recipient, message);
      throw new RestException("An unexpected error occurs: " + message);
    }
  }

  /*
   * -- PROCESS --
   */

  @Override
  public Response process(HttpServletRequest request, String instanceCode, String triggerMapping, InputStream inputStream,
      HttpHeaders headers, TechnicalUser technicalUser) {
    return processTriggeredAsync(request, instanceCode, triggerMapping,
        Arrays.asList(
            new DocumentFileBody(format("%s-%s.%s", triggerMapping, currentTimeMillis(), getExtensionString(headers.getMediaType())),
                inputStream,
                headers.getMediaType()
                    .toString())),
        getRequestHeaderParams(headers), technicalUser);
  }

  @Override
  public Response processMultipart(HttpServletRequest request, String instanceCode, String triggerMapping, List<Attachment> files,
      HttpHeaders headers, TechnicalUser technicalUser) {
    List<DocumentFileBody> fileBodies = ofNullable(files).orElseGet(() -> new ArrayList<>())
        .stream()
        .map(file ->
        {
          try {
            return new DocumentFileBody(file.getContentDisposition()
                .getFilename(),
                file.getDataHandler()
                    .getInputStream(),
                file.getContentType()
                    .toString());
          }
          catch (IOException ioe) {
            throw new UncheckedIOException(ioe);
          }
        })
        .collect(Collectors.toList());
    return processTriggeredAsync(request, instanceCode, triggerMapping, fileBodies, getRequestHeaderParams(headers), technicalUser);
  }

  @Override
  public Response processSynchronous(HttpServletRequest request, String instanceCode, String triggerMapping, InputStream inputStream,
      HttpHeaders headers, TechnicalUser technicalUser) {
    return processTriggered(request, instanceCode, triggerMapping,
        Arrays.asList(
            new DocumentFileBody(format("%s-%s.%s", triggerMapping, currentTimeMillis(), getExtensionString(headers.getMediaType())),
                inputStream,
                headers.getMediaType()
                    .toString())),
        getRequestHeaderParams(headers), technicalUser);
  }

  @Override
  public Response processMultipartSynchronous(HttpServletRequest request, String instanceCode, String triggerMapping,
      List<Attachment> files, HttpHeaders headers, TechnicalUser technicalUser) {
    List<DocumentFileBody> fileBodies = ofNullable(files).orElseGet(() -> new ArrayList<>())
        .stream()
        .map(file ->
        {
          try {
            return new DocumentFileBody(file.getContentDisposition()
                .getFilename(),
                file.getDataHandler()
                    .getInputStream(),
                file.getContentType()
                    .toString());
          }
          catch (IOException ioe) {
            throw new UncheckedIOException(ioe);
          }
        })
        .collect(Collectors.toList());
    return processTriggered(request, instanceCode, triggerMapping, fileBodies, getRequestHeaderParams(headers), technicalUser);
  }

  private boolean checkParams(HttpServletRequest request, String instanceCode, String triggerMapping, List<DocumentFileBody> files,
      TechnicalUser technicalUser) {
    log.info("REST process triggered ({}) for instance {} with {} document's files",
        triggerMapping, instanceCode, files == null ? 0 : files.size());

    // - sanity checks -

    if (isBlank(triggerMapping))
      throw new RestException("eDocument type is requiered");
    if (isEmpty(files))
      throw new RestException("No document's file to process");

    if (!this.triggers.containsKey(this.createTriggerKey(instanceCode, triggerMapping)))
      return false;

    // - security checks -
    if (technicalUser == null) {
      checkCredential(request, instanceCode, true);
    }
    else {
      //authenticated user should be company user
      checkTechnicalUserInfo(technicalUser, instanceCode, true);
    }
    return true;
  }

  @Override
  public boolean checkIfTriggerIsActive(String instanceCode, String triggerMapping) {
    log.info("REST process triggered ({}) for instance {}.",
        triggerMapping, instanceCode);
    if (isBlank(triggerMapping))
      return false;
    if (!triggers.keySet()
        .contains(this.createTriggerKey(instanceCode, triggerMapping)))
      return false;

    return true;
  }

  private Response processTriggeredAsync(HttpServletRequest request, String instanceCode, String triggerMapping,
      List<DocumentFileBody> files,
      Map<String, Object> headerParams, TechnicalUser technicalUser) {
    if (this.getExecutorService().isShuttingDown())
    {
      log.info("Tomcat server is shutting down, nu more async triggers processed; trigger rejected: {} for instance {} ", triggerMapping, instanceCode);
      return null;
    }
    boolean checkPrms = checkParams(request, instanceCode, triggerMapping, files, technicalUser);
    // since now a process is deployed on all nodes, if the trigger is not active on this node, it will not be active on other nodes as well
    if (checkIfTriggerIsActive(instanceCode, triggerMapping)) {
      asyncRestService.writeTriggerToFile(instanceCode, triggerMapping, files, headerParams);
    }
    else {
      log.warn("Rejecting trigger ({}) as it is not configured for the instance {}", triggerMapping, instanceCode);
      throw new RestException("No such trigger '%s'", triggerMapping);
    }
    return ok().entity("{\"message\" : \"The file has been received.\"}")
        .type(APPLICATION_JSON)
        .build();
  }

  private Response processTriggered(HttpServletRequest request, String instanceCode, String triggerMapping, List<DocumentFileBody> files,
      Map<String, Object> headerParams, TechnicalUser technicalUser) {
    boolean checkPrms = checkParams(request, instanceCode, triggerMapping, files, technicalUser);
    if (!checkPrms) {
      log.warn("Rejecting trigger ({}) as it is not configured for the instance {}", triggerMapping, instanceCode);
      throw new RestException("No such trigger '%s'", triggerMapping);
    }
    try {
      final Report report = executeProcess(instanceCode, triggerMapping, files, headerParams);

      // something went wrong...
      if (ReportHelper.isKO(report)) {
        throw new RestProcessException(report, 500);
      }

      // document successfully processed...
      return ok()
          .entity(new RestReport(report))
          .type(APPLICATION_JSON)
          .build();

    }
    catch (WebApplicationException wae) {
      throw wae;
    }
    catch (Exception e) {
      String message = getRootCauseMessage(e);
      log.error("REST triggering process error: {} {} (instance='{}', mapping='{}')",
          message, instanceCode, triggerMapping);
      throw new RestException(message);
    }
  }

  @Override
  public Report executeProcess(String instanceCode, String triggerMapping, List<DocumentFileBody> files,
      Map<String, Object> headerParams) {

    // - executes triggered process -
    final RestTrigger trigger = this.triggers.get(this.createTriggerKey(instanceCode, triggerMapping));
    if (trigger == null || trigger.isDisabled()) {
      log.error("No process trigger configured for environment '{}' and trigger's mapping '{}'",
          instanceCode, triggerMapping);
      throw new RestException("No process trigger configured for environment '%s' and trigger's mapping '%s'",
          instanceCode, triggerMapping);
    }
    // ONEDAY uses REST parameters to set document's properties
    DocumentBuilder db;
    if (CollectionUtils.isNotEmpty(files)) {
      db = createDocument()
          .status(NONE.name())
          .stage(UNDEFINED)
          .owners(instanceService.getInstanceByCode(instanceCode)
              .getGroup()
              .getCode())
          .directory(format("rest/%s/%s/%s",
              instanceCode, triggerMapping, currentTimeMillis()));

      // creates the document to process based on the attachments
      files.forEach(attachment -> {
          db.addStreamedFile(
                  ofNullable(attachment.getFilename())
                      .orElse(format("rst-%s.data", currentTimeMillis())),
                  attachment.getInputStream())
              // uses first attached file as document's reference
              .updateDocumentReference(db.document()
                  .getReference() == null)
              .resolveType()
              .actionName(triggerMapping)
              .processInstanceId("REST")
              .comment(attachment.getComment());
      });
    }
    else {
      db = null;
    }

    // executes the job's process

    try {
      JobExecutionBuilder jobExecutionBuilder = builder()
          .trigger(trigger)
          .executor(this)
          .variables(headerParams);
      if(db != null)
        jobExecutionBuilder.variable(DOCUMENTS.toString(), singletonList(db.document()));

      final Report report = (Report) trigger.getJob()
          .call(jobExecutionBuilder.build());

      return report;
    }
    catch (Exception e) {
      log.error("Cannot create report: ", e);
      throw new RestException("Cannot create report:", e);
    }

  }

  private Map<String, Object> getRequestHeaderParams(HttpHeaders headers) {
    Map<String, Object> headerParams = new HashMap<String, Object>();
    headers.getRequestHeaders()
        .entrySet()
        .forEach(entry ->
        {
          List<String> value = entry.getValue();
          headerParams.put(REST_TRIGGER_PARAMETERS_PREFIX + entry.getKey(), value.size() == 1 ? value.get(0) : value);
        });
    return headerParams;
  }

  /*
   * -- PROCESS --
   */

  /** @see RestService#search(HttpServletRequest, java.lang.String, java.lang.String, java.lang.String) */
  @Override
  public Response search(HttpServletRequest request, String instanceCode, String collectionName, String bql) {
    log.info("Search {}'s collection '{}' using query: {}", instanceCode, collectionName, bql);

    // - sanity checks -
    if (isBlank(collectionName))
      throw new RestException("Collection name is requiered");

    // - security -

    checkCredential(request, instanceCode, true);

    // - searches -
    try {
      return ok(this.documentService
          .searchIndexData(collectionName, parse(bql), Pageable.unpaged())
          .getContent()
          .stream()
          .map(MongoIndexableDocument::toJson)
          .collect(Collectors.toList())
          .toString(),
          APPLICATION_JSON_TYPE)
              .build();
    }
    catch (BqlParseException bpe) {
      String message = String.format("Wrong BQL query: '{}' ({})",
          bql, getRootCauseMessage(bpe));
      log.error(message);
      throw new RestException(message);
    }
    catch (Exception e) {
      String message = getRootCauseMessage(e);
      log.error("Error searching {}'s collection '{}' with query '{}': {}",
          instanceCode, collectionName, bql, message);
      throw new RestException(message);
    }
  }

  @Override
  public Response disableTriggers(HttpServletRequest request, InputStream inputStream) {
    return disableTriggers(null, new InputStreamReader(inputStream));
  }

  @Override
  public Response disableTriggers(HttpServletRequest request, String instanceCode, InputStream inputStream) {

    return disableTriggers(instanceCode, new InputStreamReader(inputStream));
  }

  @Override
  public Response enableTriggers(HttpServletRequest request, InputStream inputStream) {

    return enableTriggers(null, new InputStreamReader(inputStream));
  }

  @Override
  public Response enableTriggers(HttpServletRequest request, String instanceCode, InputStream inputStream) {

    return enableTriggers(instanceCode, new InputStreamReader(inputStream));
  }

  @Override
  public Response checkRunningProcess(HttpServletRequest request, InputStream inputStream) {
    TimestampedDTO timestampedDTO = GsonHelper.getGson()
        .fromJson(new InputStreamReader(inputStream), TimestampedDTO.class);
    boolean isTimestampOk = restJobProcessManagement.checkTimestamp(timestampedDTO != null ? timestampedDTO.getLongTimestamp() : -1);
    if (!isTimestampOk) {
      return Response.status(Status.UNAUTHORIZED)
          .build();
    }
    return ok(getGson().toJson(createProcessManagementDTO())).type(APPLICATION_JSON)
        .build();
  }

  @Override
  @Transactional
  /**
   *
   */
  public Response environmentsEnvCodeInvoicesGet(HttpServletRequest request, boolean fromAngular, String bql, UUID requestId, Integer limit,
      Integer offset, BaseType base, String flowDirection, String sortBy, com.byzaneo.generix.api.bean.SortOrderDto order, boolean count, String portletId,
      String localeAsString, boolean isCountEnabled, String uuid, User userN, TechnicalUser technicalUser) {
    if (requestId == null) {
      requestId = UUID.randomUUID();
    }
    if (limit == null)
      limit = -1;
    if (offset == null)
      offset = 0;
    log.debug("Searching {} eDocuments : {} (page={}, size={})",
        INVOIC_TYPE, bql, offset, limit);
    // check required fields
    if (!fromAngular && isBlank(bql)) {
      return getResponseOnError("400", "Bad query: Bql is required.", Status.BAD_REQUEST, requestId);
    }

    if (!(flowDirection == null || flowDirection.isEmpty() || ACCEPTED_FLOW_DIRECTION_VALUES.contains(flowDirection))) {
      return getResponseOnError("400", "Flow direction is invalid. Available values: SENDING, RECEIVING",
          Status.BAD_REQUEST, requestId);
    }

    if (base == null)
      base = BaseType.ACTIVE;

    // - search for invoice -
    try {
      // get companyCode and perimeter for users
      final OrganizationHelper.UserOrganizations userOrgs = userN != null
          ? OrganizationHelper.resolveUserOrganizations(userN)
          : OrganizationHelper.resolveTechnicalUserOrganizations(technicalUser);
      String companyCode = userOrgs.getCompanyCode();
      Collection<String> perimeter = userOrgs.getCodes();
      InvoiceIndex invoiceIndex;
      if (isNotBlank(uuid)) {
        invoiceIndex = restGenericService.getInvoiceIndexByBql(uuid, request, userOrgs.getCompanyCode(), userOrgs.getCodes());
        if (ProcessingWay.SENDING.equals(invoiceIndex.getProcessingWay())) {
          perimeter = List.of(invoiceIndex.getTo());
        }
        else if (ProcessingWay.RECEIVING.equals(invoiceIndex.getProcessingWay())) {
          perimeter = List.of(invoiceIndex.getFrom());
        }
      }
      if (base == BaseType.ARCHIVE && !fromAngular) {
        bql = "invoice." + bql;
      }
      QueryBuilder qbuilder = resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql),
          AbstractTask.FIELD_OWNERS,
          AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, AbstractTask.FIELD_THIRD_PARTY, flowDirection, sortBy,
          order != null ? order.name() : SortOrderDto.ASC.name());
      boolean isWorkflowMonitoringTask = false;
      Object task = null;
      Locale locale = null;
      Instance instance = null;
      String instanceCode = null;
      if (request != null && request.getServerName() != null) {
        instance = getInstance(request);
        instanceCode = instance.getCode();
        List<User> currentUserAndBackedupUsersByCurrentUser = new ArrayList<>();
        if (userN != null)
          currentUserAndBackedupUsersByCurrentUser.add(userN);

        //applied just for a normal user
        if (userN != null && localeAsString != null && portletId != null) {
          localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
          locale = new Locale(localeAsString);
          task = taskService.getTask(Long.valueOf(portletId), locale, instanceCode);
          if (task instanceof InvoiceTask) {
            String finalFlowDirection = flowDirection;
            if( ((InvoiceTask) task).getPortletFlowDirection() != null){
              finalFlowDirection = ((InvoiceTask) task).getPortletFlowDirection();
            }
            qbuilder = resolveUserOrganizationsQueryWithBaseSecurityFields(companyCode, perimeter, parse(bql),
                AbstractTask.FIELD_OWNERS,((InvoiceTask) task).getSelectedSecurityFields(), sortBy,
                order != null ? order.name() : SortOrderDto.ASC.name());
            log.debug("query generated with security fields  {}",qbuilder.query().toString());
            if (((InvoiceTask) task).isShowOnlyWorkflow()) {

              qbuilder.and(Clauses.in(Indexable.ENTITY_ID,
                  Stream.concat(workflowDocumentStatusService.getDocumentsOfConcernedUsers(currentUserAndBackedupUsersByCurrentUser)
                              .stream(),
                          workflowStepRedirectService.getInvoicesForRedirectedUser(userN.getId())
                              .stream())
                      .collect(toList())));
            }
            // replace the old  qbuilder value to handle the PortletFlowDirection in invoice case
          }
          else if (task instanceof ArchivedInvoiceTask) {
            log.debug("query generated with security fields  {}", qbuilder.query()
                .toString());
            boolean isArchivingConfigurationEnabled = ((ArchivedInvoiceTask) task).isArchivingConfigurationValid();
            if(!isArchivingConfigurationEnabled){
              return Response.serverError()
                      .entity("archive_error_missing_archiving_configuration")
                      .header("Request-Id", requestId.toString())
                      .build();
            }
            if (sortBy != null) {
              String finalSortBy = sortBy;
              PropertyDescriptor propertyDescriptor = ((ArchivedInvoiceTask) task).getDescriptor()
                  .getProperties()
                  .stream()
                  .filter(o -> o.getName()
                      .contains(finalSortBy))
                  .findFirst()
                  .orElse(null);
              if (propertyDescriptor != null) {
                sortBy = propertyDescriptor.getName();
              }
            }
            List<String> securityFields = ((ArchivedInvoiceTask) task).getSelectedSecurityFields()
                .stream()
                .map(s -> "invoice.".concat(s))
                .collect(toList());
            bql = RestServiceHelper.prepareBqlForSql(bql, (ArchivedInvoiceTask)task);
            qbuilder = resolveUserOrganizationsQueryWithBaseSecurityFields(companyCode, perimeter, parse(bql),
                AbstractTask.FIELD_OWNERS, securityFields, sortBy,
                order != null ? order.name() : SortOrderDto.ASC.name());
          }
          else if (task instanceof WorkflowMonitoringTask) {
            isWorkflowMonitoringTask = true;
          }
        }
      }
        if (userN != null)
          aapService.addUserAndRoleInvoiceBqlQueryToTheSearch(qbuilder, userN);
        else
          qbuilder.append(securityService.createQueryFromTechnicalUserRole(technicalUser));

        final Query qb = qbuilder.query();
        final EDocument edoc = valueOfIgnoringCase(INVOIC_TYPE);

        final PageRequest pageable = of(
            offset < 0 ? 0 : offset,
            limit < 1 || limit > MAX_LIMIT ? MAX_LIMIT : limit);
        pageable.setCount(isCountEnabled);
        pageable.setFromAngular(true);

        log.info("RequestId {} : API {} is searching {} edocuments: {} ({})", requestId, userN != null ? userN
            .getLogin() : technicalUser.getClientId(), edoc, qb, pageable);

        List result;
        long totalElements = 0;
        long totalPage = 0;
        if (count == true) {
          long totalCount;
          if (base == BaseType.ACTIVE) {
            totalCount = this.documentService.countIndexable(edoc.getXcblDocumentClass(), qb);
          }
          else {
            totalCount = this.eDocumentService.countArchiveInvoices(qb);
          }
          return ok(getGson().toJson(totalCount)).header("Request-Id", requestId.toString())
              .build();
        }
        else {
          if (base == BaseType.ACTIVE) {
            log.debug("Searching for invoice with query {}",qb.toString());
            Page<InvoiceIndex> page = (Page<InvoiceIndex>) this.documentService.searchIndexables(edoc.getXcblDocumentClass(), qb, pageable);
            result = page.getContent();
            List<InvoiceIndex> invs = page.getContent();
            totalElements = page.getTotalElements();
            totalPage = page.getTotalPages();
          }
          else {
            Page<ArchivedInvoice> page = (Page<ArchivedInvoice>) this.eDocumentService.searchArchives(ArchivedInvoice.class, qb, pageable);
            if (!fromAngular && base != BaseType.ARCHIVE) {
              result = page.getContent()
                  .stream()
                  .map(ArchivedInvoice::getInvoice)
                  .collect(toList());
            }
            else {
              result = (List<ArchivedInvoice>) page.getContent();
            }
            totalElements = page.getTotalElements();
            totalPage = page.getTotalPages();
          }
          if (fromAngular && (result == null || result.isEmpty()) && !isWorkflowMonitoringTask) {
            //return getResponseOnError("404", "No result.", Status.NOT_FOUND, requestId);
            if (base == BaseType.ACTIVE) {
              return ok(getGson().toJson(
                  getInvoiceList(locale, instanceCode, task, userOrgs, result, limit, offset, totalElements, totalPage, portletId,
                      fromAngular, requestId.toString()))).header(
                      "Request-Id",
                      requestId.toString())
                  .build();
            }else{
              return ok(getGson().toJson(
                  getArchivedInvoiceList(instanceCode, result, limit, offset, totalElements, totalPage, portletId, fromAngular,
                      localeAsString))).header(
                      "Request-Id",
                      requestId.toString())
                  .build();
            }

          }
          else if (fromAngular && (result == null || result.isEmpty()) && isWorkflowMonitoringTask) {
            return ok(getGson().toJson(
                getWorkflowMonitoringList((WorkflowMonitoringTask) task, locale, instanceCode, userN, result, limit, offset, totalElements,
                    totalPage, portletId, fromAngular, localeAsString))).header(
                    "Request-Id",
                    requestId.toString())
                .build();
          }
          if (result == null || result.isEmpty()) {
            return getResponseOnError("404", "No result.", Status.NOT_FOUND, requestId);
          }

          if (!fromAngular)
            log.debug("API result: {}", result);
          if (isWorkflowMonitoringTask) {
            return ok(getGson().toJson(
                getWorkflowMonitoringList((WorkflowMonitoringTask) task, locale, instanceCode, userN, result, limit, offset, totalElements,
                    totalPage, portletId, fromAngular, localeAsString))).header(
                    "Request-Id", requestId.toString())
                .build();
          }
          else {
            if (base == BaseType.ACTIVE) {
              return ok(getGson().toJson(
                  getInvoiceList(locale, instanceCode, task, userOrgs, result, limit, offset, totalElements, totalPage,
                      portletId, fromAngular, requestId.toString()))).header(
                      "Request-Id", requestId.toString())
                  .build();
            }
            else {
              return ok(getGson().toJson(
                  getArchivedInvoiceList(instanceCode, result, limit, offset, totalElements, totalPage, portletId, fromAngular,
                      localeAsString))).header(
                      "Request-Id", requestId.toString())
                  .build();
            }

                }

}

        } catch (SecurityException e) {
            log.error("API eDocument search error: {} (q='{}', type='{}')",
                    e.getMessage(), bql, INVOIC_TYPE);
            throw new RestSecurityException(e.getMessage());
        } catch (BqlParseException | IllegalArgumentException | NoResultException | NonUniqueResultException e) {
            log.error("API eDocument search error: {} (q='{}', type='{}')",
                    e.getMessage(), bql, INVOIC_TYPE);
            throw new RestException(e.getMessage());
        } catch (Exception e) {
            String message = getRootCauseMessage(e);
            log.error("API eDocument search error: {} ( q='{}', type='{}')",
                    message, bql, INVOIC_TYPE);
            throw new RestException("An unexpected error occurs: " + message);
        }
    }

  /*
   * -- JOB EXECUTOR --
   */

  /** @see com.byzaneo.commons.job.JobExecutor#getName() */
  @Override
  public String getName() {
    return SERVICE_NAME;
  }

  /** @see com.byzaneo.commons.job.JobExecutor#accept(com.byzaneo.commons.job.JobTrigger) */
  @Override
  public boolean accept(JobTrigger jobTrigger) {
    Job job;
    return jobTrigger != null &&
        jobTrigger.getTrigger() != null &&
        (job = (Job) jobTrigger.getJob()) != null &&
        PROCESS_JOB_GROUP.equals(job.getGroup()) &&
        isNotEmpty(job.getArguments()) &&
        job.getArguments()[0] instanceof DefinitionDescriptor &&
        accept(jobTrigger.getTrigger()
            .getClass());
  }

  /** @see com.byzaneo.commons.job.JobExecutor#accept(java.lang.Class) */
  @Override
  public boolean accept(Class<? extends Trigger> triggerType) {
    return triggerType != null &&
        RestTrigger.class.isAssignableFrom(triggerType);
  }

  @Override
  public void processTrigger(JobTrigger jobTrigger) throws JobException {
    // gets process properties
    final RestTrigger trigger = jobTrigger.<RestTrigger> getTrigger();
    if (trigger.getJob() == null)
      trigger.setJob(jobTrigger.getJob());

    this.triggers.put(this.createTriggerKey(jobTrigger), trigger);
  }

  private String createTriggerKey(JobTrigger jobTrigger) {
    return this.createTriggerKey(
        ((DefinitionDescriptor) ((Job) jobTrigger.getJob()).getArguments()[0]).getOwner(),
        jobTrigger.<RestTrigger> getTrigger()
            .getMapping());
  }

  private String createTriggerKey(String instanceCode, String triggerMapping) {
    return instanceCode
        .concat("_")
        .concat(triggerMapping);
  }

  /** @see com.byzaneo.commons.job.JobExecutor#revoke(com.byzaneo.commons.job.JobTrigger) */
  @Override
  public void revokeTrigger(JobTrigger jobTrigger) throws JobException {
    if (jobTrigger == null) {
      log.debug("Cannot remove null jobTrigger");
      return;
    }
    this.triggers.remove(this.createTriggerKey(jobTrigger));
  }

  /** @see com.byzaneo.commons.job.JobExecutor#manage(com.byzaneo.commons.job.JobTrigger) */
  @Override
  public boolean manage(JobTrigger jobTrigger) throws JobException {
    return accept(jobTrigger) &&
        this.triggers.containsKey(this.createTriggerKey(jobTrigger));
  }

  /** @see com.byzaneo.commons.job.JobExecutor#nextExecutionTime(com.byzaneo.commons.job.JobTrigger) */
  @Override
  public Date nextExecutionTime(JobTrigger jobTrigger) {
    return nextExecutionTime(null, jobTrigger);
  }

  /** @see com.byzaneo.commons.job.JobExecutor#nextExecutionTime(java.util.Date, com.byzaneo.commons.job.JobTrigger) */
  @Override
  public Date nextExecutionTime(Date fromDate, JobTrigger jobTrigger) {
    return null;
  }

  /** @see com.byzaneo.commons.job.JobExecutor#pause() */
  @Override
  public void pause() throws JobExecutorException, UnsupportedOperationException {
    this.stop();
  }

  /** @see com.byzaneo.commons.job.JobExecutor#resume() */
  @Override
  public void resume() throws JobExecutorException, UnsupportedOperationException {
    this.start();
  }

  /** @see com.byzaneo.commons.job.JobExecutor#stop() */
  @Override
  public void stop() throws JobExecutorException, UnsupportedOperationException {
    this.disabled = true;
  }

  /** @see com.byzaneo.commons.job.JobExecutor#start() */
  @Override
  public void start() throws JobExecutorException, UnsupportedOperationException {
    this.disabled = false;
  }

  /** @see com.byzaneo.commons.job.JobExecutor#shutdown(boolean) */
  @Override
  public void shutdown(boolean force) throws JobExecutorException, UnsupportedOperationException {
    this.stop();
  }

  /** @see com.byzaneo.commons.job.JobExecutor#isPaused() */
  @Override
  public boolean isPaused() throws JobExecutorException, UnsupportedOperationException {
    return disabled;
  }

  /** @see com.byzaneo.commons.job.JobExecutor#isStarted() */
  @Override
  public boolean isStarted() throws JobExecutorException, UnsupportedOperationException {
    return !disabled;
  }

  /** @see com.byzaneo.commons.job.JobExecutor#isShutdown() */
  @Override
  public boolean isShutdown() throws JobExecutorException, UnsupportedOperationException {
    return !disabled;
  }

  @Scheduled(cron = "0 0/1 * * * *")
  public void scheduledProcessTriggeredFromFolder() {
    if (this.getExecutorService().isShuttingDown()){
      log.info("Tomcat server is shutting down, no async rest triggers will be processed.");
      return;
    }
    File folder = new File(getConfigurationService().getFile(DATA_DIR, getTempDirectory()), RESTFOLDER);
    String[] restTriggerFiles = folder.list();
    if (restTriggerFiles == null) {
      return;
    }
    for (String filename : restTriggerFiles) {
      processTriggeredFromFolder(folder, filename);
    }
  }
  /*
   * -- PRIVATE --
   */

  private void processTriggeredFromFolder(File folder, String filename) {
    File restTriggerFile = new File(folder, filename);
    if (!restTriggerFile.exists() || !documentLockingService.tryLock(restTriggerFile.getAbsolutePath())) {
      return;
    }
    List<DocumentFileBody> files = new ArrayList<DocumentFileBody>();
    try {
      String[] splitFolderName = restTriggerFile.getName()
          .split(DELIMITER);
      String instanceCode = splitFolderName[1];
      String triggerMapping = splitFolderName[2];
      // since now a process is deployed on all nodes, if the trigger is not active on this node, it will not be active on other nodes as well
      if (checkIfTriggerIsActive(instanceCode, triggerMapping)) {
        Map<String, Object> headerParams = asyncRestService.readFromFolder(folder, filename, files);
        if(headerParams == null){
          headerParams = new HashMap<>();
          headerParams.put(String.valueOf(REST_TRIGGER_NO_HEADER_PARAMS_FILE), true);
          headerParams.put(String.valueOf(REST_TRIGGER_NAME), triggerMapping);
          headerParams.put(String.valueOf(REST_TRIGGER_TIMESTAMP), splitFolderName[0]);
          headerParams.put(String.valueOf(REST_TRIGGER_INSTANCE), instanceCode);
          if (CollectionUtils.isEmpty(files))
            headerParams.put(String.valueOf(REST_TRIGGER_NO_FILES_TO_PROCESS), true);
        }
        Report repo = executeProcess(instanceCode, triggerMapping, files, headerParams);
        if (repo != null){
          //if a shutting down occurred we get a null Report, and we do not delete the files from disk, so another node can pick them up and execute => no data loss
          FileUtils.forceDelete(new File(getConfigurationService().getFile(DATA_DIR, getTempDirectory()),
              RESTFOLDER + File.separator + filename));
        }
      }
      else
        log.error("There is no such trigger '{}'", triggerMapping);
    }
    catch (Exception e) {
      throw new RestException("Something went wrong when launching async the process: ", e);
    }
    finally {
      // if the files weren't deleted we must close the existing inputStreams
      files.forEach(attachment -> {
        IOUtils.closeQuietly(attachment.getInputStream());
      });
      documentLockingService.unlock(restTriggerFile.getAbsolutePath());
    }
  }

  XcblDocument searchXcblDocument(String eDocumentType, String number, String recipient, final User user) {
    final EDocument edoc = valueOfIgnoringCase(eDocumentType);
    return this.documentService.searchIndexables(
            edoc.getXcblDocumentClass(),
            resolveUserOrganizationsQuery(
                user,
                createBuilder().and(
                        equal(edoc.getNumberPath(), number),
                        ofNullable(recipient)
                            .map(r -> equal("to", recipient))
                            .orElse(null))
                    .query())
                .query(),
            // the same document is returned in case of multiple result
            // it should be the latest created
            of(0, 1, DESC, "id"))
        .getContent()
        .stream()
        .findFirst()
        .orElseThrow(() -> new DocumentNotFound("Document not found"));
  }

  protected <A extends Archive> A searchArchivedDocument(Class<A> archivedType, String numberPath, String number, String recipient,
      final User user) {
    // final EDocument edoc = valueOfIgnoringCase(eDocumentType);

    return this.eDocumentService.searchArchives(
        archivedType,
        resolveUserOrganizationsQuery(
            user,
            createBuilder().and(
                equal(numberPath, number),
                ofNullable(recipient)
                    .map(r -> equal("to", recipient))
                    .orElse(null))
                .query())
                    .query(),
        // the same document is returned in case of multiple result
        // it should be the latest created
        of(0, 1, DESC, "id"))
        .getContent()
        .stream()
        .findFirst()
        .orElseThrow(() -> new DocumentNotFound("Document not found"));
  }

  protected File getArchivedInvoice(String archiveUid, String entityFolder) throws IOException, ArchiveDownloadFailedException {
    Path archiveZipPath = Paths.get(safeboxService.getSafeboxRetrievePath(), getPathFromKey(archiveUid));
    if (amazonService == null || !amazonService.isS3ArchiveServiceAvailable()) {
      throw new DocumentNotFound("Document not found");
    }
    amazonService.downloadArchive(archiveUid, archiveZipPath.toString());
    if (archiveZipPath.toFile()
        .exists() &&
        archiveZipPath.toFile()
            .isFile()) {
      Path unzippedFolder = AmazonArchivingHelper.unzipArchiveZip(archiveZipPath);
      // Looking for the file
      Path folder = unzippedFolder.resolve(entityFolder);
      Collection<File> pdfFiles = FileUtils.listFiles(folder.toFile(), new WildcardFileFilter("*.pdf"), null);
      if (pdfFiles.isEmpty())
        return null;

      return pdfFiles.iterator()
          .next();
    }
    throw new ArchiveDownloadFailedException("Failed to download archive %s", archiveUid);
  }

  private void log(final String instanceCode, final String eDocumentType,
      final String number, final String recipient, final String message) {
    log.error("REST {} error: {} (instance='{}', number='{}', type='{}', recipient='{}')",
        currentThread().getStackTrace()[2].getMethodName(),
        message, instanceCode, number,
        eDocumentType, trimToEmpty(recipient));
  }

  /*
   * -- TESTING --
   */

  public void setEDocumentService(EDocumentService eDocumentService) {
    this.eDocumentService = eDocumentService;
  }

  /**
   * Based on the request body input stream check timestamp and enable the list of triggers provided
   *
   * @param instanceCode null for all instances
   * @param requestInput
   * @return
   */
  private Response enableTriggers(String instanceCode, Reader requestInput) {
    TriggersDTD triggersDTD = getGson()
        .fromJson(requestInput, TriggersDTD.class);
    boolean isTimestampOk = restJobProcessManagement.checkTimestamp(triggersDTD != null ? triggersDTD.getLongTimestamp() : -1);
    if (!isTimestampOk) {
      return Response.status(Status.UNAUTHORIZED)
          .build();
    }
    try {
      List<String> enableTriggers = restJobProcessManagement.enableTriggers(instanceCode, triggersDTD.getTriggerIdList());
      return ok(getGson().toJson(enableTriggers)).type(APPLICATION_JSON)
          .build();
    }
    catch (Exception e) {
      return Response.status(Status.INTERNAL_SERVER_ERROR)
          .entity("An error occured while enabling the triggers, for more informations check the server logs")
          .build();
    }
  }

  /**
   * Based on the request body input stream check timestamp and disable all the triggers of the current node for the given instance code
   *
   * @param instanceCode null for all instances
   * @param requestInput
   * @return
   */
  private Response disableTriggers(String instanceCode, Reader requestInput) {
    TimestampedDTO timestampedData = getGson()
        .fromJson(requestInput, TimestampedDTO.class);
    boolean isTimestampOk = restJobProcessManagement.checkTimestamp(timestampedData != null ? timestampedData.getLongTimestamp() : -1);
    if (!isTimestampOk) {
      return Response.status(Status.UNAUTHORIZED)
          .build();
    }
    try {
      List<String> disabledTriggers = restJobProcessManagement.disableTriggers(instanceCode);
      return ok(getGson().toJson(disabledTriggers)).type(APPLICATION_JSON)
          .build();
    }
    catch (Exception e) {
      return Response.status(Status.INTERNAL_SERVER_ERROR)
          .entity("An error occured while disabling the triggers, for more information check the server logs")
          .build();
    }
  }

  public ArchivedInvoiceListDTO getArchivedInvoiceList(String instanceCode, List<ArchivedInvoice> inv, Integer limit, Integer offset,
      long totalElements, long totalPages,
      String portletId, boolean fromAngular, String localAsString) {
    ArchivedInvoiceListDTO invList = new ArchivedInvoiceListDTO();
    Locale local = new Locale(isNotBlank(localAsString) ? localAsString : "en");
    try {
      List<ArchivedInvoiceDTO> invDTO = List.copyOf(inv)
          .stream()
          .map(i -> {
            return fromAngular ? archivedInvoiceTransformService.transformToDTO(i, local,instanceCode): RestServiceHelper.transformToArchiveInvoiceDtoForRest(i, fromAngular, local, instanceCode);
          })
          .collect(toList());
      invList.setContent(invDTO);
      invList.setTotal(inv.size());
    }
    catch (Exception e) {
      e.printStackTrace();
    }
    InvoiceListPageableDTO invListPageable = new InvoiceListPageableDTO();
    invListPageable.setLimit(limit);
    invListPageable.setOffset(offset);
    invList.setPageable(invListPageable);

    invList.setTotalElements(totalElements);
    invList.setTotalPages(totalPages);
    invList.setPortletId(portletId);

    return invList;
  }

  @Override
  public InvoiceListDTO transformInvoiceList(Locale local, String instanceCode,
      List<InvoiceIndex> inv, Integer limit, Integer offset, Page<InvoiceIndex> page) {
    long totalElements = page.getTotalElements();
    long totalPages = page.getTotalPages();
    InvoiceListDTO invList = new InvoiceListDTO();
    List<InvoiceDTO> invDTO = List.copyOf(inv)
        .stream()
        .map(i -> {
          return invoiceTransformService.transformToDTO(i, local, instanceCode);
        })
        .collect(toList());

    invList.setContent(invDTO);
    invList.setTotal(inv.size());

    InvoiceListPageableDTO invListPageable = new InvoiceListPageableDTO();
    invListPageable.setLimit(limit);
    invListPageable.setOffset(offset);
    invList.setPageable(invListPageable);

    invList.setTotalElements(totalElements);
    invList.setTotalPages(totalPages);

    return invList;
  }

  @Deprecated
  /** should be removed after old invocie portlet is removed and use
   * {@link com.byzaneo.generix.rest.service.basic.RestCommonServiceImpl#transformInvoiceList(Locale, String, List, Integer, Integer, long, long, String, String)}
   **/
  public InvoiceListDTO getInvoiceList(Locale local, String instanceCode, Object task, OrganizationHelper.UserOrganizations userOrgs,
      List<InvoiceIndex> inv, Integer limit, Integer offset, long totalElements, long totalPages, String portletId, boolean fromAngular,
      String requestId) {
    if (task instanceof InvoiceTask) {
      // retrieve rossumId from xcbl invoice if it is not already set (it could be the case for older invoices)
      setRossumIdIfOCRVerifiable((InvoiceTask) task, userOrgs, inv, requestId);
    }
    InvoiceListDTO invList = new InvoiceListDTO();
    List<InvoiceDTO> invDTO = List.copyOf(inv)
        .stream()
        .map(i -> {
          return fromAngular ? invoiceTransformService.transformToDTO(i,local, instanceCode):RestServiceHelper.transformToInvoiceDtoForRest(i, fromAngular, local, instanceCode);
        })
        .collect(toList());

    invList.setContent(invDTO);
    invList.setTotal(inv.size());

    InvoiceListPageableDTO invListPageable = new InvoiceListPageableDTO();
    invListPageable.setLimit(limit);
    invListPageable.setOffset(offset);
    invList.setPageable(invListPageable);

    invList.setTotalElements(totalElements);
    invList.setTotalPages(totalPages);
    invList.setPortletId(portletId);

    return invList;
  }

  @Deprecated
  /**   should be removed after old invoice portlet is removed, you can use
   {@link InvoiceService#setRossumIdIfOCRVerifiable(OrganizationHelper.UserOrganizations, List, String)}
   **/
  private void setRossumIdIfOCRVerifiable(InvoiceTask invoiceTask, OrganizationHelper.UserOrganizations userOrgs, List<InvoiceIndex> inv,
      String requestId) {
    if (!rossumService.isRossumServiceActive() || !variablesOperations.hasAuthenticationChannelAccessTokenV1ForUse(AccessTokenV1Use.ROSSUM,
        userOrgs.getPartner(), userOrgs.getCompany())) {
      return;
    }

    for (InvoiceIndex invoiceIndex : inv) {
      try {
        String rossumId = invoiceTask.getRossumIdFromXcblIfOCRVerifiable(invoiceIndex);
        if (rossumId != null) {
          invoiceIndex.setRossumId(rossumId);
        }
      }
      catch (Exception ex) {
        log.warn("RequestId {} : Can't retrieve Rossum ID for invoice uuid {} : {}", requestId, invoiceIndex.getUuid(), ex.getMessage());
      }
    }
  }

  public InvoiceListDTO getWorkflowMonitoringList(final WorkflowMonitoringTask workflowMonitoringTask, Locale local, String instanceCode,
      User user, List<InvoiceIndex> inv, Integer limit, Integer offset, long totalElements, long totalPages,
      String portletId, boolean fromAngular, String localAsString) {
    InvoiceListDTO invList = new InvoiceListDTO();


    WorkflowMonitoringTask finalWorkflowMonitoringTask = workflowMonitoringTask;
    List<InvoiceDTO> invDTO = List.copyOf(inv)
            .stream()
            .map(i -> {
              if (finalWorkflowMonitoringTask != null) {
                Document document = restGenericService.getDocumentsByBql(i.getUuid(), user);
                i.setHaveAttachReadableFile(this.eDocumentService.getAttachedFilesReadable(document) > 0);
              }
              return invoiceTransformService.transformToDTO(i, local, instanceCode);
            })
            .collect(Collectors.toList());

    invList.setContent(invDTO);
    invList.setTotal(inv.size());

    InvoiceListPageableDTO invListPageable = new InvoiceListPageableDTO();
    invListPageable.setLimit(limit);
    invListPageable.setOffset(offset);
    invList.setPageable(invListPageable);

    invList.setTotalElements(totalElements);
    invList.setTotalPages(totalPages);
    invList.setPortletId(portletId);

    return invList;
  }

  public String getInstanceCodeFromRequest(HttpServletRequest request) {
    if (request == null) return null;
    Instance instance = getInstance(request);
    return instance != null ? instance.getCode() : null;
  }

  public Instance getInstance(HttpServletRequest request) {
    Host host = this.instanceService.resolveHost(request.getServerName());
    if (Objects.isNull(host)) throw new RestJwtException("Host not found", ExceptionType.NOT_FOUND);
    Instance instance = this.instanceService.resolveInstance(host);
    return instance;
  }

  /**
   * Returns the extension of the file depending of the content-type
   *
   * @param contentType
   * @return
   */
  private String getExtensionString(MediaType contentType) {
    switch (contentType.toString()) {
    case APPLICATION_JSON:
      return "json";
    case TEXT_XML:
    case APPLICATION_XML:
      return "xml";
    case APPLICATION_OCTET_STREAM:
      return "data";
    case TEXT_PLAIN:
      return "txt";
    case TEXT_HTML:
      return "html";
    default:
      throw new IllegalArgumentException("Unhandled Content-Type");
    }
  }

  @Override
  public void sendKeyByMail(String downloadUrl, Instance instance, User user) {
    try {
      final Map<String, Object> variables = ImmutableMap.<String, Object> builder()
          .put("hasProcessSucceed", StringUtils.isNotEmpty(downloadUrl))
          .put("downloadLinkUrl", downloadUrl)
          .put("user", user)
          .build();

      final Template template = getTransformService().getTemplate(DOWNLOAD_REPORT_URI);
      final MessagingService.Notification notif = new MessagingService.Notification(user, instance);

      getMessagingService().notifyAsync(Message.class, Integration.toString(),
          instance.getConfiguration()
              .getFromAddress(),
          template, null, null, null, variables, notif);

    }
    catch (ServiceException e) {
      log.error("Error during sending mail ", e.getMessage());
    }
  }

  @Override
  public void sendMailForWrongExport(Instance instance, User user) {
    try {

      final Map<String, Object> variables = ImmutableMap.<String, Object> builder()
          .put("user", user)
          .put("fromAddress", instance.getConfiguration()
              .getFromAddress())
          .build();

      final Template template = getTransformService().getTemplate(WRONG_EXPORT_EMAIL);
      final MessagingService.Notification notif = new MessagingService.Notification(user, instance);
      getMessagingService().notifyAsync(Message.class, Integration.toString(),
          instance.getConfiguration()
              .getFromAddress(),
          template, null, null, null, variables, notif);

    }
    catch (ServiceException e) {
      log.error("Error during sending mail ", e.getMessage());
    }
  }

  public File asynchronousDownload(int listExportRangeMax, int documentExportRangeMax, Function<Document, String> vBaseName, Class clazz, FileType fileType, Instance instance,
      int archiveItemsCount, int asynchronousLimit,
      String archiveFileName,
      BeanDescriptor desc,
      Query searchQuery, String sortField,
      Sort.Direction sortOrder, User sessionUser, boolean isCompanyUser, HttpServletRequest request, String instanceCode, String url,
      boolean allIndexesSelected, boolean exportArchive, String legalEntity) {
    if (isBlank(instance.getConfiguration()
        .getFromAddress())) {

      throw new RestJwtException(
          i18NService.findByCodeAndLocale("environnement_from_adress_missing", getLocaleFromRequest(request)),
          ExceptionType.NOT_FOUND);
    }

        // Create zip file and then create the aws directory and send a mail
        CompletableFuture
                .supplyAsync(
                        () -> processingDownloadFile(listExportRangeMax, documentExportRangeMax, vBaseName, clazz, fileType, archiveItemsCount, archiveFileName, desc, searchQuery, sortField,
                                sortOrder,
                                sessionUser.getLocale(),
                                sessionUser.getDatePattern(),
                                instance, isCompanyUser, null, sessionUser, request, instanceCode, allIndexesSelected, exportArchive, legalEntity),
                        getExecutorService().getExecutor())
                .exceptionally((e) ->
                {
                    log.error("Error during asynchronous export {}", e.getMessage());
                    sendMailForWrongExport(instance, sessionUser);
                    return null;
                })
                .thenAccept(file ->
                {
                    log.info("End of the archive export generation");
                    if (file == null) {
                        throw new ServiceException();
                    } else {
                        sendKeyByMail(creatArchiveInTemporaryAsyncFolder(file, instance, sessionUser, url), instance, sessionUser);
                    }
                });
        return null;
    }

  private MessagingService getMessagingService() {
    if (this.messagingService == null) {
      this.messagingService = getBean(MessagingService.class, MessagingService.SERVICE_NAME);
    }
    return this.messagingService;
  }

  private TransformService getTransformService() {
    if (this.transformService == null) {
      this.transformService = getBean(TransformService.class, TransformService.SERVICE_NAME);
    }
    return this.transformService;
  }

  private ExecutorService getExecutorService() {
    if (this.executorService == null) {
      this.executorService = getBean(ExecutorService.class, ExecutorService.SERVICE_NAME);
    }
    return this.executorService;
  }

    public File processingDownloadFile(
            int listExportRangeMax, int documentExportRangeMax,
            Function<Document, String> vBaseName,
            Class clazz,
            FileType fileType,
            int count,
            String fileName,
            BeanDescriptor desc,
            Query searchQuery,
            String sortField,
            Sort.Direction sortOrder,
            Locale userLocale,
            String userDateFormat,
            Instance instance,
            boolean isCompanyUser,
            List<Document> documentsToUpdate,
            User user,
            HttpServletRequest request,
            String instanceCode,
            boolean allIndexesSelected,
            boolean exportArchive, String legalEntity) throws ServiceException {

    List<IndexableDocument> items = new ArrayList<>();
    List<IndexableDocument> indexes = new ArrayList<>();
    Group group = getCompany(user);

        try {
            // This will be updated during the refactoring process for the export functionality.
      if (clazz.equals(RecapListItem.class) && ("PDF").equals(fileType.name()) && exportArchive) {

        return restAdvancedRecapListService.processingDownloadFile(fileType, count, fileName,
                searchQuery, sortField, sortOrder, userLocale,
                instanceCode, legalEntity);
      }
      else if (clazz.equals(DailyRecapListItem.class) && exportArchive) {
        indexes = searchIndexables(clazz, searchQuery, sortField, sortOrder, documentExportRangeMax, listExportRangeMax, request,
                exportArchive);
        return advancedRecapDailyServiceDelegator.prepareDownloadAdvancedRecapDailyMultipleXMLs(fileType,indexes,group,userLocale);
      }
      else  if (clazz.equals(Exchange.class) && ("CSV").equals(fileType.name()) && exportArchive) {

        return restDematPartnerFileServiceDelegator.processingDownloadFile(fileType, count, fileName,
            searchQuery, sortField, sortOrder, userLocale,
            instanceCode, legalEntity,desc);
      }
      else if (clazz.equals(ArchivedInvoice.class) || exportArchive) {
                int limit = exportArchive ? documentExportRangeMax : listExportRangeMax;
                PageRequest pageable = createPageRequest(limit, sortField, sortOrder);

        if (clazz.equals(ArchivedInvoice.class)) {
          Page<IndexableDocument> page = eDocumentService.searchArchives(clazz, searchQuery, pageable);
          indexes = page.getContent();
        } else {
          indexes = searchIndexables(clazz, searchQuery, sortField, sortOrder, documentExportRangeMax, listExportRangeMax, request, exportArchive);
        }

        if (!indexes.isEmpty() && !exportArchive) {
          return prepareDownloadExcel(fileName, desc, userLocale, userDateFormat, indexes, instanceCode);
        }
        else {
          IndexableDocument[] maxDocToExport = resolveSelectedIndexables(documentExportRangeMax, items, indexes, allIndexesSelected);
          DocumentFileViewer dfv = prepareDownloadArchive(vBaseName, fileType, fileName, userLocale, instance,
              documentsToUpdate, maxDocToExport, user, desc);

          if (dfv != null && dfv.getFile() != null) {
            return dfv.getFile();
          }
        }
      }
      else if (clazz.equals(AapAccountingEntry.class)) {
        return prepareDownloadExcel(fileName, desc, userLocale, userDateFormat,
            aapService.getAccountingEntries(searchQuery, PageRequest.of(0, Integer.MAX_VALUE, Sort.by(sortOrder, sortField)))
                .getContent(),
            instanceCode);
      }
      else {
        indexes = searchIndexables(clazz, searchQuery, sortField, sortOrder, documentExportRangeMax, listExportRangeMax, request,
            exportArchive);
        return prepareDownloadExcel(fileName, desc, userLocale, userDateFormat, indexes, instanceCode);
      }
        }
        catch (TemplateMissingException tme) {
          log.error(tme.getMessage());
        }
        catch (Exception e) {
          throw new ServiceException(e);
        }

    return null;
  }


  private PageRequest createPageRequest(int limit, String sortField, Sort.Direction sortOrder) {
    if (isNotBlank(sortField)) {
      return PageRequest.of(0, limit, sortOrder, sortField);
    } else {
      return PageRequest.of(0, limit);
    }
  }

  public File processingDownloadFileAgreement(Class claszz, String fileName, BeanDescriptor desc, Query searchQuery, Locale userLocale,
      String userDateFormat, HttpServletRequest request, String instanceCode, String sortField, Sort.Direction sortOrder, int max,
      boolean exportArchive) throws ServiceException {
    List<IndexableDocument> indexes = new ArrayList<>();
    try {
      indexes = this.searchIndexables(claszz, searchQuery, sortField, sortOrder, 0, max, request, exportArchive);
      File tmpFile = prepareDownloadExcel(fileName, desc, userLocale, userDateFormat, indexes, instanceCode);
      return tmpFile;

    }
    catch (TemplateMissingException tme) {
      log.error(tme.getMessage());
    }
    catch (Exception e) {
      throw new ServiceException(e);
    }
    return null;
  }


  private static final  SimpleDateFormat yyyyMMFormatter = new SimpleDateFormat("yyyyMM");
  private static final  SimpleDateFormat yyyyMMddFormatter = new SimpleDateFormat("yyyyMMdd");

  public File buildXmlFilePath(Object item, ConfigurationService.ConfigurationKey directoryType) {
    File baseDir = getConfigurationService().getFile(directoryType, getTempDirectory());
    String directoryPath = baseDir + File.separator + getLegalEntity(item) + File.separator;
    File directory = new File(directoryPath);
    if (!directory.exists()) {
      directory.mkdirs();
    }
    return directory;
  }

  public File buildXmlFilePath(Object item, File directory, int index, String filePrefix) {
    String dateFormatted = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
    String xmlFileNameBuilder = filePrefix +
        "_" +
        getUuid(item) +
        "_" +
        dateFormatted +
        "_" +
        index +
        ".xml";
    return new File(directory, xmlFileNameBuilder);
  }

  public File prepareDownloadMultipleXMLs(String zipFileName, List<?> indexesList, Group group,
      ConfigurationService.ConfigurationKey directoryType, String filePrefix) throws IOException, ArchiveDownloadFailedException {
    if (indexesList == null || indexesList.isEmpty()) {
      throw new IllegalArgumentException("Indexes list cannot be null or empty.");
    }
    File zipFile = new File(FileUtils.getTempDirectory(), zipFileName + ".zip");
    try (FileOutputStream fos = new FileOutputStream(zipFile);
         ZipOutputStream zos = new ZipOutputStream(fos)) {
      for (int i = 0; i < indexesList.size(); i++) {
        Object dto = mapToDTO(indexesList.get(i), directoryType);
        File directory = buildXmlFilePath(dto, directoryType);
        File xmlFile = buildXmlFilePath(dto, directory, i, filePrefix);
        Path s3FilePath = Path.of(getS3FilePath(indexesList.get(i), group, directoryType));
        Files.copy(s3FilePath, Path.of(xmlFile.getPath()), StandardCopyOption.REPLACE_EXISTING);
        addFileToZip(xmlFile, zos);
        if (!xmlFile.delete()) {
          System.err.println("Failed to delete temporary file: " + xmlFile.getAbsolutePath());
        }
      }
    }
    return zipFile;
  }

  private String getLegalEntity(Object item) {
    if (item instanceof AdvancedRecapDailyDTO) {
      return ((AdvancedRecapDailyDTO) item).getLegalEntity();
    }
    else if (item instanceof DematPartnerFileDailyDTO) {
      return ((DematPartnerFileDailyDTO) item).getLegalEntity();
    }
    return "Unknown";
  }

  private String getUuid(Object item) {
    if (item instanceof AdvancedRecapDailyDTO) {
      return ((AdvancedRecapDailyDTO) item).getUuid();
    }
    else if (item instanceof DematPartnerFileDailyDTO) {
      return ((DematPartnerFileDailyDTO) item).getUuid();
    }
    return "Unknown";
  }

  private Object mapToDTO(Object item, ConfigurationService.ConfigurationKey directoryType) {
    if (item == null || directoryType == null) {
      throw new IllegalArgumentException("Item and directoryType must not be null.");
    }

    return directoryType.equals(RECAPLIST_DIR)
        ? modelMapper.map(item, AdvancedRecapDailyDTO.class)
        : modelMapper.map(item, DematPartnerFileDailyDTO.class);
  }

  private String getS3FilePath(Object item, Group group, ConfigurationService.ConfigurationKey directoryType)
      throws ArchiveDownloadFailedException, IOException {
    if (directoryType.equals(RECAPLIST_DIR)) {
      return safeboxService.getDailyRecapFromS3((DailyRecapListItem) item, group)
          .getPath();
    }
    else {
      return safeboxService.getDailyDematFromS3((DailyExchangeListItem) item, group)
          .getPath();
    }
  }
  /**
   * Helper method to add a file to the ZIP output stream.
   */
  private void addFileToZip(File file, ZipOutputStream zos) throws IOException {
    try (FileInputStream fis = new FileInputStream(file)) {
      ZipEntry zipEntry = new ZipEntry(file.getName());
      zos.putNextEntry(zipEntry);

      byte[] buffer = new byte[1024];
      int length;
      while ((length = fis.read(buffer)) >= 0) {
        zos.write(buffer, 0, length);
      }

      zos.closeEntry();
    }
  }

  public List<IndexableDocument> searchIndexables(Class claszz, Query searchQuery, String sortField, Sort.Direction sortOrder, int docCount,
      int listCount, HttpServletRequest request, boolean exportArchive) {
    // geting the list only in cases where it's used
    int limit = exportArchive ? docCount : listCount;
    org.springframework.data.domain.PageRequest pageable = isNotBlank(sortField)
        ? org.springframework.data.domain.PageRequest.of(0, limit, sortOrder, sortField)
        : org.springframework.data.domain.PageRequest.of(0, limit);
    return searchIndexables(claszz, searchQuery, pageable);
  }

  @Override
  public List<IndexableDocument> searchIndexables(Class claszz, Query searchQuery, org.springframework.data.domain.PageRequest pageable) {
    return new ArrayList<>(this.documentService.searchIndexables(claszz, searchQuery, pageable)
        .getContent());
  }

  public DocumentFileViewer prepareDownloadArchive(Function<Document, String> vBaseName, FileType fileType, String fileName,
      Locale userLocale, Instance instance,
      List<Document> documentsToUpdate, IndexableDocument[] maxDocToExport, User user, BeanDescriptor desc) {
    Partner partner = null;
    if (user.getPrimaryGroup() instanceof Partner) {
      partner = (Partner) user.getPrimaryGroup();
    }
    DocumentFileViewer dfv = this.documentViewerService.download(maxDocToExport,
            instance,
            partner,
            userLocale,
            fileName,
            vBaseName, false, false, null, documentsToUpdate,
            stream(TransformService.BirtOutoutFileType.values()).filter(f -> f.getFileType()
                    .equals(fileType))
                .findFirst()
                .get())
        .orElse(null);
    return dfv;
  }

  private IndexableDocument[] resolveSelectedIndexables(int docCount, List<IndexableDocument> items, List<IndexableDocument> indexes,
      boolean allIndexesSelected) {
    IndexableDocument[] maxDocToExport;
    if (allIndexesSelected) {
      maxDocToExport = indexes.toArray(new IndexableDocument[indexes.size()]);
    }
    else {
      if (indexes.size() > docCount) {
        indexes.forEach(indexdoc -> {
          items.add(indexdoc);
        });
        maxDocToExport = items.toArray(new IndexableDocument[docCount]);
      }
      else {
        maxDocToExport = indexes.toArray(new IndexableDocument[indexes.size()]);
      }
    }
    return maxDocToExport;
  }

  protected String creatArchiveInTemporaryAsyncFolder(File file, Instance instance, User sessionUser, String url) {
    return asyncDownloadService.createEndpointInAsyncFolder(file, instance, sessionUser, url);
  }

  public static File prepareDownloadExcel(String fileName, BeanDescriptor desc, Locale userLocale, String userDateFormat,
                                          List<?> indexes, String instanceCode) {
    // Filename xls.
    String xlsFileName = RestServiceHelper.ensureXlsExtension(fileName);

    // Create file in temporary directory.
    File tmpFile = new File(FileUtils.getTempDirectory(), xlsFileName);

    // Write the content in the file.
    ExcelExportHelper.exportAsXls(indexes, desc, tmpFile, userLocale, userDateFormat, xlsFileName, instanceCode);

    return tmpFile;
  }

  @Transactional
  public Optional<DocumentViewer> downloadPdf(Instance instance, InvoiceIndex indexable, TransformService.BirtOutoutFileType type,
      Partner partner, User user, Function<Document, String> vBaseName) {
    final Optional<DocumentViewer> view = this.documentViewerService.view(
        indexable, instance, partner,
        user
            .getLocale(),
        vBaseName, false, false, null, type);
    // document not found
    if (!view.isPresent()) {
      throw new RestJwtException("Host not found", ExceptionType.NOT_FOUND);
    }

    if (!view.map(DocumentViewer::isValid)
        .get()) {
      return empty();
    }
    return view;
  }

  public XcblDocument searchXcblDocument(String eDocumentType, UUID invoiceUid, final User user, final TechnicalUser technicalUser) {
    final OrganizationHelper.UserOrganizations userOrgs = user != null
        ? OrganizationHelper.resolveUserOrganizations(user)
        : OrganizationHelper.resolveTechnicalUserOrganizations(technicalUser);
    String companyCode = userOrgs.getCompanyCode();
    Collection<String> perimeter = userOrgs.getCodes();
    final EDocument edoc = valueOfIgnoringCase(eDocumentType);
    final QueryBuilder qb = createBuilder().and(equal("uuid", invoiceUid.toString()));
    if (user != null)
      aapService.addUserAndRoleInvoiceBqlQueryToTheSearch(qb, user);
    else
      qb.append(securityService.createQueryFromTechnicalUserRole(technicalUser));
    final Query query = resolveUserOrganizationsQuery(companyCode, perimeter, qb.query(), "owners",
        "from", "to", "thirdParty")
        .query();

    return this.documentService.searchIndexable(
        edoc.getXcblDocumentClass(), query);
  }

  public XcblDocument searchXcblDocumentBasedOnUUID(String eDocumentType, UUID invoiceUid, User userN, TechnicalUser technicalUser) {
    final EDocument edoc = valueOfIgnoringCase(eDocumentType);
    final QueryBuilder qb = createBuilder().and(equal("uuid", invoiceUid.toString()));
    if (userN != null)
      aapService.addUserAndRoleInvoiceBqlQueryToTheSearch(qb, userN);
    else
      qb.append(securityService.createQueryFromTechnicalUserRole(technicalUser));

    return this.documentService.searchIndexable(
        edoc.getXcblDocumentClass(), qb.query());
  }

  public void addUserAndRoleInvoiceBqlQueryToTheSearch(QueryBuilder qb, User user) {
    qb.append(securityService.createQueryFromUserRoles(user));
    qb.append(user.getBqlInvQuery());
  }

  @Transactional
  public Response attachFile(HttpServletRequest request, Attachment file, String localeAsString, boolean fromAngular, UUID requestId,
      String comment, User userN, TechnicalUser technicalUser, UUID uuid, boolean clientReadable, Long portletId) {
    InputStream fileInputStream = null;
    String fileName = file.getContentDisposition()
        .getFilename();
    int length = fileName.length();

    Locale locale = Locale.ENGLISH;
    if (fromAngular) {
      localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
      locale = new Locale(localeAsString);
    }
    if (!(AcceptedFilesExtension.getAcceptedFilesExtensions().contains(fileName.subSequence(length - 4, length)
        .toString()
        .toLowerCase()) ||
        AcceptedFilesExtension.getAcceptedFilesExtensions().contains(fileName.subSequence(length - 5, length)
            .toString()
            .toLowerCase()))) {
      if (fromAngular) {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_type_unaccepted", "", locale);
        throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
      }
      log.error("The file type is not accepted.");
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);
    }

    try {
      fileInputStream = file.getDataHandler()
          .getInputStream();
    }
    catch (IOException e) {
      throw new RuntimeException(e);
    }
    if (fromAngular && fileInputStream == null) {
      String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_no_file", "", locale);
      throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
    }
    else if (!fromAngular && fileInputStream == null) {
      log.error("The file is not valid.");
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);
    }
    String contentLengthHeader = request.getHeader("Content-Length");
    long size = Long.parseLong(contentLengthHeader);
    byte[] byteArray;
    try {
      byteArray = convertInputStreamToByteArray(fileInputStream);
    }
    catch (IOException e) {
      if (fromAngular)
        throw new RestJwtException(e.getMessage(), ExceptionType.BAD_REQUEST);
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);

    }
    Document document = null;
    InvoiceIndex invoice = null;
    try {
      XcblDocument xcblBasedOnUUID = searchXcblDocumentBasedOnUUID(INVOIC_TYPE, uuid, userN, technicalUser);
      XcblDocument xdoc = searchXcblDocument(INVOIC_TYPE, uuid, userN, technicalUser);
      if (!fromAngular && xcblBasedOnUUID != null && xdoc == null) {
        log.error("The user doesn't have rights to access the document.");
        return getResponseOnError(String.valueOf(Status.FORBIDDEN.getStatusCode()), "Forbidden.",
            Status.FORBIDDEN, requestId);
      }
      else if (!fromAngular && xcblBasedOnUUID == null) {
        log.error("Invoice not found for given UUID.");
        return getResponseOnError(String.valueOf(Status.NOT_FOUND.getStatusCode()), "Resource Not Found.",
            Status.NOT_FOUND, requestId);
      }
      document = documentService.getDocument(xdoc);
      invoice = (InvoiceIndex) xdoc;

      if (!getBaseName(fileName).matches(FILE_NAME_REGEX)) {
        if (fromAngular) {
          String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_incorrect_file_name", "", locale);
          throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
        }
        log.error("The file name is not correct.");
        return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
            Status.BAD_REQUEST, requestId);
      }
      // if the user tries to make readable a file which is not PDF type
      else if (clientReadable && stream(PDF.getMimes())
          .noneMatch(mime -> mime.equals(file.getContentType()
              .toString()))) {
        return getResponseOnError("BAD_PARAMETER", "The clientReadable parameter is only usable for filetype PDF",
            Status.BAD_REQUEST, requestId);
      }
      else if (size < InvoiceTask.MAX_FILE_SIZE_BYTES) {
        if (clientReadable) {
          this.eDocumentService.updateAttachedFilesReadableByAddFileToInvoice(document, fileName);
        }
        this.eDocumentService.attachFileToInvoice(invoice,
            new InMemoryFile(fileName, byteArray),
            comment, clientReadable ? ResultingFileAction.CLIENT_READABLE.getName() : ResultingFileAction.ADD_FILE_TO_INVOICE.getName());
        if (userN != null)
          documentTimelineService.saveActionInTimeline(document, userN,
              ATTACHFILE, comment, -1);
        else
          documentTimelineService.saveTechnicalUserActionInTimeline(document, technicalUser,
              ATTACHFILE, comment, -1);
        // override the old attached files with actionName clientReadable and make them addFileToInvoice
        if (clientReadable) {
          this.eDocumentService.updateAttachedFilesReadableByAddFileToInvoice(document, fileName);
        }
        String messageSuccess = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_successfully_added", "", locale);
        if (fromAngular)
          return ok(messageSuccess)
              .build();
        return getResponse(Status.NO_CONTENT, "No Content - No content expected.", requestId);

      }
      else {
        if (fromAngular) {
          String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "labelValue", "", locale, InvoiceTask.MAX_FILE_SIZE_MB);

          throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
        }
        log.error("The file size is too big.");
        return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
            Status.BAD_REQUEST, requestId);
      }
    }
    catch (RestJwtException e) {
      if (fromAngular) {
        ExceptionType exceptionType = ExceptionType.fromResponseStatus(e.getException()
            .getStatus());
        throw new RestJwtException(e.getMessage(), exceptionType);
      }
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);
    }

    catch (FileAlreadyExistsException e) {
      if (fromAngular) {
        String messageError = MessageHelper.getMessage("edctsklbls" + "." + "att_attachment_error_joining_filealreadyexists", "", locale);
        throw new RestJwtException(messageError, ExceptionType.ALREADY_EXIST);
      }
      log.error("This file is already attached.");
      return getResponseOnError(String.valueOf(Status.CONFLICT.getStatusCode()), "Conflict.",
          Status.CONFLICT, requestId);
    }
    catch (final Exception e) {
      if (fromAngular) {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_joining", "", locale,
            invoice.getInvoiceNumber());

        throw new RestJwtException(messageError, ExceptionType.INTERNAL_ERROR);
      }
      return getResponseOnError(String.valueOf(Status.INTERNAL_SERVER_ERROR.getStatusCode()), "Server Internal Error..",
          Status.INTERNAL_SERVER_ERROR, requestId);
    }
  }

  public void deleteReport(String uuid) {
    org.springframework.data.mongodb.core.query.Query query = query(
        where("_uuid").is(uuid));
    indexOperations.remove(query, PeriodicReport.class);
  }

  @Transactional
  public Response attachFileToIndexable(HttpServletRequest request, Attachment file, String localeAsString, boolean fromAngular,
      UUID requestId,
      String comment, User userN, TechnicalUser technicalUser, UUID uuid, Long portletId, String entityId) {
    InputStream fileInputStream = null;
    String fileName = file.getContentDisposition()
        .getFilename();
    int length = fileName.length();

    Locale locale = Locale.ENGLISH;
    if (fromAngular) {
      localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
      locale = new Locale(localeAsString);
    }
    if (!(AcceptedFilesExtension.getAcceptedFilesExtensions().contains(fileName.subSequence(length - 4, length)
        .toString()
        .toLowerCase()) ||
        AcceptedFilesExtension.getAcceptedFilesExtensions().contains(fileName.subSequence(length - 5, length)
            .toString()
            .toLowerCase()))) {
      if (fromAngular) {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_type_unaccepted", "", locale);
        deleteReport(uuid.toString());
        throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
      }
      log.error("The file type is not accepted.");
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);
    }

    try {
      fileInputStream = file.getDataHandler()
          .getInputStream();
    }
    catch (IOException e) {
      throw new RuntimeException(e);
    }
    if (fromAngular && fileInputStream == null) {
      String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_no_file", "", locale);
      throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
    }
    else if (!fromAngular && fileInputStream == null) {
      log.error("The file is not valid.");
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);
    }
    String contentLengthHeader = request.getHeader("Content-Length");
    long size = Long.parseLong(contentLengthHeader);
    byte[] byteArray;
    try {
      byteArray = convertInputStreamToByteArray(fileInputStream);
    }
    catch (IOException e) {
      if (fromAngular)
        throw new RestJwtException(e.getMessage(), ExceptionType.BAD_REQUEST);
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);

    }
    if (!getBaseName(fileName).matches(FILE_NAME_REGEX)) {
      if (fromAngular) {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_incorrect_file_name", "", locale);
        throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
      }
      log.error("The file name is not correct.");
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);
    }
    try {
      if (getBaseName(fileName).length() > 250) {
        if (fromAngular) {
          String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_incorrect_file_name", "", locale);
          deleteReport(uuid.toString());
          throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
        }
        log.error("The file name is not correct.");
        return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
            Status.BAD_REQUEST, requestId);
      }
      // if the user tries to make readable a file which is not PDF type

      else if (size < PeriodicReport.MAX_FILE_SIZE_BYTES) {
        File f = this.eDocumentService.attachFileToIndexable(entityId, entityId,
            new InMemoryFile(fileName, byteArray),
            comment, null);
        String messageSuccess = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_successfully_added", "", locale);
        if (fromAngular)
          return ok(f)
              .build();
        return getResponse(Status.NO_CONTENT, "No Content - No content expected.", requestId);

      }
      else {
        if (fromAngular) {
          String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "max_size_allowed_is_50MB", "", locale,
              PeriodicReport.MAX_FILE_SIZE_BYTES);

          throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
        }
        log.error("Maximum size allowed : 50Mo");
        deleteReport(uuid.toString());
        return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Maximum size allowed : 50Mo",
            Status.BAD_REQUEST, requestId);
      }
    }
    catch (RestJwtException e) {
      if (fromAngular) {
        ExceptionType exceptionType = ExceptionType.fromResponseStatus(e.getException()
            .getStatus());
        throw new RestJwtException(e.getMessage(), exceptionType);
      }
      return getResponseOnError(String.valueOf(Status.BAD_REQUEST.getStatusCode()), "Bad Request.",
          Status.BAD_REQUEST, requestId);
    }

    catch (FileAlreadyExistsException e) {
      if (fromAngular) {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "att_attachment_error_joining_filealreadyexists_report", "",
            locale);
        deleteReport(uuid.toString());
        throw new RestJwtException(messageError, ExceptionType.ALREADY_EXIST);
      }
      log.error("This file is already attached.");
      return getResponseOnError(String.valueOf(Status.CONFLICT.getStatusCode()), "Conflict.",
          Status.CONFLICT, requestId);
    }
    catch (final Exception e) {
      if (fromAngular) {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_joining", "", locale);
        deleteReport(uuid.toString());
        throw new RestJwtException(messageError, ExceptionType.INTERNAL_ERROR);
      }
      return getResponseOnError(String.valueOf(Status.INTERNAL_SERVER_ERROR.getStatusCode()), "Server Internal Error..",
          Status.INTERNAL_SERVER_ERROR, requestId);
    }
  }

  @Override
  @Transactional
  public File attachFileToInvoice(HttpServletRequest request, Attachment file, String localeAsString, boolean fromAngular, UUID requestId,
      String comment, User user, TechnicalUser technicalUser, UUID uuid) {

    if (requestId == null)
      requestId = UUID.randomUUID();
    InputStream fileInputStream = null;
    String fileName = file.getContentDisposition()
        .getFilename();
    int length = fileName.length();

    Locale locale = Locale.ENGLISH;
    if (fromAngular) {
      localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
      locale = new Locale(localeAsString);
    }
    if (!(AcceptedFilesExtension.getAcceptedFilesExtensions().contains(fileName.subSequence(length - 4, length)
        .toString()
        .toLowerCase()) ||
        AcceptedFilesExtension.getAcceptedFilesExtensions().contains(fileName.subSequence(length - 5, length)
            .toString()
            .toLowerCase()))) {
      String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_type_unaccepted", "", locale);
      throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
    }

    try {
      fileInputStream = file.getDataHandler()
          .getInputStream();
    }
    catch (IOException e) {
      throw new RuntimeException(e);
    }
    if (fileInputStream == null) {
      String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_no_file", "", locale);
      throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
    }
    String contentLengthHeader = request.getHeader("Content-Length");
    long size = Long.parseLong(contentLengthHeader);
    byte[] byteArray;
    try {
      byteArray = convertInputStreamToByteArray(fileInputStream);
    }
    catch (IOException e) {
      throw new RestJwtException(e.getMessage(), ExceptionType.BAD_REQUEST);
    }
    Document document = null;
    InvoiceIndex invoice = null;
    try {
      XcblDocument xcblBasedOnUUID = searchXcblDocumentBasedOnUUID(INVOIC_TYPE, uuid, user, technicalUser);
      XcblDocument xdoc = searchXcblDocument(INVOIC_TYPE, uuid, user, technicalUser);
      document = documentService.getDocument(xdoc);
      invoice = (InvoiceIndex) xdoc;
      if (xcblBasedOnUUID == null) {
        throw new RestJwtException("Resource Not Found.", ExceptionType.NOT_FOUND);
      }
      if (!getBaseName(fileName).matches(FILE_NAME_REGEX)) {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_incorrect_file_name", "", locale);
        throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
      }
      else if (size < InvoiceTask.MAX_FILE_SIZE_BYTES) {
        File targetFile = this.eDocumentService.attachFileToInvoice(invoice,
            new InMemoryFile(fileName, byteArray),
            comment, ResultingFileAction.ADD_FILE_TO_INVOICE.getName());
        documentTimelineService.saveActionInTimeline(document, user,
            ATTACHFILE, comment, -1);
        String messageSuccess = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_successfully_added", "", locale);
        return targetFile;

      }
      else {
        String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "labelValue", "", locale, InvoiceTask.MAX_FILE_SIZE_MB);
        throw new RestJwtException(messageError, ExceptionType.BAD_REQUEST);
      }
    }
    catch (RestJwtException e) {
      ExceptionType exceptionType = ExceptionType.fromResponseStatus(e.getException()
          .getStatus());
      throw new RestJwtException(e.getMessage(), exceptionType);
    }

    catch (FileAlreadyExistsException e) {
      String messageError = MessageHelper.getMessage("edctsklbls" + "." + "att_attachment_error_joining_filealreadyexists", "", locale);
      throw new RestJwtException(messageError, ExceptionType.INTERNAL_ERROR);
    }
    catch (final Exception e) {
      String messageError = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "attachment_error_joining", "", locale,
          invoice.getInvoiceNumber());

      throw new RestJwtException(messageError, ExceptionType.INTERNAL_ERROR);

    }
  }

  @Override
  @Transactional(readOnly = true)
  public File getAttachmentsZipFile(User user, String uuid, Boolean relatedFiles, String locale, boolean fromLifeCycle) {
    String nomFichier = "";

    Set<File> files = new HashSet<>();
    File zipFile = null;
    if (!relatedFiles) {
      if (Locale.ENGLISH.getLanguage()
          .equals(locale)) {
        nomFichier = "attachments_" + uuid + ".zip";
      }
      else {
        nomFichier = "piècesAttachées_" + uuid + ".zip";
      }
    }
    else {
      if (Locale.ENGLISH.getLanguage()
          .equals(locale)) {
        nomFichier = "relatedFiles_" + uuid + ".zip";
      }
      else {
        nomFichier = "fichiersAssociés_" + uuid + ".zip";
      }
    }
    zipFile = new File(FileUtils.getTempDirectory(), nomFichier);
    Document document = restGenericService.getDocumentsByBql(uuid, user);
    if (!relatedFiles) {
      files.addAll(getAttachedFiles(document)
          .stream()
          .map(DocumentFile::getFile)
          .filter(Objects::nonNull)
          .collect(Collectors.toList()));
    }
    else {
      files.addAll(InvoiceTaskHelper.getRelatedFiles(document, fromLifeCycle)
          .stream()
          .map(DocumentFile::getFile)
          .filter(Objects::nonNull)
          .collect(Collectors.toList()));
      List<File> errorFiles = DocumentPdfHelper.getDocumentFiles(document)
          .stream()
          .filter(documentFile -> documentFile.getType()
              .equals(FileType.ERROR))
          .map(documentFile -> documentFile.getFile())
          .collect(toList());
      if (!errorFiles.isEmpty()) {
        files.addAll(errorFiles);
      }
      File errorFile = createErrorFileFromDbErrors(document);
      if (errorFile != null) {
        files.add(errorFile);
      }
    }

    try (FileOutputStream zipOut = new FileOutputStream(zipFile)) {
      if (files.size() > 0) {
        com.byzaneo.commons.util.FileHelper.zip(files, zipOut);
      }
    }
    catch (IOException e) {
      throw new RestJwtException("Cannot create zipfile: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }

    return zipFile;
  }

  @Override
  @Transactional(readOnly = true)
  public Map<String, List<DocumentFileDto>> getAllAttachmentsFilesWithType(User user, String uuid) {
    Function<Document, String> documentVBaseName = viewerBaseNameForDocument(
        getLabel("gnxxcblcomlbls", "readable_export_file_name", "", user.getLocale()));
    Map<String, List<DocumentFileDto>> result = new HashMap<>();

    Document document = restGenericService.getDocumentsByBql(uuid, user);
    // Get files for all types in one pass using streams
    Map<String, List<DocumentFile>> filesByType = getFilesByType(document);
    List<DocumentFile> errorFiles = getAllErrorFiles(document);
    String vBaseName = documentVBaseName.apply(document)
        .concat("-");

    // Map the files to DTOs
    for (Map.Entry<String, List<DocumentFile>> entry : filesByType.entrySet()) {
      List<DocumentFileDto> fileDtos = mapFilesToDtos(entry.getValue(), vBaseName, true);
      result.put(entry.getKey(), fileDtos);

      // Add error files to the map
      List<DocumentFileDto> errorFileDtos = mapFilesToDtos(errorFiles, vBaseName, true);
      result.put("errorFiles", errorFileDtos);
    }
    return result;
  }

  @Override
  public Response getAttachmentsFilesWithType(HttpServletRequest request, String uuid, AttachedFileType attachedFileType,
      boolean fromLifeCycle) {
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Document document = restGenericService.getDocumentsByBql(uuid, user);
    List<DocumentFile> files = new ArrayList<>();
    List<DocumentFileDto> documentFileDtos = null;
    Function<Document, String> vBaseName = viewerBaseNameForDocument(
        getLabel("gnxxcblcomlbls", "readable_export_file_name", "", user.getLocale()));

    switch (attachedFileType) {
    case ATTACHED_FILE:
      files = getAttachedFiles(document);
      break;
    case REPORTED_DATA:
      files = getReportedData(document);
      break;
    case RELATED_FILES:
      files = InvoiceTaskHelper.getRelatedFiles(document, fromLifeCycle);
      break;
    case ERROR_FILES:
      files = getAllErrorFiles(document);
      break;
    case TAX_ORIGINAL:
      files = InvoiceTaskHelper.getTaxOriginalFiles(document);
      break;
    }
    Map<String, String> baseNameFilesMapping = new HashMap<>();
    files.forEach(documentFile -> {
      String filename = documentFile.getFile()
          .getName();
      String fileNameWithVBaseName = vBaseName.apply(document)
          .concat("-")
          .concat(filename);
      baseNameFilesMapping.put(documentFile.getFile()
          .getName(), fileNameWithVBaseName);
    });
    documentFileDtos = RestServiceHelper.mapList(files, DocumentFileDto.class);
    int index = 0;
    for (DocumentFileDto documentFileDto : documentFileDtos) {
      documentFileDto.setFileName(baseNameFilesMapping.get(documentFileDto.getFile()
          .getName()));
      documentFileDto.setFileSize(getFileSize(files.get(index)));
      Optional<DocumentFile> file = files
          .stream()
          .filter(tempFile -> tempFile.getUri()
              .equals(documentFileDto.getUri()))
          .findFirst();
      visualiseAs(documentFileDto, file);
      index++;
    }
    return RestServiceHelper.getResponse(Response.Status.OK, documentFileDtos);
  }

  @Override
  public List<DocumentFileDto> mapFilesToDtos(List<DocumentFile> files, String vbaseName, boolean visualise) {
    List<DocumentFileDto> documentFileDtos = RestServiceHelper.mapList(files, DocumentFileDto.class);
    int index = 0;
    for (DocumentFileDto documentFileDto : documentFileDtos) {
      documentFileDto.setFileName(vbaseName.concat(documentFileDto.getFile()
          .getName()));
      documentFileDto.setFileSize(getFileSize(files.get(index)));
      Optional<DocumentFile> file = files.stream()
          .filter(tempFile -> tempFile.getUri()
              .equals(documentFileDto.getUri()))
          .findFirst();
      if (visualise) {
        visualiseAs(documentFileDto, file);
      }

      index++;
    }
    return documentFileDtos;
  }

  private static void visualiseAs(DocumentFileDto documentFileDto, Optional<DocumentFile> file) {
    if ((isFileType(file.get()
        .getType(), "json") && !file.get()
        .getType()
        .name()
        .equals(FileType.ERROR.name())) || (file.get()
        .getType()
        .equals(FileType.UNKNOWN.name()) && probeIsFileType(file.get(), "json"))) {
      documentFileDto.setVisualiseAs("json");
    }
    else if (isFileType(file.get()
        .getType(), "xml") || (file.get()
        .getType()
        .name()
        .equals(FileType.UNKNOWN.name()) && probeIsFileType(file.get(), "xml"))) {
      documentFileDto.setVisualiseAs("xml");
    }
    else if (isFileType(file.get()
        .getType(), "pdf")) {
      documentFileDto.setVisualiseAs("pdf");
    }
    else if (isFileType(file.get()
        .getType(), "image") || (file.get()
        .getType()
        .name()
        .equals(FileType.UNKNOWN.name()) && probeIsFileType(file.get(), "image"))) {
      documentFileDto.setVisualiseAs("image");
    }
    else if (isFileType(file.get()
        .getType(), "text") || (file.get()
        .getType()
        .name()
        .equals(FileType.UNKNOWN.name()) && probeIsFileType(file.get(), "text"))) {
      documentFileDto.setVisualiseAs("text");
    }
    else if (isFileType(file.get()
        .getType(), "json") && file.get()
        .getType()
        .name()
        .equals(FileType.ERROR.name())) {
      documentFileDto.setVisualiseAs("error");
    }
  }

  @Override
  public Function<Document, String> viewerBaseNameForDocument(String baseFileName) {
    return document -> viewerBaseName(baseFileName).apply(document);
  }

  @Override
  public Function<Object, String> viewerBaseName(String baseFileName) {
    return obj -> {
      if (obj instanceof Document) {
        Document document = (Document) obj;
        if (document.isIndexed()) {
          InvoiceIndex invoiceIndex = document.getIndexValue();
          return ofNullable(invoiceIndex)
              .map(i -> joinInvoiceNumberAndInvoiceIssueDate(baseFileName, i, EXTENDED_DATE_FORMAT))
              .orElse("document");
        }
        else {
          return document.getReference();
        }
      }
      else if (obj instanceof ArchivedInvoice) {
        ArchivedInvoice archivedInvoice = (ArchivedInvoice) obj;
        return ofNullable(archivedInvoice)
            .map(i -> joinInvoiceNumberAndInvoiceIssueDate(baseFileName, i, EXTENDED_DATE_FORMAT))
            .orElse("archive");
      }
      return "document";
    };
  }

  @Override
  public List<DocumentFile> getAllErrorFiles(Document document) {
    List<DocumentFile> files = getErrorsFiles(document);
    File errorFileFromDbErrors = createErrorFileFromDbErrors(document);
    if (errorFileFromDbErrors != null) {
      DocumentFile documentFile = new DocumentFile(errorFileFromDbErrors, FileType.ERROR, null, null, document, null);
      files.add(documentFile);
    }
    return files;
  }

  public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
    Set<Object> seen = ConcurrentHashMap.newKeySet();
    return t -> isBlank((CharSequence) keyExtractor.apply(t)) || seen.add(keyExtractor.apply(t));
  }

  @Override
  public File createErrorFileFromDbErrors(Document document) {
    List<String> documentErrorCodes = EDocumentErrorHelper.getDocumentErrorService()
        .findErrors(document)
        .stream()
        .map(documentError -> documentError.getErrorCode())
        .collect(toList());
    if (!documentErrorCodes.isEmpty()) {
      StringBuilder stringErrorsBuilder = new StringBuilder();
      EDocumentErrorHelper.getEDocumentError(null, document)
          .stream()
          .filter(distinctByKey(LabelSet::getKey))
          .forEach(labelSet -> {
            if (documentErrorCodes.contains(labelSet.getKey())) {
              stringErrorsBuilder.append(getGson()
                  .toJson(labelSet));
            }
          });
      BufferedWriter writer;
      try {
        String fileName = document.getReference()
            .concat(".err");
        AttachedToInvoiceFileLocation attachedToInvoiceFileLocation = new AttachedToInvoiceFileLocation(document.getOwners(),
            document.getIndex()
                .getId());
        File rawDataFolder = configurationService.getFile(DATA_DIR, null);
        Path internFolderPath = attachedToInvoiceFileLocation.asRelativePath();
        Path folderPath = Paths.get(rawDataFolder.getAbsolutePath())
            .resolve(internFolderPath)
            .toAbsolutePath();
        File absoluteFolder = folderPath.toFile();
        if (!absoluteFolder.exists()) absoluteFolder.mkdirs();
        File errorFile = new File(absoluteFolder, fileName);
        writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(errorFile.getPath()), StandardCharsets.UTF_8));
        writer.write(stringErrorsBuilder.toString());
        writer.close();
        return errorFile;
      }
      catch (IOException e) {
        throw new RuntimeException(e);
      }
    }
    return null;

  }

  /**
   * Method used in to get the User connected from the API call (user, Local, Login...) throw an exception if the user not found
   *
   * @param request
   * @param localeAsString
   * @return
   */
  public User getUser(HttpServletRequest request, String localeAsString) {
    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);
    String login = getJWTUsername(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    if (Objects.isNull(user)) {
      throw new RestJwtException(i18NService.findByCodeAndLocale(USER_NOT_FOUND, locale, login), ExceptionType.NOT_FOUND);
    }
    if (user.getSwitchUserLogin() != null) {
      user = accountService.getUserByLoginOrOpenID(user.getSwitchUserLogin());
    }
    return user;
  }

    public String createFileName(boolean list, String type, Locale locale, String familyVar) {
        StringBuilder fileName = new StringBuilder(familyVar);

      fileName.append("_")
              .append(new SimpleDateFormat("yyyyMMddHHmmss").format(Calendar.getInstance().getTime()));

        return fileName.toString();
    }

    public static String createExportFileName(boolean list, String type, Locale locale, boolean fromAAPAccountingReferential, String familyVar) {
        return new StringBuilder("export")
                .append(!list ? "-" : "")
                .append(!list ? getLabel("gnxxcblcomlbls", "list", "", locale) : "")
                .append("-")
                .append(getLabel(familyVar,
                        fromAAPAccountingReferential ? "aap-accounting-referential" : "account-referential-supplier", "", locale))
                .append(list && !isBlank(type) ? "-" + type : "")
                .append("-")
                .append(new SimpleDateFormat(PATTERN_DATE).format(Calendar.getInstance()
                        .getTime()))
                .append("-")
                .append(new SimpleDateFormat("HHmmss-SSSSSS").format(Calendar.getInstance()
                        .getTime()))
                .toString();
    }

  /**
   * prepare a file to Download as CSV
   * @param fileName
   * @return
   * @throws IOException
   */
  public static File prepareDownloadCSV(String fileName) throws IOException {
    // Create file in temporary directory.
    File tmpFile = new File(FileUtils.getTempDirectory(), fileName);

    // Write the content in the file.
    ExcelExportHelper.exportAsCsv(null, null, tmpFile, null, null, fileName, null);

        return tmpFile;
    }

  public static File prepareDownloadTemplateCSV(String fileName,String headerNames) throws IOException {
    File tmpFile = new File(FileUtils.getTempDirectory(), fileName);
    ExcelExportHelper.exportTemplateAsCsv(headerNames, tmpFile);
    return tmpFile;
  }

    /**
     * prepare a file to Download as XML
     *
     * @return
     * @throws IOException
     */

    @Override
    public ProcessManagementDTO createProcessManagementDTO() {
      ProcessManagementDTO processManagementDTO = new ProcessManagementDTO();
      processManagementDTO.setRunningProcessIds(new ArrayList<>(restJobProcessManagement.getRunningProcesses()));
      processManagementDTO.setWaitingProcessInExecQueue(restJobProcessManagement.getBlockingQueueSize());
      return processManagementDTO;
    }

  @Override
  public Map<String, String> getLegalEntitiesMap(User user) {
    //get all the Legal entities that the user can see
    Set<String> legalEntities = new HashSet<>();
    legalEntities =
        (PrincipalHelper.isPartnerUser(user) || (!PrincipalHelper.isRootUser(user) && RestServiceHelper.isClientWithPerimeter(
            user)))
            ? UserDetailsService.getUserScopeCodes(Objects.requireNonNull(user))
            : null;
    Company company = PrincipalHelper.getCompany(user);

    //case of admin user connected
    if (legalEntities != null && company == null && legalEntities.isEmpty()) {
      return Collections.emptyMap();
    }
    Map<String, String> legalEntitiesMapped = new HashMap<>();
    if (legalEntities == null || legalEntities.isEmpty()) {
      if (PrincipalHelper.isCompanyUser(user)) {
        Stream<Partner> parterStream = (this.securityService.getPartners(
            company)).stream();
        legalEntitiesMapped = parterStream
            .collect(Collectors.toMap(
                Group::getCode, // Key mapper: use p.getCode()
                p -> p.getFullnameOrName() + " / " + p.getCode() // Value mapper
            ));
      }

    }
    else {
      for (String legalEntityElem : legalEntities) {
        String partnerToAdd = this.securityService.getPartner(legalEntityElem, company)
            .getFullnameOrName() + " / " + legalEntityElem;
        legalEntitiesMapped.put(legalEntityElem, partnerToAdd);
      }
    }

    Map<String, String> sortedLegalEntities = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
    sortedLegalEntities.putAll(legalEntitiesMapped);
    return sortedLegalEntities;
  }
}
