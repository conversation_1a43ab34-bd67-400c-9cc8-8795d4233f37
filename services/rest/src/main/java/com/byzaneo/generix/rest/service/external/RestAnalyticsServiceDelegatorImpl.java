package com.byzaneo.generix.rest.service.external;

import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.user.stat.*;
import com.byzaneo.generix.api.service.external.delegators.RestAnalyticsServiceDelegator;
import com.byzaneo.generix.api.util.*;
import com.byzaneo.generix.service.SecurityService.Resource;
import com.byzaneo.generix.util.PartnerHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.SecurityService;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType;
import com.google.gson.*;
import com.mongodb.DBObject;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.text.*;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.byzaneo.generix.rest.helper.RestServiceHelper.verifyEnvironmentCode;
import static org.slf4j.LoggerFactory.getLogger;

@Component
@RequiredArgsConstructor
public class RestAnalyticsServiceDelegatorImpl implements RestAnalyticsServiceDelegator {

  private static final Logger log = getLogger(PerimeterApiServiceDelegatorImpl.class);
  private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH.mm.ss.SSS");

  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private transient SecurityService securityService;

  @Autowired
  private PermissionHelper permissionHelper;

  @Autowired
  private UserStatService userStatAPIService;
  @Autowired
  private DocumentService documentService;
  private static final List<InvoiceTypeCodeType> INVOICE_TYPE_CODED_LIST = Arrays.asList(
      InvoiceTypeCodeType.SELF_BILLED_CREDIT_NOTE, InvoiceTypeCodeType.SELF_BILLED_INVOICE,
      InvoiceTypeCodeType.SELF_BILLED_PREPAYMENT_INVOICE,
      InvoiceTypeCodeType.SELF_BILLED_FACTORED_INVOICE,
      InvoiceTypeCodeType.SELF_BILLED_FACTORED_CREDIT_NOTE);
  private static final Pattern PARTNER_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{1,128}$");
  private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

  @Override
  public Response getInvoiceCountByPartnerAndPeriod(HttpServletRequest request, String env, UUID requestId,
      InvoiceRequestDTO invoiceRequest) {
    if (!com.byzaneo.generix.rest.helper.RestServiceHelper.hasBearerToken(request)) {
      return RestServiceHelper.getResponseOnError(
          Response.Status.FORBIDDEN.toString(),
          "Authorization header must not be null or empty",
          Response.Status.FORBIDDEN,
          requestId
      );
    }

    if (requestId == null)
      requestId = UUID.randomUUID();

    if (invoiceRequest.getPartnerIds() == null || invoiceRequest.getPartnerIds()
        .isEmpty()) {
      return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(), "partners must not be empty",
          Response.Status.BAD_REQUEST,
          requestId);
    }

    for (String id : invoiceRequest.getPartnerIds()) {
      if (id == null || !PARTNER_ID_PATTERN.matcher(id)
          .matches()) {
        return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(),
            "Invalid partner ID: '" + id + "'. Must be 1 to 128 alphanumeric characters or underscore",
            Response.Status.BAD_REQUEST,
            requestId);
      }
    }

    if (invoiceRequest.getProcessingWay() == null || invoiceRequest.getProcessingWay()
        .isEmpty()) {
      return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(), "processingWay must not be null",
          Response.Status.BAD_REQUEST,
          requestId);
    }

    log.info("Start analytics with RequestId: {}, Env: {}, Request: {}", requestId, env, invoiceRequest, SDF.format(Calendar.getInstance()
        .getTime()));

    TechnicalUser technicalUser = securityService.getTechnicalUserById(RestServiceHelper.getTechnicalUserIdFromRequest(request));
    verifyEnvironmentCode(technicalUser, env);
    List<Partner> partnerList;

    boolean hasPermission = permissionHelper.isGrantedTechnicalUser(
        Resource.Api_permissions_get_invoice, Right.READ, technicalUser);
    boolean isDisabled = technicalUser.isDisabled();

    if (!hasPermission || isDisabled) {
      return RestServiceHelper.getResponseOnError(Response.Status.FORBIDDEN.toString(),
          "Security error: You don't have the rights to access to the API.",
          Response.Status.FORBIDDEN,
          requestId);
    }

    partnerList = PartnerHelper.filterPartnerList(technicalUser, invoiceRequest.getPartnerIds());

    Date start;
    Date end;
    try {
      start = Optional.ofNullable(invoiceRequest.getStartDate())
          .map(this::parseDateUnchecked)
          .orElse(new Date(Long.MIN_VALUE));

      end = Optional.ofNullable(invoiceRequest.getEndDate())
          .map(this::parseDateUnchecked)
          .orElse(new Date(Long.MAX_VALUE));
    }
    catch (RuntimeException e) {
      return RestServiceHelper.getResponseOnError(
          Response.Status.BAD_REQUEST.toString(),
          "Invalid date format. Expected format: yyyy-MM-dd",
          Response.Status.BAD_REQUEST,
          requestId
      );
    }
    if (start.after(end)) {
      log.warn("Invalid date range: start={} is after end={}, requestId={}", start, end, requestId);
      return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(),
          "Start date must be before or equal to end date",
          Response.Status.BAD_REQUEST,
          requestId);
    }

    Map<String, Long> countOfInvoicesByPartner = countInvoicesBySendingWayAndDateRange(
        Document.ProcessingWay.valueOf(invoiceRequest.getProcessingWay()), start, end, partnerList.stream()
            .map(Partner::getCode)
            .collect(Collectors.toList()), env);

    List<PartnerCountDto> partnerCountDtos = countOfInvoicesByPartner.entrySet()
        .stream()
        .map(entry -> new PartnerCountDto(entry.getKey(), entry.getValue()
            .intValue()))
        .collect(Collectors.toList());

    InvoiceAnalyticsResponseDTO invoiceAnalyticsResponseDTO = new InvoiceAnalyticsResponseDTO();
    invoiceAnalyticsResponseDTO.setCountByPartner(partnerCountDtos);
    invoiceAnalyticsResponseDTO.setProcessingWay(invoiceRequest.getProcessingWay());
    invoiceAnalyticsResponseDTO.setStartDate(invoiceRequest.getStartDate());
    invoiceAnalyticsResponseDTO.setEndDate(invoiceRequest.getEndDate());
    invoiceAnalyticsResponseDTO.setCountByPartner(partnerCountDtos);
    Gson gson = new GsonBuilder()
        .create();

    return Response.status(Response.Status.OK)
        .entity(gson.toJson(invoiceAnalyticsResponseDTO))
        .header("Request-Id", requestId)
        .build();
  }

  private Date parseDateUnchecked(String dateStr) {
    try {
      SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
      formatter.setLenient(false);
      return formatter.parse(dateStr);
    }
    catch (ParseException e) {
      throw new RuntimeException("Invalid date format", e);
    }
  }

  public Map<String, Long> countInvoicesBySendingWayAndDateRange(
      Document.ProcessingWay sendinWay,
      Date startDate,
      Date endDate,
      List<String> partners,
      String envCode) {
    if (partners == null || partners.isEmpty()) {
      return Collections.emptyMap();
    }
    Query query = new Query(new AndClause(
        Clauses.between("invoiceIssueDate", startDate, endDate),
        Clauses.equal("processingWay", sendinWay),
        Clauses.equal("_owner", envCode)

    ));

    AggregationOperation distinctHashGroup = Aggregation.group("hashcode")
        .first("$$ROOT")
        .as("invoiceHashcode");

    AggregationOperation replaceRoot = Aggregation.replaceRoot("invoiceHashcode");

    AggregationOperation addContactField = Aggregation.addFields()
        .addFieldWithValue("contactField",
            new org.bson.Document("$cond",
                Arrays.asList(
                    new org.bson.Document("$in",
                        Arrays.asList("$invoiceTypeCoded", INVOICE_TYPE_CODED_LIST)),
                    sendinWay == Document.ProcessingWay.SENDING ? "$_to" : "$_from",
                    sendinWay == Document.ProcessingWay.SENDING ? "$_from" : "$_to"
                )
            )
        )
        .build();

    AggregationOperation matchPartners = Aggregation.match(
        Criteria.where("contactField")
            .in(partners)
    );

    AggregationOperation groupByContact = Aggregation.group("contactField")
        .count()
        .as("count");

    List<DBObject> results = documentService.aggregate(
        InvoiceIndex.class,
        DBObject.class,
        query,
        distinctHashGroup,
        replaceRoot,
        addContactField,
        matchPartners,
        groupByContact
    );

    Map<String, Long> resultMap = new HashMap<>();
    for (DBObject result : results) {
      String partnerId = String.valueOf(result.get("_id"));
      Long count = ((Number) result.get("count")).longValue();
      resultMap.put(partnerId, count);
    }

    return resultMap;
  }

  @Override
  public Response processUser(HttpServletRequest request, String env, UUID requestId, UserRequestDTO userRequest) throws Exception {
    if (!com.byzaneo.generix.rest.helper.RestServiceHelper.hasBearerToken(request)) {
      return RestServiceHelper.getResponseOnError(
          Response.Status.FORBIDDEN.toString(),
          "Authorization header must not be null or empty",
          Response.Status.FORBIDDEN,
          requestId
      );
    }
    if (requestId == null)
      requestId = UUID.randomUUID();

    if (userRequest.getPartnerIds() == null || userRequest.getPartnerIds()
        .isEmpty()) {
      return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(), "partners must not be empty",
          Response.Status.BAD_REQUEST,
          requestId);
    }

    for (String id : userRequest.getPartnerIds()) {
      if (id == null || !PARTNER_ID_PATTERN.matcher(id)
          .matches()) {
        return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(),
            "Invalid partner ID: '" + id + "'. Must be 1 to 128 alphanumeric characters or underscore",
            Response.Status.BAD_REQUEST,
            requestId);
      }
    }

    if (userRequest.getReportType() == null || userRequest.getReportType()
        .isEmpty()) {
      return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(), "UserReportType must not be null",
          Response.Status.BAD_REQUEST,
          requestId);
    }

    Date start = null;
    Date end = null;
    try {
      start = Optional.ofNullable(userRequest.getStartDate())
          .map(UserStatServiceImpl::parseDate)
          .orElse(new Date(0));
    }
    catch (Exception e) {
      return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(),
          "Invalid date format: " + userRequest.getStartDate() + ". Expected format: yyyy-MM-dd",
          Response.Status.BAD_REQUEST,
          requestId);
    }
    try {
      end = Optional.ofNullable(userRequest.getEndDate())
          .map(UserStatServiceImpl::parseDate)
          .orElse(new Date());
    }
    catch (Exception e) {
      return RestServiceHelper.getResponseOnError(Response.Status.BAD_REQUEST.toString(),
          "Invalid date format: " + userRequest.getEndDate() + ". Expected format: yyyy-MM-dd",
          Response.Status.BAD_REQUEST,
          requestId);
    }

    TechnicalUser technicalUser = securityService.getTechnicalUserById(RestServiceHelper.getTechnicalUserIdFromRequest(request));
    RestServiceHelper.verifyEnvironmentCode(technicalUser, env);

    if (start.compareTo(end) >= 0) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity("Start date should be before end date")
          .header("Request-Id", requestId)
          .build();
    }
    UserAnalyticsResponseDTO userAnalyticsResponseDTO = userStatAPIService.getUserStatistics(technicalUser, env, requestId,
        userRequest.getPartnerIds(), userRequest.getReportType(), start, end);
    return Response.status(Response.Status.OK)
        .entity(new GsonBuilder().create()
            .toJson(userAnalyticsResponseDTO))
        .header("Request-Id", requestId)
        .build();
  }

}
