package com.byzaneo.generix.rest.service.jwt;

import com.byzaneo.angular.service.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.generix.api.bean.PeriodicReportListDTO;
import com.byzaneo.generix.api.bean.portal.*;
import com.byzaneo.generix.api.service.internal.delegators.RestPeriodicReportServiceDelegator;
import com.byzaneo.generix.rest.*;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.rest.service.basic.RestCommonService;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.util.SortOrderDto;
import com.byzaneo.generix.task.portal.document.PeriodicReportTask;
import com.byzaneo.generix.util.OrganizationHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.BqlParseException;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.security.spring.UserDetailsService;
import com.byzaneo.xtrade.bean.PeriodicReport;
import com.byzaneo.xtrade.index.MongoIndexOperations;
import com.byzaneo.xtrade.service.DocumentService;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.bson.types.ObjectId;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.*;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.bean.FileType.ARCHIVE;
import static com.byzaneo.commons.bean.FileType.getType;
import static com.byzaneo.commons.dao.mongo.PageRequest.of;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.generix.rest.service.jwt.RestAccountingPostingServiceDelegatorImpl.transformPartnerToLegalEntityDTO;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.security.util.PrincipalHelper.isCompanyUser;
import static com.byzaneo.security.util.PrincipalHelper.isPartnerUser;
import static java.nio.file.Files.createTempFile;
import static java.util.stream.Collectors.toList;
import static javax.ws.rs.core.Response.ok;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

@Component
@RequiredArgsConstructor
public class RestPeriodicReportServiceDelegatorImpl implements RestPeriodicReportServiceDelegator {

    private static final Logger log = getLogger(RestPeriodicReportServiceDelegatorImpl.class);

    public static final String USER_NOT_FOUND = "user_not_found";
    public static final String PERIODICREPORT = "PERIODICREPORTLIST";

    @Autowired
    @Qualifier(I18NService.BEAN_NAME)
    private I18NService i18NService;

    @Autowired
    @Qualifier(SecurityService.SERVICE_NAME)
    private SecurityService securityService;

    @Autowired
    @Qualifier(AccountService.SERVICE_NAME)
    private AccountService accountService;

    @Autowired
    @Qualifier(DocumentService.SERVICE_NAME)
    private DocumentService documentService;

    @Autowired
    @Qualifier(TaskService.SERVICE_NAME)
    private TaskService taskService;

    @Autowired
    @Qualifier(RestCommonService.SERVICE_NAME)
    RestCommonService restCommonService;

    @Autowired
    @Qualifier(DocumentService.INDEX_OPERATIONS_NAME)
    MongoIndexOperations indexOperations;

    @Autowired
    private ModelMapper modelMapper;

    private Company getCompany(User user) {
        Company company = null;
        if (isCompanyUser(user)) {
            company = (Company) user.getPrimaryGroup();
        } else if (isPartnerUser(user)) {
            company = (Company) user.getPrimaryGroup().getParent();
        }
        return company;
    }

    @Override
    @Transactional
    public Response v1getPeriodicReport(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset,
        String legalEntity, String flowDirection, String sortBy, com.byzaneo.generix.api.bean.SortOrderDto order, boolean count,
        String portletId, String locale, boolean isCountEnabled) {
        if (requestId == null) {
            requestId = UUID.randomUUID();
        }

        String login = RestServiceHelper.getUserFromRequest(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        TechnicalUser technicalUser = null;
        if (limit == null || limit < 0)
            limit = Integer.MAX_VALUE;
        if (offset == null || offset < 0)
            offset = 0;
        if(StringUtils.isEmpty(sortBy)) {
            sortBy = null;
        }

        log.debug("Searching {} eDocuments : {} (page={}, size={})",
                PERIODICREPORT, bql, offset, limit);

        // - search for Reports -
        try {
            List<LegalEntityDTO> legalEntitiesMapped = new ArrayList<>();
            List<LegalEntityDTO> sortedLegalEntities = new ArrayList<>();
            //get the default Legal entity for the current user
            String userLegalEntity = isPartnerUser(user) ? user.getPrimaryGroup()
                .getCode() : null;
            Company company = getCompany(user);

            if (StringUtils.isEmpty(legalEntity)) {
                Set<String> legalEntities =
                    (isPartnerUser(user) || (!RestServiceHelper.isBackOfficeUserUser(user) && RestServiceHelper.isClientWithPerimeter(
                        user)))
                        ? UserDetailsService.getUserScopeCodes(Objects.requireNonNull(user))
                        : null;

                //case of admin user connected
                if (company == null && legalEntities != null && legalEntities.isEmpty()) {
                    Response.status(Response.Status.NOT_FOUND)
                        //.entity("No data found")
                            .build();
                }

                if (CollectionUtils.isEmpty(legalEntities)) {
                    if (isCompanyUser(user)) {
                        Set<Partner> partners = this.securityService.getPartners(company);
                        legalEntitiesMapped = partners.stream().map(org -> transformPartnerToLegalEntityDTO(org, locale))
                                .collect(Collectors.toList());
                    }
                }
                else {
                    for (String legalEntityElem : legalEntities) {
                        addLegalEntity(legalEntityElem, company, legalEntitiesMapped, locale);
                    }
                }
            }
            else {
                addLegalEntity(legalEntity, company, legalEntitiesMapped, locale);
            }

            sortedLegalEntities = legalEntitiesMapped.stream()
                .sorted(Comparator.comparing(LegalEntityDTO::getName))
                .collect(toList());

            if (sortedLegalEntities.isEmpty()) {
                throw new RestJwtException("legal entity is not valid", ExceptionType.BAD_REQUEST);
            }

            String owner = isPartnerUser(user) ? user.getPrimaryGroup()
                .getParent()
                .getCode()
                : user.getPrimaryGroup()
                    .getCode();
            final QueryBuilder qbuilder = OrganizationHelper.resolveUserOrganizationsQuery(owner, sortedLegalEntities.stream()
                    .map(LegalEntityDTO::getCode)
                    .collect(toList()), parse(bql),
                "owner",
                null, "entity", null, flowDirection, sortBy, order != null ? order.name() : SortOrderDto.ASC.name());

            if (user != null)
                restCommonService.addUserAndRoleInvoiceBqlQueryToTheSearch(qbuilder, user);
            else
                qbuilder.append(securityService.createQueryFromTechnicalUserRole(technicalUser));

            final Query qb = qbuilder.query();
            final PageRequest pageable = of(
                offset < 0 ? 0 : offset,
                limit < 1 ? -1 : limit);

            pageable.setCount(true);
            pageable.setFromAngular(true);

            log.info("RequestId {} : API {} is searching {} edocuments: {} ({})", requestId, user != null ? user
                    .getLogin() : technicalUser.getClientId(), PeriodicReport.class, qb, pageable);

            List<PeriodicReport> result;
            long totalElements;
            long totalPage;
            if (count == true) {
                long totalCount;
                totalCount = this.documentService.countIndexable(PeriodicReport.class, qb);
                return ok(getGson().toJson(totalCount)).header("Request-Id", requestId.toString())
                    .build();
            }
            else {
                Page<PeriodicReport> page = this.documentService.searchIndexables(PeriodicReport.class, qb,
                    pageable);
                result = page.getContent();
                totalElements = page.getTotalElements();
                totalPage = page.getTotalPages();

                log.debug("API result: {}", result);
                return ok(getGson().toJson(
                    getPeriodicReportList(result, totalElements, totalPage, portletId, userLegalEntity))).header(
                        "Request-Id", requestId.toString())
                    .build();
            }

        }
        catch (SecurityException e) {
            log.error("API eDocument search error: {} (q='{}', type='{}')",
                    e.getMessage(), bql, PERIODICREPORT);
            throw new RestSecurityException(e.getMessage());
        }
        catch (BqlParseException | IllegalArgumentException | NoResultException |
               NonUniqueResultException e) {
            log.error("API eDocument search error: {} (q='{}', type='{}')",
                    e.getMessage(), bql, PERIODICREPORT);
            throw new RestException(e.getMessage());
        }
        catch (Exception e) {
            String message = getRootCauseMessage(e);
            log.error("API eDocument search error: {} ( q='{}', type='{}')",
                    message, bql, PERIODICREPORT);
            throw new RestException("An unexpected error occurs: " + message);
        }
    }

    private void addLegalEntity(String legalEntity, Company company, List<LegalEntityDTO> legalEntitiesMapped, String localeAsString) {
        Partner partner = this.securityService.getPartner(legalEntity, company);
        LegalEntityDTO legalEntityDTO = transformPartnerToLegalEntityDTO(partner, localeAsString);
        // TODO for AIO-18378 partner identifier should not be taken into account as legal entity, keep only the SIREN
        legalEntitiesMapped.add(legalEntityDTO);
    }

    public PeriodicReportListDTO getPeriodicReportList(List<PeriodicReport> PeriodicReports, long totalElements, long totalPages,
        String portletId, String userLegalEntity) {
        PeriodicReportListDTO periodicReportListDTO = new PeriodicReportListDTO();
        List<PeriodicReportDto> periodicReportDtos = List.copyOf(PeriodicReports)
                .stream()
                .map(i -> {
                    return toDTO(i);
                })
                .collect(Collectors.toList());

        periodicReportListDTO.setContent(periodicReportDtos);
        periodicReportListDTO.setTotal(periodicReportDtos.size());
        periodicReportListDTO.setUserLegalEntity(userLegalEntity);
        periodicReportListDTO.setTotalElements(totalElements);
        periodicReportListDTO.setTotalPages(totalPages);
        periodicReportListDTO.setPortletId(portletId);

        return periodicReportListDTO;
    }

    public  PeriodicReportDto toDTO(PeriodicReport periodicReport) {
        return modelMapper.map(periodicReport, PeriodicReportDto.class);
    }

    public PeriodicReport fromDTO(PeriodicReportDto dto) {
        return modelMapper.map(dto, PeriodicReport.class);
    }

    @Override
    @Transactional
    public Response v1SetPeriodicReport(HttpServletRequest request, Long portletId, String localeAsString, Attachment file,
                                     String periodicReportDtoAsString) {
        ObjectMapper objectMapper = new ObjectMapper();
        localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
        Locale locale = new Locale(localeAsString);
        String login = RestServiceHelper.getJWTUsername(request);
        User user = accountService.getUserByLoginOrOpenID(login);
        if(user.getSwitchUserLogin()!=null){
            user = accountService.getUserByLoginOrOpenID(user.getSwitchUserLogin());
        }
        if (Objects.isNull(user)) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(i18NService.findByCodeAndLocale(USER_NOT_FOUND, locale, login))
                    .build();
        }

        PeriodicReportDto periodicReportDto;
        PeriodicReport periodicReport;
        try {
            periodicReportDto = objectMapper.readValue(periodicReportDtoAsString, PeriodicReportDto.class);
            Map<String, String> violationsReport = RestServiceHelper.checkValidity(
                    periodicReportDto);
            if (MapUtils.isNotEmpty(violationsReport)) {
                return RestServiceHelper.getResponse(Response.Status.BAD_REQUEST, violationsReport);
            }
            // set Object  periodicReport
            periodicReport = new PeriodicReport(
                    periodicReportDto.getReportName(),
                    periodicReportDto.getPeriod(),
                    periodicReportDto.getEntity(),
                    PERIODICREPORT
            );
            periodicReport.setDate(new Date());
            //create random uuid
            UUID uuid = UUID.randomUUID();
            periodicReport.setDate(new Date());
            String owner = isPartnerUser(user) ? user.getPrimaryGroup().getParent().getCode()
                    : user.getPrimaryGroup().getCode();
            periodicReport.setOwner(owner);
            String contentLengthHeader = request.getHeader("Content-Length");
            Double size = (double)Math.round(Long.parseLong(contentLengthHeader)/ 1024);
            periodicReport.setSize(size);
            Response filepath =restCommonService.attachFileToIndexable(request, file, localeAsString, true, null, null, user, null, uuid, portletId,periodicReportDto.getEntity());
            periodicReport.setFilePath(filepath.getEntity().toString());
            documentService.saveIndexable(periodicReport);

        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonParseException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return RestServiceHelper.getResponse(Response.Status.OK, periodicReport);

    }

    @Override
    @Transactional
    public Response deleteReport(HttpServletRequest request, String id, String localeAsString ,  String uuid) {
        org.springframework.data.mongodb.core.query.Query query = query(
                where("_id").is(new ObjectId(id)));
        PeriodicReport periodicReports= indexOperations.find(query, PeriodicReport.class).get(0);
        File f = new File(periodicReports.getFilePath());
        f.delete();
         indexOperations.remove(query, PeriodicReport.class);
        return RestServiceHelper.getResponse(Response.Status.OK, "Report is deleted");
    }

    @Override
    @Transactional
    public Response deleteReports(HttpServletRequest request,  List<String> uuids, String localeAsString) {
        try {
        List<String> uuidList;
        uuidList = Arrays.asList(uuids.get(0).split(","));

        uuidList.forEach(periodicReportUuid -> {
        org.springframework.data.mongodb.core.query.Query query = query(
                where("_id").is(new ObjectId(periodicReportUuid)));
            PeriodicReport periodicReports= indexOperations.find(query, PeriodicReport.class).get(0);
            File f = new File(periodicReports.getFilePath());
            f.delete();
        indexOperations.remove(query, PeriodicReport.class);

        });
        return RestServiceHelper.getResponse(Response.Status.OK, "Reports is deleted");
        } catch (Exception e) {
            return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, "Failed to delete reports");
        }
    }
    @Override
    @Transactional(readOnly = true)
    public Response getReportZipFile(HttpServletRequest request, String portletId, boolean allIndexesSelected, List<String> uuids) {
            Object task = taskService.getTask(Long.parseLong(portletId));
            Set<File> files = new HashSet<>();
            String baseName = "Periodic-Report";
            File zipFile = null;
            try {
                zipFile = createTempFile(baseName.concat("-"), ARCHIVE.getExtension()).toFile();
            } catch (IOException e) {
                throw new RestJwtException("Cannot create temporary file: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
            }
            if (task != null && task instanceof PeriodicReportTask) {
                PeriodicReportTask periodicReportTask = (PeriodicReportTask) task;
                List<String> uuidList;
                try (FileOutputStream zipOut = new FileOutputStream(zipFile)) {
                        uuidList = Arrays.asList(uuids.get(0).split(","));
                    uuidList.forEach(id -> {
                        org.springframework.data.mongodb.core.query.Query query = query(
                                where("_id").is(new ObjectId(id)));
                        PeriodicReport periodicReports= indexOperations.find(query, PeriodicReport.class).get(0);
                        File f = new File(periodicReports.getFilePath());
                        files.add(f);

                    });
                    if (!files.isEmpty()) {
                        com.byzaneo.commons.util.FileHelper.zip(files, zipOut);
                    }
                }
                catch (IOException e) {
                    throw new RestJwtException("Cannot create zipfile: " + e.getMessage(), ExceptionType.INTERNAL_ERROR);
                }
            }
            return ok(zipFile, getType(zipFile).getDefaultMime())
                    .header("Content-Disposition", "Report; filename=" + zipFile.getName())
                    .build();
        }

}
