package com.byzaneo.generix.rest.service.jwt.dematpartnerfile;

import com.byzaneo.angular.service.*;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.portal.*;
import com.byzaneo.generix.api.bean.portal.dematpartnerfile.*;
import com.byzaneo.generix.api.service.internal.delegators.*;
import com.byzaneo.generix.audit.bean.Revision;
import com.byzaneo.generix.audit.service.AuditService;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.bean.Template;
import com.byzaneo.generix.edocument.bean.RecapListItem;
import com.byzaneo.generix.edocument.service.EDocumentService;
import com.byzaneo.generix.edocument.task.bean.*;
import com.byzaneo.generix.edocument.task.portal.DematPartnerFileTask;
import com.byzaneo.generix.edocument.util.CsvExportHelper;
import com.byzaneo.generix.rest.*;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.rest.service.basic.RestCommonService;
import com.byzaneo.generix.rest.service.jwt.RestGenericService;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.util.*;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.generix.xtrade.util.ExcelExportHelper;
import com.byzaneo.location.bean.Address;
import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.BqlParseException;
import com.byzaneo.query.builder.*;
import com.byzaneo.query.operator.Operator;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.*;
import com.byzaneo.security.spring.UserDetailsService;
import com.byzaneo.security.util.PrincipalHelper;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.xcbl.util.CustomLocaleMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.data.domain.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.*;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import javax.xml.bind.*;
import java.io.*;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.byzaneo.commons.bean.FileType.getType;
import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.commons.util.I18NHelper.toLabelSet;
import static com.byzaneo.generix.rest.service.basic.RestCommonServiceImpl.USER_NOT_FOUND;
import static com.byzaneo.generix.rest.service.jwt.RestAccountingPostingServiceDelegatorImpl.transformPartnerToLegalEntityDTO;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.security.util.PrincipalHelper.isPartnerUser;
import static com.sun.syndication.io.impl.DateParser.parseDate;
import static java.lang.System.currentTimeMillis;
import static java.util.Collections.sort;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static javax.faces.application.FacesMessage.SEVERITY_WARN;
import static javax.ws.rs.core.Response.ok;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.primefaces.context.RequestContext.getCurrentInstance;
import static org.primefaces.model.SortOrder.ASCENDING;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;

@Component
@RequiredArgsConstructor
public class RestDematPartnerFileServiceDelegatorImpl implements RestDematPartnerFileServiceDelegator {

  private static final Logger log = getLogger(RestDematPartnerFileServiceDelegatorImpl.class);

  public static final String DEMAT_PARTNER_FILE_TYPE = "DEMAT_PARTNER_FILE";
  public static final Locale locale = Locale.ENGLISH;
  public static final String XML = "XML";
  public static final String PDF = "PDF";
  public static final String CSV = "CSV";

  public static final String LABEL_FAMILY = "gnxxcbldematpartnerfilelbls";
  protected static final String PATTERN_DATE = "YYYYMMdd";
  private static final String EXCHANGE_LIST_DIRECTORY_NAME = "download_list_dir";

  protected static final String TEMP_DIR = FileUtils.getTempDirectory() + FileSystems.getDefault()
      .getSeparator();
  protected static final int pdfLimitFileSize = 2500;
  private String templateUri = DEFAULT_TEMPLATE_URI;
  private static final String DEFAULT_TEMPLATE_URI = "/_global_/BIRT/exchanges/exchanges";

  // mapping for the export
  private transient Map<String, String> exportMapping = null;

  private final SecurityService securityService;
  private final AccountService accountService;
  private final RestCommonService restCommonService;
  private final TaskService taskService;
  private final EDocumentService eDocumentService;
  private final RestGenericService restGenericService;
  private final I18NService i18NService;
  private final InstanceService instanceService;
  private final TransformService transformService;
  private final UserTaskDefinitionService userTaskDefinitionService;
  private final RestPortalServiceDelegator portalServiceDelegator;
  private final ModelMapper modelMapper;
  private final AuditService auditService;
  private static final Comparator<Revision> TIMESTAMP_COMPARATOR = Comparator.comparing(Revision::getTimestamp);

  private static final List<String> REQUIRED_FIELDS = Arrays.asList(
      "id.legalEntity.code",
      "id.dematPartner.code",
      "id.kind",
      "id.direction",
      "start"
  );

  private static final List<String> ALLOWED_FIELDS = Arrays.asList(
      "id.dematPartner.code",
      "id.direction",
      "id.kind",
      "start",
      "end",
      "id.legalEntity.code"
  );

  @Transactional
  @Override
  public Response v1getDematPartnerFile(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset,
      String legalEntity, String flowDirection, String sortBy, Sort.Direction order, boolean count, String portletId, String localeAsString,
      boolean isCountEnabled) {
    requestId = requestId != null ? requestId : UUID.randomUUID();
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);

    limit = (limit == null || limit < 0) ? Integer.MAX_VALUE : limit;
    offset = (offset == null || offset < 0) ? 0 : offset;
    sortBy = (StringUtils.isEmpty(sortBy) || "undefined".equals(sortBy) || "null".equals(sortBy)) ? "id" : sortBy;

    try {
      bql = escapeReservedKeywords(bql);
      DematPartnerFileTask dematPartnerFileTask = getDematPartnerFileTask(portletId);
      bql = generateSqlFromBqlNoColumnLimit(bql, dematPartnerFileTask);

      String userLegalEntity = getUserLegalEntity(user);
      QueryBuilder builder = buildDynamicQuery(legalEntity, parse(bql));
      Pageable pageable = createPageable(limit, offset, sortBy, order);
      Page<Exchange> page = securityService.searchExchange(builder.query(false), pageable);

      logResults(page.getContent());

      if (count) {
        long totalCount = getDematPartnerFileList(page.getContent(), page, portletId, userLegalEntity).getContent()
            .size();
        return ok(getGson().toJson(totalCount)).header("Request-Id", requestId.toString())
            .build();
      }
      else {
        List<Exchange> results = page.getContent();
        return buildResponse(requestId, results, page, portletId, userLegalEntity);
      }

    }
    catch (Exception e) {
      handleException(e, bql);
      return null;
    }
  }

  @Override
  public Response v1getDocumentTypes(HttpServletRequest request, String bql, String envCode, UUID requestId, String legalEntity,
      String portletId, String locale) {
    List<String> results = eDocumentService.searchDocumentTypes(bql);
    return RestServiceHelper.getResponse(Response.Status.OK, results);
  }

  @Override
  public Response v1saveExchange(HttpServletRequest request, String envCode, String portletId, ExchangeDTO exchangeDTO) {
    Locale currentLocale = restGenericService.getCurrentLocale(request);
    String login = RestServiceHelper.getJWTUsername(request);
    User user = restCommonService.getUser(request, currentLocale.getLanguage());

    // Ensure instance exists
    Instance instance = this.instanceService.getInstanceByCode(envCode);
    if (instance == null) {
      throw new RestJwtException(i18NService.findByCodeAndLocale("instance.not.found", currentLocale, user.getFullname()),
          ExceptionType.NOT_FOUND);
    }

    Company company = (Company) instance.getGroup();

    // Validate input data
    Map<String, String> violationsExchangeRates = RestServiceHelper.checkValidity(exchangeDTO);
    if (MapUtils.isNotEmpty(violationsExchangeRates)) {
      return RestServiceHelper.getResponse(Response.Status.BAD_REQUEST, violationsExchangeRates);
    }

    try {
      // Ensure user exists
      if (Objects.isNull(user)) {
        return Response.status(Response.Status.NOT_FOUND)
            .entity(i18NService.findByCodeAndLocale(USER_NOT_FOUND, currentLocale, login))
            .build();
      }

      ExchangeIdDTO exchangeIdDTO = exchangeDTO.getId();

      Partner receiver = securityService.getPartner(exchangeIdDTO.getDematPartnerId(), company);
      Partner sender = securityService.getPartner(exchangeIdDTO.getLegalEntityId(), company);

      // Create exchange ID
      ExchangeId exchangeId = new ExchangeId(receiver, sender, exchangeIdDTO.getKind(), exchangeIdDTO.getDirection());

      Exchange exchange = new Exchange();
      exchange.setId(exchangeId);
      exchange.setStart(exchangeDTO.getStart());
      exchange.setEnd(exchangeDTO.getEnd());

      if (exchangeAlreadyExists(exchangeId)) {
        log.warn("Exchange already exists for Receiver: {}, Sender: {}, Direction: {}, Kind: {}",
            receiver.getId(), sender.getId(), exchangeIdDTO.getDirection(), exchangeIdDTO.getKind());

        return RestServiceHelper.getResponse(Response.Status.CONFLICT,
            i18NService.findByCodeAndLocale("error_saving_exchange_exist", currentLocale, user.getFullname()));
      }

      securityService.addExchangeToGroupApi(exchange, receiver);

      return RestServiceHelper.getResponse(Response.Status.OK, exchangeDTO);

    }
    catch (Exception e) {
      log.error("Error saving exchange rate: ", e);
      return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR,
          i18NService.findByCodeAndLocale("error_while_saving_exchange_rate", currentLocale, user.getFullname()));
    }
  }

  @Override
  public Response getExportTemplateFileExchange(HttpServletRequest request, String envCode, Long portletId, String localeAsString) {
    try {
      Date date = new Date();
      Instance instance = this.instanceService.getInstanceByCode(envCode);

      if (instance == null) {
        log.error("Instance not found for environment code: {}", envCode);
        throw new RestJwtException(i18NService.findByCodeAndLocale("instance.not.found", locale),
            ExceptionType.NOT_FOUND);
      }

      String instanceCode = instance.getCode();
      localeAsString = (localeAsString != null && !localeAsString.isEmpty()) ? localeAsString : "en";
      Locale locale = new Locale(localeAsString);
      SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
      String name = i18NService.findByCodeAndLocale("exchange_file_name", locale);
      String fileName = name + " " + dateFormat.format(date) + ".csv";

      String login = RestServiceHelper.getUserFromRequest(request);
      User user = accountService.getUserByLoginOrOpenID(login);

      // Get the descriptor efficiently
      BeanDescriptor descriptor = getBeanDescriptor(user, portletId, locale, instanceCode);

      File tmpFile = prepareDownloadCSV(fileName, descriptor, locale, user.getDatePattern(), null, instanceCode);
      return ok(tmpFile, getType(tmpFile).getDefaultMime())
          .header("Content-Disposition", "attachment; filename=" + tmpFile.getName())
          .build();
    }
    catch (ObjectOptimisticLockingFailureException ex) {
      log.error("Optimistic locking failure occurred while exporting: ", ex);
      return Response.status(Response.Status.CONFLICT)
          .entity("Optimistic locking failure occurred while exporting. Please try again later.")
          .build();
    }
    catch (IOException e) {
      log.error("IO Exception while exporting template file: ", e);
      throw new RuntimeException(e);
    }
    catch (Exception e) {
      log.error("Unexpected error occurred while exporting template file: ", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity("An unexpected error occurred. Please try again later.")
          .build();
    }
  }

  /**
   * Efficiently retrieves the BeanDescriptor by checking userTaskDefinition first.
   */
  private BeanDescriptor getBeanDescriptor(User user, Long portletId, Locale locale, String instanceCode) {
    UserTaskDefinition userTaskDefinition = userTaskDefinitionService.getUserTaskDefinitionByPortlet(user, portletId);
    if (userTaskDefinition != null && userTaskDefinition.getDescriptor() != null) {
      return userTaskDefinition.getDescriptor();
    }
    Object task = taskService.getTask(portletId, locale, instanceCode);
    return ((AbstractTask) task).getDescriptor();
  }

  @Override
  @Transactional
  public Response importExchanges(HttpServletRequest request, String envCode, Long portletId, String localeAsString, Attachment file) {

    Instance instance = instanceService.getInstanceByCode(envCode);

    if (instance == null) {
      log.error("Instance not found for environment code: {}", envCode);
      throw new RestJwtException(i18NService.findByCodeAndLocale("instance.not.found", locale),
          ExceptionType.NOT_FOUND);
    }

    String instanceCode = instance.getCode();
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);
    Company company = (Company) instance.getGroup();

    // Read CSV File
    List<List<String>> records = new ArrayList<>();
    try (BufferedReader br = new BufferedReader(new InputStreamReader(file.getDataHandler()
        .getInputStream()))) {
      String line;

      while ((line = br.readLine()) != null) {
        records.add(Arrays.asList(line.split(";")));
      }
    }
    catch (IOException e) {
      log.error("Unexpected error occurred while importing content of file: ", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(i18NService.findByCodeAndLocale("error.reading.file", locale, user.getFullname()) + e.getMessage())
          .build();
    }

    if (records.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(i18NService.findByCodeAndLocale("upload.file.empty", locale, user.getFullname()))
          .build();
    }

    // Extract header row from the uploaded CSV
    List<String> headers = records.get(0);

    // Normalize headers (trim & lowercase)
    List<String> normalizedHeaders = headers.stream()
        .map(header -> header.trim()
            .toLowerCase())
        .collect(Collectors.toList());

    // Determine the locale (default to English if not provided)
    localeAsString = (localeAsString != null && !localeAsString.isEmpty()) ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);

    // Retrieve task and descriptor
    BeanDescriptor descriptor = getBeanDescriptor(user, portletId, locale, instanceCode);

    // **Multi-locale descriptor label mapping**
    Map<String, String> descriptorLabelMap = new HashMap<>();
    descriptor.getProperties()
        .forEach(property -> {
          String propertyName = property.getName();
          String labelEn = toLabelSet(property.getLabel(), Locale.ENGLISH).getLabelValue(Locale.ENGLISH)
              .trim()
              .toLowerCase();
          String labelFr = toLabelSet(property.getLabel(), Locale.FRENCH).getLabelValue(Locale.FRENCH)
              .trim()
              .toLowerCase();

          descriptorLabelMap.put(labelEn, propertyName);
          descriptorLabelMap.put(labelFr, propertyName);
        });

    // **Reverse Mapping: CSV Headers to Property Names**
    Map<String, String> csvHeaderToPropertyMap = new HashMap<>();
    for (String header : normalizedHeaders) {
      for (Map.Entry<String, String> entry : descriptorLabelMap.entrySet()) {
        if (header.equals(entry.getKey())) {
          csvHeaderToPropertyMap.put(entry.getValue(), header);
          break;
        }
      }
    }

    List<Exchange> exchangeList = new ArrayList<>();
    List<String> errors = new ArrayList<>();
    int lineNumber = 1;

    for (List<String> row : records.subList(1, records.size())) {
      try {
        Map<String, String> rowData = new HashMap<>();
        for (int i = 0; i < headers.size(); i++) {
          rowData.put(normalizedHeaders.get(i), row.get(i)
              .trim());
        }

        // Validate required fields
        List<String> missingFields = new ArrayList<>();
        for (String field : REQUIRED_FIELDS) {
          String value = rowData.getOrDefault(csvHeaderToPropertyMap.get(field), "")
              .trim();
          if (value.isEmpty()) {
            // Get the corresponding label instead of the property code
            String label = descriptorLabelMap.entrySet()
                .stream()
                .filter(entry -> entry.getValue()
                    .equals(field))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(field); // Fallback to field if no label is found
            missingFields.add(label);
          }
        }

        if (!missingFields.isEmpty()) {
          errors.add(i18NService.findByCodeAndLocale("import.line", locale, user.getFullname()) +
              lineNumber + i18NService.findByCodeAndLocale("import.missing.fields", locale, user.getFullname()) + String.join(", ",
              missingFields));
          lineNumber++;
          continue; // Skip this row and move to the next
        }

        // Parse data
        String legalEntityId = rowData.get(csvHeaderToPropertyMap.get("id.legalEntity.code"));
        String dematPartnerId = rowData.get(csvHeaderToPropertyMap.get("id.dematPartner.code"));
        String kind = rowData.get(csvHeaderToPropertyMap.get("id.kind"));
        String directionStr = rowData.get(csvHeaderToPropertyMap.get("id.direction"));
        String identifier = rowData.get(csvHeaderToPropertyMap.get("id.identifier"));
        Date startDate = parseDate(rowData.get(csvHeaderToPropertyMap.get("start")));
        Date endDate = parseDate(rowData.get(csvHeaderToPropertyMap.get("end")));

        // Find entities
        Group legalEntity = securityService.getPartner(legalEntityId, company);
        Group dematPartner = securityService.getPartner(dematPartnerId, company);
        Exchange.Direction direction = Exchange.Direction.valueOf(directionStr);

        // Construct ID and Exchange entity
        ExchangeId exchangeId = new ExchangeId(legalEntity, dematPartner, kind, direction);
        exchangeId.setIdentifier(identifier);

        Exchange exchange = new Exchange(legalEntity, dematPartner, kind, direction, startDate, endDate);
        exchange.setId(exchangeId);

        // Additional validation
        if (exchange.getStart() == null) {
          errors.add(i18NService.findByCodeAndLocale("import.line", locale,
              user.getFullname()) + lineNumber + ": " + i18NService.findByCodeAndLocale("error_start_date", locale, user.getFullname()));
          lineNumber++;
          continue;
        }

        if (exchangeAlreadyExists(exchangeId)) {
          errors.add(
              i18NService.findByCodeAndLocale("import.line", locale, user.getFullname())
                  + lineNumber + ": " + i18NService.findByCodeAndLocale("error_saving_exchange_exist", locale, user.getFullname()));
          lineNumber++;
          continue;
        }

        try {
          securityService.addExchangeToGroupApi(exchange, legalEntity);
        }
        catch (Exception e) {
          errors.add(i18NService.findByCodeAndLocale("import.line", locale, user.getFullname())
              + lineNumber + ": " + i18NService.findByCodeAndLocale("error.adding.exchange", locale,
              user.getFullname()) + " - " + e.getMessage());
        }

        exchangeList.add(exchange);
      }
      catch (Exception e) {
        errors.add(i18NService.findByCodeAndLocale("import.line", locale, user.getFullname())
            + lineNumber + ": " + i18NService.findByCodeAndLocale("error.processing.file", locale, user.getFullname()));
      }
      lineNumber++;
    }

    if (!errors.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(i18NService.findByCodeAndLocale("import.with.error", locale, user.getFullname()) + String.join(", ", errors))
          .build();
    }

    return Response.ok(i18NService.findByCodeAndLocale("import.successful.file", locale, user.getFullname()) + exchangeList.size())
        .build();
  }

  @Override
  @Transactional
  public Response getUserAllLegalEntities(HttpServletRequest request, String envCode, Long portletId, String localeAsString) {
    User user = restCommonService.getUser(request, localeAsString);
    try {
      final boolean isPartner = isPartnerUser(user);
      Group primaryGroup = user.getPrimaryGroup();

      List<LegalEntityDTO> legalEntities = getLegalEntitiesList(user, localeAsString);

      List<LegalEntityDTO> legalEntitiesConfig = legalEntities.parallelStream()
          .collect(Collectors.toList());

      return Response.ok(getGson().toJson(new AccountingPostingDTO(
              isPartner ? primaryGroup.getCode() : null,
              legalEntitiesConfig
          )))
          .build();

    }
    catch (Exception e) {
      log.error("Error fetching accounting posting data: ", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(e.getMessage())
          .build();
    }
  }

  /**
   * return list of all entities
   *
   * @param user
   * @return
   */
  @Transactional
  public List<LegalEntityDTO> getLegalEntitiesList(User user, String localeAsString) {
    Objects.requireNonNull(user, "User cannot be null");

    OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    String scopeCodes = String.join("-", UserDetailsService.getUserScopeCodes(user));

    Set<String> legalEntities = computeUserLegalEntities(user, companyCode, scopeCodes);
    Company company = PrincipalHelper.getCompany(user);

    //case of admin user connected
    if (company == null && (legalEntities == null || legalEntities.isEmpty())) {
      Response.status(Response.Status.NOT_FOUND)
          .build();
      return Collections.emptyList();
    }

    List<LegalEntityDTO> legalEntitiesMapped = getAllLegalEntitiesForCompanyUser(company, localeAsString);
    return legalEntitiesMapped.stream()
        .sorted(Comparator.comparing(LegalEntityDTO::getName))
        .collect(Collectors.toList());
  }

  @Override
  public Response exportDematPartnerAsCsvAndPdfFile(HttpServletRequest request, String envCode, BqlUuidDto bqlList, String sortBy,
      Sort.Direction order, Long portletId, String localeAsString,
      String legalEntity, FileType fileType, boolean exportArchive) {

    try {
      String bql = "";
      if (bqlList != null && bqlList.getUuid() != null && !bqlList.getUuid()
          .isEmpty()) {
        StringJoiner joiner = new StringJoiner(" OR ");
        bqlList.getUuid()
            .forEach(element -> joiner.add("id.dematPartner.code = " + element));
        bql = joiner.toString();
      }
      return executeExportQuery(
          request,
          envCode, bql, sortBy,
          order, portletId, localeAsString,
          fileType, exportArchive,
          legalEntity);

    }
    catch (ObjectOptimisticLockingFailureException ex) {
      log.error("Optimistic locking failure occurred while exporting :", ex);
      return Response.status(Response.Status.CONFLICT)
          .entity("Optimistic locking failure occurred while exporting. Please try again later.")
          .build();
    }
    catch (Exception e) {
      log.error("Error executing export query logic:", e);
      throw new RestJwtException(e.getMessage(), ExceptionType.INTERNAL_ERROR);
    }
  }

  @Override
  public List<RevisionDTO> v1getRevisionsByExchangeId(String login, String envCode, Long portletId, HistoryDTO historyDTO, Locale locale) {
    User user = accountService.getUserByLoginOrOpenID(login);
    Instance instance = instanceService.getInstanceByCode(envCode);

    try {

      if (instance == null) {
        throw new RestJwtException(i18NService.findByCodeAndLocale("instance.not.found", locale, user.getFullname()),
            ExceptionType.NOT_FOUND);
      }

      Company company = (Company) instance.getGroup();

      if (historyDTO == null) {
        throw new RestJwtException(i18NService.findByCodeAndLocale("Exchange not found for ID", locale, user.getFullname()),
            ExceptionType.NOT_FOUND);
      }

      List<RevisionDTO> revisionsDtos = getRevisions(historyDTO, company, locale);

      return revisionsDtos;

    }
    catch (Exception e) {
      log.error("Unexpected error occurred while get revisions by exchange: ", e);
      throw new RestJwtException(i18NService.findByCodeAndLocale("Error fetching revisions", locale, user.getFullname()),
          ExceptionType.NOT_FOUND);
    }
  }

  private Response executeExportQuery(HttpServletRequest request,
      String envCode, String bql, String sortBy, Sort.Direction order, Long portletId,
      String localeAsString, FileType fileType, boolean exportArchive, String legalEntity) throws Exception {
    Sort.Direction directionOrder = null;
    boolean asyncExport = false;

    Instance instance = this.instanceService.getInstanceByCode(envCode);

    if (instance == null) {
      throw new RestJwtException(i18NService.findByCodeAndLocale("instance.not.found", locale),
          ExceptionType.NOT_FOUND);
    }

    localeAsString = localeAsString != null && !localeAsString.isEmpty() ? localeAsString : "en";
    Locale locale = new Locale(localeAsString);
    String instanceCode = instance.getCode();
    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);

    String contextPath = String.join(request.getServerName(), ":", String.valueOf(request.getServerPort()), request.getContextPath());
    TaskDto taskDto = portalServiceDelegator.getTask(login, envCode, portletId, localeAsString, 0, contextPath);

    if (!(taskDto.getTask() instanceof DematPartnerFileTaskDTO)) {
      throw new RestJwtException("task is not a DematPartnerFileTaskDTO", ExceptionType.NOT_FOUND);
    }

    DematPartnerFileTaskDTO dematPartnerFileTaskDTO = (DematPartnerFileTaskDTO) taskDto.getTask();
    UserTaskDefinitionDto userTaskDefinitionDto = taskDto.getUserTaskDefinition();

    BeanDescriptor descriptor = modelMapper.map(userTaskDefinitionDto != null ? userTaskDefinitionDto.getDescriptor()
        : dematPartnerFileTaskDTO.getDescriptor(), BeanDescriptor.class);

    FileHelper.createDownloadFolder(getDownloadDirectory());

    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
    String companyCode = userOrgs.getCompanyCode();
    if (legalEntity.isEmpty()) {
      legalEntity = PrincipalHelper.isPartnerUser(user) ? user.getPrimaryGroup()
          .getCode() : null;
    }

    QueryBuilder builder = buildDynamicQuery(legalEntity, parse(bql));

    final Query qb = builder.query();

    int itemsCount = Long.valueOf(securityService.countExchange(builder.query(false)))
        .intValue();

    if (itemsCount == 0)
      throw new RestJwtException(
          i18NService.findByCodeAndLocale("ddl_no_file_warning_message", RestServiceHelper.getLocaleFromRequest(request)),
          ExceptionType.NOT_FOUND);
    File file = null;
    if (order != null) {
      directionOrder = Sort.Direction.valueOf(order.toString());
    }

    int asynchronousLimit = dematPartnerFileTaskDTO.getAsynchronousListExportRange()
        .getMax();
    int documentExportRangeMax = dematPartnerFileTaskDTO.getDocumentExportRange()
        .getMax();
    int listExportRangeMax = dematPartnerFileTaskDTO.getListExportRange()
        .getMax();

    int max = Math.min(itemsCount, listExportRangeMax);
    int maxPdfExport = 1000;
    Function<Document, String> vBaseName = viewerBaseName(getLabel("gnxxcblcomlbls", "readable_export_file_name", "", locale), descriptor);

    if (fileType.name()
        .equals(CSV)) {
      if (itemsCount < asynchronousLimit) {
        // Synchronous download
        String dematPartnerFileName = "demat-partner-file-" + currentTimeMillis();
        file = processingDownloadFile(fileType, max, dematPartnerFileName, qb, sortBy, directionOrder, locale, companyCode,
            legalEntity,
            descriptor);
      }
      else {
        // Asynchronous download
        asyncExport = true;
        String url = request.getRequestURL()
            .toString();

        file = restCommonService.asynchronousDownload(listExportRangeMax, documentExportRangeMax, vBaseName,
            Exchange.class, fileType, instance, max, asynchronousLimit,
            createExportFileName(exportArchive, String.valueOf(fileType)
                .toLowerCase(), locale), descriptor, qb, sortBy, directionOrder, user, true, request, instanceCode, url, false, true, legalEntity);
      }
    }
    else if (fileType.name()
        .equals(PDF)) {
      if (itemsCount < maxPdfExport) {
        // Synchronous download
        file = downloadFile(fileType, itemsCount, instance, qb, sortBy, order, descriptor, locale, companyCode, legalEntity, maxPdfExport);
      }
      else {
        // Asynchronous download
        asyncExport = true;
        String url = request.getRequestURL()
            .toString();

        file = restCommonService.asynchronousDownload(listExportRangeMax, documentExportRangeMax, vBaseName,
            Exchange.class, fileType, instance, itemsCount, maxPdfExport,
            createExportFileName(exportArchive, String.valueOf(fileType)
                .toLowerCase(), locale), descriptor, qb, sortBy, directionOrder, user, true, request, instanceCode, url, false, true, legalEntity);
      }
    }
    if (asyncExport) {
      return ok(i18NService.findByCodeAndLocale("export_oversized_ddl_file_warning_message", locale)).build();
    }
    else {
      return ok(file, getType(file).getDefaultMime())
          .header("Content-Disposition", "attachment; filename=" + file.getName())
          .build();
    }
  }

  public List<RevisionDTO> getRevisions(HistoryDTO historyDTO, Company company, Locale locale) {

    Partner receiver = securityService.getPartner(historyDTO.getDematPartner()
        .getCode(), company);
    Partner sender = securityService.getPartner(historyDTO.getLegalEntity()
        .getCode(), company);

    ExchangeId exchangeId = new ExchangeId(
        sender,
        receiver,
        historyDTO.getKind(),
        historyDTO.getDirection()
    );

    ArrayList<Revision> revisions = new ArrayList<>();
    if (historyDTO != null) {
      QueryBuilder qbExchange = createBuilder("_entity_id = " + auditService.generateExchangeMongoUuid(exchangeId))
          .desc("timestamp");
      revisions.addAll(auditService.getRevisionsByQuery(qbExchange.query()));

      QueryBuilder qbLegalEntity = createBuilder("_entity_id = " + exchangeId
          .getLegalEntity()
          .getId()).desc("timestamp");
      revisions.addAll(auditService.getRevisionsByQuery(qbLegalEntity.query()));

      QueryBuilder qbLegalEntityLocation = createBuilder("_entity_id = " + exchangeId
          .getLegalEntity()
          .getLocation()
          .getId()).desc("timestamp");
      revisions.addAll(auditService.getRevisionsByQuery(qbLegalEntityLocation.query()));

      QueryBuilder qbDematPartner = createBuilder("_entity_id = " + historyDTO
          .getDematPartner()
          .getId()).desc("timestamp");
      revisions.addAll(auditService.getRevisionsByQuery(qbDematPartner.query()));

      QueryBuilder qbDematPartnerLocation = createBuilder("_entity_id = " + exchangeId
          .getDematPartner()
          .getLocation()
          .getId()).desc("timestamp");
      revisions.addAll(auditService.getRevisionsByQuery(qbDematPartnerLocation.query()));

      sort(revisions, TIMESTAMP_COMPARATOR.reversed());
    }

    revisions.forEach(rev -> {
      rev.setItems(rev.getItems()
          .stream()
          .filter(item -> !item.getNewValue()
              .equals(item.getOldValue()))
          .collect(Collectors.toList()));
      setRevisionEntityInfo(rev, exchangeId);
    });
    return revisions.stream()
        .map(rev -> convertToRevisionDTO(rev, locale))  // Use a conversion method
        .collect(Collectors.toList());
  }

  private RevisionDTO convertToRevisionDTO(Revision revision, Locale locale) {
    RevisionDTO dto = modelMapper.map(revision, RevisionDTO.class);
    dto.setEntityInfo(MessageHelper.getMessage("edctsklbls" + "." + revision.getEntityInfo(), "", locale));
    dto.setItems(revision.getItems()
        .stream()
        .map(item -> {
          RevisionItemDTO itemDTO = modelMapper.map(item, RevisionItemDTO.class);
          itemDTO.setNewValue(item.getNewValue() != null && !"null".equalsIgnoreCase(item.getNewValue()
              .toString())
              ? item.getNewValue()
              .toString()
              .trim()
              : "");
          itemDTO.setOldValue(item.getOldValue() != null && !"null".equalsIgnoreCase(item.getOldValue()
              .toString())
              ? item.getOldValue()
              .toString()
              .trim()
              : "");
          return itemDTO;
        })
        .collect(Collectors.toList()));
    return dto;
  }

  private void setRevisionEntityInfo(Revision revision, ExchangeId exchangeId) {
    if (revision.getEntityClass()
        .getName()
        .endsWith("Partner") ||
        revision.getEntityClass()
            .getName()
            .endsWith("Company")) {
      if (revision.getEntityId()
          .toString()
          .equals(exchangeId
              .getDematPartner()
              .getId())) {
        revision.setEntityInfo("demat_partner");
      }
      else {
        revision.setEntityInfo("legal_entity");
      }
    }
    else if (revision.getEntityClass()
        .getName()
        .endsWith("Location")) {
      if (revision.getEntityId()
          .toString()
          .equals(exchangeId
              .getDematPartner()
              .getLocation()
              .getId())) {
        revision.setEntityInfo("demat_partner");
      }
      else {
        revision.setEntityInfo("legal_entity");
      }
    }
  }

  private Function<Document, String> viewerBaseName(String baseFileName, BeanDescriptor desc) {
    return document -> Optional.of(document)
        .filter(com.byzaneo.xtrade.bean.Document::isIndexed)
        .map(com.byzaneo.xtrade.bean.Document::<RecapListItem>getIndexValue)
        .map(doc -> viewerBaseName(doc, baseFileName, desc))
        .orElseGet(document::getReference);
  }

  private String viewerBaseName(RecapListItem recapListItem, String baseFileName, BeanDescriptor desc) {
    return ofNullable(recapListItem).map(i -> new StringJoiner("-").add(baseFileName)
            .add(ofNullable(desc.get("documentNumber")).map(p -> p.getDisplayValue(i))
                .orElse(i.getEntityRef()))
            .add(ofNullable(desc.get("documentDate")).map(p -> p.getDisplayValue(i, null, null, PATTERN_DATE, null))
                .orElse(""))
            .toString())
        .orElse("document");
  }

  protected String getDownloadDirectory() {
    return TEMP_DIR + EXCHANGE_LIST_DIRECTORY_NAME;
  }

  protected String createExportFileName(boolean list, String type, Locale locale) {
    StringBuilder fileName = new StringBuilder("export");

    if (!list) {
      fileName.append("-")
          .append(getLabel("gnxxcblcomlbls", null, locale.equals(Locale.ENGLISH) ? "list" : "liste", getLocale()));
    }

    if (list && !isBlank(type)) {
      fileName.append("-")
          .append(type);
    }

    fileName.append("-")
        .append(new SimpleDateFormat(PATTERN_DATE).format(Calendar.getInstance()
            .getTime()))
        .append("-")
        .append(new SimpleDateFormat("HHmmss-SSSSSS").format(Calendar.getInstance()
            .getTime()));

    return fileName.toString();
  }

  private File downloadFile(FileType fileType, Integer itemsCount, Instance instance, Query qb, String sort, Sort.Direction order,
      BeanDescriptor descriptor, Locale locale, String companyCode, String legalEntity, int asynchronousLimit) {
    if (itemsCount == 0) {
      getCurrentInstance()
          .addCallbackParam("validationFailed", true);
      MessageHelper.addMessage(null, SEVERITY_WARN, "edctsklbls.ddl_no_file_warning_message");
      return null;
    }
    String dematPartnerFileName = "demat-partner-file-" + currentTimeMillis();

    return processingDownloadFile(fileType, itemsCount, dematPartnerFileName,
        qb, sort, Sort.Direction.valueOf(order.toString()), locale, companyCode, legalEntity, descriptor);
  }

  public File processingDownloadFile(FileType fileType, int count, String dematPartnerFileName, Query searchQuery,
      String sortField, Sort.Direction sortOrder, Locale locale, String companyCode,
      String legalEntity, BeanDescriptor descriptor) throws ServiceException {
    int limitFileSize = fileType == FileType.PDF ? pdfLimitFileSize : count;
    int totalFileNumber = (count + limitFileSize - 1) / limitFileSize;
    String baseFileName = dematPartnerFileName.substring(0, dematPartnerFileName.length() - 3);

    List<File> generatedFiles = new ArrayList<>();

    for (int i = 0; i < totalFileNumber; i++) {
      String fileName = baseFileName + "_" + i;
      int size = Math.min(limitFileSize, count - (i * limitFileSize));

      List<Exchange> exchangeList = getExchangesForEdition(i, size, sortField, sortOrder, searchQuery, legalEntity);
      if (exchangeList == null || exchangeList.isEmpty()) {
        log.warn("No item to process for file {}", fileName);
        continue;
      }

      try {
          generatedFiles.add(generateFile(fileType, exchangeList, fileName, locale, descriptor));
      }
      catch (IOException e) {
        throw new ServiceException("Error generating file: " + fileName, e);
      }
    }

    return generatedFiles.size() == 1 ? generatedFiles.get(0) : zipFiles(dematPartnerFileName, generatedFiles);
  }

  private File generateFile(FileType fileType, List<Exchange> exchangeList, String fileName, Locale locale, BeanDescriptor descriptor)
      throws IOException {
    switch (fileType) {
    case PDF:
      File exchangeXmlFile = generateExchangesEditionXmlFile(exchangeList, fileName, locale);
      File pdfFile = createRecapListFromTemplate(FileType.PDF, exchangeXmlFile, locale);
      deleteFile(exchangeXmlFile);
      return pdfFile;
    case CSV:
      return createCsvExchangeFile(fileType, exchangeList, fileName, locale, descriptor);
    default:
      return null;
    }
  }

  private void deleteFile(File file) {
    try {
      Files.deleteIfExists(file.toPath());
    }
    catch (IOException e) {
      log.warn("Failed to delete file {}: {}", file.getName(), e.getMessage());
    }
  }

  private File zipFiles(String zipFileName, List<File> files) {
    return FileHelper.zipList(TEMP_DIR + zipFileName + ".zip", getDownloadDirectory());
  }

  private String getExchangeKey(String propertyKey) {
    if (exportMapping == null) {
      initExportMapping();
    }
    return exportMapping.get(propertyKey);
  }

  private void initExportMapping() {
    this.exportMapping = new HashMap<>();
    exportMapping.put("id.dematPartner.fullname", "dematPartnerName");
    exportMapping.put("id.dematPartner.code", "dematPartnerCode");
    exportMapping.put("id.dematPartner.registration", "dematPartnerSiren");
    exportMapping.put("id.dematPartner.duns", "dematPartnerDuns");
    exportMapping.put("id.dematPartner.vat", "dematPartnerVATNumber");
    exportMapping.put("id.dematPartner.location.address.streetName", "dematPartnerAddress");
    exportMapping.put("id.dematPartner.location.address.postalCode", "dematPartnerPostalCode");
    exportMapping.put("id.dematPartner.location.address.city", "dematPartnerCity");
    exportMapping.put("id.dematPartner.location.address.country", "dematPartnerCountry");
    exportMapping.put("id.direction", "direction");
    exportMapping.put("id.kind", "messageType");
    exportMapping.put("start", "startDate");
    exportMapping.put("end", "endDate");
    exportMapping.put("id.dematPartner.creation", "dematPartnerCreationDate");
    exportMapping.put("id.legalEntity.creation", "legalEntityCreationDate");
    exportMapping.put("id.legalEntity.fullname", "legalEntityName");
    exportMapping.put("id.legalEntity.code", "legalEntityCode");
    exportMapping.put("id.legalEntity.registration", "legalEntitySiren");
    exportMapping.put("id.legalEntity.duns", "legalEntityDuns");
    exportMapping.put("id.legalEntity.vat", "legalEntityVATNumber");
    exportMapping.put("id.legalEntity.location.address.streetName", "legalEntityAddress");
    exportMapping.put("id.legalEntity.location.address.postalCode", "legalEntityPostalCode");
    exportMapping.put("id.legalEntity.location.address.city", "legalEntityCity");
    exportMapping.put("id.legalEntity.location.address.country", "legalEntityCountry");
  }

  private File createCsvExchangeFile(FileType fileType, List<Exchange> exchanges, String exchangeFileName, Locale locale,
      BeanDescriptor descriptor)
      throws IOException {
    File exchangesFileToDownload;
    exchangesFileToDownload = new File(getDownloadDirectory(),
        FilenameUtils.getBaseName(exchangeFileName) + fileType.getExtension());

    List<PropertyDescriptor> properties = descriptor.getProperties()
        .stream()
        .map(p -> {
          p.setName(getExchangeKey(p.getName()));
          return p;
        })
        .collect(Collectors.toList());
    descriptor.setProperties(properties);
    List<Object> exchangeEditionList = new ArrayList<>();
    exchanges.forEach(exchange -> exchangeEditionList.add(buildExchangeEdition(exchange, locale)));
    CsvExportHelper.exportAsCSV(exchangeEditionList, descriptor, new FileOutputStream(exchangesFileToDownload),
        locale, null);
    return exchangesFileToDownload;
  }

  private ExchangeEditionBean buildExchangeEdition(Exchange exchange, Locale locale) {
    ExchangeEditionBean exchangeEditionBean = new ExchangeEditionBean();
    if (exchange != null) {

      Group dematPartner = Optional.ofNullable(exchange)
          .map(Exchange::getId)
          .map(ExchangeId::getDematPartner)
          .orElse(null);

      Group legalEntity = Optional.ofNullable(exchange)
          .map(Exchange::getId)
          .map(ExchangeId::getLegalEntity)
          .orElse(null);

      if (dematPartner != null) {
        exchangeEditionBean.setDematPartnerName(Optional.of(dematPartner)
            .map(Group::getFullname)
            .orElse(null));
        exchangeEditionBean.setDematPartnerCode(Optional.of(dematPartner)
            .map(Group::getCode)
            .orElse(null));
        exchangeEditionBean.setDematPartnerSiren(Optional.of(dematPartner)
            .map(Group::getRegistration)
            .orElse(null));
        exchangeEditionBean.setDematPartnerDuns(Optional.of(dematPartner)
            .map(Group::getDuns)
            .orElse(null));
        exchangeEditionBean.setDematPartnerVATNumber(Optional.of(dematPartner)
            .map(Group::getVat)
            .orElse(null));
        exchangeEditionBean.setDematPartnerAddress(Optional.of(dematPartner)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getStreetName)
            .orElse(null));
        exchangeEditionBean.setDematPartnerPostalCode(Optional.of(dematPartner)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getPostalCode)
            .orElse(null));
        exchangeEditionBean.setDematPartnerCity(Optional.of(dematPartner)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getCity)
            .orElse(null));
        exchangeEditionBean.setDematPartnerCountry(Optional.of(dematPartner)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getCountry)
            .map(countryCode -> CustomLocaleMapper.getDisplayCountry(countryCode, locale))
            .orElse(null));

        exchangeEditionBean.setDematPartnerCountry(Optional.of(dematPartner)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getCountry)
            .map(countryCode -> CustomLocaleMapper.getDisplayCountry(countryCode, locale))
            .orElse(null));

        exchangeEditionBean.setDematPartnerCreationDate(Optional.of(dematPartner)
            .map(Group::getCreation)
            .orElse(null));

        exchangeEditionBean.setDirection(
            Optional.ofNullable(exchange)
                .map(Exchange::getId)
                .map(ExchangeId::getDirection)
                .map(Object::toString)
                .map(direction -> getMessage("." + direction, direction, locale)) // Pass values to getMessage()
                .orElse(null)
        );

        exchangeEditionBean.setMessageType(
            Optional.ofNullable(exchange)
                .map(Exchange::getId)
                .map(ExchangeId::getKind)
                .orElse(null)
        );

        exchangeEditionBean.setStartDate(
            Optional.ofNullable(exchange)
                .map(Exchange::getStart)
                .orElse(null)
        );

        exchangeEditionBean.setEndDate(
            Optional.ofNullable(exchange)
                .map(Exchange::getEnd)
                .orElse(null)
        );

        exchangeEditionBean.setLegalEntityCreationDate(Optional.of(legalEntity)
            .map(Group::getCreation)
            .orElse(null));

        exchangeEditionBean.setLegalEntityName(Optional.of(legalEntity)
            .map(Group::getFullname)
            .orElse(null));

        exchangeEditionBean.setLegalEntityCode(Optional.of(legalEntity)
            .map(Group::getCode)
            .orElse(null));

        exchangeEditionBean.setLegalEntitySiren(Optional.of(legalEntity)
            .map(Group::getRegistration)
            .orElse(null));

        exchangeEditionBean.setLegalEntityDuns(Optional.of(legalEntity)
            .map(Group::getDuns)
            .orElse(null));

        exchangeEditionBean.setLegalEntityVATNumber(Optional.of(legalEntity)
            .map(Group::getVat)
            .orElse(null));

        exchangeEditionBean.setLegalEntityAddress(Optional.of(legalEntity)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getStreetName)
            .orElse(null));

        exchangeEditionBean.setLegalEntityPostalCode(Optional.of(legalEntity)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getPostalCode)
            .orElse(null));

        exchangeEditionBean.setLegalEntityPostalCode(Optional.of(legalEntity)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getPostalCode)
            .orElse(null));

        exchangeEditionBean.setLegalEntityCity(Optional.of(legalEntity)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getCity)
            .orElse(null));

        exchangeEditionBean.setLegalEntityCountry(Optional.of(legalEntity)
            .map(Group::getLocation)
            .map(Location::getAddress)
            .map(Address::getCountry)
            .map(countryCode -> CustomLocaleMapper.getDisplayCountry(countryCode, locale))
            .orElse(null));
      }
    }
    return exchangeEditionBean;
  }

  private File generateExchangesEditionXmlFile(List<Exchange> exchanges, String exchangeFileName, Locale locale) {
    List<ExchangeEditionBean> exchangeEditionList = new ArrayList<>();
    exchanges.forEach(exchange -> exchangeEditionList.add(buildExchangeEdition(exchange, locale)));
    File exchangesEditionXmlFile = new File(FileUtils.getTempDirectory(), exchangeFileName + ".xml");
    try {
      JAXBContext jaxbContext = JAXBContext.newInstance(ExchangeEditionWrapper.class);
      Marshaller jaxbMarshaller = jaxbContext.createMarshaller();
      jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
      jaxbMarshaller.marshal(new ExchangeEditionWrapper(exchangeEditionList), exchangesEditionXmlFile);
    }
    catch (JAXBException e) {
      log.error("Unable to generate a corresponding xml file for exchanges:");
    }
    return exchangesEditionXmlFile;
  }

  private File createRecapListFromTemplate(FileType fileType, File recapListXmlFile, Locale userLocale) {
    File fileRecapList = new File(getDownloadDirectory() + FileSystems.getDefault()
        .getSeparator() + FilenameUtils.getBaseName(recapListXmlFile.getName()) + fileType.getExtension());
    Template template;
    if (StringUtils.isNotEmpty(templateUri)) {

      template = transformService.getTemplate(templateUri);
      Map<String, Object> config = new HashMap<>();
      config.put("uri", recapListXmlFile.toURI()
          .toString());
      try (final OutputStream os = new FileOutputStream(fileRecapList)) {
        transformService.transform(
            template,
            fileType,
            os,
            userLocale,
            null, null, null,
            config);
      }
      catch (ServiceException se) {
        log.error("Error transform", se);
        throw se;
      }
      catch (IOException e) {
        log.error("Error creating template", e);
        throw new ServiceException(e);
      }
    }
    return fileRecapList;
  }

  public List<Exchange> getExchangesForEdition(int start, int end, String sortField, Sort.Direction sortOrder, Query searchQuery,
      String legalEntity) {
    List<Exchange> exchanges = new ArrayList<>();
    org.springframework.data.domain.PageRequest pageable = isNotBlank(sortField)
        ? org.springframework.data.domain.PageRequest.of(start, end, ASCENDING.equals(sortOrder) ? ASC : DESC, sortField)
        : org.springframework.data.domain.PageRequest.of(start, end);

    QueryBuilder builder = buildDynamicQuery(legalEntity, searchQuery);
    Page<Exchange> page = securityService.searchExchange(builder.query(false), pageable);

    exchanges.addAll(page.getContent());
    return exchanges;
  }

  private Set<String> computeUserLegalEntities(User user, String companyCode, String scopeCodes) {
    if (isPartnerUser(user) || (!RestServiceHelper.isBackOfficeUserUser(user) && RestServiceHelper.isClientWithPerimeter(user))) {
      return Set.of(companyCode + "-" + scopeCodes);
    }
    return Collections.emptySet();
  }

  private List<LegalEntityDTO> getAllLegalEntitiesForCompanyUser(Company company, String localeAsString) {
    return securityService.getPartners(company)
        .stream()
        .map(partner -> transformPartnerToLegalEntityDTO(partner, localeAsString))
        .collect(Collectors.toList());
  }

  /**
   * Checks if an exchange already exists in the system.
   */
  private boolean exchangeAlreadyExists(ExchangeId exchangeId) {
    Query query = QueryBuilder.createBuilder()
        .and(
            Clauses.equal("id.legalEntity.id", exchangeId.getLegalEntity()
                .getId()),
            Clauses.equal("id.dematPartner.id", exchangeId.getDematPartner()
                .getId()),
            Clauses.equal("id.direction", exchangeId.getDirection()),
            Clauses.equal("id.kind", exchangeId.getKind())
        )
        .query();

    Page<Exchange> existingExchange = securityService.searchExchange(query, null);
    return existingExchange != null && existingExchange.hasContent();
  }

  public File prepareDownloadCSV(String fileName, BeanDescriptor desc, Locale userLocale, String userDateFormat,
      List<?> indexes, String instanceCode) throws IOException {

    File tmpFile = new File(FileUtils.getTempDirectory(), fileName);

    List<PropertyDescriptor> properties = desc.getProperties()
        .stream()
        .filter(PropertyDescriptor::getRendered)
        .filter(property -> ALLOWED_FIELDS.contains(property.getName()))
        .collect(Collectors.toList());

    List<String> columnLabels = properties.stream()
        .map(property -> toLabelSet(property.getLabel(), userLocale).getLabelValue(userLocale))
        .collect(Collectors.toList());

    String[] arrayOfLabels = columnLabels.toArray(new String[0]);

    ExcelExportHelper.exportAsCsvGeneric(null, desc, tmpFile, userLocale, userDateFormat, fileName, instanceCode, arrayOfLabels);

    return tmpFile;
  }

  private String generateSqlFromBqlNoColumnLimit(String bql, AbstractTask task) {
    return RestServiceHelper.prepareBqlForSql(bql, task);
  }

  private String escapeReservedKeywords(String bql) {
    if (StringUtils.isEmpty(bql)) {
      return bql;
    }
    return bql
        .replaceAll("\\bstart\\b", "\"start\"")
        .replaceAll("\\bend\\b", "\"end\"");
  }

  private QueryBuilder buildDynamicQuery(
      String legalEntity,
      Query bql
  ) {
    QueryBuilder builder = QueryBuilder.createBuilder(bql);
    builder.and(Clauses.clause("id.dematPartner.fullname", Operator.NOT_LIKE, "Removed"));
    if (!org.springframework.util.StringUtils.isEmpty(legalEntity)) {
      builder.and(Clauses.equal("id.legalEntity.code", List.of(legalEntity)));
    }
    else {
      builder.and(Clauses.equal("id.legalEntity.code", Collections.emptyList()));
      builder.and(Clauses.equal("id.dematPartner.code", Collections.emptyList()));
    }
    return builder;
  }

  private DematPartnerFileTask getDematPartnerFileTask(String portletId) {
    try {
      return (DematPartnerFileTask) taskService.getTask(Long.valueOf(portletId));
    }
    catch (Exception e) {
      throw new RestException("Error retrieving DematPartnerFileTask", e);
    }
  }

  private String getUserLegalEntity(User user) {
    return PrincipalHelper.isPartnerUser(user) ? user.getPrimaryGroup()
        .getCode() : null;
  }

  private Pageable createPageable(int limit, int offset, String sortBy, Sort.Direction order) {
    Sort sort = (order != null && order.isDescending()) ? Sort.by(Sort.Direction.DESC, sortBy) : Sort.by(Sort.Direction.ASC, sortBy);
    return PageRequest.of(offset, limit, sort);
  }

  private void logResults(List<Exchange> results) {
    results.forEach(System.out::println);
    log.debug("API result: {}", results);
  }

  private Response buildResponse(UUID requestId, List<Exchange> results, Page<Exchange> page, String portletId, String userLegalEntity) {
    return ok(getGson().toJson(getDematPartnerFileList(results, page, portletId, userLegalEntity)))
        .header("Request-Id", requestId.toString())
        .build();
  }

  private void handleException(Exception e, String bql) {
    String message = getRootCauseMessage(e);
    log.error("API eDocument search error: {} (q='{}', type='{}')", message, bql, DEMAT_PARTNER_FILE_TYPE);

    if (e instanceof SecurityException) {
      throw new RestSecurityException(e.getMessage());
    }
    else if (e instanceof BqlParseException || e instanceof IllegalArgumentException || e instanceof NoResultException || e instanceof NonUniqueResultException) {
      throw new RestException(e.getMessage());
    }
    else {
      throw new RestException("An unexpected error occurred: " + message);
    }
  }

  private AngularWrapperDTO getDematPartnerFileList(List<Exchange> exchangeList, Page<Exchange> page,
      String portletId,
      String userLegalEntity) {
    AngularWrapperDTO angularWrapperDTO = new AngularWrapperDTO();

    ModelMapper modelMapper = new ModelMapper();
    List<ExchangeDTO> exchangeDTOS = List.copyOf(exchangeList)
        .stream()
        .map(i -> modelMapper.map(i, ExchangeDTO.class))
        .collect(toList());

    angularWrapperDTO.setContent(exchangeDTOS);
    angularWrapperDTO.setTotal(exchangeDTOS.size());
    angularWrapperDTO.setUserLegalEntity(userLegalEntity);
    angularWrapperDTO.setTotalElements(page.getTotalElements());
    angularWrapperDTO.setTotalPages(page.getTotalPages());
    angularWrapperDTO.setPortletId(portletId);
    return angularWrapperDTO;
  }
}
