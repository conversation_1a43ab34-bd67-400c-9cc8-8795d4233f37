package com.byzaneo.generix.rest;

import com.byzaneo.generix.api.bean.*;
import com.byzaneo.security.bean.TechnicalUser;

import java.util.Date;

public interface StatusManagementService {

  String SERVICE_NAME = "gnxStatusManagementService";

  InvoiceStatusManagementResponse getChangedInvoiceStatus(
      InvoiceRequestWithPaginationDTO invoiceRequest,
      TechnicalUser technicalUser,
      Date start,
      Date end
  );
}
