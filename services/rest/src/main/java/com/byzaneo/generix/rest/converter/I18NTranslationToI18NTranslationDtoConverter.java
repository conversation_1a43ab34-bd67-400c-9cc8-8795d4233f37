package com.byzaneo.generix.rest.converter;

import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.generix.api.bean.portal.AccountingReferentialDto;
import com.byzaneo.generix.service.repository.bean.aap.AapReferential;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.generix.service.repository.service.translation.TranslationUserDto;
import lombok.RequiredArgsConstructor;
import org.modelmapper.Converter;
import org.modelmapper.spi.MappingContext;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class I18NTranslationToI18NTranslationDtoConverter implements Converter<I18NTranslation, I18NTranslationDto> {

    @Override
    public I18NTranslationDto convert(MappingContext<I18NTranslation, I18NTranslationDto> context) {
        I18NTranslation source = context.getSource();
        I18NTranslationDto destination = context.getDestination();

        if (destination == null) {
            destination = new I18NTranslationDto();
        }

        // Map fields
        destination.setId(source.getId());
        destination.setDefaultValue(source.getDefaultValue());
        destination.setCode(source.getCode());
        destination.setNewValue(source.getNewValue());
        destination.setLocale(source.getLocale());
        destination.setDefaultValueChangedAt(source.getDefaultValueChangedAt());
        destination.setNewValueChangedAt(source.getNewValueChangedAt());
        if(source.getNewValueChangedBy()!=null){
            TranslationUserDto user = new TranslationUserDto();
            user.setId(source.getNewValueChangedBy().getId());
            user.setFullname(source.getNewValueChangedBy().getFullname());
            destination.setNewValueChangedBy(user);
        } else {
            destination.setNewValueChangedBy(null);
        }
        destination.setI18NModuleId(source.getI18NModule().getId());
        destination.setLocaleAsString(source.getLocaleAsString());
        return destination;
    }
}
