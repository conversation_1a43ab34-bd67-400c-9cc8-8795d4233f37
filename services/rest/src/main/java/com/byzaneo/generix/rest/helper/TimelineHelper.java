package com.byzaneo.generix.rest.helper;

import java.io.Serializable;
import java.util.Locale;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;

import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.api.bean.portal.DocumentTimelineDto;

public class TimelineHelper implements Serializable {

  private static final long serialVersionUID = 1L;

  public final static String COMMENT_SEPARATOR = "COMMENT_SEPARATOR";

  public static String getMessage(final String messageKey, final String defaultMessage, final Locale locale, final Object... args) {
    return MessageHelper.getMessage(messageKey, defaultMessage, locale, args);
  }
  
  public static String getLabelForDesignationOfActors(String labelFamily, String key, DocumentTimelineDto timeline, Locale locale) {
    return getMessage(labelFamily + "." + key, " has designated " + timeline.getComment() + " as actor(s) of step " + timeline.getNumStep(), locale,
        timeline.getComment(), timeline.getNumStep());
  }
  
  public static String getLabelForAssignTask(String labelFamily, String key, DocumentTimelineDto timeline, Locale locale) {
    return getMessage(labelFamily + "." + key,
        "assigned a task to " + timeline.getRedirectUser() + " with the comment: " + timeline.getComment(), locale,
        timeline.getRedirectUser(), timeline.getComment());
  }

  public static String getLabelForAutomaticValidation(String labelFamily, String key, DocumentTimelineDto timeline, Locale locale) {
    int step = timeline.getNumStep() - 1;
    return getMessage(labelFamily + "." + key,
        "Automatic message: Number of reminder reach, the step " + step + " is validated automatically.", locale, step);
  }

  public static String getLabelForAutomaticReminder(String labelFamily, String key, DocumentTimelineDto timeline, Locale locale) {
    String reminderNb = timeline.getComment();
    return getMessage(labelFamily + "." + key,
        "Automatic message: Reminder N°" + reminderNb + " sent by email.", locale, reminderNb);
  }

  public static String getLabelForEpSubmitedAction(String labelFamily, String key, DocumentTimelineDto timeline, Locale locale) {
    String daysNb = timeline.getComment();
    return getMessage(labelFamily + "." + key,
        "offers to pay the invoice " + daysNb + " days before its due date in exchange for a discount.", locale, daysNb);
  }
  
  public static String getLabelMultipleCommentParameters(String labelFamily, String key, DocumentTimelineDto timeline, Locale locale) {
    return getLabelMultipleCommentParameters(labelFamily, key, timeline, "-", locale);
  }

  public static String getLabelMultipleCommentParameters(String labelFamily, String key, DocumentTimelineDto timeline, String separator,
      Locale locale) {
    Object[] args;
    if (timeline == null || timeline.getComment() == null || timeline.getComment()
        .isEmpty())
      return getMessage(labelFamily + "." + key, "", locale, "");
    args = timeline.getComment()
        .split(separator);
    return getMessage(labelFamily + "." + key, "", locale, args);
  }

  public static String getLabelMultipleCommentParametersByProfile(String labelFamily, String key, DocumentTimelineDto timeline,
      String separator,
      Locale locale) {
    Object[] args;
    if (timeline == null || timeline.getComment() == null || timeline.getComment()
        .isEmpty())
      return getMessage(labelFamily + "." + key, "", locale, "");
    args = timeline.getComment()
        .split(separator);
    if (args != null && args.length == 2)
      key = key + "_profile";
    return getMessage(labelFamily + "." + key, "", locale, args);
  }

  public static String getLabelMultipleCommentParametersByProfile(String labelFamily, String key, DocumentTimelineDto timeline,
      Locale locale) {
    return getLabelMultipleCommentParametersByProfile(labelFamily, key, timeline, "-", locale);
  }

  public static String getCommentSeparator() {
    return COMMENT_SEPARATOR;
  }

  public static String getActionMessageKey(String action, String comment, String user) {
    Function<String, Boolean> isCommentNotEmpty = c -> StringUtils.isNotEmpty(c);
    switch (action) {
      case "INIT": return "gnxxcblinvlbls.init_action_message";
      case "ATTACHFILE": return isCommentNotEmpty.apply(comment) ? "gnxxcblinvlbls.attatch_file_action_message_comment" : "gnxxcblinvlbls.attatch_file_action_message";
      case "REFUSE": return isCommentNotEmpty.apply(comment) ? "gnxxcblinvlbls.refuse_action_message_comment" : "gnxxcblinvlbls.refuse_action_message";
      case "REDIRECT": return isCommentNotEmpty.apply(comment) ? "gnxxcblinvlbls.transferTask_action_message_comment" : "gnxxcblinvlbls.transferTask_action_message";
      case "CUSTOM": return "gnxxcblinvlbls.custom_action_message";
      case "CUSTOM_ACTION": return "gnxxcblinvlbls.custom_action_message";
      case "TASK_DONE": return "gnxxcblinvlbls.give_back_control_action_message";
      case "TASK_CANCEL": return "gnxxcblinvlbls.cancel_assign_task_action_message";
      case "LOCK": return "gnxxcblinvlbls.lock_action_message";
      case "UNLOCK": return "gnxxcblinvlbls.unlock_action_message";
      case "VALID": return "gnxxcblinvlbls.validate_action_message";
      case "END": return "gnxxcblinvlbls.end_action_message";
      case "WKF_EXIT": return "gnxxcblinvlbls.wkf_exit_action_message";
    case "ENRICH":
      return "gnxxcblinvlbls.enrich_action_message";
    case "EDIT":
      return "gnxxcblinvlbls.edit_action_message";
    case "FIX":
      return "gnxxcblinvlbls.fix_action_message";
    case "RECONCILIATION_START":
      return "gnxxcblinvlbls.reconciliation_start_message";
    case "RECONCILIATION_EMPTY_SELLER_PARTY":
      return "gnxxcblinvlbls.reconciliation_empty_seller_party_message";
    case "RECONCILIATION_EMPTY_ORDER_NUMBER":
      return "gnxxcblinvlbls.reconciliation_empty_order_number_message";
    case "RECONCILIATION_ORDER_NOT_FOUND":
      return "gnxxcblinvlbls.reconciliation_order_not_found_message";
    case "RECONCILIATION_EMPTY_RECEPTION_NUMBER":
      return "gnxxcblinvlbls.reconciliation_empty_reception_number_message";
    case "RECONCILIATION_RECEPTION_NOT_FOUND":
      return "gnxxcblinvlbls.reconciliation_reception_not_found_message";
    case "RECONCILIATION_GAP_FOUND":
      return "gnxxcblinvlbls.reconciliation_gap_found_message";
    case "RECONCILIATION_SUCCESS":
      return "gnxxcblinvlbls.reconciliation_success_message";
    case "RECONCILIATION_RELAUNCH":
      return "gnxxcblinvlbls.relaunch_reconciliation_message";
    case "CONSULT":
      return "gnxxcblinvlbls.consult_message";
    case "DISCONNECTED":
      return "gnxxcblinvlbls.disconnected_action_message";
    case "COMMENT":
      return user != "System" ? "gnxxcblinvlbls.comment_action_message" : "gnxxcblinvlbls.comment_automatic_message";
    case "PAUSE":
      return "gnxxcblinvlbls.stopNotification_message";
    case "CTRL_RULES_FR":
      return "gnxxcblinvlbls.control_fr_message";
    case "CTRL_RULES_EU":
      return "gnxxcblinvlbls.control_eu_message";
    case "CTRL_DUPLICATE":
      return "gnxxcblinvlbls.control_duplicate_message";
    case "LEGAL_ACTION_COMPLETED":
      return "gnxxcblinvlbls.legal_action_completed";
    case "LEGAL_ACTION_PAYMENT_RECEIVED":
      return "gnxxcblinvlbls.legal_action_payment_received";
    case "LEGAL_ACTION_APPROVED":
      return "gnxxcblinvlbls.legal_action_approved";
    case "LEGAL_ACTION_REFUSED":
      return "gnxxcblinvlbls.legal_action_refused";
    case "LEGAL_ACTION_APPROVED_SUBROGATE":
      return "gnxxcblinvlbls.legal_action_approved_subrogate";
    case "LEGAL_ACTION_PAYMENT_SENT":
      return "gnxxcblinvlbls.legal_action_payment_sent";
    case "LEGAL_ACTION_FACTOR":
      return "gnxxcblinvlbls.legal_action_factor";
    case "LEGAL_ACTION_FACTOR_CONFIDENTIAL":
      return "gnxxcblinvlbls.legal_action_factor_confidential";
    case "AAP_ALREADY_COMPLETED":
      return "gnxxcblinvlbls.aap_already_completed";
    case "AAP_COMPLETED_AUTOMATICALLY":
      return "gnxxcblinvlbls.aap_completed_automatically";
    case "AAP_EXPORTED":
      return "gnxxcblinvlbls.aap_exported";
    case "OCR_VERIFY":
      return "gnxxcblinvlbls.ocr_verify_action_message";
    case "OCR_POSTPONED":
      return "gnxxcblinvlbls.ocr_postponed_action_message";
    default:
      return "";
    }
  }

}
