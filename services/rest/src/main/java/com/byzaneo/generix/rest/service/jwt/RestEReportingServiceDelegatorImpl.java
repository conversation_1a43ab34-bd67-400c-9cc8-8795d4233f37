package com.byzaneo.generix.rest.service.jwt;

import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.commons.util.GsonHelper;
import com.byzaneo.generix.api.bean.payment.EReportingPaymentsDTO;
import com.byzaneo.generix.api.bean.transaction.EReportingTransactionDTO;
import com.byzaneo.generix.api.bean.transmission.*;
import com.byzaneo.generix.api.service.internal.delegators.RestEReportingServiceDelegator;
import com.byzaneo.generix.rest.*;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.service.repository.util.SortOrderDto;
import com.byzaneo.generix.util.*;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.BqlParseException;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.transmission.*;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Component;

import javax.persistence.*;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.getValueAwesomeBootstrap;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.toStageItem;
import static com.byzaneo.query.builder.QueryBuilder.parse;
import static com.byzaneo.security.util.PrincipalHelper.isPartnerUser;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;

@Component
@RequiredArgsConstructor
public class RestEReportingServiceDelegatorImpl implements RestEReportingServiceDelegator {

  private static final Logger log = getLogger(RestEReportingServiceDelegatorImpl.class);
  @Autowired
  @Qualifier(AccountService.SERVICE_NAME)
  private AccountService accountService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  @Autowired
  private ModelMapper modelMapper;

  public static final String LABEL_FAMILY = "gnxxcblinvlbls";

  @Override
  public Response getTransmissions(HttpServletRequest request, String bql, UUID requestId, TransmissionTypeCoded transmissionType,
      Integer limit, Integer offset, String sortBy, com.byzaneo.generix.api.bean.SortOrderDto order, boolean count, String portletId) {
    if (requestId == null) {
      requestId = UUID.randomUUID();
    }

    String login = RestServiceHelper.getUserFromRequest(request);
    User user = accountService.getUserByLoginOrOpenID(login);

    // - check API right -
    // TODO create API right (Api_permissions_get_transmission) and use it with @Secured annotation
        /* if (!permissionHelper.isGranted(SecurityService.Resource.Api_permissions_get_transmission, Right.READ, user) || user.isDisabled()) {
          return getResponseOnError("403", "Security error: You don't have the rights to access to the API.", Response.Status.FORBIDDEN,
              requestId);
        } */
    if (limit == null) limit = -1;
    if (offset == null) offset = 0;
    log.debug("Searching {} eDocuments (page={}, size={})", bql, offset, limit);

    // - search for transmissions -
    try {
      // get companyCode and perimeter for users
      final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(user);
      String companyCode = userOrgs.getCompanyCode();
      Collection<String> perimeter = userOrgs.getCodes();
      final Query qb = OrganizationHelper.resolveUserOrganizationsQuery(companyCode, perimeter, parse(bql), AbstractTask.FIELD_OWNERS,
              AbstractTask.FIELD_FROM, AbstractTask.FIELD_TO, AbstractTask.FIELD_THIRD_PARTY)
          .query();

      Sort sort = Sort.unsorted();
      if (sortBy != null && order != null) {
        sort = order.equals(SortOrderDto.ASC)
            ? Sort.by(sortBy)
            .ascending()
            : Sort.by(sortBy)
                .descending();
      }
      final PageRequest pageable = PageRequest.of(offset < 0 ? 0 : offset, limit < 1 || limit > 100 ? 100 : limit, sort);

      log.info("RequestId {} : API {} is searching {} edocuments ({})", requestId, user.getLogin(), qb, pageable);

      List<Transmission> result;
      long totalElements = 0;
      long totalPage = 0;
      if (count == true) {
        long totalCount;
        totalCount = this.documentService.countIndexable(Transmission.class, qb);
        return Response.ok(GsonHelper.getGson()
                .toJson(totalCount))
            .header("Request-Id", requestId.toString())
            .build();
      }
      else {
        Page<Transmission> page = this.documentService.searchIndexables(Transmission.class, qb, pageable);
        result = page.getContent();
        totalElements = page.getTotalElements();
        totalPage = page.getTotalPages();
        log.debug("API result: {}", result);
        return Response.ok(GsonHelper.getGson()
                .toJson(getTransmissionList(result, limit, offset, totalElements, totalPage, portletId)))
            .header("Request-Id", requestId.toString())
            .build();
      }

    }
    catch (SecurityException e) {
      log.error("API eDocument search error: {} (q='{}', type='{}')", e.getMessage(), bql, transmissionType);
      throw new RestSecurityException(e.getMessage());
    }
    catch (BqlParseException | IllegalArgumentException | NoResultException | NonUniqueResultException e) {
      log.error("API eDocument search error: {} (q='{}', type='{}')", e.getMessage(), bql, transmissionType);
      throw new RestException(e.getMessage());
    }
    catch (Exception e) {
      String message = getRootCauseMessage(e);
      log.error("API eDocument search error: {} ( q='{}', type='{}')", message, bql, transmissionType);
      throw new RestException("An unexpected error occurs: " + message);
    }
  }

  public TransmissionListDTO getTransmissionList(List<Transmission> transmissions, Integer limit, Integer offset, long totalElements,
      long totalPages, String portletId) {
    List<TransmissionDTO> transmissionDTO = new ArrayList<>();
    DocumentStatusService documentStatusServiceBean = getBean(DocumentStatusService.class, DocumentStatusService.SERVICE_NAME);
    transmissionDTO = transmissions.stream()
        .map(i -> {
          TransmissionDTO transmissionDTO1 = modelMapper.map(i, TransmissionDTO.class);
          TransmissionDTO.ArchiveStatusEnum archiveStatusEnum = TransmissionDTO.ArchiveStatusEnum.fromValue(i.getArchiveStatus());
          String labelValue = archiveStatusEnum != null ? archiveStatusEnum.getValue() : "";
          String labelStatus = MessageHelper.getMessage(LABEL_FAMILY + "." + labelValue, "", Locale.FRENCH);
          DocumentStatusEntity documentStatusEntity = documentStatusServiceBean.getDocumentStatusByCode(i.getStatusAsString());
          String status = DocumentStyleHelper.resolveStatusLabelText(documentStatusEntity, Locale.FRENCH, "GIS");
          transmissionDTO1.setStatus(Map.of("code", i.getStatus()
              .getStatusCode(), "value", status, "styleClass", getValueAwesomeBootstrap(documentStatusEntity, "GIS")));

          transmissionDTO1.setStage(Map.of("value", toStageItem(TransmissionDTO.StageEnum.fromValue(i.getStage())
              .getValue(), Locale.FRENCH), "name", i.getStage()
              .name(), "styleClass", getValueAwesomeBootstrap(i.getStage())));
          transmissionDTO1.setArchiveStatus(archiveStatusEnum != null
              ? Map.of("value", labelStatus, "styleClass", getValueAwesomeBootstrap(i.getArchiveStatus()))
              : null);
          return transmissionDTO1;
        })
        .collect(Collectors.toList());
    TransmissionListDTO transmissionList = new TransmissionListDTO();
    transmissionList.setContent(transmissionDTO);
    transmissionList.setTotal(transmissions.size());
    TransmissionListPageableDTO transmissionListPageable = new TransmissionListPageableDTO();
    transmissionListPageable.setLimit(limit);
    transmissionListPageable.setOffset(offset);
    transmissionList.setPageable(transmissionListPageable);
    transmissionList.setTotalElements(totalElements);
    transmissionList.setTotalPages(totalPages);
    transmissionList.setPortletId(portletId);
    return transmissionList;
  }

  @Override
  public Response addTransaction(HttpServletRequest request, EReportingTransactionDTO eReportingTransactionDTO) {
    try {
      final Document document = new Document();

      EReportingTransaction eReportingTransaction = new EReportingTransaction();
      EReportingTransactionType transactionType = new EReportingTransactionType();

      transactionType.setDate(eReportingTransactionDTO.getDate());
      transactionType.setTransactionsCurrency(eReportingTransactionDTO.getTransactionsCurrency());
      transactionType.setTaxDueDateTypeCode(TaxDueDateTypeCodeType.fromValue(eReportingTransactionDTO.getTaxDueDateTypeCode()));
      transactionType.setCategoryCode(TransmissionCategoryCodeType.fromValue(eReportingTransactionDTO.getCategoryCode()));
      transactionType.setTaxExclusiveAmount(eReportingTransactionDTO.getTaxExclusiveAmount());
      transactionType.setTaxTotal(eReportingTransactionDTO.getTaxTotal());
      transactionType.setTransactionsCount(eReportingTransactionDTO.getTransactionsCount());
      transactionType.setTaxSubtotal(eReportingTransactionDTO.getTaxSubtotal()
          .stream()
          .map(i -> modelMapper.map(i, TransactionTaxSubTotalType.class))
          .collect(Collectors.toList()));
      eReportingTransaction.setTransactions(transactionType);
      document.setIndexValue(eReportingTransaction);
      documentService.saveDocument(document);

      return RestServiceHelper.getResponse(Response.Status.OK, eReportingTransaction);
    }
    catch (Exception e) {
      return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
    }
  }

  @Override
  public Response addPayment(HttpServletRequest request, EReportingPaymentsDTO eReportingPaymentDto) {
    try {
      final Document document = new Document();

      EReportingPayment eReportingPayment = new EReportingPayment();
      PaymentType paymentType = new PaymentType();
      EReportingPaymentType eReportingPaymentType = new EReportingPaymentType();
      String login = RestServiceHelper.getUserFromRequest(request);

      User user = accountService.getUserByLoginOrOpenID(login);
      String userLegalEntity = isPartnerUser(user) ? user.getPrimaryGroup()
          .getCode() : null;

      paymentType.setSubTotals(eReportingPaymentDto.getTaxSubtotal()
          .stream()
          .map(i -> modelMapper.map(i, PaymentSubtotalType.class))
          .collect(Collectors.toList()));
      String owner = isPartnerUser(user)
          ? user.getPrimaryGroup()
          .getParent()
          .getCode()
          : user.getPrimaryGroup()
              .getCode();
      document.setOwners(owner);
      document.setFrom(user.getLogin());
      document.setReference("Payment");
      document.setType("eReportingPayments");
      eReportingPaymentType.setPayment(paymentType);
      eReportingPaymentType.setIdDeclarant(userLegalEntity);
      eReportingPaymentType.setInvoiceId(eReportingPaymentDto.getInvoiceId());
      eReportingPaymentType.setIssueDate(eReportingPaymentDto.getDateInvoice());
      eReportingPaymentType.setDate(eReportingPaymentDto.getDatePayment());
      eReportingPayment.setPayments(eReportingPaymentType);

      document.setIndexValue(eReportingPayment);
      documentService.saveDocument(document);

      return RestServiceHelper.getResponse(Response.Status.OK, eReportingPayment);
    }
    catch (Exception e) {
      return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
    }
  }
}
