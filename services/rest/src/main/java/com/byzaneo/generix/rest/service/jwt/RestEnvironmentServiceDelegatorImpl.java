/*
 * Copyright (c) 2023.
 * created by <PERSON><PERSON>
 */

package com.byzaneo.generix.rest.service.jwt;

import com.byzaneo.angular.service.*;
import com.byzaneo.commons.ui.ApplicationHandler;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.service.internal.delegators.RestEnvironmentServiceDelegator;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.rest.helper.RestServiceHelper;
import com.byzaneo.generix.service.*;
import com.byzaneo.generix.service.repository.bean.address.Address;
import com.byzaneo.generix.service.repository.service.*;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.AccountService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.*;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.*;

import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static javax.ws.rs.core.Response.ok;

@Component
@RequiredArgsConstructor
public class RestEnvironmentServiceDelegatorImpl implements RestEnvironmentServiceDelegator {

    @Autowired
    @Qualifier(InstanceService.SERVICE_NAME)
    private InstanceService instanceService;

    @Autowired
    @Qualifier(AddressService.SERVICE_NAME)
    protected AddressService addressService;

    @Autowired
    @Qualifier(AccountService.SERVICE_NAME)
    private AccountService accountService;

    @Autowired
    @Qualifier(RestGenericService.SERVICE_NAME)
    private RestGenericService restGenericService;

    @Autowired
    @Qualifier(I18NService.BEAN_NAME)
    private I18NService i18NService;

    @Autowired
    @Qualifier(ValuesControlService.SERVICE_NAME)
    private transient ValuesControlService valuesControlService;

    ModelMapper modelMapper = new ModelMapper();

    @Override
    public Response getValuesControl(String listId) {
        if (listId == null) {
            throw new RestJwtException("list id should not be null", ExceptionType.BAD_REQUEST);
        }
        ValuesControl valuesControl = this.valuesControlService.getValuesControlByListId(listId);
        if (valuesControl == null) {
            throw new RestJwtException("values control not found", ExceptionType.NOT_FOUND);
        }
        ValuesControlDto valuesControlDto = modelMapper.map(valuesControl, ValuesControlDto.class);
        return RestServiceHelper.getResponse(Response.Status.OK, valuesControlDto);
    }

    @Override
    public Response getEnvironmentConfiguration(HttpServletRequest request, String envCode) {
        Instance instance = this.instanceService.getInstanceByCode(envCode);
        if (instance == null) {
            throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
        }
        String serverName = request.getServerName();
        Host host = instanceService.resolveHost(serverName);
        String login = SecurityContextHolder.getContext().getAuthentication().getName();
        User user = accountService.getUserByLoginOrOpenID(login);
        Locale currentLocale = instance.getConfiguration().getDefaultLanguage();

        try {
            instanceService.resolveInstance(host, user);
        } catch (Exception e) {
            String translatedMessage = i18NService.findByCodeAndLocale(
                "user_not_authorised_for_this_domain", currentLocale, login
            );
            String messageWithLocale = String.format(
                "{\"message\": \"%s\", \"locale\": \"%s\"}",
                translatedMessage,
                currentLocale.toLanguageTag()
            );
            throw new RestJwtException(messageWithLocale, ExceptionType.FORBIDDEN);
        }

        Boolean selfRegister = instance
                .getConfiguration()
                .getSelfRegister();
        String selfRegisterPh1 = restGenericService.getEnabledInstancePropertt(InstanceProperties::getSelfRegisterPh1, null, null, request);
        String selfRegisterPh2 = restGenericService.getEnabledInstancePropertt(InstanceProperties::getSelfRegisterPh2, null, null, request);
        String defaultLanguage = instance.getConfiguration().getDefaultLanguage().getLanguage();
        List<Locale> languages = instance.getConfiguration().getLanguages();
        EnvironmentConfigurationDTO environmentConfigurationDTO = new EnvironmentConfigurationDTO(selfRegister, selfRegisterPh1,
                selfRegisterPh2, defaultLanguage, languages);

        return RestServiceHelper.getResponse(Response.Status.OK, environmentConfigurationDTO);
    }

    @Override
    public Response getPartnerAddresses(HttpServletRequest request, String envCode) {
        Instance instance = this.instanceService.getInstanceByCode(envCode);
        if (instance == null) {
            throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
        }
        Locale currentLocale = instance.getConfiguration().getDefaultLanguage();
        String login = SecurityContextHolder.getContext().getAuthentication().getName();
        User user = accountService.getUserByLoginOrOpenID(login);
        if (user.getSwitchUserLogin() != null) {
            user = accountService.getUserByLoginOrOpenID(user.getSwitchUserLogin());
        }
        if (Objects.isNull(user)) {
            return RestServiceHelper.getResponse(Response.Status.NOT_FOUND, i18NService.findByCodeAndLocale("user_not_found", currentLocale, login));
        }
        try {
            Group group = user.getPrimaryGroup();
            if (Objects.isNull(group)) {
                return RestServiceHelper.getResponse(Response.Status.NOT_FOUND, i18NService.findByCodeAndLocale("group_not_found_for_user", currentLocale, login));
            }

            List<Address> partnerAddresses;
            if (group instanceof Company)
                partnerAddresses = new ArrayList<>();
            else
                partnerAddresses = addressService.findPartnerAddresses(group);
            return RestServiceHelper.getResponse(Response.Status.OK, partnerAddresses);
        } catch (Exception e) {
            return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }

    }

    @Override
    public Response getCountry(HttpServletRequest request, String envCode) {
        Instance instance = this.instanceService.getInstanceByCode(envCode);
        if (instance == null) {
            throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
        }
        Locale currentLocale = instance.getConfiguration().getDefaultLanguage();
        List<SelectItem> countries = RestServiceHelper.getCountryItems();
        if (CollectionUtils.isEmpty(countries)) {
            return RestServiceHelper.getResponse(Response.Status.NOT_FOUND, i18NService.findByCodeAndLocale("countries_not_found", currentLocale));
        }
        return RestServiceHelper.getResponse(Response.Status.OK, countries);
    }


    @Override
    public Response getVersion(HttpServletRequest request) {
        try {
            String version = ApplicationHandler.appVersion;
            Map<String, String> versionMap = new HashMap<>();
            versionMap.put("version", version);

            return RestServiceHelper.getResponse(Response.Status.OK, versionMap);
        } catch (Exception e) {
            return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    @Override
    public Response getApiUrl(HttpServletRequest request, String url) {
        try {
            Host host = this.instanceService.getHostByAngularUrl(url);
            Instance instance = this.instanceService.resolveInstance(host);
            KeycloakConfigDTO keycloakDataDTO = new KeycloakConfigDTO(this.instanceService.getAuthenticationServerConfiguration(instance, url, true));

            // application type : Development | Acceptance | Preproduction | Production
            final String applicationType = getConfigurationService().getProperties()
                    .getProperty("app.type")
                    .toLowerCase();

            String predomain = instanceService.resolvePartnerAuthenticationPreDomain(instance, host.getAngularUrl(), url);
            JwtDTO userDto = JwtDTO.builder()
                    .apiUrl(host.getName())
                    .togetherCase(StringUtils.isNotBlank(predomain))
                    .predomain(predomain)
                    .environment(instance.getCode())
                    .backendApplicationType(applicationType)
                    .keycloakConfig(keycloakDataDTO)
                    .build();
            return ok(userDto).build();
        } catch (Exception e) {
            return RestServiceHelper.getResponseOnError("500", "Error while retrieving angular url from instance.",
                    Response.Status.INTERNAL_SERVER_ERROR,
                    null);
        }
    }

    @Override
    public Response getFactorPartnerAddresses(HttpServletRequest request, String envCode) {
        Instance instance = this.instanceService.getInstanceByCode(envCode);
        if (instance == null) {
            throw new RestJwtException("instance not found", ExceptionType.NOT_FOUND);
        }
        Locale currentLocale = instance.getConfiguration().getDefaultLanguage();
        String login = SecurityContextHolder.getContext().getAuthentication().getName();
        User user = accountService.getUserByLoginOrOpenID(login);
        if (user.getSwitchUserLogin() != null) {
            user = accountService.getUserByLoginOrOpenID(user.getSwitchUserLogin());
        }
        if (Objects.isNull(user)) {
            return RestServiceHelper.getResponse(Response.Status.NOT_FOUND, i18NService.findByCodeAndLocale("user_not_found", currentLocale, login));
        }
        try {
            Group group = user.getPrimaryGroup();
            if (Objects.isNull(group)) {
                return RestServiceHelper.getResponse(Response.Status.NOT_FOUND, i18NService.findByCodeAndLocale("group_not_found_for_user", currentLocale, login));
            }

            List<Address> partnerAddresses = addressService.findPartnerFactorAddresses(group);
            return RestServiceHelper.getResponse(Response.Status.OK, partnerAddresses);
        } catch (Exception e) {
            return RestServiceHelper.getResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }
}
