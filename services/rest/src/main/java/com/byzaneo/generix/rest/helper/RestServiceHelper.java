package com.byzaneo.generix.rest.helper;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.byzaneo.angular.service.*;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.portal.InvoiceCorrectionDTO;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.bean.xcbl.XcblPath;
import com.byzaneo.generix.rest.RestSecurityException;
import com.byzaneo.generix.service.InstanceService;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.service.repository.bean.aap.*;
import com.byzaneo.generix.util.*;
import com.byzaneo.generix.xtrade.task.AbstractTask;
import com.byzaneo.security.api.Organization;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.security.spring.UserDetails;
import com.byzaneo.security.spring.UserDetailsService;
import com.byzaneo.security.util.PrincipalHelper;
import com.byzaneo.xtrade.bean.DocumentStatusEntity;
import com.byzaneo.xtrade.service.DocumentStatusService;
import com.byzaneo.xtrade.xcbl.api.XcblDocument;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.util.CustomLocaleMapper;
import com.fasterxml.jackson.databind.*;
import com.google.gson.*;
import com.google.gson.internal.bind.TypeAdapters;
import com.google.gson.stream.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.saml.SAMLCredential;
import org.springframework.stereotype.Component;

import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import javax.validation.*;
import javax.ws.rs.core.Response;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATA_DIR;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.getValueAwesomeBootstrap;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.toStageItem;
import static com.byzaneo.security.service.SecurityService.GroupName.ADMIN;
import static com.byzaneo.security.util.PrincipalHelper.isCompanyUser;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.Locale.getDefault;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.io.FileUtils.getTempDirectory;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.security.core.context.SecurityContextHolder.getContext;

/**
 * Helper for Rest Service exposed to users.
 *
 * <AUTHOR> Boutour
 * @date 05/08/2015
 */
@Component
public abstract class RestServiceHelper {
  private static final Logger logger = getLogger(RestServiceHelper.class);

  private static ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
  private static Validator validator = validatorFactory.getValidator();

  private static final Gson gson = new GsonBuilder().serializeNulls()
      .setPrettyPrinting()
      .create();
  public static DocumentStatusService documentStatusServiceBean = getBean(DocumentStatusService.class, DocumentStatusService.SERVICE_NAME);

  public static final ModelMapper modelMapper = new ModelMapper();

  public static final String LABEL_FAMILY = "gnxxcblinvlbls";

  private static InstanceService instanceService;

    private  final SecurityService securityService;

  /**
   * Check if Principal user of Spring's context rely to the specified instance.
   *
   * @param instance Instance
   * @return Credential if authorization is ok
   * @throws RestSecurityException If User has no access to Instance
   */
  public static Credential checkInstanceUser(HttpServletRequest request, final Instance instance, boolean isCompanyOnly) {
    notNull(instance, "Environment not found (check instance code)");

    final User user = getAndCheckPrincipalUser(request);
    final Group org = user.getPrimaryGroup();

        if (isCompanyOnly) {
            isTrue(org instanceof Organization && instance.getGroup()
                            .equals(org),
                    "Authenticated user's organizaton is not managing environment: " + instance.getCode());
        }
        else {
            boolean isAdministrator = CollectionUtils.containsAny(PrincipalHelper.getGroupNameList(user.getGroups()), asList(ADMIN.toString()));
            isTrue(isAdministrator || org instanceof Organization && (instance.getGroup()
                            .equals(org) ||
                            instance.getGroup()
                                    .equals(org.getParent())),
                    "Authenticated user's partner is not allowed on environment: " + instance.getCode());
        }

    return new Credential(user, instance, (Organization) org);
  }

  /**
   * Check if Principal user of Spring's context rely to the specified instance.
   *
   * @return Credential if authorization is ok
   * @throws RestSecurityException If User is not administrator
   */
  public static Credential checkAdminUser(HttpServletRequest request) {

    final User user = getAndCheckPrincipalUser(request);
    final Group org = user.getPrimaryGroup();

    boolean isAdministrator = CollectionUtils.containsAny(PrincipalHelper.getGroupNameList(user.getGroups()), asList(ADMIN.toString()));
    isTrue(isAdministrator, "Authenticated user is not administrator");

    return new Credential(user, null, (Organization) org);
  }

    public static void checkTechnicalUserInfo(TechnicalUser technicalUser, String instanceCode, boolean isCompanyOnly) {
        boolean isAdministrator = false;
        if (instanceCode == null) {
            isAdministrator = technicalUser.getPrimaryGroup()
                    .equals(ADMIN.toString());
            isTrue(isAdministrator, "Authenticated user is not administrator");
        }
        else {
            Instance instance = ofNullable(instanceCode).map(code -> code.toUpperCase(getDefault()))
                    .map(getInstanceService()::getInstanceByCode)
                    .orElseThrow(() -> new RestSecurityException("Environment not found (Check the instance code)"));
            final Group org = technicalUser.getPrimaryGroup();
            if (isCompanyOnly) {
                isTrue(org instanceof Organization && instance.getGroup()
                                .equals(org),
                        "Authenticated user's organizaton is not managing environment: " + instance.getCode());
            }
      else {
                isAdministrator = technicalUser.getPrimaryGroup()
                        .equals(ADMIN.toString());
                isTrue(isAdministrator || org instanceof Organization && (instance.getGroup()
                                .equals(org) ||
                                instance.getGroup()
                                        .equals(org.getParent())),
                        "Authenticated user's partner is not allowed on environment: " + instance.getCode());
            }
        }
    }

  /**
   * Check if Principal user of Spring's context rely to the given Organization. User can access to their own Partner, or Company that holds
   * Partner. Not siblings Partners. By the way, user on Company has access to every Partners hold by Company.
   *
   * @param checkedOrganization Organization checked (Company or Partner)
   * @throws RestSecurityException If User has no access to Organization
   */
  public static void checkGroupUser(HttpServletRequest request, final com.byzaneo.security.bean.Organization checkedOrganization) {
    if (!isGroupUser(request, checkedOrganization)) {
      throw new RestSecurityException(
          "Authenticated user's organizaton is not managing the checkedOrganization <" + checkedOrganization.getCode() + ">");
    }
  }

  /**
   * Check if Principal user of Spring's context rely to the given Organization. User can access to their own Partner, or Company that holds
   * Partner. Not siblings Partners. By the way, user on Company has access to every Partners hold by Company.
   *
   * @param checkedOrganization Organization checked (Company or Partner)
   * @return True is user has access, false otherwise
   */
  public static boolean isGroupUser(HttpServletRequest request, final com.byzaneo.security.bean.Organization checkedOrganization) {
    Assert.notNull(checkedOrganization, "Group is required");

        final Group userOrganization = getAndCheckPrincipalUser(request).getPrimaryGroup();
        if (userOrganization instanceof Partner && checkedOrganization instanceof Partner ||
                userOrganization instanceof Company && checkedOrganization instanceof Company) {
            return userOrganization.equals(checkedOrganization);
        }
        else if (checkedOrganization instanceof Company && userOrganization instanceof Partner) {
            return userOrganization.getParent()
                    .equals(checkedOrganization);
        }
    else if (checkedOrganization instanceof Partner && userOrganization instanceof Company) {
            return userOrganization.equals(checkedOrganization.getParent());
        }

    logger.warn("Unable to determine access rights for organisation <" + checkedOrganization.getCode() + ">");
    return false;
  }

  public static File getRestTriggerFolder(String filename) {
    return new File(getConfigurationService().getFile(DATA_DIR, getTempDirectory()), "restTrigger" + File.separator + filename);
  }

  public static File getRestTriggerFilesFolder(String filename) {
    return getRestTriggerFolder(filename);
  }

  private static void notNull(final Object obj, final String message) {
    isTrue(obj != null, message);
  }

  private static void isTrue(final boolean condition, final String message) {
    if (!condition) {
      throw new RestSecurityException(message);
    }
  }

  private static User getAndCheckPrincipalUser(HttpServletRequest request) {
    try {
      // in case of SSO login the UserDetails object is set on getContext().getAuthentication().getDetails() and not on
      // getContext().getAuthentication().getPrincipal()
      Optional<Object> samlCredentials = ofNullable(getContext().getAuthentication()
          .getCredentials());
      if (samlCredentials.isPresent() && samlCredentials.get() instanceof SAMLCredential) {
        return ofNullable(((UserDetails) getContext()
            .getAuthentication()
            .getDetails())
            .getUser())
            .orElseThrow(() -> new RestSecurityException("User not found"));
      }
      //get the user from request instead of context if it exists (used for Swagger)
      String login = getUserFromRequest(request);
      if (login != null && !login.equals("REST_USER")) {
        AccountService accountService = getBean(AccountService.class, AccountService.SERVICE_NAME);
        User userByLoginOrOpenID = accountService.getUserByLoginOrOpenID(login);
        if (userByLoginOrOpenID.getSwitchUserLogin() != null) {
          userByLoginOrOpenID = accountService.getUserByLoginOrOpenID(userByLoginOrOpenID.getSwitchUserLogin());
        }
        if (userByLoginOrOpenID != null)
          return userByLoginOrOpenID;
      }
      return ofNullable(((UserDetails) getContext()
          .getAuthentication()
          .getPrincipal())
          .getUser())
          .orElseThrow(() -> new RestSecurityException("User not found"));

        }
        catch (RestSecurityException rse) {
            throw rse;
        }
    catch (Exception e) {
            throw new RestSecurityException(getRootCauseMessage(e));
        }
    }

  private RestServiceHelper() {
    throw new AssertionError();
  }

  public static <T> Map<String, String> checkValidity(T t) {
    Set<ConstraintViolation<T>> violations = validator.validate(t);
    if (CollectionUtils.isNotEmpty(violations)) {
      return violations.stream()
          .collect(
              Collectors.toMap(v -> v.getPropertyPath()
                  .toString(), v -> v.getMessage()));
    }
    return null;
  }

  public static Response getResponse(Response.Status status, Object entity) {
    Gson gson = GsonHelper.getGson();
    return Response.status(status)
        .entity(entity instanceof String
            ? entity
            : GsonHelper.getGson()
                .toJson(entity))
        .build();
  }

  public static Response getResponse(Response.Status status, Object entity, UUID requestId) {
    return Response.status(status)
        .entity(entity instanceof String ? entity : gson.toJson(entity))
        .header("Request-Id", requestId)
        .build();
  }

  public static Response getResponseWithRequestId(Response.Status status, Object entity, UUID requestId) {
    return Response.status(status)
        .entity(entity)
        .header("Request-Id", requestId)
        .build();
  }

  static class EmptyToNullTypeAdapter extends TypeAdapter<String> {

        @Override
        public void write(JsonWriter jsonWriter, String s) throws IOException {
            if (s != null && (s.isEmpty() || "null".equals(s))) {
                jsonWriter.nullValue();
            }
            else {
                TypeAdapters.STRING.write(jsonWriter, s);
            }
        }

    @Override
    public String read(JsonReader jsonReader) throws IOException {
      return TypeAdapters.STRING.read(jsonReader);
    }
  }

  public static CompanyDto convertNull(CompanyDto t) {
    Gson gson = createGson();
    return gson.fromJson(String.valueOf(gson.toJson(t)), CompanyDto.class);
  }

  static Gson createGson() {
    return new GsonBuilder().serializeNulls()
        .registerTypeAdapter(String.class,
            new EmptyToNullTypeAdapter())
        .create();
  }

    public static List<SelectItem> getCountryItems() {
        return CustomLocaleMapper.getCountryItems();
    }

  /**
   * methode to get a username from HttpServletRequest. We do that in case of Swagger only This allows to let the admin test the API using
   * Swagger
   *
   * @param request
   * @return
   */
  public static String getUserFromRequest(HttpServletRequest request) {
    if (SecurityContextHolder.getContext()
        .getAuthentication()
        .getAuthorities()
        .stream()
        .filter(c -> c.getAuthority()
            .equals("ROLE_ADMIN"))
        .findFirst()
        .orElse(null) !=
        null) {
      //used to let it work for Swagger
      if (request.getHeader("userName") != null) {
        return request.getHeader("userName");
      }
    }
    return SecurityContextHolder.getContext()
        .getAuthentication()
        .getName();
  }

  public static String getTechnicalUserIdFromRequest(HttpServletRequest request) {
    TechnicalUser technicalUser = (TechnicalUser) SecurityContextHolder.getContext()
        .getAuthentication()
        .getPrincipal();
    return technicalUser.getId();
  }

    public static boolean hasBearerToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        return authHeader != null && authHeader.toLowerCase()
                .startsWith("bearer ");
    }

    public static String getJWTUsername(HttpServletRequest request) {
        return SecurityContextHolder.getContext()
                .getAuthentication()
                .getName();
    }

    public static String getJWTImpersonatorUsername(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.length() > 7) {
            DecodedJWT decodedJWT = JWT.decode(token.substring(7));
            if (decodedJWT.getClaim("impersonator") != null && !decodedJWT.getClaim("impersonator")
                    .isNull()) {
                return decodedJWT.getClaim("impersonator")
                        .asMap()
                        .get("username")
                        .toString();
            }
            return null;
        }

        return null;
  }

  public static InvoiceDTO transformToInvoiceDTO(InvoiceIndex invoice, boolean fromAngular, Locale locale, String instanceCode) {

        return fromAngular
                ? transformToInvoiceDtoForAngular(invoice, fromAngular, locale, instanceCode)
                : transformToInvoiceDtoForRest(invoice, fromAngular, locale, instanceCode);
    }

    public static ArchivedInvoiceDTO transformToArchiveInvoiceDTO(ArchivedInvoice archivedInvoice, boolean fromAngular, Locale locale,
                                                                  String instanceCode) {
        return fromAngular
                ? transformToArchiveInvoiceDtoForAngular(archivedInvoice, fromAngular, locale, instanceCode)
                : transformToArchiveInvoiceDtoForRest(archivedInvoice, fromAngular, locale, instanceCode);
    }

    public static InvoiceDTO transformToInvoiceDtoForRest(InvoiceIndex invoice, boolean fromAngular, Locale locale, String instanceCode) {
        InvoiceForRestDTO invoiceDTO = new InvoiceForRestDTO();
        invoiceDTO.setOwners(invoice.getOwners());
        invoiceDTO.setFrom(invoice.getFrom());
        invoiceDTO.setTo(invoice.getTo());
        invoiceDTO.setStage(InvoiceDTO.StageEnum.fromValue(invoice.getStage()));
        invoiceDTO.setArchiveStatus(InvoiceDTO.ArchiveStatusEnum.fromValue(invoice.getArchiveStatus()));
        invoiceDTO.setConsultStatus(InvoiceDTO.ConsultStatusEnum.fromValue(invoice.getConsultStatus()));
        invoiceDTO.setInvoiceTypeCoded(InvoiceDTO.InvoiceTypeCodedEnum.fromValue(invoice.getInvoiceTypeCoded()));
        invoiceDTO.setInvoiceCurrencyCoded(InvoiceDTO.CurrencyCodedEnum.fromValue(invoice.getInvoiceCurrencyCoded()));
        invoiceDTO.setBuyerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getBuyerPartyCountryCoded()));
        invoiceDTO.setSellerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getSellerPartyCountryCoded()));
        invoiceDTO.setBillToPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getBillToPartyCountryCoded()));
        invoiceDTO.setStatus(invoice.getStatusAsString());
        setInvoiceDto(invoice, invoiceDTO);
        invoiceDTO.setType(invoice.getType());
        invoiceDTO.setContractID(invoice.getContractID());
        invoiceDTO.setFreeText01(invoice.getFreeText01());
        invoiceDTO.setFreeText02(invoice.getFreeText02());
        invoiceDTO.setFreeText03(invoice.getFreeText03());
        invoiceDTO.setFreeText04(invoice.getFreeText04());
        invoiceDTO.setFreeText05(invoice.getFreeText05());
        invoiceDTO.setFreeText06(invoice.getFreeText06());
        invoiceDTO.setFreeText07(invoice.getFreeText07());
        invoiceDTO.setFreeText08(invoice.getFreeText08());
        invoiceDTO.setFreeText09(invoice.getFreeText09());
        invoiceDTO.setFreeText10(invoice.getFreeText10());
        invoiceDTO.setFreeText11(invoice.getFreeText11());
        invoiceDTO.setFreeText12(invoice.getFreeText12());
        invoiceDTO.setFreeText13(invoice.getFreeText13());
        invoiceDTO.setFreeText14(invoice.getFreeText14());
        invoiceDTO.setFreeText15(invoice.getFreeText15());
        invoiceDTO.setFreeText16(invoice.getFreeText16());
        invoiceDTO.setFreeText17(invoice.getFreeText17());
        invoiceDTO.setFreeText18(invoice.getFreeText18());
        invoiceDTO.setFreeText19(invoice.getFreeText19());
        invoiceDTO.setFreeText20(invoice.getFreeText20());
        invoiceDTO.setFreeText21(invoice.getFreeText21());
        invoiceDTO.setFreeText22(invoice.getFreeText22());
        invoiceDTO.setFreeText23(invoice.getFreeText23());
        invoiceDTO.setFreeText24(invoice.getFreeText24());
        invoiceDTO.setFreeText25(invoice.getFreeText25());
        invoiceDTO.setFreeText26(invoice.getFreeText26());
        invoiceDTO.setFreeText27(invoice.getFreeText27());
        invoiceDTO.setFreeText28(invoice.getFreeText28());
        invoiceDTO.setFreeText29(invoice.getFreeText29());
        invoiceDTO.setFreeText30(invoice.getFreeText30());
        invoiceDTO.setFreeText31(invoice.getFreeText31());
        invoiceDTO.setFreeText32(invoice.getFreeText32());
        invoiceDTO.setFreeText33(invoice.getFreeText33());
        invoiceDTO.setFreeText34(invoice.getFreeText34());
        invoiceDTO.setFreeText35(invoice.getFreeText35());
        return invoiceDTO;
    }

    public static ArchivedInvoiceDTO transformToArchiveInvoiceDtoForRest(ArchivedInvoice archivedInvoice, boolean fromAngular, Locale locale,
                                                                         String instanceCode) {
        ArchiveInvoiceForRestDTO archiveInvoiceForRestDTO = new ArchiveInvoiceForRestDTO();
        archiveInvoiceForRestDTO.setOwners(archivedInvoice.getOwners());
        archiveInvoiceForRestDTO.setFrom(archivedInvoice.getFrom());
        archiveInvoiceForRestDTO.setTo(archivedInvoice.getTo());
        archiveInvoiceForRestDTO.setStage(InvoiceDTO.StageEnum.fromValue(archivedInvoice.getStage()));
        archiveInvoiceForRestDTO.setArchiveStatus(InvoiceDTO.ArchiveStatusEnum.fromValue(archivedInvoice.getArchiveStatus()));
        archiveInvoiceForRestDTO.setConsultStatus(InvoiceDTO.ConsultStatusEnum.fromValue(archivedInvoice.getInvoice()
                .getConsultStatus()));
        archiveInvoiceForRestDTO.setInvoiceTypeCoded(InvoiceDTO.InvoiceTypeCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getInvoiceTypeCoded()));
        archiveInvoiceForRestDTO.setInvoiceCurrencyCoded(InvoiceDTO.CurrencyCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getInvoiceCurrencyCoded()));
        archiveInvoiceForRestDTO.setBuyerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getBuyerPartyCountryCoded()));
        archiveInvoiceForRestDTO.setSellerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getSellerPartyCountryCoded()));
        archiveInvoiceForRestDTO.setBillToPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getBillToPartyCountryCoded()));
        archiveInvoiceForRestDTO.setStatus(archivedInvoice.getStatusAsString());
        setArchiveInvoiceDto(archivedInvoice, archiveInvoiceForRestDTO);
        archiveInvoiceForRestDTO.setType(archivedInvoice.getType());
        archiveInvoiceForRestDTO.setContractID(archivedInvoice.getInvoice()
                .getContractID());
        archiveInvoiceForRestDTO.setFreeText01(archivedInvoice.getInvoice()
                .getFreeText01());
        archiveInvoiceForRestDTO.setFreeText02(archivedInvoice.getInvoice()
                .getFreeText02());
        archiveInvoiceForRestDTO.setFreeText03(archivedInvoice.getInvoice()
                .getFreeText03());
        archiveInvoiceForRestDTO.setFreeText04(archivedInvoice.getInvoice()
                .getFreeText04());
        archiveInvoiceForRestDTO.setFreeText05(archivedInvoice.getInvoice()
                .getFreeText05());
        archiveInvoiceForRestDTO.setFreeText06(archivedInvoice.getInvoice()
                .getFreeText06());
        archiveInvoiceForRestDTO.setFreeText07(archivedInvoice.getInvoice()
                .getFreeText07());
        archiveInvoiceForRestDTO.setFreeText08(archivedInvoice.getInvoice()
                .getFreeText08());
        archiveInvoiceForRestDTO.setFreeText09(archivedInvoice.getInvoice()
                .getFreeText09());
        archiveInvoiceForRestDTO.setFreeText10(archivedInvoice.getInvoice()
                .getFreeText10());
        archiveInvoiceForRestDTO.setFreeText11(archivedInvoice.getInvoice()
                .getFreeText11());
        archiveInvoiceForRestDTO.setFreeText12(archivedInvoice.getInvoice()
                .getFreeText12());
        archiveInvoiceForRestDTO.setFreeText13(archivedInvoice.getInvoice()
                .getFreeText13());
        archiveInvoiceForRestDTO.setFreeText14(archivedInvoice.getInvoice()
                .getFreeText14());
        archiveInvoiceForRestDTO.setFreeText15(archivedInvoice.getInvoice()
                .getFreeText15());
        archiveInvoiceForRestDTO.setFreeText16(archivedInvoice.getInvoice()
                .getFreeText16());
        archiveInvoiceForRestDTO.setFreeText17(archivedInvoice.getInvoice()
                .getFreeText17());
        archiveInvoiceForRestDTO.setFreeText18(archivedInvoice.getInvoice()
                .getFreeText18());
        archiveInvoiceForRestDTO.setFreeText19(archivedInvoice.getInvoice()
                .getFreeText19());
        archiveInvoiceForRestDTO.setFreeText20(archivedInvoice.getInvoice()
                .getFreeText20());
        archiveInvoiceForRestDTO.setFreeText21(archivedInvoice.getInvoice()
                .getFreeText21());
        archiveInvoiceForRestDTO.setFreeText22(archivedInvoice.getInvoice()
                .getFreeText22());
        archiveInvoiceForRestDTO.setFreeText23(archivedInvoice.getInvoice()
                .getFreeText23());
        archiveInvoiceForRestDTO.setFreeText24(archivedInvoice.getInvoice()
                .getFreeText24());
        archiveInvoiceForRestDTO.setFreeText25(archivedInvoice.getInvoice()
                .getFreeText25());
        archiveInvoiceForRestDTO.setFreeText26(archivedInvoice.getInvoice()
                .getFreeText26());
        archiveInvoiceForRestDTO.setFreeText27(archivedInvoice.getInvoice()
                .getFreeText27());
        archiveInvoiceForRestDTO.setFreeText28(archivedInvoice.getInvoice()
                .getFreeText28());
        archiveInvoiceForRestDTO.setFreeText29(archivedInvoice.getInvoice()
                .getFreeText29());
        archiveInvoiceForRestDTO.setFreeText30(archivedInvoice.getInvoice()
                .getFreeText30());
        archiveInvoiceForRestDTO.setFreeText31(archivedInvoice.getInvoice()
                .getFreeText31());
        archiveInvoiceForRestDTO.setFreeText32(archivedInvoice.getInvoice()
                .getFreeText32());
        archiveInvoiceForRestDTO.setFreeText33(archivedInvoice.getInvoice()
                .getFreeText33());
        archiveInvoiceForRestDTO.setFreeText34(archivedInvoice.getInvoice()
                .getFreeText34());
        archiveInvoiceForRestDTO.setFreeText35(archivedInvoice.getInvoice()
                .getFreeText35());
        return archiveInvoiceForRestDTO;
    }

    public static InvoiceDTO transformToInvoiceDtoForAngular(InvoiceIndex invoice, boolean fromAngular, Locale locale, String instanceCode) {

        DocumentStatusEntity documentStatusEntity = documentStatusServiceBean.getDocumentStatusByCode(
                invoice.getStatusAsString());
        String status = fromAngular
                ? DocumentStyleHelper.resolveStatusLabelText(documentStatusEntity, locale, instanceCode)
                : invoice.getStatusAsString();
        InvoiceForAngularDTO invoiceDTO = modelMapper.map(invoice, InvoiceForAngularDTO.class);
        invoiceDTO.setInvoiceTotalTAC(invoice.getInvoiceTotalTAC() == null ? invoice.getInvoiceTotal() : invoice.getInvoiceTotalTAC());
        invoiceDTO.setTaxableValueTAC(invoice.getTaxableValueTAC() == null ? invoice.getTaxableValue() : invoice.getTaxableValueTAC());
        invoiceDTO.setTotalTaxAmountTAC(invoice.getTotalTaxAmountTAC() == null ? invoice.getTotalTaxAmount() : invoice.getTotalTaxAmountTAC());
        invoiceDTO.setTotalAmountPayableTAC(
                invoice.getTotalAmountPayableTAC() == null ? invoice.getTotalAmountPayable() : invoice.getTotalAmountPayableTAC());
        invoiceDTO.setOwners(invoice.getOwners());
        invoiceDTO.setFrom(invoice.getFrom());
        invoiceDTO.setTo(invoice.getTo());
        invoiceDTO.setTranslator(invoice.getTranslator());
        if (invoice.getOrderingParty() != null) {
            invoiceDTO.setOrderingParty(invoice.getOrderingParty());
        }
        if (invoice.getInvoiceCostAllocationNumber() != null) {
            invoiceDTO.setInvoiceCostAllocationNumber(invoice.getInvoiceCostAllocationNumber());
        }
        if (invoice.getBillingFramework() != null) {
            invoiceDTO.setBillingFramework(invoice.getBillingFramework());
        }
        invoiceDTO.setStatus(Map.of(
                "code", invoice.getStatus()
                        .getStatusCode(),
                "value", status,
                "styleClass", getValueAwesomeBootstrap(documentStatusEntity, instanceCode)));

        invoiceDTO.setStage(Map.of(
                "value", toStageItem(InvoiceDTO.StageEnum.fromValue(invoice.getStage())
                        .getValue(), locale),
                "name", invoice.getStage()
                        .name(),
                "styleClass", getValueAwesomeBootstrap(invoice.getStage())));
        // TODO get translation
        InvoiceDTO.ArchiveStatusEnum archiveStatusEnum = invoice.getArchiveStatus() != null ? InvoiceDTO.ArchiveStatusEnum.fromValue(
                invoice.getArchiveStatus()) : null;
        String labelValue = archiveStatusEnum != null ? archiveStatusEnum.getValue() : "";
        String labelStatus = MessageHelper.getMessage(LABEL_FAMILY + "." + labelValue, "", locale);
        invoiceDTO.setArchiveStatus(archiveStatusEnum != null ? Map.of(
                "value", labelStatus,
                "styleClass", getValueAwesomeBootstrap(invoice.getArchiveStatus())) : null);
        invoiceDTO.setPaymentStatus(
                invoice.getPaymentStatus() != null && InvoiceDTO.PaymentStatusEnum.fromValue(invoice.getPaymentStatus()) != null
                        ? DocumentStyleHelper.resolvePaymentStatusLabelText(invoice.getPaymentStatus()) : null);

        invoiceDTO.setConsultStatus(
                invoice.getConsultStatus() != null && InvoiceDTO.ConsultStatusEnum.fromValue(invoice.getConsultStatus()) != null
                        ? DocumentStyleHelper.resolveConsultStatusLabelText(invoice.getConsultStatus(), locale)
                        : null);

        if (invoice.getInvoiceTypeCoded() != null) {
            String labelInvoiceTypeCoded = MessageHelper.getMessage(LABEL_FAMILY + "." + invoice.getInvoiceTypeCoded()
                    .name(), "", locale);
            invoiceDTO.setInvoiceTypeCoded(labelInvoiceTypeCoded);
        }
        invoiceDTO.setInvoiceCurrencyCoded(
                invoice.getInvoiceCurrencyCoded() != null && InvoiceDTO.CurrencyCodedEnum.fromValue(invoice.getInvoiceCurrencyCoded()) != null
                        ? InvoiceDTO.CurrencyCodedEnum.fromValue(invoice.getInvoiceCurrencyCoded())
                        .getValue()
                        : null);

        invoiceDTO.setBuyerPartyCountryCoded(invoice.getBuyerPartyCountryCoded() != null && InvoiceDTO.PartiesCountryCodedEnum.fromValue(
                invoice.getBuyerPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getBuyerPartyCountryCoded())
                .getValue()
                : null);

        invoiceDTO.setSellerPartyCountryCoded(invoice.getSellerPartyCountryCoded() != null && InvoiceDTO.PartiesCountryCodedEnum.fromValue(
                invoice.getSellerPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getSellerPartyCountryCoded())
                .getValue()
                : null);

        invoiceDTO.setBillToPartyCountryCoded(invoice.getBillToPartyCountryCoded() != null && InvoiceDTO.PartiesCountryCodedEnum.fromValue(
                invoice.getBillToPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(invoice.getBillToPartyCountryCoded())
                .getValue()
                : null);

        setInvoiceDto(invoice, invoiceDTO);
        if (invoice.getType() != null) {
            invoiceDTO.setType(getType(invoice.getType()));
        }
        invoiceDTO.setAcquisition(invoice.getAcquisition() != null && InvoiceDTO.AcquisitionEnum.fromValue(invoice.getAcquisition()
                .name()) != null
                ? InvoiceDTO.AcquisitionEnum.fromValue(invoice.getAcquisition()
                .name())
                : null);
        invoiceDTO.setProcessingWay(invoice.getProcessingWay() != null && InvoiceDTO.ProcessingWayEnum.fromValue(invoice.getProcessingWay()
                .name()) != null
                ? InvoiceDTO.ProcessingWayEnum.fromValue(invoice.getProcessingWay()
                .name())
                : null);
        invoiceDTO.setAttachFileCount(invoice.getAttachFileCount());
        return invoiceDTO;
    }

  public static InvoiceCorrectionDTO transformXcblPathDTO(XcblPath xcblPath) {
    InvoiceCorrectionDTO invoiceCorrectionDTO = new InvoiceCorrectionDTO();
    invoiceCorrectionDTO.setName(xcblPath.getName());
    invoiceCorrectionDTO.setValue(xcblPath.getValue());
    invoiceCorrectionDTO.setXpath(xcblPath.getXpath());

    return invoiceCorrectionDTO;
  }

    public static ArchivedInvoiceDTO transformToArchiveInvoiceDtoForAngular(ArchivedInvoice archivedInvoice, boolean fromAngular,
                                                                            Locale locale, String instanceCode) {

        DocumentStatusEntity documentStatusEntity = documentStatusServiceBean.getDocumentStatusByCode(
                archivedInvoice.getStatusAsString());
        String status = fromAngular
                ? DocumentStyleHelper.resolveStatusLabelText(documentStatusEntity, locale, instanceCode)
                : archivedInvoice.getStatusAsString();
        modelMapper.getConfiguration()
                .setAmbiguityIgnored(true);
        ArchiveInvoiceForAngularDTO invoiceForAngularDTO = modelMapper.map(archivedInvoice, ArchiveInvoiceForAngularDTO.class);
        invoiceForAngularDTO.setOwners(archivedInvoice.getInvoice()
                .getOwners());
        invoiceForAngularDTO.setFrom(archivedInvoice.getInvoice()
                .getFrom());
        invoiceForAngularDTO.setTo(archivedInvoice.getInvoice()
                .getTo());
        invoiceForAngularDTO.setTranslator(archivedInvoice.getInvoice()
                .getTranslator());
        invoiceForAngularDTO.setOrderingParty(archivedInvoice.getInvoice()
                .getOrderingParty());
        //invoiceForAngularDTO.setInvoice(transformToInvoiceDtoForAngular(archivedInvoice.getInvoice(),fromAngular,locale,instanceCode));
        invoiceForAngularDTO.setArchiveId(archivedInvoice.getArchiveId());
        invoiceForAngularDTO.setArchiveUid(archivedInvoice.getArchiveUid());

        invoiceForAngularDTO.setArchiveHash(archivedInvoice.getArchiveHash());
        invoiceForAngularDTO.setArchiveReportUid(archivedInvoice.getArchiveReportUid());
        invoiceForAngularDTO.setStatus(Map.of(
                "code", archivedInvoice.getInvoice()
                        .getStatus()
                        .getStatusCode(),
                "value", status,
                "styleClass", getValueAwesomeBootstrap(documentStatusEntity, instanceCode)));

        invoiceForAngularDTO.setStage(Map.of(
                "value", toStageItem(InvoiceDTO.StageEnum.fromValue(archivedInvoice.getInvoice()
                                .getStage())
                        .getValue(), locale),
                "name", archivedInvoice.getInvoice()
                        .getStage()
                        .name(),
                "styleClass", getValueAwesomeBootstrap(archivedInvoice.getInvoice()
                        .getStage())));
        // TODO get translation
        InvoiceDTO.ArchiveStatusEnum archiveStatusEnum = InvoiceDTO.ArchiveStatusEnum.fromValue(archivedInvoice.getInvoice()
                .getArchiveStatus());
        String labelValue = archiveStatusEnum != null ? archiveStatusEnum.getValue() : "";
        String labelStatus = MessageHelper.getMessage(LABEL_FAMILY + "." + labelValue, "", locale);
        String labelInvoiceTypeCoded = MessageHelper.getMessage(LABEL_FAMILY + "." + archivedInvoice.getInvoice()
                .getInvoiceTypeCoded()
                .name(), "", locale);
        invoiceForAngularDTO.setArchiveStatus(archiveStatusEnum != null ? Map.of(
                "value", labelStatus,
                "styleClass", getValueAwesomeBootstrap(archivedInvoice.getInvoice()
                        .getArchiveStatus())) : null);
        invoiceForAngularDTO.setPaymentStatus(InvoiceDTO.PaymentStatusEnum.fromValue(archivedInvoice.getInvoice()
                .getPaymentStatus()) != null
                ? DocumentStyleHelper.resolvePaymentStatusLabelText(archivedInvoice.getInvoice()
                .getPaymentStatus()) : null);

        invoiceForAngularDTO.setConsultStatus(InvoiceDTO.ConsultStatusEnum.fromValue(archivedInvoice.getInvoice()
                .getConsultStatus()) != null
                ? DocumentStyleHelper.resolveConsultStatusLabelText(archivedInvoice.getInvoice()
                .getConsultStatus(), locale)
                : null);

        invoiceForAngularDTO.setInvoiceTypeCoded(labelInvoiceTypeCoded);

        invoiceForAngularDTO.setInvoiceCurrencyCoded(InvoiceDTO.CurrencyCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getInvoiceCurrencyCoded()) != null
                ? InvoiceDTO.CurrencyCodedEnum.fromValue(archivedInvoice.getInvoice()
                        .getInvoiceCurrencyCoded())
                .getValue()
                : null);

        invoiceForAngularDTO.setBuyerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getBuyerPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                        .getBuyerPartyCountryCoded())
                .getValue()
                : null);

        invoiceForAngularDTO.setSellerPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getSellerPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                        .getSellerPartyCountryCoded())
                .getValue()
                : null);

        invoiceForAngularDTO.setBillToPartyCountryCoded(InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                .getBillToPartyCountryCoded()) != null
                ? InvoiceDTO.PartiesCountryCodedEnum.fromValue(archivedInvoice.getInvoice()
                        .getBillToPartyCountryCoded())
                .getValue()
                : null);

        setArchiveInvoiceDto(archivedInvoice, invoiceForAngularDTO);
        invoiceForAngularDTO.setType(getType(archivedInvoice.getInvoice()
                .getType()));
        invoiceForAngularDTO.setAcquisition(archivedInvoice.getInvoice()
                .getAcquisition() != null && InvoiceDTO.AcquisitionEnum.fromValue(archivedInvoice.getInvoice()
                .getAcquisition()
                .name()) != null
                ? InvoiceDTO.AcquisitionEnum.fromValue(archivedInvoice.getInvoice()
                .getAcquisition()
                .name())
                : null);
        invoiceForAngularDTO.setProcessingWay(archivedInvoice.getInvoice()
                .getProcessingWay() != null && InvoiceDTO.ProcessingWayEnum.fromValue(archivedInvoice.getInvoice()
                .getProcessingWay()
                .name()) != null
                ? InvoiceDTO.ProcessingWayEnum.fromValue(archivedInvoice.getInvoice()
                .getProcessingWay()
                .name())
                : null);
        invoiceForAngularDTO.setAttachFileCount(archivedInvoice.getInvoice()
                .getAttachFileCount());

        invoiceForAngularDTO.setUuid(archivedInvoice.getId());

        return invoiceForAngularDTO;
    }

    private static void setInvoiceDto(InvoiceIndex invoice, InvoiceDTO invoiceDTO) {
    /*getMessage("gnxworkflowlbls" + ".delete_functionUsers_msg",
        "A function cannot be empty. Please select at least one name", getLocale());*/
        invoiceDTO.setReference(invoice.getReference());
        invoiceDTO.setEntityId(invoice.getEntityId());
        invoiceDTO.setEntityRef(invoice.getEntityRef());
        invoiceDTO.setCreationDate(invoice.getCreationDate());
        invoiceDTO.setUuid(invoice.getUuid());
        invoiceDTO.setModificationDate(invoice.getModificationDate());
        invoiceDTO.setInvoiceNumber(invoice.getInvoiceNumber());
        invoiceDTO.setInvoiceIssueDate(invoice.getInvoiceIssueDate());
        invoiceDTO.setPurchaseOrderDate(invoice.getPurchaseOrderDate());
        invoiceDTO.setBuyerOrderNumber(invoice.getBuyerOrderNumber());
        invoiceDTO.setAsnNumber(invoice.getAsnNumber());
        invoiceDTO.setAsnDate(invoice.getAsnDate());
        invoiceDTO.setInvoiceDueDate(invoice.getInvoiceDueDate());
        invoiceDTO.setInvoiceProcessDateTime(invoice.getInvoiceProcessDateTime());
        invoiceDTO.setInvoicePreparationDateTime(invoice.getInvoicePreparationDateTime());
        invoiceDTO.setBuyerPartyID(invoice.getBuyerPartyID());
        invoiceDTO.setBuyerPartyName(invoice.getBuyerPartyName());
        invoiceDTO.setSellerPartyID(invoice.getSellerPartyID());
        invoiceDTO.setSellerPartyName(invoice.getSellerPartyName());
        invoiceDTO.setSellerPartyTaxIdentifier(invoice.getSellerPartyTaxIdentifier());
        invoiceDTO.setSellerPartyOtherID(invoice.getSellerPartyOtherID());
        invoiceDTO.setBillToPartyID(invoice.getBillToPartyID());
        invoiceDTO.setBillToPartyName(invoice.getBillToPartyName());
        invoiceDTO.setBillToPartyTaxIdentifier(invoice.getBillToPartyTaxIdentifier());
        invoiceDTO.setBillToPartyOtherID(invoice.getBillToPartyOtherID());
        invoiceDTO.shipToPartyID(invoice.getShipToPartyID());
        invoiceDTO.shipToPartyName(invoice.getShipToPartyName());
        invoiceDTO.setInvoiceTotal(getDoubleValue(invoice.getInvoiceTotal()));
        invoiceDTO.setInvoiceTotalTAC(invoice.getInvoiceTotalTAC());
        invoiceDTO.setTaxableValue(getDoubleValue(invoice.getTaxableValue()));
        invoiceDTO.setTaxableValueTAC(invoice.getTaxableValueTAC());
        invoiceDTO.setTotalTaxAmount(getDoubleValue(invoice.getTotalTaxAmount()));
        invoiceDTO.setTotalTaxAmountTAC(invoice.getTotalTaxAmountTAC());
        invoiceDTO.setTotalAmountPayableTAC(invoice.getTotalAmountPayableTAC());
        invoiceDTO.setNature(invoice.getNature());
        invoiceDTO.setB2gRules(invoice.getB2gRules());
        invoiceDTO.setEreportingOption(invoice.getEreportingOption());
        invoiceDTO.setCountryRules1(invoice.getCountryRules1());
        invoiceDTO.setCountryRules2(invoice.getCountryRules2());
        invoiceDTO.setUseCase(invoice.getUseCase());
        invoiceDTO.setBillingFramework(invoice.getBillingFramework());
        invoiceDTO.setSovosId(invoice.getSovosId());
        invoiceDTO.setSovosStatus(invoice.getSovosStatus());
        invoiceDTO.setSovosTransactionId(invoice.getSovosTransactionId());
        invoiceDTO.setSovosStatusCode(invoice.getSovosStatusCode());
        invoiceDTO.setSovosNotificationId(invoice.getSovosNotificationId());
        invoiceDTO.setArchiveExpirationDate(invoice.getArchiveExpirationDate());
        invoiceDTO.setRossumId(invoice.getRossumId());
        invoiceDTO.setThirdParty(invoice.getThirdParty());
        transformInvoiceWorkflowIndexDTO(invoice, invoiceDTO);
        if (invoice.getSummaryBreakdownValueAddedTax() != null) {
            List<ValueAddedTaxDTO> addedTaxDTOs = invoice.getSummaryBreakdownValueAddedTax()
                    .stream()
                    .map(addedVal -> transformValueAddedTaxToValueAddedTaxDTO(addedVal))
                    .collect(Collectors.toList());
            invoiceDTO.setSummaryBreakdownValueAddedTax(addedTaxDTOs);
        }
        if (invoice.getDetails() != null) {
            List<InvoiceDetailDTO> invoiceDetailDTOList = invoice.getDetails()
                    .stream()
                    .map(inv -> transformInvoiceDetailIndexToInvoiceDetailDTO(inv))
                    .collect(Collectors.toList());
            invoiceDTO.setDetails(invoiceDetailDTOList);
        }
        invoiceDTO.setImmatpdp(invoice.getImmatpdp());
        invoiceDTO.setProfile(invoice.getProfile());
    }

    private static void setArchiveInvoiceDto(ArchivedInvoice invoice, InvoiceDTO invoiceDTO) {
    /*getMessage("gnxworkflowlbls" + ".delete_functionUsers_msg",
        "A function cannot be empty. Please select at least one name", getLocale());*/
        invoiceDTO.setReference(invoice.getReference());
        invoiceDTO.setEntityId(Long.parseLong(invoice.getEntityId()
                .toString()));
        invoiceDTO.setEntityRef(invoice.getEntityRef());
        invoiceDTO.setCreationDate(invoice.getCreationDate());
        invoiceDTO.setUuid(invoice.getUuid());
        invoiceDTO.setModificationDate(invoice.getModificationDate());
        invoiceDTO.setInvoiceNumber(invoice.getInvoice()
                .getInvoiceNumber());
        invoiceDTO.setInvoiceIssueDate(invoice.getInvoice()
                .getInvoiceIssueDate());
        invoiceDTO.setPurchaseOrderDate(invoice.getInvoice()
                .getPurchaseOrderDate());
        invoiceDTO.setBuyerOrderNumber(invoice.getInvoice()
                .getBuyerOrderNumber());
        invoiceDTO.setAsnNumber(invoice.getInvoice()
                .getAsnNumber());
        invoiceDTO.setAsnDate(invoice.getInvoice()
                .getAsnDate());
        invoiceDTO.setInvoiceDueDate(invoice.getInvoice()
                .getInvoiceDueDate());
        invoiceDTO.setInvoiceProcessDateTime(invoice.getInvoice()
                .getInvoiceProcessDateTime());
        invoiceDTO.setInvoicePreparationDateTime(invoice.getInvoice()
                .getInvoicePreparationDateTime());
        invoiceDTO.setBuyerPartyID(invoice.getInvoice()
                .getBuyerPartyID());
        invoiceDTO.setBuyerPartyName(invoice.getInvoice()
                .getBuyerPartyName());
        invoiceDTO.setSellerPartyID(invoice.getInvoice()
                .getSellerPartyID());
        invoiceDTO.setSellerPartyName(invoice.getInvoice()
                .getSellerPartyName());
        invoiceDTO.setSellerPartyTaxIdentifier(invoice.getInvoice()
                .getSellerPartyTaxIdentifier());
        invoiceDTO.setSellerPartyOtherID(invoice.getInvoice()
                .getSellerPartyOtherID());
        invoiceDTO.setBillToPartyID(invoice.getInvoice()
                .getBillToPartyID());
        invoiceDTO.setBillToPartyName(invoice.getInvoice()
                .getBillToPartyName());
        invoiceDTO.setBillToPartyTaxIdentifier(invoice.getInvoice()
                .getBillToPartyTaxIdentifier());
        invoiceDTO.setBillToPartyOtherID(invoice.getInvoice()
                .getBillToPartyOtherID());
        invoiceDTO.shipToPartyID(invoice.getInvoice()
                .getShipToPartyID());
        invoiceDTO.shipToPartyName(invoice.getInvoice()
                .getShipToPartyName());
        invoiceDTO.setInvoiceTotal(getDoubleValue(invoice.getInvoice()
                .getInvoiceTotal()));
        invoiceDTO.setTaxableValue(getDoubleValue(invoice.getInvoice()
                .getTaxableValue()));
        invoiceDTO.setTotalTaxAmount(getDoubleValue(invoice.getInvoice()
                .getTotalTaxAmount()));
        invoiceDTO.setNature(invoice.getNature());
        invoiceDTO.setB2gRules(invoice.getB2gRules());
        invoiceDTO.setEreportingOption(invoice.getEreportingOption());
        invoiceDTO.setCountryRules1(invoice.getCountryRules1());
        invoiceDTO.setCountryRules2(invoice.getCountryRules2());
        invoiceDTO.setUseCase(invoice.getInvoice()
                .getUseCase());
        invoiceDTO.setBillingFramework(invoice.getInvoice()
                .getBillingFramework());
        invoiceDTO.setSovosId(invoice.getInvoice()
                .getSovosId());
        invoiceDTO.setSovosStatus(invoice.getInvoice()
                .getSovosStatus());
        invoiceDTO.setSovosTransactionId(invoice.getInvoice()
                .getSovosTransactionId());
        invoiceDTO.setSovosStatusCode(invoice.getInvoice()
                .getSovosStatusCode());
        invoiceDTO.setSovosNotificationId(invoice.getInvoice()
                .getSovosNotificationId());
        invoiceDTO.setArchiveExpirationDate(invoice.getInvoice()
                .getArchiveExpirationDate());
        invoiceDTO.setRossumId(invoice.getInvoice()
                .getRossumId());
        invoiceDTO.setThirdParty(invoice.getThirdParty());
        transformArchiveInvoiceWorkflowIndexDTO(invoice, invoiceDTO);
        if (invoice.getInvoice()
                .getSummaryBreakdownValueAddedTax() != null) {
            List<ValueAddedTaxDTO> addedTaxDTOs = invoice.getInvoice()
                    .getSummaryBreakdownValueAddedTax()
                    .stream()
                    .map(addedVal -> transformValueAddedTaxToValueAddedTaxDTO(addedVal))
                    .collect(Collectors.toList());
            invoiceDTO.setSummaryBreakdownValueAddedTax(addedTaxDTOs);
        }
        if (invoice.getInvoice()
                .getDetails() != null) {
            List<InvoiceDetailDTO> invoiceDetailDTOList = invoice.getInvoice()
                    .getDetails()
                    .stream()
                    .map(inv -> transformInvoiceDetailIndexToInvoiceDetailDTO(inv))
                    .collect(Collectors.toList());
            invoiceDTO.setDetails(invoiceDetailDTOList);
        }
        invoiceDTO.setImmatpdp(invoice.getImmatpdp());
        invoiceDTO.setProfile(invoice.getInvoice()
                .getProfile());
    }

    public static void transformInvoiceWorkflowIndexDTO(InvoiceIndex index, InvoiceDTO invoiceDTO) {
        if (index != null && index.getWkfName() != null) {
            invoiceDTO.setWkfName(index.getWkfName());
            invoiceDTO.setWkfLastModificationDate(index.getWkfLastModificationDate());
            invoiceDTO.setWkfCompletionPercentage(index.getWkfCompletionPercentage());
            invoiceDTO.setWkfLockedBy(index.getWkfLockedBy());
            invoiceDTO.setWkfStep(index.getWkfStep());
            invoiceDTO.setWkfLockedById(index.getWkfLockedById());
            invoiceDTO.setWkfVersion(index.getWkfVersion());
            invoiceDTO.setWkfNumberOfSteps(index.getWkfNumberOfSteps());
            invoiceDTO.setWkfStepActions(index.getWkfStepActions());
            invoiceDTO.setWkfStepActors(index.getWkfStepActors());
            invoiceDTO.setWkfStepActorsList(index.getWkfStepActorsList());
        }
    }

    public static void transformArchiveInvoiceWorkflowIndexDTO(ArchivedInvoice index, InvoiceDTO invoiceDTO) {
        if (index != null && index.getInvoice()
                .getWkfName() != null) {
            invoiceDTO.setWkfName(index.getInvoice()
                    .getWkfName());
            invoiceDTO.setWkfLastModificationDate(index.getInvoice()
                    .getWkfLastModificationDate());
            invoiceDTO.setWkfCompletionPercentage(index.getInvoice()
                    .getWkfCompletionPercentage());
            invoiceDTO.setWkfLockedBy(index.getInvoice()
                    .getWkfLockedBy());
            invoiceDTO.setWkfStep(index.getInvoice()
                    .getWkfStep());
            invoiceDTO.setWkfLockedById(index.getInvoice()
                    .getWkfLockedById());
            invoiceDTO.setWkfVersion(index.getInvoice()
                    .getWkfVersion());
            invoiceDTO.setWkfNumberOfSteps(index.getInvoice()
                    .getWkfNumberOfSteps());
            invoiceDTO.setWkfStepActions(index.getInvoice()
                    .getWkfStepActions());
            invoiceDTO.setWkfStepActors(index.getInvoice()
                    .getWkfStepActors());
            invoiceDTO.setWkfStepActorsList(index.getInvoice()
                    .getWkfStepActorsList());
        }
    }

    public static InvoiceDetailDTO transformInvoiceDetailIndexToInvoiceDetailDTO(InvoiceDetailIndex index) {
        InvoiceDetailDTO invoiceDetailDTO = new InvoiceDetailDTO();
        invoiceDetailDTO.setItemLineNumber(index.getItemLineNumber());
        invoiceDetailDTO.setItemProductIdentifier(index.getItemProductIdentifier());
        invoiceDetailDTO.setItemBuyerPartNumber(index.getItemBuyerPartNumber());
        invoiceDetailDTO.setItemSellerPartNumber(index.getItemSellerPartNumber());
        invoiceDetailDTO.setItemDescription(index.getItemDescription());
        invoiceDetailDTO.setItemInvoicedQuantityValue(getDoubleValue(index.getItemInvoicedQuantityValue()));
        invoiceDetailDTO.setItemQuantityUnitOfMeasurement(index.getItemQuantityUnitOfMeasurement());
        invoiceDetailDTO.setItemTaxPercent(getDoubleValue(index.getItemTaxPercent()));
        invoiceDetailDTO.setItemTotal(getDoubleValue(index.getItemTotal()));
        invoiceDetailDTO.setItemTotalTAC(index.getItemTotalTAC() == null ? index.getItemTotal() : index.getItemTotalTAC());
        if (index.getItemSubTotal() != null) {
            invoiceDetailDTO.setItemSubTotal(getDoubleValue(index.getItemSubTotal()));
        }
        invoiceDetailDTO.setItemGrossUnitPrice(getDoubleValue(index.getItemGrossUnitPrice()));
        invoiceDetailDTO.setItemNetUnitPrice(getDoubleValue(index.getItemNetUnitPrice()));
        return invoiceDetailDTO;
    }

    public static ValueAddedTaxDTO transformValueAddedTaxToValueAddedTaxDTO(com.byzaneo.xtrade.xcbl.bean.ValueAddedTax addedVal) {
        ValueAddedTaxDTO addedTaxDTO = new ValueAddedTaxDTO();
        addedTaxDTO.setRate(addedVal.getRate());
        addedTaxDTO.setTaxableAmount(getDoubleValue(addedVal.getTaxableAmount()));
        addedTaxDTO.setTaxAmount(getDoubleValue(addedVal.getTaxAmount()));
        addedTaxDTO.setTaxAmountTAC(addedVal.getTaxAmountTAC() == null ? addedVal.getTaxAmount() : addedVal.getTaxAmountTAC());
        addedTaxDTO.setTaxableAmountTAC(addedVal.getTaxableAmountTAC() == null ? addedVal.getTaxableAmount() : addedVal.getTaxableAmountTAC());
        return addedTaxDTO;
    }

  public static Double getDoubleValue(BigDecimal value) {
    if (value != null)
      return value.doubleValue();
    return null;
  }

  public static Response getResponseOnError(String errorCode, String errorMessage, Response.Status statut, UUID requestId) {
    ErrorDTO error = new ErrorDTO();
    error.setErrorCode(errorCode);
    error.setErrorMessage(errorMessage);
    return RestServiceHelper.getResponse(statut, error, requestId);
  }

  public static Response getResponseOnError(String errorCode, String errorMessage, Response.Status statut) {
    ErrorDTO error = new ErrorDTO();
    error.setErrorCode(errorCode);
    error.setErrorMessage(errorMessage);
    return RestServiceHelper.getResponse(statut, error);
  }

    public static String getType(String type) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(type);

            String normValue = jsonNode.get("norm")
                    .asText();
            return normValue;
        }
        catch (Exception e) {
            return type;
        }
    }

  public static Locale getLocaleFromRequest(HttpServletRequest request) {
    if (request == null || request.getLocale() == null) return Locale.ENGLISH;
    return new Locale(request.getLocale()
        .getLanguage());
  }

    public static String ensureXlsExtension(String fileName) {
        String xlsFileName = fileName;
        if (FilenameUtils.getExtension(fileName)
                .isEmpty()) {
            xlsFileName = fileName.concat(FileType.EXCEL.getExtension());
        }
        else if (!FilenameUtils.getExtension(fileName)
                .equals(FileType.EXCEL.getExtension())) {
            FilenameUtils.removeExtension(fileName);
            xlsFileName = fileName.concat(FileType.EXCEL.getExtension());
        }
        return xlsFileName;
    }

  public static Credential checkCredential(HttpServletRequest request, String instanceCode,
      boolean companyUserOnly) {
    if (instanceCode == null) return checkAdminUser(request);
    return checkInstanceUser(request, ofNullable(instanceCode).map(code -> code.toUpperCase(getDefault()))
        .map(getInstanceService()::getInstanceByCode)
        .orElseThrow(() -> new RestSecurityException("Environment not found (Check the instance code)")), companyUserOnly);
  }

  private static InstanceService getInstanceService() {
    if (instanceService == null) {
      instanceService = getBean(InstanceService.class, InstanceService.SERVICE_NAME);
    }
    return instanceService;
  }

  public static <S, T> List<T> mapList(List<S> source, Class<T> targetClass) {
    return source
        .stream()
        .map(element -> modelMapper.map(element, targetClass))
        .collect(Collectors.toList());
  }

  public static <S, T> T genericMap(S source, Class<T> targetClass) {
    return modelMapper.map(source, targetClass);
  }

  public static <S, T> List<T> mapList(List<S> source, Class<T> targetClass, ModelMapper customModelMapper) {
    return source
        .stream()
        .map(element -> customModelMapper.map(element, targetClass))
        .collect(Collectors.toList());
  }

  public static byte[] convertInputStreamToByteArray(InputStream inputStream) throws IOException {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    byte[] buffer = new byte[4096]; // Adjust the buffer size as needed
    int bytesRead;

    while ((bytesRead = inputStream.read(buffer)) != -1) {
      outputStream.write(buffer, 0, bytesRead);
    }

    return outputStream.toByteArray();

  }

    public static void verifyEnvironmentCode(TechnicalUser technicalUser, String envCode) {
        Instance instance = ofNullable(envCode).map(code -> code.toUpperCase(getDefault()))
                .map(getInstanceService()::getInstanceByCode)
                .orElseThrow(() -> new RestSecurityException("Environment not found (Check the instance code)"));
        final Group org = technicalUser.getPrimaryGroup();
        boolean isCompanyUser = PrincipalHelper.isCompanyTechnicalUser(technicalUser);
        if (isCompanyUser) {
            isTrue(org instanceof Organization && instance.getGroup()
                            .equals(org),
                    "Authenticated user's organizaton is not managing environment: " + instance.getCode());
        }
        else {
            isTrue(org instanceof Organization && (instance.getGroup()
                            .equals(org) ||
                            instance.getGroup()
                                    .equals(org.getParent())),
                    "Authenticated user's partner is not allowed on environment: " + instance.getCode());
        }
    }

  public static void checkObjectNullability(Object object, String errorMsg,
      ExceptionType exceptionType) {
    if (object == null) {
      throw new RestJwtException(errorMsg, exceptionType);
    }
  }

  public static void checkEmptyString(String uuid, String errorMsg, ExceptionType exceptionType) {
    if (StringUtils.isEmpty(uuid)) {
      throw new RestJwtException(errorMsg, exceptionType);
    }
  }

  public static boolean isClientWithPerimeter(User user) {
    final OrganizationHelper.UserOrganizations userOrgs = OrganizationHelper.resolveUserOrganizations(
        user);
    Collection<String> perimeter = userOrgs.getCodes();
    return isCompanyUser(user) && perimeter.size() > 0;
  }

  public static boolean isBackOfficeUserUser(User user) {
    return null == user.getPrimaryGroup();
  }

  public static enum RestDocument {
    AccountingPostingScenario(RestDocumentType.ACCOUNTING_POSTING_SCENARIO,
        AccountingPostingScenario.class),
    AapRefSupplier(RestDocumentType.AAP_REF_SUPPLIER,
        AapRefSupplier.class),

    AapAccountingEntry(RestDocumentType.ACCOUNTING_ENTRIES,
        AapAccountingEntry.class),

        RecapListItem(RestDocumentType.ADVANCED_RECAP,
                RecapListItem.class),

        DailyRecapListItem(RestDocumentType.ADVANCED_RECAP_DAILY,
                DailyRecapListItem.class),

        DailyDematPartnerFile(RestDocumentType.DEMAT_PARTNER_FILE_DAILY,
                DailyExchangeListItem.class);

    private final RestDocumentType documentType;
    private final Class<?> xcblDocumentClass;
    ;

    private static List<String> documentTypes;

    RestDocument(RestDocumentType type, Class<?> xcblDocumentClass) {
      this.documentType = type;
      this.xcblDocumentClass = xcblDocumentClass;
    }

    public RestDocumentType getDocumentType() {
      return documentType;
    }

    public Class<?> getXcblDocumentClass() {
      return xcblDocumentClass;
    }

    public String getCollection() {
      org.springframework.data.mongodb.core.mapping.Document anno;
      return xcblDocumentClass == null ||
          (anno = xcblDocumentClass.getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class)) == null
          ? null
          : anno.collection();
    }

    public static RestDocument valueOfIgnoringCase(String value) {
      return isBlank(value)
          ? null
          : stream(values())
              .filter(e -> e.toString()
                  .equalsIgnoreCase(value) ||
                  e.documentType.toString()
                      .equalsIgnoreCase(value))
              .findFirst()
              .orElse(null);
    }

    public static List<String> getDocumentTypes() {
      if (documentTypes == null) {
        documentTypes = stream(values())
            .map(e -> e.documentType.toString())
            .collect(toList());
      }
      return documentTypes;
    }

    public static RestDocument valueOf(XcblDocument xdoc) {
      if (xdoc == null) {
        return null;
      }
      return valueOf(xdoc.getClass());
    }

    public static RestDocument valueOf(Class<?> type) {
      if (type == null) {
        return null;
      }
      return stream(values())
          .filter(edoc -> edoc.xcblDocumentClass.isAssignableFrom(type))
          .findFirst()
          .orElse(null);
    }

    public static String toString(XcblDocument xdoc) {
      if (xdoc == null) {
        return "null";
      }
      final RestDocument edoc = valueOf(xdoc);
      if (edoc == null) {
        return xdoc.toString();
      }

      return xdoc.toString();

    }

    public static List<String> getCollections() {
      return stream(values())
          .map(RestDocument::getCollection)
          .filter(Objects::nonNull)
          .sorted()
          .collect(toList());
    }
  }

  public static enum RestDocumentType {
    ACCOUNTING_POSTING_SCENARIO,
    AAP_REF_SUPPLIER,
    ACCOUNTING_ENTRIES,
        ADVANCED_RECAP,
        ADVANCED_RECAP_DAILY,
        DEMAT_PARTNER_FILE_DAILY
    }

  public static String prepareBql(String bql, AbstractTask task) {
    if (bql != null && bql.contains("_text_")) {
      //String val = bql.replace("\"_text_ i\" ~ ", "");
      String val = bql.replace("_text_ i~", "");

      bql = bql.replaceAll("\\*", "\\\\u002a");  // Replace * with \u002a
      bql = bql.replaceAll("i~ ", "");

            List<String> matches = new ArrayList<>();
            BeanDescriptor descriptor = task.getDescriptor();
            matches = descriptor
                    .getProperties()
                    .stream()
                    .filter(e -> ("java.lang.String").equals(e.getType()
                            .getName()) || ("java.math.BigDecimal").equals(e.getType()
                            .getName()))
                    .map(p -> {
                        String value;
                        value = p.getName() + " ~ " + val;
                        return value;
                    })
          .limit(7)
          .collect(toList());
      for (int i = 0; i < matches.size(); i++) {
        bql += matches.get(i);
        if (i < matches.size() - 1)
          bql += " OR ";
      }
    }
    return bql;

  }

  public static String prepareBqlForSql(String bql, AbstractTask task) {
        return prepareBqlForSql(bql, task.getDescriptor());
    }

    public static String prepareBqlForSql(String bql, BeanDescriptor descriptor) {
        if (bql != null && bql.contains("_text_")) {
            String decodedBql = URLDecoder.decode(bql, StandardCharsets.UTF_8);
            decodedBql = URLDecoder.decode(decodedBql, StandardCharsets.UTF_8);

            String[] bqlArr = decodedBql.split(" ");
            String val = bqlArr.length > 2 ? String.join(" ", (Arrays.copyOfRange(bqlArr, 2, bqlArr.length))) : bqlArr[2];

            List<String> matches = new ArrayList<>();
            matches = descriptor
                    .getProperties()
                    .stream()
                    .filter(e -> ("java.lang.String").equals(e.getType()
                            .getName()))
                    .map(p -> {
                        String value;
                        value = p.getName() + " ~ " + val;
                        return value;
          })
          .collect(toList());
      bql = "";
      for (int i = 0; i < matches.size(); i++) {
        bql += matches.get(i);
        if (i < matches.size() - 1)
          bql += " OR ";
      }
    }
    return bql;

    }

    public static <S, D> D mapAndClean(S source, Class<D> destinationClass) {
        cleanNullStrings(source);
        D destination = modelMapper.map(source, destinationClass);
        return destination;
    }

    @SuppressWarnings("unchecked")
    private static void cleanNullStrings(Object object) {
        if (object == null) return;

        Class<?> clazz = object.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object value = field.get(object);

                if (value instanceof String str && "null".equalsIgnoreCase(str)) {
                    field.set(object, null);
                } else if (value instanceof List<?> list) {
                    for (Object item : list) {
                        cleanNullStrings(item);
                    }
                } else if (isCustomObject(field.getType())) {
                    cleanNullStrings(value);
                }

            } catch (IllegalAccessException e) {
                throw new RuntimeException("general.errors.nullStringCleaning", e);
            }
        }
    }

    private static boolean isCustomObject(Class<?> type) {
        return !type.isPrimitive()
                && !type.getName().startsWith("java.")
                && !type.isEnum();
  }
}
