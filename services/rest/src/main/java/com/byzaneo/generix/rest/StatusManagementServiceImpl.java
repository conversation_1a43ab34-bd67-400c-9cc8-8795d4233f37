package com.byzaneo.generix.rest;

import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.util.PartnerHelper;
import com.byzaneo.security.bean.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.service.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service(StatusManagementService.SERVICE_NAME)

public class StatusManagementServiceImpl implements StatusManagementService {

  @Autowired
  private DocumentService documentService;

  private static final String LEGAL_ACTION = "LEGAL_ACTION_";
  private static final List<String> selfBillingList = Arrays.asList("19b", "selfBilled");
  private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

  @Override
  public InvoiceStatusManagementResponse getChangedInvoiceStatus(InvoiceRequestWithPaginationDTO invoiceRequest, TechnicalUser technicalUser , Date start , Date end) {

    List<Partner> partnerList = PartnerHelper.filterPartnerList(technicalUser, invoiceRequest.getPartnerIds());


    List<PartnerCountStatusDto> partnerCountStatusDtos = getTimelineEntries(
        LEGAL_ACTION,
        start,
        end,
        Document.ProcessingWay.valueOf(invoiceRequest.getProcessingWay()),
        partnerList.stream().map(Partner::getCode).collect(Collectors.toList()),
        invoiceRequest.getLimit(),
        invoiceRequest.getOffset()
    );

    InvoiceStatusManagementResponse response = new InvoiceStatusManagementResponse();
    response.setProcessingWay(invoiceRequest.getProcessingWay());
    response.setStartDate(invoiceRequest.getStartDate());
    response.setEndDate(invoiceRequest.getEndDate());
    response.setPartners(partnerCountStatusDtos);

    return response;
  }



  private List<PartnerCountStatusDto> getTimelineEntries(String action, Date start, Date end,
      Document.ProcessingWay processingWay,
      List<String> partnersIds, int limit, int offset) {
    List<Object[]> results = documentService.getDocumentChangedStatus(
        action, start, end, processingWay, selfBillingList, partnersIds, limit, offset);

    Map<String, List<Statuses>> grouped = results.stream()
        .collect(Collectors.groupingBy(
            row -> (String) row[0],
            Collectors.mapping(row -> {
              String formattedDate = null;
              if (row[5] != null) {
                formattedDate = ((Date) row[5]).toInstant()
                    .atZone(ZoneId.systemDefault())
                    .format(dateFormatter);
              }

              String legalAction = (String) row[3];
              String extractedPart = "";
              if (legalAction != null && legalAction.startsWith(LEGAL_ACTION)) {
                extractedPart = legalAction.substring(LEGAL_ACTION.length());
              }

              return new Statuses(
                  (String) row[1],
                  (String) row[2],
                  extractedPart,
                  row[4] != null ? (String) row[4] : "",
                  formattedDate
              );
            }, Collectors.toList())
        ));

    for (String partnerId : partnersIds) {
      grouped.putIfAbsent(partnerId, Collections.emptyList());
    }

    return grouped.entrySet()
        .stream()
        .map(entry -> new PartnerCountStatusDto(entry.getKey(), entry.getValue()))
        .collect(Collectors.toList());
  }
}

