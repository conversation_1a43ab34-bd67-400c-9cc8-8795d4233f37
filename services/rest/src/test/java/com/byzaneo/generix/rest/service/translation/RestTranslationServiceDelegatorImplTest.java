package com.byzaneo.generix.rest.service.translation;

import com.byzaneo.angular.service.RestJwtException;
import com.byzaneo.generix.api.service.internal.impl.translation.TranslationService;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.DictionaryImportResponse;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.service.AccountService;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.io.File;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RestTranslationServiceDelegatorImplTest {

    @Mock
    private I18NService i18NService;

    @Mock
    private AccountService accountService;

    @Mock
    private TranslationService translationService;

    @Mock
    private ModelMapper modelMapper;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Mock
    private Attachment attachment;

    @InjectMocks
    private RestTranslationServiceDelegatorImpl delegator;

    private User testUser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        testUser = new User();
        testUser.setLogin("testUser");
        
        // Mock getAuthenticatedUser method
        when(accountService.getAuthenticatedUser()).thenReturn(testUser);
    }

    @Test
    void downloadDictionary_ShouldReturnFrontendTranslations_WhenDictTypeIsFrontend() {
        // Given
        String dictType = "frontend";
        String language = "de";
        Response expectedResponse = Response.ok().build();
        
        when(translationService.exportFrontendTranslations(any(File.class), eq(language)))
            .thenReturn(expectedResponse);
        
        // When
        Response response = delegator.downloadDictionary(httpServletRequest, dictType, language);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationService).exportFrontendTranslations(any(File.class), eq(language));
    }

    @Test
    void downloadDictionary_ShouldReturnErrorTranslations_WhenDictTypeIsErrors() {
        // Given
        String dictType = "errors";
        String language = "en";
        Response expectedResponse = Response.ok().build();
        
        when(translationService.exportCSVTranslation(any(File.class)))
            .thenReturn(expectedResponse);
        
        // When
        Response response = delegator.downloadDictionary(httpServletRequest, dictType, language);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationService).exportCSVTranslation(any(File.class));
    }

    @Test
    void downloadDictionary_ShouldThrowException_WhenInvalidDictType() {
        // Given
        String invalidDictType = "invalid";
        String language = "en";
        
        // When & Then
        assertThrows(RestJwtException.class, () -> {
            delegator.downloadDictionary(httpServletRequest, invalidDictType, language);
        });
    }

    @Test
    void uploadDictionary_ShouldImportFrontendTranslations_WhenDictTypeIsFrontend() {
        // Given
        String dictType = "frontend";
        String language = "de";
        DictionaryImportResponse importResponse = DictionaryImportResponse.builder()
            .totalImported(5)
            .errors(Collections.emptyList())
            .build();
        Response expectedResponse = Response.status(Response.Status.CREATED).entity(importResponse).build();
        
        when(translationService.importFrontendTranslations(eq(testUser), eq(language), eq(attachment)))
            .thenReturn(expectedResponse);
        
        // When
        Response response = delegator.uploadDictionary(httpServletRequest, dictType, language, attachment);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationService).importFrontendTranslations(eq(testUser), eq(language), eq(attachment));
    }

    @Test
    void uploadDictionary_ShouldImportErrorTranslations_WhenDictTypeIsErrors() {
        // Given
        String dictType = "errors";
        String language = "en";
        Response expectedResponse = Response.ok().build();
        
        when(translationService.importTranslationsData(eq(testUser), eq(language), eq(attachment)))
            .thenReturn(expectedResponse);
        
        // When
        Response response = delegator.uploadDictionary(httpServletRequest, dictType, language, attachment);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationService).importTranslationsData(eq(testUser), eq(language), eq(attachment));
    }

    @Test
    void uploadDictionary_ShouldUseDefaultLanguage_WhenLanguageIsNull() {
        // Given
        String dictType = "errors";
        String language = null;
        Response expectedResponse = Response.ok().build();
        
        when(translationService.importTranslationsData(eq(testUser), eq("en"), eq(attachment)))
            .thenReturn(expectedResponse);
        
        // When
        Response response = delegator.uploadDictionary(httpServletRequest, dictType, language, attachment);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationService).importTranslationsData(eq(testUser), eq("en"), eq(attachment));
    }

    @Test
    void uploadDictionary_ShouldThrowException_WhenInvalidDictType() {
        // Given
        String invalidDictType = "invalid";
        String language = "en";
        
        // When & Then
        assertThrows(RestJwtException.class, () -> {
            delegator.uploadDictionary(httpServletRequest, invalidDictType, language, attachment);
        });
    }

    @Test
    void downloadDictionary_ShouldHandleNullLanguage_WhenDictTypeIsFrontend() {
        // Given
        String dictType = "frontend";
        String language = null;
        Response expectedResponse = Response.ok().build();
        
        when(translationService.exportFrontendTranslations(any(File.class), isNull()))
            .thenReturn(expectedResponse);
        
        // When
        Response response = delegator.downloadDictionary(httpServletRequest, dictType, language);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationService).exportFrontendTranslations(any(File.class), isNull());
    }

    @Test
    void uploadDictionary_ShouldHandleMultipleLanguages_WhenDictTypeIsFrontend() {
        // Given
        String dictType = "frontend";
        String languages = "de,es,it";
        DictionaryImportResponse importResponse = DictionaryImportResponse.builder()
            .totalImported(10)
            .errors(Collections.emptyList())
            .build();
        Response expectedResponse = Response.status(Response.Status.CREATED).entity(importResponse).build();
        
        when(translationService.importFrontendTranslations(eq(testUser), eq(languages), eq(attachment)))
            .thenReturn(expectedResponse);
        
        // When
        Response response = delegator.uploadDictionary(httpServletRequest, dictType, languages, attachment);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationService).importFrontendTranslations(eq(testUser), eq(languages), eq(attachment));
    }
}
