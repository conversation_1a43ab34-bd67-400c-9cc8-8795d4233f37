<PaymentStatusResponse xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd ../../schema/org/xcbl/path_delim/schemas/xcbl/v4_0/financial/v1_0/financial.xsd">
    <PaymentStatusResponseHeader>
        <PaymentStatusResponseID>PaymentStatusResponse/PaymentStatusResponseHeader/PaymentStatusResponseID</PaymentStatusResponseID>
        <PaymentStatusResponseIssueDate>2003-01-01T00:00:01</PaymentStatusResponseIssueDate>
        <PaymentStatusRequestID>
            <core:RefNum>PaymentStatusResponse/PaymentStatusResponseHeader/PaymentStatusRequestID/core:RefNum</core:RefNum>
        </PaymentStatusRequestID>
        <PayerParty>
            <core:PartyID>
                <core:Ident>PaymentStatusResponse/PaymentStatusResponseHeader/PayerParty/core:PartyID/core:Ident</core:Ident>
            </core:PartyID>
        </PayerParty>
        <FinancialServicesParty>
            <core:PartyID>
                <core:Ident>PaymentStatusResponse/PaymentStatusResponseHeader/FinancialServicesParty/core:PartyID/core:Ident</core:Ident>
            </core:PartyID>
        </FinancialServicesParty>
        <Language>
            <core:LanguageCoded>aa</core:LanguageCoded>
        </Language>
    </PaymentStatusResponseHeader>
    <ListOfPaymentStatusResponseDetail>
        <PaymentStatusResponseDetail>
            <PaymentRequestID>
                <core:RefNum>PaymentStatusResponse/ListOfPaymentStatusResponseDetail/PaymentStatusResponseDetail/PaymentRequestID/core:RefNum</core:RefNum>
            </PaymentRequestID>
            <ConfirmationID>PaymentStatusResponse/ListOfPaymentStatusResponseDetail/PaymentStatusResponseDetail/ConfirmationID</ConfirmationID>
            <ListOfPaymentException>
                <PaymentException>
                    <PaymentExceptionCoded>Success</PaymentExceptionCoded>
                </PaymentException>
            </ListOfPaymentException>
        </PaymentStatusResponseDetail>
    </ListOfPaymentStatusResponseDetail>
</PaymentStatusResponse>