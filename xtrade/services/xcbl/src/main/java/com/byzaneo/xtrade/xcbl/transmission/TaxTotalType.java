package com.byzaneo.xtrade.xcbl.transmission;

import java.math.BigDecimal;

import javax.xml.bind.annotation.*;

import lombok.*;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TaxTotalType", propOrder = {
    "taxAmount",
    "taxAmount2"
})
@Getter
@Setter
public class TaxTotalType implements java.io.Serializable {

  private static final long serialVersionUID = 1L;

  @XmlElement(name = "TaxAmount")
  protected BigDecimal taxAmount;
  @XmlElement(name = "TaxAmount2")
  protected BigDecimal taxAmount2;// to see about this as it is the same name

}
