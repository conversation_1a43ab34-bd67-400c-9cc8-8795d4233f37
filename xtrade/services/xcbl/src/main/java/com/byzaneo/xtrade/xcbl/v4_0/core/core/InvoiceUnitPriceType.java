//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2021.02.01 at 10:47:11 AM EET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import java.io.Serializable;

import javax.xml.bind.annotation.*;

/**
 * container element that holds the pricing information for a basic unit of an item.
 * <p>
 * Java class for InvoiceUnitPriceType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="InvoiceUnitPriceType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="UnitPriceValue" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ComplexBigDecimalType"/>
 *         &lt;element name="Currency" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}CurrencyType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InvoiceUnitPriceType", propOrder = {
    "unitPriceValue",
    "currency"
})
public class InvoiceUnitPriceType
    implements Serializable {

  private final static long serialVersionUID = 1L;
  @XmlElement(name = "UnitPriceValue", required = true)
  protected ComplexBigDecimalType unitPriceValue;
  @XmlElement(name = "Currency")
  protected CurrencyType currency;

  /**
   * Gets the value of the unitPriceValue property.
   * 
   * @return possible object is {@link ComplexBigDecimalType }
   */
  public ComplexBigDecimalType getUnitPriceValue() {
    if (unitPriceValue == null)
      unitPriceValue = new ComplexBigDecimalType();
    return unitPriceValue;
  }

  /**
   * Sets the value of the unitPriceValue property.
   * 
   * @param value allowed object is {@link ComplexBigDecimalType }
   */
  public void setUnitPriceValue(ComplexBigDecimalType value) {
    this.unitPriceValue = value;
  }

  /**
   * Gets the value of the currency property.
   * 
   * @return possible object is {@link CurrencyType }
   */
  public CurrencyType getCurrency() {
    return currency;
  }

  /**
   * Sets the value of the currency property.
   * 
   * @param value allowed object is {@link CurrencyType }
   */
  public void setCurrency(CurrencyType value) {
    this.currency = value;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((currency == null) ? 0 : currency.hashCode());
    result = prime * result + ((unitPriceValue == null) ? 0 : unitPriceValue.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    InvoiceUnitPriceType other = (InvoiceUnitPriceType) obj;
    if (currency == null) {
      if (other.currency != null) return false;
    }
    else if (!currency.equals(other.currency)) return false;
    if (unitPriceValue == null) {
      if (other.unitPriceValue != null) return false;
    }
    else if (!unitPriceValue.equals(other.unitPriceValue)) return false;
    return true;
  }

}
