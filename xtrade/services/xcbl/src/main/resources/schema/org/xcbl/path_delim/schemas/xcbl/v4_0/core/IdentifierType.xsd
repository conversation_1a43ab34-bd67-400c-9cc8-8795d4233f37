<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="IdentifierType">
        <xsd:annotation>
            <xsd:documentation>is used to provide an identification number
            that is assigned by a standards agency.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element minOccurs="0" name="Agency" type="AgencyType">
                <xsd:annotation>
                    <xsd:documentation>identifies the standards agency, and if necessary, the codelist which the identification
        belongs to.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="Ident" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>holds the identification number assigned by the agency.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ListOfIdentifierType">
        <xsd:annotation>
            <xsd:documentation>contains one or more <!--code-->Identifier<!--/code--> elements.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element maxOccurs="unbounded" name="Identifier" type="IdentifierType">
                <xsd:annotation>
                    <xsd:documentation>is used to provide an identification number
            that is assigned by a standards agency.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
