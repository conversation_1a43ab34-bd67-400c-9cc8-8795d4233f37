<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="PaymentRecordType">
        <xsd:annotation>
            <xsd:documentation>contains a record of the type of payment instrument or other
        method used to make the payment.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element minOccurs="0" name="PaymentRef" type="ReferenceType">
                <xsd:annotation>
                    <xsd:documentation>contains a reference or other code provided by the financial
        institution that authorizes or settled the payment that may be used to identify
        the payment record.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:choice>
                <xsd:element name="CardInfo" type="CardInfoType">
                    <xsd:annotation>
                        <xsd:documentation>contains details of the credit or debit card that is being used
        for the payment.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="FITransfer" type="FITransferType">
                    <xsd:annotation>
                        <xsd:documentation>contains details about the transfer from one financial
        institution to another. </xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
