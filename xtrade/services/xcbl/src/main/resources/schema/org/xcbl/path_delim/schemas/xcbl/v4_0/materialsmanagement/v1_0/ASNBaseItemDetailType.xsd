<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="ASNBaseItemDetailType">
        <xsd:annotation>
            <xsd:documentation>contains the identifying and descriptive information for the line items in
        the ASN.  The <!--code-->TotalQuantity<!--/code--> element from <!--code-->BaseItemDetail<!--/code--> is used
        to hold the quantity of the item that is being shipped in this advance shipment notice.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="LineItemNum" type="core:LineItemNumType">
                <xsd:annotation>
                    <xsd:documentation>is a set of numbers uniquely identifying the line item for the
					number. In the case where the seller's system splits a line item number into 2
					line items, the buyer item number will remain the same for both line items, but
					the seller item number will be different.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="LineItemType" type="core:LineItemTypeType">
                <xsd:annotation>
                    <xsd:documentation>identifies whether the line item is a component group (consists of other line items),
					a typical line item, or a textual line item.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ParentItemNumber" type="core:LineItemNumberReferenceType">
                <xsd:annotation>
                    <xsd:documentation>identifies the line item that groups this and other line items.
					This is done using <!--code-->LineItemNumberReference<!--/code-->, which specifies the number of the
					parent line item and whether this reference is the Buyer or Seller's number.
					<!--code-->LineItemNumberReference<!--/code--> is a string content model of datatype integer that
					contains an attribute to specify whether the <!--code-->ParentItemNumber<!--/code--> is a Seller or Buyer
					number, with the default being Buyer. This value references the parent item
					represented a group.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ItemIdentifiers" type="core:ItemIdentifiersType">
                <xsd:annotation>
                    <xsd:documentation>details all of the part numbers assigned to the item.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfDimension" type="core:ListOfDimensionType">
                <xsd:annotation>
                    <xsd:documentation>lists one or more quantitative measurements of the item.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ASNQuantities" type="ASNQuantitiesType">
                <xsd:annotation>
                    <xsd:documentation>specifies the quantities of the item pertaining to the ship notice.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="CountryOfOrigin" type="core:CountryType">
                <xsd:annotation>
                    <xsd:documentation>specifies the country code for the country in which the line item
					was manufactured.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="CountryOfDestination" type="core:CountryType">
                <xsd:annotation>
                    <xsd:documentation>specifies the country code for the country in which the delivery
					is finally received.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNLineItemParty" type="ASNLineItemPartyType">
                <xsd:annotation>
                    <xsd:documentation>identifies the parties relevant to the item.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="HazardousMaterials" type="core:HazardousType">
                <xsd:annotation>
                    <xsd:documentation>contains the hazardous information for the line item.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="DetailResponseCoded" type="core:DetailResponseCodeType">
             	<xsd:annotation>
                    <xsd:documentation>specifies the response for the item in the advance shipment notice.</xsd:documentation>
                </xsd:annotation>
           	</xsd:element>
            <xsd:element minOccurs="0" name="DetailResponseCodedOther" type="xsd:string">
            	<xsd:annotation>
                    <xsd:documentation>is used to provide a non-standard <!--code-->DetailResponseCode<!--/code-->.
        This element is mandatory if the value of <!--code-->DetailResponseCoded<!--/code--> is
        'Other'. These codes should not contain white space unless absolutely
        necessary.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNLineItemReferences" type="ASNLineItemReferencesType">
                <xsd:annotation>
                    <xsd:documentation>describes the references at the line item level if different than the
        header level. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
			<xsd:element minOccurs="0" name="ItemPackagingReference" type="core:ItemPackagingReferenceType">
             	<xsd:annotation>
                	<xsd:documentation>identifies which package the line item is in.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfDestinationRef" type="core:ListOfDestinationRefType">
             	<xsd:annotation>
                    <xsd:documentation>holds a list of all the destinations for the line
        item.</xsd:documentation>
                </xsd:annotation>
        	</xsd:element>
        	<xsd:element minOccurs="0" name="ASNItemDates" type="ASNItemDatesType">
			 	<xsd:annotation>
			    	<xsd:documentation>holds a collection of dates relative to this item.</xsd:documentation>
				</xsd:annotation>
        	</xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
