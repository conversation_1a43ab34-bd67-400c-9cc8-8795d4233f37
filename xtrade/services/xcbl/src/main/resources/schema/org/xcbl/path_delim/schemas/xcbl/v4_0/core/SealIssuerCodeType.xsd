<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="SealIssuerCodeType">
        <xsd:annotation>
            <xsd:documentation>This code identifies the issuer of the seal number. This code list is derived from EDIFACT 9303 (Sealing party coded)</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consolidator">
                <xsd:annotation>
                    <xsd:documentation>Party which consolidates cargo.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Unknown">
                <xsd:annotation>
                    <xsd:documentation>The sealing party is unknown.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="QuarantineAgency">
                <xsd:annotation>
                    <xsd:documentation>Agency responsible for the administration of statutory disease controls on the movement of people, animals and plants.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Carrier">
                <xsd:annotation>
                    <xsd:documentation>Party undertaking or arranging transport of goods between named points.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Customs">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Shipper">
                <xsd:annotation>
                    <xsd:documentation>Party which, by contract with a carrier, consigns or sends goods with the carrier, or has conveyed by him.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TerminalOperator">
                <xsd:annotation>
                    <xsd:documentation>Party which handles the loading and unloading of marine vessels.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
