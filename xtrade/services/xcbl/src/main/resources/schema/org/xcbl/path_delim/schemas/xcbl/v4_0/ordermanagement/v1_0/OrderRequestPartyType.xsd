<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="OrderRequestPartyType">
        <xsd:annotation>
            <xsd:documentation>holds information on the parties for the
          <!--code-->OrderRequest<!--/code-->, including unique ID's and address
          information.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element minOccurs="0" name="BuyerParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains the information for the entity purchasing the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="SellerParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains the information for the entity selling the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ShipToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>is the party that is receiving the goods or services.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="BillToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>describes the party to receive the invoice.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="RemitToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains the information for the party will be paid..</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ShipFromParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains party information identifying the location from which the
          items are to be shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfPartyCoded" type="core:ListOfPartyCodedType">
                <xsd:annotation>
                    <xsd:documentation>contains information relevant to describing parties other than the
          source target parties which are relevant to the time series data. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
