package com.byzaneo.xtrade.service;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.GenericDAO;
import com.byzaneo.commons.service.*;
import com.byzaneo.query.Query;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.Report;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.dao.DocumentDAO.DocumentField;
import com.byzaneo.xtrade.dao.mongo.MongoIndexableDocument;
import com.byzaneo.xtrade.util.ReportedCallable;
import org.activiti.engine.delegate.DelegateExecution;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.mongodb.core.aggregation.*;

import javax.faces.model.SelectItem;
import javax.persistence.metamodel.SingularAttribute;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;

/**
 * Search service used to integrate indexing system like Lucene between user's search requests and database.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @version 1.7
 * @company Byzaneo
 * @date 2008-06-01 14:35:17
 */
public interface DocumentService extends Initializable, Disposable {
  String SERVICE_NAME = "xtdDocumentService";

  String INDEX_OPERATIONS_NAME = "xtdIndexOperations";

  /** Reference of the root folder document for documentation management */
  String DOCUMENT_ROOT_REFERENCE = "ROOT";

  /**
   * Database document's fields
   */
  public enum Field {
    DOC_ID,
    DOC_REF,
    DOC_OWNER,
    DOC_TYPE,
    DOC_FROM,
    DOC_TO,
    DOC_DATE,
    DOC_STATUS,
    DOF_TYPE,
    DOF_SUBTYPE,
    DOF_URI,
    DOF_CONTENTS;
  }

  /**
   * Defines the policy to apply on parent document action (Archiving, Removing...) to its children.
   *
   * @since 7.0 GNX-1576
   */
  public enum DocumentChildrenPolicy {
    /** Cascading the action */
    Cascade("Cascade"),
    /** Detaching the parent from its children */
    Detach("Detach"),
    /** Ignore parent action */
    Ignore("Ignore");

    private DocumentChildrenPolicy(String value) {
      this.value = value;
    }

    private String value;

    public String getValue() {
      return value;
    }

  }

  /**
   * @return the documentation's directory path
   * @since 8.0
   */
  Path getDocumentationDirectory();

  public Path getDefaultReportDirectory();

  /**
   * @param type  of the document
   * @param query to filter the count
   * @return the count of document denoted by the given query
   * @since 7.0
   */
  <D extends Document> long count(Query query);

  /**
   * @param type  of the document
   * @param query to filter the count
   * @return the count of document denoted by the given query
   * @since 9.0
   */
  <D extends Document> long count(Class<D> type, Query query);

  /**
   * @param type  of the indexable
   * @param query to filter the count
   * @return the count of document denoted by the given query
   * @since 7.0
   */
  <I extends Indexable> long countIndexable(Class<I> type, Query query);

  /**
   * @param type of the indexable
   * @param json query to filter the count
   * @return the count of document denoted by the given query
   * @since 7.0
   */
  <I extends Indexable> long countIndexable(Class<I> type, final String jsonQuery);

  /**
   * @param type  of indexable
   * @param field to distinct
   * @param query to filter the distinct result (optional)
   * @return the list of distinct value in the indexable collection
   * @since 8.0
   */
  <I extends Indexable, T> List<T> distinctIndexable(Class<I> type, String field, Query query, Class<T> result);

  Document getDocument(Long id);

  Document getDocumentLazy(Long id);

  <D extends Document> D getDocument(Class<D> type, Long id);

  <D extends Document> D getDocument(Class<D> type, Long id, boolean loadIndex);

  Document getDocumentByReference(String ref);

  List<Document> findByIndexReferences(Set<String> entityRefs);

  Document findByIndexReferenceAndLoadFiles(String entityRef);

  Document getDocument(Indexable indexable);

  <D extends Document> D getDocument(Class<D> type, Indexable indexable);

  Document getDocumentByUUID(String uuid);

  /**
   * Used for backward document search compatibility. Prefers {@link #search(Class, Query, Pageable)} methods.
   *
   * @since 5.5
   */
  <D extends Document> Page<D> getDocuments(Class<D> type, Pageable pageable, List<String> owner,
      List<FileType> filetypes, String doctype, String from, String to,
      boolean recipientsNotNull, String referenceLike, Date fromDate,
      Date toDate, DocumentStatusEntity... status);

  List<Document> getDocuments(List<Long> ids);

  /**
   * @param query    query
   * @param pageable (optional)
   * @return Page
   * @since 5.5
   */
  Page<Document> search(String query, Pageable pageable);

  /**
   * @param query    query
   * @param pageable (optional)
   * @return Page
   * @since 5.5
   */
  Page<Document> search(Query query, Pageable pageable);

  /**
   * @param query query
   * @return List
   * @since 5.5
   */
  List<Document> search(Query query);

  /**
   * Used to manage inherited {@link Document} search.
   *
   * @param type     {@link Document} subclass
   * @param query    query
   * @param pageable (optional)
   * @return Page
   * @since 5.5
   */
  <D extends Document> Page<D> search(Class<D> type, String query, Pageable pageable);

  /**
   * Used to manage inherited {@link Document} search.
   *
   * @param type     {@link Document} subclass
   * @param query    query
   * @param pageable (optional)
   * @return Page
   * @since 5.5
   */
  <D extends Document> Page<D> search(Class<D> type, Query query, Pageable pageable);

  /**
   * @see GenericDAO#search(Class, Query, Pageable, String...)
   * @since 6.0
   */
  <D extends Document> Page<D> search(Class<D> type, Query query, Pageable pageable, String... fetches);

  /**
   * @param type          (optional) default is {@link Document}
   * @param specification (optional) if <code>null</code>, no filtering
   * @param pageable      (optional) if <code>null</code>, returns all entities which match this search
   * @return the list of searched document
   * @since 7.0
   */
  <D extends Document> Page<D> search(Class<D> type, Specification<D> specification, Pageable pageable);

  /** @since XTD-330 */
  Map<String, Map<String, Number>> getReportCount(List<DocumentField> groupFields, List<DatePeriod> periods, DocumentField countField,
      String owner,
      List<String> docType, List<String> froms, List<String> tos, boolean recipientsNotNull, List<DocumentStatus> status)
      throws ServiceException;

  <D extends Document> List<D> saveDocuments(Collection<D> docs);

  <D extends Document> List<D> saveDocuments(Collection<D> docs, boolean updateIndex);

  <D extends Document> List<D> detachDocuments(Collection<D> docs);

  <D extends Document> D save(D doc);

  <D extends Document> List<D> saveAll(Collection<D> docs);

  <D extends Document> D saveDocument(D doc);

  void removeDocuments(Collection<Document> docs);

  void removeDocument(Document doc);

  void removeDocuments(Collection<Document> docs, boolean deleteFile);

  void removeDocument(Document doc, boolean deleteFile);

  /**
   * Removes the documents and its associated files referenced by the given indexables.
   *
   * @param indexables referencing the documents to remove
   * @return true, if the actual remove process has been called (no insurance the removing was successful)
   * @see #getDocument(Indexable)
   * @see #removeDocument(Document, boolean)
   */
  boolean removeDocuments(Indexable... indexables);

  /* -- ATTRIBUTES -- */

  List<String> getDocumentTypes();

  List<String> getDocumentFroms();

  List<String> getDocumentTos();

  /**
   * @param attribute   attribute to retrieve (projection)
   * @param query       to apply. If <code>null</code>, all attributes will be returned
   * @param firstResult the position of the first returned element
   * @return the list of distinct document's attributes.
   */
  <T> List<T> searchAttribute(SingularAttribute<Document, T> attribute, Query query, int firstResult, int maxResults);

  /* -- FILES -- */

  DocumentFile getDocumentFile(Long id);

  List<DocumentFile> getDocumentFiles(Collection<Long> ids);

  InputStream getContent(DocumentFile dof);

  String getUri(String name, InputStream content, String scheme, String enconding, String mimetype, File directory);

  String getUri(String name, File file, String scheme, String encoding, String mimetype);

  List<DocumentFile> searchFiles(Query query);

  /**
   * Attaches an existing file as a {@link DocumentFile} to the {@link Document} denoted by the the given indexable.
   *
   * @param indexable used to retrieve the document
   * @param file      to add as a {@link DocumentFile} to the document
   * @since 8.0
   */
  void addFileToDocument(Indexable indexable, File file, String action, String comment);

  /**
   * @param owner owner
   * @return the root document folder for the given owner. Creates it if necessary.
   * @since 6.0
   */
  Document getRootFolder(String owner);

  /**
   * @param owner owner
   * @param name  name
   * @return the root document folder for the given owner and name (as document's reference). Creates it if necessary.
   * @since 8.0
   */
  Document getRootFolder(String owner, String name);

  /**
   * @param owner           owner
   * @param name            name
   * @param createIfMissing createIfMissing
   * @return the root document folder for the given owner and name (as document's reference). Creates it if necessary when createIfMissing
   *     is true, otherwise return null when the folder is not existing.
   * @since 8.0
   */
  Document getRootFolder(String owner, String name, boolean createIfMissing);

  /**
   * @param parentFolder parentFolder
   * @param folderName   folderName
   * @return the document folder with the given name in the parent folder. Creates it if necessary.
   * @since 6.0
   */
  Document getFolder(Document parentFolder, String folderName);

  /**
   * @param folder the parent folder document
   * @param file   the file to add to the folder
   * @return the {@link DocumentFile} persisted into the parent folder document and associated to the given file (moved to the documentation
   *     directory)
   * @throws FileAlreadyExistsException TODO
   * @since 6.0
   */
  DocumentFile addFolderFile(Document folder, File file) throws FileAlreadyExistsException;

  /**
   * @param folder             the parent folder document
   * @param filename           the name of the file to add
   * @param fileAuthor
   * @param fileDescription    {@link DocumentFile#description File Description}
   * @param daysBeforeDeletion
   * @param fileContents       the content of the file to add
   * @return the {@link DocumentFile} persisted into the parent folder document and associated to a wrote file from the given fileContents
   * @since 6.0
   */
  DocumentFile addFolderFile(Document folder, String filename, String fileAuthor, String fileDescription, Integer daysBeforeDeletion,
      byte[] fileContents);

  /**
   * @param folder       the parent folder document
   * @param file         the name of the file to update
   * @param fileContents the content of the file to update
   * @return the {@link DocumentFile} persisted into the parent folder document and associated to a wrote file from the given fileContents
   * @throws FileNotFoundException if file not exist
   * @since 6.0
   */
  Document updateFolderFile(Document folder, DocumentFile file, byte[] fileContents) throws FileNotFoundException;

  /**
   * @param folder the parent folder document
   * @param file   the {@link DocumentFile} to remove (removes also the associated file)
   * @since 6.0
   */
  Document removeFile(Document folder, DocumentFile file);

  /**
   * @param folder      to remove
   * @param deleteFiles removes files associated to the {@link DocumentFile} in the folder
   * @since 6.0
   */
  void removeFolder(Document folder, boolean deleteFiles);

  /**
   * Verify if the file name has changed for the given fileId
   *
   * @param folder
   * @param fileId
   * @param newName
   * @return true if it doesn't exist another file with the same id and name
   */
  boolean fileNameHasChanged(Document folder, long fileId, String newName);

  /**
   * @param folder  the parent folder document
   * @param file    the {@link DocumentFile} to rename
   * @param newName the new name for the file
   * @return the updated parent folder document
   * @since 6.0
   */
  Document renameFile(Document folder, DocumentFile file, String newName) throws FileAlreadyExistsException;

  /**
   * Sets the description for this document
   *
   * @param file
   */
  DocumentFile setDocumentFile(DocumentFile file);

  /**
   * @param dofin source document file
   * @param dofout result document file
   * @param engine used to process the transformation
   * @throws TransformException
   * @throws ServiceException
   */
  // void transform(DocumentFile dofin, DocumentFile dofout, TransformEngine engine)
  // throws TransformException, ServiceException;

  // -- INDEXING --

  /**
   * @return <code>true</code> if indexing is enabled
   */
  boolean isIndexing();

  // - SAVE -

  <D extends Document> D saveDocumentIndex(D document);

  <D extends Document> List<D> saveDocumentIndexes(Collection<D> documents);

  <I extends Indexable> I saveIndexable(I indexeable);

  <I extends Indexable> Collection<I> saveIndexables(Collection<I> indexables);

  <I extends com.byzaneo.xtrade.bean.Indexable> List<I> saveContextIndexables(List<I> indexables);

  // - GET -

  /**
   * Sets the Indexable of the given document, based on it's Index id or reference.
   *
   * @param <D>      Type of Document.
   * @param document the document to update.
   * @return the passed document.
   */
  <D extends Document> D populateIndex(D document);

  <I extends Indexable> I getIndexable(Document document);

  <I extends Indexable> Optional<I> getIndexable(Index index);

  <I extends Indexable> I getIndexable(Class<I> type, String identifier);

  // - REMOVE -

  void removeDocumentIndex(Document document);

  void removeDocumentIndexes(Collection<Document> documents);

  void removeIndex(Index index);

  void removeIndexable(Indexable indexable);

  void removeIndexables(Collection<? extends Indexable> indexables);

  // - SEARCH -

  /**
   * @param type      of the entity to find
   * @param jsonQuery used to filter result. If <code>blank</code>, a <code>null</code> result is returned
   * @return return a single result based on the given query
   */
  <I extends Indexable> I searchIndexable(Class<I> type, String jsonQuery);

  /**
   * @param type  of the entity to find
   * @param query used to filter result. If <code>null</code>, a <code>null</code> result is returned
   * @return return a single result based on the given query
   */
  <I extends Indexable> I searchIndexable(Class<I> type, Query query);

  /**
   * @param type      of the entity to find
   * @param jsonQuery used to filter result. If <code>blank</code>, an empty paged result is returned
   * @param pageable  sort and pagination information. If <code>null</code>, a single page with all the entries will be returned.
   * @return the page containing the elements with the given type and filtered by the query.
   */
  <I extends Indexable> Page<I> searchIndexables(Class<I> type, String jsonQuery, int limit, Pageable pageable);

  /**
   * @param type     of the entity to find
   * @param query    used to filter result. If <code>null</code>, an empty paged result is returned
   * @param pageable sort and pagination information. If <code>null</code>, a single page with all the entries will be returned.
   * @return the page containing the elements with the given type and filtered by the query.
   */
  <I extends Indexable> Page<I> searchIndexables(Class<I> type, Query query, Pageable pageable);

  <D extends Document, I extends Indexable> List<D> searchIndexedDocuments(
      Class<D> documentType, Class<I> indexableType, Query indexableQuery, Pageable pageable);

  /**
   * @param aggregation command to process
   * @param outputType  expected result type
   * @return the aggregation result
   */
  <O, I extends Indexable> List<O> aggregate(TypedAggregation<I> aggregation, Class<O> outputType);

  /**
   * @param inputType  indexable input type
   * @param outputType output result type
   * @param match      (optional) the aggregation match query
   * @param operations to use within the aggregation
   * @return the aggregation result
   * @see #aggregate(TypedAggregation, Class)
   */
  <O, I extends Indexable> List<O> aggregate(Class<I> inputType, Class<O> outputType, Query match, AggregationOperation... operations);

  /**
   * @param collection where to create the index
   * @param field      to index
   * @param indexName  name of the index to create
   * @param ascending  direction of the index
   * @return <code>true</code> if the index has been successfully created.
   * @throws IndexOutOfBoundsException if the collection's index count reaches its maximum.
   */
  boolean indexField(String collection, String field, String indexName, boolean ascending);

  // - INDEX DATA -

  /**
   * @param collection holding the index data
   * @param query      to filter the index data
   * @param pageable   to retrieve a slice of the index data
   * @return the page of index data in the given collection filtered by the query
   */
  Page<MongoIndexableDocument> searchIndexData(String collection, Query query, Pageable pageable);

  /**
   * @param collection holding the index data
   * @param query      to filter the index data
   * @param pageable   to retrieve a slice of the index data
   * @param mapper     Map data to IndexData type converter
   * @return the page of index data in the given collection filtered by the query
   */
  <I extends IndexData> Page<I> searchIndexData(String collection, Query query, Pageable pageable,
      Function<? super Map<String, Object>, I> mapper);

  // - INDEXING -

  /**
   * @return the collection of managed indexers
   * @since 7.0
   */
  Map<String, Indexer<?>> getIndexers();

  // -- REPORTING --

  /**
   * @param report            from which creates the document
   * @param generateXmlReport if <code>true</code>, marshals the report into XML and attach it as {@link DocumentFile} to the created
   *                          {@link Document}.
   * @param metadata          if <code>true</code>, adds metadata to the created document (backward compatibility or if indexing is
   *                          disabled
   * @param throwOnError      if <code>true</code>, throws an error if a problem occurs. Otherwise, <code>null</code> document is returned
   * @return the brand-new report document
   * @since 7.0
   */
  Document createReportDocument(Report report, boolean generateXmlReport, boolean metadata, boolean throwOnError);

  /**
   * @param report       from which creates the document
   * @param xmlReport    the XML report representation will be attached to the document as a {@link DocumentFile} (optional)
   * @param metadata     if <code>true</code>, adds metadata to the created document (backward compatibility or if indexing is disabled
   * @param throwOnError if <code>true</code>, throws an error if a problem occurs. Otherwise, <code>null</code> document is returned
   * @return the brand-new report document
   * @since 7.0
   */
  Document createReportDocument(Report report, File xmlReport, boolean metadata, boolean throwOnError);

  // -- PURGE --

  /**
   * Purge all documents based on the given query
   *
   * @param query       mandatory query (cannot be empty)
   * @param execution   (mandatory only if called from DocumentPurgeTask)
   * @param dryRun      set true if the modifications won't be persisted
   * @param isReprocess set true if reprocess
   * @param reporting   optional parameter if the process is called from a ReportedFutureTask
   * @return
   */
  public int purge(final Query query, DelegateExecution execution, final boolean dryRun, final boolean isReprocess,
      Optional<ReportedCallable<Document>> reporting);

  /**
   * Purge all documents based on the given query in a FutureTask and allows reporting progress and loggs from it
   *
   * @param query
   * @param readOnly
   * @return
   */
  public ReportedFutureTask<Document> purgeFutureTask(final Query query, final boolean readOnly);

  // -- ARCHIVING --

  /**
   * @param type           {@link Document} kind
   * @param query          to gets the documents to archive
   * @param archiveTo      to of the returned {@link Document} archive
   * @param archiveFrom    from of the returned {@link Document} archive
   * @param archiveOwner   owner of the returned {@link Document} archive
   * @param backup         if <code>true</code>, creates a compressed file containing the archived documents in the backup directory
   * @param remove         if <code>true</code>, removes the documents found with the given query. Consider to use backup option to avoid
   *                       losing data.
   * @param childrenPolicy policy to apply to the processed documents' children
   * @param dryRun         if <code>true</code>, the back-end remains untouched
   * @return a {@link Document} representing the archive with as document's file the created compressed file (see backup option) and as
   *     children, all the archive documents.
   */
  ReportedFutureTask<Document> archiving(Class<Document> type, Query query,
      String archiveTo, String archiveFrom, String archiveOwner,
      boolean backup, boolean remove,
      DocumentChildrenPolicy childrenPolicy, boolean dryRun);

  /**
   * @param stream to restore
   * @return the root {@link Document} represented by the given archive. The returned document holds all the restored documents and their
   *     hierarchy.
   */
  ReportedFutureTask<Document> restoring(InputStream stream);

  /**
   * @return List of all the collections names in the database
   */
  List<String> getCollectionNames();

  /**
   * @return List of all the collections names in beans
   */
  List<String> getDocumentCollectionNames();

  /**
   * @param deads
   * @return List of dead documents in the database
   */
  List<DeadDocument> saveDeadDocuments(List<Document> deads, String process);

  /**
   * @param query
   * @param pageable
   * @return
   */
  Page<DeadDocument> searchDeadDocuments(Query query, Pageable pageable);

  /**
   * @param files
   * @return
   */
  List<DeadDocument> searchDeadDocumentsByFiles(Collection<String> files);

  /**
   * @param type
   * @param query
   * @param pageable
   * @return
   */
  <D extends Document> List<D> searchDocumentsWithIndexable(Class<D> type, Query query, Pageable pageable);

  /**
   * @param type
   * @param owner
   * @return
   */
  <R extends com.byzaneo.xtrade.bean.Report> List<SelectItem> getReportFieldItems(Class<R> type, String owner, String field);

  long countDeadDocuments(Query query);

  List<String> searchByFieldsText(Query query, String field, String value, int limit);

  boolean removeDeadDocuments(DeadDocument... deads);

  <I extends Indexable> Map<Long, Integer> getAttachFileCount(List<I> indexes, String... actions);

  void markDocumentConsultStatusAsRead(Indexable indexable, boolean canUpdateConsultStatus, String username);

  public <I extends Indexable> List<String> getOwnersFromIdexable(Class<I> inputType);

  public Collection<Document> searchAllChildren(Document doc, Query query);

  public int deleteAllReconciliationRelations(Collection<Long> docsToDelete);

  boolean attachFileToADocFromRTE(String uuid, String fullPath, String type, String action, String subtype, String comment,
      List<Document> contextDocuments) throws ServiceException;

  void updateDocumentLock(String lockProcessInstanceId, List<Long> docIds);

  ReentrantLock getMapLockOnMultithreading();

  boolean isLockedByOtherThread();

  void unlockLockedDocumentsMap();

  void updateSqlDocumentStatus(List<String> invoices);

  boolean tryLockDocumentsMap();

  Map<String, Long> getDocumentsByGroup(String[] fieldNames, Query query);

  Map<Document, String> getDocumentsOverLimit(List<Document> documents);

  List<Object[]> getDocumentChangedStatus(String action, Date start, Date end, Document.ProcessingWay processingWay,
      List<String> selfBillingList, List<String> partnersList, int limit, int offset);

}
