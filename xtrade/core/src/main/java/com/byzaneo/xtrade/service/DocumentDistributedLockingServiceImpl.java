package com.byzaneo.xtrade.service;

import com.byzaneo.commons.bean.Persistent;
import com.byzaneo.commons.config.HazelcastConfiguration;
import com.byzaneo.commons.exception.LockedDirectoryException;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.dao.IndexDataDAO;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.cp.lock.FencedLock;
import com.hazelcast.map.IMap;
import org.activiti.engine.delegate.DelegateExecution;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.xtrade.service.DocumentLockingServiceImpl.LOCKED_BY_OTHER_BT;
import static java.lang.System.currentTimeMillis;
import static java.util.Collections.emptyList;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * <AUTHOR> <<EMAIL>>
 */
@Service(DocumentDistributedLockingServiceImpl.SERVICE_NAME)
@Conditional(HazelcastConfiguration.IsMultiNodeCondition.class)
public class DocumentDistributedLockingServiceImpl implements DocumentLockingService {

  public static final String SERVICE_NAME = "xtdDocumentDistributedLockingService";

  private static final Logger log = getLogger(DocumentDistributedLockingServiceImpl.class);

  @Value("${process.node.input.lock.waitSeconds:2}")
  private int waitSeconds;

  private final ThreadLocal<List<String>> documentsLockedByThread = new ThreadLocal<List<String>>() {
    @Override
    protected List<String> initialValue() {
      return new ArrayList<String>();
    }
  };

  @Autowired(required = false)
  @Qualifier(HazelcastConfiguration.HAZELCAST_SPRING_NAME)
  private transient HazelcastInstance hazelcastInstance;

  @Autowired
  @Qualifier(MultiNodeService.SERVICE_NAME)
  private transient MultiNodeService multiNodeService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private transient DocumentService documentService;

  @Autowired
  @Qualifier(IndexDataDAO.DAO_NAME)
  IndexDataDAO indexDataDAO;

  @Autowired
  @Qualifier(SharedLockingService.SERVICE_NAME)
  private transient SharedLockingService sharedLockingService;

  public static final String DOCUMENT_COLLECTION = "document";

  @Override
  public void init() throws Exception {
    final long start = currentTimeMillis();
    log.info("STARTING DOCUMENT DISTIBUTED LOCKING SERVICE...");
    log.info("DOCUMENT DISTIBUTED LOCKING SERVICE STARTED in {} ms.", currentTimeMillis() - start);

  }

  @Override
  public void destroy() throws Exception {
    final long start = currentTimeMillis();
    log.info("STOPPING DOCUMENT DISTIBUTED LOCKING SERVICE...");
    log.info("DOCUMENT DISTIBUTED LOCKING SERVICE STOPPED in {} ms.", currentTimeMillis() - start);

  }

  /**
   * @see com.byzaneo.commons.service.DocumentLockingService#getLocks()
   */
  @SuppressWarnings("unchecked")
  @Override
  public Map<String, FencedLock> getLocks() {
    return Collections.emptyMap();
  }

  /**
   * @see com.byzaneo.commons.service.DocumentLockingService#addDocumentLocks(java.util.Collection)
   */
  @Override
  public <T extends Persistent<?>> void addDocumentLocks(Collection<T> documents) {
    Iterator<T> iterator = documents.iterator();
    log.info("Locking document(s)");
    while (iterator.hasNext()) {
      this.addDocumentLock(iterator.next());
    }
  }

  @Override
  public <T extends Persistent<?>> void addDocumentLocks(String repo, DelegateExecution delegateExecution,
      boolean unlockLockedDocumentsMap, boolean addDocumentsToExecution, List<T> documentsToAdd) {
    List<Document> documentList = documentsToAdd.stream()
        .filter(Document.class::isInstance)
        .map(Document.class::cast)
        .filter(doc -> doc.getId() != null)
        .collect(Collectors.toList());
    if (documentList.isEmpty()) {
      if (unlockLockedDocumentsMap) {
        multiNodeService.unlockDistributedLockedDocumentsMap();
      }
      return;
    }

    //documents will be added in hazelcast map
    IMap<String, Map<String, Set<Long>>> lockedDocumentsMap = multiNodeService.getDistributedLockedDocumentsMap();
    List<Long> docIds = documentList.stream()
        .map(Document::getId)
        .collect(Collectors.toList());
    //will add new documents in map
    sharedLockingService.addDocumentsInSharedMap(repo, delegateExecution, documentList, docIds, lockedDocumentsMap,
        unlockLockedDocumentsMap);
    documentsLockedByThread.get()
        .addAll(docIds.stream()
            .map(String::valueOf)
            .collect(Collectors.toList()));
    sharedLockingService.lockMongoAndRelationalDatabase(multiNodeService.getDistributedLockedDocumentsMap()
            .get(delegateExecution.getProcessInstanceId()), delegateExecution.getProcessInstanceId(), repo, docIds,
        delegateExecution, documentList, addDocumentsToExecution);
  }

  /**
   * @throws InterruptedException
   * @see com.byzaneo.commons.service.DocumentLockingService#addDocumentLock(com.byzaneo.commons.bean.Persistent)
   */
  @Override
  public <T extends Persistent<?>> void addDocumentLock(T document) {
    if (document == null || document.getId() == null) {
      return;
    }
    addLock(String.valueOf(document.getId()));
  }

  /**
   * @see com.byzaneo.commons.service.DocumentLockingService#addDirectoryLock(java.io.File, boolean)
   */
  @Override
  public List<String> addDirectoryLock(File directory, boolean lockSubdirectories) {
    if (directory == null || !directory.isDirectory()) {
      return emptyList();
    }
    List<String> pathsLocked = new ArrayList<String>();
    log.info("Locking input folder(s)");
    addLock(directory.getAbsolutePath());
    pathsLocked.add(directory.getAbsolutePath());
    if (lockSubdirectories) {
      log.info("\t Locking input folder sudirectories");
      FileHelper.findAllSubdirs(directory)
          .forEach(dir -> {
            addLock(dir.getAbsolutePath());
            pathsLocked.add(dir.getAbsolutePath());
          });
    }
    return pathsLocked;
  }

  /**
   * @see com.byzaneo.commons.service.DocumentLockingService#addLock(java.lang.String)
   */
  @Override
  public void addLock(String key) {
    if (!tryLock(key)) {
      // if the lock can't be acquired we stop the process
      // this process will be stopped
      log.info(key + " " + LOCKED_BY_OTHER_BT);
      throw new LockedDirectoryException(key + " " + LOCKED_BY_OTHER_BT);
    }
  }

  @Override
  public boolean tryLock(String key) {
    if (key == null) {
      return true;
    }
    boolean isLocked;
    FencedLock fencedLock = hazelcastInstance.getCPSubsystem()
        .getLock(key);
    if (fencedLock.isLockedByCurrentThread()) {
      return true;
    }
    log.debug(" Try Locking {} by Thread {} at {} ", key, Thread.currentThread(), LocalDateTime.now());
    isLocked = fencedLock.tryLock();
    if (isLocked) {
      documentsLockedByThread.get()
          .add(key);
    }
    return isLocked;
  }

  @Override
  public void unlockAllDocumentsInDatabase() {
    List<Long> documentsToBeRemovedFromDistributedMap = documentsLockedByThread.get()
        .stream()
        .map(StringHelper::toLongValue)
        .filter(value -> value != null)
        .collect(Collectors.toList());

    if (!documentsToBeRemovedFromDistributedMap.isEmpty()) {
      log.debug("Current thread contains this list of documents: " + documentsToBeRemovedFromDistributedMap);
      IMap<String, Map<String, Set<Long>>> lockedDocumentsMap = multiNodeService.getDistributedLockedDocumentsMap();
      sharedLockingService.unlockSharedMemory(documentsToBeRemovedFromDistributedMap, lockedDocumentsMap);
    }
  }

  @Override
  public void unlockAllDocuments() {
    log.debug(" Unlocking all from Local Thread by Thread {} at {} ", Thread.currentThread(), LocalDateTime.now());
    documentsLockedByThread.get()
        .forEach(docId -> unlock(docId));
    documentsLockedByThread.get()
        .clear();
  }

  /**
   * @see com.byzaneo.commons.service.DocumentLockingService#unlock(java.util.List)
   */
  @Override
  public <T extends Persistent<?>> void unlock(List<T> documents) {
    if (isNotEmpty(documents)) {
      documents.forEach(document -> this.unlock(document));
    }
  }

  /**
   * @see com.byzaneo.commons.service.DocumentLockingService#unlock(com.byzaneo.commons.bean.Persistent)
   */
  @Override
  public <T extends Persistent<?>> void unlock(T document) {
    if (document == null || document.getId() == null) {
      return;
    }
    unlock(String.valueOf(document.getId()));
  }

  /**
   * @see com.byzaneo.commons.service.DocumentLockingService#unlock(java.lang.String)
   */
  @Override
  public void unlock(String key) {
    if (key == null) {
      return;
    }
    FencedLock fencedLock = hazelcastInstance.getCPSubsystem()
        .getLock(key);
    if (!fencedLock.isLocked()) {
      return;
    }
    log.debug(" Unlocking {} by Thread {} at {} ", key, Thread.currentThread(), LocalDateTime.now());
    fencedLock.unlock();
  }

  /**
   * @see com.byzaneo.commons.service.DocumentLockingService#isDocumentLocked(com.byzaneo.commons.bean.Persistent)
   */
  @Override
  public <T extends Persistent<?>> boolean isDocumentLocked(T document) {
    if (document == null || document.getId() == null) {
      return false;
    }

    FencedLock fencedLock = hazelcastInstance.getCPSubsystem()
        .getLock(String.valueOf(document.getId()));
    return fencedLock.isLocked();
  }

  @Override
  public boolean tryLock(List<File> files) {
    if (files == null || files.isEmpty()) {
      return true;
    }

    List<File> lockedFiles = new ArrayList<>();
    log.debug(" Try Locking list of files by Thread {} at {} ", Thread.currentThread(), LocalDateTime.now());
    try {

      for (File file : files) {
        if (file == null) continue;
        String path = file.getAbsolutePath();
        FencedLock fencedLock = hazelcastInstance.getCPSubsystem()
            .getLock(path);

        if (fencedLock.isLockedByCurrentThread()) {
          continue;
        }
        // in case of a distributed locking mechanism we want other nodes to wait before a directory was processed
        boolean isLocked = fencedLock.tryLock(waitSeconds, SECONDS);
        log.debug("\t - '{}' was locked: {}", path, isLocked);
        if (isLocked) {
          lockedFiles.add(file);
          documentsLockedByThread.get()
              .add(path);
        }
        else {
          unlockFiles(lockedFiles);
          return false;
        }
      }
    }
    catch (Exception e) {
      unlockFiles(lockedFiles);
      return false;
    }
    return true;

  }

  @Override
  public void unlockFiles(List<File> files) {
    for (File file : files) {
      unlockFile(file.getAbsolutePath());
    }
  }

  @Override
  public void unlockFile(String filePath) {
    unlock(filePath);
    documentsLockedByThread.get()
        .remove(filePath);

  }

  void setHazelcastInstance(HazelcastInstance hazelcastInstance) {
    this.hazelcastInstance = hazelcastInstance;
  }

  @Override
  public boolean isFileLocked(File file) {

    if (file == null || file.getAbsolutePath() == null) {
      return false;
    }

    FencedLock fencedLock = hazelcastInstance.getCPSubsystem()
        .getLock(file.getAbsolutePath());
    return fencedLock.isLocked();
  }

  public Set<Long> getLockedDocumentsInAnotherProcesses(String currentProcessId) {
    Set<Long> lockedDocsId = new HashSet<Long>();
    if (currentProcessId != null) {
      IMap<String, Map<String, Set<Long>>> lockedDocumentsMap = multiNodeService.getDistributedLockedDocumentsMap();
      lockedDocsId = lockedDocumentsMap.entrySet()
          .stream()
          .filter(entry -> !currentProcessId
              .equals(entry.getKey()))
          .flatMap(entry -> entry.getValue()
              .values()
              .stream())
          .flatMap(Set::stream)
          .collect(Collectors.toSet());
    }
    return lockedDocsId;
  }

  @Override
  public void unlockDocumentsInSharedMap(Set<String> endedProcessesIds) {
    IMap<String, Map<String, Set<Long>>> lockedDocumentsMap = multiNodeService.getDistributedLockedDocumentsMap();
    for (String endedProcessId : endedProcessesIds) {
      lockedDocumentsMap.remove(endedProcessId);
    }
  }
}
