package com.byzaneo.xtrade.process.listener;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.exception.MongoFileSizeLimitExceededException;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.query.util.QueryHelper;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.api.ReportAcknowledgement;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.Indexable;
import com.byzaneo.xtrade.bean.Report;
import com.byzaneo.xtrade.process.VariableHelper;
import com.byzaneo.xtrade.process.el.FixedValue;
import com.byzaneo.xtrade.process.reprocess.*;
import com.byzaneo.xtrade.process.task.ActivitiRunntimeException;
import com.byzaneo.xtrade.service.*;
import com.byzaneo.xtrade.util.*;
import com.google.common.collect.ImmutableList;
import com.google.gson.*;
import org.activiti.engine.delegate.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.spi.StandardLevel;
import org.bson.BsonMaximumSizeExceededException;
import org.hibernate.Hibernate;
import org.slf4j.*;

import java.io.*;
import java.lang.reflect.Type;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.*;

import static com.byzaneo.commons.api.EventAction.create;
import static com.byzaneo.commons.api.EventAction.error;
import static com.byzaneo.commons.bean.FileType.JSON;
import static com.byzaneo.commons.bean.Logs.Tag.END;
import static com.byzaneo.commons.bean.Logs.Tag.START;
import static com.byzaneo.commons.event.EventBuilder.fromSource;
import static com.byzaneo.commons.util.GsonHelper.newJsonWriter;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.commons.util.StringHelper.toObjectMap;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.EXECUTION_LINK;
import static com.byzaneo.xtrade.process.Variable.FATAL_ERROR;
import static com.byzaneo.xtrade.process.Variable.INDEXABLES;
import static com.byzaneo.xtrade.process.Variable.LOGGERS;
import static com.byzaneo.xtrade.process.Variable.LOG_LEVEL;
import static com.byzaneo.xtrade.process.Variable.PROCESS_BACKUP_ID;
import static com.byzaneo.xtrade.process.Variable.PROCESS_LOGS_DIR;
import static com.byzaneo.xtrade.process.Variable.PROCESS_OWNER;
import static com.byzaneo.xtrade.process.Variable.PROCESS_WORK_DIR;
import static com.byzaneo.xtrade.process.Variable.REPORT;
import static com.byzaneo.xtrade.process.Variable.REPORT_DOCUMENT;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_INSTANCE;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_NAME;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_NO_FILES_TO_PROCESS;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_NO_HEADER_PARAMS_FILE;
import static com.byzaneo.xtrade.process.Variable.REST_TRIGGER_TIMESTAMP;
import static com.byzaneo.xtrade.process.Variable.S3_DOWNLOAD_DIR;
import static com.byzaneo.xtrade.process.Variable.WRITE_EMPTY_REPORT;
import static com.byzaneo.xtrade.process.VariableHelper.addDeads;
import static com.byzaneo.xtrade.process.VariableHelper.getBoolean;
import static com.byzaneo.xtrade.process.VariableHelper.getDeadDirectory;
import static com.byzaneo.xtrade.process.VariableHelper.getDeads;
import static com.byzaneo.xtrade.process.VariableHelper.getDocuments;
import static com.byzaneo.xtrade.process.VariableHelper.getExpressionText;
import static com.byzaneo.xtrade.process.VariableHelper.getFile;
import static com.byzaneo.xtrade.process.VariableHelper.getInputBackupDirectory;
import static com.byzaneo.xtrade.process.VariableHelper.getInstance;
import static com.byzaneo.xtrade.process.VariableHelper.getList;
import static com.byzaneo.xtrade.process.VariableHelper.getMap;
import static com.byzaneo.xtrade.process.VariableHelper.getProcessDefinition;
import static com.byzaneo.xtrade.process.VariableHelper.getProcessInstance;
import static com.byzaneo.xtrade.process.VariableHelper.getString;
import static com.byzaneo.xtrade.process.VariableHelper.getVariable;
import static com.byzaneo.xtrade.process.VariableHelper.resolveExecutionId;
import static com.byzaneo.xtrade.service.ProcessService.PROCESS_EVENT_TYPE;
import static com.byzaneo.xtrade.util.DocumentHelper.getDocumentsTree;
import static com.byzaneo.xtrade.util.DocumentHelper.getFiles;
import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static com.byzaneo.xtrade.util.DocumentHelper.searchFiles;
import static com.byzaneo.xtrade.util.ReportHelper.copyReportLogFileAndDelete;
import static com.byzaneo.xtrade.util.ReportHelper.formattedElapsedTime;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.lang.String.format;
import static java.util.Collections.emptyList;
import static java.util.Optional.ofNullable;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.io.FileUtils.copyFileToDirectory;
import static org.apache.commons.io.FileUtils.getTempDirectory;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static org.apache.logging.log4j.Level.INFO;

/**
 * Execution process listener dedicated to listen the logs and to manage the report.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * <AUTHOR> Sarrazin <<EMAIL>>
 * @company Byzaneo
 * @date Jun 17, 2011
 * @ @since 5.0
 */
public class ReportListener implements ExecutionListener, Serializable {
  private static final long serialVersionUID = -751002886035490187L;

  private static final Logger log = LoggerFactory.getLogger(ReportListener.class);

  public static final String LOG_FILE_PREFIX = "report";

  private static final String[] DEFAULT_LOGGED = { Log4jService.DEFAULT_PACKAGE };

  private static final Long TIMEOUT = Long.valueOf(120L);

  // Services
  private transient Log4jService logService;

  private transient DocumentService documentService;

  private transient WorkflowDocumentStatusService workflowDocumentStatusService;

  private transient DocumentTimelineService documentTimeLineService;

  private transient ReconciliationRelationService recRelService;

  private transient DocumentErrorService documentErrorService;

  private transient ProcessBackupService processBackupService;

  // Commons
  private Expression enabled = new FixedValue(TRUE.toString());

  // Logger definition
  private Expression loggerLevel = new FixedValue(INFO.toString());

  private Expression loggedPackages;

  // Check persistency at the end of the process
  private Expression checkPersistency = new FixedValue(FALSE.toString());

  // Allow multiple execution of this process in parallel
  private Expression allowParallel = new FixedValue(TRUE.toString());

  // Report's content
  private Expression addVariables = new FixedValue(FALSE.toString());

  private Expression addDocuments = new FixedValue(TRUE.toString());

  private Expression addDeads = new FixedValue(TRUE.toString());

  private Expression addLogs = new FixedValue(TRUE.toString());

  // Report's persistency
  private Expression persist = new FixedValue(FALSE.toString());

  private Expression persistReport = new FixedValue(FALSE.toString());

  private Expression persistDeads = new FixedValue(FALSE.toString());

  private Expression writeEmptyReport = new FixedValue(FALSE.toString());

  private Expression writeAsync = new FixedValue(TRUE.toString());

  private Expression writeTimeoutInMinutes = new FixedValue(TIMEOUT);

  // Mail notification
  private Expression notifyOnlyIfProblem = new FixedValue(TRUE.toString());

  private Expression notifyAdminGroups;

  private Expression notifyUserGroups;

  // Report html/text template parameters (velocity)
  private Expression transformParameters;

  // Acknowledgement (ack since XTD-429)
  /** Enable the acknowledgement for this report */
  private Expression acknowledged = new FixedValue(FALSE.toString());

  /** Input document's files filtering query (required to resolves the inputs files) */
  private Expression acknowledgedInputsQuery;

  /** Output folder for the acknowledgement of this report */
  private Expression acknowledgementDirectory = new FixedValue(PROCESS_WORK_DIR.toStringPlaceHolder() + "/ack");

  /** Producer of the acknowledgement of this report */
  private Expression acknowledgementProducer = new FixedValue(DefaultAckProducer.class.getName());

  // Notification topic
  private Expression errorTopic;

  private Expression fatalTopic;

  /*
   * -- CONSTRUCTORS --
   */

  public ReportListener() {
    this.logService = getBean(Log4jService.class, Log4jService.SERVICE_NAME);
    this.documentService = getBean(DocumentService.class, DocumentService.SERVICE_NAME);
    this.documentTimeLineService = getBean(DocumentTimelineService.class, DocumentTimelineService.SERVICE_NAME);
    this.workflowDocumentStatusService = getBean(WorkflowDocumentStatusService.class, WorkflowDocumentStatusService.SERVICE_NAME);
    this.recRelService = getBean(ReconciliationRelationService.class, ReconciliationRelationService.SERVICE_NAME);
    this.documentErrorService = getBean(DocumentErrorService.class, DocumentErrorService.SERVICE_NAME);
    this.processBackupService = getBean(ProcessBackupService.class, ProcessBackupService.SERVICE_NAME);
  }

  public ReportListener(boolean enabled, Level loggerLevel, boolean persist, boolean persistReport) {
    this();
    this.enabled = new FixedValue(enabled);
    this.loggerLevel = new FixedValue(loggerLevel);
    this.persist = new FixedValue(persist);
    this.persistReport = new FixedValue(persistReport);
  }

  /*
   * -- LISTENER --
   */

  /** @see org.activiti.engine.delegate.ExecutionListener#notify(org.activiti.engine.delegate.DelegateExecution) */
  @Override
  public void notify(DelegateExecution execution) {
    try {
      if (!getBoolean(execution, enabled, true))
        return;
      Report report = getVariable(execution, REPORT);
      final Exception fatalError = getVariable(execution, FATAL_ERROR);
      if ((report == null) && (fatalError == null)) {
        log.info("Starting reporting...");
        this.startReporting(execution);
      }
      else if (report != null) {
        this.stopReporting(execution, report);
        log.info("Reporting Stopped...");
      }
      else {
        log.error("Fatal error cant be reported", fatalError);
        throw new ActivitiRunntimeException(fatalError);
      }
    }
    catch (InterruptedException | ExecutionException | TimeoutException e) {
      throw new ActivitiRunntimeException(e);
    }
  }

  /*
   * -- REPORTING --
   */

  private void startReporting(DelegateExecution execution) {
    // New report
    // Should we parameterize the report class to instantiate?
    Date currentDate = new Date();
    final Report report = new Report();
    execution.setVariable(REPORT.var, report);
    execution.setVariable(WRITE_EMPTY_REPORT.var, getBoolean(execution, writeEmptyReport));
    report.setCreationDate(currentDate);
    report.setReference(resolveExecutionId(execution));
    report.setProcessDefinition(getProcessDefinition(execution));
    report.setProcessInstance(getProcessInstance(execution));
    report.setFrom(report.getProcessDefinitionId()); // to = process definition identifier
    report.setTo(report.getProcessInstanceId()); // from = process instance identifier
    report.setType(FileType.REPORT.toString());
    report.setInProgress(true);
    report.setLogsFilePath(new File(VariableHelper.<File> getVariable(execution, PROCESS_LOGS_DIR), "report.html").getAbsolutePath());
    report.setOwners(VariableHelper.<String> getVariable(execution, PROCESS_OWNER));
    report.setLink(getVariable(execution, EXECUTION_LINK));
    Reprocess reprocess = (Reprocess) execution.getVariable(Reprocess.class.getSimpleName());
    if (reprocess != null) {
      report.setProcessBackupId(reprocess.getProcessBackupId());
      report.setS3DownloadDir(reprocess.getS3DownloadDir());
    }
    else {
      report.setProcessBackupId(getVariable(execution, PROCESS_BACKUP_ID));
      report.setS3DownloadDir(getVariable(execution, S3_DOWNLOAD_DIR));
    }

    // Starts recording logs
    final Level logLevel = getLevel(execution);
    execution.setVariable(LOG_LEVEL.var, logLevel);
    final String[] loggers = getLoggedPackageArray(execution);
    execution.setVariable(LOGGERS.var, loggers);
    log.info("Log recording is going to be started, report reference: " + report.getReference());
    String logFilePrefix = null;
    if (StringUtils.isNotEmpty(report.getProcessDefinitionName())) {
      logFilePrefix = String.join("_", LOG_FILE_PREFIX, report.getProcessDefinitionName(),
          StringUtils.replace(report.getReference(), ":", "_"));
    }

    ReportLogsWrapper reportLogsWrapper = this.logService.startLogsRecord(report.getReference(),
        // INFO level is needed to write log's tags to evaluate
        // process and tasks duration...
        logLevel, null, logFilePrefix, null, false, LayoutExtension.HTML, loggers);
    report.setTempLogFilePath(reportLogsWrapper.getTempLogFile()
        .getAbsolutePath());
    String appId = reportLogsWrapper.getAppId();
    report.setLogAppenderId(appId);

    Boolean isHeaderParamFileMissing = (Boolean) ofNullable(execution.getVariable(REST_TRIGGER_NO_HEADER_PARAMS_FILE.toString()))
        .orElse(FALSE);
    if (isHeaderParamFileMissing) {
      String restTriggerName = getVariable(execution, REST_TRIGGER_NAME);
      String restTriggerDateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
          .withZone(ZoneId.systemDefault())
          .format(
              Instant.ofEpochMilli(Long.parseLong(getVariable(execution, REST_TRIGGER_TIMESTAMP))));
      String instanceCode = getVariable(execution, REST_TRIGGER_INSTANCE);

      if ((Boolean) ofNullable(execution.getVariable(REST_TRIGGER_NO_FILES_TO_PROCESS.toString()))
          .orElse(FALSE))
        log.error(
            "Rest trigger {} in {} environment received a request at {} for which we didn't manage to save header param and file to treat.",
            restTriggerName, instanceCode, restTriggerDateTime);
      else
        log.warn("Rest trigger {} in {} environment received a request at {}.", restTriggerName, instanceCode, restTriggerDateTime);
    }


    // create the report here, but we only save the Report indexable (to have an in progress view over the process activity)
    // The report document will be crated here but will only be persisted at the end of the process (if necessary) otherwise we remove the
    // indexable create here
    Document reportDocument = ReportHelper.toReportDocument(report, new File(report.getLogsFilePath()), true);
    documentService.saveDocumentIndexes(Collections.singleton(reportDocument));
    execution.setVariable(REPORT_DOCUMENT.var, reportDocument);
  }

  private void stopReporting(final DelegateExecution execution, final Report report)
      throws InterruptedException, ExecutionException, TimeoutException {
    long start = System.currentTimeMillis();
    Marker timeMarker = MarkerFactory.getMarker(Log4jService.TIME_MARKER);
    log.info(timeMarker, "{} Report", START);

    final Exception fatalError = getVariable(execution, FATAL_ERROR);
    // booleans for report.set...
    final boolean reportDocs = getBoolean(execution, this.addDocuments, TRUE);
    final boolean reportDeads = getBoolean(execution, this.addDeads, TRUE);
    final boolean reportVars = getBoolean(execution, this.addVariables, FALSE);
    // booleans for documentService.save...
    final boolean persistDocs = getBoolean(execution, this.persist, TRUE);
    final boolean persistDead = getBoolean(execution, this.persistDeads, FALSE);
    final boolean persistRpt = getBoolean(execution, this.persistReport, TRUE);

    // Process Work Dir
    report.setProcessDeadQueue(VariableHelper.getDeadDirectory(execution)
        .getAbsolutePath());
    report.setProcessInputBackupDir(VariableHelper.getInputBackupDirectory(execution)
        .getAbsolutePath());
    report.setProcessAttachBackupDir(VariableHelper.getAttachBackupDirectory(execution)
        .getAbsolutePath());

    // set owners here in case of the process' owner execution
    // variable has been updated during the process (example
    // VariablesListener)
    report.setOwners(VariableHelper.<String> getVariable(execution, PROCESS_OWNER));
    // -- Documents --
    // gets contextual documents
    List<Document> ctxtdocs = new ArrayList<Document>(QueryHelper.search(
        getString(execution, null),
        Document.class,
        getVariable(execution, DOCUMENTS.toString(), emptyList())));
    List<Document> docsToSave = ctxtdocs.stream()
        .filter(this::persistDocument)
        .collect(Collectors.toList());
    List<Indexable> indexables = new ArrayList<Indexable>(
        getVariable(execution, INDEXABLES.toString(), emptyList()));
    // persists...
    if (persistDocs && fatalError == null) {
      try {
        docsToSave = this.documentService.saveDocuments(getDocumentsTree(docsToSave, false));
        // lazy loads (data used on XML report marshaling)
        docsToSave.forEach(d -> d.flattened()
            .forEach(c -> c.getFiles()
                .size()));
        indexables = new ArrayList<Indexable>(documentService.saveContextIndexables(indexables));
        // store reconciliationRelation, documentTimelines and wkf_document_status
        storeReconciliationRelations(execution);
        storeWorkflowStatuses(execution);
        storeTimelines(execution);

        List<String> ctxdocUuids = DocumentHelper.getFlattenDocuments(docsToSave)
            .stream()
            .map(doc -> doc.getUuid())
            .collect(Collectors.toList());
        List<DocumentError> documentErrors = VariableHelper.getDocumentErrors(execution)
            .stream()
            .filter(error -> ctxdocUuids.contains(error.getUuid()))
            .collect(
                Collectors.toList());
        if (CollectionUtils.isNotEmpty(documentErrors)) {
          documentErrorService.storeAll(documentErrors);
          log.info("{} new records were added into XTD_DOCUMENT_ERROR table", documentErrors.size());
        }
      }
      catch (BsonMaximumSizeExceededException ex) {
        Map<Document, String> documentsOverLimit = documentService.getDocumentsOverLimit(ctxtdocs);
        if (MapUtils.isEmpty(documentsOverLimit)) {
          throw ex;
        }

        documentsOverLimit.forEach((doc, docSize) -> log.error(
            "Document {} ({}MB) exceeds the maximum size limit of 16MB. This document will not be saved in the database",
            doc.getReference(), docSize));

        addDeads(execution, Stream.concat(getDeads(execution).stream(), documentsOverLimit.keySet()
                .stream())
            .collect(Collectors.toList()));

        setReportDocuments(report, ctxtdocs, reportDocs, fatalError);

        setReportDeads(report, execution, fatalError, reportDeads, persistDead);

        throw new MongoFileSizeLimitExceededException("Document exceeds the maximum file size accepted by Mongo (16MB)");
      }
      catch (Exception e) {
        log.error("Failed to save documents: ", e);
      }
    }
    else { // detachs...
      log.warn("Documents are not persisted (see persist option)");
      try {
        ctxtdocs = this.documentService.detachDocuments(ctxtdocs);
      }
      catch (Exception e) {
        log.warn("Failed to detach documents: " + ctxtdocs, e);
      }

    }

    setReportDocuments(report, ctxtdocs, reportDocs, fatalError);

    setReportDeads(report, execution, fatalError, reportDeads, persistDead);

    finalizeReport(report, report.getLogAppenderId(), getLoggedPackageArray(execution), reportVars, execution.getVariables(), execution,
        fatalError, start, persistRpt);

    log.info(timeMarker, "{} Report", END);
  }

  private void setReportDocuments(Report report, List<Document> ctxtdocs, boolean reportDocs, Exception fatalError) {
    List<com.byzaneo.xtrade.bean.ReportDocument> reportCtxDocs = DocumentHelper.cloneDeeplyDocumentsForReport(ctxtdocs);
    if (reportDocs && fatalError == null) {
      report.setDocuments(reportCtxDocs);
      report.addChildren(reportCtxDocs);
    }
  }

  private void setReportDeads(Report report, DelegateExecution execution, Exception fatalError, boolean reportDeads, boolean persistDead) {
    if (fatalError == null) {
      final List<Document> deads = this.applyDeadQueueChannelPolicy(execution);
      List<com.byzaneo.xtrade.bean.ReportDocument> reportDeadsDocs = DocumentHelper.cloneDeeplyDocumentsForReport(deads);
      report.setDeads(reportDeads ? reportDeadsDocs : null);
      if (!deads.isEmpty()) {
        updateProcessBackupDocuments(deads, execution);
        report.addChildren(reportDeadsDocs);
        if (persistDead) {
          documentService.saveDeadDocuments(deads, report.getProcessDefinitionName());
        }
      }
    }
  }

  private void updateProcessBackupDocuments(List<Document> deads, DelegateExecution execution) {
    String processBackupId = getVariable(execution, PROCESS_BACKUP_ID);
    if (processBackupId != null) {
      ProcessBackup processBackup = processBackupService.getProcessBackupById(processBackupId);
      if (processBackup != null && processBackup.getDocuments() != null) {
        Set<String> deadDocUuids = deads.stream()
            .map(Document::getUuid)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        for (ProcessBackupDocument doc : processBackup.getDocuments()) {
          if (doc.getUuid() != null && deadDocUuids.contains(doc.getUuid())) {
            doc.setDead(true);
          }
        }
        documentService.saveIndexable(processBackup);
      }
    }
  }

  public Report finalizeReport(Report report, String id, String[] loggers, boolean reportDeads, Map<String, Object> variables,
      DelegateExecution execution, Exception fatalError, long start, boolean persistRpt) {
    ReportLogsWrapper reportLogsWrapper = null;
    try {
      reportLogsWrapper = finalize(report, id, loggers, reportDeads, variables, execution, fatalError, start, persistRpt);
    }
    catch (BsonMaximumSizeExceededException ex) {
      throw ex;
    }
    catch (Exception e) {
      throw new ServiceException("Could not finalize report", e);
    }
    finally {
      deleteTempLogFile(reportLogsWrapper);
    }
    return report;
  }

  public ReportLogsWrapper finalize(Report report, String id, String[] loggers, boolean reportDeads, Map<String, Object> variables,
      DelegateExecution execution, Exception fatalError, long start, boolean persistRpt) throws Exception {
    ReportLogsWrapper reportLogsWrapper = null;

    log.info("Log recording is stopped, report reference: " + report.getReference());

    Document reportDocument = (Document) execution.getVariable(REPORT_DOCUMENT.var);

    // -- Update variables --
    updateVariablesOfReport(reportDeads, variables, execution, report);

    // -- Notify --
    updateReportNotification(report, execution);

    // -- Acknowledgement --
    processAcknowledgement(getBoolean(execution, acknowledged, FALSE), execution, fatalError);

    // -- Log elapsed time for the Report --
    printElapsedTime(start);
    log.error(Log4jService.EOF_REPORT);

    // -- Stop log recording --
    reportLogsWrapper = logService.stopLogsRecord(id, loggers);

    // in case of directory polling trigger when there are no files to process by the InputTask we stop the execution and do not create
    // report in case the process does not have write empty report checked and for that in InputTask we stop the logs so here we need to
    // provide a value to the reportLogsWrapper.getLevel()
    if (CollectionUtils.isEmpty(reportLogsWrapper.getLevel())) {
      SortedSet<Level> level = new TreeSet<Level>();
      level.add(Level.INFO);
      reportLogsWrapper.setLevel(level);
    }

    // -- Write empty report --
    StandardLevel logErrorLevel = reportLogsWrapper.getLevel()
        .first()
        .getStandardLevel();
    if (checkForWritingEmptyReport(report, id, loggers, execution, logErrorLevel))
      return reportLogsWrapper;

    // -- Remove temporary log file --
    copyTempFileLogIntoFinalLog(report, execution, reportLogsWrapper);

    // -- Status --
    setReportStatus(reportDocument, reportLogsWrapper);

    // -- Duration --
    setReportDuration(report);

    try {
      // -- Save Report Document --
      saveReportDocument(report, reportDocument, persistRpt);
    }
    catch (BsonMaximumSizeExceededException e) {
      throw e;
    }

    // -- Publishes process report event --
    fromSource(report).type(PROCESS_EVENT_TYPE)
        .action(create)
        .publish();

    return reportLogsWrapper;
  }

  public Document getReportDocumentFromIndexable(Report report) {
    return documentService.getDocument(report);
  }

  public void saveReportDocument(Report report, Document reportDocument, boolean persistRpt) {
    if (reportDocument == null) {
      log.error("Cannot save null report document");
      return;
    }

    if (!persistRpt && isEqualsStatusCode(DocumentStatus.OK, reportDocument.getStatus())) {
      documentService.removeIndexable(report);
    }
    else if (persistRpt) {
      reportDocument.setIndexValue(report);
      documentService.saveDocument(reportDocument);
    }
  }

  public void setReportDuration(Report report) {
    if (report == null)
      return;
    report.setDuration(System.currentTimeMillis() - report.getCreationDate()
        .toInstant()
        .toEpochMilli());
  }

  public void printElapsedTime(long start) {
    Marker timeMarker = MarkerFactory.getMarker(Log4jService.TIME_MARKER);
    log.info(timeMarker, "{} Report ({})", END, formattedElapsedTime(System.currentTimeMillis() - start));
  }

  /**
   * Report is not stored and displayed if the 'write empty report' is disabled in BO, there is no Warn/Error in the report logs, and the
   * report does not have any processed documents or deads.
   */
  public boolean checkForWritingEmptyReport(Report report, String id, String[] loggers, DelegateExecution execution, StandardLevel level) {
    if (!getBoolean(execution, writeEmptyReport, FALSE) && ("INFO".equals(level.name())) && (report.getDocumentCount() <= 0) &&
        (report.getDeadCount() <= 0)) {
      documentService.removeIndexable(report);
      // delete WORK_DIR
      FileHelper.deleteFile(getVariable(execution, PROCESS_WORK_DIR));
      return true;
    }
    return false;
  }

  /**
   * Applies the dead queue channel policy
   * 
   * @param execution current context
   * @return the list of dead document in the current context
   */
  private List<Document> applyDeadQueueChannelPolicy(DelegateExecution execution) {
    final List<Document> deads = getDeads(execution);
    if (isEmpty(deads))
      return emptyList();

    // ONEDAY manages more dead queue channel policies
    // for now, we are copying dead documents' files to [data.dir]/[process
    // name]/
    final List<File> deadFiles = getFiles(deads, true);
    if (isEmpty(deadFiles))
      return deads;

    File inputBackupDir = getInputBackupDirectory(execution);
    File deadDir = getDeadDirectory(execution);
    log.info("{} dead documents' files copied to {}", deadFiles.size(), deadDir.getAbsolutePath());
    for (Document doc : deads) {
      for (DocumentFile dof : doc.getFiles()) {
        if (dof.getFile() == null)
          continue;
        try {
          if (dof.getFile() != null && dof.getFile()
              .isFile()) {
            // copy only files which are in "input_backup" directory because they are the only input files that we need to reprocess
            String dofFileName = dof.getFile()
                .getName();
            File inputBackupFile = new File(inputBackupDir, dofFileName);
            if (inputBackupFile.exists() || ReportHelper.checkIfExistsInOriginalProcess(execution, dofFileName)) {
              File newCopy = new File(deadDir, dofFileName);
              copyFileToDirectory(dof.getFile(), deadDir, true);
              dof.setFile(newCopy);
            }
            else if (doc.getParent() != null && doc.getParent()
                .getFiles() != null && DocumentHelper.findFileByType(doc.getParent(), FileType.EDI) != null) {
                  // it can be a message from an interchange. In this case we need to copy the entire interchange
                  dof = DocumentHelper.findFileByType(doc.getParent(), FileType.EDI);
                  inputBackupFile = new File(inputBackupDir, dof.getFile()
                      .getName());
                  if (inputBackupFile.exists()) {
                    File newCopy = new File(deadDir, dof.getFile()
                        .getName());
                    copyFileToDirectory(dof.getFile(), deadDir, true);
                    dof.setFile(newCopy);
                  }
                }
          }
        }
        catch (Exception e) {
          log.error("Unable to copy {} file copied to dead queue channel folder ({})",
              dof.getFile()
                  .getName(),
              e.getMessage());
        }
      }
    }

    // send file to queue
    if (this.getErrorTopic() != null && this.getErrorTopic()
        .getExpressionText() != null) {
      for (Document dead : deads) {
        fromSource(dead)
            .type(PROCESS_EVENT_TYPE)
            .action(error)
            .topic(this.getErrorTopic()
                .getExpressionText())
            .publish();

      }
    }

    return deads;
  }

  private Level getLevel(DelegateExecution execution) {
    return Level.toLevel(getString(execution, loggerLevel, INFO.toString()));
  }

  private String[] getLoggedPackageArray(DelegateExecution execution) {
    final List<String> pkgs = getList(execution, loggedPackages);
    return isEmpty(pkgs) ? DEFAULT_LOGGED : pkgs.toArray(new String[pkgs.size()]);
  }

  /*
   * -- ACCESSORS --
   */

  public boolean isDisabled() {
    return !getBoolean(null, enabled, true);
  }

  public Expression getEnabled() {
    return enabled;
  }

  public void setEnabled(Expression enabled) {
    this.enabled = enabled;
  }

  // -- LOGS --

  public Expression getLoggerLevel() {
    return loggerLevel;
  }

  public void setLoggerLevel(Expression loggerLevel) {
    this.loggerLevel = loggerLevel;
  }

  public Expression getLoggedPackages() {
    return loggedPackages;
  }

  public void setLoggedPackages(Expression loggedPackages) {
    this.loggedPackages = loggedPackages;
  }

  public Expression getCheckPersistency() {
    return checkPersistency;
  }

  public void setCheckPersistency(Expression checkPersistency) {
    this.checkPersistency = checkPersistency;
  }

  public Expression getAllowParallel() {
    return allowParallel;
  }

  public void setAllowParallel(Expression allowParallel) {
    this.allowParallel = allowParallel;
  }

  // -- CONTENT --

  public Expression getAddVariables() {
    return addVariables;
  }

  public void setAddVariables(Expression addVariables) {
    this.addVariables = addVariables;
  }

  public Expression getAddDocuments() {
    return addDocuments;
  }

  public void setAddDocuments(Expression addDocuments) {
    this.addDocuments = addDocuments;
  }

  public Expression getAddLogs() {
    return addLogs;
  }

  public void setAddLogs(Expression addLogs) {
    this.addLogs = addLogs;
  }

  public Expression getAddDeads() {
    return addDeads;
  }

  public void setAddDeads(Expression addDeads) {
    this.addDeads = addDeads;
  }

  // -- PERSISTENCY --

  public Expression getPersist() {
    return persist;
  }

  public void setPersist(Expression persist) {
    this.persist = persist;
  }

  public Expression getPersistDeads() {
    return persistDeads;
  }

  public void setPersistDeads(Expression persistDeads) {
    this.persistDeads = persistDeads;
  }

  public Expression getPersistReport() {
    return persistReport;
  }

  public void setPersistReport(Expression persistReport) {
    this.persistReport = persistReport;
  }

  public Expression getWriteEmptyReport() {
    return writeEmptyReport;
  }

  public void setWriteEmptyReport(Expression writeEmptyReport) {
    this.writeEmptyReport = writeEmptyReport;
  }

  public Expression getWriteAsync() {
    return writeAsync;
  }

  public void setWriteAsync(Expression writeAsync) {
    this.writeAsync = writeAsync;
  }

  public Expression getWriteTimeoutInMinutes() {
    return writeTimeoutInMinutes;
  }

  public void setWriteTimeoutInMinutes(Expression writeTimeoutInMinutes) {
    this.writeTimeoutInMinutes = writeTimeoutInMinutes;
  }

  // -- NOTIFICATION --

  public Expression getNotifyOnlyIfProblem() {
    return notifyOnlyIfProblem;
  }

  public void setNotifyOnlyIfProblem(Expression notifyOnlyIfProblem) {
    this.notifyOnlyIfProblem = notifyOnlyIfProblem;
  }

  public Expression getNotifyAdminGroups() {
    return notifyAdminGroups;
  }

  public void setNotifyAdminGroups(Expression notifyAdminGroups) {
    this.notifyAdminGroups = notifyAdminGroups;
  }

  public Expression getNotifyUserGroups() {
    return notifyUserGroups;
  }

  public void setNotifyUserGroups(Expression notifyUserGroups) {
    this.notifyUserGroups = notifyUserGroups;
  }

  public Expression getTransformParameters() {
    return transformParameters;
  }

  public void setTransformParameters(Expression transformParameters) {
    this.transformParameters = transformParameters;
  }

  // -- ACKNOWLEDGEMENT --

  public Expression getAcknowledged() {
    return acknowledged;
  }

  public void setAcknowledged(Expression acknowledged) {
    this.acknowledged = acknowledged;
  }

  public Expression getAcknowledgedInputsQuery() {
    return acknowledgedInputsQuery;
  }

  public void setAcknowledgedInputsQuery(Expression acknowledgedInputsQuery) {
    this.acknowledgedInputsQuery = acknowledgedInputsQuery;
  }

  public Expression getAcknowledgementDirectory() {
    return acknowledgementDirectory;
  }

  public void setAcknowledgementDirectory(Expression acknowledgementDirectory) {
    this.acknowledgementDirectory = acknowledgementDirectory;
  }

  public Expression getAcknowledgementProducer() {
    return acknowledgementProducer;
  }

  public void setAcknowledgementProducer(Expression acknowledgementProducer) {
    this.acknowledgementProducer = acknowledgementProducer;
  }

  // -- NOTIFICATION TOPIC --

  public Expression getErrorTopic() {
    return errorTopic;
  }

  public void setErrorTopic(Expression errorTopic) {
    this.errorTopic = errorTopic;
  }

  public Expression getFatalTopic() {
    return fatalTopic;
  }

  public void setFatalTopic(Expression fatalTopic) {
    this.fatalTopic = fatalTopic;
  }

  // -- OVERRIDE --

  /** @see java.lang.Object#toString() */
  @Override
  public String toString() {
    return format(
        "ReportListener [enabled=%s, loggerLevel=%s, loggedPackages=%s, checkPersistency=%s, allowParallel=%s, " + "variables=%s, documents=%s, deads=%s, " +
            "persist=%s, persistReport=%s, writeEmptyReport=%s, " + "writeAsync=%s, writeTimeout=%s, " +
            "notifyOnlyIfProblem=%s, notifyAdminGroups=%s, notifyUserGroups=%s, " + "transformParameters=%s, " +
            "ack=%s, ackInputsQuery=%s, ackDirectory=%s, ackProducer=%s]",
        getExpressionText(enabled),
        getExpressionText(loggerLevel),
        getExpressionText(loggedPackages),
        getExpressionText(checkPersistency),
        getExpressionText(allowParallel),
        getExpressionText(addVariables),
        getExpressionText(addDocuments),
        getExpressionText(addDeads),
        getExpressionText(persist),
        getExpressionText(persistReport),
        getExpressionText(writeEmptyReport),
        getExpressionText(writeAsync),
        getExpressionText(writeTimeoutInMinutes),
        getExpressionText(notifyOnlyIfProblem),
        getExpressionText(notifyAdminGroups),
        getExpressionText(notifyUserGroups),
        getExpressionText(transformParameters),
        getExpressionText(acknowledged),
        getExpressionText(acknowledgedInputsQuery),
        getExpressionText(acknowledgementDirectory),
        getExpressionText(acknowledgementProducer));
  }

  /**
   * Default acknowledgement of the execution report producer.
   * <p/>
   * Produces JSon {@link ReportAcknowledgement} representation.
   */
  public static final class DefaultAckProducer implements ReportAcknowledgementProducer {

    private static final List<String> FIELDS_TO_EXCLUDE = Arrays.asList(
        "uri", "actionName", "processInstanceId", // DocumentFile
        "model", "index"); // Document

    private static final Gson GSON = new GsonBuilder().setPrettyPrinting()
        .setDateFormat("yyyy-MM-dd HH:mm:ssZ")
        .setVersion(0d)
        .addSerializationExclusionStrategy(new ExclusionStrategy() {
          @Override
          public boolean shouldSkipField(FieldAttributes f) {
            return FIELDS_TO_EXCLUDE.contains(f.getName());
          }

          @Override
          public boolean shouldSkipClass(Class<?> clazz) {
            return false;
          }
        })
        .registerTypeAdapter(File.class, new GsonHelper.AbstractGsonAdapter<File>() {
          @Override
          public File deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            throw new JsonParseException("JSon File desirializaion not supported");
          }

          @Override
          public JsonElement serialize(File src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.getName());
          }
        })
        .create();

    /**
     * @see com.byzaneo.xtrade.api.ReportAcknowledgementProducer#produce(com.byzaneo.xtrade.api.ReportAcknowledgement, java.io.OutputStream)
     */
    @Override
    public void produce(ReportAcknowledgement acknowledgement, OutputStream output) {
      try (final Writer writer = new OutputStreamWriter(output)) {
        GSON.toJson(
            acknowledgement,
            com.byzaneo.xtrade.bean.ReportAcknowledgement.class,
            newJsonWriter(writer, false, true, false));
      }
      catch (Exception e) {
        log.error("Failed to write acknowledgement", e);
      }
    }

    /** @see com.byzaneo.xtrade.api.ReportAcknowledgementProducer#getFileType() */
    @Override
    public FileType getFileType() {
      return JSON;
    }
  }

  public void updateReportNotification(Report report, DelegateExecution execution) {
    report.setNotifyOnlyIfProblem(getBoolean(execution, notifyOnlyIfProblem, FALSE));
    report.setNotifyGroups(getList(execution, notifyAdminGroups));
    report.setNotifyIfProblemGroups(getList(execution, notifyUserGroups));
    report.setNotifyParameters(toObjectMap(getMap(execution, transformParameters)));
  }

  public void processAcknowledgement(final boolean ack, DelegateExecution execution, Exception fatalError) {
    if (ack) {
      // outputs
      final List<Document> outputs = fatalError != null ? Collections.<Document> emptyList()
          : getDocuments(execution);
      // deads
      final List<Document> deads = fatalError != null ? ImmutableList.<Document> builder()
          .addAll(getDocuments(execution))
          .addAll(getDeads(execution))
          .build() : getDeads(execution);
      // all
      final List<Document> docs = ImmutableList.<Document> builder()
          .addAll(outputs)
          .addAll(deads)
          .build();
      // inputs
      final String qinputs = getString(execution, acknowledgedInputsQuery);
      final List<DocumentFile> inputs = isNotEmpty(qinputs) ? searchFiles(docs, qinputs)
          : Collections.<DocumentFile> emptyList();
      // acknowledgement
      final ReportAcknowledgement acknowledgement = new com.byzaneo.xtrade.bean.ReportAcknowledgement(inputs,
          outputs, deads);
      // producer
      final ReportAcknowledgementProducer producer = getInstance(execution, acknowledgementProducer,
          DefaultAckProducer.class);
      // writes
      final File ackFile = new File(
          // file's directory
          getFile(execution, acknowledgementDirectory,
              new File(VariableHelper.<File> getVariable(execution, PROCESS_WORK_DIR,
                  getTempDirectory()), "ack")),
          // file's name
          format("ACK-%s%s", execution.getId(), producer.getFileType()
              .getExtension()));
      ackFile.getParentFile()
          .mkdirs();
      try (final OutputStream ackout = new FileOutputStream(ackFile)) {
        producer.produce(acknowledgement, ackout);
      }
      catch (IOException e) {
        log.error("Error writing acknowledgment", e);
      }
    }
  }

  public boolean copyLogFileAndDeleteIt(Report report) {
    return copyReportLogFileAndDelete(report);
  }

  // FOR UNIT TESTS
  public void updateVariablesOfReport(boolean reportVars, Map<String, Object> variables, DelegateExecution execution,
      Report report) {
    ReportHelper.updateVariablesOfReport(reportVars, variables, execution, report);
  }

  public void copyTempFileLogIntoFinalLog(Report report, DelegateExecution execution, ReportLogsWrapper reportLogsWrapper) {
    ReportHelper.copyTempFileLogIntoFinalLog(report, execution, reportLogsWrapper);
  }

  public void setReportStatus(Document reportDocument, ReportLogsWrapper reportLogsWrapper) {
    ReportHelper.setReportStatus(reportDocument, reportLogsWrapper);
  }

  public void deleteTempLogFile(ReportLogsWrapper reportLogsWrapper) {
    ReportHelper.deleteTempLogFile(reportLogsWrapper);
  }

  private void storeReconciliationRelations(DelegateExecution execution) {
    List<ReconciliationRelation> relations = VariableHelper.getReconciliationRelations(execution);
    if (CollectionUtils.isNotEmpty(relations)) {
      List<ReconciliationRelation> relationsToSave = relations.stream().
          filter(relation -> persistDocument(relation.getInvoiceDocument()))
          .collect(Collectors.toList());
      if (relations.size() != relationsToSave.size()) {
        log.error("{} reconciliation relation(s) can't be saved because corresponding documents are not persisted",
            relations.size() - relationsToSave.size());
      }
      if (relationsToSave.size() > 0) {
        recRelService.saveAll(relationsToSave);
        log.info("{} new records were added into Reconciliation_relation table", relationsToSave.size());
      }
    }
  }

  private void storeWorkflowStatuses(DelegateExecution execution) {
    List<WorkflowDocumentStatus> wkfStatuses = VariableHelper.getWkfDocumentStatuses(execution);
    if (CollectionUtils.isNotEmpty(wkfStatuses)) {
      List<WorkflowDocumentStatus> wkfStatusesToSave = wkfStatuses.stream()
          .filter(workflowDocumentStatus -> persistDocument(workflowDocumentStatus.getDocument()))
          .collect(Collectors.toList());
      if (wkfStatuses.size() != wkfStatusesToSave.size()) {
        log.error("{} workflow status(es) can't be saved because corresponding documents are not persisted",
            wkfStatuses.size() - wkfStatusesToSave.size());
      }
      if (wkfStatusesToSave.size() > 0) {
        workflowDocumentStatusService.storeAll(wkfStatusesToSave);
        log.info("{} new records were added into WKF_DOCUMENT_STATUS table", wkfStatusesToSave.size());
      }
    }
  }

  private void storeTimelines(DelegateExecution execution) {
    List<DocumentTimeline> timelines = VariableHelper.getDocumentTimelines(execution);
    if (CollectionUtils.isNotEmpty(timelines)) {
      List<DocumentTimeline> timelinesToSave = timelines.stream()
          .filter(timeline -> persistDocument(timeline.getDocument()))
          .collect(Collectors.toList());
      if (timelines.size() != timelinesToSave.size()) {
        log.error("{} timeline(s) can't be saved because corresponding documents are not persisted",
            timelines.size() - timelinesToSave.size());
      }
      if (timelinesToSave.size() > 0) {
        documentTimeLineService.storeAll(timelinesToSave);
        log.info("{} new records were added into TML_TIMELINE table", timelinesToSave.size());
      }
    }
  }

  private boolean persistDocument(Document document) {
    if (Hibernate.isInitialized(document.getMetaGroup())) {
      PropertyGroup metaGroup = document.getMetaGroup();
      if (metaGroup != null) {
        return String.valueOf(true)
            .equals(metaGroup
                .getPropertyValue(Document.Metadata.PERSIST_DOCUMENT.name()));
      }
    }
    return true;
  }
}
