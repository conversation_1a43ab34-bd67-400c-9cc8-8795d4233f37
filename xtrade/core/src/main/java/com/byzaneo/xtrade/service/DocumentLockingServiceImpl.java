package com.byzaneo.xtrade.service;

import com.byzaneo.commons.bean.Persistent;
import com.byzaneo.commons.config.HazelcastConfiguration;
import com.byzaneo.commons.exception.LockedDirectoryException;
import com.byzaneo.commons.locks.BusinessProcessLock;
import com.byzaneo.commons.service.DocumentLockingService;
import com.byzaneo.commons.util.*;
import com.byzaneo.xtrade.bean.Document;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.activiti.engine.delegate.DelegateExecution;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static java.lang.System.currentTimeMillis;
import static java.util.Collections.emptyList;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * <AUTHOR> <<EMAIL>>
 */
@Service(DocumentLockingServiceImpl.SERVICE_NAME)
@Conditional(HazelcastConfiguration.IsNotMultiNodeCondition.class)
public class DocumentLockingServiceImpl implements DocumentLockingService {

  public static final String SERVICE_NAME = "xtdDocumentLockingService";

  private static final Logger log = getLogger(DocumentLockingServiceImpl.class);

  /**
   * MINUTES
   */
  private static final int EXPIRE_TIME = 60;

  private static final long MAXIMUM_SIZE = 100000;

  public static final String LOCKED_BY_OTHER_BT = "is used by another Business Task";

  private final ThreadLocal<List<String>> documentsLockedByThread = new ThreadLocal<List<String>>() {
    @Override
    protected List<String> initialValue() {
      return new ArrayList<String>();
    }
  };

  /**
   * DOCUMENTS - key = DOC_ID(LONG)</br> INDEXABLE - key = id(String) <b>Currently NOT used</b></br> INPUT FILE - key = Directory PATH
   */
  private final Map<String, BusinessProcessLock> locks = Caffeine.newBuilder()
      .expireAfterWrite(EXPIRE_TIME, TimeUnit.MINUTES)
      .maximumSize(MAXIMUM_SIZE)
      .<String, BusinessProcessLock> build()
      .asMap();

  public ConcurrentMap<String, Map<String, Set<Long>>> lockedDocumentsMap = new ConcurrentHashMap<>();

  @Autowired
  @Qualifier(SharedLockingService.SERVICE_NAME)
  private transient SharedLockingService sharedLockingService;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private transient DocumentService documentService;

  @Override
  public void init() throws Exception {
    final long start = currentTimeMillis();
    log.info("STARTING DOCUMENT LOCKING SERVICE...");
    log.info("DOCUMENT LOCKING SERVICE STARTED in {} ms.", currentTimeMillis() - start);

  }

  @Override
  public void destroy() throws Exception {
    if (!this.locks.isEmpty()) {
      this.locks.forEach((k, v) -> log.error(k, " {} was locked when the service was stopped "));
      this.locks.clear();
    }
    final long start = currentTimeMillis();
    log.info("STOPPING DOCUMENT LOCKING SERVICE...");
    log.info("DOCUMENT LOCKING SERVICE STOPPED in {} ms.", currentTimeMillis() - start);

  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#getLocks()
   */
  @SuppressWarnings("unchecked")
  @Override
  public Map<String, BusinessProcessLock> getLocks() {
    return this.locks;
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#addLocks(DelegateExecution, Object)
   */
  @Override
  public <T extends Persistent<?>> void addDocumentLocks(Collection<T> documents) {
    Iterator<T> iterator = documents.iterator();
    log.info("Locking document(s)");
    while (iterator.hasNext()) {
      this.addDocumentLock(iterator.next());
    }
  }

  @Override
  public <T extends Persistent<?>> void addDocumentLocks(String repo, DelegateExecution delegateExecution,
      boolean unlockLockedDocumentsMap, boolean addDocumentsToExecution, List<T> documentsToAdd) {
    List<Document> documentList = documentsToAdd.stream()
        .filter(Document.class::isInstance)
        .map(Document.class::cast)
        .filter(doc -> doc.getId() != null)
        .collect(Collectors.toList());
    if (documentList.isEmpty()) {
      if (unlockLockedDocumentsMap) {
        documentService.unlockLockedDocumentsMap();
      }
      return;
    }

    List<Long> docIds = documentList.stream()
        .map(doc -> doc.getId())
        .collect(Collectors.toList());

    //will add new documents in map
    sharedLockingService.addDocumentsInSharedMap(repo, delegateExecution, documentList, docIds, lockedDocumentsMap,
        unlockLockedDocumentsMap);
    documentsLockedByThread.get()
        .addAll(docIds.stream()
            .map(String::valueOf)
            .collect(Collectors.toList()));
    sharedLockingService.lockMongoAndRelationalDatabase(lockedDocumentsMap.get(delegateExecution.getProcessInstanceId()),
        delegateExecution.getProcessInstanceId(), repo, docIds,
        delegateExecution, documentList, addDocumentsToExecution);
  }

  /**
   * @throws InterruptedException
   * @see com.byzaneo.xtrade.service.DocumentLockingService#addDocumentLock(com.byzaneo.xtrade.bean.Document)
   */
  @Override
  public <T extends Persistent<?>> void addDocumentLock(T document) {
    if (document == null || document.getId() == null) {
      return;
    }
    addLock(String.valueOf(document.getId()));
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#addDirectoryLock(File)
   */
  @Override
  public List<String> addDirectoryLock(File directory, boolean lockSubdirectories) {
    if (directory == null || !directory.isDirectory()) {
      return emptyList();
    }
    List<String> pathsLocked = new ArrayList<String>();
    log.info("Locking input folder(s)");
    addLock(directory.getAbsolutePath());
    pathsLocked.add(directory.getAbsolutePath());
    if (lockSubdirectories) {
      FileHelper.findAllSubdirs(directory)
          .forEach(dir -> {
            addLock(dir.getAbsolutePath());
            pathsLocked.add(dir.getAbsolutePath());
          });
    }
    return pathsLocked;
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#addLock(String)
   */
  @Override
  public void addLock(String key) {

    if (!tryLock(key)) {
      // if the lock can't be acquired we stop the process
      // this process will be stopped
      log.info("{} " + LOCKED_BY_OTHER_BT, key);
      throw new LockedDirectoryException(key + " " + LOCKED_BY_OTHER_BT);
    }
  }

  @Override
  public synchronized boolean tryLock(String key) {
    boolean isLocked;
    if (this.locks.containsKey(key)) {
      BusinessProcessLock businessProcessLock = this.locks.get(key);
      if (businessProcessLock.isHeldByCurrentThread()) {
        return true;
      }
      log.debug(" Try Locking {} by Thread {} at {} ", key, Thread.currentThread(), LocalDateTime.now());
      isLocked = businessProcessLock.tryLock();
    }
    else {
      BusinessProcessLock lock = new BusinessProcessLock();
      log.debug(" Locking {} by Thread {} at {} ", key, Thread.currentThread(), LocalDateTime.now());

      isLocked = lock.tryLock();
      this.locks.put(key, lock);
    }
    if (isLocked) {
      documentsLockedByThread.get()
          .add(key);
    }
    return isLocked;
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#unlockAllDocuments()
   */

  @Override
  public synchronized void unlockAllDocumentsInDatabase() {
    log.debug(" Unlocking all from Local Thread by Thread {} at {} ", Thread.currentThread(), LocalDateTime.now());
    List<Long> documentsToBeRemovedFromDistributedMap = documentsLockedByThread.get()
        .stream()
        .map(StringHelper::toLongValue)
        .filter(value -> value != null)
        .collect(Collectors.toList());

    if (!documentsToBeRemovedFromDistributedMap.isEmpty()) {
      log.debug("Current thread contains this list of documents: " + documentsToBeRemovedFromDistributedMap);
      sharedLockingService.unlockSharedMemory(documentsToBeRemovedFromDistributedMap, lockedDocumentsMap);
    }
  }

  @Override
  public void unlockAllDocuments() {
    log.info("Unlocking input folder(s) ");
    documentsLockedByThread.get()
        .forEach(docId -> unlock(docId));
    log.info("Removing input folder(s) ");
    documentsLockedByThread.get()
        .clear();
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#unlock(List)
   */
  @Override
  public <T extends Persistent<?>> void unlock(List<T> documents) {
    if (isNotEmpty(documents)) {
      documents.forEach(document -> this.unlock(document));
    }
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#unlock(com.byzaneo.xtrade.bean.Document)
   */
  @Override
  public <T extends Persistent<?>> void unlock(T document) {
    if (document == null || document.getId() == null) {
      return;
    }
    unlock(String.valueOf(document.getId()));
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#unlock(Long)
   */
  @Override
  public void unlock(String id) {
    if (id == null || !this.locks.containsKey(id)) {
      return;
    }
    log.debug(" Unlocking {} by Thread {} at {} ", id, Thread.currentThread(), LocalDateTime.now());
    BusinessProcessLock businessProcessLock = this.locks.get(id);
    businessProcessLock.unlock();
    if (!businessProcessLock.hasQueuedThreads() && !businessProcessLock.isLocked()) {
      log.debug(" Removing {} from map by Thread {} at {} ", id, Thread.currentThread(), LocalDateTime.now());
      this.locks.remove(id);
    }
  }

  /**
   * @see com.byzaneo.xtrade.service.DocumentLockingService#isDocumentLocked(com.byzaneo.xtrade.bean.Document)
   */
  @Override
  public <T extends Persistent<?>> boolean isDocumentLocked(T document) {
    if (document == null || document.getId() == null || !this.locks.containsKey(String.valueOf(document.getId()))) {
      return false;
    }
    return this.locks.get(String.valueOf(document.getId()))
        .isLocked();
  }

  @Override
  public boolean tryLock(List<File> files) {

    if (files == null || files.isEmpty()) {
      return true;
    }

    List<File> lockedFiles = new ArrayList<>();
    log.debug(" Try Locking list of files by Thread {} at {} ", Thread.currentThread(), LocalDateTime.now());
    try {

      for (File file : files) {
        if (file == null) continue;
        String path = file.getAbsolutePath();
        boolean isLocked = tryLock(path);
        if (isLocked) {
          lockedFiles.add(file);
        }
        else {
          unlockFiles(lockedFiles);
          return false;
        }
      }
    }
    catch (Exception e) {
      unlockFiles(lockedFiles);
      return false;
    }
    return true;

  }

  @Override
  public void unlockFiles(List<File> files) {
    for (File file : files) {
      unlockFile(file.getAbsolutePath());
    }
  }

  @Override
  public void unlockFile(String filePath) {
    unlock(filePath);
    documentsLockedByThread.get()
        .remove(filePath);
  }

  @Override
  public boolean isFileLocked(File file) {

    if (file == null || file.getAbsoluteFile() == null || !this.locks.containsKey(file.getAbsolutePath())) {
      return false;
    }
    return this.locks.get(file.getAbsolutePath())
        .isLocked();
  }

  public Set<Long> getLockedDocumentsInAnotherProcesses(String currentProcessId) {
    Set<Long> lockedDocsId = new HashSet<Long>();
    if (currentProcessId != null) {
      synchronized (lockedDocumentsMap) {
        lockedDocsId = lockedDocumentsMap.entrySet()
            .stream()
            .filter(entry -> !currentProcessId
                .equals(entry.getKey()))
            .flatMap(entry -> entry.getValue()
                .values()
                .stream())
            .flatMap(Set::stream)
            .collect(Collectors.toSet());
      }
    }
    return lockedDocsId;
  }

  @Override
  public void unlockDocumentsInSharedMap(Set<String> endedProcessesIds) {
    synchronized (lockedDocumentsMap) {
      for (String endedProcessId : endedProcessesIds) {
        lockedDocumentsMap.remove(endedProcessId);
      }
    }
  }
}
