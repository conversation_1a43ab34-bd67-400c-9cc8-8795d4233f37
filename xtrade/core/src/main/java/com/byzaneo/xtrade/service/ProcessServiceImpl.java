package com.byzaneo.xtrade.service;

import com.byzaneo.commons.api.EventAction;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.exception.*;
import com.byzaneo.commons.job.*;
import com.byzaneo.commons.job.Job;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.service.ConfigurationService.ConfigurationKey;
import com.byzaneo.commons.service.ExecutorServiceImpl.JobFuture;
import com.byzaneo.commons.ui.DistributedExecution;
import com.byzaneo.commons.util.*;
import com.byzaneo.query.util.QueryHelper;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.SecurityService;
import com.byzaneo.xtrade.ProcessException;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.api.DefinitionDescriptor;
import com.byzaneo.xtrade.api.DeploymentDescriptor;
import com.byzaneo.xtrade.api.DeploymentOptions;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.Report;
import com.byzaneo.xtrade.api.ReportDocument;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.dao.*;
import com.byzaneo.xtrade.process.*;
import com.byzaneo.xtrade.process.Variable;
import com.google.common.cache.*;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMap.Builder;
import com.hazelcast.map.IMap;
import org.activiti.bpmn.model.*;
import org.activiti.bpmn.model.Process;
import org.activiti.engine.*;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.ServiceImpl;
import org.activiti.engine.impl.bpmn.parser.BpmnParse;
import org.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.activiti.engine.impl.context.Context;
import org.activiti.engine.impl.interceptor.*;
import org.activiti.engine.impl.persistence.entity.*;
import org.activiti.engine.impl.util.*;
import org.activiti.engine.impl.variable.*;
import org.activiti.engine.repository.*;
import org.activiti.engine.runtime.ProcessInstance;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.spi.StandardLevel;
import org.bson.BsonMaximumSizeExceededException;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.orm.jpa.EntityManagerHolder;
import org.springframework.scheduling.annotation.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.*;
import org.springframework.util.Assert;

import javax.annotation.*;
import javax.mail.internet.InternetAddress;
import javax.persistence.*;
import javax.persistence.EntityManager;
import java.io.*;
import java.nio.channels.ClosedByInterruptException;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.zip.*;

import static com.byzaneo.commons.api.EventAction.create;
import static com.byzaneo.commons.api.EventAction.error;
import static com.byzaneo.commons.api.EventAction.started;
import static com.byzaneo.commons.bean.Logs.Tag.END;
import static com.byzaneo.commons.event.EventBuilder.fromIdentifier;
import static com.byzaneo.commons.event.EventBuilder.fromSource;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATA_DIR;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.TEMP_DIR;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.WORK_DIR;
import static com.byzaneo.commons.util.DomHelper.asXml;
import static com.byzaneo.commons.util.DomHelper.parseStream;
import static com.byzaneo.commons.util.FileHelper.isZip;
import static com.byzaneo.commons.util.FormatHelper.asDateTimeStrings;
import static com.byzaneo.commons.util.FormatHelper.asString;
import static com.byzaneo.commons.util.FormatHelper.removeAccentuation;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.commons.util.SpringContextHelper.getContext;
import static com.byzaneo.commons.util.SpringContextHelper.getMessage;
import static com.byzaneo.security.spring.UserDetailsService.getAuthenticatedUser;
import static com.byzaneo.security.util.PrincipalHelper.getInternetAddresses;
import static com.byzaneo.xtrade.bean.IOFile.fromFile;
import static com.byzaneo.xtrade.process.Constants.INPUT_DIR_NAME;
import static com.byzaneo.xtrade.process.Constants.MODE_DEV;
import static com.byzaneo.xtrade.process.Constants.MODE_PROD;
import static com.byzaneo.xtrade.process.Constants.OUTPUT_DIR_NAME;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static com.byzaneo.xtrade.process.Variable.EXECUTION_LINK;
import static com.byzaneo.xtrade.process.Variable.EXECUTION_MODE;
import static com.byzaneo.xtrade.process.Variable.JOB_DATE;
import static com.byzaneo.xtrade.process.Variable.JOB_EXECUTOR;
import static com.byzaneo.xtrade.process.Variable.JOB_SOURCE;
import static com.byzaneo.xtrade.process.Variable.JOB_TRIGGER;
import static com.byzaneo.xtrade.process.Variable.LOCKED_DIRECTORY_INPUT_BT;
import static com.byzaneo.xtrade.process.Variable.LOGGERS;
import static com.byzaneo.xtrade.process.Variable.PROCESS_INPUT_DIR;
import static com.byzaneo.xtrade.process.Variable.PROCESS_LOGS_DIR;
import static com.byzaneo.xtrade.process.Variable.PROCESS_OUTPUT_DIR;
import static com.byzaneo.xtrade.process.Variable.PROCESS_OWNER;
import static com.byzaneo.xtrade.process.Variable.PROCESS_RESOURCES;
import static com.byzaneo.xtrade.process.Variable.PROCESS_WORK_DIR;
import static com.byzaneo.xtrade.process.Variable.REPORT;
import static com.byzaneo.xtrade.process.Variable.REPORT_DOCUMENT;
import static com.byzaneo.xtrade.process.Variable.WRITE_EMPTY_REPORT;
import static com.byzaneo.xtrade.process.VariableHelper.getVariable;
import static com.byzaneo.xtrade.process.VariableHelper.resolveExecutionId;
import static com.byzaneo.xtrade.util.BpmnHelper.BpmnDiagramGenerator.getProcessDiagramStream;
import static com.byzaneo.xtrade.util.BpmnHelper.cleanDocument;
import static com.byzaneo.xtrade.util.BpmnHelper.getBpmnId;
import static com.byzaneo.xtrade.util.ReportHelper.copyReportLogFileAndDelete;
import static com.byzaneo.xtrade.util.ReportHelper.copyTempFileLogIntoFinalLog;
import static com.byzaneo.xtrade.util.ReportHelper.deleteTempLogFile;
import static com.byzaneo.xtrade.util.ReportHelper.formattedElapsedTime;
import static com.byzaneo.xtrade.util.ReportHelper.modifyReportHTML;
import static com.byzaneo.xtrade.util.ReportHelper.resolveReport;
import static com.byzaneo.xtrade.util.ReportHelper.resolveReports;
import static com.byzaneo.xtrade.util.ReportHelper.setReportStatus;
import static com.byzaneo.xtrade.util.ReportHelper.toHtml;
import static com.byzaneo.xtrade.util.ReportHelper.toText;
import static com.byzaneo.xtrade.util.ReportHelper.updateVariablesOfReport;
import static java.io.File.createTempFile;
import static java.io.File.separator;
import static java.lang.Boolean.TRUE;
import static java.lang.String.format;
import static java.lang.System.currentTimeMillis;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.emptySet;
import static java.util.Collections.sort;
import static java.util.Locale.FRANCE;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static org.activiti.engine.impl.context.Context.getProcessEngineConfiguration;
import static org.activiti.engine.impl.context.Context.removeProcessEngineConfiguration;
import static org.activiti.engine.impl.context.Context.setProcessEngineConfiguration;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.io.FileUtils.copyInputStreamToFile;
import static org.apache.commons.io.FileUtils.getTempDirectory;
import static org.apache.commons.io.IOUtils.closeQuietly;
import static org.apache.commons.io.IOUtils.copy;
import static org.apache.commons.lang3.ArrayUtils.isEmpty;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.endsWithAny;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNoneBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.apache.commons.lang3.time.FastDateFormat.SHORT;
import static org.springframework.orm.jpa.EntityManagerFactoryUtils.closeEntityManager;
import static org.springframework.transaction.TransactionDefinition.PROPAGATION_REQUIRES_NEW;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;
import static org.springframework.transaction.support.TransactionSynchronizationManager.bindResource;
import static org.springframework.transaction.support.TransactionSynchronizationManager.hasResource;
import static org.springframework.transaction.support.TransactionSynchronizationManager.unbindResource;
import static org.springframework.util.Assert.notNull;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * <AUTHOR> Sarrazin <<EMAIL>>
 * @company Byzaneo
 * @created 23 oct. 2009
 * @since 4.0
 */
@Service(ProcessService.SERVICE_NAME)
public class ProcessServiceImpl implements ProcessService, ApplicationListener<ContextClosedEvent> {
  private static final int DEFAULT_PROCESS_SIZE = 100_000;

  public static final String PROCESS_JOB_GROUP = "PROCESS";

  private static final Logger log = LoggerFactory.getLogger(ProcessServiceImpl.class);

  private static final ThreadLocal<DefinitionDescriptor> currentDefinitionDescriptor = new InheritableThreadLocal<>();

  // ACTIVITI
  @Autowired
  @Qualifier("processEngine")
  private ProcessEngine processEngine;

  @Autowired
  @Qualifier("processEngineConfiguration")
  private ProcessEngineConfigurationImpl processEngineConfiguration;

  @Autowired
  @Qualifier(IndexDataDAO.DAO_NAME)
  IndexDataDAO indexDataDAO;

  // services
  private RepositoryService repositoryService;

  private HistoryService historyService;

  private RuntimeService runtimeService;

  // XTRADE
  // @Autowired @Qualifier(VelocityService.SERVICE_NAME)
  // private VelocityService velocityService;
  @Autowired
  @Qualifier(DocumentDAO.DAO_NAME)
  private DocumentDAO documentAO;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  private DocumentService documentService;

  // COMMONS
  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private ConfigurationService configService;

  @Autowired
  @Qualifier(MailService.SERVICE_NAME)
  private MailService mailService;

  @Autowired
  @Qualifier(ExecutorService.SERVICE_NAME)
  private ExecutorService executorService;

  @Autowired
  @Qualifier(Log4jService.SERVICE_NAME)
  private Log4jService logService;

  @Autowired
  private DocumentLockingService documentLockingService;

  // SECURITY
  @Autowired
  @Qualifier(SecurityService.SERVICE_NAME)
  private SecurityService securityService;

  /** Process Resources Directory */
  private File resourcesDir;

  @Autowired
  @Qualifier(SharedLockingService.SERVICE_NAME)
  private transient SharedLockingService sharedLockingService;

  /**
   * Keeps track of the current executions (Limited to 100 000 elements).
   * <p/>
   * &lt;K&gt; is the process definition identifier &lt;V&gt; is the list of process instances running for the K definition
   */
  private Map<String, List<ProcessExecution>> executions;

  private Map<String, String> processThreads;

  private Map<String, List<IOFile>> processResources;

  private List<ProcessExecutionListener> processExecutionListenerList = new ArrayList<ProcessExecutionListener>();


  @Autowired
  @Qualifier(MultiNodeService.SERVICE_NAME)
  private transient MultiNodeService multiNodeService;

  /*
   * -- LIFE CYCLE --
   */

  /** @see com.byzaneo.commons.service.Initializable#init() */
  @Override
  @PostConstruct
  public final void init() throws Exception {
    final long start = System.currentTimeMillis();
    log.info("STARTING PROCESS SERVICE...");
    if (this.processEngine == null)
      throw new IllegalArgumentException("ProcessService must be injected.");
    this.repositoryService = this.processEngine.getRepositoryService();
    this.historyService = this.processEngine.getHistoryService();
    this.runtimeService = this.processEngine.getRuntimeService();
    this.processResources = Collections.synchronizedMap(new LRUMap<>(DEFAULT_PROCESS_SIZE));
    this.executions = Collections.synchronizedMap(new LRUMap<>(DEFAULT_PROCESS_SIZE));
    this.processThreads = Collections.synchronizedMap(new LRUMap<>(DEFAULT_PROCESS_SIZE));

    // Since Activiti 5.16.4 a JPA variable types are managed by the variable scope.
    // These variable types checks if the JPA entity (ex: Document) put in the
    // execution
    // variables gets a not null identifier (has been persisted). For now, we
    // generally
    // delegate the persistence of the JPA entities in the context to the report
    // listener.
    // Removes the JPAEntity*VariableTypes to allow transient entity in the
    // execution
    // context
    VariableTypes variableTypes;
    VariableType variableType;
    if (this.processEngine.getProcessEngineConfiguration() instanceof ProcessEngineConfigurationImpl &&
        (variableTypes = ((ProcessEngineConfigurationImpl) this.processEngine
            .getProcessEngineConfiguration()).getVariableTypes()) != null) {
      if ((variableType = variableTypes.getVariableType(JPAEntityVariableType.TYPE_NAME)) != null)
        variableTypes.removeType(variableType);
      if ((variableType = variableTypes.getVariableType(JPAEntityListVariableType.TYPE_NAME)) != null)
        variableTypes.removeType(variableType);
    }

    log.info("PROCESS SERVICE STARTED in {}ms.", System.currentTimeMillis() - start);

  }

  @Override
  public void onApplicationEvent(ContextClosedEvent event) {
    log.debug("Process sevice: Closing context, source is : {}", event.getSource());
    if (this.processEngine.getProcessEngineConfiguration().isAsyncExecutorActivate()){
      this.processEngine.getProcessEngineConfiguration().setAsyncExecutorActivate(false);
    }
  }

  /** @see com.byzaneo.commons.service.Disposable#destroy() */
  @Override
  @PreDestroy
  public final void destroy() throws Exception {
    final long start = System.currentTimeMillis();
    log.info("STOPPING PROCESS SERVICE...");
    try {
      this.processEngine.close();
    }
    catch (Exception e) {
      log.error("Error closing Process Engine: {}", e.getMessage());
    }
    finally{
      CatalinaHelper.forceStopJDBCDrivers();
    }
    log.info("PROCESS SERVICE STOPPED in {} ms.", System.currentTimeMillis() - start);
  }

  /*
   * -- DEPLOYMENT --
   */

  // -- DEPLOY --

  /**
   * @see com.byzaneo.xtrade.api.ProcessEngine#deploy(byte[], java.lang.String, DeploymentOptions, String)
   */
  @Override
  @Transactional
  public DeploymentDescriptor deploy(byte[] source, String name, DeploymentOptions options) {
    // Prepares returned deploiement descriptor
    final DeploymentDescriptor r = new com.byzaneo.xtrade.bean.DeploymentDescriptor(name);

    // DEFINITION
    try {
      if (this.documentAO != null) {
        this.documentAO.flushAndClear();
      }
      // deployment
      final Deployment deployment = this.deployStream(new ByteArrayInputStream(source), name);
      if (deployment == null)
        r.setError("Empty deployment returned by the engine");
      else {
        r.setDate(deployment.getDeploymentTime() == null ? new Date() : deployment.getDeploymentTime());
        r.setId(deployment.getId());

        // retrieves definition from deployment
        ProcessDefinition def = this.repositoryService.createProcessDefinitionQuery()
            .deploymentId(deployment.getId())
            .singleResult();
        // gets the latest definition if
        // we are not keeping the previous version
        boolean keepPreviousVersion = options != null && options.isKeepPreviousVersion();
        if (!keepPreviousVersion) {
          // removing previous process definition
          removeAllPreviousProcessDefinitionByKey(def.getKey());
          def = this.getProcessDefinition(def.getKey(), keepPreviousVersion, false);
        }
        r.setDefinitionId(def.getId());
        r.setDefinitionKey(def.getKey());
        r.setDefinitionName(def.getName());
        r.setDefinitionVersion(String.valueOf(def.getVersion()));
        r.setAllowParallel(options != null ? options.isAllowParallel() : TRUE);
      }
      // owner
      if (options != null) {
        r.setDefinitionOwner(options.getOwner());
      }
      cleanProcessResourcesByKey(r);
      /* this.documentAO.flushAndClear(); */
    }
    catch (Exception e) {
      log.error("Error deploying " + name, e);
      r.setException(e);
    }
    if (options != null && options.getTriggerCount() > 0) {
      createAndSubmitJob(name, null, r, options.getTriggers());
    }

    return r;
  }

  @Override
  public boolean createAndSubmitJob(String name, Long deploymentId, final DeploymentDescriptor deploymentDescriptor,
      final Trigger[] triggers) {
    // triggering options

    try {
      // gets existing job
      // creates and submit the process job
      Job job = this.createJob(deploymentDescriptor.getDefinition(), triggers);
      // in case of a new deployment the deployment.descriptor will be saved and all the descriptor.jobs will be saved
      deploymentDescriptor.setJob(job);

      Optional.ofNullable(this.executorService.<Job> submit(job, true))
          .filter(JobFuture.class::isInstance)
          .map(JobFuture.class::cast)
          .map(JobFuture::toJobException)
          .filter(Objects::nonNull)
          .ifPresent(jex ->
          {
            throw jex;
          });
      return true;
    }
    catch (JobDisabledException jde) {
      log.warn("Process job is disabled: {}", deploymentDescriptor);
    }
    catch (Exception e) {
      log.error("Error triggering process " + name, e);
      log.info("Undeploying process key (caused by: process job triggering error): {}", deploymentDescriptor);
      this.undeploy(deploymentDescriptor.getDefinitionKey(), true, false, true);
      deploymentDescriptor.setException(e);
    }
    return false;
  }

  protected void cleanProcessResourcesByKey(DeploymentDescriptor r) {
    if (this.processResources.containsKey(r.getDefinitionKey())) {
      processResources.remove(r.getDefinitionKey());
    }
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#deployFromClasspath(java.lang.String)
   */
  @Override
  @Transactional(propagation = REQUIRES_NEW)
  public Deployment deployFromClasspath(String processDefinition) {
    return this.deployFile(new File(this.getClass()
        .getClassLoader()
        .getResource(processDefinition)
        .getFile()));
  }

  /** @see com.byzaneo.xtrade.service.ProcessService#deployFile(java.io.File) */
  @Override
  @Transactional(propagation = REQUIRES_NEW)
  public Deployment deployFile(File processFile) {
    FileInputStream process = null;
    FileInputStream diagram = null;
    try {
      process = new FileInputStream(processFile);
      File diagramFile = new File(
          processFile.getAbsolutePath()
              .replace(PROCESS_DEFINITION_EXTENSION, PROCESS_IMAGE_EXTENSION));
      if (diagramFile.isFile())
        diagram = new FileInputStream(diagramFile);
      return this.deploy(process, processFile.getName(), diagram);
    }
    catch (Exception e) {
      log.error("Impossible to deploy file: " + processFile, e);
      throw new ServiceException("Impossible to deploy file: " + processFile, e);
    }
    finally {
      closeQuietly(diagram);
      closeQuietly(process);
    }
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#deployStream(java.io.InputStream, java.lang.String)
   */
  @Override
  @Transactional(propagation = REQUIRES_NEW)
  public Deployment deployStream(InputStream stream, String filename) {
    Deployment d = this.deploy(stream, filename, null);
    return d;
  }

  // -- UNDEPLOY --

  /**
   * @see com.byzaneo.xtrade.api.ProcessEngine#undeploy(DeploymentDescriptor, boolean)
   */
  @Override
  @Transactional
  public void undeploy(DeploymentDescriptor deployement, boolean cascade) {
    this.undeployById(deployement.getId(), cascade, cascade, cascade);
  }

  @Override
  @Transactional
  public void undeploy(DeploymentDescriptor deployement, boolean unschedule, boolean removeReports) {
    this.undeployById(deployement.getId(), unschedule, removeReports, false);
  }

  /** @see com.byzaneo.xtrade.service.ProcessService#undeploy(java.lang.String) */
  /** Use for test */
  @Override
  @Transactional(propagation = REQUIRES_NEW)
  public void undeploy(String key) {
    this.undeploy(key, true, true, true);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#undeploy(ProcessDefinition)
   */
  @Override
  @Transactional
  public void undeploy(ProcessDefinition process) {
    this.undeploy(process, true, true);
  }

  /** @see com.byzaneo.xtrade.service.ProcessService#undeploy(java.lang.String) */
  @Override
  @Transactional(propagation = REQUIRES_NEW)
  public void undeploy(String key, boolean unscheduled, boolean removeReports, boolean removeWatchers) {
    this.undeploy(this.getProcessDefinition(key), unscheduled, removeReports);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#undeployById(java.lang.String, boolean, boolean, boolean)
   */
  @Override
  @Transactional
  public void undeployById(String deploymentId, boolean unscheduled, boolean removeReports, boolean removeWatchers) {
    this.undeploy(this.repositoryService.createProcessDefinitionQuery()
        .deploymentId(deploymentId)
        .singleResult(),
        unscheduled, removeReports);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#undeploy(ProcessDefinition, boolean, boolean)
   */
  @Override
  @Transactional
  public void undeploy(ProcessDefinition process, boolean removeJob, boolean removeReports) {
    if (process == null)
      return;
    cancelProcess(process.getId(), process.getKey(), removeJob, removeReports);
    // removes deployment
    this.repositoryService.deleteDeployment(process.getDeploymentId(), true);

  }

  /**
   * Stops the process thread and all its children
   *
   * @param removeJob if set true the activiti job corresponding to the given process will be cancelled
   * @param removeReports if set true it will remove all the reports of this process definition (not only of the current execution but all
   *          the executions of this deployed process)
   */
  protected void cancelProcess(String pocessDefinitionId, String pocessDefinitionKey, boolean removeJob, boolean removeReports) {
    if (isProcessDefinitionRunning(pocessDefinitionId)) {
      // Stop the child threads first
      stopRelatedThreadsToProcessThread(pocessDefinitionId, processThreads.get(pocessDefinitionId) + "-");
      stopRelatedThreadsToProcessThread(pocessDefinitionId, processThreads.get(pocessDefinitionId));
    }
    // TODO Manage the case we can get the same job in parallel
    // removeAllPreviousProcessDefinitionByKey(process.getKey());
    if (isNotEmpty(pocessDefinitionKey))
      removeAllPreviousProcessDefinitionByKey(pocessDefinitionKey);

    // removes associated job
    if (removeJob) {
      // remove only job of current instance
      this.removeJob(this.getJob(pocessDefinitionId));
    }

    // removes reports
    if (removeReports && isNotEmpty(pocessDefinitionKey))
      try {
        List<Document> reports = this.getReportDocuments(pocessDefinitionKey, -1);
        reports.forEach(documentService::removeDocument);
      }
      catch (Exception e) {
        log.warn("Error removing process's reports documents.", e);
      }
  }

  /**
   * Stop related threads to the process thread
   *
   * @param pocessDefinitionId - {@link ProcessDefinition}
   * @param threadName
   * @return
   *         <ul>
   *         <li>{@code true} if a thread with name {@code threadName} has been stopped</li>
   *         <li>{@code false} if no thread was stopped</li>
   *         </ul>
   */
  public boolean stopRelatedThreadsToProcessThread(String pocessDefinitionId, String threadName) {
    if (threadName == null) {
      return false;
    }
    boolean stopped = false;
    Set<Thread> threadSet = Thread.getAllStackTraces()
        .keySet();
    for (Thread thread : threadSet) {
      if (thread.getName()
          .startsWith(threadName)) {
        thread.interrupt();
        stopped = true;
        log.debug("Stop thread {} related to the process {} succesfully", threadName, pocessDefinitionId);
      }
    }
    return stopped;
  }

  public boolean isProcessDefinitionRunning(String pocessDefinitionId) {
    return CollectionUtils.isNotEmpty(this.executions.get(pocessDefinitionId));
  }

  /*
   * -- EXECUTIONS --
   */

  @Override
  public boolean isDeployProcessRunning(String key) {
    ProcessDefinition pdef = getProcessDefinition(key, true, false);
    return pdef != null && isProcessDefinitionRunning(pdef.getId());
  }

  /**
   * @see com.byzaneo.xtrade.api.ProcessEngine#start(DefinitionDescriptor, Map)
   */
  @Override
  public Report start(DefinitionDescriptor definition) {
    return start(definition, emptyMap());
  }

  /**
   * @see com.byzaneo.xtrade.api.ProcessEngine#start(DefinitionDescriptor, Map)
   */
  @Override
  public Report start(DefinitionDescriptor definition, JobExecution execution) {
    final Map<String, Object> variables;
    if (execution != null) {
      final Builder<String, Object> builder = ImmutableMap.builder();
      if (execution.getTrigger() != null)
        builder.put(JOB_TRIGGER.toString(), execution.getTrigger());
      if (execution.getExecutor() != null)
        builder.put(JOB_EXECUTOR.toString(), execution.getExecutor());
      if (execution.getSource() != null)
        builder.put(JOB_SOURCE.toString(), execution.getSource());
      if (execution.getDate() != null)
        builder.put(JOB_DATE.toString(), execution.getDate());
      builder.putAll(execution.getVariables());
      variables = builder.build();
    }
    else {
      variables = emptyMap();
    }
    return start(definition, variables);
  }

  /**
   * @see com.byzaneo.xtrade.api.ProcessEngine#start(DefinitionDescriptor, Map)
   */
  @Override
  public Report start(DefinitionDescriptor definition, Map<String, Object> variables) {
    final long start = currentTimeMillis();

    ProcessDefinition pdf = getProcessDefinition(definition.getKey());
    if (pdf != null && !pdf.getId()
        .equals(definition.getId())) {
      throw new ProcessException("Found a newer version of the process %s (old : %s)", pdf.getId(),
          definition.getId());
    }
    if (executions.containsKey(definition.getId()) && executions.get(definition.getId())
        .size() > 0 && !definition.isAllowParallel()) {
      throw new ServiceException(
          "Cannot start process instance. Process definition %s (id = %s) is already running",
          definition.getName(), definition.getId());
    }

    // Open Process Entity Manager (if necessary)
    boolean participate = this.openProcessEntityManager();
    currentDefinitionDescriptor.set(definition);

    final com.byzaneo.xtrade.api.Report defaultReport = new com.byzaneo.xtrade.bean.Report();
    final GnxActivitiExecutionContext context = new GnxActivitiExecutionContext(defaultReport);
    try {
      final TransactionTemplate txTemplate = new TransactionTemplate(getContext().getBean(PlatformTransactionManager.class));
      txTemplate.setPropagationBehavior(PROPAGATION_REQUIRES_NEW);
      final Report report = txTemplate.execute(new TransactionCallback<Report>() {
        @Override
        public Report doInTransaction(TransactionStatus status) {

          // execute process in new transaction with PROPAGATION_REQUIRES_NEW
          return executeInTransaction(definition, variables, start, defaultReport, context);
        }
      });

      // publishes end event
      fromIdentifier(definition.getKey()).type(PROCESS_EVENT_TYPE)
          .action(EventAction.ended)
          .owner(getAuthenticatedUser())
          .publish();
      return ofNullable(getVariable(context.getExecutionInstance(), REPORT, report))
          .filter(Report.class::isInstance)
          .map(Report.class::cast)
          .orElse(report);
    }
    catch (Exception e) {

      /*
       * MongoDB documents have a fixed maximum size of 16 megabytes, if the report that needs to be saved is greater than that, everything
       * that was processed before in the process will be rollbacked. To avoid the rollback, the report documents and deads will be removed
       * in order to decrease the size, then the report will be saved again
       */
      if (e instanceof BsonMaximumSizeExceededException) {

        ExecutionEntity instance = context.getExecutionInstance();
        ProcessDefinition processDefinition = context.getProcessDefinition();
        Report report = ofNullable((Report) getVariable(instance, REPORT))
            .orElseGet(() -> createDefaultReport(instance, processDefinition, definition, new com.byzaneo.xtrade.bean.Report(), start));

        // -- Remove documents and deads from report to decrease the size --
        report.setDeads(new ArrayList<com.byzaneo.xtrade.bean.ReportDocument>());
        report.setDocuments(null);

        // -- Add error log in the report --
        modifyReportHTML(report.getLogsFilePath(), e.getMessage());

        Document reportDocument = (Document) getVariable(instance,
            REPORT_DOCUMENT);
        // -- Save Report Document --
        if (reportDocument != null) {
          reportDocument.setStatusWithEnumValue(DocumentStatus.WARNING);
          documentService.saveDocument(reportDocument);
        }

        // -- Publishes process report event for FATAL Status --
        fromSource(report).type(PROCESS_EVENT_TYPE)
            .action(create)
            .publish();

        return report;
      }
      // everything besides the exceptions that can occur on commit are already caught in executeInTransaction
      ExecutionEntity instance = context.getExecutionInstance();
      ProcessDefinition processDefinition = context.getProcessDefinition();
      log.error("Failed to execute process {} : {}", instance.getProcessDefinitionName(), e);

      List<Document> documents = VariableHelper.getDocuments(instance);
      List<com.byzaneo.xtrade.bean.Indexable> indexables = VariableHelper.getIndexables(instance);
      log.error("Rollback datas in mongo on transaction {}", instance.getId());
      documents.forEach(doc -> {
        if (documentService.getDocument(doc.getId()) == null) {
          if (doc.getIndexValue() == null || doc.getIndexValue()
              .getId() == null)
            return;
          documentService.removeIndexable(doc.getIndexValue());
        }
        else {
          revertIndexable(instance, doc);
        }
      });
      documentService.removeIndexables(indexables);

      Report report = ofNullable((Report) getVariable(instance, REPORT))
          .orElseGet(() -> createDefaultReport(instance, processDefinition, definition, new com.byzaneo.xtrade.bean.Report(), start));

      Document reportDocument = (Document) getVariable(instance, REPORT_DOCUMENT);
      Object needToStopAsDirectoryIsLocked = getVariable(instance, LOCKED_DIRECTORY_INPUT_BT);

      if (reportDocument != null) {
        Report reportIndex = reportDocument.getIndexValue();
        reportIndex.setTransactionStatus("Rollback");
        if (needToStopAsDirectoryIsLocked == null)
          reportDocument.setStatusWithEnumValue(DocumentStatus.FATAL);
        if (!isEmptyReportSavingDisabled(instance, report)) {
          documentService.saveDocument(reportDocument);
        }
      }

      // -- Publishes process report event for FATAL Status --
      fromSource(report).type(PROCESS_EVENT_TYPE)
          .action(create)
          .publish();

      return report;
    }
    finally {
      documentLockingService.unlockAllDocumentsInDatabase();
      documentLockingService.unlockAllDocuments();
      currentDefinitionDescriptor.remove();
      // Close Process Entity Manager (if necessary)
      this.closeProcessEntityManager(participate);
    }
  }

  public boolean isEmptyReportSavingDisabled(ExecutionEntity instance, Report report) {
    final String[] loggers = instance.getVariable(LOGGERS.var, String[].class);
    ReportLogsWrapper reportLogsWrapper = logService.stopLogsRecord(report.getLogAppenderId(), loggers);

    // if no log level on process due to no files or empty report
    // add INFO as default
    if (org.apache.commons.collections.CollectionUtils.isEmpty(reportLogsWrapper.getLevel())) {
      SortedSet<Level> level = new TreeSet<Level>();
      level.add(Level.INFO);
      reportLogsWrapper.setLevel(level);
    }

    StandardLevel logLevel = reportLogsWrapper.getLevel()
        .first()
        .getStandardLevel();

    // Report should not be stored and displayed if the 'write empty report' is disabled in BO,
    // there is no Warn/Error in the report logs,
    // and the report does not have any processed documents or deads.
    if (!instance.getVariable(WRITE_EMPTY_REPORT.var, Boolean.class) && ("INFO".equals(
        logLevel.name())) && (report.getDocumentCount() <= 0) &&
        (report.getDeadCount() <= 0)) {
      documentService.removeIndexable(report);
      FileHelper.deleteFile(getVariable(instance, PROCESS_WORK_DIR));
      return true;
    }
    return false;
  }

  @Transactional
  public void unlockLockedDocsByEndedProcesses() {
    // look for processes that are keeping the documents locked in sql/oracle database
    Set<String> processIdsForLockedDocuments = documentAO.findDocumentFieldValues(Document_.locks.getName())
        .stream()
        .filter(lock -> lock != null)
        .collect(Collectors.toSet());
    Set<String> endedProcessesIds = new HashSet<>();

    for (String processInstanceId : processIdsForLockedDocuments) {
      if (!isProcessExecutionRunning(processInstanceId)) {
        endedProcessesIds.add(processInstanceId);
      }
    }
    if (!endedProcessesIds.isEmpty())
      documentAO.unlockDocumentsForEndedProcesses(endedProcessesIds);

    // look for processes that are keeping the documents locked in mongo database
    Collection<Indexer<?>> listOfIndexers = documentService.getIndexers()
        .values();
    for (Indexer<?> indexer : listOfIndexers) {
      Class<Indexable> indexType = indexer.getIndexType();
      Set<String> processIdsForLockedDocumentsMongo = indexDataDAO.getProcessIdsForLockedDocuments(indexType);
      for (String processInstanceId : processIdsForLockedDocumentsMongo) {
        if (processInstanceId == null) {
          continue;
        }
        // if the execution is no longer in progress, unlock the documents
        if (!isProcessExecutionRunning(processInstanceId)) {
          endedProcessesIds.add(processInstanceId);
          indexDataDAO.unlockDocumentsLockedByProcess(indexType, processInstanceId);
        }
      }
    }

    // remove ended processes from shared locked documents map
    if (!endedProcessesIds.isEmpty()) {
      documentLockingService.unlockDocumentsInSharedMap(endedProcessesIds);
      log.debug("Documents were unlocked by 'unlockDocumentsFromEndedProcess' job for process ids : {}", endedProcessesIds);
    }
  }

  private boolean isProcessExecutionRunning(String processInstanceId) {
    if (processInstanceId == null)
      return false;
    if (multiNodeService.isMultiNodeEnabled()) {
      IMap<String, DistributedExecution<ProcessExecution>> distributedExecutions = multiNodeService.getDistributedExecutionMap();
      return distributedExecutions.values()
          .stream()
          .flatMap(distributedExecution -> distributedExecution
              .values()
              .stream())
          .flatMap(List::stream)
          .filter(processExecution -> processInstanceId
              .equals(processExecution.getInstanceId()))
          .count() != 0;
    }
    return executions.values()
        .stream()
        .flatMap(List::stream)
        .filter(processExecution -> processInstanceId
            .equals(processExecution.getInstanceId()))
        .count() != 0;
  }

  /**
   * Executes the business process
   *
   * @param definition
   * @param variables
   * @param start
   * @param defaultReport
   * @param context
   * @return
   */
  private Report executeInTransaction(DefinitionDescriptor definition, Map<String, Object> variables, final long start,
      final com.byzaneo.xtrade.api.Report defaultReport, final GnxActivitiExecutionContext context) {
    try {
      return ((ServiceImpl) ProcessServiceImpl.this.runtimeService).getCommandExecutor()
          .execute(new StartProcessInstanceCmd(definition, variables, start, context));
    }
    catch (Exception e) {
      if (e instanceof BsonMaximumSizeExceededException) {
        throw e;
      }
      ExecutionEntity instance = context.getExecutionInstance();
      boolean lockedDirectory = e instanceof LockedDirectoryException;
      boolean fileSizeLimitExceeded = e instanceof MongoFileSizeLimitExceededException;
      // if the exception is either ActivitiInterruptedException (thrown by a CancelableTask)
      // or ClosedByInterruptException (thrown by an InterruptableChannel IO opperation ex: FileUtils.doCopyFile() )
      // we add an error log and stop the process normally
      if (e instanceof ProcessInterruptedException ||
          e instanceof ClosedByInterruptException ||
          e instanceof InterruptedException ||
          ((e.getCause() instanceof ProcessInterruptedException ||
              e.getCause() instanceof ClosedByInterruptException ||
              e.getCause() instanceof InterruptedException))) {
        log.error("Process was interrupted {}", e.getMessage());
      }
      else if (lockedDirectory || fileSizeLimitExceeded) {
        // no need to log anything as it is already logged
      }
      else {
        log.error("Failed to run process", e);
      }

      Report ireport = getVariable(instance, REPORT);
      if (StringUtils.isEmpty(ireport.getOwners())) {
        defaultReport.setOwners(VariableHelper.<String> getVariable(instance, PROCESS_OWNER));
      }

      final String[] loggers = instance.getVariable(LOGGERS.var, String[].class);

      Document reportDocument = getVariable(instance, REPORT_DOCUMENT);

      updateVariablesOfReport(true, variables, instance, ireport);
      final User user = getAuthenticatedUser();
      // -- publishes error event
      fromIdentifier(definition.getKey()).type(PROCESS_EVENT_TYPE)
          .action(error)
          .owner(user)
          .publish();

      Marker timeMarker = MarkerFactory.getMarker(Log4jService.TIME_MARKER);
      log.info(timeMarker, "{} Report ({})", END, formattedElapsedTime(System.currentTimeMillis() - start));

      log.error(Log4jService.EOF_REPORT);

      // -- Stop log recording
      com.byzaneo.commons.bean.ReportLogsWrapper reportLogsWrapper = logService.stopLogsRecord(ireport.getLogAppenderId(), loggers);

      // -- Remove temporary log file
      copyTempFileLogIntoFinalLog(ireport, instance, reportLogsWrapper);
      deleteTempLogFile(reportLogsWrapper);

      // -- Status
      if (lockedDirectory) {
        reportDocument.setStatusWithEnumValue(DocumentStatus.FATAL);
      }
      setReportStatus(reportDocument, reportLogsWrapper);

      // -- Duration
      ireport.setDuration(System.currentTimeMillis() - ireport.getCreationDate()
          .toInstant()
          .toEpochMilli());

      // -- Save report document and index
      reportDocument.setIndexValue(ireport);
      // TODO
      // // instance.destroy();
      // if(exists)
      // runtimeService.deleteProcessInstance(instance.getProcessInstanceId(), null);
      /*
       * We only need to move the log files in case of an exception. Otherwise the logs are deleted in report generation (ReportListner) or
       * at the end of the Report generation (if something went wrong during report generation)
       */
      if (ireport.haslogs())
        copyReportLogFileAndDelete(ireport);
    }
    finally {
      // removes instance execution from service's executions tracking
      removeExecution(definition, context.getExecutionInstance());
    }
    return defaultReport;
  }

  private Report createDefaultReport(ExecutionEntity instance, ProcessDefinition processDefinition,
      DefinitionDescriptor definition, com.byzaneo.xtrade.bean.Report defaultReport, long start) {

    final String reportReference = resolveExecutionId(instance);

    // if any report has been associated to the process instance,
    // finalizes and return the execution one.
    defaultReport.setDuration(currentTimeMillis() - start);
    defaultReport.setCreationDate(new Date(start));
    defaultReport.setReference(reportReference);
    defaultReport.setProcessDefinition(processDefinition);
    defaultReport.setProcessInstance(instance);
    defaultReport.setFrom(defaultReport.getProcessDefinitionId()); // to = process definition identifier
    defaultReport.setTo(defaultReport.getProcessInstanceId()); // from = process instance identifier
    defaultReport.setType(FileType.REPORT.toString());
    String owner;
    if (isNotBlank(owner = instance.getVariable(PROCESS_OWNER.toString(), String.class))) {
      defaultReport.setOwners(owner);
    }
    else {
      defaultReport.setOwners(definition.getOwner() == null ? definition.getKey() : definition.getOwner());
    }

    // stores execution report
    defaultReport.setInProgress(false);
    documentService.createReportDocument(defaultReport, true, false, false);

    return defaultReport;
  }

  private void revertIndexable(DelegateExecution execution, Document doc) {
    List<Document> rslts = QueryHelper.search("id = " + doc.getId(), Document.class,
        VariableHelper.getBackupDocuments(execution));
    if (CollectionUtils.isNotEmpty(rslts) && rslts.size() == 1) {
      documentService.saveIndexable(rslts.get(0)
          .getIndexValue());
    }
    else {
      log.warn("Failed to revert index from document {}", doc);
    }
  }

  /** @see com.byzaneo.xtrade.service.ProcessService#start(String) */
  @Override
  public ProcessInstance start(String key) {
    return this.start(key, null);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#startAsync(java.lang.String, java.util.Map)
   */
  @Override
  @Async
  public Future<ProcessInstance> startAsync(String key, Map<String, Object> variables) {
    return new AsyncResult<>(this.start(key, variables));
  }

  /**
   * @see com.byzaneo.xtrade.api.ProcessEngine#startAsync(com.byzaneo.xtrade.api.DefinitionDescriptor, java.util.Map)
   */
  @Override
  @Async
  public Future<Report> startAsync(DefinitionDescriptor definition, Map<String, Object> variables) {
    return new AsyncResult<>(this.start(definition, variables));
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#start(java.lang.String, java.util.Map)
   */
  @Override
  public ProcessInstance start(String key, Map<String, Object> variables) {
    final ProcessDefinition pdef = this.getProcessDefinition(key);
    if (pdef == null)
      return null;
    final Report report = this.start(new com.byzaneo.xtrade.bean.DefinitionDescriptor(pdef), variables);
    return report != null ? report.getProcessInstance() : null;
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#setProcessEngineConfigurationContext()
   */
  @Override
  public void setProcessEngineConfigurationContext() {
    if (getProcessEngineConfiguration() == null)
      setProcessEngineConfiguration(processEngineConfiguration);
  }

  /*
   * -- IMPORT/EXPORT --
   */

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#saveProcessDocument(org.w3c.dom.Document, List)
   */
  @Override
  @Transactional(propagation = REQUIRES_NEW)
  public org.w3c.dom.Document saveProcessDocument(org.w3c.dom.Document document, List<IOFile> resources) {
    if (document == null)
      return null;

    // clean document
    cleanDocument(document);

    // id
    final ProcessDefinition pd = this.getProcessDefinition(getBpmnId(document));
    Assert.notNull(pd, "Process not found.");
    if (log.isDebugEnabled())
      log.debug("Deploying document with key={}", pd.getKey());

    // stop scheduler
    Job job = this.getJob(pd.getKey());
    if (job != null) {
      job.setDisabled(true);
      this.saveJob(job);
    }

    // stream to deploy
    final InputStream inputStream;
    try {
      inputStream = new ByteArrayInputStream(
          this.export(pd, document, new ByteArrayOutputStream(), resources)
              .toByteArray());
    }
    catch (Exception e) {
      log.error("Error creating deployment archive: {}", getRootCauseMessage(e));
      return document;
    }

    // replace
    log.info("Redeploying process '{}'", pd.getKey());
    this.undeploy(pd, false, false);
    this.deploy(inputStream, pd.getKey() + ".bar", null);

    return this.getProcessDefinitionDocument(pd);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#export(org.activiti.engine.repository.ProcessDefinition)
   */
  @Override
  @Transactional(readOnly = true)
  public File export(ProcessDefinition pd) throws ServiceException {
    try {
      File bar = createTempFile(pd.getKey() + "-", PROCESS_ARCHIVE_EXTENSION);
      export(pd, null, new FileOutputStream(bar), null);
      return bar;
    }
    catch (Exception e) {
      throw new ServiceException(e);
    }
  }

  /*
   * -- FIND --
   */

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#isProcessDeployed(java.lang.String)
   */
  @Override
  @Transactional(readOnly = true)
  public boolean isProcessDeployed(String key) {
    return !this.repositoryService.createProcessDefinitionQuery()
        .processDefinitionKey(key)
        .list()
        .isEmpty();
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getDeploymentTime(ProcessDefinition)
   */
  @Override
  @Transactional(readOnly = true)
  public Date getDeploymentTime(ProcessDefinition pd) {
    if (pd == null)
      return null;
    Deployment dep = this.repositoryService.createDeploymentQuery()
        .deploymentId(pd.getDeploymentId())
        .singleResult();
    return dep.getDeploymentTime();
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getProcessDefinition(java.lang.String)
   */
  @Override
  @Transactional(readOnly = true)
  public ProcessDefinition getProcessDefinition(String key) {
    return getProcessDefinition(key, true, true);
  }

  /** @see com.byzaneo.xtrade.service.ProcessService#getProcessDefinitions() */
  @Override
  @Transactional(readOnly = true)
  public List<ProcessDefinition> getProcessDefinitions() {
    List<ProcessDefinition> processesList;
    try {
      processesList = this.repositoryService.createProcessDefinitionQuery()
          .orderByProcessDefinitionKey()
          .asc()
          .list();
    }
    catch (Exception e) {
      log.error("The list of processes could not be fetched. Cause: {}", e.getLocalizedMessage());
      return emptyList();
    }
    return processesList;
  }

  /** @see com.byzaneo.xtrade.service.ProcessService#getProcessInstances() */
  @Override
  @Transactional(readOnly = true)
  public List<ProcessInstance> getProcessInstances() {
    return this.runtimeService.createProcessInstanceQuery()
        .list();
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getProcessInstances(ProcessDefinition)
   */
  @Override
  @Transactional(readOnly = true)
  public List<ProcessInstance> getProcessInstances(ProcessDefinition process) {
    return this.runtimeService.createProcessInstanceQuery()
        .processDefinitionId(process.getId())
        .list();
  }

  /*
   * -- RESOURCES --
   */

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getProcessDefinitionDocument(ProcessDefinition)
   */
  @Override
  @Transactional(readOnly = true)
  public org.w3c.dom.Document getProcessDefinitionDocument(ProcessDefinition pd) {
    if (pd == null)
      return null;

    List<String> rnames = this.repositoryService.getDeploymentResourceNames(pd.getDeploymentId());
    for (String rname : rnames) {
      if (rname.endsWith(PROCESS_DEFINITION_EXTENSION)) {
        try {
          return parseStream(this.repositoryService.getResourceAsStream(pd.getDeploymentId(), rname));
        }
        catch (Exception e) {
          log.error("Error parsing '{}': {}", rname, e.getMessage());
          break;
        }
      }
    }

    return null;
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getProcessDefinitionDiagramFile(ProcessDefinition)
   */
  @Override
  @Transactional(readOnly = true)
  public File getProcessDefinitionDiagramFile(ProcessDefinition pd) {
    InputStream imgStream = this.getProcessDefinitionDiagramStream(pd);
    if (imgStream == null)
      return null;

    File imgFile = new File(this.configService.getFile(TEMP_DIR, getTempDirectory()), pd.getDiagramResourceName());
    if (imgFile.isFile())
      imgFile.delete();
    if (!imgFile.getParentFile()
        .isDirectory())
      imgFile.getParentFile()
          .mkdirs();

    FileOutputStream out = null;
    try {
      out = new FileOutputStream(imgFile);
      copy(imgStream, out);
    }
    catch (Exception e) {
      log.error("Error writing image: " + imgFile, e);
    }
    finally {
      closeQuietly(imgStream);
      closeQuietly(out);
    }

    return imgFile;
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getProcessResources(String, String, String...)
   */
  @Override
  @Transactional(readOnly = true)
  public synchronized List<IOFile> getProcessResources(final String key, String definitionId, final String... excludedSuffixes) {
    if (isBlank(key))
      return emptyList();

    try {
      if (processResources.containsKey(key)) {
        return processResources.get(key);
      }
      final ProcessDefinition pd = this.getProcessDefinition(key);
      final String depid = pd.getDeploymentId();
      final List<String> rnames = this.repositoryService.getDeploymentResourceNames(depid);
      if (isEmpty(rnames))
        return emptyList();

      final List<IOFile> r = new ArrayList<>(rnames.size());
      for (String rname : rnames) {
        if (isNotEmpty(excludedSuffixes) && endsWithAny(rname, excludedSuffixes)) {
          log.debug("Excluded resource: {}", rname);
          continue;
        }
        IOFile ioFile = getFileByResourceName(rname, key, depid);
        if (ioFile != null) {
          r.add(ioFile);
        }
        else {
          continue;
        }
      }
      processResources.put(key, r);
      return r;
    }
    catch (Exception e) {
      log.error("Error getting resources from process: " + key, e);
      return emptyList();
    }
  }

  protected IOFile getFileByResourceName(String resource, String key, String deploymentId) {
    InputStream in = null;
    try {
      File rfile = new File(getResourcesDir(key), resource);
      if (!rfile.isFile() || rfile.delete()) {
        in = this.repositoryService.getResourceAsStream(deploymentId, resource);
        copyInputStreamToFile(in, rfile);
        log.debug("{}'s resource created: {}", key, rfile);
      }
      else {
        log.debug("{}'s resource exists: {}", key, rfile);
      }
      IOFile iof = fromFile(rfile, null);
      iof.setName(resource);
      return iof;
    }
    catch (Exception e) {
      log.error("Error adding resource '" + resource + "' as process variable: " + key, e);
      return null;
    }
    finally {
      closeQuietly(in);
    }
  }

  // -- IMAGES --

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getProcessDefinitionDiagramName(ProcessDefinition)
   */
  @Override
  @Transactional(readOnly = true)
  public String getProcessDefinitionDiagramName(ProcessDefinition pd) {
    if (pd == null)
      return null;

    String imgName = pd.getDiagramResourceName();
    if (isBlank(imgName))
      return null;

    int idx;
    if ((idx = imgName.lastIndexOf('/')) != -1)
      return imgName.substring(idx);
    if ((idx = imgName.lastIndexOf('\\')) != -1)
      return imgName.substring(idx);
    return imgName;
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getProcessDefinitionDiagramStream(ProcessDefinition)
   */
  @Override
  @Transactional(readOnly = true)
  public synchronized InputStream getProcessDefinitionDiagramStream(ProcessDefinition pd) {
    if (pd == null)
      return null;

    // load the process activities from the deployment stream
    // to use the overrode getProcessDiagramStream method
    // which is not cropping the image
    InputStream stream = null;
    try {
      String name = null;
      List<String> rnames = this.repositoryService.getDeploymentResourceNames(pd.getDeploymentId());
      for (String rname : rnames) {
        if (rname.endsWith(PROCESS_DEFINITION_EXTENSION)) {
          stream = this.repositoryService.getResourceAsStream(pd.getDeploymentId(), rname);
          name = rname;
          break;
        }
      }

      if (stream == null)
        return null;

      final DeploymentEntity deployment = (DeploymentEntity) this.repositoryService.createDeploymentQuery()
          .deploymentId(pd.getDeploymentId())
          .singleResult();
      // @since ACT 5.11: we need to put the ProcessEngineConfiguration bean in the
      // thread locale
      this.setProcessEngineConfigurationContext();
      BpmnParse bpmnParse = processEngineConfiguration.getBpmnParser()
          .createParse()
          .sourceInputStream(stream)
          .deployment(deployment)
          .name(name);
      bpmnParse.execute();

      return getProcessDiagramStream(bpmnParse.getBpmnModel());
    }
    catch (Exception e) {
      log.error("Error resolving ProcessDefinition: " + pd, e);
      return null;
    }
    finally {
      closeQuietly(stream);
      // @since ACT 5.11: removes the ProcessEngineConfiguration from the thread local
      removeProcessEngineConfiguration();
    }
  }

  /*
   * -- HISTORY --
   */

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getProcessHistory(java.lang.String)
   */
  @Override
  @Transactional(readOnly = true)
  public List<HistoricProcessInstance> getProcessHistory(String key) {
    return this.historyService.createHistoricProcessInstanceQuery()
        .processDefinitionId(this.getProcessDefinition(key)
            .getId())
        .orderByProcessInstanceStartTime()
        .desc()
        .list();
  }

  /*
   * -- REPORTING --
   */

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getReportDocuments(java.lang.String, int)
   */
  @Override
  @Transactional(readOnly = true)
  public List<Document> getReportDocuments(String key, int count) {
    return this.documentAO.findReportDocuments(key, count);
  }

  /** @see com.byzaneo.xtrade.service.ProcessService#toReports(java.util.List) */
  @Override
  public List<Report> toReports(List<Document> docs) {
    return resolveReports(docs);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#toReport(com.byzaneo.xtrade.bean.Document)
   */
  @Override
  public Report toReport(Document doc) {
    return resolveReport(doc);
  }

  @Override
  public List<Document> getDocuments(List<com.byzaneo.xtrade.bean.ReportDocument> reportDocuments) {
    List<Long> ids = reportDocuments
        .stream()
        .map(ReportDocument::getDocId)
        .collect(Collectors.toList());
    return this.documentService.getDocuments(ids);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#toReportHtml(Report, Writer, boolean)
   */
  @Override
  public void toReportHtml(Report report, Writer out, boolean embedded) {
    toHtml(report, out, embedded, null);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#sendReport(Report, Map, String...)
   */
  @Override
  public boolean sendReport(Report report, Map<String, Object> transformParameters, String... notifiedGroupNames) {
    if (report == null || isEmpty(notifiedGroupNames))
      return false;

    try (StringWriter htmlWriter = new StringWriter(); StringWriter textWriter = new StringWriter()) {
      // DATE & TIME
      String[] dateTime = asDateTimeStrings(report.getCreationDate(), SHORT, SHORT, FRANCE);

      // TO Addresses
      List<Group> ngroups = securityService.getGroupsByNames(notifiedGroupNames);
      if (isEmpty(ngroups))
        return false;
      InternetAddress[] tos = getInternetAddresses(
          securityService.getUsersInGroups(false, false, ngroups.toArray(new Group[ngroups.size()])));

      toText(report, textWriter, transformParameters);
      toHtml(report, htmlWriter, false, transformParameters);

      return mailService.sendMessage(null, tos, null, null, getMessage("report_subject",
          new Object[] { report.getState(), report.getDocumentCount(), report.getProcessInstanceId(),
              dateTime[0], dateTime[1] },
          format("[xTrade] - %s - %s docs processed by %s the %s at %s", report.getState(),
              report.getDocumentCount(), report.getProcessInstanceId(), dateTime[0], dateTime[1])),
          textWriter.toString(), htmlWriter.toString(), null, null);
    }
    catch (IOException | ServiceException e) {
      log.error("Error during notification content creation: {}", getRootCauseMessage(e));
    }

    return false;
  }

  /*
   * -- JOB EXECUTION --
   */

  /** @see com.byzaneo.xtrade.service.ProcessService#getJobs() */
  @Override
  @Transactional(readOnly = true)
  public <J extends Job> List<J> getJobs() {
    return this.executorService.getJobs(PROCESS_JOB_GROUP);
  }

  /** @see com.byzaneo.xtrade.service.ProcessService#getJob(java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  public <J extends Job> J getJob(String processKey) {
    return this.executorService.getJob(PROCESS_JOB_GROUP, processKey);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#createJob(ProcessDefinition, Trigger[])
   */
  @Override
  @Transactional(readOnly = true)
  public <J extends Job> J createJob(ProcessDefinition processDefinition, Trigger... triggers) {
    return this.createJob(new com.byzaneo.xtrade.bean.DefinitionDescriptor(processDefinition), triggers);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#createJob(com.byzaneo.xtrade.api.DefinitionDescriptor,
   *      com.byzaneo.commons.job.Trigger[])
   */
  @Override
  @SuppressWarnings("unchecked")
  public <J extends Job> J createJob(DefinitionDescriptor definition, Trigger... triggers) {
    if (definition == null || isBlank(definition.getKey()))
      return null;
    return (J) new com.byzaneo.commons.bean.Job(definition.getId(), PROCESS_JOB_GROUP,
        this.configService.getFullContextUrl(), definition.isAllowParallel(), SERVICE_NAME, ProcessService.class, "start",
        new Object[] { definition }, triggers);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#removeJob(Job)
   */
  @Override
  @Transactional
  public void removeJob(Job job) {
    if (job == null || !(job instanceof com.byzaneo.commons.bean.Job))
      return;
    com.byzaneo.commons.bean.Job j = (com.byzaneo.commons.bean.Job) job;
    this.executorService.suppress(j, true);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#saveJob(Job)
   */
  @Override
  @Transactional
  @Deprecated
  public <J extends Job> J saveJob(J job) {
    if (job.isDisabled())
      this.executorService.suppress(job, false);
    else this.executorService.submit(job, false);

    return this.executorService.save(job);
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#saveJob(Job)
   */
  @Override
  @Transactional(readOnly = true)
  public Date getNextExecutionDate(Job job) {
    return this.executorService.nextExecutionTime(job);
  }

  /*
   * -- EXECUTION --
   */

  /** @see com.byzaneo.xtrade.service.ProcessService#getProcessDefinitions() */
  @Override
  @Transactional(readOnly = true)
  public DefinitionDescriptor getCurrentDefinitionDescriptor() {
    return currentDefinitionDescriptor.get();
  }

  /**
   * @see com.byzaneo.xtrade.api.ProcessEngine#getExecutions(com.byzaneo.xtrade.api.DefinitionDescriptor)
   */
  @Override
  @SuppressWarnings("unchecked")
  public List<ProcessExecution> getExecutions(DefinitionDescriptor definition) {
    return definition != null && definition.getId() != null ? this.executions.get(definition.getId())
        : Collections.<ProcessExecution> emptyList();
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getRunningProcessesIds()
   */
  @Override
  public Set<String> getRunningProcessesIds() {
    return of(this.executions)
        .map(Map::keySet)
        .orElse(emptySet());
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#cancelProcessByDeploymentDescriptor(com.byzaneo.xtrade.api.DefinitionDescriptor)
   */
  @Override
  public synchronized boolean cancelProcessByDeploymentDescriptor(DefinitionDescriptor descriptor, boolean onlyRunningProcess) {
    notNull(descriptor, "Process can't be null");
    // Testing to see if the process is currently running
    if (onlyRunningProcess && this.executions.get(descriptor.getId()) == null) {
      log.debug("No process with id {} was found running ", descriptor.getId());
      return false;
    }
    try {
      cancelProcess(descriptor.getId(), descriptor.getKey(), true, false);
    }
    catch (Exception e) {
      log.error("Could not cancel the process{}", descriptor
          .getId());
      return false;
    }
    return true;
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#clearBlockingQueue()
   */
  @Override
  public void clearBlockingQueue() {
    this.executorService.clearBlockingQueue();
  }

  /**
   * @see com.byzaneo.xtrade.service.ProcessService#getBlockingQueueSize()
   */
  @Override
  public int getBlockingQueueSize() {
    return this.executorService.getBlockingQueueFromExecutor()
        .size();
  }

  protected ProcessDefinition getProcessDefinition(String key, boolean keepPreviousVersion, boolean throwOnNotFound) {
    if (isBlank(key))
      return null;

    List<ProcessDefinition> processes = searchExistingProcessesWithKey(key);

    // checks & removes duplicates (if requested)
    final ProcessDefinition process;
    if (isEmpty(processes)) {
      process = null;
    }
    else if (processes.size() > 1 && !keepPreviousVersion) {
      log.warn("More than one process '{}' has been found : {}", key, processes);
      sortProcessDefinitions(processes);
      process = processes.remove(processes.size() - 1);
    }
    else {
      process = processes.get(0);
    }

    // returns process
    if (throwOnNotFound && process == null)
      throw new ServiceException("ProcessDefinition '%s' not found.", key);

    return process;
  }

  void removeAllPreviousProcessDefinitionByKey(String key) {
    // searches for existing
    final List<ProcessDefinition> processes = searchExistingProcessesWithKey(key);
    if (isEmpty(processes)) {
      return;
    }
    else if (processes.size() > 1) {
      sortProcessDefinitions(processes);
      processes.remove(processes.size() - 1);
      processes.forEach(this::removePreviousProcess);
    }
  }

  protected void removePreviousProcess(ProcessDefinition previous) {
    try {
      this.repositoryService.deleteDeployment(previous.getDeploymentId(), true);
      Job job = getJob(previous.getId());
      if (job != null) {
        this.executorService.suppress(job, true);
        log.warn("Duplicate process undeployed: {}", previous);
      }

    }
    catch (Exception e) {
      log.error("Error undeploying duplicate process: " + previous, e);
    }
  }

  public List<ProcessDefinition> searchExistingProcessesWithKey(String key) {
    ProcessDefinitionQuery pdQuery = this.repositoryService.createProcessDefinitionQuery()
        .processDefinitionKey(key);
    if (pdQuery == null)
      return null;
    final List<ProcessDefinition> processes = pdQuery.list();
    return processes;
  }

  /**
   * sort the ProcessDefinition according to their version.
   *
   * @param processes
   */
  private void sortProcessDefinitions(List<ProcessDefinition> processes) {
    if (isNotEmpty(processes) && processes.size() > 1)
      sort(processes, PROCESS_VERSION_COMPARATOR);
  }

  /**
   * Adds Byzaneo xTrade's execution variables.
   *
   * @param executionId
   * @see Variable
   */
  @SuppressWarnings("unchecked")
  private Map<String, Object> addInternalVariables(DefinitionDescriptor definition, String executionId, Map<String, Object> variables) {
    Map<String, Object> vars = new HashMap<>();
    if (variables != null)
      vars.putAll(variables);
    // - configuration service -
    for (Variable var : Variable.values()) {
      switch (var) {
      case DATA_DIR:
      case TEMP_DIR:
      case WEBAPP_DIR:
      case WORK_DIR:
      case BACKUP_DIR:
      case INPUT_DIR:
      case OUTPUT_DIR:
      case BIN_DIR:
        this.addFileVariable(vars, var, configService.getFile(ConfigurationKey.valueOf(var.name()), null));
        break;
      default:
        break;
      }
    }

    // - evaluated variables -
    // owner
    if (!vars.containsKey(PROCESS_OWNER.toString()))
      vars.put(PROCESS_OWNER.toString(),
          definition.getOwner() == null ? definition.getKey() : definition.getOwner());
    // documents
    vars.put(EXECUTION_LINK.toString(), configService.getFullContextUrl());

    final Object documents = vars.get(DOCUMENTS.toString());
    vars.put(DOCUMENTS.toString(), documents == null || !(documents instanceof List) ? new ArrayList<Document>()
        : new ArrayList<Document>(Collection.class.cast(documents)));

    // execution mode
    vars.put(EXECUTION_MODE.toString(),
        configService.getString(ConfigurationKey.EXECUTION_MODE, MODE_DEV.toString())
            .equalsIgnoreCase(MODE_DEV.toString()) ? MODE_DEV : MODE_PROD);

    // - directories -
    final File wdir = resolveProcessWorkingDirectory(definition.getKey(), definition.getName(), definition.getOwner(), executionId);
    this.addFileVariable(vars, PROCESS_WORK_DIR, wdir);
    this.addFileVariable(vars, PROCESS_INPUT_DIR, new File(wdir, INPUT_DIR_NAME.toString()));
    this.addFileVariable(vars, PROCESS_LOGS_DIR, wdir);
    this.addFileVariable(vars, PROCESS_OUTPUT_DIR, new File(wdir, OUTPUT_DIR_NAME.toString()));

    return vars;
  }

  /**
   * Adds ProcessDefinition resources.
   *
   * @param definition
   * @param variables
   * @return
   */
  private Map<String, Object> addResourcesAsVariables(DefinitionDescriptor definition,
      Map<String, Object> variables) {

    Map<String, Object> vars = new HashMap<>();
    if (variables != null)
      vars.putAll(variables);
    final List<IOFile> iofiles = this.getProcessResources(definition.getKey(), definition.getId(), definition.getId(),
        PROCESS_DEFINITION_EXTENSION, PROCESS_IMAGE_EXTENSION);
    if (isNotEmpty(iofiles)) {
      final Map<String, String> rmap = new HashMap<>(iofiles.size());
      for (IOFile iofile : iofiles) {
        File file = iofile.getFile();
        if (file == null) {
          log.warn("Impossible to add resource iofile: {}", iofile);
          continue;
        }
        log.debug("Add variable as temporary process resource file: {}", file);
        rmap.put(file.getName(), file.getAbsolutePath());
      }
      vars.put(PROCESS_RESOURCES.toString(), rmap);
    }
    return vars;
  }

  /**
   * Adds 2 variables:
   * <ul>
   * <li>K=var as {@link Variable#toString()} , V=file</li>
   * <li>K=var as {@link Variable#toStringVar()}, V=file as {@link String} ({@link File#getAbsolutePath()})</li>
   * </ul>
   *
   * @param vars Execution variables
   * @param var {@link Variable} to add
   * @param file value to add
   */
  private void addFileVariable(Map<String, Object> vars, Variable var, File file) {
    if (file == null)
      return;
    vars.put(var.toString(), file);
    vars.put(var.toStringVar(), file.getAbsolutePath());
  }

  private File resolveProcessWorkingDirectory(final String key, final String name, final String owner, String executionId) {
    final Date date = new Date();
    final StringBuilder pathname = new StringBuilder(
        configService.getString(WORK_DIR, System.getProperty("java.io.tmpdir")));
    // owner folder
    final String nomalizedOwner;
    if (isNoneBlank(owner) && isNoneBlank(nomalizedOwner = removeAccentuation(owner).replaceAll("[^a-zA-Z0-9]", ""))) {
      pathname.append(separator)
          .append(nomalizedOwner);
    }
    pathname.append(separator)
        .append(key)
        .append("_")
        .append(name.substring(0, Math.min(name.length(), 20)))
        .append(separator)
        .append(asString(date, "yyyy-MM-dd"))
        .append(separator)
        .append(asString(date, "HH-mm-ss.SSS"))
        .append("-")
        .append(executionId);
    return new File(pathname.toString());
  }

  private <O extends OutputStream> O export(ProcessDefinition pd, org.w3c.dom.Document document, O output,
      List<IOFile> resources) {
    // retrives resources if not gave
    final List<IOFile> iofiles = isEmpty(resources)
        ? this.getProcessResources(pd.getKey(), pd.getId(), PROCESS_DEFINITION_EXTENSION, PROCESS_IMAGE_EXTENSION)
        : resources;
    try (final ZipOutputStream zip = new ZipOutputStream(output);) {
      // bpmn
      zip.putNextEntry(new ZipEntry(format("%s%s", pd.getKey(), PROCESS_DEFINITION_EXTENSION)));
      zip.write(asXml(document == null ? this.getProcessDefinitionDocument(pd) : document).getBytes());
      zip.closeEntry();
      // resources
      if (isNotEmpty(iofiles))
        for (IOFile iofile : iofiles) {
          zip.putNextEntry(new ZipEntry(iofile.getName()));
          zip.write(iofile.getBytes());
          zip.closeEntry();
        }
      // to input stream
      return output;
    }
    catch (Exception e) {
      log.error("Error creating deployment archive: {}", e.getMessage());
      return null;
    }
    finally {
      closeQuietly(output);
    }
  }

  private File getResourcesDir(String processKey) {
    if (this.resourcesDir == null)
      this.resourcesDir = new File(this.configService.getFile(DATA_DIR, FileHelper.getTempDirectory()),
          "resources");
    File processResourcesDir = new File(this.resourcesDir, processKey);
    processResourcesDir.mkdirs();
    return processResourcesDir;
  }

  private Deployment deploy(InputStream stream, String filename, InputStream diagram) {
    DeploymentBuilder deployment = this.repositoryService.createDeployment();
    deployment.name(filename);

    if (filename.toLowerCase()
        .endsWith(PROCESS_DEFINITION_EXTENSION)) {
      log.debug("deploying process file {}", filename);
      try {
        // bpmn
        deployment.addInputStream(filename, stream);
        // png
        if (diagram != null) {
          deployment.addInputStream(filename.replace(PROCESS_DEFINITION_EXTENSION, PROCESS_IMAGE_EXTENSION),
              diagram);
        }
      }
      catch (Exception e) {
        log.error("Error deploying: " + filename, e);
        throw new ServiceException("Error deploying: " + filename, e);
      }
    }
    else if (filename.toLowerCase()
        .endsWith("ar") || isZip(stream)) {
          log.debug("deploying business archive {}", filename);
          try (ZipInputStream zipStream = new ZipInputStream(stream)) {
            deployment.addZipInputStream(zipStream);
          }
          catch (Exception e) {
            throw new ServiceException("couldn't read business archive " + filename, e);
          }
        }
    else {
      throw new ServiceException(
          "unsupported extension: " + filename + "  Only .bpmn files and .*ar archives are supported");
    }

    // DEPLOYS
    Deployment d = deployment.deploy();
    return d;
  }

  private synchronized void addExecution(DefinitionDescriptor definition, ProcessInstance instance) {
    if (definition == null || definition.getId() == null || instance == null || instance.getId() == null)
      return;
    if (!this.executions.containsKey(definition.getId()))
      this.executions.put(definition.getId(), new ArrayList<ProcessExecution>());
    final ProcessExecution execution = new ProcessExecution(definition, instance.getId());
    this.executions.get(definition.getId())
        .add(execution);
    this.processThreads.put(definition.getId(), Thread.currentThread()
        .getName());
    this.processExecutionListenerList.forEach(listenr -> listenr.onExecutionAdded(execution));
    log.debug("Process execution added: {}", execution);
  }

  private synchronized boolean removeExecution(DefinitionDescriptor definition, ProcessInstance instance) {
    // first we make sure we remove any execution from the distributed map (if enabled)
    this.processExecutionListenerList.forEach(
        listenr -> listenr.onExecutionRemoved(new ProcessExecution(definition, instance.getId())));

    // then we remove the local execution
    if (definition == null || definition.getId() == null || instance == null || instance.getId() == null ||
        !this.executions.containsKey(definition.getId()))
      return false;

    ProcessExecution found = findExecution(definition, instance.getId());
    if (Objects.nonNull(found)) {
      log.debug("Process {} was removed from the execution stack", found);
      this.processThreads.remove(definition.getId());
      if (this.executions.get(definition.getId())
          .size() > 1) {
        return this.executions.get(definition.getId())
            .remove(found);
      }
      return Objects.nonNull(this.executions.remove(definition.getId()));
    }
    return false;
  }

  private ProcessExecution findExecution(DefinitionDescriptor definition, String processExecutionId) {
    ProcessExecution found = null;
    for (ProcessExecution exec : this.executions.get(definition.getId())) {
      if (processExecutionId.equals(exec.getInstanceId())) {
        found = exec;
        break;
      }
    }
    return found;
  }

  // -- ENTITY MANAGER --

  private void closeProcessEntityManager(boolean participate) {
    final EntityManagerFactory emf = getBean(EntityManagerFactory.class, "entityManagerFactory");
    if (!participate && emf != null) {
      EntityManagerHolder emHolder = (EntityManagerHolder) unbindResource(emf);
      closeEntityManager(emHolder.getEntityManager());
    }
  }

  private boolean openProcessEntityManager() {
    final EntityManagerFactory emf = getBean(EntityManagerFactory.class, "entityManagerFactory");

    // do not modify the EntityManager: just set the participate flag.
    if (emf == null || hasResource(emf))
      return true;

    // binds entity manager to the thread local
    final EntityManager em = emf.createEntityManager();
    bindResource(emf, new EntityManagerHolder(em));
    return false;
  }

  /*
   * -- INNERS --
   */

  /**
   * {@link RemovalListener} that counts each {@link RemovalNotification} it receives, and provides access to the most-recently received
   * one.
   */
  public static class ProcessExecution implements Execution, Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 4218181999397110325L;

    private final String definitionId;
    private final String definitionKey;

    private final Date startDate;

    private String instanceId;

    public ProcessExecution(DefinitionDescriptor definition, String instanceId) {
      super();
      this.definitionId = definition.getId();
      this.definitionKey = definition.getKey();
      this.startDate = new Date();
      this.instanceId = instanceId;
    }

    @Override
    public String getDefinitionId() {
      return definitionId;
    }

    @Override
    public Date getStartDate() {
      return startDate;
    }

    public String getDefinitionKey() {
      return definitionKey;
    }

    public String getInstanceId() {
      return instanceId;
    }

    public void setInstanceId(String instanceId) {
      this.instanceId = instanceId;
    }

    @Override
    public int hashCode() {
      return Objects.hash(instanceId);
    }

    @Override
    public boolean equals(Object obj) {
      if (this == obj) return true;
      if (obj == null) return false;
      if (getClass() != obj.getClass()) return false;
      ProcessExecution other = (ProcessExecution) obj;
      return Objects.equals(instanceId, other.instanceId);
    }

    @Override
    public String toString() {
      return format("Execution [definition=%s, startDate=%s, instance=%s]", definitionKey, startDate,
          instanceId != null ? instanceId : "");
    }
  }

  public final class GnxActivitiExecutionContext {

    private ExecutionEntity executionInstance;

    private ProcessDefinition processDefinition;

    private Report report;

    public GnxActivitiExecutionContext(Report report) {
      this.report = report;
    }

    public ExecutionEntity getExecutionInstance() {
      if (executionInstance == null) {
        throw new ActivitiException("The process could not be started");
      }
      return executionInstance;
    }

    public void setExecutionInstance(ExecutionEntity executionInstance) {
      this.executionInstance = executionInstance;
    }

    public ProcessDefinition getProcessDefinition() {
      if (processDefinition == null) {
        throw new ActivitiException("The process could not be found");
      }
      return processDefinition;
    }

    public void setProcessDefinition(ProcessDefinition processDefinition) {
      this.processDefinition = processDefinition;
    }

    public Report getReport() {
      return report;
    }

  }

  /**
   * Adds to the default Activiti {@link org.activiti.engine.impl.cmd.StartProcessInstanceCmd}: report generation, execution, events...
   */
  public final class StartProcessInstanceCmd implements Command<Report> {

    private final DefinitionDescriptor definition;

    private final Map<String, Object> variables;

    private GnxActivitiExecutionContext gnxExecutionContext;

    StartProcessInstanceCmd(DefinitionDescriptor definition, Map<String, Object> variables, long start,
        GnxActivitiExecutionContext gnxExecutionContext) {
      this.definition = definition;
      this.variables = variables;
      this.gnxExecutionContext = gnxExecutionContext;
    }

    @Override
    public synchronized Report execute(CommandContext commandContext) {
      if(executorService.isShuttingDown()){
        //after a shutDown command for the server we do NOT accept commands ad we signal this by returning null report (so we can test the null and do not delete files or other data loss, for example rest trigger async)
        log.info("Shutting down, not executing commands context anymore.");
        return null;
      }
      // Find the process definition
      final ProcessDefinition processDefinition = Context.getProcessEngineConfiguration()
          .getDeploymentManager()
          .findDeployedProcessDefinitionById(StartProcessInstanceCmd.this.definition.getId());
      if (processDefinition == null) {
        throw new ServiceException("No process definition found for id = '%s'", definition.getId());
      }
      // do not start process a process instance if the process definition is
      // suspended
      if (processDefinition.isSuspended()) {
        throw new ServiceException(
            "Cannot start process instance. Process definition %s (id = %s) is suspended",
            processDefinition.getName(), processDefinition.getId());
      }

      // publishes error event
      final User user = getAuthenticatedUser();
      fromIdentifier(definition.getKey()).type(PROCESS_EVENT_TYPE)
          .action(started)
          .owner(user)

          .publish();
      // creates the process instance

      ProcessInstanceHelper processInstanceHelper = commandContext.getProcessEngineConfiguration()
          .getProcessInstanceHelper();
      Process process = ProcessDefinitionUtil.getProcess(processDefinition.getId());
      if (process == null) {
        throw new ActivitiException("Cannot start process instance. Process model " + processDefinition.getName() + " (id = " +
            processDefinition.getId() + ") could not be found");
      }

      FlowElement initialFlowElement = process.getInitialFlowElement();
      if (initialFlowElement == null) {
        throw new ActivitiException("No start element found for process definition " + processDefinition.getId());
      }

      ExecutionEntity instance = (ExecutionEntity) processInstanceHelper.createAndStartProcessInstanceWithInitialFlowElement(
          processDefinition, null, null, initialFlowElement, process, variables, null, false);

      gnxExecutionContext.setExecutionInstance(instance);
      gnxExecutionContext.setProcessDefinition(processDefinition);
      if (instance == null) {
        throw new ServiceException("Process instance creation failed from definition %s (id = %s)",
            processDefinition.getName(), processDefinition.getId());
      }
      Report report = gnxExecutionContext.getReport();
      return startProcess(instance, commandContext, report);

    }

    private Report startProcess(final ExecutionEntity instance, CommandContext commandContext, Report report) {

      // Prepares the transient execution report
      log.info("Starting process definition '{}' at {}}", definition.getId(),
          report.getCreationDate());

      ProcessInstanceHelper processInstanceHelper = commandContext.getProcessEngineConfiguration()
          .getProcessInstanceHelper();
      // starts the process instance
      // sets the variables
      instance.setVariables(addResourcesAsVariables(definition, addInternalVariables(definition, instance.getId(), variables)));

      // adds instance to the current executions
      addExecution(definition, instance);

      // starts the instances
      processInstanceHelper.startProcessInstance(instance, commandContext,
          addResourcesAsVariables(definition, addInternalVariables(definition, instance.getId(), variables)));

      report.setProcessInstance(instance);
      return report;
    }

  }

  @Override
  public void addExecutionListenr(ProcessExecutionListener listenr) {
    this.processExecutionListenerList.add(listenr);
  }

  @Override
  public void removeExecutionListenr(ProcessExecutionListener listenr) {
    this.processExecutionListenerList.remove(listenr);
  }

}