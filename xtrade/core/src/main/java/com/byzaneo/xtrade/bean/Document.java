package com.byzaneo.xtrade.bean;

import com.byzaneo.commons.bean.*;
import com.byzaneo.security.bean.Exchange.Direction;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.converter.*;
import com.byzaneo.xtrade.process.VariableHelper;
import com.byzaneo.xtrade.ui.convert.DocStatusInterfaceAdapter;
import com.google.gson.annotations.Until;
import lombok.*;
import org.hibernate.annotations.*;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.*;
import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.*;
import java.net.URI;
import java.time.Instant;
import java.util.*;
import java.util.stream.Stream;

import static com.byzaneo.commons.util.PropertiesHelper.asPropertyGroup;
import static com.byzaneo.commons.util.PropertiesHelper.getProperty;
import static com.byzaneo.xtrade.api.DocumentStatus.NONE;
import static com.byzaneo.xtrade.bean.Document.Metadata.ERROR_MESSAGE;
import static com.byzaneo.xtrade.bean.Document.Metadata.INFO_MESSAGE;
import static com.byzaneo.xtrade.bean.Document.Metadata.SCHEDULING;
import static com.byzaneo.xtrade.bean.Document.Metadata.VERSION;
import static com.byzaneo.xtrade.bean.Document.Metadata.WARNING_MESSAGE;
import static com.byzaneo.xtrade.bean.Document.Scheduling.MANUAL;
import static com.byzaneo.xtrade.util.DocumentHelper.findFileByType;
import static com.byzaneo.xtrade.util.DocumentHelper.findFiles;
import static java.lang.Boolean.TRUE;
import static java.util.UUID.randomUUID;
import static java.util.stream.Stream.concat;
import static java.util.stream.Stream.of;
import static javax.persistence.CascadeType.ALL;
import static javax.persistence.DiscriminatorType.STRING;
import static javax.persistence.InheritanceType.JOINED;
import static javax.persistence.TemporalType.TIMESTAMP;
import static javax.xml.bind.annotation.XmlAccessType.FIELD;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.truncate;
import static org.hibernate.annotations.SourceType.DB;

/**
 * Document representation. A document can be composed with several {@link DocumentFile} which are holding the {@link URI} of the document's
 * {@link File}.
 *
 * <AUTHOR> Rossi
 * @company Byzaneo
 * @see DocumentFile
 */
@Entity
@Table(name = "XTD_DOCUMENT")
@Inheritance(strategy = JOINED)
@DiscriminatorColumn(name = "DOC_TYPE", discriminatorType = STRING)
@AssociationOverrides({
    @AssociationOverride(name = "metaGroup", joinColumns = @JoinColumn(name = "DOC_METADATA"))
})
@XmlRootElement(name = "document", namespace = "xtd")
@XmlAccessorType(FIELD)
public class Document extends AbstractMetadata
    implements Node<Document>, Persistent<Long>, Comparable<Document>,
    com.byzaneo.xtrade.api.Document {
  private static final long serialVersionUID = -6833870754854382706L;

  /** Document properties ( @since 5.2 ) */
  public enum Property {
    id,
    reference,
    uuid,
    owners,
    type,
    from,
    to,
    transport,
    purpose,
    creationDate,
    modificationDate,
    status,
    linCount,
    model,
    files,
    index_id,
    index_reference,
    index_type,
    archiveStatus,
    translator,
    thirdParty,
    nature,
    immatpdp,
    b2gRules,
    ereportingOption,
    countryRules1,
    countryRules2;

    @Override
    public String toString() {
      return super.name()
          .replace('_', '.');
    }

  }

  /** Scheduling mode (generally used for the {@link FileType#PROCESS_DEFINITION} documents */
  public enum Scheduling {
    GENERAL,
    CRON,
    MANUAL;
  }

  public enum AcquisitionType {
    EDI,
    UPLOAD,
    PORTAL,
    OCR,
    API,
    OTHER;
  }

  public enum ProcessingWay {
    SENDING(Direction.S),
    RECEIVING(Direction.R);

    private final Direction direction;

    private ProcessingWay(Direction direction) {
      this.direction = direction;
    }

    public Direction getDirection() {
      return direction;
    }
  }

  /** Metadata predefined keys */
  public enum Metadata implements com.byzaneo.commons.bean.MetaEnum {
    ERROR_MESSAGE(PropertyType.String,
        null,
        null),
    WARNING_MESSAGE(PropertyType.String,
        null,
        null),
    INFO_MESSAGE(PropertyType.String,
        null,
        null),
    COUNT(PropertyType.Integer,
        null,
        0),
    VERSION(PropertyType.String,
        null,
        "1.0"),
    TO_DETAIL(PropertyType.String,
        null,
        null),
    FROM_DETAIL(PropertyType.String,
        null,
        null),
    SCHEDULING(PropertyType.String,
        null,
        MANUAL.toString()),
    PERSIST_DOCUMENT(PropertyType.Boolean,
        null,
        TRUE);

    private Object defaultValue;
    private String format;
    private PropertyType type;

    private Metadata(PropertyType type, String format, Object defaultValue) {
      this.defaultValue = defaultValue;
      this.format = format;
      this.type = type;
    }

    @Override
    public <T> T defaultValue() {
      return this.type.<T> toClass()
          .cast(defaultValue);
    }

    @Override
    public String format() {
      return format;
    }

    @Override
    public PropertyType type() {
      return type;
    }
  }

  @Id
  @GeneratedValue(generator = "native-gen")
  @GenericGenerator(name = "native-gen", strategy = "native")
  @Column(name = "DOC_ID")
  @XmlAttribute
  protected Long id;

  @Column(name = "DOC_UUID")
  @XmlAttribute
  protected String uuid;

  @Until(0.0)
  @ManyToOne
  @JoinColumn(name = "DOC_PARENT")
  // @XmlTransient
  protected Document parent = null;

  @OneToMany(mappedBy = "parent", cascade = ALL)
  @XmlTransient
  protected Set<Document> children = new LinkedHashSet<Document>();

  @Column(name = "DOC_REFERENCE")
  @XmlAttribute
  protected String reference;

  @Column(name = "DOC_OWNERS")
  @XmlAttribute
  protected String owners;

  @Column(name = "DOC_TYPE")
  @XmlAttribute
  protected String type;

  @Column(name = "DOC_FROM")
  @XmlAttribute
  protected String from;

  @Column(name = "DOC_TO")
  @XmlAttribute
  protected String to;

  @Column(name = "DOC_NUMBER")
  @XmlAttribute
  protected String number;

  @Column(name = "DOC_ISSUEDATE")
  @XmlAttribute(name = "issue-date")
  @DateTimeFormat(iso = ISO.DATE)
  protected Date issueDate;

  @Column(name = "DOC_SUBTYPE")
  @XmlAttribute
  protected String subtype;

  @Column(name = "DOC_TRANSPORT")
  @XmlAttribute
  protected String transport;

  @Column(name = "DOC_PURPOSE")
  @XmlAttribute
  protected String purpose;

  @Column(name = "DOC_ACQUISITION")
  @XmlAttribute
  protected AcquisitionType acquisition;

  @Column(name = "DOC_TRANSLATOR")
  @XmlAttribute
  @Getter
  @Setter
  protected String translator;

  @Column(name = "DOC_CREATION_DATE", updatable = false)
  @XmlAttribute(name = "creation-date")
  protected Date creationDate = new Date();

  @Column(name = "DOC_MODIFICATION_DATE")
  @Source(DB)
  @Temporal(TIMESTAMP)
  @XmlAttribute(name = "modification-date")
  protected Date modificationDate = new Date();

  @ManyToOne(targetEntity = DocumentStatusEntity.class)
  @JoinColumn(name = "DOC_STATUS")
  @XmlAttribute(name = "status", namespace = "xtd")
  @XmlJavaTypeAdapter(value = DocStatusInterfaceAdapter.class)
  protected DocumentStatusEntityInterface status = new DocumentStatusEntity(NONE);

  @Column(name = "DOC_STAGE")
  @XmlAttribute(name = "stage", namespace = "xtd")
  @Convert(converter = DocumentStageConverter.class)
  protected DocumentStage stage = DocumentStage.UNDEFINED;

  @Column(name = "DOC_ARCHIVE_STATUS")
  @Convert(converter = ArchiveStatusConverter.class)
  @XmlAttribute(name = "archiveStatus", namespace = "xtd")
  protected ArchiveStatus archiveStatus = ArchiveStatus.UNDEFINED;

  @Column(name = "DOC_LIN_COUNT")
  @XmlAttribute(name = "count")
  protected Integer linCount;

  @ManyToOne(cascade = ALL)
  @JoinColumn(name = "DOC_MODEL")
  @XmlTransient
  protected PropertyGroup model;

  @OneToMany(mappedBy = "document", cascade = ALL, orphanRemoval = true)
  @XmlElementWrapper(name = "files", namespace = "xtd")
  @XmlElement(name = "file", namespace = "xtd")
  protected List<DocumentFile> files = new ArrayList<DocumentFile>();

  @Column(name = "DOC_EMAIL")
  @XmlAttribute
  @Length(max = 512)
  protected String email;

  @Column(name = "DOC_ORDERING_PARTY")
  @XmlAttribute
  protected String orderingParty;

  @Column(name = "DOC_CONSULTSTATUS")
  @XmlAttribute(name = "consultStatus", namespace = "xtd")
  @Convert(converter = DocumentConsultStatusConverter.class)
  protected DocumentConsultStatus consultStatus = DocumentConsultStatus.NONE;

  @Column(name = "DOC_PROCESSING_WAY")
  @XmlTransient
  protected ProcessingWay processingWay;

  @Column(name = "DOC_THIRD_PARTY")
  @XmlTransient
  @Getter
  @Setter
  protected String thirdParty;

  @Column(name = "DOC_NATURE")
  @XmlTransient
  @Getter
  @Setter
  protected String nature;

  @Column(name = "DOC_IMMATPDP")
  @XmlTransient
  @Getter
  @Setter
  protected String immatpdp;

  @Column(name = "DOC_B2G_RULES")
  @XmlTransient
  @Getter
  @Setter
  protected String b2gRules;

  @Column(name = "DOC_EREPORTING_OPTION")
  @XmlTransient
  @Getter
  @Setter
  protected String ereportingOption;

  @Column(name = "DOC_COUNTRY_RULES_1")
  @XmlTransient
  @Getter
  @Setter
  protected String countryRules1;

  @Column(name = "DOC_COUNTRY_RULES_2")
  @XmlTransient
  @Getter
  @Setter
  protected String countryRules2;

  @Column(name = "DOC_USE_CASE")
  @XmlTransient
  @Getter
  @Setter
  protected String useCase;

  @Column(name = "DOC_BILLING_FRAMEWORK")
  @XmlTransient
  @Getter
  @Setter
  protected String billingFramework;

  @Column(name = "DOC_SOVOS_ID")
  @XmlTransient
  @Getter
  @Setter
  protected String sovosId;

  @Column(name = "DOC_SOVOS_STATUS")
  @XmlTransient
  @Getter
  @Setter
  protected String sovosStatus;

  @Column(name = "DOC_SOVOS_TRANSACTION_ID")
  @XmlTransient
  @Getter
  @Setter
  protected String sovosTransactionId;

  @Column(name = "DOC_SOVOS_STATUS_CODE")
  @XmlTransient
  @Getter
  @Setter
  protected String sovosStatusCode;

  @Column(name = "DOC_SOVOS_NOTIFICATION_ID")
  @XmlTransient
  @Getter
  @Setter
  protected String sovosNotificationId;

  @Column(name = "DOC_ARCHIVE_EXPIRATION_DATE")
  @XmlTransient
  @Getter
  @Setter
  protected Date archiveExpirationDate;

  @Column(name = "DOC_ROSSUM_ID")
  @XmlTransient
  @Getter
  @Setter
  protected String rossumId;

  @Column(name = "DOC_LOCKS")
  @XmlTransient
  @Getter
  @Setter
  private String locks;

  @Column(name = "DOC_PROFILE")
  @XmlAttribute
  @Getter
  @Setter
  protected String profile;

  @PreUpdate
  public void preUpdateFunction() {
    setModificationDate(Date.from(Instant.now()));
  }

  /**
   * Indexed document reference
   *
   * @since 7.0
   */
  @Embedded
  @AttributeOverrides({
      @AttributeOverride(name = "type", column = @Column(name = "DOC_IDX_TYPE")),
      @AttributeOverride(name = "collection", column = @Column(name = "DOC_IDX_COL")),
      @AttributeOverride(name = "id", column = @Column(name = "DOC_IDX_ID")),
      @AttributeOverride(name = "reference", column = @Column(name = "DOC_IDX_REF"))
  })
  // @XmlTransient
  protected Index index;

  @Version
  @Column(name = "DOC_REVISION")
  protected Long revision;

  /*
   * -- CONSTRUCTORS --
   */

  public Document() {
    super();
    this.uuid = randomUUID().toString();
  }

  public Document(String reference, String owners, String type, String from, String to,
      DocumentStatusEntityInterface documentstatusEntityInterface) {
    this();
    this.reference = reference;
    this.owners = owners;
    this.type = type;
    this.from = from;
    this.to = to;
    this.status = documentstatusEntityInterface;
  }

  public Document(String reference, String owners, String type, String from, String to, DocumentStatus status) {
    this();
    this.reference = reference;
    this.owners = owners;
    this.type = type;
    this.from = from;
    this.to = to;
    this.status = new DocumentStatusEntity(status);
  }

  /*
   * -- ACCESSORS --
   */

  @Override
  public Long getId() {
    return id;
  }

  @Override
  public void setId(Long id) {
    this.id = id;
  }

  @Override
  public Date getCreationDate() {
    return creationDate;
  }

  @Override
  public void setCreationDate(Date creationDate) {
    this.creationDate = creationDate;
  }

  // - REFERENCE -
  @Override
  public String getReference() {
    if (this.reference == null) {
      this.reference = this.getModelReference();
      if (this.reference == null)
        this.reference = this.getFileReference();
    }
    return this.reference;
  }

  @Override
  public void setReference(String reference) {
    this.reference = reference;
  }

  public String getFileReference() {
    File f;
    return (f = this.getFirstFile()) == null ? null : f.getName();
  }

  public String getModelReference() {
    return this.model == null ? null : this.model.<String> getProperty(Property.reference.toString());
  }

  public Date getModificationDate() {
    return modificationDate;
  }

  public void setModificationDate(Date modificationDate) {
    this.modificationDate = modificationDate;
  }

  @Override
  public DocumentStatusEntityInterface getStatus() {
    return status == null ? new DocumentStatusEntity(NONE) : status;
  }

  @Override
  public void setStatus(DocumentStatusEntityInterface status) {
    this.status = status;
  }

  @Override
  public String getStatusAsString() {
    return this.status == null ? DocumentStatus.NONE.name() : status.getStatusCode();
  }

  @Override
  public void setStatusWithString(String statusCode) {
    this.status = new DocumentStatusEntity(statusCode);
  }

  @Override
  public DocumentStatus getStatusAsEnumValue() {
    if (this.status == null)
      return DocumentStatus.NONE;
    try {
      return DocumentStatus.valueOf(status.getStatusCode());
    }
    catch (IllegalArgumentException e) {
      return DocumentStatus.NOT_STANDARD;
    }
  }

  @Override
  public void setStatusWithEnumValue(DocumentStatus statusCode) {
    this.status = new DocumentStatusEntity(statusCode);
  }

  @Override
  public ArchiveStatus getArchiveStatus() {
    return archiveStatus;
  }

  @Override
  public void setArchiveStatus(ArchiveStatus archiveStatus) {
    this.archiveStatus = archiveStatus;
  }

  public boolean isStatus(DocumentStatus status) {
    return this.status != null ? this.status.equals(status) : false;
  }

  @Override
  public DocumentStage getStage() {
    return stage == null ? DocumentStage.UNDEFINED : stage;
  }

  @Override
  public void setStage(DocumentStage stage) {
    this.stage = stage;
  }

  public DocumentConsultStatus getConsultStatus() {
    return consultStatus == null ? DocumentConsultStatus.NONE : consultStatus;
  }

  public void setConsultStatus(DocumentConsultStatus consultStatus) {
    this.consultStatus = consultStatus;
  }

  public ProcessingWay getProcessingWay() {
    return processingWay;
  }

  public void setProcessingWay(ProcessingWay processingWay) {
    this.processingWay = processingWay;
  }

  @Override
  public String getOwners() {
    return owners;
  }

  @Override
  public void setOwners(String owner) {
    this.owners = owner;
  }

  @Override
  public String getFrom() {
    return from;
  }

  @Override
  public void setFrom(String from) {
    this.from = from;
  }

  @Override
  public String getTo() {
    return to;
  }

  @Override
  public void setTo(String to) {
    this.to = to;
  }

  public String getNumber() {
    return number;
  }

  public void setNumber(String number) {
    this.number = number;
  }

  public Date getIssueDate() {
    return issueDate;
  }

  public void setIssueDate(Date issueDate) {
    this.issueDate = issueDate;
  }

  public String getSubtype() {
    return subtype;
  }

  public void setSubtype(String subtype) {
    this.subtype = subtype;
  }

  @Override
  public String getType() {
    return type;
  }

  @Override
  public void setType(String type) {
    this.type = type;
  }

  public void setType(DocumentType type) {
    this.type = type.toString();
  }

  @Override
  public String getUuid() {
    return this.uuid;
  }

  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  @Override
  public String getTransport() {
    return this.transport;
  }

  @Override
  public void setTransport(String transport) {
    this.transport = transport;
  }

  @Override
  public String getPurpose() {
    return this.purpose;
  }

  @Override
  public void setPurpose(String purpose) {
    this.purpose = purpose;
  }

  public AcquisitionType getAcquisition() {
    return acquisition;
  }

  public void setAcquisition(AcquisitionType acquisition) {
    this.acquisition = acquisition;
  }

  public Integer getLinCount() {
    return linCount;
  }

  public void setLinCount(Integer linCount) {
    this.linCount = linCount;
  }

  // - MODEL -
  public PropertyGroup getModel() {
    if (this.model != null &&
        !this.model.hasProperties() &&
        !this.model.hasChildren())
      this.model = null;
    return model;
  }

  public <T> T getModel(MetaEnum meta) {
    return getProperty(this.getNotNullModel(), meta.toString(), meta.<T> defaultValue(), false);
  }

  public PropertyGroup getNotNullModel() {
    return model == null ? (model = new PropertyGroup("model")) : model;
  }

  public void setModel(PropertyGroup model) {
    this.model = model;
  }

  public void addModel(String key, Object value) {
    this.addModel(key, null, null, value);
  }

  public void addModel(MetaEnum meta, Object value) {
    this.addModel(meta.toString(), meta.type(), meta.format(), value);
  }

  public void addModel(String key, PropertyType type, String format, Object value) {
    this.getNotNullModel()
        .addProperty(key, type, format, value);
  }

  public void addModel(String xEDIPath, String modelDefinitionPath) {
    this.model = asPropertyGroup(xEDIPath, modelDefinitionPath);
  }

  public void addModel(String xEDIPath, InputStream modelStream) {
    this.model = asPropertyGroup(xEDIPath, modelStream);
  }

  public boolean removeModel(MetaEnum meta) {
    return this.getNotNullModel()
        .getProperties()
        .remove(meta.toString()) == null;
  }

  // - METADATA -
  public String getVersion() {
    return this.getMetadata(VERSION);
  }

  public Scheduling getScheduling() {
    return Scheduling.valueOf(this.<String> getMetadata(SCHEDULING));
  }

  public Boolean getPersistDocument() { return this.getMetadata(Metadata.PERSIST_DOCUMENT); }

  public void setPersistDocument(boolean persistValue) { this.addMetadata(Metadata.PERSIST_DOCUMENT, persistValue); }

  // Messages
  public String getInfo() {
    return this.getMetadata(INFO_MESSAGE);
  }

  public void setInfo(String message) {
    this.addMetadata(INFO_MESSAGE, truncate(message, 4000));
  }

  public String getWarning() {
    return this.getMetadata(WARNING_MESSAGE);
  }

  public void setWarning(String message) {
    this.addMetadata(WARNING_MESSAGE, truncate(message, 4000));
  }

  public String getError() {
    return this.getMetadata(ERROR_MESSAGE);
  }

  public void setError(String message) {
    this.addMetadata(ERROR_MESSAGE, truncate(message, 4000));
  }

  // - FILES -
  public List<DocumentFile> getFiles() {
    return files;
  }

  public void setFiles(List<DocumentFile> files) {
    this.files = files;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public boolean hasFiles() {
    return isNotEmpty(files);
  }

  public DocumentFile getFirstDocumentFile() {
    return this.hasFiles() ? this.files.get(0) : null;
  }

  public DocumentFile getLastDocumentFile() {
    return this.hasFiles() ? this.files.get(this.files.size() - 1) : null;
  }

  public File getFirstFile() {
    DocumentFile dof;
    return (dof = this.getFirstDocumentFile()) != null ? dof.getFile() : null;
  }

  /**
   * Returns the first DocumentFile with the specified type, contained in the given document. If the document doesn't contains a file with
   * the specified type, returns {@code null}.
   *
   * @param doc
   * @param type
   * @return
   */
  public DocumentFile getDocumentFileWithType(FileType type) {
    return findFileByType(this, type);
  }

  public List<DocumentFile> getDocumentFilesWithType(FileType type) {
    return findFiles(this, "type", type);
  }

  public boolean hasFileType(FileType type) {
    return this.getDocumentFileWithType(type) != null;
  }


  public boolean removeFile(DocumentFile def) {
    boolean isRemoved = this.files.remove(def);
    if (isRemoved) def.setDocument(null);
    return isRemoved;
  }

  public void addFile(DocumentFile file) {
    if (file == null) return;
    VariableHelper.verifyIfOtherDocumentHasTaxOriginalTag(this, file);
    file.setDocument(this);
    this.files.add(file);
  }

  public void addFiles(Collection<DocumentFile> files) {
    if (files == null) return;
    this.files.addAll(files);
  }

  // -- HIERARCHY --

  @Override
  public void addChild(Document child) {
    if (child == null)
      return;

    // Remove from old parent - one-to-many multiplicity
    if (child.getParent() != null && child.getParent()
        .getChildren() != null)
      child.getParent()
          .getChildren()
          .remove(child);

    // Set parent in child
    child.setParent(this);

    // Set child in parent
    this.getChildren()
        .add(child);
  }

  @Override
  public boolean removeChild(Document child) {
    if (child == null)
      return false;

    // Remove from parent and set parent to null
    boolean hasBeenRemoved = false;
    if (child.getParent() != null)
      hasBeenRemoved = child.getParent()
          .getChildren()
          .remove(child);

    if (hasBeenRemoved)
      child.setParent(null);

    return hasBeenRemoved;
  }

  /**
   * Remove all the children of the document
   *
   * @return
   */
  public List<Document> removeAllChildren() {
    if (!this.hasChildren()) {
      return Collections.emptyList();
    }

    List<Document> children = new ArrayList<>();
    Iterator<Document> iterator = this.getChildren()
        .iterator();
    while (iterator.hasNext()) {
      Document child = iterator.next();
      iterator.remove();
      child.setParent(null);
      children.add(child);
    }

    return children;
  }

  @Override
  @Transient
  public boolean isRoot() {
    return this.parent == null;
  }

  @Override
  public Document getRoot() {
    return isRoot() ? this : this.parent.getRoot();
  }

  @Override
  public boolean hasChildren() {
    return this.getChildCount() > 0;
  }

  @Override
  public int getChildCount() {
    return this.children == null ? 0 : this.children.size();
  }

  @Override
  @XmlTransient
  public Document getParent() {
    return this.parent;
  }

  @Override
  public void setParent(Document parent) {
    this.parent = parent;
  }

  @Override
  @XmlTransient
  public Set<Document> getChildren() {
    return this.children;
  }

  @Override
  public void setChildren(Set<Document> children) {
    this.children = children;
  }

  @XmlTransient
  @SuppressWarnings("unchecked")
  public <T extends Document> T getTypedParent() {
    return (T) this.parent;
  }

  /**
   * @return stream of all the documents part of this document hierarchy (this document as root and all its children).
   */
  public Stream<Document> flattened() {
    return concat(
        of(this),
        children
            .stream()
            .flatMap(Document::flattened));
  }

  // -- INDEX --

  public boolean isIndexStored() {
    return getIndexId() != null;
  }

  public boolean isIndexed() {
    return getIndex().isValid();
  }

  public boolean isIndexType(Class<? extends Indexable> indexableType) {
    return getIndexType() != null && getIndexType().isAssignableFrom(indexableType);
  }

  public Index getIndex() {
    return index == null ? index = new Index() : index;
  }

  public void setIndex(Index index) {
    this.index = index;
  }

  // index delegated method
  public String getIndexId() {
    return getIndex().getId();
  }

  public String getIndexReference() {
    return getIndex().getReference();
  }

  public String getIndexCollection() {
    return getIndex().getCollection();
  }

  public <I extends Indexable> Class<I> getIndexType() {
    return getIndex().getType();
  }

  public <I extends Indexable> I getIndexValue() {
    return getIndex().<I> getValue();
  }

  public void setIndexValue(Indexable index) {
    this.index = index == null
        ? null
        : new Index(index, this.getIndexCollection()); // keeps the collection
  }

  public String getOrderingParty() {
    return this.orderingParty;
  }

  public void setOrderingParty(String orderingParty) {
    this.orderingParty = orderingParty;
  }

  // -- REVISION --

  public Long getRevision() {
    return revision;
  }

  /*
   * -- OVERRIDE --
   */

  /** @see java.lang.Object#toString() */
  @Override
  public String toString() {
    return new StringBuilder("Document@").append(this.getId())
        .append("[ref=")
        .append(this.getReference())
        .append(", UUID=")
        .append(this.getUuid())
        .append(", type=")
        .append(this.getType())
        .append(", owners=")
        .append(this.getOwners())
        .append(", from=")
        .append(this.getFrom())
        .append(", to=")
        .append(this.getTo())
        .append(", acquisition=")
        .append(this.getAcquisition())
        .append(", translator=")
        .append(this.getTranslator())
        .append(", orderingParty=")
        .append(this.getOrderingParty())
        .append(", status=")
        .append(this.status)
        .append(", tranport=")
        .append(this.getTransport())
        .append(", nature=")
        .append(this.getNature())
        .append(", immatpdp=")
        .append(this.getImmatpdp())
        .append(", b2gRules=")
        .append(this.getB2gRules())
        .append(", ereportingOption=")
        .append(this.getEreportingOption())
        .append(", countryRules1=")
        .append(this.getCountryRules1())
        .append(", countryRules2=")
        .append(this.getCountryRules2())
        .append(", useCase=")
        .append(this.getUseCase())
        .append(", billingFramework=")
        .append(this.getBillingFramework())
        .append(", sovosId=")
        .append(this.getSovosId())
        .append(", sovosStatus=")
        .append(this.getSovosStatus())
        .append(", sovosTransactionId=")
        .append(this.getSovosTransactionId())
        .append(", sovosStatusCode=")
        .append(this.getSovosStatusCode())
        .append(", sovosNotificationId=")
        .append(this.getSovosNotificationId())
        .append(", archiveExpirationDate=")
        .append(this.getArchiveExpirationDate())
        .append(", rossumId=")
        .append(this.getRossumId())
        .append(", thirdParty=")
        .append(this.getThirdParty())
        .append(", purpose=")
        .append(this.getPurpose())
        .append(", creation=")
        .append(this.creationDate)
        .append(", modification=")
        .append(this.modificationDate)
        .append(", revision=")
        .append(this.revision)
        .append(", index=")
        .append(this.index)
        .append(", profile=")
        .append(this.profile)
        .append("]")
        .toString();
  }

  public String getDocumentKeyValuePairs() {
    return new StringBuilder()
        .append("id=")
        .append(this.getId())
        .append("\n")
        .append("reference=")
        .append(this.getReference())
        .append("\n")
        .append("creationDate=")
        .append(this.getCreationDate())
        .append("\n")
        .append("type=")
        .append(this.getType())
        .append("\n")
        .append("stage=")
        .append(this.getStage())
        .append("\n")
        .append("status=")
        .append(this.getStatus())
        .append("\n")
        .append("owners=")
        .append(this.getOwners())
        .append("\n")
        .append("from=")
        .append(this.getFrom())
        .append("\n")
        .append("to=")
        .append(this.getTo())
        .append("\n")
        .append("acquisition=")
        .append(this.getAcquisition())
        .append("\n")
        .append("translator=")
        .append(this.getTranslator())
        .append("\n")
        .append("orderingParty=")
        .append(this.getOrderingParty())
        .append("\n")
        .append("indexReference=")
        .append(this.getIndexReference())
        .append("\n")
        .append("linCount=")
        .append(this.getLinCount())
        .append("\n")
        .append("modificationDate=")
        .append(this.getModificationDate())
        .append("\n")
        .append("number=")
        .append(this.getNumber())
        .append("\n")
        .append("subtype=")
        .append(this.getSubtype())
        .append("\n")
        .append("archiveStatus=")
        .append(this.getArchiveStatus())
        .append("\n")
        .append("purpose=")
        .append(this.getPurpose())
        .append("\n")
        .append("transport=")
        .append(this.getTransport())
        .append("\n")
        .append("thirdParty=")
        .append(this.getThirdParty())
        .append("\n")
        .append("nature=")
        .append(this.getNature())
        .append("\n")
        .append("immatpdp=")
        .append(this.getImmatpdp())
        .append("\n")
        .append("b2gRules=")
        .append(this.getB2gRules())
        .append("\n")
        .append("ereportingOption=")
        .append(this.getEreportingOption())
        .append("\n")
        .append("countryRules1=")
        .append(this.getCountryRules1())
        .append("\n")
        .append("countryRules2=")
        .append(this.getCountryRules2())
        .append("\n")
        .append("useCase=")
        .append(this.getUseCase())
        .append("\n")
        .append("billingFramework=")
        .append(this.getBillingFramework())
        .append("\n")
        .append("sovosId=")
        .append(this.getSovosId())
        .append("\n")
        .append("sovosStatus=")
        .append(this.getSovosStatus())
        .append("\n")
        .append("sovosTransactionId=")
        .append(this.getSovosTransactionId())
        .append("\n")
        .append("sovosStatusCode=")
        .append(this.getSovosStatusCode())
        .append("\n")
        .append("sovosNotificationId=")
        .append(this.getSovosNotificationId())
        .append("\n")
        .append("archiveExpirationDate=")
        .append(this.getArchiveExpirationDate())
        .append("\n")
        .append("rossumId=")
        .append(this.getRossumId())
        .append("\n")
        .append("uuid=")
        .append(this.getUuid())
        .append("\n")
        .append("profile=")
        .append(this.getProfile())
        .append("\n")
        .toString();
  }

  /** @see java.lang.Object#equals(java.lang.Object) */
  @Override
  public boolean equals(Object o) {
    if (o == null)
      return false;
    if (!(o instanceof Document))
      return false;

    Document that = (Document) o;
    // first check the ids
    if (this.id != null)
      return this.id.equals(that.getId());

    if (this.getUuid() != null)
      return this.uuid.equals(that.getUuid());

    if (this.reference != null)
      return this.reference.equals(that.getReference());

    return super.equals(o);
  }

  /** @see java.lang.Object#hashCode() */
  @Override
  public int hashCode() {
    int hashCode = 1;
    hashCode = 31 * hashCode + (int) (+serialVersionUID ^ (serialVersionUID >>> 32));
    hashCode = 31 * hashCode + (this.getId() == null ? 0 : getId().hashCode());
    return hashCode;
  }

  /** @see java.lang.Comparable#compareTo(java.lang.Object) */
  @Override
  public int compareTo(Document o) {
    return this.uuid != null ? this.uuid.compareTo(o.getUuid()) : -1;
  }

}
