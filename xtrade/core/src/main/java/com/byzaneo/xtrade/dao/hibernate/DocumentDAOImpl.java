package com.byzaneo.xtrade.dao.hibernate;

import com.byzaneo.commons.bean.Property;
import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.DataAccessException;
import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.commons.util.*;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.query.limit.Limit;
import com.byzaneo.query.util.QueryHelper;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.Document.Metadata;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.Index;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.dao.DocumentDAO;
import com.byzaneo.xtrade.process.DocumentPolicy;
import com.byzaneo.xtrade.service.DocumentService.DocumentChildrenPolicy;
import com.byzaneo.xtrade.util.ReportedCallable;
import com.byzaneo.xtrade.util.*;
import com.google.gson.*;
import org.apache.commons.collections4.*;
import org.apache.commons.io.output.NullOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.apache.poi.hssf.usermodel.*;
import org.hibernate.*;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.*;
import org.hibernate.transform.DistinctRootEntityResultTransformer;
import org.slf4j.Logger;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.*;

import javax.persistence.*;
import javax.persistence.criteria.*;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.CriteriaBuilder.In;
import java.io.*;
import java.nio.file.Path;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.*;
import java.util.regex.Pattern;
import java.util.stream.*;
import java.util.zip.*;

import static com.byzaneo.commons.bean.FileType.ARCHIVE;
import static com.byzaneo.commons.bean.FileType.REPORT;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATABASE_TYPE;
import static com.byzaneo.commons.util.DatePeriodHelper.getMaxDate;
import static com.byzaneo.commons.util.DatePeriodHelper.getMinDate;
import static com.byzaneo.commons.util.FileHelper.unzip;
import static com.byzaneo.commons.util.FormatHelper.asString;
import static com.byzaneo.commons.util.GsonHelper.createGson;
import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.commons.util.PersistentHelper.isPersisted;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.commons.util.SpringContextHelper.getContext;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.xtrade.api.DocumentStatus.NONE;
import static com.byzaneo.xtrade.api.DocumentType.FOLDER;
import static com.byzaneo.xtrade.dao.DocumentDAO.DocumentField.creationDate;
import static com.byzaneo.xtrade.dao.DocumentDAO.DocumentField.from;
import static com.byzaneo.xtrade.dao.DocumentDAO.DocumentField.id;
import static com.byzaneo.xtrade.dao.DocumentDAO.DocumentField.owners;
import static com.byzaneo.xtrade.dao.DocumentDAO.DocumentField.status;
import static com.byzaneo.xtrade.dao.DocumentDAO.DocumentField.to;
import static com.byzaneo.xtrade.dao.DocumentDAO.DocumentField.type;
import static com.byzaneo.xtrade.util.DocumentHelper.isDocumentTypeFolder;
import static com.byzaneo.xtrade.util.DocumentHelper.isThereAFolderIntheTree;
import static java.lang.Integer.MAX_VALUE;
import static java.lang.String.valueOf;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.nio.file.Files.deleteIfExists;
import static java.nio.file.Files.isDirectory;
import static java.nio.file.Files.isRegularFile;
import static java.nio.file.Files.newBufferedReader;
import static java.nio.file.Files.newDirectoryStream;
import static java.text.MessageFormat.format;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Optional.ofNullable;
import static java.util.regex.Pattern.compile;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.collections4.CollectionUtils.size;
import static org.apache.commons.io.IOUtils.closeQuietly;
import static org.apache.commons.io.IOUtils.toByteArray;
import static org.apache.commons.lang3.ArrayUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.apache.logging.log4j.Level.DEBUG;
import static org.apache.logging.log4j.Level.ERROR;
import static org.apache.logging.log4j.Level.INFO;
import static org.apache.logging.log4j.Level.WARN;
import static org.hibernate.CacheMode.IGNORE;
import static org.hibernate.ScrollMode.FORWARD_ONLY;
import static org.hibernate.criterion.MatchMode.ANYWHERE;
import static org.hibernate.criterion.MatchMode.START;
import static org.hibernate.criterion.Restrictions.between;
import static org.hibernate.criterion.Restrictions.disjunction;
import static org.hibernate.criterion.Restrictions.ilike;
import static org.hibernate.criterion.Restrictions.in;
import static org.hibernate.criterion.Restrictions.isNotNull;
import static org.hibernate.criterion.Restrictions.like;
import static org.hibernate.criterion.Restrictions.or;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.transaction.TransactionDefinition.PROPAGATION_REQUIRES_NEW;
import static org.springframework.util.Assert.isTrue;
import static org.springframework.util.Assert.notNull;

/**
 * <AUTHOR> ROSSI
 * @company Byzaneo
 */
@Repository(DocumentDAO.DAO_NAME)
public class DocumentDAOImpl extends GenericJpaDAO<Document, Long>
    implements DocumentDAO {
  private static final Logger log = getLogger(DocumentDAOImpl.class);

  private static final int SESSION_RENEW_SIZE = 50;

  private static final String DEFAULT_QUERY = "all";

  private static final int DOCUMENTS_COUNT_LOGS_LIMIT = 100000;

  private static final Pattern DOCUMENT_ARCHIVE_FOLDER = compile("^\\d*$");

  private static final String DOCUMENT_JSON = "document.json";

  private static final String INDEX_JSON = "index.json";

  private static final List<DocumentPolicy> RESTORING_POLICIES = asList(DocumentPolicy.DEAD, DocumentPolicy.IGNORE, DocumentPolicy.REPLACE);

  private static final int LIMIT_IN_STATEMENT_QUERY = 900;

  private static final Query ALIKE_QUERY = createBuilder().and(
      equal(Document_.reference.getName(), "(0)"),
      equal(Document_.type.getName(), "(1)"),
      equal(Document_.owners.getName(), "(2)"),
      equal(Document_.from.getName(), "(3)"),
      equal(Document_.to.getName(), "(4)"))
      .query();

  private static Gson archivingGson;

  /** @see DocumentDAO#updateDocumentFileBaseUri(String, String) */
  @Override
  @Transactional
  public void updateDocumentFileBaseUri(final String oldBaseUri, final String newBaseUri) {
    try (final Stream<DocumentFile> stream = getSession()
        .createQuery("from DocumentFile dof where dof.uri like :uri", DocumentFile.class)
        .setParameter("uri", oldBaseUri + "%")
        .stream()) {
      stream.forEach(dof -> dof.setUri(dof.getUri()
          .replace(oldBaseUri, newBaseUri)));
    }
  }
@Override
@Transactional
public void updateDocumentLock(String lockProcessInstanceId, List<Long> docIds) {
  getEntityManager().createQuery("UPDATE " + Document.class.getSimpleName() + " d SET d.locks = :locks WHERE d.id in :docIds")
      .setParameter("locks", lockProcessInstanceId)
      .setParameter("docIds", docIds)
      .executeUpdate();
}

  @Transactional
  @Override
  public void updateSqlDocumentStatus(List<String> invoices) {
    if (invoices == null || invoices.isEmpty()) {
      return;
    }

    int updated = entityManager.createQuery(
            "UPDATE Document d SET d.status = :status WHERE d.index.id IN :invoiceIds")
        .setParameter("status", new DocumentStatusEntity(DocumentStatus.OCR_TO_VERIFY))
        .setParameter("invoiceIds", invoices)
        .executeUpdate();

    log.debug("Updated {} records in SQL database", updated);
  }

  @Override
  @Transactional
  public void unlockDocumentsForEndedProcesses(Set<String> endedProcessesIds) {
    getEntityManager().createQuery(
            "UPDATE " + Document.class.getSimpleName() + " d SET d.locks = :locks WHERE d.locks in :endedProcessesIds")
        .setParameter("locks", null)
        .setParameter("endedProcessesIds", endedProcessesIds)
        .executeUpdate();
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findById(java.lang.Class, java.lang.Long) */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> D findById(final Class<D> type, final Long id) throws DataAccessException {
    return id == null
        ? null
        : this.getRepository(type)
            .findById(id)
            .orElse(null);
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#getDocumentLazy( java.lang.Long) */
  @Override
  @Transactional(readOnly = true)
  public Document getDocumentLazy(final Long id) throws DataAccessException {

    // since we don't want to make all the joins to get all referenced entities
    // perform a search by id which can only return 1 or 0 results
    List<Document> documents = this.getRepository()
        .findAll((root, query, cb) -> cb.equal(root.get(Document_.id), id));

    return documents == null || documents.isEmpty()
        ? null
        : documents.get(0);
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findByIds(java.util.List) */
  @Override
  @Transactional(readOnly = true)
  public List<Document> findByIds(List<Long> ids) throws DataAccessException {
    return isEmpty(ids)
        ? emptyList()
        : this.getRepository()
            .findAllById(ids);
  }

  /*
   * FIND
   */

  @Override
  @Transactional(readOnly = true)
  public Document findByUUID(String uuid) throws DataAccessException {
    return this.getRepository()
        .findOne((root, query, cb) -> cb.equal(root.get(Document_.uuid), uuid))
        .orElse(null);
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findByReference(java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  public Document findByReference(final String reference) throws DataAccessException {
    if (isBlank(reference))
      return null;

    final Page<Document> page = this.getRepository()
        .findAll(
            (root, query, cb) -> cb.equal(root.get(Document_.reference), reference),
            PageRequest.of(0, 1, DESC, Document_.creationDate.getName()));

    if (!page.hasContent())
      return null;

    Document doc = page.iterator()
        .next();

    if (page.getTotalElements() > 1)
      log.warn("Multiple ({}) documents with reference '{}' has been found. " +
          "The last created has been returned: {}", page.getTotalElements(), reference, doc);

    return doc;
  }

  @Override
  @Transactional(readOnly = true)
  public List<Document> findByLockCode(String lockCode) throws DataAccessException {
    if (StringUtils.isBlank(lockCode)) {
      log.debug("Wrong parameter lockCode");
      return Collections.<Document> emptyList();
    }

    List<Document> result = this.getRepository()
        .findAll(
            (root, query, cb) -> cb.equal(root.get(Document_.locks), lockCode));
    return result;

  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findByIndexReferences(java.util.List) */
  @Override
  @Transactional(readOnly = true)
  public List<Document> findByIndexReferences(final Set<String> entityRefs) throws DataAccessException {
    final CriteriaBuilder cb = this.entityManager.getCriteriaBuilder();
    CriteriaQuery<Document> cq = cb.createQuery(Document.class);
    Root<Document> root = cq.from(Document.class);

    log.info("Splitting in chunks of {} elements", LIMIT_IN_STATEMENT_QUERY);
    List<Document> documents = new ArrayList<>();
    List<List<String>> partitions = ListUtils.partition(entityRefs.stream()
        .collect(toList()), LIMIT_IN_STATEMENT_QUERY);

    for (List<String> partition : partitions) {
      In<String> inClause = cb.in(root.get(Document_.index)
          .get(Index_.reference));
      cq.where(inClause);
      partition.forEach(ref -> inClause.value(ref));
      cq.select(root)
          .where(inClause);
      documents.addAll(this.entityManager
          .createQuery(cq)
          .getResultList());
    }
    int docsSize = documents.size();
    int entityRefsSize = entityRefs.size();
    if (docsSize != entityRefsSize)
      log.warn("The size of the documents {} is different than the size of entityRefs: {}", docsSize, entityRefsSize);
    return documents;
  }

  /**
   * @see com.byzaneo.xtrade.dao.DocumentDAO#findByReferenceStartWithAndType(java.lang.String, FileType)
   */
  @Override
  @Transactional(readOnly = true)
  public List<Document> findByReferenceStartWithAndType(final String refStartWith, final FileType type) throws DataAccessException {
    return isBlank(refStartWith) || type == null
        ? emptyList()
        : this.getRepository()
            .findAll(
                (root, query, cb) -> cb.and(
                    cb.like(root.get(Document_.reference), START.toMatchString(refStartWith)),
                    cb.equal(root.join(Document_.files)
                        .get(DocumentFile_.type), type)),
                Sort.by(DESC, Document_.creationDate.getName()));
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findByTypeAndRefAndVersion(FileType, String, String) */
  @Override
  @Transactional(readOnly = true)
  @Deprecated
  public Document findByTypeAndRefAndVersion(final FileType type, final String reference, final String version) throws DataAccessException {
    if (isBlank(reference) || type == null)
      return null;

    return this.getRepository()
        .findOne((root, query, cb) -> {
          Predicate predicate = cb.and(
              cb.like(root.get(Document_.reference), START.toMatchString(reference)),
              cb.equal(root.join(Document_.files)
                  .get(DocumentFile_.type), type));

          if (isBlank(version))
            return predicate;

          final Join<Object, Object> meta = root
              .join(Document_.metadata)
              .join(PropertyGroup_.properties.getName());

          return cb.and(predicate,
              cb.equal(meta.get(Property_.key.getName()), Metadata.VERSION.toString()),
              cb.equal(meta.get(Property_.value.getName()), version));
        })
        .orElse(null);
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findByTypes(com.byzaneo.commons.bean.FileType[]) */
  @Override
  @Transactional(readOnly = true)
  public Map<String, Document> findByTypes(final FileType... types) {
    return isEmpty(types)
        ? emptyMap()
        : this.getRepository()
            .findAll((root, query, cb) -> root.join(Document_.files)
                .get(DocumentFile_.type)
                .in((Object[]) types))
            .stream()
            .collect(toMap(Document::getReference, Function.identity()));
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findLastCreated(String, List, int) */
  @Override
  @Transactional(readOnly = true)
  public List<Document> findLastCreated(final String owner, final List<FileType> types, final int number) {
    return this.getRepository()
        .findAll((root, query, cb) -> {
          query.distinct(true);
          List<Predicate> predicates = new ArrayList<>(2);
          if (owner != null)
            predicates.add(cb.like(root.get(Document_.owners), MatchMode.ANYWHERE.toMatchString(owner)));
          if (types != null && types.size() < FileType.values().length)
            predicates.add(root.join(Document_.files)
                .get(DocumentFile_.type)
                .in(types));
          return cb.and(predicates.toArray(new Predicate[predicates.size()]));
        },
            PageRequest.of(0, number > 0 ? number : MAX_VALUE, DESC, Document_.creationDate.getName()))
        .getContent();
  }

  /**
   * @see com.byzaneo.xtrade.dao.DocumentDAO#findGroupsByOwnerTypeAndStatus(java.lang.String, java.lang.String,
   *      com.byzaneo.xtrade.api.DocumentStatusEntity, java.lang.String)
   */
  @Override
  @Transactional(readOnly = true)
  public Map<Object, List<Document>> findGroupsByOwnerTypeAndStatus(final String owner, final String type,
      final DocumentStatusEntity status,
      final String groupProperty) throws DataAccessException {
    final CriteriaBuilder cb = this.getEntityManager()
        .getCriteriaBuilder();
    final CriteriaQuery<Object> query = cb.createQuery(Object.class);
    final Root<Document> root = query.from(getEntityClass());
    final javax.persistence.criteria.Path<Document> prop = root.get(groupProperty);
    query
        .select(prop)
        .where(
            cb.like(root.get(Document_.owners), ANYWHERE.toMatchString(owner)),
            cb.equal(root.get(Document_.type), type),
            cb.equal(root.get(Document_.status), status))
        .orderBy(cb.asc(prop));

    // TODO to use GenericJpaDAO#stream instead or rewrite query with a group by
    try (Stream<Object> stream = this.getSession()
        .createQuery(query)
        .getResultList()
        .stream()) {
      return stream.collect(Collectors.toMap(Function.identity(), o -> this.getRepository()
          .findAll((doc, q, builder) -> builder.and(
              builder.like(doc.get(Document_.owners), ANYWHERE.toMatchString(owner)),
              builder.equal(doc.get(Document_.type), type),
              builder.equal(doc.get(Document_.status), status),
              builder.equal(doc.get(groupProperty), o)),
              Sort.by(ASC, Document_.reference.getName()))));
    }
  }

  /*
   * FIELD'S VALUES
   */

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findDocumentFieldValues(java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  @SuppressWarnings("unchecked")
  public List<String> findDocumentFieldValues(final String field) throws DataAccessException {
    return getSession()
        .createQuery(
            format("select distinct doc.{0} from Document as doc where doc.{0} is not null", field))
        .getResultList();
  }

  /**
   * @see com.byzaneo.xtrade.dao.DocumentDAO#findAll(boolean)
   */
  @Override
  @Transactional(readOnly = true)
  public Collection<Document> findAll(boolean onlyRootCategories) {
    return onlyRootCategories
        ? this.getRepository()
            .findAll((root, query, cb) -> root.get(Document_.parent)
                .isNull())
        : findAll();
  }

  /**
   * @see com.byzaneo.xtrade.dao.DocumentDAO#findBy(java.lang.Class, org.springframework.data.domain.Pageable, java.util.List,
   *      java.util.List, java.lang.String, java.util.List, java.util.List, boolean, boolean, java.lang.String, java.util.Date,
   *      java.util.Date, com.byzaneo.xtrade.api.DocumentStatus[])
   */
  @Override
  @Transactional(readOnly = true)
  @SuppressWarnings("unchecked")
  public <D extends Document> Page<D> findBy(Class<D> type, Pageable pageable, List<String> owner, List<FileType> filetypes, String doctype,
      List<String> froms, List<String> tos, boolean fromsOrTos, boolean recipientsNotNull, String referenceLike, Date fromDate, Date toDate,
      DocumentStatusEntity... status) throws DataAccessException {
    return (Page<D>) this.repository.findAll(
        createSpecification(owner, filetypes, doctype, froms,
            tos, fromsOrTos, recipientsNotNull, referenceLike,
            fromDate, toDate, status),
        pageable == null ? Pageable.unpaged() : pageable);
  }

  /*
   * PROCESS
   */

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findReportDocuments(java.lang.String, int) */
  @Override
  @Transactional(readOnly = true)
  public List<Document> findReportDocuments(final String key, final int count) {
    final List<Document> reports = this.getRepository()
        .findAll((root, query, cb) -> {
          List<Predicate> predicates = new ArrayList<>(2);
          predicates.add(cb.equal(root.get(Document_.type), REPORT.toString()));
          if (isNotBlank(key))
            predicates.add(cb.like(root.get(Document_.reference), START.toMatchString(key)));

          return cb.and(predicates.toArray(new Predicate[predicates.size()]));
        },
            PageRequest.of(0, count > 0 ? count : MAX_VALUE, DESC, Document_.creationDate.getName()))
        .getContent();

    reports.forEach(d -> { // TODO to replace by a fetch
      size(d.getFiles());
      d.getMetaGroup();
    });

    return reports;
  }

  /*
   * REPORTING
   */

  // -- DATABASE SPECIFIC --

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findDocumentFieldValues(java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  public Map<String, Map<String, Number>> reportCount(final List<DocumentField> groupFields,
      final List<DatePeriod> periods, final DocumentField countField, final String owner,
      final List<String> doctypes, final List<String> froms, final List<String> tos,
      final boolean recipientsNotNull, final List<DocumentStatus> statusList) throws DataAccessException {

    Collections.sort(periods);

    // SELECT
    StringBuilder q = new StringBuilder("SELECT ");
    // group-key
    if (DatabaseHelper.isOracle()) {
      for (DocumentField gfield : groupFields) {
        q.append(q.length() > 8 ? "||'-'||" : "")
            .append(gfield.getFieldValue());
      }
    }
    else {
      // 'ora-00909 invalid number of arguments' if the query uses a CONCAT function with only 1 argument
      if (groupFields.size() == 1) {
        q.append(groupFields.get(0)
            .getFieldValue());
      }
      else {
        q.append("CONCAT(");
        for (Iterator<DocumentField> iter = groupFields.iterator(); iter.hasNext();) {
          DocumentField gfield = iter.next();
          q.append(gfield.getFieldValue());
          if (iter.hasNext()) {
            q.append(",'-',");
          }
        }
        q.append(')');
      }
    }

    // periods
    for (DatePeriod p : periods) {
      q.append(format(", ''{0}'', COUNT(case when ({1} between {2} and {3}) then {4} end)",
          p.getLabel(), creationDate.getField(),
          toSqlDate(p.getStartDate()),
          toSqlDate(p.getEndDate()),
          countField == null ? 1 : countField.getField()));
    }
    q.append(" FROM XTD_DOCUMENT");

    // WHERE, GROUP BY and ORDER BY
    appendReportFilterClauses(q, groupFields,
        getMinDate(periods), getMaxDate(periods),
        owner, doctypes, statusList, false,
        froms, tos, recipientsNotNull);

    @SuppressWarnings("unchecked")
    List<Object[]> results = getEntityManager().createNativeQuery(q.toString())
        .getResultList();

    if (isEmpty(results))
      return emptyMap();

    // PROCESS RESULT
    final Map<String, Map<String, Number>> r = new LinkedHashMap<>(results.size());
    for (Object[] row : results) {
      Map<String, Number> dv = r.get(row[0]);
      if (dv == null) {
        dv = new LinkedHashMap<>(periods.size());
        r.put((String) row[0], dv);
      }
      for (int i = 1; i < periods.size() + 1; i++)
        dv.put((String) row[i * 2 - 1], (Number) row[i * 2]);
    }

    return r;
  }

  /**
   * @see DocumentDAO#asXLS(OutputStream, String, boolean, Map, List, List, String, List, List, boolean, boolean, String, Date, Date,
   *      DocumentStatus...)
   */
  @Override
  @Transactional(readOnly = true)
  public int asXLS(final OutputStream out, final String sortedBy, final boolean ascending, final Map<String, String> cpyNames,
      final List<String> owners, final List<FileType> filetypes, final String doctype,
      final List<String> froms, final List<String> tos, final boolean fromsOrTos, final boolean recipientsNotNull,
      final String referenceLike, final Date fromDate, final Date toDate, final DocumentStatus... status)
      throws DataAccessException {

    final Session session = getSession();
    final String orderBy = isBlank(sortedBy) ? "creationDate" : sortedBy;
    final Order o = ascending ? Order.asc(orderBy) : Order.desc(orderBy);
    final DetachedCriteria dc = createDetachedCriteria(getEntityClass(), owners, filetypes, doctype, froms,
        tos, fromsOrTos, recipientsNotNull, referenceLike, fromDate,
        toDate, status);

    final ScrollableResults scroll = dc.getExecutableCriteria(session)
        .addOrder(o)
        .setResultTransformer(DistinctRootEntityResultTransformer.INSTANCE)
        .setCacheMode(IGNORE)
        .scroll(FORWARD_ONLY);

    int colidx, rowidx = 1;
    try (final HSSFWorkbook wb = new HSSFWorkbook()) {
      final HSSFSheet sheet = wb.createSheet("DESADV IN");
      HSSFRow row;

      // Date style
      final HSSFCellStyle cellStyle = wb.createCellStyle();
      cellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("m/d/yy"));

      // -- HEADERS --
      colidx = 0;
      row = sheet.createRow(0);
      row.createCell(colidx++)
          .setCellValue(new HSSFRichTextString("STATUS"));
      row.createCell(colidx++)
          .setCellValue(new HSSFRichTextString("REFERENCE"));
      row.createCell(colidx++)
          .setCellValue(new HSSFRichTextString("FROM"));
      row.createCell(colidx++)
          .setCellValue(new HSSFRichTextString("FROM_NAME"));
      row.createCell(colidx++)
          .setCellValue(new HSSFRichTextString("DATE"));

      // -- ROWS --
      Document d;
      HSSFCell cell;
      String cpyName;
      while (scroll.next()) {
        d = (Document) scroll.get(0);
        colidx = 0;
        row = sheet.createRow(rowidx++);
        row.createCell(colidx++)
            .setCellValue(new HSSFRichTextString(d.getStatus()
                .toString()));
        row.createCell(colidx++)
            .setCellValue(new HSSFRichTextString(d.getReference()));
        row.createCell(colidx++)
            .setCellValue(new HSSFRichTextString(d.getFrom()));
        row.createCell(colidx++)
            .setCellValue(new HSSFRichTextString(
                cpyNames == null || (cpyName = cpyNames.get(d.getFrom())) == null ? "" : cpyName));
        cell = row.createCell(colidx++);
        cell.setCellValue(d.getCreationDate());
        cell.setCellStyle(cellStyle);

        if (rowidx % SESSION_RENEW_SIZE == 0) {
          session.flush();
          session.clear();
        }
      }

      // -- WRITE --
      wb.write(out);
    }
    catch (Exception e) {
      throw new DataAccessException(e);
    }
    finally {
      scroll.close();
      closeQuietly(out);
    }
    return rowidx > 0 ? rowidx - 1 : 0;
  }

  /**
   * @see com.byzaneo.xtrade.dao.DocumentDAO#archiving(Class, Query, Document, boolean, DocumentChildrenPolicy, ReportedCallable,
   *      IndexOperations, boolean)
   */
  @Override
  public synchronized Document archiving(final Class<Document> type, final Query query,
      final Document archive, final boolean remove,
      final DocumentChildrenPolicy childrenPolicy,
      final ReportedCallable<Document> reporting,
      final IndexOperations indexOperations,
      final boolean dryRun) {

    // sanity checks
    if (archive == null)
      return null;
    if (indexOperations == null) {
      archive.setStatusWithEnumValue(DocumentStatus.ERROR);
      archive.setError("Missing index operations");
      return archive;
    }

    // archives documents
    final boolean readonly = !remove || dryRun;
    final Set<Index> indexes = new HashSet<>();
    final Set<File> files = new HashSet<>();
    final Set<Long> docids = new HashSet<>();
    final String applicationType = getConfigurationService().getProperties()
        .getProperty("app.type");

    final Document arch = createTransactionTemplate(PROPAGATION_REQUIRES_NEW, readonly)
        .execute(new TransactionCallback<Document>() {

          /**
           * @see org.springframework.transaction.support.TransactionCallback#doInTransaction(org.springframework.transaction.TransactionStatus)
           */
          @Override
          public Document doInTransaction(TransactionStatus status) {
            final Session session = getSession();
            final DocumentChildrenPolicy cpolicy = childrenPolicy == null
                ? DocumentChildrenPolicy.Detach
                : childrenPolicy;

            // excludes documents with FOLDER type from archiving
            // there are not considered as proper Documents
            // but used as virtual file system directory
            final Query q = QueryBuilder.createBuilder(query)
                .query();
            final QuerySpecification<Document> spec = new QuerySpecification<Document>(q);
            final String qstring = query == null || isBlank(query.toString()) ? DEFAULT_QUERY : query.toString();
            // temporary total document count
            // without the children policy applied
            final int total = getCountQuery(spec, type).getSingleResult()
                .intValue();
            reporting.getReport()
                .setDocumentCount(total);
            if (total < 1) {
              log.info("No document to process found with query: {}", qstring);
              archive.setStatusWithEnumValue(NONE);
              return archive;
            }

            final Logs logs = reporting.getReport()
                .getLogs();
            logs.info("Processing %s documents from query: %s", total, qstring);

            // archiving?
            final File archiveFile = archive.getFirstFile();
            final boolean zipit = !dryRun && archiveFile != null;
            if (zipit)
              logs.info("Archive file: %s", archiveFile);

            final AtomicInteger count = new AtomicInteger();
            final AtomicInteger childcount = new AtomicInteger();
            Supplier<Stream<Document>> streamSupplier = () -> stream(getQuery(spec, (Sort) null, null/*
                                                                                                      * cannot put limit on
                                                                                                      * ScrollableResults result
                                                                                                      */, "files", "metaGroup", "model"));
            try (
                final OutputStream out = archiveFile == null
                    ? new NullOutputStream()
                    : new FileOutputStream(archiveFile);
                final ZipOutputStream zip = new ZipOutputStream(out, UTF_8)) {
              streamSupplier.get()
                  .forEach(document -> { // NOSONAR
                                         // - progress -
                    count.incrementAndGet();
                    if (log.isDebugEnabled()) {
                      log.debug("Processing document {}/{} ({}%): {}",
                          count,
                          total,
                          reporting.getProgress(),
                          document.getId());
                    }
                    // - index -
                    if (document.isIndexed() &&
                        !populateIndex(indexOperations, document))
                      logs.warn("Impossible to retrieve index for: %s", document);
                    // - archiving -
                    try {
                      // children management
                      boolean ignore = false;
                      if (DocumentHelper.isDocumentNotEligibleToPurge(applicationType, document)) {
                        ignore = true;
                      }
                      if (isDocumentTypeFolder(document)) {
                        logger(INFO, null, "Cannot purge %s : its type is FOLDER", document.getReference());
                        return;
                      }
                      else if (isThereAFolderIntheTree(document)) {
                        logger(INFO, null, "Cannot purge %s : has a FOLDER in a parent or a child", document.getReference());
                        return;
                      }

                      if (document.hasChildren()) {
                        switch (cpolicy) {
                        case Cascade:
                          int cascaded = cascading(session,
                              zip, zipit, valueOf(document.getId()),
                              document.getChildren());
                          childcount.addAndGet(cascaded);
                          logger(INFO, null, "Document %s children cascaded count: %s",
                              document.getId(), cascaded);
                          break;
                        case Ignore:
                          List<Document> eligibleDocuments = streamSupplier.get()
                              .filter(doc -> !DocumentHelper.isDocumentNotEligibleToPurge(applicationType, document))
                              .collect(Collectors.toList());
                          ignore = areAllDescedantsEligibleToPurge(eligibleDocuments, document, ignore);
                          if (ignore) {
                            logger(INFO, null, "Parent document ignored: %s", document.getId());
                          }
                          else {
                            List<Document> children = document.removeAllChildren();
                            children.forEach(session::save);
                          }
                          break;
                        case Detach:
                        default:
                          if (!readonly) {
                            List<Document> children = document.removeAllChildren();
                            children.forEach(session::save);
                            logger(INFO, null, "Document %s children detached count: %s",
                                document.getId(), children.size());
                          }
                          break;
                        }
                      }

                      // ignored document due to the "Ignore" children policy
                      // are excluded from the archive (considered as dead)
                      if (!ignore) {
                        // deletes document
                        delete(session, document, true);
                        // backing up...
                        zip(zip, document, zipit, valueOf(document.getId()));
                        // adds successfully archived document
                        // to archive document
                        archive.addChild(document);
                      }
                    }
                    catch (Exception e) {
                      logger(ERROR, e, "Error processing document: %s", document);
                    }
                    finally {
                      // increments progress
                      reporting.setProgress(count.get() * 100 / total);
                    }
                  }); // -end for

              // finalize the report
              logs.info("%s/%s documents successfully processed", archive.getChildCount(), total);
              reporting.getReport()
                  .setDeadCount(total - archive.getChildCount());
              reporting.getReport()
                  .setDocumentCount(archive.getChildCount() + childcount.get());
              reporting.getReport()
                  .setInProgress(false);
              // should we add the report to the archive document as
              // index or document's file?

              // finalize the archive
              archive.setStatus(reporting.getReport()
                  .getStatus());
              return archive;
            }
            catch (Exception e) {
              throw new DataAccessException(e);
            }
          }

          private boolean areAllDescedantsEligibleToPurge(List<Document> eligibleDocuments, Document document, boolean ignore) {
            boolean result = ignore;
            if (document.getChildren() != null) {
              for (Document child : document.getChildren()) {
                if (eligibleDocuments.contains(child)) {
                  areAllDescedantsEligibleToPurge(eligibleDocuments, child, result);
                }
                else {
                  result = true;
                  break;
                }
              }
            }
            return result;
          }

          private int cascading(Session session,
              ZipOutputStream zip, boolean zipit, String zipFolder,
              Set<Document> documents) {
            final AtomicInteger count = new AtomicInteger();
            documents.forEach(doc -> {
              String folder = String.format("%s/%s", zipFolder, doc.getId());
              count.incrementAndGet();
              // recurses
              if (doc.hasChildren()) {
                count.addAndGet(cascading(session,
                    zip, zipit, folder,
                    doc.getChildren()));
              }
              // removes
              delete(session, doc, false);
              // zips
              zip(zip, doc, zipit, folder);
            });
            return count.get();
          }

          private void delete(Session session, Document document, boolean detachParent) {
            if (readonly || docids.contains(document.getId()))
              return;

            docids.add(document.getId());

            // files
            document.getFiles()
                .stream()
                .peek(dof -> session.delete(session.get(DocumentFile.class, dof.getId())))
                .map(DocumentFile::getFile)
                .filter(Objects::nonNull)
                .forEach(files::add);

            // index
            Optional.ofNullable(document.getIndex())
                .ifPresent(indexes::add);

            // parent
            if (detachParent && document.getParent() != null) {
              Document parent = document.getParent();
              parent.removeChild(document);
              session.save(parent);
            }

            // document
            session.delete(document);
          }

          private void zip(ZipOutputStream zip, Document document, boolean zipit, String zipFolder) {
            if (!zipit)
              return;

            try {
              // Document
              final JsonElement jdoc = getGson().toJsonTree(document);
              // removes children from the JSon serialized
              // archive document
              jdoc.getAsJsonObject()
                  .remove("children");
              zip.putNextEntry(new ZipEntry(String.format("%s/%s", zipFolder, DOCUMENT_JSON)));
              zip.write(jdoc.toString()
                  .getBytes());
              zip.closeEntry();

              // Index
              if (document.getIndexValue() != null) {
                zip.putNextEntry(new ZipEntry(String.format("%s/%s", zipFolder, INDEX_JSON)));
                zip.write(getGson()
                    .toJson(document.getIndexValue(), document.getIndexType())
                    .getBytes());
                zip.closeEntry();
              }
            }
            catch (IOException ioe) {
              throw new UncheckedIOException(ioe);
            }

            // Document's files
            document.getFiles()
                .stream()
                .forEach(dof -> {
                  try {
                    File file = dof.getFile();
                    if (file != null && file.isFile()) {
                      zip.putNextEntry(new ZipEntry(String.format("%s/F%s/%s",
                          zipFolder, dof.getId(), file.getName())));
                      zip.write(toByteArray(file.toURI()));
                      zip.closeEntry();
                    }
                  }
                  catch (IOException ioe) {
                    throw new UncheckedIOException(ioe);
                  }
                });
          }

          private void logger(Level level, Throwable throwable, String message, Object... args) {
            final Logs logs = reporting.getReport()
                .getLogs();
            if (logs.getLogEventCount() < DOCUMENTS_COUNT_LOGS_LIMIT &&
                (level.isMoreSpecificThan(INFO))) {
              logs.log(level, null,
                  throwable != null
                      ? String.format("%s (%s)", message, getRootCauseMessage(throwable))
                      : message,
                  args);
            }
            else {
              String msg = String.format(message, args);
              if (DEBUG.equals(level))
                log.debug(msg);
              else if (INFO.equals(level))
                log.info(msg);
              else if (WARN.equals(level))
                log.warn(msg);
              else if (ERROR.equals(level))
                log.error(msg, throwable);
            }
          }
        });

    if (!readonly) {
      // removes indexes
      indexes.stream()
          .forEach(idx -> {
            try {
              indexOperations.remove(idx);
            }
            catch (Exception e) {
              log.warn("Error deleting document's index: {} ({})",
                  idx, getRootCauseMessage(e));
            }
          });

      // removes files
      files.stream()
          .map(File::getAbsolutePath)
          .map(Paths::get)
          .forEach(path -> {
            try {
              deleteIfExists(path);
            }
            catch (IOException e) {
              log.warn("Error deleting document's file: {} ({})",
                  path, getRootCauseMessage(e));
            }
          });
    }

    return arch;
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#restore(Document, DocumentPolicy, IndexOperations, ReportedCallable) */
  @SuppressWarnings("squid:S2259")
  @Override
  public Document restore(Document archive,
      DocumentPolicy policy,
      IndexOperations indexOperations,
      ReportedCallable<Document> reporting) {
    final File archfile;
    // sanity
    notNull(archive, "Archive to restore is required");
    notNull(indexOperations, "Index operation is required to restoration");
    isTrue((archfile = archive.getFirstFile()) != null && archfile.isFile(), "Archive file is required: " + archfile);
    DocumentPolicy documentPolicy = policy == null ? DocumentPolicy.DEAD : policy;
    isTrue(RESTORING_POLICIES.contains(documentPolicy),
        "Duplicate document policy not supported (" + RESTORING_POLICIES + "): " + documentPolicy);

    // decompresses
    File rootFolder = archfile.getParentFile();
    try {
      unzip(archfile, rootFolder, null);
    }
    catch (Exception e) {
      throw new DataAccessException(e,
          "Error decompressing archive %s (%s)",
          archfile, getRootCauseMessage(e));
    }

    reporting.incrementProgress(10); // 10%

    // goes thru the archives folders (documents)
    populateArchivedDocument(
        new ArchivedDocument(archive, rootFolder.toPath()),
        reporting.getReport()
            .getLogs());
    if (archive.getChildCount() == 0)
      return archive;

    reporting.incrementProgress(15); // 25%

    // cleans
    final AtomicInteger count = new AtomicInteger();
    HashSet<Document> children = new HashSet<>(archive.getChildren());
    archive.getChildren()
        .forEach(doc -> {
          doc.flattened()
              .filter(d -> !doc.equals(d))
              .distinct()
              .forEach(child -> {
                count.incrementAndGet();
                // removes document's children
                // from archive's children
                children.remove(child);
                // reset identifier
                child.setId(null);
              });
        });
    archive.setChildren(children);
    archive.getChildren()
        .forEach(d -> d.setId(null));

    reporting.getReport()
        .setDocumentCount(count.addAndGet(archive.getChildCount()));
    reporting.incrementProgress(15); // 40%

    // persists
    createTransactionTemplate(PROPAGATION_REQUIRES_NEW, false)
        .execute(new TransactionCallbackWithoutResult() {
          @Override
          protected void doInTransactionWithoutResult(TransactionStatus status) {
            restoreArchive(archive.getChildren(),
                documentPolicy,
                reporting,
                new AtomicInteger(),
                (x) -> x * 50 / reporting.getReport()
                    .getDocumentCount(),
                getSession());
          }
        }); // 90%

    // indexing
    archive.flattened()
        .filter(PersistentHelper::isPersisted)
        .map(Document::getIndex)
        .forEach(idx -> {
          try {
            indexOperations.saveIndex(idx);
          }
          catch (Exception e) {
            reporting.getReport()
                .getLogs()
                .warn("Error saving index %s (%s)", idx, getRootCauseMessage(e));
          }
        });

    reporting.incrementProgress(100); // 100%

    return archive;
  }

  private void restoreArchive(
      final Set<Document> children,
      final DocumentPolicy policy,
      final ReportedCallable<Document> reporting,
      final AtomicInteger count,
      final IntUnaryOperator progress,
      final Session session) {

    if (isEmpty(children))
      return;

    new HashSet<>(children).forEach(child -> {
      // goes deep...
      if (child.hasChildren()) {
        restoreArchive(child.getChildren(), policy, reporting, count, progress, session);
      }
      // progress...
      reporting.incrementProgress(
          progress.applyAsInt(count.incrementAndGet()));
      // persist documents on the way back...
      try {
        final Document parent = child.getParent();
        final Optional<Long> existing = this.searchAttribute(
            Document_.id,
            createBuilder(ALIKE_QUERY).query(true,
                child.getReference(),
                child.getType(),
                child.getOwners(),
                child.getFrom(),
                child.getTo()),
            0, 1)
            .stream()
            .findFirst();
        if (existing.isPresent()) {
          switch (policy) {
          case DEAD:
          case IGNORE:
            parent.removeChild(child);
            reporting.getReport()
                .setDeadCount(reporting.getReport()
                    .getDeadCount() + 1);
            reporting.getReport()
                .getLogs()
                .warn("Ignores %s due to existing document %s",
                    child.getReference(), existing.get());
            return;
          case REPLACE:
            this.remove(existing.get());
            reporting.getReport()
                .getLogs()
                .warn("%s replaces existing document %s",
                    child.getReference(), existing.get());
            break;
          default:
            reporting.getReport()
                .getLogs()
                .error(null, "Unknown policy: %s", policy);
            return;
          }
        }
        // stores only the root archive document children (cascading)
        if (parent.getParent() == null &&
            ARCHIVE.toString()
                .equals(parent.getType())) {
          child.setParent(null);
          session.save(child);
          reporting.getReport()
              .getLogs()
              .info("%s restored (%s)",
                  child.getReference(), child.getId());
        }
      }
      catch (Exception e) {
        reporting.getReport()
            .getLogs()
            .error(null, "Failed to restore: %s (%s)",
                child, getRootCauseMessage(e));
        reporting.getReport()
            .setDeadCount(reporting.getReport()
                .getDeadCount() + 1);
      }
    });
  }

  private void populateArchivedDocument(final ArchivedDocument adoc, final Logs logs) {
    try (final DirectoryStream<Path> folders = newDirectoryStream(adoc.getFolder(),
        (path) -> isDirectory(path) &&
            DOCUMENT_ARCHIVE_FOLDER
                .matcher(path.getFileName()
                    .toString())
                .matches())) {
      StreamSupport.stream(folders.spliterator(), false)
          .map(p -> this.toArchivedDocument(p, logs))
          .filter(Objects::nonNull)
          .filter(ArchivedDocument::isValid)
          .peek(ad -> this.populateArchivedDocument(ad, logs))
          .map(ArchivedDocument::getDocument)
          .forEach(adoc::addChild);
    }
    catch (IOException e) {
      logs.error(null, "Error restoring document from: %s (%s)",
          adoc.getFolder(), getRootCauseMessage(e));
    }
  }

  private ArchivedDocument toArchivedDocument(final Path folder, final Logs logs) {
    if (folder == null)
      return null;

    // document
    final Document document = this.fromArchivedJson(folder, DOCUMENT_JSON, Document.class, logs);
    // Folders document are not restored as there
    // are not considered as document but as a virtual
    // file system (see archiving)
    if (document == null || FOLDER.equals(document.getType()))
      return null;

    // properties groups
    Consumer<? super PropertyGroup> consumer = pg -> pg.getProperties()
        .values()
        .forEach(p -> p.setPropertyGroup(pg));
    Optional.ofNullable(document.getMetaGroup())
        .ifPresent(consumer);
    Optional.ofNullable(document.getModel())
        .ifPresent(consumer);

    // index value
    document.setIndexValue(this.fromArchivedJson(folder, INDEX_JSON, document.getIndexType(), logs));

    // files
    document.setFiles(document
        .getFiles()
        .stream()
        .map(dof -> {
          Path file = folder
              .resolve("F" + dof.getId())
              .resolve(dof.getFile()
                  .getName());
          if (!isRegularFile(file)) {
            // backward compatibility
            file = folder.resolve(dof.getFile()
                .getName());
            if (!isRegularFile(file)) {
              logs.warn("Document's file not found: %s", dof);
              return null;
            }
          }
          dof.setFile(file.toFile());
          dof.setDocument(document);
          dof.setId(null);
          return dof;
        })
        .filter(Objects::nonNull)
        .collect(toList()));

    return new ArchivedDocument(document, folder);
  }

  private <T> T fromArchivedJson(final Path folder, final String jsonFileName, final Class<T> type, final Logs logs) {
    if (type == null)
      return null;

    final Path path = folder.resolve(jsonFileName);
    if (isRegularFile(path)) {
      try (final Reader reader = newBufferedReader(path)) {
        return getArchivingGson().fromJson(reader, type);
      }
      catch (IOException e) {
        logs.error(null, "No or corrupt %s file found in %s (%s)",
            jsonFileName, folder, getRootCauseMessage(e));
      }
    }

    return null;
  }

  private Gson getArchivingGson() {
    if (archivingGson == null)
      archivingGson = createGson(0d, new ExclusionStrategy() {
        @Override
        public boolean shouldSkipField(FieldAttributes f) {
          return "entityId".equals(f.getName()) || (DocumentField.id.toString()
              .equals(f.getName()) &&
              !Document.class.equals(f.getDeclaringClass()) &&
              !AbstractPersitentLongId.class.equals(f.getDeclaringClass())); // DocumentFile
        }

        @Override
        public boolean shouldSkipClass(Class<?> clazz) {
          return false;
        }
      });
    return archivingGson;
  }

  private final static class ArchivedDocument {
    private final Document document;

    private final Path folder;

    private ArchivedDocument(Document document, Path folder) {
      this.folder = folder;
      this.document = document;
    }

    @SuppressWarnings("squid:UnusedPrivateMethod")
    private boolean isValid() {
      return document != null && folder != null;
    }

    @SuppressWarnings("squid:UnusedPrivateMethod")
    private Document getDocument() {
      return document;
    }

    private Path getFolder() {
      return folder;
    }

    @SuppressWarnings("squid:UnusedPrivateMethod")
    private void addChild(Document child) {
      if (document != null)
        document.addChild(child);
    }
  }

  /*
   * DOCUMENTATION
   */

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#findFolderRootByOwnerAndReference(java.lang.String, String) */
  @Override
  @Transactional(readOnly = true)
  public Document findFolderRootByOwnerAndReference(String owner, String reference) {
    return isBlank(owner)
        ? null
        : this.getRepository()
            .findOne((root, query, cb) -> cb.and(
                cb.equal(root.get(Document_.reference), reference),
                cb.equal(root.get(Document_.type), FOLDER.toString()),
                cb.equal(root.get(Document_.owners), owner),
                root.get(Document_.parent)
                    .isNull()))
            .orElse(null);
  }

  /** @see DocumentDAO#findFolderByParentAndName(com.byzaneo.xtrade.bean.Document, java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  public Document findFolderByParentAndName(Document parentFolder, String folderName) {
    return parentFolder == null || isBlank(folderName)
        ? null
        : this.getRepository()
            .findOne((root, query, cb) -> cb.and(
                cb.equal(root.get(Document_.reference), folderName),
                cb.equal(root.get(Document_.type), FOLDER.toString()),
                cb.equal(root.get(Document_.owners), parentFolder.getOwners()),
                cb.equal(root.join(Document_.parent)
                    .get(Document_.id), parentFolder.getId())))
            .orElse(null);
  }

  /** @see com.byzaneo.xtrade.dao.DocumentDAO#detach(java.util.Collection) */
  @Override
  @Transactional(readOnly = true)
  public <D extends Document> List<D> detach(Collection<D> documents) {
    if (isEmpty(documents))
      return emptyList();

    final List<D> r = new ArrayList<>(documents.size());
    for (D document : documents) {
      if (isPersisted(document)) {
        // fetches before detach
        try {
          size(document.getChildren());
          size(document.getFiles());
          // should we manage children's data?
          this.getEntityManager()
              .detach(document);
        }
        catch (Exception e) {
          log.trace("Error detaching document: {} ({})", document, e.getMessage());
        }
      }
      r.add(document);
    }

    return r;
  }

  @SuppressWarnings("unchecked")
  @Override
  @Transactional(readOnly = true)
  public Collection<? extends Serializable> getAssociatedPropertyGroup(Collection<? extends Serializable> ids) {

    if (ids == null || ids.isEmpty()) {
      return emptyList();
    }
    List<? extends Serializable> groupIDs = new ArrayList<>();
    if (ids.size() > LIMIT_IN_STATEMENT_PURGE && "oracle".equals(getDataBaseType())) {
      // Split the collection in chunks of LIMIT_IN_STATEMENT elements
      groupIDs = getAssociatedPropertyGroupIdsPartition(ids);
    }
    else {
      // obtain all groups metaGroup and model
      groupIDs = this.entityManager
          .createQuery("SELECT p.id FROM " + PropertyGroup.class.getSimpleName() + " p JOIN " + Document.class.getSimpleName() +
              " d ON p.id = d.model OR p.id = d.metaGroup where d.id IN :idList")
          .setParameter("idList", ids)
          .getResultList();
    }
    return groupIDs;
  }

  /**
   * This method is called when the {@link Collection} of {@code ids} contains more then 1000 elements (you can't have more then 1000
   * elements in a 'IN' statement {@code ORA-01795}).</br>
   * Returns the Associated Group Properties for given {@code ids}
   *
   * @param ids - {@link Collection} of ids
   * @param entityName - Entity name
   * @param idAttribute - Attribute name
   * @return a {@link List}
   */
  @SuppressWarnings("unchecked")
  private List<? extends Serializable> getAssociatedPropertyGroupIdsPartition(Collection<? extends Serializable> ids) {
    log.info("Splitting in chunks of {} elements", LIMIT_IN_STATEMENT_PURGE);
    List<? extends Serializable> result = new ArrayList<>();
    List<?> idsPartition = ListUtils.partition(ids.stream()
        .collect(toList()), LIMIT_IN_STATEMENT_PURGE);
    StringBuilder query = new StringBuilder();
    query.append("SELECT p.id FROM ");
    query.append(PropertyGroup.class.getSimpleName());
    query.append(" p JOIN ");
    query.append(Document.class.getSimpleName());
    query.append(" d ON p.id = d.model OR p.id = d.metaGroup where d.id IN :idList");
    for (Object partition : idsPartition) {
      result.addAll(this.entityManager
          .createQuery(query.toString())
          .setParameter("idList", partition)
          .getResultList());
    }
    return result;
  }

  @Override
  @Transactional
  public int removePropertyGroups(Collection<? extends Serializable> propertyGrpIds) {
    return removeEntityByIds(propertyGrpIds, PropertyGroup.class.getSimpleName(), "PRGP_ID", "model", "metaGroup");
  }

  @Override
  @Transactional
  public int removePropertiesOfGroups(Collection<? extends Serializable> propertyGrpIds) {
    return removeEntityByIds(propertyGrpIds, Property.class.getSimpleName(), "PROP_GROUP");
  }

  @Override
  @Transactional
  public int removeDocumentFilesOfDocument(Collection<? extends Serializable> documentIds) {
    return removeEntityByIds(documentIds, DocumentFile.class.getSimpleName(), "DOF_DOCUMENT", id.toString());
  }

  @Override
  @Transactional
  public int removeTimelinesOfDocument(Collection<? extends Serializable> documentIds) {
    return removeEntityByIds(documentIds, DocumentTimeline.class.getSimpleName(), "TML_DOC", id.toString());
  }

  @Override
  @Transactional
  public int removeWorkflowOfDocument(Collection<? extends Serializable> documentIds) {
    return removeEntityByIds(documentIds, WorkflowDocumentStatus.class.getSimpleName(), "WKF_DOCUMENT", id.toString());

  }

  @Override
  @Transactional
  public int removeDocuments(Collection<? extends Serializable> documentIds) {
    return removeEntityByIds(documentIds, Document.class.getSimpleName(), id.toString(), id.toString());
  }

  @Override
  @Transactional
  public int removeReconciliationOfDocuments(Collection<? extends Serializable> receptionIds,
      Collection<? extends Serializable> recPreviousInvoiceIds, Collection<? extends Serializable> relationIds) {
    removeEntityByIds(receptionIds, ReconciliationReception.class.getSimpleName(), "REC_ID", id.toString());
    removeEntityByIds(recPreviousInvoiceIds, ReconciliationPreviousInvoice.class.getSimpleName(), "REC_ID", id.toString());
    return removeEntityByIds(relationIds, ReconciliationRelation.class.getSimpleName(), "REC_ID", id.toString());
  }
  /*
   * PRIVATES
   */

  private void appendReportFilterClauses(StringBuilder q, final List<DocumentField> groupFields, final Date start, final Date end,
      final String owner, final List<String> doctypes, final List<DocumentStatus> statusList, boolean excludeStatus,
      final List<String> froms, final List<String> tos, final boolean recipientsNotNull) {

    // WHERE
    q.append(" WHERE ")
        .append(creationDate.getField())
        .append(" between ")
        .append(toSqlDate(start))
        .append(" and ")
        .append(toSqlDate(end));

    if (isNotBlank(owner))
      q.append(" AND ")
          .append(owners.getField())
          .append(" LIKE '%")
          .append(owner)
          .append("%'");

    q.append(appendReportInClause(type.getField(), doctypes));
    q.append(appendReportInClause(from.getField(), froms));
    q.append(appendReportInClause(to.getField(), tos));

    if (recipientsNotNull)
      q.append(" AND ")
          .append(to.getField())
          .append(" is not null")
          .append(" AND ")
          .append(from.getField())
          .append(" is not null");
    if (isNotEmpty(statusList)) {
      StringBuilder qstatus = new StringBuilder(" AND ")
          .append(status.getField())
          .append(excludeStatus ? " not " : "")
          .append(" in (");
      boolean found = false;
      for (int i = 0; i < statusList.size(); i++) {
        DocumentStatus status = statusList.get(i);
        if (status != null) {
          qstatus.append(!found ? "" : ", ")
              .append(status.name());
          found = true;
        }
      }
      qstatus.append(")");
      if (found)
        q.append(qstatus);
    }

    // GROUP BY
    q.append(" GROUP BY ");
    for (int i = 0; i < groupFields.size(); i++)
      q.append(i == 0 ? "" : ", ")
          .append(groupFields.get(i)
              .getField());

    // ORDER BY
    q.append(" ORDER BY ");
    for (int i = 0; i < groupFields.size(); i++)
      q.append(i == 0 ? "" : ", ")
          .append(groupFields.get(i)
              .getField())
          .append(" asc");
  }

  private String appendReportInClause(final String field, final List<String> values) {
    if (isEmpty(values))
      return "";

    final StringBuilder r = new StringBuilder(" AND ").append(field)
        .append(" in (");
    boolean found = false;
    for (int i = 0; i < values.size(); i++) {
      String value = values.get(i);
      if (isNotBlank(value)) {
        r.append(!found ? "" : ", ")
            .append("'")
            .append(value)
            .append("'");
        found = true;
      }
    }
    r.append(")");

    return found ? r.toString() : "";
  }

  private DetachedCriteria createDetachedCriteria(final Class<? extends Document> type,
      final List<String> owners,
      final List<FileType> filetypes, final String doctype,
      final List<String> froms, final List<String> tos,
      final boolean fromsOrTos, final boolean recipientsNotNull,
      final String referenceLike, final Date fromDate, final Date toDate,
      final DocumentStatus... status) {
    final DetachedCriteria dc = DetachedCriteria.forClass(type);

    // Owners
    if (isNotEmpty(owners)) {
      Disjunction cowners = disjunction();
      for (String owner : owners)
        cowners.add(like("owners", owner, MatchMode.ANYWHERE));
      dc.add(cowners);
    }

    // Status
    if (status != null && status.length > 0 && status[0] != null)
      dc.add(in("status", (Object[]) status));

    // Document Type
    if (doctype != null)
      dc.add(ilike("type", doctype, MatchMode.ANYWHERE));

    // Recipients
    if (fromsOrTos && froms != null && tos != null) {
      dc.add(or(
          in("from", froms),
          in("to", tos)));
    }
    else {
      if (froms != null)
        dc.add(in("from", froms));
      if (tos != null)
        dc.add(in("to", tos));
    }
    if (recipientsNotNull) {
      dc.add(isNotNull("from"));
      dc.add(isNotNull("to"));
    }

    if (isNotBlank(referenceLike))
      dc.add(like("reference",
          referenceLike == null ? "" : referenceLike,
          MatchMode.ANYWHERE));

    dc.add(between("creationDate",
        fromDate == null ? new Date(1) : fromDate,
        toDate == null ? new Date() : toDate));

    if (filetypes != null && !filetypes.isEmpty())
      dc.createCriteria("files")
          .add(in("type", filetypes));

    return dc;
  }

  private Specification<Document> createSpecification(final List<String> owners,
      final List<FileType> filetypes,
      final String doctype, final List<String> froms,
      final List<String> tos, final boolean fromsOrTos,
      final boolean recipientsNotNull, final String referenceLike,
      final Date fromDate, final Date toDate, final DocumentStatusEntity... status) {
    return new Specification<Document>() {
      @Override
      public Predicate toPredicate(Root<Document> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        List<Predicate> predicates = new ArrayList<>();
        // Owners
        if (isNotEmpty(owners)) {
          Predicate[] powners = new Predicate[owners.size()];
          for (int i = 0; i < owners.size(); i++) {
            powners[i] = cb.like(root.<String> get("owners"), "%" + owners.get(i) + "%");
          }
          predicates.add(cb.or(powners));
        }

        // Status
        if (status != null && (status.length > 1 || status[0] != null)) {
          In<Object> in = cb.in(root.get("status"));
          Arrays.stream(status)
              .filter(Objects::nonNull)
              .forEach(in::value);
          predicates.add(in);
        }

        // Document Type
        if (doctype != null) {
          predicates.add(cb.like(root.<String> get("type"), doctype));
        }

        // Recipients
        if (fromsOrTos && froms != null && tos != null) {
          predicates.add(cb.or(
              root.get("from")
                  .in(froms),
              root.get("to")
                  .in(tos)));
        }
        else {
          if (froms != null)
            predicates.add(root.get("from")
                .in(froms));
          if (tos != null)
            predicates.add(root.get("to")
                .in(tos));
        }
        if (recipientsNotNull) {
          predicates.add(root.get("from")
              .isNotNull());
          predicates.add(root.get("to")
              .isNotNull());
        }

        if (isNotBlank(referenceLike))
          predicates.add(cb.like(root.<String> get("reference"), "%" + referenceLike + "%"));

        predicates.add(cb.between(root.<Date> get("creationDate"),
            fromDate == null ? new Date(1) : fromDate,
            toDate == null ? new Date() : toDate));

        if (CollectionUtils.isNotEmpty(filetypes)) {
          predicates.add(root.join("files")
              .get("type")
              .in(filetypes));
        }

        return cb.and(predicates.toArray(new Predicate[predicates.size()]));
      }
    };
  }

  /**
   * TODO To replace by date parameter in SQL native query to avoid test on each database types...
   */
  private String toSqlDate(Date date) {
    switch (getDataBaseType()) {
    case "oracle": // oracle
      return format("TO_DATE(''{0}'', ''YYYY-MM-DD HH24:MI'')", asString(date, "yyyy-MM-dd HH:mm"));
    default: // mysql and others...
      return format("''{0}''", asString(date, "yyyy-MM-dd HH:mm"));
    }
  }

  private static String databaseType;

  private static String getDataBaseType() {
    return databaseType == null ? (databaseType = getConfigurationService().getString(DATABASE_TYPE)) : databaseType;
  }

  private TransactionTemplate createTransactionTemplate(int propagation, boolean readonly) {
    final TransactionTemplate txTemplate = new TransactionTemplate(getContext().getBean(PlatformTransactionManager.class));
    txTemplate.setPropagationBehavior(propagation);
    txTemplate.setReadOnly(readonly);
    return txTemplate;
  }

  private boolean populateIndex(final IndexOperations indexOperations, final Document document) {
    try {
      return document != null && indexOperations.populateIndex(document.getIndex());
    }
    catch (Exception e) {
      return false;
    }
  }

  /**
   * Deletes all the entries from 'entityName' table which have the idAttribute in the list of 'ids'
   *
   * @param ids
   * @param entityName
   * @param idAttribute
   * @param documentRelationAttribute optional list containing document attributes that joins document entity with 'entityName'. Used only
   *          for logging the problematic documents in case of an error (ex: Constraint Violation)
   * @return
   */
  private int removeEntityByIds(Collection<? extends Serializable> ids, String entityName, String idAttribute,
      String... documentRelationAttribute) {
    int deleted = 0;
    if (ids == null || ids.isEmpty()) {
      return deleted;
    }
    try {
      if (ids.size() > LIMIT_IN_STATEMENT_PURGE && "oracle".equals(getDataBaseType())) {
        // Split the collection in chunks of LIMIT_IN_STATEMENT elements
        deleted = removeEntityByIdsPartitions(ids, entityName, idAttribute);
      }
      else {
        deleted = this.entityManager.createQuery("DELETE FROM " + entityName + " WHERE " + idAttribute + " IN :idList")
            .setParameter("idList", ids)
            .executeUpdate();
      }
    }
    catch (PersistenceException e) {
      // if a constraint violation occurred try to delete the entities individually to identify the problem
      for (Serializable id : ids) {
        try {
          this.entityManager.createQuery("DELETE FROM " + entityName + " WHERE " + idAttribute + " = :id")
              .setParameter(id.toString(), id)
              .executeUpdate();
        }
        catch (PersistenceException exception) {
          logErrors(entityName, idAttribute, id, documentRelationAttribute);
        }

      }
      throw e;
    }

    return deleted;
  }

  /**
   * This method is called when the {@link Collection} of {@code ids} contains more then 1000 elements (you can't have more then 1000
   * elements in a 'IN' statement {@code ORA-01795}).</br>
   * Deletes all the entries from 'entityName' table which have the idAttribute in the list of 'ids'
   *
   * @param ids - {@link Collection} of ids
   * @param entityName - Entity name
   * @param idAttribute - Attribute name
   * @return number of rows deleted
   * @throws PersistenceException
   */
  private int removeEntityByIdsPartitions(Collection<?> ids, String entityName, String idAttribute)
      throws PersistenceException {
    log.info("Splitting in chunks of {} elements", LIMIT_IN_STATEMENT_PURGE);
    List<?> idsPartition = ListUtils.partition(ids.stream()
        .collect(toList()), LIMIT_IN_STATEMENT_PURGE);
    int deleted = 0;
    StringBuilder query = new StringBuilder();
    query.append("DELETE FROM ");
    query.append(entityName);
    query.append(" WHERE ");
    query.append(idAttribute);
    query.append(" IN :idList");
    for (Object partition : idsPartition) {
      deleted += this.entityManager.createQuery(query.toString())
          .setParameter("idList", partition)
          .executeUpdate();
    }
    return deleted;
  }

  /**
   * Log the error and obtain the documents that are causing the error
   *
   * @param entityName
   * @param idAttribute
   * @param ref
   * @param documentRelationAttribute
   */
  private void logErrors(String entityName, String idAttribute, Serializable ref, String... documentRelationAttribute) {

    CriteriaBuilder builder = entityManager.getCriteriaBuilder();
    CriteriaQuery<Document> cQuery = builder.createQuery(getEntityClass());
    Root<Document> root = cQuery.from(getEntityClass());

    Predicate[] predicates = Arrays.stream(documentRelationAttribute)/* ex: model.id=:id OR metaGroup.id =:id */
        .map(attr -> builder.equal(id.toString()
            .equals(attr) ? root.get(attr)
                : root.get(attr)
                    .get(id.toString()),
            ref))/* ex: id=:id */
        .toArray(x -> new Predicate[documentRelationAttribute.length]);

    List<Document> docs = entityManager.createQuery(cQuery.select(root)
        .where(builder.or(predicates)))
        .getResultList();
    log.error("Cannot delete {} with {}({}) related to the following documents:", entityName, idAttribute, ref);
    for (Document document : docs) {
      log.error("   * " + document.toString());
    }
  }

  @Override
  @SuppressWarnings("unchecked")
  public List<Document> findByIndexType(Class<? extends Indexable> c, int begin, int size) {
    TypedQuery<Document> query = (TypedQuery<Document>) entityManager
        .createQuery("FROM Document doc where doc.index.type = :indexType ORDER BY doc.index.reference")
        .setParameter("indexType", c);

    return query.setFirstResult(begin)
        .setMaxResults(size)
        .getResultList();
  }



  @Override
  public List<Document> searchDocuments(Query query, Pageable pageable) {

    if (DatabaseHelper.isOracle(databaseType))
      this.entityManager.createNativeQuery("call DBMS_SESSION.SET_NLS('nls_numeric_characters', '''. ''')")
          .executeUpdate();

    final TypedQuery<Document> q = getQuery(new QuerySpecification<Document>(query), Document.class,
        pageable == null ? null : pageable.getSort(), query.getLimit());
    if (pageable == null) {
      if (query.getLimit() != null && query.getLimit()
          .getLimitNumber() != -1) {
        q.setMaxResults(query.getLimit()
            .getLimitNumber());
      }
      return q.getResultList();
    }
    q.setFirstResult((int) pageable.getOffset());
    q.setMaxResults(ofNullable(query.getLimit())
        .map(Limit::getLimitNumber)
        .filter(limit -> limit != -1 && limit < pageable.getPageSize())
        .orElse(pageable.getPageSize()));

    return q.getResultList();

  }

  @Override
  public List<Document> searchChildren(List<Document> parentList, Query documentQuery) {
    return this.getRepository()
        .findAll(
            (root, query, cb) -> {

              In<Object> in = cb.in(root.get(Document_.parent));
              parentList.stream()
                  .map(Document::getId)
                  .forEach(code -> in.value(code));

              root.fetch(Document_.files, JoinType.LEFT);
              Predicate predicate = cb.and(
                  cb.and(in));
              if (documentQuery == null || documentQuery.getWhereClause() == null || CollectionUtils.isEmpty(documentQuery.getWhereClause()
                  .getClauses())) {
                return predicate;
              }
              return cb.and(predicate, cb.or(QueryHelper.toJpaPredicate(documentQuery, root, cb)));
            });
  }

  @Override
  public void removeDocumentsFromSession(List<Long> docIdsToRemove) {
    final Session session = getSession();
    for (Long docIdToRemove : docIdsToRemove) {
      if (docIdToRemove != null) {
        Document documentToRemove = session.get(Document.class, docIdToRemove);
        if (documentToRemove != null) {
          session.evict(documentToRemove);
        }
      }
    }
  }

  private  javax.persistence.criteria.Path<?> resolveNestedPath( javax.persistence.criteria.Path<?> root, String[] fieldPath) {
    javax.persistence.criteria.Path<?> path = root.get(fieldPath[0]);
    for (int i = 1; i < fieldPath.length; i++) {
      path = path.get(fieldPath[i]);
    }
    return path;
  }


  public Map<String, Long> countDocumentsGroupedByField(String[] fieldNames, Query bqlQuery) {
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();
    CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
    Root<Document> root = query.from(Document.class);

    Predicate whereClause = QueryHelper.toJpaPredicate(bqlQuery ,root ,cb) ;
    javax.persistence.criteria.Path<?> path = resolveNestedPath(root, fieldNames);


    query.multiselect(
                    path,
                    cb.count(root)
            )
            .where(whereClause)
            .groupBy(path);

    List<Object[]> results = entityManager.createQuery(query).getResultList();

    Map<String, Long> resultMap = new HashMap<>();
    for (Object[] row : results) {
      resultMap.put(String.valueOf(row[0]), ((Number) row[1]).longValue());
    }

    return resultMap;
  }

  @Override
  public List<Object[]> getDocumentChangedStatus(
      String action,
      Date start, Date end,
      Document.ProcessingWay processingWay,
      List<String> selfBillingList,
      List<String> partnersList,
      int limit,
      int offset
  ) {

    StringBuilder jpql = new StringBuilder(
        "SELECT " +
            "CASE " +
            "WHEN doc.useCase IN :selfBillingList THEN " +
            "CASE WHEN doc.processingWay = 0 THEN doc.to ELSE doc.from END " +
            "ELSE " +
            "CASE WHEN doc.processingWay = 0 THEN doc.from ELSE doc.to END " +
            "END AS partnerId, " +
            "doc.uuid, doc.reference, doctml.action, doctml.comment, doctml.date " +
            "FROM Document doc " +
            "JOIN DocumentTimeline doctml ON doc.id = doctml.document " +
            "WHERE doctml.action LIKE :action " +
            "AND doc.processingWay = :processingWay " +
            "AND (" +
            "CASE " +
            "WHEN doc.useCase IN :selfBillingList THEN " +
            "CASE WHEN doc.processingWay = 0 THEN doc.to ELSE doc.from END " +
            "ELSE " +
            "CASE WHEN doc.processingWay = 0 THEN doc.from ELSE doc.to END " +
            "END" +
            ") IN :partnersList "
    );

    if (start != null && end != null) {
      jpql.append("AND doctml.date BETWEEN :start AND :end ");
    }
    else if (start != null) {
      jpql.append("AND doctml.date >= :start ");
      jpql.append("AND doc.modificationDate >= :start ");
    }
    else if (end != null) {
      jpql.append("AND doctml.date <= :end ");
    }

    TypedQuery<Object[]> query = entityManager.createQuery(jpql.toString(), Object[].class);
    query.setParameter("action", "%" + action + "%");
    query.setParameter("selfBillingList", selfBillingList);
    query.setParameter("partnersList", partnersList);
    query.setParameter("processingWay", processingWay);

    if (start != null) {
      query.setParameter("start", start);
    }
    if (end != null) {
      query.setParameter("end", end);
    }

    if (limit != 0) {
      query.setMaxResults(limit);
      query.setFirstResult(offset);
    }
    return query.getResultList();
  }

}
