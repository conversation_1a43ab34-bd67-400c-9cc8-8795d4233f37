package com.byzaneo.xtrade.util;

import static com.byzaneo.commons.bean.FileType.REPORT;
import static com.byzaneo.commons.util.JAXBHelper.marshal;
import static com.byzaneo.commons.util.JAXBHelper.unmarshal;
import static com.byzaneo.commons.util.Log4JHelper.copyLogFileAndDelete;
import static com.byzaneo.commons.util.Log4JHelper.deleteFile;
import static com.byzaneo.commons.util.SpringContextHelper.configuration;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.commons.util.TemplateHelper.processHtml;
import static com.byzaneo.commons.util.TemplateHelper.processText;
import static com.byzaneo.xtrade.api.DocumentStatus.ERROR;
import static com.byzaneo.xtrade.api.DocumentStatus.FATAL;
import static com.byzaneo.xtrade.api.DocumentStatus.REFUSED;
import static com.byzaneo.xtrade.api.DocumentStatus.TO_CORRECT;
import static com.byzaneo.xtrade.bean.Document.Metadata.COUNT;
import static com.byzaneo.xtrade.bean.Document.Metadata.VERSION;
import static com.byzaneo.xtrade.bean.Report.Metadata.DOCUMENT_PROCESSED_NUMBER;
import static com.byzaneo.xtrade.bean.Report.Metadata.DURATION;
import static com.byzaneo.xtrade.bean.Report.Metadata.PROCESS_NAME;
import static com.byzaneo.xtrade.bean.Report.Metadata.STATUS;
import static com.byzaneo.xtrade.process.Variable.*;
import static com.byzaneo.xtrade.process.VariableHelper.*;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Locale.FRENCH;
import static java.util.Optional.ofNullable;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.beans.BeanUtils.copyProperties;

import java.beans.PropertyDescriptor;
import java.io.*;
import java.net.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.activiti.engine.delegate.DelegateExecution;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.service.*;
import com.byzaneo.xtrade.*;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.Report;
import com.byzaneo.xtrade.api.ReportDocument;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.DocumentFile;
import com.byzaneo.xtrade.bean.ReportDocumentFile;
import com.byzaneo.xtrade.process.VariableHelper;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Jun 19, 2014
 * @since 7.0 XTD-412
 */
public class ReportHelper {
  private static final Logger log = getLogger(ReportHelper.class);

  public static final List<String> DOCUMENT_KO_STATUSES = asList(ERROR.name(), REFUSED.name(), TO_CORRECT.name());

  public static final List<String> REPORT_KO_STATUSES = asList(ERROR.name(), FATAL.name());

  public static final String REPORT_FILE_NAME = "report.html";

  /**
   * private constructor to hide the implicit public one
   */
  private ReportHelper() {
  }

  /**
   * @param report used to create the {@link Document}
   * @param HTML/XML report file
   * @param metadata adds document's metadata for backward compatibility or if indexing is disabled
   * @return the {@link Document} created from the given report and its HTML/XML file representation.
   */
  public static final Document toReportDocument(final Report report, final File reportFile, boolean metadata) {
    if (report == null)
      return null;

    // Creates document
    final Document doc = new Document(
        report.getReference(),
        report.getOwners(),
        report.getType(),
        report.getFrom(),
        report.getTo(),
        report.getStatus());
    doc.setCreationDate(report.getCreationDate());

    // MetaData
    // backward compatibility or if indexing is disabled
    if (metadata) {
      doc.addMetadata(PROCESS_NAME, report.getProcessDefinitionName());
      doc.addMetadata(STATUS, report.getState());
      doc.addMetadata(DURATION, report.getFormattedDuration());
      doc.addMetadata(DOCUMENT_PROCESSED_NUMBER, report.getDocumentCount() + report.getDeadCount());
      doc.addMetadata(VERSION, report.getProcessDefinitionVersion());
      doc.addMetadata(COUNT, report.getDocumentCount());
    }

    // Index
    doc.setIndexValue(report);

    // document file
    if (reportFile != null)
      doc.addFile(new DocumentFile(reportFile, REPORT, report.getProcessDefinitionId(),
          report.getProcessInstanceId(), doc, null));

    return doc;
  }

  /**
   * @param reportDocuments
   * @return the resolved report list from the given report documents.
   * @see #resolveReport(Document)
   */
  public static final <R extends Report> List<R> resolveReports(final Collection<Document> reportDocuments) {
    if (isEmpty(reportDocuments))
      return emptyList();
    final List<R> r = new ArrayList<>(reportDocuments.size());
    for (Document doc : reportDocuments) {
      R report = resolveReport(doc);
      if (report != null)
        r.add(report);
    }
    return r;
  }

  /**
   * @param reportDocument
   * @return the report resolved from the given document's index value and/or its XML report file.
   * @see #merge(Report, Report)
   */
  public static final <R extends Report> R resolveReport(final Document reportDocument) {
    if (reportDocument == null)
      return null;

    // index report
    return reportDocument.getIndexValue();
  }

  /**
   * @param reportDocument
   * @return the Report from its XML representation
   */
  public static final <R extends Report> R getXmlReport(Document reportDocument) {
    return fromXml(getXmlReportFile(reportDocument));
  }

  /**
   * @param reportDocument
   * @return the first document file with REPORT type (existence check is not perform)
   */
  public static final File getXmlReportFile(Document reportDocument) {
    if (reportDocument == null || reportDocument.getFiles() == null)
      return null;
    for (DocumentFile dof : reportDocument.getFiles())
      if (dof != null && REPORT.equals(dof.getType()))
        return dof.getFile();
    return null;
  }

  /**
   * @param source
   * @param target
   * @return the target populated with the non <code>null</code> source's properties
   */
  public static final <R extends Report> R merge(final R source, final R target) {
    if (source == null)
      return target;
    if (target == null)
      return null;

    // search source's null properties
    final BeanWrapper src = new BeanWrapperImpl(source);
    final PropertyDescriptor[] pds = src.getPropertyDescriptors();
    final Set<String> emptyNames = new HashSet<>();
    for (java.beans.PropertyDescriptor pd : pds) {
      Object srcValue = src.getPropertyValue(pd.getName());
      if (srcValue == null) emptyNames.add(pd.getName());
    }

    // copies non null properties
    copyProperties(source, target,
        emptyNames.toArray(new String[emptyNames.size()]));

    return target;
  }

  public static boolean isKO(final Report report) {
    return report == null || report.getStatus() == null || REPORT_KO_STATUSES.contains(report.getStatusAsString()) ||
        report.getDocuments()
            .stream()
            .map(ReportDocument::getStatus)
            .anyMatch(DOCUMENT_KO_STATUSES::contains);
  }

  public static boolean isOK(final Report report) {
    return !isKO(report);
  }

  /* -- XML CONVERSION -- */

  public static final File toXml(final Report report, File outFile) throws Exception {
    if (report == null || outFile == null)
      return null;
    marshal(report, outFile, "^message", "^throwable");
    return outFile;
  }

  @SuppressWarnings("unchecked")
  public static final <R extends Report> R fromXml(File xmlFile) {
    if (xmlFile == null || !xmlFile.isFile())
      return null;
    try {
      return (R) unmarshal(com.byzaneo.xtrade.bean.Report.class, xmlFile);
    }
    catch (Exception e) {
      log.error("Error unmarshalling report file: {} ({})", xmlFile, e.getMessage());
      return null;
    }
  }

  /* -- HTML FILTER -- */

  public static String filterReportContentInHtml(String reportContent, List<String> levelList) {
    return filterReportContent(reportContent, levelList).outerHtml();
  }

  public static List<String> filterReportContentInTextList(String reportContent, List<String> levelList) {
    return filterReportContent(reportContent, levelList).select("td[title='Message'], td[bgcolor='#993300']")
        .stream()
        .map(Element::text)
        .collect(Collectors.toList());
  }

  private static org.jsoup.nodes.Document filterReportContent(String reportContent, List<String> levelList) {
    org.jsoup.nodes.Document htmlReport = Jsoup.parse(reportContent);

    Elements trElements = htmlReport.select("tr");

    for (Element trElement : trElements) {
      Elements lvlLines = trElement.select("td[title='Level']");
      for (Element lvlLine : lvlLines) {
        if (!levelList.contains(lvlLine.text())) {
          removeStackTraceError(trElement, lvlLine);
          lvlLine.parent()
              .remove();
        }
      }
    }
    return htmlReport;
  }

  public static void modifyReportHTML(String path, String ex) {
    // -- Add a new error log to the report when the report size is greater than 16 megabytes --
    File input = new File(path);
    org.jsoup.nodes.Document doc;

    try {
      doc = Jsoup.parse(input, "UTF-8", "");
      doc.select("table")
          .first()
          .append("<tr >\n" +
              "<td style=\"padding-left: 5px;\">" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "</td>\n" +
              "<td title=\"Level\" style=\"padding-left: 5px; padding-right: 5px;\"><font class=\"uppercase\" color=\"#ffb752\">WARNING</font></td>\n" +
              "<td title=\"Message\" style=\"padding-left: 5px;\">The report cannot be stored entirely because of its size, removing documents details.</td>\n" +
              "</tr>");

      PrintWriter writer = new PrintWriter(input, "UTF-8");
      writer.write(doc.html());
      writer.flush();
      writer.close();
    }
    catch (Exception e) {
      log.error("Failed to modify report.html", e);
    }
  }

  // Remove stackTrace of error if we select other status than error
  private static void removeStackTraceError(Element trElement, Element lvlLine) {
    if (Level.ERROR.toString()
        .equals(lvlLine.text())) {
      Element nextTrElement = trElement.nextElementSibling();
      if (nextTrElement != null && nextTrElement.select("td[title='Level']")
          .text()
          .isEmpty())
        nextTrElement.remove();
    }
  }

  /* -- INDEXER -- */

  /**
   * Report indexer
   */
  @Component(ReportIndexer.INDEXER_NAME)
  public static final class ReportIndexer implements Indexer<com.byzaneo.xtrade.bean.Report> {
    public static final String INDEXER_NAME = "reportIndexer";

    @Autowired
    private BeanService beanService;

    private static BeanDescriptor descriptor;

    /** @see com.byzaneo.xtrade.api.Indexer#getName() */
    @Override
    public String getName() {
      return INDEXER_NAME;
    }

    /** @see com.byzaneo.xtrade.api.Indexer#getType() */
    @Override
    public Class<com.byzaneo.xtrade.bean.Report> getType() {
      return com.byzaneo.xtrade.bean.Report.class;
    }

    /** @see com.byzaneo.xtrade.api.Indexer#manages(java.lang.Class) */
    @Override
    public boolean manages(Class<? extends Indexable> indexableType) {
      return indexableType != null && com.byzaneo.xtrade.bean.Report.class.isAssignableFrom(indexableType);
    }

    /** @see com.byzaneo.xtrade.api.Indexer#accepts(com.byzaneo.xtrade.api.Document) */
    @Override
    public boolean accepts(com.byzaneo.xtrade.api.Document document) {
      // Just checks for the document's type
      // A report document could not get XML file but only
      // an index
      return document != null &&
          document instanceof Document &&
          REPORT.toString()
              .equals(document.getType());
    }

    /** @see com.byzaneo.xtrade.api.Indexer#validates(com.byzaneo.xtrade.api.Document) */
    @Override
    public void validates(com.byzaneo.xtrade.api.Document document) throws IndexingException {
      if (document == null)
        throw new IndexingException("Document is required");
      com.byzaneo.xtrade.bean.Document doc;
      // Document Entity?
      if (document instanceof com.byzaneo.xtrade.bean.Document)
        doc = (com.byzaneo.xtrade.bean.Document) document;
      else
        throw new IndexingException("Document is not an entity");
      // is REPORT type?
      if (!REPORT.toString()
          .equals(doc.getType()))
        throw new IndexingException("Document's type is not 'REPORT'");
      // REPORT file
      if (getXmlReportFile(doc) == null)
        throw new IndexingException("Missing REPORT document's file");
    }

    /** @see com.byzaneo.xtrade.api.Indexer#indexing(com.byzaneo.xtrade.api.Document) */
    @Override
    public void indexing(com.byzaneo.xtrade.api.Document document) throws IndexingException {
      if (!accepts(document))
        throw new IndexingNotSupportedException("%s does not accept: %s ", this.getName(), document);

      final Document doc = (Document) document;

      // for now, we are not re-indexing document
      // with an associated index (purged and process
      // runtime data could be lost)
      if (doc.isIndexed()) {
        if (doc.getIndexValue() != null)
          // no indexing needed
          throw new IndexingNotAllowedException("Report re-indexation is not allowed for document %s", doc.getId());
        else
          throw new IndexingException("Report Document %s is already indexed but not populated with its value", doc.getId());
      }

      // creates Report from the XML file
      final com.byzaneo.xtrade.bean.Report report = getXmlReport(doc);
      if (report == null)
        throw new IndexingException("Report index creation failed for document %s with file: %s",
            doc.getId(), getXmlReportFile(doc));

      // updates report index specific properties
      // is it necessary?
      report.getDeadCount();
      report.setDocuments(report.getDocuments());

      // sets new document's report index
      doc.setIndexValue(report);
    }

    /** @see com.byzaneo.xtrade.api.Indexer#getDescriptor() */
    @Override
    public BeanDescriptor getDescriptor() {
      if (descriptor == null) try {
        descriptor = beanService.fromClasspath("descriptors/report-indexer.xml"); // NOSONAR : The method can't be static
      }
      catch (IOException ex) {
        log.error(ex.getLocalizedMessage(), ex);
      }
      return descriptor;
    }

    @Override
    public BeanDescriptor getDescriptor(IndexType type) {
      throw new UnsupportedOperationException("Not supported yet !!!");
    }

    @Override
    public String getDescriptorPath(IndexType type) {
      throw new UnsupportedOperationException("Not supported yet !!!");
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T extends Indexable> Class<T> getIndexType() {
      return (Class<T>) com.byzaneo.xtrade.bean.Report.class;
    }
  }

  /* -- RESTITUTION -- */

  public static final String toHtml(final Report report) {
    StringWriter writer = new StringWriter();
    toHtml(report, writer, false, null);
    return writer.toString();
  }

  public static final void toHtml(final Report report, final Writer writer, boolean embedded, final Map<String, Object> model) {
    processHtml("Report".concat(embedded ? "Body" : ""),
        createTemplateContext(report, model),
        writer);
  }

  public static final String toText(final Report report) {
    StringWriter writer = new StringWriter();
    toText(report, writer, null);
    return writer.toString();
  }

  public static final void toText(final Report report, final Writer writer, final Map<String, Object> model) {
    processText("Report",
        createTemplateContext(report, model),
        writer);
  }

  /*
   * -- P R I V A T E --
   */

  private static Context createTemplateContext(final Report report, final Map<String, Object> model) {
    if (report == null)
      return null;
    final LinkedHashMap<String, Object> vars = new LinkedHashMap<>(4);
    vars.put("imagesUrl", configuration().map(ConfigurationService::getFullContextUrl)
        .map(x -> x.concat("/images/xtrade"))
        .orElseThrow(() -> new ServiceException("Configuration Service is not upp")));
    vars.put("baseUrl", getConfigurationService() == null
        ? "localhost:8080/xtrade"
        : getConfigurationService().getFullContextUrl());
    vars.put("documentViewerUrl", "/ui/document.jsf?did=");
    vars.put("showDocumentsUrls", false);
    if (model != null)
      vars.putAll(model);
    vars.put("report", report);
    return new Context(FRENCH, vars);
  }

  public static boolean copyReportLogFileAndDelete(Report report) {
    Optional<Report> optionalReport = ofNullable(report);
    return optionalReport.map(Report::getLogs)
        .map(com.byzaneo.commons.bean.Logs::getLogFilePath)
        .filter(filePath -> new File(filePath).exists())
        .map(filePath -> copyLogFileAndDelete(filePath, optionalReport.get()
            .getLogsFilePath()))
        .orElse(false);
  }

  public static final List<File> getFilesFromReportDocument(final Collection<com.byzaneo.xtrade.api.ReportDocument> docs,
      final boolean exists) {
    if (isEmpty(docs))
      return emptyList();

    List<File> files = new ArrayList<File>();
    for (com.byzaneo.xtrade.api.ReportDocument doc : docs) {
      if (doc != null && doc.getFiles() != null && !doc.getFiles()
          .isEmpty()) {
        for (ReportDocumentFile docFile : doc.getFiles()) {
          try {
            File f = new File(new URI(docFile.getUri()));
            docFile.setFile(f);
            files.add(f);
          }
          catch (URISyntaxException e) {
            e.printStackTrace();
          }

        }
      }
    }
    return files;
  }

  public static final List<File> getFilesFromReportDocumentFiles(final boolean exists, final Collection<ReportDocumentFile> dofs) {
    if (isEmpty(dofs))
      return emptyList();
    final List<File> files = new ArrayList<>(dofs.size());
    for (ReportDocumentFile dof : dofs) {
      try {
        if (dof != null && StringUtils.isNotBlank(dof.getUri())) {
          dof.setFile(new File(new URI(dof.getUri())));
          if (!exists || dof.getFile()
              .isFile()) {
            files.add(dof.getFile());
          }
        }
      }
      catch (URISyntaxException e) {
        e.printStackTrace();
      }
    }
    return files;
  }

  public static Map<String, String> extractVarsBasedOnObjectType(Map<String, Object> vars) {
    Map<String, String> executionVars = new HashMap<String, String>();
    if (vars == null) {
      return executionVars;
    }
    for (Entry<String, Object> entry : vars.entrySet()) {
      // ignore null values
      if (entry.getValue() == null)
        continue;
      if (entry.getKey()
          .equals("instance_host"))
        continue;
      if (entry.getValue()
          .getClass()
          .equals(File.class)) {
        entry.setValue(((File) entry.getValue()).getAbsolutePath());
      }
      else if (entry.getValue()
          .getClass()
          .equals(org.apache.logging.log4j.Level.class)) {
            entry.setValue(((org.apache.logging.log4j.Level) entry.getValue()).name());
          }
      // TODO: Fix when managing the job sourcing in the variables
      // else if (entry.getKey()
      // .equals("job_source")) {
      // entry.setValue(entry.getValue()
      // .getClass()
      // .getSimpleName());
      // }
      else if (entry.getKey()
          .equals("loggers")) {
            List<String> list = new ArrayList<String>();
            for (Object obj : (Object[]) entry.getValue()) {
              list.add(obj.toString());
            }
            entry.setValue(list);
          }
      executionVars.put(entry.getKey(), entry.getValue()
          .toString());
    }
    return executionVars;
  }

  public static String formattedElapsedTime(long elapsedTimeInMillis) {
    long hours = TimeUnit.MILLISECONDS.toHours(elapsedTimeInMillis);
    long minutes = TimeUnit.MILLISECONDS.toMinutes(elapsedTimeInMillis) -
        TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(elapsedTimeInMillis));
    long seconds = TimeUnit.MILLISECONDS.toSeconds(elapsedTimeInMillis) -
        TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(elapsedTimeInMillis));
    return hours == 0 ? (minutes == 0 ? seconds + "s" : minutes + "m:" + seconds + "s") : hours + "h:" + minutes + "m:" + seconds + "s";
  }

  public static void updateVariablesOfReport(boolean reportVars, Map<String, Object> variables, DelegateExecution execution,
      Report report) {

    if (reportVars && variables != null) {
      // removes dead documents and processed
      // documents as their part of the report
      final Map<String, Object> vars = new TreeMap<>(variables);
      vars.remove(DOCUMENTS.toString());
      vars.remove(INDEXABLES.toString());
      vars.remove(DEADS.toString());
      vars.remove(DOCUMENTS_BACKUP.toString());
      vars.remove(com.byzaneo.xtrade.process.Variable.REPORT.toString());
      vars.remove(FATAL_ERROR.toString());
      // remove documents in partition collections
      VariableHelper.getVariable(execution, PARTITION_DOC_COLLECTIONS, new LinkedHashSet<String>())
          .stream()
          .forEach(n -> vars.remove(n));
      vars.remove(PARTITION_DOC_COLLECTIONS.toString());
      Map<String, String> executionVars = new HashMap<String, String>();
      for (Entry<String, String> entry : extractVarsBasedOnObjectType(vars).entrySet()) {
        String newKey = entry.getKey()
            .replaceAll("\\.", "_");
        executionVars.put(newKey, entry.getValue());
      }
      report.setVariables(executionVars);
    }
  }

  public static void copyTempFileLogIntoFinalLog(Report report, DelegateExecution execution, ReportLogsWrapper reportLogsWrapper) {
    final File workDir = getVariable(execution, PROCESS_WORK_DIR);
    File reportFile = new File(workDir, REPORT_FILE_NAME);
    try {
      if (Thread.interrupted()) {
        // this call will just mark the thread interrupted flag to false
        // so we won't have any ClosedByInterruptException on FileUtils.copyFile
      }
      if (reportLogsWrapper != null && !reportLogsWrapper.getPathes()
          .isEmpty()) {
        FileUtils.copyFile(new File(reportLogsWrapper.getPathes()
            .get(0)), reportFile);
        report.setLogsFilePath(reportFile.getAbsolutePath());
      }
    }
    catch (IOException e) {
      throw new ReportException("Error copying temporary log file", e);
    }
    report.setTempLogFilePath(null);
  }

  public static void deleteTempLogFile(ReportLogsWrapper reportLogsWrapper) {
    if (reportLogsWrapper == null || reportLogsWrapper.getPathes()
        .isEmpty()) {
      log.error("Cannot delete the temporary log file");
      return;
    }
    for (String logFilePath : reportLogsWrapper.getPathes()) {
      deleteFile(logFilePath);
    }
  }

  public static void setReportStatus(Document reportDocument, ReportLogsWrapper reportLogsWrapper) {
    SortedSet<Level> levels = reportLogsWrapper.getLevel();
    if (levels.isEmpty() || reportDocument == null) // LOG
      return;
    switch (levels.first()
        .getStandardLevel()) {
    case FATAL:
      reportDocument.setStatusWithEnumValue(DocumentStatus.FATAL);
      break;
    case ERROR:
      reportDocument.setStatusWithEnumValue(DocumentStatus.ERROR);
      break;
    case WARN:
      reportDocument.setStatusWithEnumValue(DocumentStatus.WARNING);
      break;
    case INFO:
      reportDocument.setStatusWithEnumValue(DocumentStatus.APPROVED);
      break;
    default:
      break;
    }
  }

  public static boolean checkIfExistsInOriginalProcess(DelegateExecution execution, String fileName) {
    String inputBackupDir = (String) execution.getVariable(ORIGINAL_PROCESS_INPUT_BACKUP_DIR.toString());
    if (inputBackupDir == null || fileName == null)
      return false;
    return new File(inputBackupDir, fileName).exists();
  }

  public static void displayNonPersistentPropertiesDependingOnLogLevel(Map<String, String> variables) {
    if(!variables.containsKey(NON_PERSISTENT_DOC_PROPERTIES.toString()))
      return;
    if (Level.DEBUG.toString().equals(variables.get(LOG_LEVEL.toString()))) {
      variables.put(NON_PERSISTENT_DOC_PROPERTIES.toString(),
          ReportHelper.limitPropertiesEntries(variables.get(NON_PERSISTENT_DOC_PROPERTIES.toString()), 100));
    }
    else {
      variables.remove(NON_PERSISTENT_DOC_PROPERTIES.toString());
    }
  }

  public static String limitPropertiesEntries(String input, int limit) {
    if (input == null || input.length() < 2 || input.charAt(0) != '{' || input.charAt(input.length() - 1) != '}') {
      return "";
    }
    String[] parts = input.substring(1, input.length() - 1).split("}, ");
    int count = Math.min(limit, parts.length);

    StringBuilder sb = new StringBuilder(input.length());
    sb.append('{');
    for (int i = 0; i < count; i++) {
      sb.append(parts[i]);
      if (!parts[i].endsWith("}")) {
        sb.append('}');
      }
      if (i < count - 1) {
        sb.append(", ");
      }
    }
    if (parts.length > limit) {
      sb.append(", ...");
    }
    sb.append('}');
    return sb.toString();
  }

}
