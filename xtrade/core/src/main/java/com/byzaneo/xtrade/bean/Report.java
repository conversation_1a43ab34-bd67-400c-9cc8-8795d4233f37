package com.byzaneo.xtrade.bean;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.util.JAXBHelper.MapAdapter;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.api.DefinitionDescriptor;
import com.byzaneo.xtrade.bean.Document.*;
import com.byzaneo.xtrade.util.*;
import com.google.common.io.Files;
import lombok.*;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.*;
import java.util.*;
import java.util.Map.Entry;

import static com.byzaneo.commons.bean.Logs.Status.OK;
import static com.byzaneo.commons.util.FormatHelper.toFormattedDuration;
import static com.byzaneo.commons.util.Log4JHelper.splitLogs;
import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static com.byzaneo.xtrade.util.ReportHelper.displayNonPersistentPropertiesDependingOnLogLevel;
import static java.lang.String.format;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Collections.emptyList;
import static java.util.Collections.unmodifiableList;
import static java.util.Optional.ofNullable;
import static javax.xml.bind.annotation.XmlAccessType.NONE;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * {@link Report} implementation.
 * <p/>
 * This report is stored as {@link IndexableDocument}.
 * <p/>
 *
 * <AUTHOR> Fung <<EMAIL>>
 * @company Byzaneo
 * @date 04/06/2014
 * @since 1.0
 */
@XmlRootElement(name = "report")
@XmlType(name = "report", namespace = "xtd")
@XmlAccessorType(NONE)
@org.springframework.data.mongodb.core.mapping.Document(collection = Report.COLLECTION_NAME)
public class Report extends IndexableDocument implements com.byzaneo.xtrade.api.Report {
  private static final long serialVersionUID = 1371396267719073159L;

  private static final Logger log = getLogger(Report.class);

  public static final String COLLECTION_NAME = "reports";

  public static final List<String> UNAUTHORIZED_FOR_DISPLAY_VARIABLES = Arrays.asList("cookie", "authorization");

  private String logAppenderId = null;

  private boolean saved = false;

  private String logsFilePath;

  private String tempLogFilePath;

  private String xmlFilePath;

  private transient String content;

  public enum Metadata implements MetaEnum {
    PROCESS_NAME,
    STATUS,
    DOCUMENT_PROCESSED_NUMBER,
    DURATION;

    @Override
    public <T> T defaultValue() {
      return null;
    }

    @Override
    public String format() {
      return null;
    }

    @Override
    public PropertyType type() {
      return null;
    }
  }

  public enum OpsStatus {
    CANCELED,
    CLOSED,
    IN_PROGRESS,
  }

  // GENERAL

  @Field(FIELD_DURATION)
  @XmlAttribute(name = "duration")
  protected long duration;

  @Field(FIELD_REPROCESS_DATE)
  @XmlAttribute(name = "reprocessed")
  protected Date reprocessDate;



  // PROCESS

  @Indexed
  @Field(FIELD_PROCESS_DEFINITION_ID)
  @XmlAttribute(name = "definitionId")
  protected String processDefinitionId;

  @Field(FIELD_PROCESS_DEFINITION_KEY)
  @XmlAttribute(name = "definitionKey")
  protected String processDefinitionKey;

  @Field(FIELD_PROCESS_DEFINITION_NAME)
  @XmlAttribute(name = "definitionName")
  protected String processDefinitionName;

  @Field(FIELD_PROCESS_DEFINITION_VERSION)
  @XmlAttribute(name = "definitionVersion")
  protected String processDefinitionVersion;

  @Transient
  protected transient ProcessDefinition processDefinition;

  @Transient
  protected transient DefinitionDescriptor processDefinitionDescriptor;

  @Field(FIELD_PROCESS_INSTANCE_ID)
  @XmlAttribute(name = "process")
  protected String processInstanceId;

  @Indexed
  @Field(FIELD_JOB_LINKR)
  @XmlAttribute(name = "link")
  private String link;

  @Transient
  protected transient ProcessInstance processInstance;

  // TRANSACTION

  @Field(FIELD_TRANSACTION_STATUS)
  @XmlAttribute(name = "transactionStatus")
  protected String transactionStatus = "OK";

  // LOGS

  @Transient
  @XmlElement(name = "logs")
  protected Logs logs;

  // DOCUMENTS

  // documents
  protected List<ReportDocument> documents;

  @XmlTransient
  @Field(FIELD_DOCUMENT_COUNT)
  protected int documentCount = -1;

  // documents -- xml
  @Transient
  @XmlElement(name = "document")
  protected List<Document> xmlDocuments;

  // child documents
  protected List<com.byzaneo.xtrade.bean.ReportDocument> childDocuments;

  @XmlTransient
  @Field(FIELD_CHILD_DOCUMENT_COUNT)
  protected int childDocumentsCount = -1;

  // child documents -- xml
  @Transient
  @XmlElement(name = "childDocument")
  protected List<Document> xmlChildDocuments;

  // deads
  @Field("deads")
  protected List<ReportDocument> deads = null;

  // deads -- xml
  @Transient
  @XmlElement(name = "dead")
  protected List<Document> xmlDeads = null;

  @Transient
  @XmlElement(name = "variables")
  @XmlJavaTypeAdapter(value = MapAdapter.class)
  protected Map<String, Object> xmlVariables;

  @XmlTransient
  @Field("executionVars")
  protected Map<String, String> variables;

  @XmlTransient
  @Field(FIELD_DEAD_COUNT)
  protected int deadCount = -1;

  // deads -- old
  @XmlTransient
  @Field(FIELD_DEAD_DOCUMENTS)
  protected List<DeadReportDocument> deadReportDocuments;

  // indexed
  @XmlTransient
  @Field(FIELD_DOCUMENTS)
  protected List<ReportDocument> reportDocuments;

  @Field(FIELD_PROCESS_DEAD_QUEUE)
  protected String processDeadQueue;

  @Field(FIELD_PROCESS_INPUT_BACKUP_DIR)
  protected String processInputBackupDir;

  @Field(FIELD_PROCESS_ATTACH_BACKUP_DIR)
  protected String processAttachBackupDir;

  @Field(PROCESS_BACKUP_ID)
  @XmlTransient
  protected String processBackupId;
  
  @Field(S3_DOWNLOAD_DIR)
  @XmlTransient
  @Getter
  @Setter
  protected String s3DownloadDir;
  
  @Field(FIELD_CONSULT_STATUS)
  @XmlTransient
  @Indexed
  private DocumentConsultStatus consultStatus;

  @Field("acquisition")
  @XmlTransient
  private AcquisitionType acquisition;

  @Field("processingWay")
  @XmlTransient
  private ProcessingWay processingWay;

  @Field("opsStatus")
  @XmlTransient
  @Getter
  @Setter
  private OpsStatus opsStatus;

  @Field("commentModificationDate")
  @XmlTransient
  @Getter
  @Setter
  private Date commentModificationDate;
  @Field("commentModifiedBy")
  @XmlTransient
  @Getter
  @Setter
  private String commentModifiedBy;

  @Field("comment")
  @XmlTransient
  @Getter
  @Setter
  private String comment;

  // NOTIFICATION

  @Transient
  @XmlTransient
  protected boolean notifyOnlyIfProblem;

  @Transient
  @XmlTransient
  protected List<String> notifyGroups;

  @Transient
  @XmlTransient
  protected List<String> notifyIfProblemGroups;

  @Transient
  @XmlTransient
  protected Map<String, Object> notifyParameters;

  @Getter
  @Setter
  @XmlTransient
  @Field("locks")
  private String locks;

  /* -- CONSTRUCTOR -- */

  public Report() {
    super();
  }

  /* -- ACCESSORS -- */

  @Override
  public String getLogsFilePath() {
    return logsFilePath;
  }

  @Override
  public void setLogsFilePath(String logsFilePath) {
    this.logsFilePath = logsFilePath;
  }

  public String getTempLogFilePath() {
    return tempLogFilePath;
  }

  public void setTempLogFilePath(String tempLogFilePath) {
    this.tempLogFilePath = tempLogFilePath;
  }

  public String getXmlFilePath() {
    return xmlFilePath;
  }

  public void setXmlFilePath(String xmlFilePath) {
    this.xmlFilePath = xmlFilePath;
  }

  @Override
  public long getDuration() {
    return this.duration;
  }

  @Override
  public String getFormattedDuration() {
    return toFormattedDuration(duration);
  }

  @Override
  public void setDuration(long duration) {
    this.duration = duration;
  }

  @XmlAttribute(name = "date")
  @Override
  public Date getCreationDate() {
    return super.getCreationDate();
  }

  @Override
  public void setCreationDate(Date creationDate) {
    super.setCreationDate(creationDate);
  }

  @Override
  public Date getReprocessDate() {
    return this.reprocessDate;
  }

  @Override
  public void setReprocessDate(Date reprocessDate) {
    this.reprocessDate = reprocessDate;
  }

  public List<Document> getXmlDocuments() {
    return xmlDocuments;
  }

  public void setXmlDocuments(List<Document> xmlDocuments) {
    this.xmlDocuments = xmlDocuments;
  }

  public List<Document> getXmlChildDocuments() {
    return xmlChildDocuments;
  }

  public void setXmlChildDocuments(List<Document> xmlChildDocuments) {
    this.xmlChildDocuments = xmlChildDocuments;
  }

  public List<Document> getXmlDeads() {
    return xmlDeads;
  }

  public void setXmlDeads(List<Document> xmlDeads) {
    this.xmlDeads = xmlDeads;
  }

  public Map<String, Object> getXmlVariables() {
    return xmlVariables;
  }

  public void setXmlVariables(Map<String, Object> xmlVariables) {
    this.xmlVariables = xmlVariables;
  }

  public List<DeadReportDocument> getDeadReportDocuments() {
    return deadReportDocuments;
  }

  public void setDeadReportDocuments(List<DeadReportDocument> deadReportDocuments) {
    this.deadReportDocuments = deadReportDocuments;
  }

  public List<ReportDocument> getReportDocuments() {
    return reportDocuments;
  }

  public void setReportDocuments(List<ReportDocument> reportDocuments) {
    this.reportDocuments = reportDocuments;
  }

  @Override
  public boolean isInProgress() {
    return isEqualsStatusCode(DocumentStatus.PENDING, this.getStatus());
  }

  @Override
  public void setInProgress(boolean inProgress) {
    this.status = inProgress ? new DocumentStatusEntity(DocumentStatus.PENDING) : null;
    getStatus();
  }

  // -- PROCESS --

  @SuppressWarnings("unchecked")
  public <D extends ProcessDefinition> D getProcessDefinition() {
    return (D) processDefinition;
  }

  public void setProcessDefinition(ProcessDefinition processDefinition) {
    this.processDefinition = processDefinition;
    if (this.processDefinition != null) {
      this.processDefinitionId = this.processDefinition.getId();
      this.processDefinitionName = this.processDefinition.getName();
      this.processDefinitionKey = this.processDefinition.getKey();
      this.processDefinitionVersion = String.valueOf(this.processDefinition.getVersion());
    }
  }

  public DefinitionDescriptor getProcessDefinitionDescriptor() {
    return processDefinitionDescriptor;
  }

  public void setProcessDefinitionDescriptor(DefinitionDescriptor descriptor) {
    this.processDefinitionDescriptor = descriptor;
    if (this.processDefinitionDescriptor != null) {
      this.processDefinitionId = this.processDefinitionDescriptor.getId();
      this.processDefinitionName = this.processDefinitionDescriptor.getName();
      this.processDefinitionKey = this.processDefinitionDescriptor.getKey();
      this.processDefinitionVersion = String.valueOf(this.processDefinitionDescriptor.getVersion());
    }
  }

  @Override
  public String getProcessDefinitionId() {
    return this.processDefinitionId;
  }

  @Override
  public void setProcessDefinitionId(String definitionId) {
    this.processDefinitionId = definitionId;
  }

  @Override
  public String getProcessDefinitionKey() {
    return this.processDefinitionKey;
  }

  @Override
  public void setProcessDefinitionKey(String processKey) {
    this.processDefinitionKey = processKey;
  }

  @Override
  public String getProcessDefinitionName() {
    return this.processDefinitionName;
  }

  @Override
  public void setProcessDefinitionName(String processName) {
    this.processDefinitionName = processName;
  }

  @Override
  public String getProcessDefinitionVersion() {
    return this.processDefinitionVersion;
  }

  @Override
  public void setProcessDefinitionVersion(String userVersion) {
    this.processDefinitionVersion = userVersion;
  }

  @Override
  @SuppressWarnings("unchecked")
  public <I extends ProcessInstance> I getProcessInstance() {
    return (I) processInstance;
  }

  @Override
  public void setProcessInstance(ProcessInstance processInstance) {
    this.processInstance = processInstance;
    if (this.processInstance != null)
      this.processInstanceId = this.processInstance.getId();
  }

  @Override
  public String getTransactionStatus() {
    return transactionStatus;
  }

  @Override
  public void setTransactionStatus(String transactionStatus) {
    this.transactionStatus = transactionStatus;
  }

  @Override
  public String getProcessInstanceId() {
    return processInstanceId;
  }

  @Override
  public void setProcessInstanceId(String processInstanceId) {
    this.processInstanceId = processInstanceId;
  }

  // -- LOGS --

  @Override
  public Logs getLogs() {
    if (logs == null) {
      logs = new Logs();
    }
    return logs;
  }

  @Override
  public boolean haslogs() {
    if (logs == null)
      return false;
    return true;
  }

  @Override
  public void setLogs(Logs logs) {
    this.logs = logs;
  }

  public List<Logs> getSplittedLogs() {
    return splitLogs(logs);
  }

  @Override
  public String getState() {
    if (this.logs == null) {
      if (this.status == null)
        return OK.toString();
      else return this.status.toString();
    }
    return this.logs.getStatus()
        .toString();
  }

  @Override
  public DocumentStatusEntityInterface getStatus() {
    if (this.status == null) {
      if (this.logs == null)
        this.status = new DocumentStatusEntity(DocumentStatus.APPROVED);
      else {
        switch (this.logs.getStatus()) {
        case FATAL:
          this.status = new DocumentStatusEntity(DocumentStatus.FATAL);
          break;
        case ERROR:
          this.status = new DocumentStatusEntity(DocumentStatus.ERROR);
          break;
        case WARN:
          this.status = new DocumentStatusEntity(DocumentStatus.WARNING);
          break;
        case DISABLED:
          this.status = new DocumentStatusEntity(DocumentStatus.NONE);
          break;
        default:
          this.status = new DocumentStatusEntity(DocumentStatus.APPROVED);
        }
      }
    }
    return this.status;
  }

  // -- VARIABLES --

  @Override
  public Map<String, String> getVariables() {
    return variables;
  }

  @Override
  public void setVariables(Map<String, String> executionVars) {
    this.variables = executionVars;
  }

  public List<Entry<String, String>> getVariableEntries() {
    if (variables == null || variables.size() == 0)
      return emptyList();
    Map<String, String> r = new TreeMap<>(variables);
    Set<String> notAuthorizedToDisplayVariables = new HashSet<String>();
    r.keySet()
        .forEach(key -> {
          String lowerCaseKey = key.toLowerCase();
          UNAUTHORIZED_FOR_DISPLAY_VARIABLES.forEach(var -> {
            if (lowerCaseKey.contains(var))
              notAuthorizedToDisplayVariables.add(key);
          });
        });
    if (!notAuthorizedToDisplayVariables.isEmpty())
      notAuthorizedToDisplayVariables.forEach(key -> r.remove(key));
    displayNonPersistentPropertiesDependingOnLogLevel(r);
    return new ArrayList<>(r.entrySet());
  }

  // -- DOCUMENTS --

  // - PERSISTED -

  @Override
  public List<ReportDocument> getDocuments() {
    return documents == null ? Collections.<ReportDocument> emptyList() : unmodifiableList(documents);
  }

  @Override
  public void setDocuments(List<ReportDocument> documents) {
    this.documents = documents;
  }

  @Override
  public int getDocumentCount() {
    return documentCount < 0 ? DocumentHelper.countReportDocuments(documents) : documentCount;
  }

  public void setDocumentCount(int documentCount) {
    this.documentCount = documentCount;
  }

  // - CHILD -

  @Override
  public List<com.byzaneo.xtrade.bean.ReportDocument> getChildDocuments() {
    return childDocuments == null ? Collections.<ReportDocument> emptyList() : unmodifiableList(childDocuments);
  }

  @Override
  public void setChildDocuments(List<com.byzaneo.xtrade.bean.ReportDocument> documents) {
    this.childDocuments = documents;
    this.childDocumentsCount = this.childDocuments.size();
  }

  @Override
  public int getChildDocumentsCount() {
    return childDocumentsCount < 0 ? DocumentHelper.countReportDocuments(childDocuments) : childDocumentsCount;
  }

  public void setChildDocumentsCount(int childDocumentsCount) {
    this.childDocumentsCount = childDocumentsCount;
  }

  public void addChildren(List<ReportDocument> documents) {
    List<ReportDocument> flatten = DocumentHelper.getFlattenReportDocuments(documents);
    flatten.removeAll(documents);
    if (!flatten.isEmpty()) {
      List<ReportDocument> childDocs = new ArrayList<ReportDocument>();
      childDocs.addAll(flatten);
      if (this.childDocuments == null) {
        this.childDocuments = new ArrayList<>();
      }
      this.childDocuments.addAll(childDocs);
      // counts
      this.childDocumentsCount = this.childDocuments.size();
    }
  }

  // - DEAD -

  @Override
  public List<ReportDocument> getDeads() {
    return deads == null ? Collections.<ReportDocument> emptyList() : unmodifiableList(deads);
  }

  @Override
  public void setDeads(List<ReportDocument> deads) {
    this.deads = deads;
    deadCount = deads.size();
  }

  @Override
  public int getDeadCount() {
    return deadCount < 0 ? DocumentHelper.countReportDocuments(deads) : deadCount;
  }

  public void setDeadCount(int deadCount) {
    this.deadCount = deadCount;
  }

  @Override
  public String getProcessDeadQueue() {
    return processDeadQueue;
  }

  @Override
  public void setProcessDeadQueue(String processDeadQueue) {
    this.processDeadQueue = processDeadQueue;
  }

  @Override
  public String getProcessInputBackupDir() {
    return processInputBackupDir;
  }

  @Override
  public void setProcessInputBackupDir(String processInputBackupDir) {
    this.processInputBackupDir = processInputBackupDir;
  }

  @Override
  public String getProcessAttachBackupDir() {
    return processAttachBackupDir;
  }

  @Override
  public void setProcessAttachBackupDir(String processAttachBackupDir) {
    this.processAttachBackupDir = processAttachBackupDir;
  }

  /* -- NOTIFICATION -- */

  @Override
  public boolean isNotifyOnlyIfProblem() {
    return notifyOnlyIfProblem;
  }

  @Override
  public void setNotifyOnlyIfProblem(boolean notifyOnlyIfProblem) {
    this.notifyOnlyIfProblem = notifyOnlyIfProblem;
  }

  @Override
  public List<String> getNotifyGroups() {
    return notifyGroups;
  }

  @Override
  public void setNotifyGroups(List<String> notifyGroups) {
    this.notifyGroups = notifyGroups;
  }

  @Override
  public List<String> getNotifyIfProblemGroups() {
    return notifyIfProblemGroups;
  }

  @Override
  public void setNotifyIfProblemGroups(List<String> notifyIfProblemGroups) {
    this.notifyIfProblemGroups = notifyIfProblemGroups;
  }

  @Override
  public Map<String, Object> getNotifyParameters() {
    return notifyParameters;
  }

  @Override
  public void setNotifyParameters(Map<String, Object> notifyParameters) {
    this.notifyParameters = notifyParameters;
  }

  /* -- TRANSFORM -- */

  /**
   * @return the default HTML representation
   * @see ReportHelper#toHtml(com.byzaneo.xtrade.api.Report)
   */
  public String toHtml() {
    return ReportHelper.toHtml(this);
  }

  /**
   * @return the default textual representation
   * @see ReportHelper#toText(com.byzaneo.xtrade.api.Report)
   */
  public String toText() {
    return ReportHelper.toText(this);
  }

  /* -- CONTENT -- */
  private void appendFooter() {
    if (!content.endsWith(getFooter())) {
      StringBuilder sb = new StringBuilder(content);
      sb.append(getFooter());
      content = sb.toString();
    }
  }

  private String getFooter() {
    final StringBuilder sbuf = new StringBuilder();
    appendLs(sbuf, "</table>");
    appendLs(sbuf, "<br>");
    appendLs(sbuf, "</body></html>");
    return sbuf.toString();
  }

  private StringBuilder appendLs(final StringBuilder sbuilder, final String s) {
    sbuilder.append(s)
        .append(Strings.LINE_SEPARATOR);
    return sbuilder;
  }

  public void setContent(String content) {
    this.content = content;
  }

  /**
   * Returns the content from the temporary log file if it exists, otherwise from the final log file (inside work dir). While in a BP
   * execution, the temporary log file is not complete (footer which closes some html tags is missing) so before returning the content, it
   * appends the appropriate html footer in order to have a valid html content to display.
   * 
   * @return html file content
   */
  public String getContent() {
    this.content = "";
    try {
      if (StringUtils.isNoneEmpty(tempLogFilePath) && (new File(tempLogFilePath)).exists()) {
        content = Files.toString(new File(tempLogFilePath), UTF_8);
        /* The HTML file is currently processing, so to display it, it needs the closing tags */
        appendFooter();
      }
      else if (StringUtils.isNoneEmpty(logsFilePath) && (new File(logsFilePath)).exists()) {
        content = Files.toString(new File(logsFilePath), UTF_8);
        /* The HTML file is currently processing, so to display it, it needs the closing tags */
        appendFooter();
      }
    }
    catch (IOException e) {
      log.error("Unable to return report html file content", e);
      return "";
    }
    return content;
  }

  /* -- OVERRIDE -- */

  @Override
  public String toString() {
    return format(
        "Report [duration=%s, state=%s, date=%s, %sprocessDefinitionId=%s, processDefinitionKey=%s, processDefinitionName=%s, processDefinitionVersion=%s, processInstanceId=%s, documentCount=%s, deadCount=%s]",
        duration, getStatus(), creationDate,
        reprocessDate == null
            ? ""
            : "reprocessed=".concat(reprocessDate.toString())
                .concat(", "),
        processDefinitionId, processDefinitionKey,
        processDefinitionName, processDefinitionVersion,
        processInstanceId,
        getDocumentCount(), getDeadCount());
  }

  @Override
  public String getLogAppenderId() {
    return logAppenderId;
  }

  @Override
  public void setLogAppenderId(String logAppenderId) {
    this.logAppenderId = logAppenderId;
  }

  @Override
  public boolean isSaved() {
    return saved;
  }

  @Override
  public void setSaved(boolean saved) {
    this.saved = saved;
  }
  public DocumentConsultStatus getConsultStatus() {
    return consultStatus;
  }

  public void setConsultStatus(DocumentConsultStatus consultStatus) {
    this.consultStatus = consultStatus;
  }

  public AcquisitionType getAcquisition() {
    return acquisition;
  }

  public void setAcquisition(AcquisitionType acquisition) {
    this.acquisition = acquisition;
  }

  public ProcessingWay getProcessingWay() {
    return processingWay;
  }

  public void setProcessingWay(ProcessingWay processingWay) {
    this.processingWay = processingWay;
  }

  @Override
  public boolean hasEventLogs() {
    return ofNullable(logs).map(Logs::getLogEventList)
        .map(list -> !list.isEmpty())
        .orElse(false);
  }

  public String getLink() {
    return link;
  }

  public void setLink(String link) {
    this.link = link;
  }

  public String getProcessBackupId() {
    return processBackupId;
  }

  public void setProcessBackupId(String processBackupId) {
    this.processBackupId = processBackupId;
  }
}
