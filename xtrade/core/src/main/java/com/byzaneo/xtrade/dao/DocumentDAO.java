package com.byzaneo.xtrade.dao;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.*;
import com.byzaneo.query.Query;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.process.DocumentPolicy;
import com.byzaneo.xtrade.service.DocumentService.DocumentChildrenPolicy;
import com.byzaneo.xtrade.util.ReportedCallable;
import org.springframework.data.domain.*;

import java.io.*;
import java.util.*;

/**
 * Business DAO operations related to the <tt>Document</tt> entity.
 *
 * <AUTHOR> <PERSON>
 * @version CVS $Revision: 1.8 $ $Date: 2008-05-27 06:55:57 $ TODO XTD-279
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Nov 11, 2011
 */
public interface DocumentDAO extends GenericDAO<Document, Long> {
  String DAO_NAME = "xtdDocumentDAO";

  enum DocumentField {
    id("DOC_ID"),
    creationDate("DOC_CREATION_DATE"),
    from("DOC_FROM"),
    linCount("DOC_LIN_COUNT"),
    modificationDate("DOC_MODIFICATION_DATE"),
    owners("DOC_OWNERS"),
    reference("DOC_REFERENCE"),
    status("DOC_STATUS"),
    to("DOC_TO"),
    type("DOC_TYPE"),
    parent("DOC_PARENT"),
    metaGroup("DOC_METADATA"),
    model("DOC_MODEL"),
    email("DOC_EMAIL"),
    files_type("DOF_TYPE"),
    files_subtype("DOF_SUBTYPE"),
    files_comment("DOF_COMMENT");

    private String field;

    DocumentField(String field) {
      this.field = field;
    }

    public String getField() {
      return field;
    }

    public String getFieldValue() {
      switch (this) {
      case status:
        StringBuilder r = new StringBuilder("case ").append(this.field);
        for (DocumentStatus s : DocumentStatus.values())
          r.append(" when ")
              .append(s.ordinal())
              .append(" then ")
              .append("'")
              .append(s.toString())
              .append("'");
        r.append("else 'UNKNOWN' end");
        return r.toString();
      default:
        return this.field;
      }
    }
  }

  <D extends Document> D findById(Class<D> type, Long id) throws DataAccessException;

  /**
   * Same as {@link #findById(Long)} but lazy loading
   *
   * @param id
   * @return
   * @throws DataAccessException
   */
  Document getDocumentLazy(final Long id) throws DataAccessException;

  List<Document> findByIds(List<Long> ids) throws DataAccessException;

  Collection<Document> findAll(boolean onlyRootCategories) throws DataAccessException;

  Map<String, Document> findByTypes(FileType... types) throws DataAccessException;

  Document findByReference(String reference) throws DataAccessException;

  List<Document> findByIndexReferences(Set<String> entityRefs) throws DataAccessException;

  Document findByTypeAndRefAndVersion(FileType type, String reference, String version) throws DataAccessException;

  List<Document> findByReferenceStartWithAndType(String refStartWith, FileType type) throws DataAccessException;

  List<Document> findLastCreated(String owner, List<FileType> types, int number);

  Document findByUUID(String uuid) throws DataAccessException;

  @Override
  <D extends Document> long count(Class<D> type, Query query);

  Map<Object, List<Document>> findGroupsByOwnerTypeAndStatus(String owner,
      String type,
      DocumentStatusEntity status,
      String groupProperty) throws DataAccessException;

  /**
   * The find method.
   */
  <D extends Document> Page<D> findBy(Class<D> type, Pageable pageable,
      List<String> owner, List<FileType> filetypes, String doctype,
      List<String> froms, List<String> tos, boolean fromsOrTos, boolean recipientsNotNull,
      String referenceLike, Date fromDate, Date toDate, DocumentStatusEntity... status) throws DataAccessException;

  /**
   * Excel export
   */
  int asXLS(OutputStream out, String sortedBy, boolean ascending, Map<String, String> cpyNames,
      List<String> owner, List<FileType> filetypes, String doctype,
      List<String> froms, List<String> tos, boolean fromsOrTos,
      boolean recipientsNotNull, String referenceLike, Date fromDate,
      Date toDate, DocumentStatus... status) throws DataAccessException;

  /**
   * Permits to move the data directory by updating all the documentfile which are starting with the given oldBaseUri.
   *
   * @param oldBaseUri oldBaseUri
   * @param newBaseUri newBaseUri
   */
  void updateDocumentFileBaseUri(String oldBaseUri, String newBaseUri);

  void updateDocumentLock(String lockProcessInstanceId, List<Long>docIds);

  void updateSqlDocumentStatus(List<String> invoices);

  void unlockDocumentsForEndedProcesses(Set<String> endedProcessesIds);

  /**
   * @since XTD-330
   * @param groupFields groupFields
   * @param periods periods
   * @param countField countField
   * @param owner owner
   * @param doctype doctype
   * @param froms froms
   * @param tos tos
   * @param recipientsNotNull recipientsNotNull
   * @param status status
   * @return Count
   * @throws DataAccessException
   */
  Map<String, Map<String, Number>> reportCount(List<DocumentField> groupFields,
      List<DatePeriod> periods, DocumentField countField, String owner,
      List<String> doctype, List<String> froms, List<String> tos, boolean recipientsNotNull, List<DocumentStatus> status)
      throws DataAccessException;

  // PROCESS

  /**
   * @param key key
   * @param count count
   * @return the report {@link Document} where the {@link Document#getReference()} is 'key.%' and type is 'REPORT'.
   */
  List<Document> findReportDocuments(String key, int count);

  // FIELD VALUES

  /**
   * @param field field
   * @return the distinct values of the given field (example: "from", "to", "type"...)
   * @throws DataAccessException
   */
  List<String> findDocumentFieldValues(String field) throws DataAccessException;

  // DOCUMENTATION

  /**
   * @param owner owner
   * @param reference reference
   * @return the root document folder
   * @since 6.0
   */
  Document findFolderRootByOwnerAndReference(String owner, String reference);

  /**
   * @param parentFolder parentFolder
   * @param folderName folderName
   * @return the document folder with the given name and parent
   * @since 6.0
   */
  Document findFolderByParentAndName(Document parentFolder, String folderName);

  /**
   * @param documents documents
   * @return the list of detached documents
   * @since 7.0
   */
  <D extends Document> List<D> detach(Collection<D> documents);

  /**
   * @param type {@link Document} kind
   * @param query to gets the documents to archive
   * @param archive archive document to return completed
   * @param remove if <code>true</code>, removes the documents found with the given query. Consider to use backup option to avoid losing
   *          data.
   * @param childrenPolicy policy to apply to the processed documents' children
   * @param reporting the report the progress during archiving
   * @param indexOperations manage the index operations
   * @param dryRun TODO
   * @return the archive document with processed document as children and backup file as document's file.
   */
  Document archiving(Class<Document> type, Query query,
      Document archive, boolean remove, DocumentChildrenPolicy childrenPolicy,
      ReportedCallable<Document> reporting,
      IndexOperations indexOperations, boolean dryRun);

  /**
   * @param archive document to restore
   * @param policy to apply to the duplicated document
   * @param indexOperations manage the index operations
   * @param reporting the report and the progress during restoration
   * @return the archive document with processed document as children.
   */
  Document restore(Document archive, DocumentPolicy policy,
      IndexOperations indexOperations,
      ReportedCallable<Document> reporting);

  /**
   * This method searches in relational database the documents currently being locked by this lock code. Usually the lock code is a process
   * identifier but in some (simpler) cases, like when changing archive status from front-end the code can be a random UUID
   *
   * @see QueryHandler.onChangeDocumentArchiveStatus
   * @param lockCode - the lock code to search
   * @return - the list of documents locked by this lock code or an empty list if no document is locked by this lock code
   */
  List<Document> findByLockCode(String lockCode);

  int removePropertyGroups(Collection<? extends Serializable> propertyGrpIds);

  int removePropertiesOfGroups(Collection<? extends Serializable> propertyGrpIds);

  int removeDocumentFilesOfDocument(Collection<? extends Serializable> documentIds);

  int removeTimelinesOfDocument(Collection<? extends Serializable> documentIds);

  int removeWorkflowOfDocument(Collection<? extends Serializable> documentIds);

  int removeDocuments(Collection<? extends Serializable> documentIds);

  int removeReconciliationOfDocuments(Collection<? extends Serializable> receptionIds,
      Collection<? extends Serializable> recPreviousInvoiceIds, Collection<? extends Serializable> relationIds);

  Collection<? extends Serializable> getAssociatedPropertyGroup(Collection<? extends Serializable> ids);

  List<Document> findByIndexType(Class<? extends Indexable> c, int begin, int size);

  List<Document> searchDocuments(Query query, Pageable pagable);

  public List<Document> searchChildren(List<Document> parentList, Query query);

  public void removeDocumentsFromSession(List<Long> docIdsToRemove);


  public Map<String, Long> countDocumentsGroupedByField(String[] fieldName, Query query);

  public List<Object[]> getDocumentChangedStatus(String action, Date start, Date end, Document.ProcessingWay processingWay,
      List<String> selfBilingList, List<String> partnersList, int limit, int offset);

}
