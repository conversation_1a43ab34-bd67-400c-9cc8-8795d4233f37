package com.byzaneo.xtrade.process;

import com.byzaneo.commons.service.ConfigurationService.ConfigurationKey;

import static com.byzaneo.commons.service.WatchService.POLLED_FILES_VARIABLE;

/**
 * Variables set at process startup time.
 *
 * <AUTHOR>
 * @company Byzaneo
 */
public enum Variable {
  ANT_PROJECT("ant_project"),
  /** Holds the unhandled process execution exception */
  FATAL_ERROR("fatal_error"),

  // ConfigurationService properties
  DATA_DIR(ConfigurationKey.DATA_DIR.getKey()
      .replace('.', '_')),
  TEMP_DIR(ConfigurationKey.TEMP_DIR.getKey()
      .replace('.', '_')),
  WEBAPP_DIR(ConfigurationKey.WEBAPP_DIR.getKey()
      .replace('.', '_')),
  WORK_DIR(ConfigurationKey.WORK_DIR.getKey()
      .replace('.', '_')),
  BACKUP_DIR(ConfigurationKey.BACKUP_DIR.getKey()
      .replace('.', '_')),
  INPUT_DIR(ConfigurationKey.INPUT_DIR.getKey()
      .replace('.', '_')),
  OUTPUT_DIR(ConfigurationKey.OUTPUT_DIR.getKey()
      .replace('.', '_')),
  BIN_DIR(ConfigurationKey.BIN_DIR.getKey()
      .replace('.', '_')),
  ORIGINAL_PROCESS_INPUT_BACKUP_DIR("parent_input_backup_dir"),
  PROCESS_BACKUP_ID("processBackupId"),
  IS_REPROCESSABLE("isReprocessable"),
  S3_DOWNLOAD_DIR("s3_download_dir"),
  S3_SUPPORT_REQUIRED("s3_support_required"),
  REPORT("report"), // Execution Report
  REPORT_DOCUMENT("reportDocument"), // Execution Report
  DOCUMENTS("documents"), // Processed document list
  DOCUMENTS_VAR("documents_var"), // Name of the variable holding the document list - override DOCUMENTS if set
  DOCUMENTS_BACKUP("documents_backup"), // Backup of the document in case of rollback, initialize by document store task
  DEADS("deads"), // Dead document list
  WKF_DOC_STS("workflow_document_status"),
  RECONCILIATION_RELATIONS("reconciliation_relations"),
  DOCUMENT_TIMELINE("document_timeline"),
  EXECUTION_MODE("execution_mode"), // Constant.MODE_TEST, Constant.MODE_PRODUCTION or Constant.MODE_DISABED //TODO update this comment
  EXECUTION_LINK("execution_link"), // the full context URL used to identify a node
  LOG_LEVEL("log_level"), // Logger level
  LOGGERS("loggers"), // Loggers (usually, packages logged)
  INDEXABLES("indexables"), // Indexables which are not linked to a document
  INDEXABLES_VAR("indexables_var"),
  PROCESS_OWNER("process_owner"), // Process Owner
  PROCESS_RESOURCES("process_resources"), // Process Definition Directory
  PROCESS_WORK_DIR("process_work_dir"), // Process Working directory: WORK_DIR/[processInstanceId]
  PROCESS_INPUT_DIR("process_input_dir"), // Process Input directory: PROCESS_WORK_DIR/Constant.INPUT_DIR_NAME
  PROCESS_LOGS_DIR("process_logs_dir"), // Process Logs directory: PROCESS_WORK_DIR/Constant.LOGS_DIR_NAME
  PROCESS_OUTPUT_DIR("process_output_dir"), // Process Output directory: PROCESS_WORK_DIR/Constant.OUTPUT_DIR_NAME
  WRITE_ASYNC("writeAsync"), // Process Async Mode
  WRITE_EMPTY_REPORT("writeEmptyReport"),

  POLL_DIR("poll_dir"), // @since XTD-337
  POLL_FILES(POLLED_FILES_VARIABLE), // polled files (see DirectoryPollerTrigger)

  MESSAGE_CONTENT("message_content"), // The content of a broker message

  JOB_TRIGGER("job_trigger"), // The job's trigger
  JOB_DATE("job_date"), // The job's date
  JOB_SOURCE("job_source"), // The source which triggered the job's trigger
  JOB_EXECUTOR("job_executor"), // The job executor
  PARTITION_DOC_COLLECTIONS("partitionDocCollections"), // Names of all documents collections create by partition task
  CHECK_INTEGRITY("checkIntegrity"),// check integrity boolean parameter
  DOCUMENT_ERRORS("document_errors"), // documents error
  PERSIST_DOCUMENTS("persist_documents"),// persist documents
  PROCESS_RELAUNCH_TOPIC("process_relaunch_topic"),// process relaunch topic
  REST_TRIGGER_NO_HEADER_PARAMS_FILE("rest_trigger_no_header_params_file"),
  REST_TRIGGER_NO_FILES_TO_PROCESS("rest_trigger_no_files_to_process"),
  REST_TRIGGER_NAME("rest_trigger_name"),
  REST_TRIGGER_INSTANCE("rest_trigger_instance"),
  REST_TRIGGER_TIMESTAMP("rest_trigger_timestamp"),

  LOCKED_DIRECTORY_INPUT_BT("lockedDirectoryInputBT"),

  NON_PERSISTENT_DOC_PROPERTIES("non_persistent_doc_properties");

  public static final String PLACE_HOLDER_PREFIX = "${";

  public static final String PLACE_HOLDER_SUFFIX = "}";

  public final String var;

  Variable(String var) {
    this.var = var;
  }

  public String toPlaceHolder() {
    return new StringBuilder(PLACE_HOLDER_PREFIX)
        .append(this.toString())
        .append(PLACE_HOLDER_SUFFIX)
        .toString();
  }

  public String toStringPlaceHolder() {
    return new StringBuilder(PLACE_HOLDER_PREFIX)
        .append(this.toStringVar())
        .append(PLACE_HOLDER_SUFFIX)
        .toString();
  }

  @Override
  public String toString() {
    return this.var;
  }

  public String toStringVar() {
    return this.var + "_string";
  }
}
