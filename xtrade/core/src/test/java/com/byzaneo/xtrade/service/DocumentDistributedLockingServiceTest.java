package com.byzaneo.xtrade.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.File;
import java.util.*;
import java.util.concurrent.*;

import com.byzaneo.commons.exception.ProcessInterruptedException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.beans.factory.BeanExpressionException;
import org.springframework.test.annotation.Repeat;

import com.byzaneo.xtrade.bean.Document;
import com.hazelcast.core.HazelcastInstance;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> <<EMAIL>>
 */
public class DocumentDistributedLockingServiceTest extends com.hazelcast.jet.core.JetTestSupport {

  private static final Logger log = getLogger(DocumentDistributedLockingServiceTest.class);

  public DocumentDistributedLockingServiceImpl documentService1 = new DocumentDistributedLockingServiceImpl();

  public DocumentDistributedLockingServiceImpl documentService2 = new DocumentDistributedLockingServiceImpl();

  public HazelcastInstance newHazelcastInstance_node1 = createHazelcastInstance();

  public HazelcastInstance newHazelcastInstance_node2 = createHazelcastInstance();

  public ExecutorService executor;

  public File directory = new File("src/test/resources/files");

  @AfterEach
  void resetMap() {
    stop(executor);
  }

  @BeforeEach
  void createExecutor() {
    this.executor = Executors.newFixedThreadPool(10);

    documentService1.setHazelcastInstance(newHazelcastInstance_node1);
    documentService2.setHazelcastInstance(newHazelcastInstance_node2);
  }

  @Repeat(value = 10)
  @Test
  void unlockConcurrent() throws InterruptedException {
    List<Document> documents = createDocuments(Arrays.asList(1l, 2l, 3l));
    Callable<Boolean> thread1 = () -> {
      documentService1.addDocumentLocks(documents);
      sleep(5);
      documentService1.unlock(documents);
      return documents.stream()
          .allMatch(doc -> !documentService1.isDocumentLocked(doc));
    };
    Callable<Boolean> thread2 = () -> {
      sleep(2);
      return documents.stream()
          .allMatch(doc -> documentService1.isDocumentLocked(doc));
    };
    Callable<Boolean> thread3 = () -> {
      sleep(6);
      return documents.stream()
          .allMatch(doc -> !documentService1.isDocumentLocked(doc));
    };
    List<Future<Boolean>> futures = executor.invokeAll(Arrays.asList(thread1, thread2, thread3));
    for (Future<Boolean> future : futures) {
      try {
        assertTrue(future.get());
      }
      catch (Exception e) {
        log.error("ERROR" + e);
        fail();
      }
    }
  }

  /**
   * T1->lock(1) (no unlock)</br>
   * T2->sleep(1)->lock(1) (throws and error because 1 is locked by another process)</br>
   */
  @Test
  public void testResourceIsUsedByOtherProcess_Document() throws InterruptedException, ExecutionException {
    final List<Document>[] documents = new List[]{null};
    Assertions.assertDoesNotThrow(() -> documents[0] = createDocuments(Arrays.asList(1l)));
    Callable<Boolean> thread1 = () -> {
      documentService1.addDocumentLocks(documents[0]);
      return true;
    };
    Callable<Boolean> thread2 = () -> {
      sleep(1);
      documentService2.addDocumentLocks(documents[0]);
      return true;
    };
    List<Future<Boolean>> futures = executor.invokeAll(Arrays.asList(thread1, thread2));
    assertTrue(futures.get(0).get());
    Assertions.assertThrows(ExecutionException.class, () -> futures.get(1).get());

  }

  /**
   * T1->lock(1) (no unlock)</br>
   * T2->sleep(1)->lock(1) (throws and error because 1 is locked by another process)</br>
   */
  @Test
  public void testResourceIsUsedByOtherProcess_Directory() throws InterruptedException, ExecutionException {
    Callable<Boolean> thread1 = () -> {
      documentService1.addDirectoryLock(directory, false);
      return true;
    };
    Callable<Boolean> thread2 = () -> {
      sleep(1);
      documentService2.addDirectoryLock(directory, false);
      return true;
    };
    List<Future<Boolean>> futures = executor.invokeAll(Arrays.asList(thread1, thread2));

      assertTrue(futures.get(0).get());
      Assertions.assertThrows(ExecutionException.class, () -> futures.get(1).get());

  }

  @Test
  void addDocumentLocks() {
    this.documentService1.addDocumentLocks(createDocuments(Arrays.asList(1l, 2l, 3l)));

    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("1")
        .isLockedByCurrentThread());
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("2")
        .isLockedByCurrentThread());
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("3")
        .isLockedByCurrentThread());

    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("1")
        .isLocked());
    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("2")
        .isLocked());
    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("3")
        .isLocked());

  }

  @Test
  void addDirectoryLocks() {

    this.documentService1.addDirectoryLock(directory, false);
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock(directory.getAbsolutePath())
        .isLockedByCurrentThread());
    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock(directory.getAbsolutePath())
        .isLocked());
  }

  @Test
  void addDirectoryLockWithSubdirectories() {

    this.documentService1.addDirectoryLock(directory, true);
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock(directory.getAbsolutePath())
        .isLockedByCurrentThread());
    assertTrue(newHazelcastInstance_node1.getCPSubsystem()
        .getLock(new File(directory, "child-folder").getAbsolutePath())
        .isLockedByCurrentThread());

    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock(directory.getAbsolutePath())
        .isLocked());

    assertTrue(newHazelcastInstance_node2.getCPSubsystem()
        .getLock(new File(directory, "child-folder").getAbsolutePath())
        .isLocked());
  }

  @Test
  void removeLocks() {

    List<Document> documents = createDocuments(Arrays.asList(1l, 2l, 3l));
    this.documentService1.addDocumentLocks(documents);
    this.documentService1.unlock(documents);

    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("1")
        .isLockedByCurrentThread());
    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("2")
        .isLockedByCurrentThread());
    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("3")
        .isLockedByCurrentThread());

    assertFalse(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("1")
        .isLocked());
    assertFalse(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("2")
        .isLocked());
    assertFalse(newHazelcastInstance_node2.getCPSubsystem()
        .getLock("3")
        .isLocked());

  }

  @Test
  void removeLocks2() {
    List<Document> documents = createDocuments(Arrays.asList(1l, 2l, 3l));
    this.documentService1.addDocumentLocks(documents);

    this.documentService1.unlockAllDocuments();

    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("1")
        .isLockedByCurrentThread());
    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("2")
        .isLockedByCurrentThread());
    assertFalse(newHazelcastInstance_node1.getCPSubsystem()
        .getLock("3")
        .isLockedByCurrentThread());
  }

  // test with multiple threads, ensure only one thread 'gets' to lock all of them and the others unlock their already locked files
  @Test
  void tryLockFiles() throws InterruptedException, ExecutionException {
    List<File> files = createFileList(100);

    List<Callable<Boolean>> threads = new ArrayList<>();
    for (int i = 0; i < 10; i++) {
      Callable<Boolean> thread = () -> documentService1.tryLock(files);
      threads.add(thread);
    }
    for (int i = 0; i < 10; i++) {
      Callable<Boolean> thread = () -> documentService2.tryLock(files);
      threads.add(thread);
    }

    List<Future<Boolean>> futures = executor.invokeAll(threads);

    Set<Boolean> results = new HashSet<>();
    for (Future<Boolean> future : futures) {

      results.add(future.get());
    }
    assertEquals(2, results.size());
    for (File file : files) {
      assertTrue(documentService1.isFileLocked(file));
    }
  }

  private List<File> createFileList(int size) {
    List<File> fileList = new ArrayList<>();
    for (int i = 0; i < size; i++) {
      fileList.add(new File("test_file_" + i));
    }
    return fileList;
  }

  private List<Document> createDocuments(List<Long> ids) {
    List<Document> documents = new ArrayList<Document>();
    for (Long id : ids) {
      Document document = new Document();
      document.setId(id);
      documents.add(document);
    }
    return documents;
  }

  private static void stop(ExecutorService executor) {
    executor.shutdown();
  }

  private static void sleep(int seconds) {
    try {
      TimeUnit.SECONDS.sleep(seconds);
    }
    catch (InterruptedException e) {
      throw new IllegalStateException(e);
    }
  }
}
