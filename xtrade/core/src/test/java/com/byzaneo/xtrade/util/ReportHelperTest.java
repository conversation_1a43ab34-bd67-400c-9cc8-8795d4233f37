package com.byzaneo.xtrade.util;

import static com.byzaneo.xtrade.api.DocumentStatus.APPROVED;
import static com.byzaneo.xtrade.bean.Report.Metadata.STATUS;
import static com.byzaneo.xtrade.util.ReportHelper.*;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.countMatches;
import static org.apache.logging.log4j.Level.ERROR;
import static org.apache.logging.log4j.Level.WARN;
import static org.junit.jupiter.api.Assertions.*;

import java.io.*;
import java.util.*;

import org.apache.commons.io.IOUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;
import org.springframework.util.ResourceUtils;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.*;

/**
 * <AUTHOR> Lemesle <<EMAIL>>
 * @company Byzaneo
 * @date Jun 28, 2016
 * @since 1.0
 */

class ReportHelperTest {

  @Test
  void testToReportDocument() {
    assertNull(toReportDocument(null, null, false));

    Report report = new Report();
    File file = new File("test");
    Document doc = toReportDocument(report, file, true);
    assertEquals(report.getOwners(), doc.getOwners());
    assertEquals(report.getState(), doc.getMetadata(STATUS));
    assertEquals(file, doc.getFirstDocumentFile()
        .getFile());
  }

  @Test
  void testResolveReports() throws FileNotFoundException {
    List<Document> reportDocs = new LinkedList<Document>();
    Document doc = new Document("ref1", "owner1", "XML", "from", "to", DocumentStatus.ACCEPTED);
    Report report = new Report();
    File file = ResourceUtils.getFile("pom.xml");
    doc.setIndexValue(report);
    doc.addFile(new DocumentFile(file, FileType.REPORT, "action", "id", doc, "comment"));
    report.setOwners("owner1");
    reportDocs.add(doc);
    List<com.byzaneo.xtrade.api.Report> reports = resolveReports(reportDocs);
    assertEquals("owner1", reports.get(0)
        .getOwners());
    assertEquals(APPROVED, reports.get(0)
        .getStatusAsEnumValue());
  }

  @Test
  void filterWarnMsgFromReport() throws IOException {
    testFilterReportContentHtml(WARN.toString(), 2);
    testFilterReportContentText(WARN.toString(), 1);
  }

  @Test
  void filterErrMsgFromReport() throws IOException {
    testFilterReportContentHtml(ERROR.toString(), 3);
    testFilterReportContentText(ERROR.toString(), 2);
  }

  private void testFilterReportContentHtml(String level, int trElementExpected) throws IOException {
    InputStream is = getClass().getResourceAsStream("/files/testFilterReportContent.html");
    String reportContent = IOUtils.toString(is, UTF_8);
    String filteredHtml = ReportHelper.filterReportContentInHtml(reportContent, asList(level));

    org.jsoup.nodes.Document htmlReport = Jsoup.parse(filteredHtml);
    Elements trElements = htmlReport.select("tr");
    assertEquals(trElements.size(), trElementExpected);

    Elements tdElements = htmlReport.select("td");
    List<Element> tdElementWithLevel = tdElements.stream()
        .filter(e -> "Level".equals(e.attr("title")) && level.equals(e.text()))
        .collect(toList());
    assertEquals(1, tdElementWithLevel.size());
    assertEquals(tdElementWithLevel.get(0)
        .text(), level);
  }

  private void testFilterReportContentText(String level, int countMatches) throws IOException {
    InputStream is = getClass().getResourceAsStream("/files/testFilterReportContent.html");
    String reportContent = IOUtils.toString(is, UTF_8);
    List<String> filteredTextList = ReportHelper.filterReportContentInTextList(reportContent, asList(level));

    assertFalse(filteredTextList.contains("<tr>"));
    assertFalse(filteredTextList.contains("<td>"));
    int n = Math.toIntExact(filteredTextList.stream()
        .map(text -> countMatches(text, level.toString()))
        .collect(counting()));

    assertEquals(n, countMatches);

  }

  @Test
  public void testLimitPropertiesEntriesToTwoEntries() {
    String input = "{uuid_doc_1={PROP1=TEST_1, PROP2=TEST_2}, uuid_doc_2={PROP1=TEST_1}, uuid_doc_3={PROP1=TEST_1, PROP2=TEST_2, PROP3=TEST_3}, uuid_doc_4={PROP4=TEST_4}}";
    String expected = "{uuid_doc_1={PROP1=TEST_1, PROP2=TEST_2}, uuid_doc_2={PROP1=TEST_1}, ...}";
    assertEquals(expected, limitPropertiesEntries(input, 2));
  }

  @Test
  public void testLimitPropertiesEntriesWithLimitGreaterThanEntries() {
    String input = "{uuid_doc_1={PROP1=TEST_1, PROP2=TEST_2}, uuid_doc_2={PROP1=TEST_1}}";
    String expected = "{uuid_doc_1={PROP1=TEST_1, PROP2=TEST_2}, uuid_doc_2={PROP1=TEST_1}}";
    assertEquals(expected, limitPropertiesEntries(input, 100));
  }

  @Test
  public void testInvalidPropertiesEntriesFormat() {
    assertEquals("", limitPropertiesEntries("test", 1));
  }

  @Test
  public void testNullPropertiesEntries() {
    assertEquals("", limitPropertiesEntries(null, 5));
  }

  @Test
  public void testEmptyPropertiesEntries() {
    assertEquals("", limitPropertiesEntries("", 100));
  }
}
