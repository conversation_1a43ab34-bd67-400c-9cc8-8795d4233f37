package com.byzaneo.xtrade.test.process;

import com.byzaneo.xtrade.api.DocumentType;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.process.*;
import com.byzaneo.xtrade.process.Variable;
import org.activiti.engine.delegate.DelegateExecution;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.*;
import org.mockito.*;

import java.util.*;

import static com.byzaneo.xtrade.api.DocumentStatus.ERROR;
import static com.byzaneo.xtrade.api.DocumentStatus.PENDING;
import static com.byzaneo.xtrade.process.Variable.DEADS;
import static com.byzaneo.xtrade.process.Variable.DOCUMENTS;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
class VariableHelperTest {

  @Mock
  private DelegateExecution execution;

  @BeforeEach
  void before() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  void addDeadsRecursive() {

    Document document = new Document();
    Document anotherDocument = new Document();

    Document documentChild = new Document("moi", "lui", "qui suis-je ?", "d'où viens-je ?", "où vais-je ?",
        PENDING);
    document.addChild(documentChild);

    Collection<Document> documents = Collections.singleton(document);

    List<Document> docs = new ArrayList<>(3);
    docs.add(document);
    docs.add(anotherDocument);
    docs.add(documentChild);

    when(execution.getVariable(DEADS.toString())).thenReturn(new ArrayList<>());
    when(execution.getVariable(DOCUMENTS.toString())).thenReturn(docs);
    when(execution.getProcessDefinitionId()).thenReturn("procDefId #1");
    when(execution.getProcessInstanceId()).thenReturn("procInstId #1");

    VariableHelper.addDeads(execution, true, documents);

    assertEquals(ERROR, documentChild.getStatusAsEnumValue());
    assertEquals(1, docs.size());
    assertSame(anotherDocument, docs.get(0));
  }

  @Test
  void addDeadsRecursive2() {

    Document document = new Document();
    Document anotherDocument = new Document();

    Document documentChild = new Document("moi", "lui", "qui suis-je ?", "d'où viens-je ?", "où vais-je ?",
        PENDING);
    document.addChild(documentChild);

    Collection<Document> documents = Collections.singleton(documentChild);

    List<Document> docs = new ArrayList<>(3);
    docs.add(document);
    docs.add(anotherDocument);
    docs.add(documentChild);

    when(execution.getVariable(DEADS.toString())).thenReturn(new ArrayList<>());
    when(execution.getVariable(DOCUMENTS.toString())).thenReturn(docs);
    when(execution.getProcessDefinitionId()).thenReturn("procDefId #1");
    when(execution.getProcessInstanceId()).thenReturn("procInstId #1");

    VariableHelper.addDeads(execution, true, documents);

    assertEquals(ERROR, documentChild.getStatusAsEnumValue());
    assertEquals(2, docs.size());
    int index = docs.indexOf(document);
    assertNotEquals(-1, index);
    assertEquals(0, docs.get(index)
        .getChildCount());
  }

  @Test
  void testTaxOriginalTagIsClearedFromOtherFiles() {
    DocumentFile file1 = new DocumentFile();
    DocumentFile file2 = new DocumentFile();
    DocumentFile file3 = new DocumentFile();

    file1.setActionName(VariableHelper.TAX_ORIGINAL_LABEL);
    file2.setActionName(VariableHelper.TAX_ORIGINAL_LABEL);
    file3.setActionName(VariableHelper.TAX_ORIGINAL_LABEL);

    Document document = new Document();
    document.setFiles(List.of(file1, file2));

    VariableHelper.verifyIfOtherDocumentHasTaxOriginalTag(document, file3);

    assertNull(file1.getActionName());
    assertNull(file2.getActionName());
    assertEquals(VariableHelper.TAX_ORIGINAL_LABEL, file3.getActionName());
  }

  @Test
  void testNoChangeWhenActionNameIsNotTaxOriginal() {
    DocumentFile file1 = new DocumentFile();
    DocumentFile file2 = new DocumentFile();

    file1.setActionName("otherAction");
    file2.setActionName(VariableHelper.TAX_ORIGINAL_LABEL);

    Document document = new Document();
    document.setFiles(List.of(file2));

    VariableHelper.verifyIfOtherDocumentHasTaxOriginalTag(document, file1);

    assertEquals("otherAction", file1.getActionName());
    assertEquals(VariableHelper.TAX_ORIGINAL_LABEL, file2.getActionName());
  }

  @Test
  void testNoChangeMultipleDocumentsWhenActionNameIsNotTaxOriginal() {
    DocumentFile file1 = new DocumentFile();
    DocumentFile file2 = new DocumentFile();
    DocumentFile file3 = new DocumentFile();
    DocumentFile file4 = new DocumentFile();
    DocumentFile file5 = new DocumentFile();
    DocumentFile file6 = new DocumentFile();

    file1.setActionName("otherAction");
    file2.setActionName("otherAction");
    file3.setActionName("otherAction");
    file4.setActionName("otherAction");
    file5.setActionName(VariableHelper.TAX_ORIGINAL_LABEL);
    file6.setActionName(VariableHelper.TAX_ORIGINAL_LABEL);

    Document document = new Document();
    document.setFiles(List.of(file1, file2, file3, file4, file5));

    VariableHelper.verifyIfOtherDocumentHasTaxOriginalTag(document, file6);

    assertEquals("otherAction", file1.getActionName());
    assertEquals("otherAction", file2.getActionName());
    assertEquals("otherAction", file3.getActionName());
    assertEquals("otherAction", file4.getActionName());
    assertNull(file5.getActionName());
    assertEquals(VariableHelper.TAX_ORIGINAL_LABEL, file6.getActionName());
  }

  @Test
  void testNullParentDoesNotThrow() {
    DocumentFile file = new DocumentFile();
    file.setActionName(VariableHelper.TAX_ORIGINAL_LABEL);
    Document document = new Document();
    document.setFiles(List.of(file));

    assertDoesNotThrow(() -> VariableHelper.verifyIfOtherDocumentHasTaxOriginalTag(document, file));
  }

  @Test
  void testNullActionNameDoesNotThrow() {
    DocumentFile file = new DocumentFile();
    file.setActionName(null);
    Document document = new Document();
    document.setFiles(List.of(file));

    assertDoesNotThrow(() -> VariableHelper.verifyIfOtherDocumentHasTaxOriginalTag(document, file));
  }

  @Test
  void testgetBackupDocumentsEvenItIsEmpty() {
    assertTrue(VariableHelper.getBackupDocuments(execution)
        .isEmpty(), "Backup documents should be empty");
  }

  @Test
  void testgetBackupDocuments() {
    when(execution.getVariable(Variable.DOCUMENTS_BACKUP.toString())).thenReturn(Arrays.asList(Mockito.mock(Document.class)));
    assertTrue(CollectionUtils.isNotEmpty(VariableHelper.getBackupDocuments(execution)), "Backup documents should not be empty");
  }

  @Test
  void backupDocumentsWhenItIsEmpty() {
    assertTrue(VariableHelper.backupDocuments(execution, null)
        .isEmpty(), "Backup documents should be empty");
  }

  @Test
  void backupDocuments() {
    List<Document> docs = Arrays.asList(com.byzaneo.xtrade.test.Mock.createDocumentWithIndex(1, DocumentType.INVOIC));
    assertEquals(docs.get(0), VariableHelper.backupDocuments(execution, docs)
        .get(0), "Should be the same");
  }
}
