-- Script to export existing translations in the new frontend format
-- This script generates a CSV file with format: module;key;fr;en;de;es;nl
-- Run this script before removing non-fr/en translations from flyway scripts

-- For PostgreSQL
SELECT 
    COALESCE(m.name, '') || ';' ||
    t.code || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'fr' THEN COALESCE(t.new_value, t.default_value) END), '') || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'en' THEN COALESCE(t.new_value, t.default_value) END), '') || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'de' THEN COALESCE(t.new_value, t.default_value) END), '') || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'es' THEN COALESCE(t.new_value, t.default_value) END), '') || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'nl' THEN COALESCE(t.new_value, t.default_value) END), '') AS csv_line
FROM gnx_i18n_translation t
LEFT JOIN gnx_i18n_module m ON t.i18n_module_id = m.id
WHERE t.locale IN ('fr', 'en', 'de', 'es', 'nl')
GROUP BY m.name, t.code
ORDER BY m.name, t.code;

-- For Oracle (alternative syntax)
/*
SELECT 
    NVL(m.name, '') || ';' ||
    t.code || ';' ||
    NVL(MAX(CASE WHEN t.locale = 'fr' THEN NVL(t.new_value, t.default_value) END), '') || ';' ||
    NVL(MAX(CASE WHEN t.locale = 'en' THEN NVL(t.new_value, t.default_value) END), '') || ';' ||
    NVL(MAX(CASE WHEN t.locale = 'de' THEN NVL(t.new_value, t.default_value) END), '') || ';' ||
    NVL(MAX(CASE WHEN t.locale = 'es' THEN NVL(t.new_value, t.default_value) END), '') || ';' ||
    NVL(MAX(CASE WHEN t.locale = 'nl' THEN NVL(t.new_value, t.default_value) END), '') AS csv_line
FROM gnx_i18n_translation t
LEFT JOIN gnx_i18n_module m ON t.i18n_module_id = m.id
WHERE t.locale IN ('fr', 'en', 'de', 'es', 'nl')
GROUP BY m.name, t.code
ORDER BY m.name, t.code;
*/

-- To save to file (PostgreSQL example):
-- \copy (SELECT 'module;key;fr;en;de;es;nl' UNION ALL SELECT ...) TO 'frontend_translations_export.csv' WITH CSV;

-- Header line for the CSV file
SELECT 'module;key;fr;en;de;es;nl' AS header
UNION ALL
SELECT 
    COALESCE(m.name, '') || ';' ||
    t.code || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'fr' THEN COALESCE(t.new_value, t.default_value) END), '') || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'en' THEN COALESCE(t.new_value, t.default_value) END), '') || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'de' THEN COALESCE(t.new_value, t.default_value) END), '') || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'es' THEN COALESCE(t.new_value, t.default_value) END), '') || ';' ||
    COALESCE(MAX(CASE WHEN t.locale = 'nl' THEN COALESCE(t.new_value, t.default_value) END), '')
FROM gnx_i18n_translation t
LEFT JOIN gnx_i18n_module m ON t.i18n_module_id = m.id
WHERE t.locale IN ('fr', 'en', 'de', 'es', 'nl')
GROUP BY m.name, t.code
ORDER BY m.name, t.code;
