package com.byzaneo.angular.bean;

import com.byzaneo.commons.bean.Persistent;
import com.byzaneo.security.bean.User;
import lombok.*;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.*;

@Entity
@Table(name = "gnx_i18n_translation", uniqueConstraints = { @UniqueConstraint(columnNames = { "code", "locale" }) })
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XmlRootElement( name = "I18NTranslation")
public class I18NTranslation implements Persistent<Long>, Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Column(name = "id", nullable = false)
  private Long id;

  @Column(name = "code", nullable = false, unique = true)
  private String code;
  @Column(name = "default_value")
  @Lob
  @Type(type = "org.hibernate.type.TextType")
  private String defaultValue;
  @Column(name = "new_value")
  @Lob
  @Type(type = "org.hibernate.type.TextType")
  private String newValue;
  @Column(name = "locale")
  private Locale locale;
  @Column(name = "default_value_changed_at")
  @Temporal(TemporalType.TIMESTAMP)
  private Date defaultValueChangedAt;
  @Column(name = "new_value_changed_at")
  @Temporal(TemporalType.TIMESTAMP)
  private Date newValueChangedAt;
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "new_value_changed_by", nullable = true)
  private User newValueChangedBy;
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "i18n_module_id", nullable = true)
  private I18NModule i18NModule;

  @Transient
  private String localeAsString;
}
