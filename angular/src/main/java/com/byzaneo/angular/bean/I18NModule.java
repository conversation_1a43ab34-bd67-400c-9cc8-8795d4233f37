package com.byzaneo.angular.bean;

import com.byzaneo.commons.bean.*;
import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

@Entity
@Table(name = "gnx_i18n_module")
@Getter
@Setter
public class I18NModule implements Persistent<Long>, Serializable {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Column(name = "id", nullable = false)
  private Long id;

  @Column(name = "name")
  private String name;
  @OneToMany(mappedBy="i18NModule", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  private transient Set<I18NTranslation> i18NTranslations;
}