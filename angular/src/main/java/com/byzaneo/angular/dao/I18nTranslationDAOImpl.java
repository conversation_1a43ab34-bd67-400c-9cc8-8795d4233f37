package com.byzaneo.angular.dao;

import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.commons.bean.I18NTranslationColumn;
import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.commons.util.DatabaseHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.limit.Limit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.Optional.ofNullable;
import static javax.persistence.criteria.JoinType.LEFT;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.springframework.data.jpa.repository.query.QueryUtils.toOrders;

@Repository(I18nTranslationDAO.DAO_NAME)
public class I18nTranslationDAOImpl extends GenericJpaDAO<I18NTranslation, Long> implements I18nTranslationDAO {

  public Page<I18NTranslation> search(final Query query, final Pageable pageable, Boolean onlyConflicts) {
    return this.search(null, query, pageable,  onlyConflicts, (String[]) null);
  }


  public <E extends I18NTranslation> Page<E> search(Class<E> type, Query query, Pageable pageable, Boolean onlyConflicts, String... fetches) {
    TypedQuery queryToExecute = null;
    @SuppressWarnings("unchecked") final Class<E> entityClassFunction = type == null ? (Class<E>) getEntityClass() : type;
    if (DatabaseHelper.isOracle(databaseType)){
      queryToExecute = createNativeQuery(query, pageable);
    }
    else {
      queryToExecute = getQuery(new QuerySpecification<E>(query), entityClassFunction,
              pageable == null ? null : pageable.getSort(), query.getLimit(), onlyConflicts, fetches);
    }
    if (pageable == null) {
      if (query.getLimit() != null && query.getLimit()
              .getLimitNumber() != -1) {
        queryToExecute.setMaxResults(query.getLimit()
                .getLimitNumber());
      }
      return new PageImpl<E>(queryToExecute.getResultList());
    }
    queryToExecute.setFirstResult((int) pageable.getOffset());
    queryToExecute.setMaxResults(ofNullable(query.getLimit())
            .map(Limit::getLimitNumber)
            .filter(limit -> limit != -1 && limit < pageable.getPageSize())
            .orElse(pageable.getPageSize()));

    final Long total = count(entityClassFunction, query, onlyConflicts);
    final List<E> content = total > pageable.getOffset() ? queryToExecute.getResultList() : Collections.<E> emptyList();

    return new PageImpl<E>(content, pageable, total);
  }


  /**
   * Méthode permettant de créer une requête native pour les BDs oracle (suite à une exception à cause des colonnes CLOB)
   * @param query
   * @param pageable
   * @return TypeQuery la requête créée
   */
  private TypedQuery createNativeQuery(Query query, Pageable pageable){
    String qlString = new String("SELECT ");
    if(query.isDistinct())
      qlString=qlString.concat(" distinct ");
    qlString= qlString.concat(" p FROM " + getEntityName() + " p ");
    if(query.getWhereClause()!=null) {
      qlString = qlString.concat("WHERE " + query.getWhereClause().toString().replace("\"", "'"));
      qlString = qlString.replace("~", " like ").replace("*","%");
      qlString = qlString.replace("(defaultValue)", "(to_char((to_char(defaultValue))))");
      qlString = qlString.replace("(newValue)", "(to_char((to_char(newValue))))");
      Pattern pattern = Pattern.compile("(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z)");
      Matcher matcher = pattern.matcher(qlString);
      while (matcher.find()) {
        String filterDate = matcher.group(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-YYYY HH:mm");
        LocalDateTime dateTime = LocalDateTime.of(
                Integer.valueOf(filterDate.substring(0,4)),
                Integer.valueOf(filterDate.substring(6,7)),
                Integer.valueOf(filterDate.substring(9,10)),
                Integer.valueOf(filterDate.substring(11,13)),
                Integer.valueOf(filterDate.substring(14,16)));
        String formattedDateTime = dateTime.format(formatter);
        qlString = qlString.replace("'"+filterDate+"'", " TO_TIMESTAMP('"+formattedDateTime+"')");
      }
    }
    if(pageable != null && pageable.getSort()!=null){
      for (Sort.Order sort:pageable.getSort().toList()) {
        qlString=qlString.concat(" order by "+
                ((sort.getProperty().toString().equals("newValue") || sort.getProperty().toString().equals("defaultValue")) ?
                        "to_char((to_char("+sort.getProperty().toString()+")))" +" "+ sort.getDirection().toString() :
                        sort.getProperty().toString() +" "+ sort.getDirection().toString()));
      }
    }
    return  getEntityManager().createQuery(qlString, getEntityClass());
  }

  protected <E extends I18NTranslation> TypedQuery<E> getQuery(Specification<E> spec, Class<E> type, Sort sort, Limit limit,Boolean onlyConflicts,  String... fetches) {
    CriteriaQuery<E> criteriaQuery = createCriteriaQuery(spec, type, sort, onlyConflicts, fetches);
    TypedQuery<E> typedQuery = getEntityManager().createQuery(criteriaQuery);
    if (limit != null && limit.getLimitNumber() > 0) {
      typedQuery.setMaxResults(limit.getLimitNumber());
    }
    return typedQuery;
  }

  protected <E extends I18NTranslation> CriteriaQuery<E> createCriteriaQuery(Specification<E> spec, Class<E> type, Sort sort, Boolean onlyConflicts, String... fetches) {
    CriteriaBuilder builder = this.getEntityManager()
            .getCriteriaBuilder();
    CriteriaQuery<E> query = builder.createQuery(type);

    Root<E> root = applySpecificationToCriteria(spec, type, query, onlyConflicts);


    if (isNotEmpty(fetches)) {
      for (String fetch : fetches) {
        if (isBlank(fetch))
          continue;
        root.fetch(fetch, LEFT);
      }
    }

    query.select(root);
    if (sort != null) {
      query.orderBy(toOrders(sort, root, builder));
    }

    return query;
  }

  protected <S, E extends I18NTranslation> Root<E> applySpecificationToCriteria(Specification<E> spec, Class<E> type,
                                                                                CriteriaQuery<S> query, Boolean onlyConflicts) {
    Assert.notNull(query);
    Root<E> root = query.from(type);

    if (spec == null) {
      return root;
    }

    CriteriaBuilder builder = getEntityManager().getCriteriaBuilder();
    Predicate predicate = spec.toPredicate(root, query, builder);

    if (onlyConflicts != null && onlyConflicts.equals(true)) {
      Predicate newValueGreaterThan = builder.lessThan(root.get(I18NTranslationColumn.NEW_VALUE_CHANGED_AT.getName()), root.get(I18NTranslationColumn.DEFAULT_VALUE_CHANGED_AT.getName()));
      Predicate newValueNotEqualDefaultValue = builder.notEqual(root.get(I18NTranslationColumn.NEW_VALUE.getName()), root.get(I18NTranslationColumn.DEFAULT_VALUE.getName()));
      if (newValueNotEqualDefaultValue != null && newValueGreaterThan != null) {
        predicate = predicate != null ?
                builder.and(predicate, newValueGreaterThan, newValueNotEqualDefaultValue) :
                builder.and(newValueGreaterThan, newValueNotEqualDefaultValue);
      }
    }
    if (predicate != null) {
      query.where(predicate);
    }
    return root;
  }

  public <E extends I18NTranslation> long count(Class<E> type, final Query query, Boolean onlyConflicts) {
    long count;
    if (DatabaseHelper.isOracle(databaseType)){
      count = createNativeQuery(query, null).getResultList().stream().count();
    } else {
      count = getCountQuery(new QuerySpecification<E>(query), type, onlyConflicts).getSingleResult();
    }
    return count;
  }

  protected <E extends I18NTranslation> TypedQuery<Long> getCountQuery(Specification<E> spec, Class<E> type, Boolean onlyConflicts) {
    CriteriaBuilder builder = getEntityManager().getCriteriaBuilder();
    CriteriaQuery<Long> query = builder.createQuery(Long.class);

    Root<E> root = applySpecificationToCriteria(spec, type, query, onlyConflicts);

    if (query.isDistinct()) {
      query.select(builder.countDistinct(root));
    }
    else {
      query.select(builder.count(root));
    }

    query.getOrderList()
            .clear();

    return getEntityManager().createQuery(query);
  }

  public <E extends I18NTranslation> Page<Locale> searchAllLocale(Class<E> type, String query, Pageable pageable) {
     final TypedQuery<Locale> q = getEntityManager().createQuery(query, Locale.class);
    if (pageable == null) {
      return new PageImpl<Locale>(q.getResultList());
    }
    q.setFirstResult((int) pageable.getOffset());
    q.setMaxResults(pageable.getPageSize());

    final List<Locale> content = q.getResultList();

    return new PageImpl<Locale>(content, pageable, 1000);
  }

}
