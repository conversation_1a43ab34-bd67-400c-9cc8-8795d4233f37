package com.byzaneo.angular.dao;

import com.byzaneo.commons.dao.GenericDAO;
import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.query.Query;
import org.springframework.data.domain.*;

import java.util.Locale;

public interface I18nTranslationDAO extends GenericDAO<I18NTranslation, Long> {
  public static final String DAO_NAME = "i18nTranslationDAO";
  public Page<I18NTranslation> search(final Query query, final Pageable pageable, Boolean onlyConflicts);
  public <E extends I18NTranslation> long count(Class<E> type, final Query query, Boolean onlyConflicts);
  public <E extends I18NTranslation> Page<Locale> searchAllLocale(Class<E> type, String query, Pageable pageable);
}
