package com.byzaneo.query.visitor; // NOSONAR : duplicated blocks

import static com.byzaneo.query.Constants.FIELD_NAME_SEPARATOR;
import static com.byzaneo.query.Constants.FIELD_SEPARATOR;
import static com.byzaneo.query.function.CastFunction.accept;
import static java.lang.String.format;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.collections4.CollectionUtils.select;
import static org.apache.commons.collections4.functors.AllPredicate.allPredicate;
import static org.apache.commons.collections4.functors.AnyPredicate.anyPredicate;
import static org.apache.commons.collections4.functors.EqualPredicate.equalPredicate;
import static org.apache.commons.collections4.functors.NotNullPredicate.notNullPredicate;
import static org.apache.commons.collections4.functors.NotPredicate.notPredicate;
import static org.apache.commons.collections4.functors.NullPredicate.nullPredicate;
import static org.apache.commons.lang3.StringUtils.contains;
import static org.apache.commons.lang3.StringUtils.containsIgnoreCase;
import static org.apache.commons.lang3.StringUtils.remove;
import static org.apache.commons.lang3.StringUtils.split;
import static org.apache.commons.lang3.StringUtils.substringAfterLast;
import static org.apache.commons.lang3.StringUtils.substringBeforeLast;
import static org.apache.commons.lang3.StringUtils.trimToEmpty;
import static org.apache.commons.lang3.builder.ToStringBuilder.reflectionToString;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.reflections.util.ClasspathHelper.forPackage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.beans.BeanUtils.getPropertyDescriptor;

import java.beans.PropertyDescriptor;
import java.lang.reflect.*;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

import org.apache.commons.collections4.Predicate;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.util.ConfigurationBuilder;
import org.slf4j.Logger;
import org.springframework.beans.*;
import org.springframework.expression.ExpressionException;
import org.springframework.expression.spel.standard.SpelExpressionParser;

import com.byzaneo.query.Constants;
import com.byzaneo.query.clause.*;
import com.byzaneo.query.function.*;
import com.byzaneo.query.history.*;
import com.byzaneo.query.operand.*;
import com.byzaneo.query.operator.Operator;

/**
 * Turns a BQL search into a valid Apache Commons Collections {@link org.apache.commons.collections4.Predicate}.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @date 05/12/2012
 * @since 1.0 QRY-1
 */
public final class CommonsCollectionPredicateVisitor
    implements OperandVisitor<Object>, ClauseVisitor<CommonsCollectionPredicateVisitor.Result>, PredicateVisitor<Object> {
  private static final Logger log = getLogger(CommonsCollectionPredicateVisitor.class);

  /** Spring {@link SimpleTypeConverter} */
  private static final SimpleTypeConverter CONVERTER = new SimpleTypeConverter();

  /** SpEL expresion parser */
  private static final SpelExpressionParser SPEL_EXPRESSION_PARSER = new SpelExpressionParser();

  private final Class<?> beanType;

  private final transient Map<String, Map<String, Object>> nonPersistentProperties;

  /** Reflection utils */
  private static Reflections subTypesReflections;

  private static Reflections getSubTypesReflections() {
    if (subTypesReflections == null)
      subTypesReflections = new Reflections(new ConfigurationBuilder()
          .setUrls(forPackage("com.byzaneo"))
          .setScanners(new SubTypesScanner()));
    return subTypesReflections;
  }

  public CommonsCollectionPredicateVisitor(Class<?> beanType, Map<String, Map<String, Object>> nonPersistentProperties) {
    this.beanType = beanType;
    this.nonPersistentProperties = nonPersistentProperties;
  }

  public Predicate<Object> toPredicate(Clause clause) {
    return clause == null ? null
        : clause.accept(this)
            .getPredicate();
  }

  /*
   * -- OPERAND VISITOR --
   */

  /** @see com.byzaneo.query.operand.OperandVisitor#visit(com.byzaneo.query.operand.EmptyOperand) */
  @Override
  public Object visit(final EmptyOperand empty) {
    return null;
  }

  @Override
  public Object visit(final NullOperand nullOperand) {
    return "NULL";
  }

  /** @see com.byzaneo.query.operand.OperandVisitor#visit(com.byzaneo.query.operand.FunctionOperand) */
  @Override
  public Object visit(final FunctionOperand function) {
    // CAST: type casting functions (toXxx)
    if (accept(function)) {
      CastFunction cast = new CastFunction(function, true);
      if (cast.isCollectionValue())
        return visit(cast.asMultiValueOperand());
      else
        return visit(cast.asSingleValueOperand());
    }
    // Date functions
    if (DateFunctions.accept(function)) {
      DateFunctions dateFunction = new DateFunctions(function);
      return visit(dateFunction.asSingleValueOperand());
    }

    // not supported function
    throw new IllegalArgumentException("Function " + function.getDisplayString() + " is not supported");
  }

  /** @see com.byzaneo.query.operand.OperandVisitor#visit(com.byzaneo.query.operand.MultiValueOperand) */
  @Override
  public List<String> visit(final MultiValueOperand multiValue) {
    final List<String> r = new ArrayList<>(multiValue.getValues()
        .size());
    for (Operand operand : multiValue.getValues())
      r.add((String) operand.accept(this));
    return r;
  }

  /** @see com.byzaneo.query.operand.OperandVisitor#visit(com.byzaneo.query.operand.SingleValueOperand) */
  @Override
  public String visit(final SingleValueOperand singleValueOperand) {
    if (singleValueOperand.getLongValue() != null) {
      return singleValueOperand.getLongValue()
          .toString();
    }
    if (singleValueOperand.getDateValue() != null) {
      return singleValueOperand.getDateValue()
          .toString();
    }
    if (singleValueOperand.getBooleanValue() != null) {
      return singleValueOperand.getBooleanValue()
          .toString();
    }
    return singleValueOperand.getStringValue();
  }

  /*
   * -- OPERAND VISITOR --
   */

  /** @see com.byzaneo.query.clause.ClauseVisitor#visit(com.byzaneo.query.clause.AndClause) */
  @Override
  public Result visit(final AndClause andClause) {
    return visitMultiClause(andClause, AndClause.AND, ClausePrecedence.AND);
  }

  /** @see com.byzaneo.query.clause.ClauseVisitor#visit(com.byzaneo.query.clause.OrClause) */
  @Override
  public Result visit(final OrClause orClause) {
    return visitMultiClause(orClause, OrClause.OR, ClausePrecedence.OR);
  }

  /** @see com.byzaneo.query.clause.ClauseVisitor#visit(com.byzaneo.query.clause.NotClause) */
  @Override
  public Result visit(final NotClause notClause) {
    return new Result(
        notPredicate(notClause.getSubClause()
            .accept(this)
            .getPredicate()),
        ClausePrecedence.NOT);
  }

  /** @see com.byzaneo.query.clause.ClauseVisitor#visit(com.byzaneo.query.clause.TerminalClause) */
  @Override
  public Result visit(final TerminalClause clause) {
    return visitTerminalClause(clause);
  }

  /** @see com.byzaneo.query.clause.ClauseVisitor#visit(com.byzaneo.query.clause.WasClause) */
  @Override
  public Result visit(WasClause clause) {
    return visitTerminalClause(clause);
  }

  /** @see com.byzaneo.query.clause.ClauseVisitor#visit(com.byzaneo.query.clause.ChangedClause) */
  @Override
  public Result visit(ChangedClause clause) {
    return visitChangedClause(clause);
  }

  /** @see com.byzaneo.query.history.PredicateVisitor#visit(com.byzaneo.query.history.HistoryPredicate) */
  @Override
  public Object visit(HistoryPredicate predicate) {
    // TODO
    return predicate;
  }

  /*
   * PRIVATE
   */

  private Result visitMultiClause(final MultiClause multiClause, final String clauseName, final ClausePrecedence clausePrecedence) {
    @SuppressWarnings("unchecked")
    Predicate<Object>[] predicates = multiClause.getClauses()
        .stream()
        .map(c -> c.accept(this)
            .getPredicate())
        .toArray(size -> new Predicate[size]);
    Predicate<Object> p;
    switch (clauseName) {
    case OrClause.OR:
      p = anyPredicate(predicates);
      break;
    case AndClause.AND:
      p = allPredicate(predicates);
      break;
    default:
      throw new IllegalArgumentException("Unsupported clause: " + clauseName);
    }
    return new Result(p, clausePrecedence);
  }

  private Result visitTerminalClause(TerminalClause clause) { // NOSONAR : Cyclomatic Complexity > 10 because of Switch
    // LEFT HAND SIDE
    // property name
    final String leftHandSide = trimToEmpty(clause.getName());

    // SEARCH TEXT
    if (Constants.TEXT_FIELD.equals(clause.getName())) {
      throw new IllegalArgumentException("Unsupported full text search: " + clause.getName());
    }

    // RIGHT HAND SIDE
    final Object value = clause.getOperand()
        .accept(this);
    final Class<?> type = resolvePropertyType(leftHandSide);
    final Object rightHandSide;
    // conversion
    if (value != null && !String.class.equals(type)) {
      // single
      if (value instanceof String) {
        rightHandSide = convert(type, (String) value);
      }
      // multiple
      else if (value instanceof List) {
        List<?> source = (List<?>) value;
        List<Object> target = new ArrayList<>(source.size());
        for (Object o : source) {
          target.add(convert(type, (String) o));
        }
        rightHandSide = target;
      }
      // should never happened...
      else {
        throw new IllegalArgumentException("Unsupported operand value type: " + value);
      }
    }
    else {
      rightHandSide = value;
    }

    // OPERATOR
    Predicate<Object> predicate;
    switch (clause.getOperator()) {
    case EQUALS:
      predicate = equalPredicate(rightHandSide);
      break;
    case NOT_EQUALS:
      predicate = notPredicate(equalPredicate(rightHandSide));
      break;
    case IN:
      predicate = new InPredicate((Collection<?>) rightHandSide);
      break;
    case NOT_IN:
      predicate = notPredicate(new InPredicate((Collection<?>) rightHandSide));
      break;
    case IS:
      predicate = nullPredicate();
      break;
    case IS_NOT:
      predicate = notNullPredicate();
      break;
    case ILIKE:
      predicate = new LikePredicate(rightHandSide, false);
      break;
    case LIKE:
      predicate = new LikePredicate(rightHandSide, true);
      break;
    case NOT_LIKE:
      predicate = notPredicate(new LikePredicate(rightHandSide, false));
      break;
    case AFTER:
    case GREATER_THAN:
    case FROM:
    case GREATER_THAN_EQUALS:
    case BEFORE:
    case LESS_THAN:
    case TO:
    case LESS_THAN_EQUALS:
      predicate = new ComparablePredicate(clause.getOperator(), rightHandSide);
      break;
    default:
      throw new IllegalArgumentException("Unsupported operator: " + clause.getOperator());
    }

    return new Result(new QueryBeanPredicate(leftHandSide, predicate, nonPersistentProperties), ClausePrecedence.TERMINAL);
  }

  private Class<?> resolvePropertyType(String propertyName) {
    String[] props = split(propertyName, FIELD_SEPARATOR);
    if (props.length > 1) {
      Class<?> parent = beanType;
      for (int i = 0; i < props.length - 1; i++) {
        String prop = split(props[i], FIELD_NAME_SEPARATOR)[0]; // removes :joinKey
        parent = this.resolveType(parent, prop);
      }
      return resolveType(parent, props[props.length - 1]);
    }
    else {
      return resolveType(beanType, props[0]);
    }
  }

  private Class<?> resolveType(Class<?> beanType, String propertyName) {
    // concrete type
    if (!beanType.isInterface()) {
      try {
        return resolvePropertyType(beanType, propertyName);
      }
      catch (NoSuchFieldException e) {
        log.debug("Error getting resolveType() : {}", getRootCauseMessage(e));
        // should we throw an runtime exception?
        log.warn(e.getMessage());
        return Object.class;
      }
    }
    // interface
    // tries to find the property within the sub-classes
    final Set<?> subclasses = getSubTypesReflections().getSubTypesOf(beanType);
    final Set<Class<?>> types = new HashSet<>(subclasses.size());
    for (Object subclass : subclasses) {
      Class<?> subclassType = (Class<?>) subclass;
      if (!subclassType.isInterface()) {
        try {
          types.add(resolvePropertyType(subclassType, propertyName));
        }
        catch (NoSuchFieldException e) {
          log.debug("Error getting resolveType() {}", getRootCauseMessage(e));
        }
      }
    }
    if (types.isEmpty()) {
      // should we throw an runtime exception?
      log.debug("property type {} not found in {} sub-classes: {}",
          propertyName, beanType, subclasses);
      return Object.class;
    }
    if (types.size() > 1) {
      log.debug("found multiple ({}) property type {} in {} sub-classes: {}",
          types, propertyName, beanType, subclasses);
    }
    return types.iterator()
        .next();
  }

  private Class<?> resolvePropertyType(Class<?> beanType, String propertyName) throws NoSuchFieldException {
    try {
      PropertyDescriptor pd = getPropertyDescriptor(beanType, propertyName);
      if (pd == null) {
        if (nonPersistentProperties != null) {
          return resolveNonPersistentPropertyType(propertyName);
        }
        throw new NoSuchFieldException(format("property type %s not found in class %s", propertyName, beanType));
      }
      if (Collection.class.isAssignableFrom(pd.getPropertyType())) {
        Field stringListField = beanType.getDeclaredField(propertyName);
        ParameterizedType stringListType = (ParameterizedType) stringListField.getGenericType();
        return (Class<?>) stringListType.getActualTypeArguments()[0];
      }
      if (Map.class.isAssignableFrom(pd.getPropertyType())) {
        Field stringListField = beanType.getDeclaredField(propertyName);
        ParameterizedType stringListType = (ParameterizedType) stringListField.getGenericType();
        return (Class<?>) stringListType.getActualTypeArguments()[1];
      }
      // TODO manages other kind of collections
      return pd.getPropertyType();
    }
    catch (BeansException | SecurityException e) {
      throw new IllegalArgumentException("Impossible to resolve type for property '" + propertyName + "' in " + beanType, e);
    }
  }

  private Class<?> resolveNonPersistentPropertyType(String propertyName) throws NoSuchFieldException {
    for (Map.Entry<String, Map<String, Object>> entry : nonPersistentProperties.entrySet()) {
      if (entry.getValue()
          .containsKey(propertyName)) {
        return String.class;
      }
    }
    throw new NoSuchFieldException(format("Property type %s not found in non-persistent properties from document context", propertyName));
  }

  private <T> T convert(Class<T> type, String value) {
    T docStatusEntity;
    if ((docStatusEntity = convertDocStatusIfNecessary(type, value)) != null) {
      return docStatusEntity;
    }
    else if (type.isAssignableFrom(Date.class)) {
      try {
        DateTimeFormatter format = DateTimeFormatter.ISO_DATE_TIME;
        LocalDateTime date = LocalDateTime.parse((CharSequence) value, format);
        return type.cast(Date.from(date.atZone(ZoneId.of("UTC"))
            .toInstant()));
      }
      catch (Exception e) {
        if (Object.class.equals(type)) {
          log.debug("Object value '{}' is not a date ({})", value, e.getMessage());
        }
        else {
          throw new IllegalArgumentException("Invalid date format: " + value);
        }
      }
    }
    return CONVERTER.convertIfNecessary(value, type);
  }

  @SuppressWarnings("unchecked")
  private <T> T convertDocStatusIfNecessary(Class<T> type, String value) {
    try {
      Class<?> docStatusEntityInterfaceClass = Class.forName("com.byzaneo.xtrade.api.DocumentStatusEntityInterface");
      Class<?> docStatusEntityClass = Class.forName("com.byzaneo.xtrade.bean.DocumentStatusEntity");

      if (docStatusEntityInterfaceClass.equals(type)) {
        return (T) docStatusEntityClass.getConstructor(String.class)
            .newInstance(value);
      }
    }
    catch (Exception e) {
      log.debug("Error when converting Object value '{}' in DocumentStatusEntity ({})", value, e.getMessage());
      log.warn(e.getMessage());
    }
    return null;
  }

  private Result visitChangedClause(ChangedClause clause) {
    throw new IllegalArgumentException("Unsupported changed clause: " + clause);
  }

  public final static class Result {
    private final Predicate<Object> predicate;

    private final ClausePrecedence precedence;

    private Result(final Predicate<Object> predicate, final ClausePrecedence precedence) {
      this.predicate = predicate;
      this.precedence = precedence;
    }

    public Predicate<Object> getPredicate() {
      return predicate;
    }

    public ClausePrecedence getPrecedence() {
      return precedence;
    }

    @Override
    public String toString() {
      return reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
  }

  public final static class QueryBeanPredicate implements Predicate<Object> {
    private final String propertyName;

    private final Predicate<Object> propertyPredicate;

    private final transient Map<String, Map<String, Object>> nonPersistentProperties;

    public QueryBeanPredicate(String propertyName, Predicate<Object> predicate, Map<String, Map<String, Object>> nonPersistentProperties) {
      super();
      this.propertyName = propertyName;
      this.propertyPredicate = predicate;
      this.nonPersistentProperties = nonPersistentProperties;
    }

    @Override
    public boolean evaluate(Object object) {
      try {
        // nested property
        if (propertyName.contains(".")) {
          return evaluateValue(
              SPEL_EXPRESSION_PARSER
                  .parseExpression(substringBeforeLast(propertyName, "."))
                  .getValue(object),
              substringAfterLast(propertyName, "."));
        }
        return evaluateValue(object, propertyName);
      }
      catch (ExpressionException e) {
        throw new IllegalArgumentException(e);
      }
    }

    /**
     * Applies the {@link #propertyPredicate} to the fieldName value of the given object. Manages if the object is a collection or a simple
     * property
     */
    private boolean evaluateValue(Object o, String fieldName) {
      if (o == null)
        return false;
      if (o instanceof Collection<?>)
        return isNotEmpty(select((Collection<?>) o, new BeanPredicate(fieldName, propertyPredicate, nonPersistentProperties)));
      else
        return new BeanPredicate(fieldName, propertyPredicate, nonPersistentProperties).evaluate(o);
    }
  }

  /*
   * -- SPECIFIC PREDICATES --
   */

  public static final class InPredicate implements Predicate<Object> {
    private final Collection<?> values;

    public InPredicate(Collection<?> values) {
      this.values = values;
    }

    @Override
    public boolean evaluate(Object o) {
      return values != null && values.contains(o);
    }
  }

  public static final class LikePredicate implements Predicate<Object> {
    private final String value;

    private final boolean caseSensitive;

    public LikePredicate(Object value, boolean caseSensitive) {
      this.value = value != null ? value.toString() : null;
      this.caseSensitive = caseSensitive;
    }

    @Override
    public boolean evaluate(Object o) {
      return o != null && // NOSONAR : Conditional Operators too big
          ((!caseSensitive && containsIgnoreCase(o.toString(), remove(value, '*'))) ||
              (caseSensitive && contains(o.toString(), remove(value, '*'))));
    }
  }

  public static final class ComparablePredicate implements Predicate<Object> {
    private final Operator comparator;

    private final Comparable<?> value;

    public ComparablePredicate(Operator comparator, Object value) {
      this.comparator = comparator;
      this.value = (Comparable<?>) value;
    }

    @Override
    @SuppressWarnings("unchecked")
    public boolean evaluate(Object o) { // NOSONAR : Cyclomatic Complexity > 10 because of Switch
      if (o instanceof Comparable<?>) {
        int comp = ((Comparable<Object>) o).compareTo(value);
        switch (comparator) {
        case AFTER:
        case GREATER_THAN:
          return comp > 0;
        case FROM:
        case GREATER_THAN_EQUALS:
          return comp >= 0;
        case TO:
        case BEFORE:
        case LESS_THAN:
          return comp < 0;
        case LESS_THAN_EQUALS:
          return comp <= 0;
        default:
          return false;
        }
      }
      return false;
    }
  }

  public static final class BeanPredicate implements Predicate<Object> {
    private String propertyName;

    private Predicate<Object> predicate;

    private final transient Map<String, Map<String, Object>> nonPersistentProperties;

    public BeanPredicate(String propertyName, Predicate<Object> predicate, Map<String, Map<String, Object>> nonPersistentProperties) {
      this.propertyName = propertyName;
      this.predicate = predicate;
      this.nonPersistentProperties = nonPersistentProperties;
    }

    /**
     * Evaluates the given object by applying the {@link #getPredicate()} to a property value named by {@link #getPropertyName()}.
     *
     * @param object The object being evaluated
     * @return the result of the predicate evaluation
     * @throws IllegalArgumentException when the property cannot be evaluated
     */
    @Override
    public boolean evaluate(Object object) {

      boolean evaluation = false;

      try {
        Object propValue = SPEL_EXPRESSION_PARSER
            .parseExpression(substringBeforeLast(propertyName, "."))
            .getValue(object);
        // Object propValue = PropertyUtils.getProperty( object, propertyName );
        evaluation = predicate.evaluate(propValue);
      }
      catch (ExpressionException e) {
        if(nonPersistentProperties != null) {
          String docUuid = getDocumentUuid(object);
          if (docUuid != null && nonPersistentProperties.containsKey(docUuid)) {
            Map<String, Object> props = nonPersistentProperties.get(docUuid);
            return predicate.evaluate(props.get(propertyName));
          }
          return false;
        }
        throw new IllegalArgumentException(e);
      }

      return evaluation;
    }

    private String getDocumentUuid(Object doc) {
      try {
        Method method = doc.getClass()
            .getMethod("getUuid");
        Object uuid = method.invoke(doc);
        return uuid != null ? uuid.toString() : null;
      }
      catch (Exception e) {
        log.error("Problems getting the uuid from the document", e);
        return null;
      }
    }
  }

  @Override
  public Object visit(PartnerScopeOperand partnerScopeOperand) {
    throw new IllegalArgumentException("Operand " + PartnerScopeOperand.OPERAND_NAME + " is not supported");
  }

}
