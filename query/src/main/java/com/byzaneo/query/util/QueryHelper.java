package com.byzaneo.query.util;

import static com.byzaneo.query.order.SortOrder.ASC;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.select;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.slf4j.LoggerFactory.getLogger;

import java.util.*;
import java.util.stream.Collectors;

import javax.persistence.criteria.*;
import javax.persistence.criteria.Predicate;

import org.slf4j.Logger;

import com.byzaneo.query.Query;
import com.byzaneo.query.bql.parser.*;
import com.byzaneo.query.clause.*;
import com.byzaneo.query.function.CastFunction;
import com.byzaneo.query.order.SearchSort;
import com.byzaneo.query.visitor.*;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Dec 6, 2012
 * @version 1.0
 */
public class QueryHelper {

  private static final Logger log = getLogger(QueryHelper.class);

  private QueryHelper() {

  }

  private static final BqlStringSupport BQL = new BqlStringSupport(new DefaultBqlQueryParser());
  private static final BqlQueryParser PARSER = new DefaultBqlQueryParser();

  public static final String toBql(Query query) {
    return BQL.generateBqlString(query);
  }

  /**
   * @param bql
   * @return the {@link Query} created from the given BQL. If BQL is blank, <code>null</code> will be returned
   * @throws BqlParseException if parsing fails
   */
  public static final Query toQuery(String bql) {
    return isBlank(bql) ? null : PARSER.parseQuery(bql);
  }

  public static final List<Clause> toClauses(Clause clause) {
    if (clause == null)
      return emptyList();
    if (clause.getClauses()
        .isEmpty())
      return asList(clause);
    else {
      List<Clause> clauses = new ArrayList<>();
      for (Clause c : clause.getClauses()) {
        if (c.getClauses()
            .isEmpty())
          clauses.add(c);
        else
          clauses.addAll(toClauses(c));
      }
      return clauses;
    }
  }

  /*
   * -- JPA --
   */

  public static final Predicate toJpa(Query query, Root<?> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
    // DISTINCT...
    criteriaQuery.distinct(query.isDistinct());

    // ORDER BY...
    criteriaQuery.orderBy(toJpaOrders(query, root, criteriaBuilder));

    // WHERE...
    return toJpaPredicate(query, root, criteriaBuilder);
  }

  public static Predicate toJpaPredicate(Query query, Root<?> root, CriteriaBuilder criteriaBuilder) {
    return new JpaPredicateVisitor(root, criteriaBuilder, query.getContext()).toPredicate(query.getWhereClause());
  }

  /**
   * Based on the given query object create a native SQL string and add the necessary parameters to the given map
   * 
   * @param query
   * @param clazz The class mapping for the table being searched
   * @param tableAlias the table alias used in the SELECT statement
   * @param params
   * @return
   */
  public static String toNativeSqlPredicate(Query query, Class<?> clazz, String tableAlias, Map<String, Object> params) {
    if (query == null || query.getWhereClause() == null) {
      return "";
    }
    StringBuilder stringBuilder = new StringBuilder();
    String sql = new NativeSqlClauseVisitor(clazz, tableAlias, params).toNativeSql(query.getWhereClause());
    if (!isEmpty(sql)) {
      if (query.getWhereClause() instanceof TerminalClause) {
        // if only a single clause is in the query we need to manually add AND
        stringBuilder.append(" AND ");
      }
      stringBuilder.append(sql);
    }
    return stringBuilder.toString();
  }

  public static List<Order> toJpaOrders(Query query, Root<?> root, CriteriaBuilder criteriaBuilder) {
    if (query.getOrderByClause() == null || isEmpty(query.getOrderByClause()
        .getSearchSorts()))
      return emptyList();

    final List<Order> orders = new ArrayList<>(query.getOrderByClause()
        .getSearchSorts()
        .size());
    for (SearchSort sort : query.getOrderByClause()
        .getSearchSorts()) {
      Expression<?> sexp;
      if (sort.getField() != null) {
        sexp = root.get(sort.getField());
      }
      // function
      else {
        CastFunction cast = new CastFunction(sort.getFunction(), false);
        sexp = root.get(sort.getFunction()
            .getArgs()
            .get(0))
            .as(cast.getType());
      }
      if (sort.getOrder() == null || ASC.equals(sort.getSortOrder()))
        orders.add(criteriaBuilder.asc(sexp));
      else
        orders.add(criteriaBuilder.desc(sexp));
    }

    return orders;
  }

  /*
   * -- COMMONS COLLECTION --
   */

  public static final org.apache.commons.collections4.Predicate<Object> toCommonsCollectionPredicate(Query query, Class<?> type) {
    return new CommonsCollectionPredicateVisitor(type, null).toPredicate(query.getWhereClause());
  }

  public static final org.apache.commons.collections4.Predicate<Object> toCommonsCollectionPredicate(Query query, Class<?> type, Map<String, Map<String, Object>> nonPersistentProperties) {
    return new CommonsCollectionPredicateVisitor(type, nonPersistentProperties).toPredicate(query.getWhereClause());
  }

  /**
   * @param bql the BQL query to apply to the collection
   * @param type the type of the elements hold by the collection
   * @param collection containing the element to filter
   * @return the filtered list of the given collection's elements based on the BQL query. If collection is empty or <code>null</code>, empty
   *         Collection will be returned. If BQL is blank, collection will be returned
   * @see #search(Query, Class, Collection)
   */
  public static final <T> List<T> search(String bql, Class<T> type, Collection<T> collection) {
    return search(toQuery(bql), type, collection, null);
  }

  public static final <T> List<T> search(Query query, Class<T> type, Collection<T> collection) {
    return search(query, type, collection, null);
  }

  public static final <T> List<T> search(String bql, Class<T> type, Collection<T> collection, Map<String, Map<String, Object>> nonPersistentProperties) {
    return search(toQuery(bql), type, collection, nonPersistentProperties);
  }

  /**
   * @param query the BQL query to apply to the collection
   * @param type the type of the elements hold by the collection
   * @param collection containing the element to filter
   * @return the filtered list of the given collection's elements based on the BQL query. If collection is empty or <code>null</code>, empty
   *         Collection will be returned If query is <code>null</code>, collection will be returned
   */
  public static final <T> List<T> search(Query query, Class<T> type, Collection<T> collection, Map<String, Map<String, Object>> nonPersistentProperties) {
    if (isEmpty(collection))
      return emptyList();
    if (query == null)
      return new ArrayList<>(collection);
    try {
      final List<T> r = new ArrayList<T>(collection.size());
      select(collection, toCommonsCollectionPredicate(query, type, nonPersistentProperties), r);
      return r;
    }
    catch (Exception e) {
      log.error("Failed to execute query {} : {}", query, e.getMessage());
      return new ArrayList<>(collection);
    }
  }

  public static class SelectedRejected<T> {
    private final List<T> selected;
    private final List<T> rejected;

    public SelectedRejected(List<T> selected, List<T> rejected) {
      this.selected = selected;
      this.rejected = rejected;
    }

    public List<T> getSelected() {
      return selected;
    }

    public List<T> getRejected() {
      return rejected;
    }
  }

  /**
   * separates the elements of collection into two lists : SelectedRejected.selected for those who satisfy the query-predicate
   * SelectedRejected.rejected for thos who do not satisfy the query-predicate
   * 
   * @param bql - can not be null
   * @param type - can not be null
   * @param collection - can be null, will return two empty lists
   * @return
   */
  public static final <T> SelectedRejected<T> partition(String bql, Class<T> type, Collection<T> collection) {
    return partition(toQuery(bql), type, collection);
  }

  /**
   * separates the elements of collection into two lists : SelectedRejected.selected for those who satisfy the query-predicate
   * SelectedRejected.rejected for thos who do not satisfy the query-predicate
   * 
   * @param query - can be null, will return two empty lists
   * @param type - can not be null
   * @param collection - can be null, will return two empty lists
   * @return
   */
  public static final <T> SelectedRejected<T> partition(Query query, Class<T> type, Collection<T> collection) {
    if (isEmpty(collection) || query == null) {
      return new SelectedRejected<>(emptyList(), emptyList());
    }

    org.apache.commons.collections4.Predicate<Object> predicate = toCommonsCollectionPredicate(query, type);
    java.util.function.Predicate<T> javaPredicate = new java.util.function.Predicate<T>() {
      @Override
      public boolean test(T t) {
        return predicate.evaluate(t);
      }
    };
    Map<Boolean, List<T>> partition = collection.stream()
        .collect(Collectors.partitioningBy(javaPredicate));
    return new SelectedRejected<>(partition.get(Boolean.TRUE), partition.get(Boolean.FALSE));
  }
}
