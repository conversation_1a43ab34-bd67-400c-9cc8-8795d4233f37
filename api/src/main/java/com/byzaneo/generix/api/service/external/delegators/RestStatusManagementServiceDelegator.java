package com.byzaneo.generix.api.service.external.delegators;

import com.byzaneo.generix.api.bean.InvoiceRequestWithPaginationDTO;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.UUID;

public interface RestStatusManagementServiceDelegator {
  Response getInvoiceChangedStatus(HttpServletRequest request, String env, UUID requestId, InvoiceRequestWithPaginationDTO invoiceRequest);
}
