package com.byzaneo.generix.api.service.internal.delegators;

import com.byzaneo.generix.api.bean.SortOrderDto;
import com.byzaneo.generix.api.bean.payment.EReportingPaymentsDTO;
import com.byzaneo.generix.api.bean.transaction.EReportingTransactionDTO;
import com.byzaneo.xtrade.xcbl.transmission.TransmissionTypeCoded;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.UUID;

public interface RestEReportingServiceDelegator {

  Response getTransmissions(HttpServletRequest request, String bql, UUID requestId, TransmissionTypeCoded transmissionType, Integer limit,
      Integer offset, String sortBy, SortOrderDto order, boolean count, String portletId);

  Response addTransaction(HttpServletRequest request, EReportingTransactionDTO eReportingTransactionDTO);

  Response addPayment(HttpServletRequest request, EReportingPaymentsDTO eReportingPaymentDto);
}
