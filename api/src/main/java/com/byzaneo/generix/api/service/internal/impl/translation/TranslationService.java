/**
 *
 */
package com.byzaneo.generix.api.service.internal.impl.translation;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.security.bean.User;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;

import javax.ws.rs.core.Response;
import java.io.File;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

/**
 * <AUTHOR> GHOZZI <<EMAIL>>
 * @company Generix group
 * @date 17 april 2025
 */
public interface TranslationService {

  String SERVICE_NAME = "gnxTranslationService";

  Response exportCSVTranslation(File tmpFile);

  Response importTranslationsData(User user, String locale, Attachment file);

  Response exportFrontendTranslations(File tmpFile, String languages);

  Response importFrontendTranslations(User user, String languages, Attachment file);
}
