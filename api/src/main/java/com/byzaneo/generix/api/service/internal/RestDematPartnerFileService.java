package com.byzaneo.generix.api.service.internal;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.BqlUuidDto;
import com.byzaneo.generix.api.bean.portal.dematpartnerfile.*;
import com.byzaneo.generix.api.security.annotation.*;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.api.Right;
import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.cxf.jaxrs.ext.multipart.*;
import org.apache.cxf.jaxrs.model.wadl.Description;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.ws.rs.*;
import javax.ws.rs.core.*;
import java.util.*;

@Api(tags = "gnxRestDematPartnerFileService", description = "Operations about DematPartnerFile")

@Path(RestDematPartnerFileService.SERVICE_PATH)
public interface RestDematPartnerFileService {
  String SERVICE_NAME = "gnxRestDematPartnerFileService";
  String SERVICE_PATH = "";

  @GET
  @Path("/v1/environments/{env-code}/portlet/{portlet-id}/demat-partner-file")
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(summary = "Get data for Demat Partner File portlet", tags = { "Demat Partner File" })
  @ApiOperation(value = "Get data for Demat Partner File portlet", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token") })
  Response v1getDematPartnerFile(@Context HttpServletRequest request, @QueryParam("bql") String bql,
      @HeaderParam("Request-Id") UUID requestId, @Max(1000) @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset,
      @QueryParam("legalEntity") String legalEntity, @QueryParam("flowDirection") String flowDirection, @QueryParam("sortBy") String sortBy,
      @QueryParam("order") Sort.Direction order, @QueryParam("count") boolean count, @PathParam("portlet-id") String portletId,
      @QueryParam("locale") @NotNull String locale, @QueryParam("isCountEnabled") boolean isCountEnabled);

  @GET
  @Path("/v1/environments/{env-code}/portlet/{portlet-id}/document-types")
  @Produces({ "application/json" })
  @Consumes(MediaType.APPLICATION_JSON)
  @Operation(summary = "Get all the document types allowed", tags = { "Document types" })
  @ApiOperation(value = "Get all the document types allowed", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token") })
  Response v1getDocumentTypes(@Context HttpServletRequest request, @QueryParam("bql") String bql, @PathParam("env-code") String envCode,
      @HeaderParam("Request-Id") UUID requestId, @QueryParam("legalEntity") String legalEntity, @PathParam("portlet-id") String portletId,
      @QueryParam("locale") @NotNull String locale);

  @POST
  @Path("/v1/environments/{env-code}/portlet/{portlet-id}/exchanges")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(summary = "Save Exchange", tags = { "Exchanges" })
  @ApiOperation(value = "Save Exchange", authorizations = { @Authorization(value = "Bearer") },
      notes = "<p>Requires permission <b>Portlet_Demat_Partner_File = CREATE</b></p>")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  @Secured(items = {
      @Permission(resourceId = SecurityService.Resource.Portlet_Demat_Partner_File, right = Right.CREATE)
  })
  Response saveExchange(@Context HttpServletRequest request, @PathParam("env-code") String envCode,
      @PathParam("portlet-id") String portletId, @Valid ExchangeDTO exchangeDTO);

  @POST
  @Path("/v1/environments/{env-code}/portlet/{portlet-id}/exchange/export/template")
  @Produces(MediaType.APPLICATION_OCTET_STREAM)
  @Operation(summary = "Get template for exporting file exchange", tags = { "Exchanges" })
  @ApiOperation(value = "Get template for exporting file exchange", authorizations = { @Authorization(value = "Bearer") })
  Response getExportTemplateFileExchange(@Context HttpServletRequest request, @PathParam("env-code") String envCode,
      @PathParam("portlet-id") Long portletId, @QueryParam("locale") @NotNull String locale);

  @POST
  @Path("/v1/environments/{env-code}/portlet/{portlet-id}/exchange/import")
  @Consumes(MediaType.MULTIPART_FORM_DATA)
  @Operation(summary = "Import exchanges", tags = { "Exchanges" })
  @ApiOperation(value = "Import exchanges", authorizations = { @Authorization(value = "Bearer") },
      notes = "<p>Requires permission <b>Portlet_Demat_Partner_File = CREATE</b></p>")
  @Secured(items = {
      @Permission(resourceId = SecurityService.Resource.Portlet_Demat_Partner_File, right = Right.CREATE)
  })
  Response importExchanges(@Context HttpServletRequest request, @PathParam("env-code") String envCode,
      @PathParam("portlet-id") Long portletId, @QueryParam("locale") @NotNull String locale, @Multipart(value = "file") Attachment file);

  @GET
  @Path("/v1/environments/{env-code}/portlet/{portlet-id}/legal-entities")
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(summary = "Read legal entities of user", tags = { "Legal Entities" })
  @ApiOperation(value = "Read legal entities of user", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getUserAllLegalEntities(@Context HttpServletRequest request, @PathParam("env-code") String envCode,
      @PathParam("portlet-id") Long portletId, @QueryParam("locale") @NotNull String locale);

  @POST
  @Path("/v1/environments/{env-code}/export/portlet/{portlet-id}/dematPartner/legalEntity/{legalEntity}")
  @Consumes(MediaType.MULTIPART_FORM_DATA)
  @Produces(MediaType.APPLICATION_OCTET_STREAM)
  @Operation(summary = "Export data as CSV and PDF file", tags = { "Demat partner file" })
  @ApiOperation(value = "Export data as CSV and PDF file", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response exportDematPartnerAsCsvAndPdfFile(@Context HttpServletRequest request, @PathParam("env-code") String envCode,
      @Description("The BQL query used to ") @RequestBody BqlUuidDto bql, @QueryParam("sortBy") String sortBy,
      @QueryParam("order") Sort.Direction order, @PathParam("portlet-id") Long portletId, @QueryParam("locale") @NotNull String locale,
      @PathParam("legalEntity") String legalEntity, @QueryParam("file-type") FileType fileType,
      @QueryParam("export-archive") boolean exportArchive);

  @POST
  @Path("/v1/environments/{env-code}/portlet/{portlet-id}/history")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @Operation(summary = "Get revisions", tags = { "Revisions" })
  @ApiOperation(value = "Get revisions", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response v1getRevisionsByExchangeId(@Context HttpServletRequest request, @PathParam("env-code") String envCode,
      @PathParam("portlet-id") Long portletId, @RequestBody HistoryDTO exchangeId, @QueryParam("locale") @NotNull Locale locale);
}