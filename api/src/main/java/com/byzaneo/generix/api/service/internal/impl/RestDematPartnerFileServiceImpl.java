package com.byzaneo.generix.api.service.internal.impl;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.BqlUuidDto;
import com.byzaneo.generix.api.bean.portal.dematpartnerfile.*;
import com.byzaneo.generix.api.service.internal.RestDematPartnerFileService;
import com.byzaneo.generix.api.service.internal.delegators.RestDematPartnerFileServiceDelegator;
import com.byzaneo.generix.api.util.RestServiceHelper;
import lombok.RequiredArgsConstructor;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.*;

@Service(RestDematPartnerFileService.SERVICE_NAME)
@RequiredArgsConstructor
public class RestDematPartnerFileServiceImpl implements RestDematPartnerFileService {

  private final RestDematPartnerFileServiceDelegator dematPartnerFileServiceDelegator;

  @Transactional
  @Override
  public Response v1getDematPartnerFile(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset,
      String legalEntity, String flowDirection, String sortBy, Sort.Direction order, boolean count, String portletId, String localeAsString,
      boolean isCountEnabled) {
    return dematPartnerFileServiceDelegator.v1getDematPartnerFile(request, bql, requestId, limit, offset, legalEntity, flowDirection,
        sortBy, order, count, portletId, localeAsString, isCountEnabled);
  }

  @Override
  public Response v1getDocumentTypes(HttpServletRequest request, String bql, String envCode, UUID requestId, String legalEntity,
      String portletId, String locale) {
    return dematPartnerFileServiceDelegator.v1getDocumentTypes(request, bql, envCode, requestId, legalEntity, portletId, locale);
  }

  @Override
  public Response saveExchange(HttpServletRequest request, String envCode, String portletId, ExchangeDTO exchangeRateDto) {
    return dematPartnerFileServiceDelegator.v1saveExchange(request, envCode, portletId, exchangeRateDto);
  }

  @Override
  public Response getExportTemplateFileExchange(HttpServletRequest request, String envCode, Long portletId, String localeAsString) {
    return dematPartnerFileServiceDelegator.getExportTemplateFileExchange(request, envCode, portletId, localeAsString);
  }

  @Transactional(isolation = Isolation.READ_COMMITTED)
  @Override
  public Response importExchanges(HttpServletRequest request, String envCode, Long portletId, String localeAsString, Attachment file) {
    return dematPartnerFileServiceDelegator.importExchanges(request, envCode, portletId, localeAsString, file);
  }

  @Override
  @Transactional
  public Response getUserAllLegalEntities(HttpServletRequest request, String envCode, Long portletId, String locale) {
    return dematPartnerFileServiceDelegator.getUserAllLegalEntities(request, envCode, portletId, locale);
  }

  @Override
  public Response exportDematPartnerAsCsvAndPdfFile(HttpServletRequest request, String envCode, BqlUuidDto bqlList, String sortBy,
      Sort.Direction order, Long portletId, String localeAsString, String legalEntity, FileType fileType, boolean exportArchive) {
    return dematPartnerFileServiceDelegator.exportDematPartnerAsCsvAndPdfFile(request, envCode, bqlList, sortBy, order, portletId,
        localeAsString, legalEntity, fileType, exportArchive);
  }

  @Override
  public Response v1getRevisionsByExchangeId(HttpServletRequest request,String envCode, Long portletId, HistoryDTO historyDTO, Locale locale) {
    String login = SecurityContextHolder.getContext()
        .getAuthentication()
        .getName();
    return RestServiceHelper.getResponse(Response.Status.OK,dematPartnerFileServiceDelegator.v1getRevisionsByExchangeId(login, envCode, portletId, historyDTO, locale));
  }
}