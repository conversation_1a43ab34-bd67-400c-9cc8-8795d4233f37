/*
 * Copyright (c) 2023.
 * created by <PERSON><PERSON>
 */

package com.byzaneo.generix.api.service.internal.impl;

import com.byzaneo.generix.api.service.internal.RestEnvironmentService;
import com.byzaneo.generix.api.service.internal.delegators.RestEnvironmentServiceDelegator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;

@Service(RestEnvironmentService.SERVICE_NAME)
@RequiredArgsConstructor
public class RestEnvironmentServiceImpl implements RestEnvironmentService {

  private final RestEnvironmentServiceDelegator environmentServiceDelegator;

  public Response getValuesControl(String listId) {
    return environmentServiceDelegator.getValuesControl(listId);
  }

  @Override
  public Response getEnvironmentConfiguration(HttpServletRequest request, String envCode) {
    return environmentServiceDelegator.getEnvironmentConfiguration(request, envCode);
  }

  @Override
  public Response getCountry(HttpServletRequest request, String envCode) {
    return environmentServiceDelegator.getCountry(request, envCode);
  }

  @Override
  public Response getVersion(HttpServletRequest request) {
    return environmentServiceDelegator.getVersion(request);
  }

  @Override
  public Response getApiUrl(HttpServletRequest request, String url) {
    return environmentServiceDelegator.getApiUrl(request, url);
  }

  @Override
  public Response getFactorPartnerAddresses(HttpServletRequest request, String envCode) {
    return environmentServiceDelegator.getFactorPartnerAddresses(request, envCode);
  }

}
