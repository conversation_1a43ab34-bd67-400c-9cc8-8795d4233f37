package com.byzaneo.generix.api.service.internal;

import com.byzaneo.generix.api.bean.SortOrderDto;
import com.byzaneo.generix.api.bean.payment.EReportingPaymentsDTO;
import com.byzaneo.generix.api.bean.transaction.EReportingTransactionDTO;
import com.byzaneo.generix.api.security.annotation.*;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.api.Right;
import com.byzaneo.xtrade.xcbl.transmission.TransmissionTypeCoded;
import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Max;
import javax.ws.rs.*;
import javax.ws.rs.core.*;
import java.util.UUID;

@Api(tags = "gnxRestEReportingService", description = "Operations about EReporting")
@Path(RestEReportingService.SERVICE_PATH)
public interface RestEReportingService {

  String SERVICE_NAME = "gnxRestEReportingService";
  String SERVICE_PATH = "";

  @GET
  @Path("/v1/e-reporting/transmissions/portlet/{portlet-id}")
  @Produces({ MediaType.APPLICATION_JSON })
  @Operation(summary = "Search for transmissions", tags = { "EReporting" })
  @ApiOperation(value = "Search for transmissions", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token") })
  Response getTransmissions(@Context HttpServletRequest request, @QueryParam("bql") String bql, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("type") TransmissionTypeCoded transmissionType, @Max(1000) @DefaultValue("100") @QueryParam("limit") Integer limit,
      @QueryParam("offset") @DefaultValue("0") Integer offset, @QueryParam("sortBy") String sortBy, @QueryParam("order") SortOrderDto order,
      @QueryParam("count") boolean count, @PathParam("portlet-id") String portletId);

  @POST
  @Path("/v1/e-reporting/transactions")
  @Consumes(MediaType.APPLICATION_JSON)
  @Operation(summary = "Add new transaction", tags = { "EReporting" })
  @ApiOperation(value = "Add new transaction", authorizations = {
      @Authorization(value = "Bearer") }, notes = "<p>Requires permission <b>transactions = CREATE</b></p>")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token") })
  @Secured(items = { @Permission(resourceId = SecurityService.Resource.transactions, right = Right.CREATE) })
  Response addTransaction(@Context HttpServletRequest request, @RequestBody EReportingTransactionDTO eReportingTransactionDTO);

  @POST
  @Path("/v1/e-reporting/payment")
  @Consumes(MediaType.APPLICATION_JSON)
  @Operation(summary = "Add new payment", tags = { "EReporting" })
  @ApiOperation(value = "Add new payment", authorizations = {
      @Authorization(value = "Bearer") }, notes = "<p>Requires permission <b>payments = CREATE</b></p>")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token") })
  @Secured(items = { @Permission(resourceId = SecurityService.Resource.payments, right = Right.CREATE) })
  Response addPayment(@Context HttpServletRequest request, @RequestBody EReportingPaymentsDTO eReportingPaymentDto);

}
