package com.byzaneo.generix.api.service.external;

import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.security.annotation.*;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.api.Right;
import io.swagger.annotations.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.*;
import java.util.UUID;

@Api(tags = "gnxStatusManagementService", description = "Endpoints for managing invoice Statuses")
@Path(RestStatusManagementService.SERVICE_PATH)
public interface RestStatusManagementService {
  String SERVICE_NAME = "gnxStatusManagementApi";
  String SERVICE_PATH = "/";

  /**
   * V 2 process invoices response.
   *
   * @param request        HTTP request context.
   * @param env            GIS environment identifier.
   * @param requestId      Unique identifier for the request.
   * @param invoiceRequest Request body containing invoice processing parameters.
   * @return Response containing the result of invoice processing.
   */
  @POST
  @Path("/v2/environments/{env}/analytics/invoices/status")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @ApiOperation(
      value = "Process Invoices",
      notes = "get the list of status of the invoices that have changed status for a given period",
      response = InvoiceStatusManagementResponse.class
  )
  @ApiResponses({
      @ApiResponse(code = 200, message = "Successful operation", response = InvoiceStatusManagementResponse.class),
      @ApiResponse(code = 400, message = "Bad request", response = ErrorDTO.class),
      @ApiResponse(code = 403, message = "Forbidden", response = ErrorDTO.class),
      @ApiResponse(code = 422, message = "Unprocessable entity", response = ErrorDTO.class),
      @ApiResponse(code = 500, message = "Server internal error", response = ErrorDTO.class),
      @ApiResponse(code = 503, message = "Unavailable resource", response = ErrorDTO.class)

  })

  @Secured(items = {
      @Permission(resourceId = SecurityService.Resource.Api_permissions_get_invoice, right = Right.CREATE)
  })
  Response getInvoiceChangedStatus(
      @Context HttpServletRequest request,
      @PathParam("env") @NotNull String env,
      @HeaderParam("requestId") UUID requestId,
      @Valid InvoiceRequestWithPaginationDTO invoiceRequest);
}