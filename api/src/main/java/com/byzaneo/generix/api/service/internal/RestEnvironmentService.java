/*
 * Copyright (c) 2023.
 * created by S<PERSON>
 */

package com.byzaneo.generix.api.service.internal;

import io.swagger.annotations.*;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.*;

@Api(tags = "gnxRestEnvironmentService", description = "Operations about environment mostly used in angular project at startup")
@Path(RestEnvironmentService.SERVICE_PATH)
public interface RestEnvironmentService {
  String SERVICE_NAME = "gnxRestEnvironmentService";
  String SERVICE_PATH = "";

  @GET
  @Path("/v1/environments/{env-code}/configuration")
  @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
  @ApiOperation(value = "Get the environment configuration like the languages, default language, default locale, instance of the environment", authorizations = {
      @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getEnvironmentConfiguration(@Context HttpServletRequest request,@PathParam("env-code") String envCode);

  @GET
  @Path("/v1/environments/values-control/{list-id}")
  @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
  @ApiOperation(value = "Get the desired valueControl value from valueControl collection based on a list-id", authorizations = {
      @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getValuesControl(@PathParam("list-id") String listId);

  @GET
  @Path("/v1/environments/{env-code}/countries")
  @Produces(MediaType.APPLICATION_JSON)
  @ApiOperation(value = "Get all the countries used in AIO project", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getCountry(@Context HttpServletRequest request, @PathParam("env-code") String envCode);

  @GET
  @Path("/v1/version")
  @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
  @ApiOperation(value = "Get the app version (war version), check the version compatibility in angular project", authorizations = {
      @Authorization(value = "Bearer") })
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getVersion(@Context HttpServletRequest request);

  @GET
  @Path("/v1/url-api")
  @Produces({ MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN })
  @ApiOperation(value = "get the host, keyclock configuration, domain, pre-domain, jwt configuration, application type into the angular project", authorizations = {
      @Authorization(value = "Bearer") })
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getApiUrl(@Context HttpServletRequest request, @QueryParam("url") String url);

  @GET
  @Path("/v1/environments/{env-code}/factor/addresses")
  @Produces(MediaType.APPLICATION_JSON)
  @ApiOperation(value = "Get partner factor addresses", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getFactorPartnerAddresses(@Context HttpServletRequest request, @PathParam("env-code") String envCode);
}
