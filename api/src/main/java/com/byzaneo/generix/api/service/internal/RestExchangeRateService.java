package com.byzaneo.generix.api.service.internal;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.BqlIdsListDto;
import com.byzaneo.generix.api.bean.SortOrderDto;
import com.byzaneo.generix.api.bean.portal.AngularWrapperDTO;
import com.byzaneo.generix.api.bean.portal.ExchangeRatesTaskDto;
import com.byzaneo.generix.api.security.annotation.Permission;
import com.byzaneo.generix.api.security.annotation.Secured;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.xtrade.util.File;
import com.byzaneo.security.api.Right;
import com.hazelcast.spi.impl.operationservice.impl.responses.ErrorResponse;
import io.swagger.annotations.*;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Api(tags = "gnxRestExchangeRateService", description = "Operations about Exchange Rates")

@Path(RestExchangeRateService.SERVICE_PATH)
public interface RestExchangeRateService {
    String SERVICE_NAME = "gnxRestExchangeRatesService";
    String SERVICE_PATH = "";

    /**
     * Fetches exchange rates based on the provided environment and portlet ID.
     *
     * @param request The HTTP request context
     * @param bql The BQL query string to filter results
     * @param envCode The environment code
     * @param portletId The portlet ID
     * @param limit The maximum number of results to return
     * @param pageNumber The page number for pagination
     * @param sortBy The field to sort the results by
     * @param order The order of sorting (ascending/descending)
     * @param count A flag indicating if count is needed
     * @param locale The locale of the user
     * @param isCountEnabled A flag to enable counting
     * @return Response with exchange rates data
     */
    @GET
    @Path("/v1/environments/{env-code}/portlet/{portlet-id}/exchange-rates")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "Get Exchange Rates",
            notes = "This API endpoint retrieves exchange rates based on the provided environment and portlet ID. It supports filtering, " +
                    "sorting, and pagination to allow users to retrieve specific subsets of data. Requires authentication.\n\n" +
                    "**Requires permission**:\n" +
                    "**`Portlet_Referentiel_Exchange_Rates = READ`**",
            authorizations = {@Authorization(value = "Bearer")}
    )
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Exchange rates retrieved successfully", response = AngularWrapperDTO.class),
            @ApiResponse(code = 400, message = "Bad Request - Invalid input or missing parameters"),
            @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
            @ApiResponse(code = 404, message = "Not Found - The requested resource could not be found"),
            @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server", response = ErrorResponse.class),
    })
    @Transactional
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.Portlet_Referentiel_Exchange_Rates, right = Right.READ)
    })
    Response v1getExchangeRates(@Context HttpServletRequest request,
                                @ApiParam(value = "The BQL query string allows for advanced filtering of results. Use this parameter to specify conditions for the data you want to retrieve.\n" +
                                        "\n" +
                                        "Example: to search for currency put 'USD', ", required = false)
                                @QueryParam("bql") String bql,
                                @ApiParam(value = "The environment code identifies the specific environment (e.g., company or customer) for which exchange rates are being retrieved.", required = true)
                                @PathParam("env-code") String envCode,
                                @ApiParam(value = "Description: The portlet ID identifies the specific portlet or module within the environment.\n" +
                                        "\n" +
                                        "Example: 12345", required = true)
                                @PathParam("portlet-id") String portletId,
                                @ApiParam(value = "Limits the number of results returned in the response. The maximum allowed value is 500.", allowableValues = "range[0, 500]", required = true)
                                @Max(500) @QueryParam("limit") Integer limit,
                                @ApiParam(value = "Specifies the page number for paginated results. Use this in combination with limit to retrieve specific pages of data.", required = false)
                                @QueryParam("offset") Integer pageNumber,
                                @ApiParam(value = "Field to sort the results by", required = true)
                                @QueryParam("sortBy") String sortBy,
                                @ApiParam(value = "The order of sorting (ascending/descending)", required = true)
                                @QueryParam("order") SortOrderDto order,
                                @ApiParam(value = "Flag indicating if count is needed", required = false)
                                @QueryParam("count") boolean count,
                                @ApiParam(value = "Specifies the locale for the response data (e.g., language or region)", required = true)
                                @QueryParam("locale") @NotNull String locale,
                                @ApiParam(value = "Flag to enable counting", required = false)
                                @QueryParam("isCountEnabled") boolean isCountEnabled
    );

    /**
     * Deletes exchange rates based on the provided list of BQL IDs.
     *
     * @param request The HTTP request context
     * @param listIds List of BQL IDs for the exchange rates to delete
     * @param localeAsString The locale of the user
     * @return Response indicating the result of the delete operation
     */
    @DELETE
    @Path("/v1/environments/delete-exchange-rates")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Delete Exchange Rates",
            notes = "Deletes exchange rates based on provided BQL list. Requires authentication.\n\n" +
                    "**Requires permission**:\n" +
                    "**`Portlet_Referentiel_Exchange_Rates = DELETE`**",
            authorizations = {@Authorization(value = "Bearer")})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Exchange rates deleted successfully"),
            @ApiResponse(code = 400, message = "Bad Request - Invalid input or missing parameters"),
            @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
            @ApiResponse(code = 404, message = "Not Found - The specified exchange rates could not be found"),
            @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server", response = ErrorResponse.class),
    })
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.Portlet_Referentiel_Exchange_Rates, right = Right.DELETE)
    })
    Response deleteExchangeRates(@Context HttpServletRequest request,
                                 @ApiParam(
                                         value = "List of BQL IDs for the exchange rates to delete. Each ID should correspond to a specific exchange rate entry.",
                                         required = true
                                 )
                                 @Valid BqlIdsListDto listIds,
                                 @ApiParam(
                                         value = "Specifies the locale for the response data (e.g., language or region). Example: 'en'",
                                         required = true
                                 )
                                 @QueryParam("locale") @NotNull String localeAsString);

    /**
     * Imports exchange rates from a file.
     *
     * @param request The HTTP request context
     * @param portletId The portlet ID where the data will be imported
     * @param locale The locale of the user
     * @param file The file containing exchange rate data
     * @return Response indicating the result of the import operation
     */
    @POST
    @Path("/v1/exchange/portlets/{portlet-id}/import")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiOperation(value = "Import Exchange Rates",
            notes = "Import exchange rates via file upload. Requires authentication.\n\n" +
                    "**Requires permission**:\n" +
                    "**`Portlet_Referentiel_Exchange_Rates = EXECUTE`**",
            authorizations = {@Authorization(value = "Bearer")})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Exchange rates imported successfully"),
            @ApiResponse(code = 400, message = "Bad Request - Invalid input, unsupported file format, or missing parameters"),
            @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
            @ApiResponse(code = 404, message = "Not Found - The specified portlet ID could not be found"),
            @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server", response = ErrorResponse.class),
    })
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.Portlet_Referentiel_Exchange_Rates, right = Right.EXECUTE)
    })
    Response importExchangeRates(
            @Context HttpServletRequest request,
            @ApiParam(
                    value = "The portlet ID where the exchange rate data will be imported. Example: 12345",
                    required = true
            )
            @PathParam("portlet-id") Long portletId,
            @ApiParam(
                    value = "Specifies the locale for the response data (e.g., language or region). Example: 'en'",
                    required = true
            )
            @QueryParam("locale") @NotNull String locale,
            @ApiParam(
                    value = "The file containing exchange rate data to be imported. Ensure the file is in the supported format (e.g., CSV, Excel).",
                    required = true
            )
            @Multipart(value = "file") Attachment file

    );

    /**
     * Fetches an export template for exchange rates.
     *
     * @param request The HTTP request context
     * @return Response with the export template file
     */
    //TODO no need the {env-code}
    @POST
    @Path("/v1/environments/{env-code}/exchange-rates/export/template")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(value = "Get Export Template File Export Exchange Rates",
            notes = "Export exchange rates as an Excel file. Requires authentication.\n\n" +
                    "**Requires permission**:\n" +
                    "**`Portlet_Referentiel_Exchange_Rates = EXECUTE`**",
            authorizations = {@Authorization(value = "Bearer")})
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.Portlet_Referentiel_Exchange_Rates, right = Right.EXECUTE)
    })
    Response getExportTemplateFileExchangeRates(@Context HttpServletRequest request);

    /**
     * Exports exchange rates as an Excel file.
     *
     * @param request The HTTP request context
     * @param envCode The environment code
     * @param portletId The portlet ID
     * @param bql The BQL query string to filter results
     * @param limit The maximum number of results to return
     * @param offset The page number for pagination
     * @param legalEntity The legal entity to filter results
     * @param sortBy The field to sort the results by
     * @param order The order of sorting (ascending/descending)
     * @param localeAsString The locale of the user
     * @param fileType The file type for export (e.g., Excel, CSV)
     * @return Response with the exported exchange rates as a file
     */
    @POST
    @Path("/v1/environments/{env-code}/portlet/{portlet-id}/exchange-rates/export")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(value = "Export Exchange Rates",
            notes = "Export exchange rates as an Excel file. Requires authentication.\n\n" +
                    "**Requires permission**:\n" +
                    "**`Portlet_Referentiel_Exchange_Rates = EXECUTE`**",
            authorizations = {@Authorization(value = "Bearer")})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "File exported successfully", response = File.class),
            @ApiResponse(code = 400, message = "Bad Request - Invalid input or parameters"),
            @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
            @ApiResponse(code = 404, message = "Not Found - The requested resource could not be found"),
            @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server", response = ErrorResponse.class),
    })
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.Portlet_Referentiel_Exchange_Rates, right = Right.EXECUTE)
    })
    Response exportExchangeRatesAsExcelFile(
            @Context HttpServletRequest request,
            @ApiParam(value = "The environment code identifying the specific environment", required = true)
            @PathParam("env-code") String envCode,
            @ApiParam(value = "The portlet ID identifying the specific module for the export", required = true)
            @PathParam("portlet-id") Long portletId,
            @ApiParam(value = "BQL query string for advanced filtering (e.g., currency or date range)", required = false)
            @QueryParam("bql") String bql,
            @ApiParam(value = "Maximum number of results to return", required = false)
            @QueryParam("limit") Integer limit,
            @ApiParam(value = "Pagination offset for retrieving specific results", required = false)
            @QueryParam("offset") Integer offset,
            @ApiParam(value = "Filter results by a specific legal entity", required = false)
            @QueryParam("legal-entity") String legalEntity,
            @ApiParam(value = "Field to sort the results by (e.g., currency, date)", required = false)
            @QueryParam("sortBy") String sortBy,
            @ApiParam(value = "Sorting order: ascending or descending", required = false)
            @QueryParam("order") SortOrderDto order,
            @ApiParam(value = "Locale for translation (e.g., en)", required = true)
            @QueryParam("locale") @NotNull String localeAsString,
            @ApiParam(value = "File type for the export (e.g., Excel or CSV)", required = true)
            @QueryParam("file-type") FileType fileType
    );

    /**
     * Saves exchange rate information.
     *
     * @param request The HTTP request context
     * @param exchangeRateDto The data transfer object containing the exchange rate details
     * @return Response indicating the result of the save operation
     */
    @POST
    @Path("/v1/environments/exchange-rates")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Save Exchange Rate",
            notes = "Save OR update Exchange Rate. Requires authentication.\n\n" +
                    "**Requires at least one of the following permissions:**\n\n" +
                    "- `Portlet_Referentiel_Exchange_Rates = CREATE` **OR**\n" +
                    "- `Portlet_Referentiel_Exchange_Rates = UPDATE`",
            authorizations = {@Authorization(value = "Bearer")})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Exchange rate saved successfully", response = AngularWrapperDTO.class),
            @ApiResponse(code = 400, message = "Bad Request - Invalid input or missing required fields", response = ErrorResponse.class),
            @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
            @ApiResponse(code = 404, message = "Not Found - The resource does not exist"),
            @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server", response = ErrorResponse.class)
    })
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.Portlet_Referentiel_Exchange_Rates, right = Right.CREATE),
            @Permission(resourceId = SecurityService.Resource.Portlet_Referentiel_Exchange_Rates, right = Right.UPDATE)
            })
    Response saveExchangeRate(@Context HttpServletRequest request,
                              @ApiParam(
                                      value = "Exchange rate details including currency pair, rate, and effective date",
                                      required = true
                              )
                              @Valid ExchangeRatesTaskDto exchangeRateDto);

}


