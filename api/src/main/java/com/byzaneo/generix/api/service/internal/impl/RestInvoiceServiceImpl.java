package com.byzaneo.generix.api.service.internal.impl;

import static com.byzaneo.commons.util.GsonHelper.getGson;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.*;

import org.apache.cxf.jaxrs.ext.multipart.*;
import org.slf4j.Logger;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.portal.DocumentTimelineDto;
import com.byzaneo.generix.api.service.internal.RestInvoiceService;
import com.byzaneo.generix.api.service.internal.delegators.RestInvoiceServiceDelegator;
import com.byzaneo.generix.api.util.RestServiceHelper;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.DocumentTimeline;

import lombok.RequiredArgsConstructor;

@Service(RestInvoiceService.SERVICE_NAME)
@RequiredArgsConstructor
public class RestInvoiceServiceImpl implements RestInvoiceService {

  private static final Logger log = getLogger(RestInvoiceServiceImpl.class);

  private final RestInvoiceServiceDelegator invoiceServiceDelegator;

  @Override
  @Transactional(readOnly = true)
  public Response v1EnvironmentsEnvCodeInvoicesGet(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset,
      BaseType base, String flowDirection, String sortBy, SortOrderDto order, boolean count, String portletId, String localeAsString,
      boolean isCountEnabled, String uuid) {
    return invoiceServiceDelegator.v1EnvironmentsEnvCodeInvoicesGet(request, bql, requestId, limit, offset, base, flowDirection, sortBy,
        order, count, portletId, localeAsString, isCountEnabled, uuid);

  }

  @Override
  @Transactional(readOnly = true)
  public Response v2EnvironmentsEnvCodeInvoicesGet(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset,
      String flowDirection, String sortBy, SortOrderDto order, String portletId, String localeAsString,
      List<String> selectedSecurityFields, boolean showOnlyWorkflow) {
    return invoiceServiceDelegator.v2EnvironmentsEnvCodeInvoicesGet(request, bql, requestId, limit, offset, flowDirection, sortBy,
        order, portletId, localeAsString, selectedSecurityFields, showOnlyWorkflow);
  }

  @Override
  @Transactional(readOnly = true)
  public Response v2EnvironmentsEnvCodeInvoicesCount(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset,
      String flowDirection, String sortBy, SortOrderDto order, String localeAsString,
      List<String> selectedSecurityFields, boolean showOnlyWorkflow) {
    return invoiceServiceDelegator.v2EnvironmentsEnvCodeInvoicesCount(request, bql, requestId, limit, offset, flowDirection, sortBy,
        order, localeAsString, selectedSecurityFields, showOnlyWorkflow);
  }

  @Override
  public Response getInvoiceStages(HttpServletRequest request, String localeAsString) {
    return invoiceServiceDelegator.getInvoiceStages(request, localeAsString);
  }

  public Response v1GetAllTimelinesForInvoice(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return invoiceServiceDelegator.v1GetAllTimelinesForInvoice(request, requestId, base, portletId, uuid);
  }

  @Override
  public Response getExportedInvoicesExcelFiles(HttpServletRequest request, String envCode, String bql, String sortBy,
      SortOrderDto order, Long portletId, String localeAsString, FileType fileType,
      boolean allIndexesSelected, boolean exportArchive,
      boolean printSingleDocument) {
    return invoiceServiceDelegator.getExportedInvoicesExcelFiles(request, envCode, bql, sortBy, order, portletId, localeAsString, fileType, allIndexesSelected,exportArchive, printSingleDocument);
  }

  @Override
  @Transactional(readOnly = true)
  public Response getInvoiceStepsLifeCycle(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return invoiceServiceDelegator.getInvoiceStepsLifeCycle(request, requestId, base, portletId, uuid);
  }

  @Override
  @Transactional(readOnly = true)
  public Response getAllAttachmentsFilesWithType(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return invoiceServiceDelegator.getAllAttachmentsFilesWithType(request, requestId, base, portletId, uuid);
  }

  @Override
  @Transactional(readOnly = true)
  public Response v2GetAllAttachmentsFilesWithType(HttpServletRequest request, UUID requestId, BaseType base, String portletId,
      String uuid) {
    return invoiceServiceDelegator.v2GetAllAttachmentsFilesWithType(request, uuid);
  }

  @Override
  @Transactional(readOnly = true)
  public Response getAttachmentsFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return invoiceServiceDelegator.getAttachmentsFiles(request, requestId, base, portletId, uuid);

  }

  @Override
  public Response updatePaymentStatusForSelectedInvoices(HttpServletRequest request, String envCode, String bql, Long portletId,
      String locale) {
    return invoiceServiceDelegator.updatePaymentStatusForSelectedInvoices(request, envCode, bql, portletId, locale);
  }

  @Override
  @Transactional(readOnly = true)
  public Response getReportedDataAttachmentsFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId,
      String uuid) {
    return invoiceServiceDelegator.getReportedDataAttachmentsFiles(request, requestId, base, portletId, uuid);

  }

  @Override
  @Transactional(readOnly = true)
  public Response getErrorFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return invoiceServiceDelegator.getErrorFiles(request, requestId, base, portletId, uuid);

  }

  @Override
  @Transactional(readOnly = true)
  public Response getTaxOriginalFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return invoiceServiceDelegator.getTaxOriginalFiles(request, requestId, base, portletId, uuid);

  }

  @Override
  @Transactional(readOnly = true)
  public Response getRelatedFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      boolean fromLifeCycle) {
    return invoiceServiceDelegator.getRelatedFiles(request, requestId, base, portletId, uuid, fromLifeCycle);

  }

  @Override
  @Transactional(readOnly = true)
  public Response getRectificationFiles(HttpServletRequest request, UUID requestId, BaseType base,
      String portletId, String uuid) {
    return invoiceServiceDelegator.getRectificationFiles(request, requestId, base, portletId, uuid);
  }

  @Override
  public Response getTimeLineXSL(HttpServletRequest request, UUID requestId, BaseType base, String filename, String locale) {
    return invoiceServiceDelegator.getTimeLineXSL(request, requestId, base, filename,locale);
  }

  @Override
  public Response getLifeCycleXSL(HttpServletRequest request, UUID requestId, BaseType base, String filename, String locale) {
    return invoiceServiceDelegator.getLifeCycleXSL(request, requestId, base, filename, locale);
  }

  @Override
  public Response getAttachedFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String path) {
    return invoiceServiceDelegator.getAttachedFile(request, requestId, base, portletId, path);
  }

  @Override
  @Transactional(readOnly = true)
  public Response getAttachmentsZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      Boolean relatedFiles, String locale, boolean fromLifeCycle) {
    return invoiceServiceDelegator.getAttachmentsZipFile(request, requestId, base, portletId, uuid, relatedFiles, locale, fromLifeCycle);
  }

  @Override
  @Transactional(readOnly = true)
  public Response v2GetAttachmentsZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      Boolean relatedFiles, String locale, boolean fromLifeCycle) {
    return invoiceServiceDelegator.v2GetAttachmentsZipFile(request, requestId, base, portletId, uuid, relatedFiles, locale, fromLifeCycle);
  }

  @Override
  @Transactional(readOnly = true)
  public Response getDeclaredAndErrorFilesInZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId,
      String uuid, String attachedFileType) {
    return invoiceServiceDelegator.getDeclaredAndErrorFilesInZipFile(request, requestId, base, portletId, uuid, attachedFileType);
  }

  @Override
  @Transactional(readOnly = true)
  public Response getTaxOriginalZipFile(HttpServletRequest request, String portletId, boolean allIndexesSelected, List<String> uuids,
      boolean hasMissingTaxOriginal, boolean hasNonEmptyTaxOriginal) {
    return invoiceServiceDelegator.getTaxOriginalZipFile(request, portletId, allIndexesSelected, uuids, hasMissingTaxOriginal,
        hasNonEmptyTaxOriginal);
  }

  @Override
  @Transactional(readOnly = true)
  public Response triggerActions(HttpServletRequest request, UriInfo uriInfo, String envCode, Long portletId, String sortBy,
      Sort.Direction order) {
    return invoiceServiceDelegator.triggerActions(request, uriInfo, envCode, portletId, sortBy, order);
  }

  @Override
  @Transactional(readOnly = true)
  public Response triggerComplianceActions(HttpServletRequest request, UriInfo uriInfo, String envCode, Long portletId) {
    return invoiceServiceDelegator.triggerComplianceActions(request, uriInfo, envCode, portletId);
  }


  @Override
  public Response removeInvoice(HttpServletRequest request, String envCode, String bql, Long portletId, String localeAsString,
      FileType fileType, boolean allIndexesSelected) {
    return invoiceServiceDelegator.removeInvoice(request, envCode, bql, portletId, localeAsString, fileType, allIndexesSelected);
  }

  @Override
  @Transactional
  public Response removeAttachment(HttpServletRequest request, String envCode, String urlAttachment, String uuid, String portletId,
      String locale) {
    return invoiceServiceDelegator.removeAttachment(request, envCode, urlAttachment, uuid, portletId, locale);
  }

  @Override
  @Transactional
  public Response saveAttachedFile(HttpServletRequest request, Long portletId, String localeAsString, UUID uuid, Attachment file,
      String comment) {

    return invoiceServiceDelegator.saveAttachedFile(request, portletId, localeAsString, uuid, file, comment);
  }

  @Override
  @Transactional(readOnly = true)
  public Response correctInvoice(HttpServletRequest request, UUID requestId, String portletId, String uuid, String localeAsString) {
    return invoiceServiceDelegator.correctInvoice(request, requestId, portletId, uuid, localeAsString);

  }


  @Override
  public Response saveCorrectInvoice(HttpServletRequest request, UUID requestId, String portletId, String invoiceUUID,
      String localeAsString, List<SaveInvoiceCorrectionDTO> listCorrectedInvoiceXcbl) {
    return invoiceServiceDelegator.saveCorrectInvoice(request, requestId, portletId, invoiceUUID, localeAsString, listCorrectedInvoiceXcbl);

  }

  @Transactional
  public Response validateWorkflowDocuments(HttpServletRequest request, String bql, UUID requestId, Integer limit,
      Integer offset, String flowDirection, String sortBy, Sort.Direction order, boolean count, String portletId, String localeAsString,
      boolean allIndexesSelected, Integer countSelected, boolean showOnlyWorkflow) {

    return invoiceServiceDelegator.validateWorkflowDocuments(request, bql, requestId, limit, offset, flowDirection, sortBy, order, count,
        portletId, localeAsString, allIndexesSelected, countSelected, showOnlyWorkflow);
  }

  @Transactional
  public Response refuseWorkflowDocuments(HttpServletRequest request, String bql, UUID requestId, Integer limit,
      Integer offset, String flowDirection, String sortBy, Sort.Direction order, boolean count, String portletId, String localeAsString,
      boolean allIndexesSelected, Integer countSelected, boolean showOnlyWorkflow) {
    return invoiceServiceDelegator.refuseWorkflowDocuments(request, bql, requestId, limit, offset, flowDirection, sortBy, order, count,
        portletId, localeAsString, allIndexesSelected, countSelected, showOnlyWorkflow);
  }

  @Override
  public Response getCompliance(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid) {
    return invoiceServiceDelegator.getCompliance(request, requestId, base, portletId, uuid);
  }

  @Override
  @Transactional
  public Response addActionInTimeline(HttpServletRequest request, UUID requestId, String uuid, DocumentTimelineDto documentTimelineDto) {
    return invoiceServiceDelegator.addActionInTimeline(request, requestId, uuid, documentTimelineDto);
  }

  @Override
  @Transactional
  public Response addActionsInTimeline(HttpServletRequest request, DocumentTimelineDto documentTimelineDto) {
    return invoiceServiceDelegator.addActionsInTimeline(request, documentTimelineDto);
  }

  @Override
  public Response getAllowedActions(HttpServletRequest request, String invoiceUuid) {
    return invoiceServiceDelegator.getAllowedActions(request, invoiceUuid);
  }

  @Override
  public Response v1GetInvoiceHeaderByUuid(HttpServletRequest request, String uuid) {
    return invoiceServiceDelegator.v1GetInvoiceHeaderByUuid(request, uuid);
  }

  @Override
  @Transactional
  public Response attachMultiFiles(HttpServletRequest request, Long portletId, UUID invoiceUid,
      MultipartBody documentDetail, UUID requestId, String localeAsString) {

    return invoiceServiceDelegator.attachMultiFiles(request, portletId, invoiceUid, documentDetail, requestId, localeAsString);
  }

  @Override
  @Transactional
  public Response updateInvoiceStatus(HttpServletRequest request, String localeAsString, String uuid, String statusInvoiceValidation) {
    return invoiceServiceDelegator.updateInvoiceStatus(request, localeAsString, uuid, statusInvoiceValidation);
  }

  @Override
  public Response canPerformAction(HttpServletRequest request, UUID requestId, String actionName) {
    String userLogin = RestServiceHelper.getUserFromRequest(request);
    Boolean canPerformAction = invoiceServiceDelegator.canPerformAction(userLogin, actionName);
    if (canPerformAction != null) {
      return RestServiceHelper.getResponse(Response.Status.OK, canPerformAction);
    }
    return RestServiceHelper.getResponseOnError("400", "Bad Request.", Response.Status.BAD_REQUEST, requestId);
  }

  @Override
  public Response fetchRossumUrl(HttpServletRequest request, UUID requestId, String rossumId, String uuid, String type) {
    String userLogin = RestServiceHelper.getUserFromRequest(request);
    String requestUrl = request.getHeader("origin");
    try {
      String rossumUrl = invoiceServiceDelegator.fetchRossumUrl(userLogin, requestUrl, rossumId, uuid, type);
      return RestServiceHelper.getResponse(Response.Status.OK, rossumUrl);
    }
    catch (Exception e) {
      return RestServiceHelper.getResponseOnError("500", MessageHelper.getMessage("gnxxcblinvlbls.rossum_fail",
          "Can't open Rossum studio", null, e.getMessage()), Response.Status.INTERNAL_SERVER_ERROR, requestId);
    }
  }

  @Override
  public Response updateInvoicesStatus(HttpServletRequest request, UUID requestId, String statusCode, String timelineAction,
      String body) {
    String userLogin = RestServiceHelper.getUserFromRequest(request);
    try {
      List<String> invoices = getGson().fromJson(body, List.class);
      if (isEmpty(invoices))
        return Response.status(Response.Status.OK)
            .build();
      DocumentStatus status = DocumentStatus.valueOf(statusCode);
      DocumentTimeline.TimelineAction action = isNotBlank(timelineAction) ? DocumentTimeline.TimelineAction.valueOf(timelineAction) : null;

      invoiceServiceDelegator.updateInvoicesStatus(userLogin, invoices, status, action);
      return Response.status(Response.Status.OK)
          .build();
    }
    catch (Exception e) {
      log.error("Error while updating invoices : ", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .build();
    }
  }
}
