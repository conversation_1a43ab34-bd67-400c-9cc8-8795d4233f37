package com.byzaneo.generix.api.bean.portal;

import java.util.*;

import javax.xml.bind.annotation.XmlRootElement;

import org.bson.types.ObjectId;

import com.byzaneo.generix.service.repository.bean.aap.enums.AutomaticCounterpartStatus;

import lombok.*;

@Data
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "AapRefSupplier")
public class AapRefSupplierDTO {
  private String name;
  private String code;
  private String vat;
  private String registration;
  private String duns;
  private String city;
  private String streetName;
  private String postalCode;
  private String country;
  private Date creation;
  private ObjectId aapReferentialLineId;
  private String finalAccountNumber;
  private String auxiliaryAccount;
  private String accountLabel;
  private String legalEntityId;
  private String dematPartnerId;
  private String legalEntityCode;
  private Map<String, String> aapStatus = null;
  private Integer counterpartAccountId;
  private AutomaticCounterpartStatus automaticCounterpart;
}
