package com.byzaneo.generix.api.service.internal;

import com.byzaneo.generix.api.bean.SortOrderDto;
import com.byzaneo.generix.api.security.annotation.*;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.api.Right;
import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.cxf.jaxrs.ext.multipart.*;
import org.glassfish.jersey.media.multipart.FormDataParam;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.*;
import javax.ws.rs.*;
import javax.ws.rs.core.*;
import java.util.*;

@Api(tags = "gnxRestPeriodicReportService", description = "Operations about Periodic Report services")

@Path(RestPeriodicReportService.SERVICE_PATH)
public interface RestPeriodicReportService {
    String SERVICE_NAME = "restRestPeriodicReportService";
    String SERVICE_PATH = "";

    @GET
    @Path("/v1/environments/periodic-report/portlet/{portlet-id}")
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Search for periodic report", tags = {"Invoice Management"})
    @ApiOperation(value = "Search for periodic report", authorizations = { @Authorization(value = "Bearer") },
        notes = "<p>Requires permission <b>Manage_Periodic_reports = READ</b></p>")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    @Secured(items = {
        @Permission(resourceId = SecurityService.Resource.Manage_Periodic_reports, right = Right.READ)
    })
    Response v1getPeriodicReport(@Context HttpServletRequest request,
                                   @QueryParam("bql") String bql, @HeaderParam("Request-Id") UUID requestId, @Max(1000)
                                   @QueryParam("limit") Integer limit, @QueryParam("offset") Integer offset, @QueryParam("legalEntity") String legalEntity, @QueryParam("flowDirection") String flowDirection, @QueryParam("sortBy") String sortBy, @QueryParam("order") SortOrderDto order, @QueryParam("count") boolean count,
                                   @PathParam("portlet-id") String portletId, @QueryParam("locale") @NotNull String locale, @QueryParam("isCountEnabled") boolean  isCountEnabled);

    @POST
    @Path("/v1/environments/periodic-report/portlets/{portlet-id}")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @ApiOperation(
            value = "Set periodic report for the given portlet",
        notes = "This endpoint allows you to upload a periodic report file for a specific portlet. The report is associated with a portlet ID, and a DTO string representing the periodic report can also be provided.\n" +
            "<p>Requires permission <b>Manage_Periodic_reports = CREATE</b></p>",
            authorizations = {@Authorization(value = "Bearer")}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    @Secured(items = {
        @Permission(resourceId = SecurityService.Resource.Manage_Periodic_reports, right = Right.CREATE)
    })
    Response v1SetPeriodicReport(
            @Context HttpServletRequest request,
            @PathParam("portlet-id") Long portletId,
            @QueryParam("locale") @NotNull String locale,
            @Multipart(value = "file") Attachment file,
            @FormDataParam("periodicReportDtoAsString") String periodicReportDtoAsString

    );

    @DELETE
    @Path("/v1/environments/periodic-report/delete/{uuid}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "Delete periodic report",
        notes = "This endpoint allows you to delete a periodic report by its UUID. The request requires the report ID and locale as query parameters." +
            "<p>Requires permission <b>Manage_Periodic_reports = DELETE</b></p>",
            authorizations = {@Authorization(value = "Bearer")}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    @Secured(items = {
        @Permission(resourceId = SecurityService.Resource.Manage_Periodic_reports, right = Right.DELETE)
    })
    Response deleteReport(@Context HttpServletRequest request, @QueryParam("id") @NotNull String id,
        @QueryParam("locale") @NotNull String localeAsString, @PathParam("uuid") String uuid);

    @DELETE
    @Path("/v1/environments/periodic-report/delete-reports")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ApiOperation(
            value = "Delete multiple periodic reports",
        notes = "This endpoint allows you to delete multiple periodic reports by their UUIDs. You must provide a list of UUIDs and a locale as query parameters." +
            "<p>Requires permission <b>Manage_Periodic_reports = DELETE</b></p>",
            authorizations = {@Authorization(value = "Bearer")}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    @Secured(items = {
        @Permission(resourceId = SecurityService.Resource.Manage_Periodic_reports, right = Right.DELETE)
    })
    Response deleteReports(@Context HttpServletRequest request, @QueryParam("uuids") List<String> uuids,
        @QueryParam("locale") @NotNull String localeAsString);

    @GET
    @Path("/v1/environments/periodic-report/zip/{portlet-id}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(
            value = "Get periodic report ZIP file",
            notes = "This endpoint allows you to download a ZIP file containing the periodic reports for a specific portlet. You can filter reports by UUIDs and select whether to include all indexes.",
            authorizations = {@Authorization(value = "Bearer")}
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    Response getReportZipFile(@Context HttpServletRequest request, @PathParam("portlet-id") String portletId, @QueryParam("allIndexesSelected") boolean allIndexesSelected, @QueryParam("uuids") List<String> uuids);
}