package com.byzaneo.generix.api.service.internal;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.portal.AngularWrapperAgedBalanceDTO;
import com.byzaneo.generix.api.security.annotation.Permission;
import com.byzaneo.generix.api.security.annotation.Secured;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.api.Right;
import com.hazelcast.spi.impl.operationservice.impl.responses.ErrorResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.Authorization;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.apache.cxf.jaxrs.model.wadl.Description;
import org.springframework.data.domain.Sort;

@Api(tags = "gnxRestAgedBalanceService", description = "Operations about Aged Balance")

@Path(RestAgedBalanceService.SERVICE_PATH)
public interface RestAgedBalanceService {

    String SERVICE_NAME = "gnxRestAgedBalanceService";
    String SERVICE_PATH = "";


    /**
     * Retrieves the aged balance for a specified environment and portlet.
     *
     * @param request       HTTP request context
     * @param bql           Query string for filtering entries
     * @param envCode       The code identifying the environment
     * @param portletId     The ID of the portlet
     * @param limit         Maximum number of results to return (default: 15)
     * @param pageNumber    The page number for pagination (default: 0)
     * @param sortBy        The field to sort the results by (e.g., date, amount)
     * @param order         Sorting order (ASC for ascending, DESC for descending)
     * @param count         Flag indicating whether to include the total count of entries
     * @param legalEntity   The legal entity to filter the results by
     * @param overdueOnly   Flag indicating whether to fetch only overdue entries
     * @param enableRefresh Flag indicating whether to refresh the data before retrieving
     * @return Response containing the aged balance data in JSON format
     */
    @GET
    @Path("/v1/environments/{env-code}/portlet/{portlet-id}/aged-balance")
    @Produces({"application/json"})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Retrieves the aged balance",
            notes = "Retrieves the aged balance. Requires authentication.\n\n" +
                    "**Requires at least one of the following permissions:**\n\n" +
                    "- `ap_automation_aged_balance_setting = READ` **OR**\n" +
                    "- `ap_automation_aged_balance_setting = UPDATE`",
            authorizations = {@Authorization(value = "Bearer")})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Aged balance data retrieved successfully", response = AngularWrapperAgedBalanceDTO.class),
            @ApiResponse(code = 400, message = "Bad Request - Invalid query parameters", response = ErrorResponse.class),
            @ApiResponse(code = 401, message = "Unauthorized - Invalid or missing authentication token"),
            @ApiResponse(code = 404, message = "Not Found - The specified environment or portlet does not exist"),
            @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred", response = ErrorResponse.class)
    })
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.ap_automation_aged_balance_setting, right = Right.READ),
            @Permission(resourceId = SecurityService.Resource.ap_automation_aged_balance_setting, right = Right.UPDATE)
    })
    Response getAgedBalance(@Context HttpServletRequest request,
                            @ApiParam(value = "Query string for filtering entries", required = false)
                            @QueryParam("bql") String bql,
                            @ApiParam(value = "Code identifying the environment", required = true, example = "prod")
                            @PathParam("env-code") String envCode,
                            @ApiParam(value = "ID of the portlet", required = true, example = "1234")
                            @PathParam("portlet-id") String portletId,
                            @ApiParam(value = "Maximum number of results to return (default: 15)", required = false, defaultValue = "15")
                            @Max(500) @QueryParam("limit") Integer limit,
                            @ApiParam(value = "Page number for pagination (default: 0)", required = false, defaultValue = "0")
                            @QueryParam("offset") Integer pageNumber,
                            @ApiParam(value = "Field to sort the results by", required = false, example = "currentPayables")
                            @QueryParam("sortBy") String sortBy,
                            @ApiParam(value = "Sorting order (ASC for ascending, DESC for descending)", required = false, defaultValue = "ASC")
                            @QueryParam("order") Sort.Direction order,
                            @ApiParam(value = "Include the total count of entries in the response", required = false, defaultValue = "false")
                            @QueryParam("count") boolean count,
                            @ApiParam(value = "Filter by legal entity", required = false, example = "BARILLA")
                            @QueryParam("legalEntity") String legalEntity,
                            @ApiParam(value = "Fetch only suppliers with overdue debts", required = false, defaultValue = "false")
                            @QueryParam("overdueOnly") boolean overdueOnly,
                            @ApiParam(value = "Enable data of aged balance report refresh before retrieving results", required = false, defaultValue = "false")
                            @QueryParam("enableRefresh") boolean enableRefresh);


    /**
     * Retrieves the invoice list for the aged balance.
     *
     * @param request       HTTP request context.
     * @param bql           Query for filtering invoices.
     * @param envCode       Environment code.
     * @param portletId     Portlet ID.
     * @param legalEntity   Legal entity filter.
     * @param overdueOnly   Whether to fetch only overdue invoices (default: false).
     * @return Response containing the invoice list for the aged balance.
     */
    @GET
    @Path("/v1/environments/{env-code}/portlet/{portlet-id}/aged-balance/invoice-list")
    @Produces({"application/json"})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Retrieve the invoice list for the aged balance",
            notes = "Fetches a list of invoices based on the provided filter. Requires authentication.\n\n" +
                    "**Requires at least one of the following permissions:**\n\n" +
                    "- `ap_automation_aged_balance_setting = READ` **OR**\n" +
                    "- `ap_automation_aged_balance_setting = UPDATE`",
            authorizations = {@Authorization(value = "Bearer")})
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.ap_automation_aged_balance_setting, right = Right.READ),
            @Permission(resourceId = SecurityService.Resource.ap_automation_aged_balance_setting, right = Right.UPDATE)
    })
    Response getAgedBalanceInvoiceList(
            @Context HttpServletRequest request,
            @ApiParam(value = "Query string for filtering entries", required = false)
            @QueryParam("bql") String bql,
            @ApiParam(value = "Code identifying the environment", required = true, example = "prod")
            @PathParam("env-code") String envCode,
            @ApiParam(value = "ID of the portlet", required = true, example = "1234")
            @PathParam("portlet-id") String portletId,
            @ApiParam(value = "Filter by legal entity", example = "BARILLA", required = false)
            @QueryParam("legalEntity") String legalEntity,
            @ApiParam(value = "Fetch only suppliers with overdue debts", required = false, defaultValue = "false")
            @QueryParam("overdueOnly") boolean overdueOnly
    );

    @POST
    @Path("/v1/environments/{env-code}/portlet/{portlet-id}/aged-balance/export")
    @ApiOperation(value = "Export aged balance",
            notes = "Export aged balance. Requires authentication.\n\n" +
                    "**Requires permission**:\n" +
                    "**`ap_automation_aged_balance_setting = EXECUTE`**",
            authorizations = {@Authorization(value = "Bearer")})
    @Secured(items = {
            @Permission(resourceId = SecurityService.Resource.ap_automation_aged_balance_setting, right = Right.EXECUTE)
    })
    Response getExportedAgedBalanceFile(@Context HttpServletRequest request,
                                              @PathParam("env-code") String envCode,
                                              @Description("Export a CSV file for aged-balance")
                                              @QueryParam("bql") String bql,
                                              @QueryParam("sortBy") String sortBy, @QueryParam("order") Sort.Direction order,
                                              @QueryParam("sortByForInvoice") String sortByForInvoice, @QueryParam("orderForInvoice") Sort.Direction orderForInvoice,
                                              @PathParam("portlet-id") Long portletId, @QueryParam("locale") @NotNull String locale,
                                              @QueryParam("file-type") FileType fileType,
                                              @QueryParam("export-archive") boolean exportArchive,
                                              @QueryParam("export-details") boolean exportDetails,
                                              @QueryParam("legalEntity") String legalEntity,
                                              @QueryParam("overdueOnly") boolean overdueOnly);

}
