/**
 *
 */
package com.byzaneo.generix.api.service.internal.impl.translation;

import com.byzaneo.angular.bean.I18NModule;
import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.generix.xtrade.util.ExcelExportHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.security.bean.User;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.core.Response;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.bean.FileType.getType;
import static javax.ws.rs.core.Response.ok;
import static org.apache.commons.lang3.LocaleUtils.toLocale;

/**
 * <AUTHOR> GHOZZI <<EMAIL>>
 * @company Generix group
 * @date 17 april 2025
 */
@Service(TranslationService.SERVICE_NAME)
public class TranslationServiceImpl implements TranslationService {

  private static final String FILE_NAME = "translations.csv";
  private static final String FRONTEND_FILE_NAME = "frontend_translations.csv";

  private final I18NService i18NService;
  private final I18nTranslationDAO i18nTranslationDAO;

  private static final String CODE = "code";
  private static final String MODULE_NAME = "module_name";
  private static final String DELIMITER = ";";

  public TranslationServiceImpl(I18NService i18NService, I18nTranslationDAO i18nTranslationDAO) {
    this.i18NService = i18NService;
    this.i18nTranslationDAO = i18nTranslationDAO;
  }

  @Transactional
  @Override
  public Response exportCSVTranslation(File tmpFile){
    List<I18NTranslationDto> translations = i18NService.getTranslations();
    List<Locale> locales = i18NService.getLocales();
    ExcelExportHelper.exportAsCsvGeneric(createExportedData(translations, locales), null, tmpFile, null, null, FILE_NAME, null, generateHeaders(locales));
    return ok(tmpFile, getType(tmpFile).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + tmpFile.getName()).build();
  }

  /**
   * Method to export data
   * @param translations list of translation lines
   * @param availableLocales list of language to export
   * @return list of line to export in csv file
   */
  @Transactional
  public List<String> createExportedData(List<I18NTranslationDto> translations, List<Locale> availableLocales) {
    Map<String, Map<Locale, String>> translationMap = new LinkedHashMap<>();
    for (I18NTranslationDto translation : translations) {
      String code = translation.getCode();
      I18NModule module = i18NService.findI18NModuleById(translation.getI18NModuleId());
      Locale locale = translation.getLocale();
      String valueToUse = translation.getNewValue()!= null ? translation.getNewValue() : translation.getDefaultValue();
      translationMap
              .computeIfAbsent(code, k -> new HashMap<>())
              .put(locale, valueToUse);
    }
    List<String> exportedData = new ArrayList<>();
    for (String code : translationMap.keySet()) {
      Map<Locale, String> localeMap = translationMap.get(code);
      Query q = new Query(new AndClause(Clauses.equal("code", code)));
      String moduleName = i18nTranslationDAO.search(q).get(0).getI18NModule().getName();
      StringBuilder row = new StringBuilder(moduleName !=null ? moduleName : "");
      row.append(DELIMITER).append(code);
      for (Locale locale : availableLocales) {
        String value = localeMap.getOrDefault(locale, "");
        row.append(DELIMITER).append(value);
      }
      exportedData.add(row.toString());
    }
    return exportedData;
  }

  /**
   * list of headers in file
   * @param availableLocales list of language available
   * @return liste of header for the file to export
   */
  private static String[] generateHeaders(List<Locale> availableLocales) {
    // Create headers array
    String[] headers = new String[availableLocales.size() + 2]; // +2 for the "code" header
    headers[1] = CODE; // First header is "code"
    headers[0] = MODULE_NAME; // second header is "module_id"
    int index = 2;
    for (Locale locale : availableLocales) {
      headers[index++] = locale.toString(); // Add each locale as a header
    }
    return headers;
  }

  @Transactional
  @Override
  public Response importTranslationsData(User user, String locale, Attachment file) {
    Locale messageLocale = new Locale(locale);
    List<List<String>> records = new ArrayList<>();
    Set<String> errorLines = new HashSet<>();
    List<String> successLines = new ArrayList<>();
    String errorLabel = i18NService.findByCodeAndLocale("error_label", messageLocale, user.getFullname());
    try (BufferedReader br = new BufferedReader(new InputStreamReader(file.getDataHandler().getInputStream()))) {
      String line;
      while ((line = br.readLine()) != null) {
        records.add(Arrays.asList(line.split(DELIMITER)));
      }
      Set<String> languages = Arrays.stream(Locale.getISOLanguages())
              .map(Locale::new)
              .map(Locale::getDisplayLanguage)
              .collect(Collectors.toCollection(TreeSet::new));
      for (int recodsLine=1; recodsLine < records.size(); recodsLine++) {
        List<String> record = records.get(recodsLine);
        updateTranslations(records, languages, record, user, locale, errorLines, errorLabel, recodsLine);
      }
      String messageSuccess = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_file_successfully", "", new Locale(locale), records.size() - errorLines.size());
      successLines.add(messageSuccess);
      return listResultMessages(errorLines, successLines, messageSuccess);
    } catch (FileNotFoundException e) {
      throw new RuntimeException(e);
    } catch (IOException e) {
      throw new RuntimeException("Error reading the file: " + e.getMessage(), e);
    }
  }

  /**
   * Method to update line in DB
   * @param resultTranslationsList list of retrived translation line
   * @param record list of language in line
   * @param user connected user
   * @param position poisition of language
   */
  private void updateRow(List<I18NTranslation> resultTranslationsList, List<String> record, User user, int position){
    I18NTranslation i18NTranslation = resultTranslationsList.get(0);
    i18NTranslation.setNewValue(record.get(position));
    i18NTranslation.setNewValueChangedAt(new Date());
    i18NTranslation.setNewValueChangedBy(user);
    i18nTranslationDAO.merge(i18NTranslation);
  }

  /**
   * Method to sort messages error messages
   * @param errorSet set of error message
   * @return list of error messages sorted
   */
  private static List<String> sortErrorList(Set<String> errorSet) {
    List<String> errorList = new ArrayList<>(errorSet);
    Collections.sort(errorList);
    return errorList;
  }

  /**
   * Method to return result listMessages
   * @param errorLines list of errors
   * @param successLines list of succes updated line
   * @param messageSuccess message of succeded update
   * @return response on method
   */
  private Response listResultMessages(Set<String> errorLines, List<String> successLines, String messageSuccess){
    if (!errorLines.isEmpty() && successLines.isEmpty()) {
      List<String> sortedErrorList =  sortErrorList(errorLines);
      return Response.status(Response.Status.NOT_ACCEPTABLE)
              .entity(String.join("\n", sortedErrorList))
              .build();
    } else if (!errorLines.isEmpty()) {
      List<String> sortedErrorList =  sortErrorList(errorLines);
      String responseMessage = String.join("\n", successLines) + "\n" +
              String.join("\n", sortedErrorList);
      return Response.status(Response.Status.NOT_ACCEPTABLE)
              .entity(responseMessage)
              .build();
    } else {
      return Response.ok(messageSuccess).build();
    }
  }

  /**
   * Method to iterate list of translation lines in CSV imported file
   * @param records traslation line
   * @param languages locale
   * @param record list of locale to translate
   * @param user user connected
   * @param locale local ofcurrent user
   * @param errorLines set of errors
   * @param errorLabel error label
   * @param recodsLine list of translations line
   */
  private void updateTranslations(List<List<String>> records, Set<String> languages, List<String> record, User user, String locale, Set<String> errorLines, String errorLabel, int recodsLine){
    // start from the 3th position in the list to not take module_name and code
    for (int recordPosition=2; recordPosition < record.size(); recordPosition++) {
      if(!record.get(recordPosition).isEmpty()) {
        String localeFromRecord = records.get(0).get(recordPosition);
        String currentLocale = toLocale(localeFromRecord).getDisplayLanguage();
        List<String> locales = languages.stream().filter(language -> language.equals(currentLocale)).toList();
        if (locales.isEmpty()) {
          String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_locale_inconnue", "", new Locale(locale), recordPosition + 1, currentLocale);
          errorLines.add(errorLabel + localeInconnue);
          break;
        } else {
          if (!record.get(recordPosition).isEmpty()) {
            // search elements with locale and code
            Query q = new Query(new AndClause(Clauses.equal("locale", localeFromRecord), Clauses.equal("code", record.get(1))));
            List<I18NTranslation> resultTranslationsList = i18nTranslationDAO.search(q);
            if (!resultTranslationsList.isEmpty()) {
              I18NTranslation i18NTranslation = new I18NTranslation();
              Optional<I18NModule> i18nModule = i18NService.findI18NModuleByName(record.get(0));
              if (i18nModule.isPresent()) {
                i18NTranslation.setI18NModule(i18nModule.get());
              } else {
                String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_ligne_ignoree", "", new Locale(locale), recodsLine, currentLocale);
                errorLines.add(errorLabel + localeInconnue);
                break;
              }
              // update data in i18n_translation table
              updateRow(resultTranslationsList, record, user, recordPosition);
            } else {
              String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_ligne_ignoree", "", new Locale(locale), recodsLine, record.get(0), record.get(1));
              errorLines.add(errorLabel + localeInconnue);
              break;
            }
          }
        }
      }
    }
  }

  @Transactional
  @Override
  public Response exportFrontendTranslations(File tmpFile, String languages) {
    List<I18NTranslationDto> translations = i18NService.getTranslations();
    List<Locale> locales = parseLanguageParameter(languages);

    // If no languages specified, export all available locales
    if (locales.isEmpty()) {
      locales = i18NService.getLocales();
    }

    ExcelExportHelper.exportAsCsvGeneric(createFrontendExportedData(translations, locales), null, tmpFile, null, null, FRONTEND_FILE_NAME, null, generateFrontendHeaders(locales));
    return ok(tmpFile, getType(tmpFile).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + tmpFile.getName()).build();
  }

  @Transactional
  @Override
  public Response importFrontendTranslations(User user, String languages, Attachment file) {
    List<Locale> allowedLanguages = parseLanguageParameter(languages);
    List<List<String>> records = new ArrayList<>();
    Set<String> errorLines = new HashSet<>();
    List<String> successLines = new ArrayList<>();
    String errorLabel = "Error: ";

    try (BufferedReader br = new BufferedReader(new InputStreamReader(file.getDataHandler().getInputStream()))) {
      String line;
      while ((line = br.readLine()) != null) {
        records.add(Arrays.asList(line.split(DELIMITER)));
      }

      if (records.isEmpty()) {
        return Response.status(Response.Status.BAD_REQUEST).entity("Empty file").build();
      }

      List<String> headers = records.get(0);
      for (int recordLine = 1; recordLine < records.size(); recordLine++) {
        List<String> record = records.get(recordLine);
        updateFrontendTranslations(headers, record, user, allowedLanguages, errorLines, errorLabel, recordLine);
      }

      String messageSuccess = "Successfully imported " + (records.size() - 1 - errorLines.size()) + " translation(s)";
      successLines.add(messageSuccess);
      return listResultMessages(errorLines, successLines, messageSuccess);
    } catch (FileNotFoundException e) {
      throw new RuntimeException(e);
    } catch (IOException e) {
      throw new RuntimeException("Error reading the file: " + e.getMessage(), e);
    }
  }

  /**
   * Parse language parameter (comma-separated values) into list of Locales
   */
  private List<Locale> parseLanguageParameter(String languages) {
    if (languages == null || languages.trim().isEmpty()) {
      return new ArrayList<>();
    }

    return Arrays.stream(languages.split(","))
        .map(String::trim)
        .filter(lang -> !lang.isEmpty())
        .map(Locale::new)
        .collect(Collectors.toList());
  }

  /**
   * Create export data for frontend format: module;key;language1;language2;...
   */
  private List<String> createFrontendExportedData(List<I18NTranslationDto> translations, List<Locale> availableLocales) {
    Map<String, Map<Locale, String>> translationMap = new LinkedHashMap<>();
    Map<String, String> codeToModuleMap = new HashMap<>();

    for (I18NTranslationDto translation : translations) {
      String code = translation.getCode();
      I18NModule module = i18NService.findI18NModuleById(translation.getI18NModuleId());
      Locale locale = translation.getLocale();
      String valueToUse = translation.getNewValue() != null ? translation.getNewValue() : translation.getDefaultValue();

      translationMap.computeIfAbsent(code, k -> new HashMap<>()).put(locale, valueToUse);
      codeToModuleMap.put(code, module != null ? module.getName() : "");
    }

    List<String> exportedData = new ArrayList<>();
    for (String code : translationMap.keySet()) {
      Map<Locale, String> localeMap = translationMap.get(code);
      String moduleName = codeToModuleMap.get(code);

      StringBuilder row = new StringBuilder(moduleName != null ? moduleName : "");
      row.append(DELIMITER).append(code);

      for (Locale locale : availableLocales) {
        String value = localeMap.getOrDefault(locale, "");
        row.append(DELIMITER).append(value);
      }
      exportedData.add(row.toString());
    }
    return exportedData;
  }

  /**
   * Generate headers for frontend format: module;key;language1;language2;...
   */
  private static String[] generateFrontendHeaders(List<Locale> availableLocales) {
    String[] headers = new String[availableLocales.size() + 2];
    headers[0] = MODULE_NAME;
    headers[1] = CODE;

    int index = 2;
    for (Locale locale : availableLocales) {
      headers[index++] = locale.toString();
    }
    return headers;
  }

  /**
   * Update translations for frontend import with business rules
   */
  private void updateFrontendTranslations(List<String> headers, List<String> record, User user,
                                        List<Locale> allowedLanguages, Set<String> errorLines,
                                        String errorLabel, int recordLine) {
    if (record.size() < 2) {
      errorLines.add(errorLabel + "Line " + recordLine + ": Invalid format, missing module or code");
      return;
    }

    String moduleName = record.get(0);
    String code = record.get(1);

    // Verify that the code exists in the database
    Query codeQuery = new Query(new AndClause(Clauses.equal("code", code)));
    List<I18NTranslation> existingTranslations = i18nTranslationDAO.search(codeQuery);
    if (existingTranslations.isEmpty()) {
      errorLines.add(errorLabel + "Line " + recordLine + ": Code '" + code + "' does not exist in database");
      return;
    }

    // Process each language column
    for (int i = 2; i < Math.min(headers.size(), record.size()); i++) {
      String languageCode = headers.get(i);
      String translationValue = record.get(i);

      if (translationValue == null || translationValue.trim().isEmpty()) {
        continue; // Skip empty values
      }

      Locale locale;
      try {
        locale = toLocale(languageCode);
      } catch (Exception e) {
        errorLines.add(errorLabel + "Line " + recordLine + ": Invalid locale '" + languageCode + "'");
        continue;
      }

      // Check if language is allowed (if filter is specified)
      if (!allowedLanguages.isEmpty() && !allowedLanguages.contains(locale)) {
        continue; // Skip languages not in the filter
      }

      // Business rule: Don't replace French and English default values
      if ("fr".equals(locale.getLanguage()) || "en".equals(locale.getLanguage())) {
        continue; // Skip French and English as per business rules
      }

      // Find or create translation for this locale and code
      Query translationQuery = new Query(new AndClause(
          Clauses.equal("code", code),
          Clauses.equal("locale", locale)
      ));

      List<I18NTranslation> translations = i18nTranslationDAO.search(translationQuery);

      if (!translations.isEmpty()) {
        // Update existing translation
        I18NTranslation translation = translations.get(0);
        translation.setNewValue(translationValue);
        translation.setNewValueChangedAt(new Date());
        translation.setNewValueChangedBy(user);
        i18nTranslationDAO.merge(translation);
      } else {
        // Create new translation entry
        I18NTranslation newTranslation = new I18NTranslation();
        newTranslation.setCode(code);
        newTranslation.setLocale(locale);
        newTranslation.setNewValue(translationValue);
        newTranslation.setNewValueChangedAt(new Date());
        newTranslation.setNewValueChangedBy(user);

        // Set the module
        Optional<I18NModule> module = i18NService.findI18NModuleByName(moduleName);
        if (module.isPresent()) {
          newTranslation.setI18NModule(module.get());
        } else {
          // Try to get module from existing translation
          if (!existingTranslations.isEmpty()) {
            newTranslation.setI18NModule(existingTranslations.get(0).getI18NModule());
          }
        }

        i18nTranslationDAO.persist(newTranslation);
      }
    }
  }

}
