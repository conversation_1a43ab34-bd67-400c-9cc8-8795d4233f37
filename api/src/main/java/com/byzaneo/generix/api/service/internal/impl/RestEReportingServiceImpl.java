package com.byzaneo.generix.api.service.internal.impl;

import com.byzaneo.generix.api.bean.SortOrderDto;
import com.byzaneo.generix.api.bean.payment.EReportingPaymentsDTO;
import com.byzaneo.generix.api.bean.transaction.EReportingTransactionDTO;
import com.byzaneo.generix.api.service.internal.RestEReportingService;
import com.byzaneo.generix.api.service.internal.delegators.RestEReportingServiceDelegator;
import com.byzaneo.xtrade.xcbl.transmission.TransmissionTypeCoded;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.UUID;

@Service(RestEReportingService.SERVICE_NAME)
@RequiredArgsConstructor
public class RestEReportingServiceImpl implements RestEReportingService {

  private final RestEReportingServiceDelegator eReportingServiceDelegator;

  @Override
  public Response getTransmissions(HttpServletRequest request, String bql, UUID requestId, TransmissionTypeCoded transmissionType,
      Integer limit, Integer offset, String sortBy, SortOrderDto order, boolean count, String portletId) {
    return eReportingServiceDelegator.getTransmissions(request, bql, requestId, transmissionType, limit, offset, sortBy, order, count,
        portletId);

  }

  @Override
  public Response addTransaction(HttpServletRequest request, EReportingTransactionDTO eReportingTransactionDTO) {
    return eReportingServiceDelegator.addTransaction(request, eReportingTransactionDTO);
  }

  @Override
  public Response addPayment(HttpServletRequest request, EReportingPaymentsDTO eReportingPaymentDto) {
    return eReportingServiceDelegator.addPayment(request, eReportingPaymentDto);
  }
}
