/*
 * Copyright (c) 2023.
 * created by <PERSON><PERSON>
 */

package com.byzaneo.generix.api.service.internal.delegators;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;


public interface RestEnvironmentServiceDelegator {

 Response getEnvironmentConfiguration(HttpServletRequest request, String envCode);

 Response getValuesControl(String listId);

 Response getPartnerAddresses(HttpServletRequest request, String envCode);

 Response getCountry(HttpServletRequest request, String envCode);

 Response getVersion(HttpServletRequest request);

 Response getApiUrl(HttpServletRequest request, String url);
 
 Response getFactorPartnerAddresses(HttpServletRequest request, String envCode);
}
