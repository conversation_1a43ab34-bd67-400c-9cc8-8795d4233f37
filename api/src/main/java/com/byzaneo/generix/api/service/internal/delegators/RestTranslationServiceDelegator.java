package com.byzaneo.generix.api.service.internal.delegators;

import com.byzaneo.generix.service.repository.service.translation.CriteriaDto;
import com.byzaneo.generix.service.repository.service.translation.UpdateTranslationDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import javax.ws.rs.PathParam;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.*;

public interface RestTranslationServiceDelegator {
  Response findI18NTranslations(String locale);

  Response findModules(HttpServletRequest request);

  Response findI18NTranslations(HttpServletRequest request, CriteriaDto criteria);


  Response editTranslation(@Context HttpServletRequest request, @PathParam("translation-id") Long id, @RequestBody UpdateTranslationDto i18NTranslation);


  Response findLanguages(@Context HttpServletRequest request);
  Response exportTranslationsData(@Context HttpServletRequest request, CriteriaDto criteria);
  Response importTranslationsData(@Context HttpServletRequest request,
                                  @QueryParam("locale") @NotNull String locale, Attachment file);

  Response exportFrontendTranslations(@Context HttpServletRequest request, String languages);
  Response importFrontendTranslations(@Context HttpServletRequest request, String languages, Attachment file);

}
