package com.byzaneo.generix.api.service.internal;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.*;
import javax.ws.rs.*;
import javax.ws.rs.core.*;

import org.apache.cxf.jaxrs.ext.multipart.*;
import org.apache.cxf.jaxrs.model.wadl.Description;
import org.glassfish.jersey.media.multipart.FormDataParam;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.RequestBody;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.portal.DocumentTimelineDto;
import com.byzaneo.generix.api.security.annotation.*;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.security.api.Right;

import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Operation;

@Api(tags = "gnxRestInvoiceService", description = "Operations about Invoices services")

@Path(RestInvoiceService.SERVICE_PATH)
public interface RestInvoiceService {
    String SERVICE_NAME = "gnxRestInvoiceService";
    String SERVICE_PATH = "";


    /**
     * Search for online invoices
     */
    @GET
    @Path("/v1/invoices/portlet/{portlet-id}")
    @Produces({ "application/json" })
    @Operation(summary = "Search for online invoices", tags = { "Invoice Management" })
    @ApiOperation(value = "Search for online invoices", authorizations = { @Authorization(value = "Bearer") })
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    public Response v1EnvironmentsEnvCodeInvoicesGet(@Context HttpServletRequest request,
        @QueryParam("bql") String bql, @HeaderParam("Request-Id") UUID requestId, @Max(1000) @DefaultValue("100")
    @QueryParam("limit") Integer limit, @QueryParam("offset") @DefaultValue("0") Integer offset, @QueryParam("base") BaseType base,
        @QueryParam("flowDirection") String flowDirection, @QueryParam("sortBy") String sortBy, @QueryParam("order") SortOrderDto order,
        @QueryParam("count") boolean count,
        @PathParam("portlet-id") String portletId, @QueryParam("locale") @NotNull String locale,
        @QueryParam("isCountEnabled") boolean isCountEnabled,
        @QueryParam("uuid") String uuid);

  @GET
  @Path("/v2/invoices/portlet/{portlet-id}")
  @Produces({ "application/json" })
  @Operation(summary = "Search for online invoices", tags = { "Invoice Management" })
  @ApiOperation(value = "Search for online invoices", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  public Response v2EnvironmentsEnvCodeInvoicesGet(@Context HttpServletRequest request,
      @QueryParam("bql") String bql, @HeaderParam("Request-Id") UUID requestId, @Max(1000) @DefaultValue("100")
  @QueryParam("limit") Integer limit, @QueryParam("offset") @DefaultValue("0") Integer offset,
      @QueryParam("flowDirection") String flowDirection, @QueryParam("sortBy") String sortBy, @QueryParam("order") SortOrderDto order,
      @PathParam("portlet-id") String portletId, @QueryParam("locale") @NotNull String locale,
      @QueryParam("selectedSecurityFields") List<String> selectedSecurityFields,
      @QueryParam("showOnlyWorkflow") boolean showOnlyWorkflow);

  @GET
  @Path("/v2/invoices/portlet/count")
  @Produces({ "application/json" })
  @Operation(summary = "Count invoices", tags = { "Invoice Management" })
  @ApiOperation(value = "Count invoices", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  public Response v2EnvironmentsEnvCodeInvoicesCount(@Context HttpServletRequest request,
      @QueryParam("bql") String bql, @HeaderParam("Request-Id") UUID requestId, @Max(1000) @DefaultValue("100")
  @QueryParam("limit") Integer limit, @QueryParam("offset") @DefaultValue("0") Integer offset,
      @QueryParam("flowDirection") String flowDirection, @QueryParam("sortBy") String sortBy, @QueryParam("order") SortOrderDto order,
      @QueryParam("locale") @NotNull String locale,
      @QueryParam("selectedSecurityFields") List<String> selectedSecurityFields,
      @QueryParam("showOnlyWorkflow") boolean showOnlyWorkflow);

  @GET
  @Path("/v1/invoices/stages")
  @Produces({ "application/json" })
  @Operation(summary = "get invoice stages", tags = { "Invoice Management" })
  @ApiOperation(value = "get invoice stages", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  public Response getInvoiceStages(@Context HttpServletRequest request, @NotNull String locale);

    /**
     * export invoices
     */
    @GET
    @Path("/v1/environments/{env-code}/export/portlet/{portlet-id}/invoices")
    @ApiOperation(value = "Export Invoices", notes = "Export invoices based on the BQL query and other parameters")
    Response getExportedInvoicesExcelFiles(@Context HttpServletRequest request, @PathParam("env-code") String envCode,
                                           @Description("The BQL query used to search the invoice") @QueryParam("bql") String bql,
                                           @QueryParam("sortBy") String sortBy,@QueryParam("order") SortOrderDto order,
                                           @PathParam("portlet-id") Long portletId, @QueryParam("locale") @NotNull String locale,
                                           @QueryParam("file-type") FileType fileType,
                                           @QueryParam("all-indexes-selected") boolean allIndexesSelected,
                                           @QueryParam("export-archive") boolean exportArchive,
                                           @QueryParam("print-single-doc") boolean printSingleDocument);

    @GET
    @Path("/v1/invoices/{uuid}/portlet/{portlet-id}/timelines")
    @Produces({"application/json"})
    @Operation(summary = "Search for timelines for invoices", tags = {"Invoice Management"})
    @ApiOperation(value = "Search for timelines for invoices", authorizations = {@Authorization(value = "Bearer")})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    Response v1GetAllTimelinesForInvoice(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);


    @GET
    @Path("/v1/invoices/{uuid}/portlet/{portlet-id}/lifecycle")
    @Produces({"application/json"})
    @Operation(summary = "Search for invoice life cycle", tags = {"Invoice Management"})
    @ApiOperation(value = "Search for invoice life cycle", authorizations = {@Authorization(value = "Bearer")})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    Response getInvoiceStepsLifeCycle(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);


    @GET
    @Path("/v1/invoices/attachments/portlet/{portlet-id}/invoice/{uuid}")
    @Produces({ "application/json" })
    @ApiOperation(value = "Search for attachments", authorizations = { @Authorization(value = "Bearer") })
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    Response
    getAttachmentsFiles(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base,
        @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);

  @PATCH
  @Path("/v1/environments/{env-code}/payment-status/portlet/{portlet-id}/invoices")
  @Produces({ "application/json" })
  @Operation(summary = "Change payment status for selected invoices", tags = { "Invoice Management" })
  @ApiOperation(value = "Change payment status for selected invoices", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response updatePaymentStatusForSelectedInvoices(@Context HttpServletRequest request, @PathParam("env-code") String envCode,
      @Description("The BQL query used to search the invoice") @QueryParam("bql") String bql, @PathParam("portlet-id") Long portletId,
      @QueryParam("locale") @NotNull String locale);

  @GET
  @Path("/v1/invoices/reported-data-attachments/portlet/{portlet-id}/invoice/{uuid}")
  @Produces({ "application/json" })
  @Operation(summary = "Search for attachments", tags = { "Invoice Management" })
  @ApiOperation(value = "Search for attachments", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getReportedDataAttachmentsFiles(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);

  @GET
  @Path("/v1/invoices/get-all-invoice-files/portlet/{portlet-id}/invoice/{uuid}")
  @Produces({ "application/json" })
  @ApiOperation(value = "Search for attachments", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getAllAttachmentsFilesWithType(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);

  @GET
  @Path("/v2/invoices/get-all-invoice-files/portlet/{portlet-id}/invoice/{uuid}")
  @Produces({ "application/json" })
  @ApiOperation(value = "Search for attachments", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response v2GetAllAttachmentsFilesWithType(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);

  @GET
  @Path("/v1/invoices/error-files/portlet/{portlet-id}/invoice/{uuid}")
  @Produces({ "application/json" })
  @Operation(summary = "Search for error files", tags = { "Invoice Management" })
  @ApiOperation(value = "Search for error files", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getErrorFiles(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base,
      @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);

  @GET
  @Path("/v1/invoices/tax-original/portlet/{portlet-id}/invoice/{uuid}")
  @Produces({ "application/json" })
  @ApiOperation(
      value = "Retrieve Tax Original Files for Invoice",
      notes = "Fetches the tax original files associated with a specific invoice and portlet. Requires authentication via an access token.",
      authorizations = { @Authorization(value = "Bearer") },
      response = Response.class
  )
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getTaxOriginalFiles(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);

  @GET
  @Path("/invoices/related-file/portlet/{portlet-id}/invoice/{uuid}/{fromLifeCycle}")
  @Produces({ "application/json" })
  @ApiOperation(
      value = "Retrieve Related Files for Invoice",
      notes = "Fetches the related files for a specific invoice and portlet, with an option to filter by lifecycle status.",
      authorizations = { @Authorization(value = "Bearer") },
      response = Response.class
  )
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getRelatedFiles(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid,
      @PathParam("fromLifeCycle") boolean fromLifeCycle);


  @GET
  @Path("/invoices/rectification/portlet/{portlet-id}/invoice/{uuid}")
  @Produces({ "application/json" })
  @Operation(summary = "Search for Rectification files", tags = { "Invoice Management" })
  @ApiOperation(value = "Search for Rectification files", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getRectificationFiles(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);


  @GET
  @Path("v1/invoices/file/{portlet-id}")
  @ApiOperation(
          value = "Retrieve an attached file for an invoice",
          notes = "This endpoint allows retrieving an attached file for a specific invoice, identified by the portlet ID and file path.",
          tags = {"Invoice Management"},
          authorizations = {@Authorization(value = "Bearer")}
  )
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getAttachedFile(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId,@QueryParam("path") String path);

  @GET
  @Path("v1/invoices/file/xsl/{filename}/locale/{locale}")
  @ApiOperation(
          value = "Retrieve a specific XSL file for invoice timeline",
          notes = "This endpoint retrieves a specific XSL file representing the timeline for an invoice, based on the given filename and locale.",
          tags = {"Invoice Management"},
          authorizations = {@Authorization(value = "Bearer")}
  )
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getTimeLineXSL(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base , @PathParam("filename") String filename, @PathParam("locale") String locale );

  @GET
  @Path("v1/invoices/lifecycle/file/xsl/{filename}/locale/{locale}")
  @ApiOperation(
          value = "Retrieve an XSL file for invoice lifecycle",
          notes = "This endpoint retrieves an XSL file representing the lifecycle of an invoice, based on the given filename and locale.",
          tags = {"Invoice Management"},
          authorizations = {@Authorization(value = "Bearer")}
  )
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getLifeCycleXSL(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base , @PathParam("filename") String filename, @PathParam("locale") String locale );


  @GET
  @Path("/v1/invoices/attachment/zip/{portlet-id}/invoice/{uuid}/{relatedFiles}/{fromLifeCycle}")
  @ApiOperation(
      value = "Get Attachments as a Zip File",
      notes = "This endpoint generates and returns a zip file containing the attachments for a specific invoice based on the provided UUID, portlet ID, and related files flag.",
      authorizations = { @Authorization(value = "Bearer") }
  )
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getAttachmentsZipFile(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid,
      @PathParam("relatedFiles") Boolean relatedFiles, @QueryParam("locale") @NotNull String locale,
      @PathParam("fromLifeCycle") boolean fromLifeCycle);

  @GET
  @Path("/v2/invoices/attachment/zip/{portlet-id}/invoice/{uuid}/{relatedFiles}/{fromLifeCycle}")
  @ApiOperation(
      value = "Get Attachments as a Zip File",
      notes = "This endpoint generates and returns a zip file containing the attachments for a specific invoice based on the provided UUID, portlet ID, and related files flag.",
      authorizations = { @Authorization(value = "Bearer") }
  )
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response v2GetAttachmentsZipFile(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid,
      @PathParam("relatedFiles") Boolean relatedFiles, @QueryParam("locale") @NotNull String locale,
      @PathParam("fromLifeCycle") boolean fromLifeCycle);

  @GET
  @Path("/v1/invoices/taxOriginal/zip/{portlet-id}/invoices")
  @Produces(MediaType.APPLICATION_OCTET_STREAM)
  @ApiOperation(
      value = "Get Tax Original Zip File",
      notes = "This endpoint generates and returns a zip file containing tax original invoices based on the provided filters.",
      authorizations = { @Authorization(value = "Bearer") }
  )
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getTaxOriginalZipFile(@Context HttpServletRequest request, @PathParam("portlet-id") String portletId,
      @QueryParam("allIndexesSelected") boolean allIndexesSelected, @QueryParam("uuids") List<String> uuids,
      @QueryParam("hasMissingTaxOriginal") boolean hasMissingTaxOriginal,
      @QueryParam("hasNonEmptyTaxOriginal") boolean hasNonEmptyTaxOriginal);

    @GET
    @Path("/v1/invoices/reported_errors/zip/{portlet-id}/invoices/{uuid}/{attachedFileType}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @ApiOperation(
        value = "Get Declared and Error Files in a Zip File",
        notes = "This endpoint generates and returns a zip file containing declared and error files for the specified invoice based on the provided UUID and attached file type.",
        authorizations = { @Authorization(value = "Bearer") }
    )
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    Response getDeclaredAndErrorFilesInZipFile(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
        @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid,
        @PathParam("attachedFileType") String attachedFileType);

    /**
     * Trigger actions for invoices
     */
    @GET
    @Path("/v1/environments/{env-code}/trigger-action/portlet/{portlet-id}/invoices")
    @ApiOperation(
        value = "Trigger Actions for Invoices",
        notes = "This endpoint triggers specific actions for invoices in the given environment and portlet."
    )
    Response triggerActions(@Context HttpServletRequest request, @Context UriInfo uriInfo, @PathParam("env-code") String envCode,
        @PathParam("portlet-id") Long portletId, @QueryParam("sortBy") String sortBy, @QueryParam("order") Sort.Direction order);


  @GET
  @Path("/v1/environments/{env-code}/trigger-compliance-action/portlet/{portlet-id}/invoices")
  @ApiOperation(
          value = "Trigger Compliance Actions for Invoices",
          notes = "This endpoint triggers compliance-related actions for invoices within the specified environment and portlet."
  )
  Response triggerComplianceActions(@Context HttpServletRequest request, @Context UriInfo uriInfo, @PathParam("env-code") String envCode, @PathParam("portlet-id") Long portletId);

  @DELETE
  @Path("/v1/environments/{env-code}/remove/portlet/{portlet-id}/invoices")
  @ApiOperation(
          value = "Remove an Invoice",
          notes = "This endpoint removes an invoice based on the BQL query provided and the specified environment and portlet.",
          authorizations = {@Authorization(value = "Bearer")}
  )
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response removeInvoice(@Context HttpServletRequest request, @PathParam("env-code") String envCode, @Description("The BQL query used to search the invoice") @QueryParam("bql") String bql, @PathParam("portlet-id") Long portletId, @QueryParam("locale") @NotNull String locale, @QueryParam("file-type") FileType fileType, @QueryParam("all-indexes-selected") boolean allIndexesSelected);

  @POST
  @Path("/v1/invoices/{uuid}/portlets/{portlet-id}/attachment")
  @Consumes(MediaType.MULTIPART_FORM_DATA)
  @ApiOperation(
          value = "Save an attachment for an invoice",
          notes = "This endpoint allows uploading and attaching a file to a specific invoice, providing optional comments."
  )
  Response saveAttachedFile(
          @Context HttpServletRequest request,
          @PathParam("portlet-id") Long portletId,
          @QueryParam("locale") @NotNull String locale,
          @PathParam("uuid") UUID uuid,
          @Multipart(value = "file") Attachment file,
          @FormDataParam("comment") String comment

  );

  @DELETE
  @Path("/v1/environments/{env-code}/remove/attachment/{urlAttachment}/uuid/{uuid}/portletId/{portletId}/locale/{locale}")
  @ApiOperation(
          value = "Remove an Attachment",
          notes = "This endpoint removes an attachment associated with a specific invoice, environment, portlet, and locale."
  )
  Response removeAttachment(@Context HttpServletRequest request, @PathParam("env-code") String envCode, @PathParam("urlAttachment") String urlAttachment, @PathParam("uuid") String uuid, @PathParam("portletId") String portletId, @PathParam("locale") String locale);


  @GET
  @Path("/v1/invoices/portlet/{portlet-id}/batch-validation")
  @Produces({ "application/json" })
  @Operation(summary = "Invoice batch validation", tags = { "Invoice batch validation" })
  @ApiOperation(value = "Invoice batch validation", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  public Response validateWorkflowDocuments(@Context HttpServletRequest request,
      @QueryParam("bql") String bql, @HeaderParam("Request-Id") UUID requestId, @Max(1000) @DefaultValue("100")
  @QueryParam("limit") Integer limit, @QueryParam("offset") @DefaultValue("0") Integer offset,
      @QueryParam("flowDirection") String flowDirection, @QueryParam("sortBy") String sortBy, @QueryParam("order") Sort.Direction order,
      @QueryParam("count") boolean count,
      @PathParam("portlet-id") String portletId, @QueryParam("locale") @NotNull String localeAsString,
      @QueryParam("all-indexes-selected") boolean allIndexesSelected, @QueryParam("count-selected") Integer countSelected,
      @QueryParam("showOnlyWorkflow") boolean showOnlyWorkflow);

  @GET
  @Path("/v1/invoices/portlet/{portlet-id}/batch-refuse")
  @Produces({ "application/json" })
  @Operation(summary = "Invoice batch refuse", tags = { "Invoice batch refuse" })
  @ApiOperation(value = "Invoice batch refuse", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  public Response refuseWorkflowDocuments(@Context HttpServletRequest request,
      @QueryParam("bql") String bql, @HeaderParam("Request-Id") UUID requestId, @Max(1000) @DefaultValue("100")
  @QueryParam("limit") Integer limit, @QueryParam("offset") @DefaultValue("0") Integer offset,
      @QueryParam("flowDirection") String flowDirection, @QueryParam("sortBy") String sortBy, @QueryParam("order") Sort.Direction order,
      @QueryParam("count") boolean count,
      @PathParam("portlet-id") String portletId, @QueryParam("locale") @NotNull String localeAsString,
      @QueryParam("all-indexes-selected") boolean allIndexesSelected, @QueryParam("count-selected") Integer countSelected,
      @QueryParam("showOnlyWorkflow") boolean showOnlyWorkflow);

  @GET
  @Path("/v1/invoices/portlet/{portlet-id}/invoice-correct")
  @Produces({ "application/json" })
  @Operation(summary = "Invoice correction", tags = { "Invoice correction" })
  @ApiOperation(value = "Invoice correction", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  public Response correctInvoice(@Context HttpServletRequest request,
      @HeaderParam("Request-Id") UUID requestId,
      @PathParam("portlet-id") String portletId,
      @QueryParam("uuid") String uuid,
      @QueryParam("locale") String localeAsString);

  @POST
  @Path("/v1/invoices/portlet/{portlet-id}/save-invoice-correct")
  @Produces("application/json")
  @Consumes("application/json")
  @ApiOperation(value = "Save Invoice Correction")
  public Response saveCorrectInvoice(@Context HttpServletRequest request,
      @HeaderParam("Request-Id") UUID requestId,
      @PathParam("portlet-id") String portletId,
      @QueryParam("uuid") String uuid,
      @QueryParam("locale") String localeAsString,
      @RequestBody List<SaveInvoiceCorrectionDTO> value);

  @POST
  @Path("/v1/portlet/{portlet-id}/invoices/{uuid}/attachment")
  @Consumes({ MediaType.MULTIPART_FORM_DATA })
  @Produces({ "application/json" })
  @ApiOperation(
          value = "Attach Files to an Invoice",
          notes = "Attaches multiple files to a specific invoice under a given portlet, ensuring that executables are excluded.",
          response = Response.class
  )
  Response attachMultiFiles(
      @Context HttpServletRequest request,
      @PathParam("portlet-id") Long portletId,
      @ApiParam(value = "The invoice unique ID") @PathParam("uuid") UUID invoiceUid,
      @ApiParam(value = "File required, executables refused (.exe, .cmd, .com, .bat .sh). The comment is not mandatory.") MultipartBody documentDetail,
      @ApiParam(value = "Header parameter used to correlate logs from several components") @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("locale") @NotNull String localeAsString);

    @GET
    @Path("/v1/invoices/compliance/portlet/{portlet-id}/invoice/{uuid}")
    @Produces({ "application/json" })
    @ApiOperation(value = "Get compliance tab data in portlet compliance", authorizations = { @Authorization(value = "Bearer") })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
    })
    //Api to get compliance tab data in portlet compliance
    Response getCompliance(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @QueryParam("base") BaseType base, @PathParam("portlet-id") String portletId, @PathParam("uuid") String uuid);


  @POST
  @Path("/v1/invoices/{uuid}/timelines")
  @Consumes({MediaType.APPLICATION_JSON})
  @ApiOperation(
          value = "Add an Action to an Invoice Timeline",
          notes = "Adds a specific action (event) to the timeline of an invoice identified by its UUID.",
          authorizations = {@Authorization(value = "Bearer")},
          response = Response.class
  )
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response addActionInTimeline(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId, @PathParam("uuid") String uuid, @RequestBody DocumentTimelineDto body);

  @POST
  @Path("/v1/invoices/timelines")
  @Consumes({MediaType.APPLICATION_JSON})
  @ApiOperation(
          value = "Add Multiple Actions to Invoices' Timelines",
          notes = "Adds a batch of actions (events) to the timelines of multiple invoices.",
          authorizations = {@Authorization(value = "Bearer")},
          response = Response.class
  )
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response addActionsInTimeline(@Context HttpServletRequest request, @RequestBody DocumentTimelineDto body);

  @GET
  @Path("/v1/invoices/compliance/allowed-actions/{invoice-uuid}")
  @Consumes({ MediaType.APPLICATION_JSON })
  @ApiOperation(value = "Get allowed actions for document stage", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Bearer access_token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response getAllowedActions(@Context HttpServletRequest request, @PathParam("invoice-uuid") String invoiceUuid);


  @GET
  @Path("/v1/invoices/{uuid}/invoice-headers")
  @Produces({"application/json"})
  @Operation(summary = "Search for online invoices headers", tags = {"Invoice Management"})
  @ApiOperation(value = "Search for online invoices headers", authorizations = {@Authorization(value = "Bearer")})
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  public Response v1GetInvoiceHeaderByUuid(@Context HttpServletRequest request, @PathParam("uuid") String uuid);

  @PATCH
  @Path("/v1/environments/invoice-status-update/{uuid}/status/{statusInvoiceValidation}")
  @Consumes("application/json")
  @Produces({ "application/json", "text/plain" })
  @ApiOperation(value = "set invoice status", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response updateInvoiceStatus(@Context HttpServletRequest request, @QueryParam("locale") @NotNull String localeAsString,
      @PathParam("uuid") String uuid, @PathParam("statusInvoiceValidation") @NotNull String statusInvoiceValidation);

  @GET
  @Path("/v1/invoices/can-perform-action")
  @Produces({ MediaType.APPLICATION_JSON })
  @Operation(summary = "Check if action can be performed on invoice", tags = { "Invoice Management" })
  @ApiOperation(value = "Check if action can be performed on invoice", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  @ApiResponses(value = {
      @ApiResponse(code = 200, message = "It has been determined whether the action is allowed or not.", response = Boolean.class),
      @ApiResponse(code = 400, message = "Bad request.") })
  public Response canPerformAction(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("actionName") String actionName);


  @POST
  @Path("/v1/invoices/ocr-verification/{rossum-id}/{uuid}")
  @Produces({ MediaType.APPLICATION_JSON })
  @ApiOperation(value = "Fetch Rossum URL", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  @ApiResponses(value = {
      @ApiResponse(code = 200, message = "Rossum studio url successfully retrieved.", response = String.class),
      @ApiResponse(code = 500, message = "Can't open Rossum studio.") })
  Response fetchRossumUrl(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @PathParam("rossum-id") String rossumId, @PathParam("uuid") String uuid, @QueryParam("type") String type);

  @POST
  @Path("v1/invoices/update-invoices")
  @Consumes(MediaType.APPLICATION_JSON)
  @ApiOperation(value = "Update invoices status and adds timeline", authorizations = { @Authorization(value = "Bearer") })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  @Secured(items = {
      @Permission(resourceId = SecurityService.Resource.Portlet_Invoice_OCR_Verify, right = Right.CREATE)
  })
  Response updateInvoicesStatus(@Context HttpServletRequest request, @HeaderParam("Request-Id") UUID requestId,
      @QueryParam("status") String statusCode, @QueryParam("timeline_code") String timelineAction, @RequestBody String body);
}
