package com.byzaneo.generix.api.service.external.impl;

import com.byzaneo.generix.api.bean.InvoiceRequestWithPaginationDTO;
import com.byzaneo.generix.api.service.external.RestStatusManagementService;
import com.byzaneo.generix.api.service.external.delegators.RestStatusManagementServiceDelegator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.UUID;

@Service(RestStatusManagementService.SERVICE_NAME)
@RequiredArgsConstructor
public class RestStatusManagementServiceImpl implements RestStatusManagementService {
  private final RestStatusManagementServiceDelegator restStatusManagementServiceDelegator;

  @Override
  @Transactional
  public Response getInvoiceChangedStatus(HttpServletRequest request, String env, UUID requestId,
      InvoiceRequestWithPaginationDTO invoiceRequest) {
    return restStatusManagementServiceDelegator.getInvoiceChangedStatus(request, env, requestId, invoiceRequest);
  }
}
