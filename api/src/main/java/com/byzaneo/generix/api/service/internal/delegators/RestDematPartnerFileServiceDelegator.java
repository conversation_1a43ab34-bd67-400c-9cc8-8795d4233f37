package com.byzaneo.generix.api.service.internal.delegators;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.BqlUuidDto;
import com.byzaneo.generix.api.bean.portal.dematpartnerfile.*;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.data.domain.Sort;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.*;

public interface RestDematPartnerFileServiceDelegator {

  Response v1getDematPartnerFile(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset, String legalEntity,
      String flowDirection, String sortBy, Sort.Direction order, boolean count, String portletId, String locale, boolean isCountEnabled);

  Response v1getDocumentTypes(HttpServletRequest request, String bql, String envCode, UUID requestId, String legalEntity, String portletId,
      String locale);

  Response v1saveExchange(HttpServletRequest request, String envCode, String portletId,
      com.byzaneo.generix.api.bean.portal.dematpartnerfile.ExchangeDTO exchangeDTO);

  Response getExportTemplateFileExchange(HttpServletRequest request, String envCode, Long portletId, String localeAsString);

  Response importExchanges(HttpServletRequest request, String envCode, Long portletId, String locale, Attachment file);

  Response getUserAllLegalEntities(HttpServletRequest request, String envCode, Long portletId, String locale);

  Response exportDematPartnerAsCsvAndPdfFile(HttpServletRequest request, String envCode, BqlUuidDto bql, String sortBy,
      Sort.Direction order, Long portletId, String locale, String legalEntity, FileType fileType, boolean exportArchive);

  List<RevisionDTO> v1getRevisionsByExchangeId(String login, String envCode, Long portletId, HistoryDTO historyDTO, Locale locale);
}