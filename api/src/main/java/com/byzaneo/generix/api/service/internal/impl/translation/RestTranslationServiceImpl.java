package com.byzaneo.generix.api.service.internal.impl.translation;

import com.byzaneo.generix.service.repository.service.translation.CriteriaDto;
import com.byzaneo.generix.service.repository.service.translation.UpdateTranslationDto;
import com.byzaneo.generix.api.service.internal.RestTranslationService;
import com.byzaneo.generix.api.service.internal.delegators.RestTranslationServiceDelegator;
import lombok.RequiredArgsConstructor;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.slf4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;

import static org.slf4j.LoggerFactory.getLogger;

@Service(RestTranslationService.SERVICE_NAME)
@RequiredArgsConstructor
public class RestTranslationServiceImpl implements RestTranslationService {

  private static final Logger log = getLogger(RestTranslationServiceImpl.class);

  private final RestTranslationServiceDelegator translationServiceDelegator;

  @Override
  @Transactional
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
  public Response findI18NTranslations(HttpServletRequest request, CriteriaDto criteria) {
    return translationServiceDelegator.findI18NTranslations(request, criteria);
  }

  @Override
  @Transactional
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'UPDATE')")
  public Response editTranslation(HttpServletRequest request,Long id, UpdateTranslationDto i18NTranslation) {
    return translationServiceDelegator.editTranslation(request, id, i18NTranslation);
  }

  @Override
  @Transactional
  public Response findI18NTranslations(String locale) {
    return translationServiceDelegator.findI18NTranslations(locale);
  }

  @Override
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
  public Response findModules(HttpServletRequest request) {
    return translationServiceDelegator.findModules(request);
  }

  @Override
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
  public Response findLanguages(HttpServletRequest request) {
    return translationServiceDelegator.findLanguages(request);
  }

  @Override
  public Response exportTranslationsData(HttpServletRequest request, CriteriaDto criteria) {
    return translationServiceDelegator.exportTranslationsData(request,criteria);
  }

  @Override
  public Response importTranslationsData(HttpServletRequest request, String locale, Attachment file) {
    return translationServiceDelegator.importTranslationsData(request, locale, file);
  }

  @Override
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'READ')")
  public Response downloadDictionary(HttpServletRequest request, String dictType, String language) {
    return translationServiceDelegator.downloadDictionary(request, dictType, language);
  }

  @Override
  @PreAuthorize("@permissionHelper.hasPermission(#request, 'TRANSLATION', 'UPDATE')")
  public Response uploadDictionary(HttpServletRequest request, String dictType, String language, Attachment file) {
    return translationServiceDelegator.uploadDictionary(request, dictType, language, file);
  }

}
