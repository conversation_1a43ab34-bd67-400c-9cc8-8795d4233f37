package com.byzaneo.generix.api.bean;

import lombok.*;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XmlRootElement(name = "InvoiceStatusManagementResponse")
public class InvoiceStatusManagementResponse {
  private String startDate;
  private String endDate;
  private List<PartnerCountStatusDto> partners;
  private String processingWay;
}
