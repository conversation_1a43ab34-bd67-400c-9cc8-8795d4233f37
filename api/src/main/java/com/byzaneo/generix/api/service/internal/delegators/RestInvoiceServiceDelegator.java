package com.byzaneo.generix.api.service.internal.delegators;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.*;

import org.apache.cxf.jaxrs.ext.multipart.*;
import org.springframework.data.domain.Sort;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.generix.api.bean.*;
import com.byzaneo.generix.api.bean.portal.DocumentTimelineDto;
import com.byzaneo.generix.rtemachine.exception.AccessTokenException;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.DocumentTimeline;

public interface RestInvoiceServiceDelegator {

  public Response v1EnvironmentsEnvCodeInvoicesGet(HttpServletRequest request,
      String bql, UUID requestId,
      Integer limit, Integer offset, BaseType base, String flowDirection, String sortBy, SortOrderDto order, boolean count,
      String portletId, String locale, boolean isCountEnabled,
      String uuid);

  Response v2EnvironmentsEnvCodeInvoicesGet(HttpServletRequest request,
      String bql, UUID requestId, Integer limit, Integer offset, String flowDirection, String sortBy, SortOrderDto order,
      String portletId, String locale, List<String> selectedSecurityFields, boolean showOnlyWorkflow);

  Response v2EnvironmentsEnvCodeInvoicesCount(HttpServletRequest request,
      String bql, UUID requestId, Integer limit, Integer offset, String flowDirection, String sortBy, SortOrderDto order,
      String locale, List<String> selectedSecurityFields, boolean showOnlyWorkflow);

  public Response getInvoiceStages(HttpServletRequest request, String locale);

  Response getExportedInvoicesExcelFiles(HttpServletRequest request, String envCode,
      String bql,
      String sortBy, SortOrderDto order,
      Long portletId, String locale,
      FileType fileType,
      boolean allIndexesSelected,
      boolean exportArchive,
      boolean printSingleDocument);
  
  Response v1GetAllTimelinesForInvoice(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);

  Response getInvoiceStepsLifeCycle(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);

  Response getAttachmentsFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);

  Response getReportedDataAttachmentsFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);

  Response getAllAttachmentsFilesWithType(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);

  Response v2GetAllAttachmentsFilesWithType(HttpServletRequest request, String uuid);

  Response getErrorFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);

  Response getTaxOriginalFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);

  Response getRelatedFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid, boolean fromLifeCycle);

  Response getRectificationFiles(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);

  Response getAttachedFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String path);

  Response getTimeLineXSL(HttpServletRequest request, UUID requestId, BaseType base, String filename, String locale);

  Response getLifeCycleXSL(HttpServletRequest request, UUID requestId, BaseType base, String filename, String locale);

  Response getAttachmentsZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      Boolean relatedFiles, String locale, boolean fromLifeCycle);

  Response v2GetAttachmentsZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      Boolean relatedFiles, String locale, boolean fromLifeCycle);

  Response getTaxOriginalZipFile(HttpServletRequest request, String portletId, boolean allIndexesSelected, List<String> uuids,
      boolean hasMissingTaxOriginal, boolean hasNonEmptyTaxOriginal);

  Response getDeclaredAndErrorFilesInZipFile(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid,
      String attachedFileType);

  Response triggerActions(HttpServletRequest request, UriInfo uriInfo, String envCode, Long portletId, String sortBy, Sort.Direction order);

  Response triggerComplianceActions(HttpServletRequest request, UriInfo uriInfo, String envCode, Long portletId);

Response removeInvoice(HttpServletRequest request, String envCode, String bql, Long portletId, String locale, FileType fileType, boolean allIndexesSelected);


  Response saveAttachedFile(
      HttpServletRequest request,
      Long portletId,
      String locale,
      UUID uuid,
      Attachment file,
      String comment

  );

 Response removeAttachment(HttpServletRequest request, String envCode, String urlAttachment, String uuid, String portletId, String locale);

  public Response validateWorkflowDocuments(HttpServletRequest request,
      String bql, UUID requestId,
      Integer limit, Integer offset, String flowDirection, String sortBy, Sort.Direction order, boolean count,
      String portletId, String localeAsString, boolean allIndexesSelected, Integer countSelected, boolean showOnlyWorkflow);

  public Response refuseWorkflowDocuments(HttpServletRequest request,
      String bql, UUID requestId,
      Integer limit, Integer offset,
      String flowDirection, String sortBy, Sort.Direction order,
      boolean count,
      String portletId, String localeAsString,
      boolean allIndexesSelected, Integer countSelected, boolean showOnlyWorkflow);


  public Response correctInvoice(HttpServletRequest request,
      UUID requestId,
      String portletId,
      String uuid,
      String localeAsString);

  public Response saveCorrectInvoice(HttpServletRequest request,
      UUID requestId,
      String portletId,
      String uuid,
      String localeAsString,
      List<SaveInvoiceCorrectionDTO> value);

  Response attachMultiFiles(
      HttpServletRequest request,
      Long portletId,
      UUID invoiceUid,
      MultipartBody documentDetail,
       UUID requestId,
      String localeAsString);
  
  Response getCompliance(HttpServletRequest request, UUID requestId, BaseType base, String portletId, String uuid);



  Response addActionInTimeline(HttpServletRequest request, UUID requestId, String uuid, DocumentTimelineDto body);

 
  Response addActionsInTimeline(HttpServletRequest request, DocumentTimelineDto body);

 
  Response getAllowedActions(HttpServletRequest request, String invoiceUuid);

  Response updatePaymentStatusForSelectedInvoices(HttpServletRequest request, String envCode, String bql, Long portletId,
      String locale);

  Response v1GetInvoiceHeaderByUuid(HttpServletRequest request, String uuid);

  Response updateInvoiceStatus(HttpServletRequest request, String localeAsString, String uuid, String statusInvoiceValidation);

  Boolean canPerformAction(String userLogin, String actionName);

  String fetchRossumUrl(String userLogin, String requestUrl, String rossumId, String uuid, String type)
      throws AccessTokenException, URISyntaxException, IOException, InterruptedException;

  void updateInvoicesStatus(String userLogin, List<String> invoices, DocumentStatus status, DocumentTimeline.TimelineAction action);
}
