package com.byzaneo.generix.api.service.internal.delegators;

import com.byzaneo.generix.api.bean.SortOrderDto;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.*;


public interface RestPeriodicReportServiceDelegator {

  Response v1getPeriodicReport(HttpServletRequest request, String bql, UUID requestId, Integer limit, Integer offset, String legalEntity,
      String flowDirection, String sortBy, SortOrderDto order, boolean count, String portletId, String locale, boolean isCountEnabled);

  Response v1SetPeriodicReport(HttpServletRequest request, Long portletId, String locale, Attachment file,
      String periodicReportDtoAsString);

  Response deleteReport(HttpServletRequest request, String id, String localeAsString, String uuid);

  Response deleteReports(HttpServletRequest request, List<String> uuids, String localeAsString);

  Response getReportZipFile(HttpServletRequest request, String portletId, boolean allIndexesSelected, List<String> uuids);
}