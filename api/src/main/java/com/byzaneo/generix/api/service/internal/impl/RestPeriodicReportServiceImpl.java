package com.byzaneo.generix.api.service.internal.impl;

import com.byzaneo.generix.api.bean.SortOrderDto;
import com.byzaneo.generix.api.service.internal.RestPeriodicReportService;
import com.byzaneo.generix.api.service.internal.delegators.RestPeriodicReportServiceDelegator;
import lombok.RequiredArgsConstructor;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.*;

@Service(RestPeriodicReportService.SERVICE_NAME)
@RequiredArgsConstructor
public class RestPeriodicReportServiceImpl implements RestPeriodicReportService {

    private final RestPeriodicReportServiceDelegator periodicReportServiceDelegator;

    @Override
    @Transactional
    public Response v1getPeriodicReport(HttpServletRequest request, String bql,
                                   UUID requestId, Integer limit,
                                   Integer offset, String legalEntity, String flowDirection, String sortBy, SortOrderDto order, boolean count,
                                   String portletId, String localeAsString, boolean isCountEnabled) {
        return periodicReportServiceDelegator.v1getPeriodicReport(request, bql, requestId, limit, offset, legalEntity, flowDirection, sortBy, order, count, portletId, localeAsString, isCountEnabled);
    }

    @Override
    @Transactional
    public Response v1SetPeriodicReport(HttpServletRequest request, Long portletId, String localeAsString, Attachment file,
                                     String periodicReportDtoAsString) {
        return periodicReportServiceDelegator.v1SetPeriodicReport(request, portletId, localeAsString, file, periodicReportDtoAsString);
    }

    @Override
    @Transactional
    public Response deleteReport(HttpServletRequest request, String id, String localeAsString ,  String uuid) {
        return periodicReportServiceDelegator.deleteReport(request, id, localeAsString, uuid);
    }

    @Override
    @Transactional
    public Response deleteReports(HttpServletRequest request,  List<String> uuids, String localeAsString) {
        return periodicReportServiceDelegator.deleteReports(request, uuids, localeAsString);
    }

    @Override
    @Transactional(readOnly = true)
    public Response getReportZipFile(HttpServletRequest request, String portletId, boolean allIndexesSelected, List<String> uuids) {
        return periodicReportServiceDelegator.getReportZipFile(request, portletId, allIndexesSelected, uuids);
    }
}
