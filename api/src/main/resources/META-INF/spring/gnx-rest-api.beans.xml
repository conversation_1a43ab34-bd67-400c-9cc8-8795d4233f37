<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:cxf="http://cxf.apache.org/core"
       xmlns:jaxrs="http://cxf.apache.org/jaxrs" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://cxf.apache.org/core http://cxf.apache.org/schemas/core.xsd
                           http://cxf.apache.org/jaxrs http://cxf.apache.org/schemas/jaxrs.xsd
                           http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <bean id="swagger2Feature1" class="org.apache.cxf.jaxrs.swagger.Swagger2Feature" lazy-init="true">
        <property name="contact" value="Generix Group"/>
        <property name="title" value="Basic authentication Rest Services"/>
        <property name="description" value="Basic authentication Rest Services documentation"/>
        <property name="version" value="1.0"/>
        <property name="supportSwaggerUi" value="true"/>
        <property name="usePathBasedConfig" value="true"/>
    </bean>
    <bean id="swagger2Feature2" class="org.apache.cxf.jaxrs.swagger.Swagger2Feature" lazy-init="true">
        <property name="contact" value="Generix Group"/>
        <property name="title" value="JWT authentication Rest Services"/>
        <property name="description" value="JWT authentication Rest Services documentation for Angular reply"/>
        <property name="supportSwaggerUi" value="true"/>
        <property name="usePathBasedConfig" value="true"/>
    </bean>
    <bean id="swagger2Feature3" class="org.apache.cxf.jaxrs.swagger.Swagger2Feature" lazy-init="true">
        <property name="contact" value="Generix Group"/>
        <property name="title" value="Invoice Rest Services"/>
        <property name="description" value="Invoice Rest Services documentation"/>
        <property name="version" value="1.0"/>
        <property name="supportSwaggerUi" value="true"/>
        <property name="usePathBasedConfig" value="true"/>
    </bean>

    <jaxrs:server id="rest2" address="/internal" transportId="http://cxf.apache.org/transports/http"
                  basePackages="com.byzaneo.generix.api.service">
        <jaxrs:serviceBeans>
            <ref bean="gnxRestJWTService"/>
            <ref bean="restAccountingPostingScenarioService"/>
            <ref bean="gnxRestDocumentCounterTaskApi"/>
            <ref bean="gnxRestCompanyService"/>
            <ref bean="gnxRestUserService"/>
            <ref bean="gnxRestBankAccountService"/>
            <ref bean="gnxRestEnvironmentService"/>
            <ref bean="gnxRestPortalService"/>
            <ref bean="gnxRestFilterService"/>
            <ref bean="gnxRestInvoiceService"/>
            <ref bean="gnxRestArchiveInvoiceService"/>
            <ref bean="gnxRestTranslationService" />
            <ref bean="gnxRestEReportingService" />
            <ref bean="gnxRestExchangeRatesService" />
            <ref bean="gnxRestLegalReferentialService" />
            <ref bean="gnxRestAgreementService" />
            <ref bean="gnxRestAgedBalanceService" />
            <ref bean="gnxRestAAPManagementService" />
            <ref bean="gnxRestStandardInvoiceEdition" />
            <ref bean="gnxRestAAPEntriesService" />
            <ref bean="gnxRestReferentialProduct" />
            <ref bean="gnxRestAdvancedRecapListService" />
            <ref bean="gnxRestAdvancedRecapDailyService" />
            <ref bean="restRestPeriodicReportService" />
            <ref bean="gnxRestDematPartnerFileService" />
            <ref bean="gnxRestDematPartnerFileDailyService" />
            <ref bean="gnxRestDocChartTaskApi" />
            <ref bean="gnxRestFileService" />
            <ref bean="gnxRestReconciliationManagementScenarioService" />
            <ref bean="gnxRestProfilePreferencesService" />
        </jaxrs:serviceBeans>
        <jaxrs:providers>
            <bean class="com.byzaneo.generix.api.exception.ExceptionHttpHandler"/>
            <bean class="com.byzaneo.generix.api.exception.AccessDeniedExceptionHandler"/>
            <bean class="org.apache.cxf.jaxrs.provider.json.JSONProvider">
                <property name="dropRootElement" value="true"/>
                <property name="supportUnwrapped" value="true"/>
            </bean>
            <bean class="org.apache.cxf.jaxrs.provider.json.JSONProvider">
				<property name="dropRootElement" value="true"/>
				<property name="supportUnwrapped" value="true"/>
			</bean>
            <bean id="stringMapDeserializer" class="com.byzaneo.generix.api.service.StringMapDeserializer" />
            <bean class="org.apache.cxf.jaxrs.provider.json.JsonMapObjectProvider">

            </bean>

            <bean class="com.byzaneo.generix.api.exception.RuntimeExceptionHandler"/>
            <bean class="com.byzaneo.generix.api.exception.SaxExceptionHandler"/>
            <bean class="com.byzaneo.generix.api.exception.RestSecurityExceptionHandler"/>
            <bean class="com.byzaneo.generix.api.security.filter.PermissionsFilter"/>
        </jaxrs:providers>
        <jaxrs:features>
            <ref bean="swagger2Feature2"/>
        </jaxrs:features>
    </jaxrs:server>
    <jaxrs:server id="rest3" address="/gis" transportId="http://cxf.apache.org/transports/http"
                  basePackages="com.byzaneo.generix.api.service">
        <jaxrs:serviceBeans>
            <ref bean="gnxRestProcessApiService"/>
            <ref bean="gnxRestInvoiceManagementApi"/>
            <ref bean="gnxRestPartnersManagementApi"/>
            <ref bean="gnxRestPerimetersManagementApi"/>
            <ref bean="gnxRestRolesManagementApi"/>
            <ref bean="gnxRestUserApiService"/>
            <ref bean="gnxRestAnalyticsManagementApi"/>
            <ref bean="gnxStatusManagementApi"/>

        </jaxrs:serviceBeans>
        <jaxrs:providers>
            <bean class="com.byzaneo.generix.api.exception.RuntimeExceptionHandler"/>
            <bean class="com.byzaneo.generix.api.exception.SaxExceptionHandler"/>
            <bean class="com.byzaneo.generix.api.exception.RestSecurityExceptionHandler"/>
            <bean class="org.apache.cxf.jaxrs.provider.json.JSONProvider">
                <property name="dropRootElement" value="true"/>
                <property name="supportUnwrapped" value="true"/>
            </bean>
        </jaxrs:providers>
        <jaxrs:features>
            <ref bean="swagger2Feature3"/>
        </jaxrs:features>
    </jaxrs:server>

    <bean id="swagger2Feature5" class="org.apache.cxf.jaxrs.swagger.Swagger2Feature" lazy-init="true">
        <property name="contact" value="Generix Group"/>
        <property name="title" value="EReporting Rest Services"/>
        <property name="description" value="EReporting Rest Services documentation"/>
        <property name="version" value="1.0"/>
        <property name="supportSwaggerUi" value="true"/>
        <property name="usePathBasedConfig" value="true"/>
    </bean>
</beans>
