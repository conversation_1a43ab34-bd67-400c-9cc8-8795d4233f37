package com.byzaneo.generix.api.service.internal.impl.translation;

import com.byzaneo.angular.bean.I18NModule;
import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.DictionaryImportResponse;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.generix.service.repository.service.translation.ImportError;
import com.byzaneo.security.bean.User;
import com.byzaneo.xtrade.dao.Query;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.activation.DataHandler;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TranslationServiceImplTest {

    @Mock
    private I18NService i18NService;

    @Mock
    private I18nTranslationDAO i18nTranslationDAO;

    @InjectMocks
    private TranslationServiceImpl translationService;

    private User testUser;
    private I18NModule testModule;
    private List<I18NTranslationDto> testTranslations;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        testUser = new User();
        testUser.setLogin("testUser");
        
        testModule = new I18NModule();
        testModule.setId(1L);
        testModule.setName("TEST_MODULE");
        
        setupTestTranslations();
    }

    private void setupTestTranslations() {
        testTranslations = new ArrayList<>();
        
        // French translation
        I18NTranslationDto frTranslation = new I18NTranslationDto();
        frTranslation.setCode("TEST_KEY");
        frTranslation.setLocale(new Locale("fr"));
        frTranslation.setDefaultValue("Bonjour");
        frTranslation.setNewValue(null);
        frTranslation.setI18NModuleId(1L);
        testTranslations.add(frTranslation);
        
        // English translation
        I18NTranslationDto enTranslation = new I18NTranslationDto();
        enTranslation.setCode("TEST_KEY");
        enTranslation.setLocale(new Locale("en"));
        enTranslation.setDefaultValue("Hello");
        enTranslation.setNewValue(null);
        enTranslation.setI18NModuleId(1L);
        testTranslations.add(enTranslation);
        
        // German translation
        I18NTranslationDto deTranslation = new I18NTranslationDto();
        deTranslation.setCode("TEST_KEY");
        deTranslation.setLocale(new Locale("de"));
        deTranslation.setDefaultValue(null);
        deTranslation.setNewValue("Hallo");
        deTranslation.setI18NModuleId(1L);
        testTranslations.add(deTranslation);
    }

    @Test
    void exportFrontendTranslations_ShouldExportAllLanguages_WhenNoLanguageFilter() throws IOException {
        // Given
        File tmpFile = File.createTempFile("test", ".csv");
        List<Locale> allLocales = Arrays.asList(new Locale("fr"), new Locale("en"), new Locale("de"));
        
        when(i18NService.getTranslations()).thenReturn(testTranslations);
        when(i18NService.getLocales()).thenReturn(allLocales);
        when(i18NService.findI18NModuleById(1L)).thenReturn(testModule);
        
        // When
        Response response = translationService.exportFrontendTranslations(tmpFile, null);
        
        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertTrue(tmpFile.exists());
        assertTrue(tmpFile.length() > 0);
        
        // Cleanup
        tmpFile.delete();
    }

    @Test
    void exportFrontendTranslations_ShouldExportSpecificLanguage_WhenLanguageFilterProvided() throws IOException {
        // Given
        File tmpFile = File.createTempFile("test", ".csv");
        String languageFilter = "de";
        
        when(i18NService.getTranslations()).thenReturn(testTranslations);
        when(i18NService.findI18NModuleById(1L)).thenReturn(testModule);
        
        // When
        Response response = translationService.exportFrontendTranslations(tmpFile, languageFilter);
        
        // Then
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertTrue(tmpFile.exists());
        assertTrue(tmpFile.length() > 0);
        
        // Cleanup
        tmpFile.delete();
    }

    @Test
    void importFrontendTranslations_ShouldImportSuccessfully_WhenValidCSV() throws Exception {
        // Given
        String csvContent = "module;key;fr;en;de\n" +
                           "TEST_MODULE;TEST_KEY;Bonjour;Hello;Hallo\n" +
                           "TEST_MODULE;ANOTHER_KEY;Au revoir;Goodbye;Auf Wiedersehen";
        
        Attachment file = createMockAttachment(csvContent);
        
        // Mock existing translations for validation
        List<I18NTranslation> existingTranslations = Arrays.asList(
            createI18NTranslation("TEST_KEY", new Locale("fr")),
            createI18NTranslation("TEST_KEY", new Locale("en")),
            createI18NTranslation("ANOTHER_KEY", new Locale("fr")),
            createI18NTranslation("ANOTHER_KEY", new Locale("en"))
        );
        
        when(i18nTranslationDAO.search(any(Query.class)))
            .thenReturn(existingTranslations)
            .thenReturn(existingTranslations);
        
        when(i18NService.findI18NModuleByName("TEST_MODULE")).thenReturn(Optional.of(testModule));
        
        // When
        Response response = translationService.importFrontendTranslations(testUser, null, file);
        
        // Then
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        
        DictionaryImportResponse importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);
        assertEquals(2, importResponse.getTotalImported()); // 2 German translations imported
        assertTrue(importResponse.getErrors().isEmpty());
        
        // Verify that persist was called for new German translations
        verify(i18nTranslationDAO, times(2)).persist(any(I18NTranslation.class));
    }

    @Test
    void importFrontendTranslations_ShouldReportErrors_WhenInvalidLocale() throws Exception {
        // Given
        String csvContent = "module;key;fr;en;zz\n" +
                           "TEST_MODULE;TEST_KEY;Bonjour;Hello;Invalid";
        
        Attachment file = createMockAttachment(csvContent);
        
        // When
        Response response = translationService.importFrontendTranslations(testUser, null, file);
        
        // Then
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        
        DictionaryImportResponse importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);
        assertEquals(0, importResponse.getTotalImported());
        assertEquals(1, importResponse.getErrors().size());
        
        ImportError error = importResponse.getErrors().get(0);
        assertEquals(0, error.getLineNumber()); // Header error
        assertTrue(error.getErrorMessage().contains("La locale zz n'est pas une locale reconnue"));
    }

    @Test
    void importFrontendTranslations_ShouldReportErrors_WhenCodeDoesNotExist() throws Exception {
        // Given
        String csvContent = "module;key;fr;en;de\n" +
                           "TEST_MODULE;NONEXISTENT_KEY;Bonjour;Hello;Hallo";
        
        Attachment file = createMockAttachment(csvContent);
        
        // Mock no existing translations found
        when(i18nTranslationDAO.search(any(Query.class))).thenReturn(Collections.emptyList());
        
        // When
        Response response = translationService.importFrontendTranslations(testUser, null, file);
        
        // Then
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        
        DictionaryImportResponse importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);
        assertEquals(0, importResponse.getTotalImported());
        assertEquals(1, importResponse.getErrors().size());
        
        ImportError error = importResponse.getErrors().get(0);
        assertEquals(2, error.getLineNumber()); // Data line error (line 2)
        assertTrue(error.getErrorMessage().contains("Code NONEXISTENT_KEY"));
        assertTrue(error.getErrorMessage().contains("non existant"));
    }

    @Test
    void importFrontendTranslations_ShouldFilterByLanguage_WhenLanguageParameterProvided() throws Exception {
        // Given
        String csvContent = "module;key;fr;en;de;es\n" +
                           "TEST_MODULE;TEST_KEY;Bonjour;Hello;Hallo;Hola";
        
        Attachment file = createMockAttachment(csvContent);
        String languageFilter = "de"; // Only import German
        
        // Mock existing translations
        List<I18NTranslation> existingTranslations = Arrays.asList(
            createI18NTranslation("TEST_KEY", new Locale("fr")),
            createI18NTranslation("TEST_KEY", new Locale("en"))
        );
        
        when(i18nTranslationDAO.search(any(Query.class)))
            .thenReturn(existingTranslations)
            .thenReturn(Collections.emptyList()); // No existing German translation
        
        when(i18NService.findI18NModuleByName("TEST_MODULE")).thenReturn(Optional.of(testModule));
        
        // When
        Response response = translationService.importFrontendTranslations(testUser, languageFilter, file);
        
        // Then
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        
        DictionaryImportResponse importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);
        assertEquals(1, importResponse.getTotalImported()); // Only German imported
        assertTrue(importResponse.getErrors().isEmpty());
        
        // Verify only one persist call (for German)
        verify(i18nTranslationDAO, times(1)).persist(any(I18NTranslation.class));
    }

    @Test
    void importFrontendTranslations_ShouldReturnBadRequest_WhenEmptyFile() throws Exception {
        // Given
        Attachment file = createMockAttachment("");
        
        // When
        Response response = translationService.importFrontendTranslations(testUser, null, file);
        
        // Then
        assertEquals(Response.Status.BAD_REQUEST.getStatusCode(), response.getStatus());
    }

    private Attachment createMockAttachment(String content) throws Exception {
        Attachment attachment = mock(Attachment.class);
        DataHandler dataHandler = mock(DataHandler.class);
        InputStream inputStream = new ByteArrayInputStream(content.getBytes("UTF-8"));
        
        when(attachment.getDataHandler()).thenReturn(dataHandler);
        when(dataHandler.getInputStream()).thenReturn(inputStream);
        
        return attachment;
    }

    private I18NTranslation createI18NTranslation(String code, Locale locale) {
        I18NTranslation translation = new I18NTranslation();
        translation.setCode(code);
        translation.setLocale(locale);
        translation.setI18NModule(testModule);
        return translation;
    }
}
