package com.byzaneo.generix.api.service.internal.impl.translation;

import com.byzaneo.generix.api.service.internal.RestTranslationService;
import com.byzaneo.generix.api.service.internal.delegators.RestTranslationServiceDelegator;
import com.byzaneo.generix.service.repository.service.translation.DictionaryImportResponse;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RestTranslationServiceImplIntegrationTest {

    @Mock
    private RestTranslationServiceDelegator translationServiceDelegator;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Mock
    private Attachment attachment;

    @InjectMocks
    private RestTranslationServiceImpl restTranslationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void downloadDictionary_ShouldCallDelegator_WithCorrectParameters() {
        // Given
        String dictType = "frontend";
        String language = "de";
        Response expectedResponse = Response.ok().build();
        
        when(translationServiceDelegator.downloadDictionary(httpServletRequest, dictType, language))
            .thenReturn(expectedResponse);
        
        // When
        Response response = restTranslationService.downloadDictionary(httpServletRequest, dictType, language);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationServiceDelegator).downloadDictionary(httpServletRequest, dictType, language);
    }

    @Test
    void uploadDictionary_ShouldCallDelegator_WithCorrectParameters() {
        // Given
        String dictType = "frontend";
        String language = "es";
        DictionaryImportResponse importResponse = DictionaryImportResponse.builder()
            .totalImported(3)
            .errors(Collections.emptyList())
            .build();
        Response expectedResponse = Response.status(Response.Status.CREATED).entity(importResponse).build();
        
        when(translationServiceDelegator.uploadDictionary(httpServletRequest, dictType, language, attachment))
            .thenReturn(expectedResponse);
        
        // When
        Response response = restTranslationService.uploadDictionary(httpServletRequest, dictType, language, attachment);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationServiceDelegator).uploadDictionary(httpServletRequest, dictType, language, attachment);
    }

    @Test
    void downloadDictionary_ShouldHandleErrorsType() {
        // Given
        String dictType = "errors";
        String language = "fr";
        Response expectedResponse = Response.ok().build();
        
        when(translationServiceDelegator.downloadDictionary(httpServletRequest, dictType, language))
            .thenReturn(expectedResponse);
        
        // When
        Response response = restTranslationService.downloadDictionary(httpServletRequest, dictType, language);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationServiceDelegator).downloadDictionary(httpServletRequest, dictType, language);
    }

    @Test
    void uploadDictionary_ShouldHandleErrorsType() {
        // Given
        String dictType = "errors";
        String language = "en";
        Response expectedResponse = Response.ok().build();
        
        when(translationServiceDelegator.uploadDictionary(httpServletRequest, dictType, language, attachment))
            .thenReturn(expectedResponse);
        
        // When
        Response response = restTranslationService.uploadDictionary(httpServletRequest, dictType, language, attachment);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationServiceDelegator).uploadDictionary(httpServletRequest, dictType, language, attachment);
    }

    @Test
    void downloadDictionary_ShouldHandleNullLanguage() {
        // Given
        String dictType = "frontend";
        String language = null;
        Response expectedResponse = Response.ok().build();
        
        when(translationServiceDelegator.downloadDictionary(httpServletRequest, dictType, language))
            .thenReturn(expectedResponse);
        
        // When
        Response response = restTranslationService.downloadDictionary(httpServletRequest, dictType, language);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationServiceDelegator).downloadDictionary(httpServletRequest, dictType, language);
    }

    @Test
    void uploadDictionary_ShouldHandleMultipleLanguages() {
        // Given
        String dictType = "frontend";
        String languages = "de,es,it";
        DictionaryImportResponse importResponse = DictionaryImportResponse.builder()
            .totalImported(15)
            .errors(Collections.emptyList())
            .build();
        Response expectedResponse = Response.status(Response.Status.CREATED).entity(importResponse).build();
        
        when(translationServiceDelegator.uploadDictionary(httpServletRequest, dictType, languages, attachment))
            .thenReturn(expectedResponse);
        
        // When
        Response response = restTranslationService.uploadDictionary(httpServletRequest, dictType, languages, attachment);
        
        // Then
        assertEquals(expectedResponse, response);
        verify(translationServiceDelegator).uploadDictionary(httpServletRequest, dictType, languages, attachment);
    }
}
