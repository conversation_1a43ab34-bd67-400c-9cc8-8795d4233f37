package com.byzaneo.generix.standalone.action;

import static org.slf4j.LoggerFactory.getLogger;

import java.util.*;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.query.Query;
import com.byzaneo.query.builder.*;
import com.byzaneo.query.clause.Clause;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.InvoiceIndex;

public class UpdateOCRInvoiceStatus implements StandaloneAction {
  private final static Logger log = getLogger(UpdateOCRInvoiceStatus.class);

  private final static String TO_VIDEOCODE_STATUS_LIST_PREFIX = "TO_VIDEOCODE_STATUS";
  static final int BATCH_MAX_SIZE = 1000;

  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  protected DocumentService documentService;

  @Override
  @Transactional
  public void doAction(String[] args) {
    List<Document> indexablesMigrated = new ArrayList<>();
    Pageable pageable = Pageable.ofSize(BATCH_MAX_SIZE);
    long saved = 0;
    Clause statusQuery = Clauses.equal("status", "TO_CORRECT");

    for (int i = 0; i < args.length; i++) {
      if (args[i].startsWith(TO_VIDEOCODE_STATUS_LIST_PREFIX)) {
        int listStart = args[i].indexOf("(") + 1;
        int listEnd = args[i].indexOf(")");
        if (listStart != 0 && listEnd > 0) {
          String[] statusList = args[i].substring(listStart, listEnd)
              .split(",");
          if (statusList.length > 0)
            statusQuery = Clauses.in("status", Arrays.asList(statusList));
        }
      }
    }

    Query query = new QueryBuilder().append("acquisition = 'OCR' AND rossumId is not EMPTY")
        .and(statusQuery)
        .query();
    long total = documentService.countIndexable(InvoiceIndex.class, query);
    List<Document> documents = documentService.searchIndexedDocuments(Document.class, InvoiceIndex.class, query, pageable);
    log.info("* * * * * Start migrating status for ocr invoices. * * * * *");
    log.info("Query used: " + query.toString());
    log.info("Total number of invoice to migrate: {}", total);
    while (documents.size() != 0) {
      for (Document document : documents)
        try {
          InvoiceIndex indexable = document.getIndexValue();
          document.setStatusWithEnumValue(DocumentStatus.OCR_TO_VERIFY);
          indexable.setStatusWithEnumValue(DocumentStatus.OCR_TO_VERIFY);
          indexablesMigrated.add(document);
          saved++;
        }
        catch (Exception e) {
          log.error("Can't migrate invoice " + document.getReference(), e);
        }
      documentService.saveDocuments(indexablesMigrated);
      indexablesMigrated.clear();
      log.info("Migrated {}/{} invoices.", saved, total);
      pageable = pageable.next();
      documents = documentService.searchIndexedDocuments(Document.class, InvoiceIndex.class, query, pageable);
    }
    log.info("* * * * * End migrating status for ocr invoices. * * * * *");
  }
}
