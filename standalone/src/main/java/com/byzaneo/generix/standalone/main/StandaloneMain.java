package com.byzaneo.generix.standalone.main;

import static org.slf4j.LoggerFactory.getLogger;

import java.util.*;

import org.apache.commons.cli.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.byzaneo.generix.standalone.action.*;
import com.byzaneo.generix.standalone.config.StandaloneConfiguration;

/**
 * Standalone process dealing with Spring Context loading/unloading and calling the StandaloneAction given in parameter.
 *
 * <AUTHOR>
 * @date 29/07/2015
 */
public abstract class StandaloneMain {
  private static final String HELP_OPTION = "help";
  private static final String PROPERTIES_OPTION = "properties";
  private static final String PROPERTIES_TYPE = "type";
  private static final String OUT_OPTION = "output";
  private static final String CLIENT_CODE_OPTION = "clientCode";
  private static final String PARTNER_CODE_OPTION = "partnerCode";

  private static final Logger logger = getLogger(StandaloneMain.class);
  private static final List<Class<? extends StandaloneAction>> availableActions = new ArrayList<>();
  private static final Options cliOptions;

  static {
    cliOptions = new Options();
    cliOptions.addOption("h", HELP_OPTION, false, "Display help");
    cliOptions.addOption("p", PROPERTIES_OPTION, true, "Location of properties' file (e.g. classpath:/gnx.properties)");
    cliOptions.addOption("t", PROPERTIES_TYPE, true, "[AIO-7413] Type de migration 1: SQL | 2: Mongo");
    cliOptions.addOption("out", OUT_OPTION, true, "Location of output file");
    cliOptions.addOption("CLIENT_CODE", CLIENT_CODE_OPTION, true, "Client code (e.g. CLIENT_CODE=AAA)");
    cliOptions.addOption("PARTNER_CODE", PARTNER_CODE_OPTION, true, "Partner code (optional) (e.g. PARTNER_CODE=XXX)");

    availableActions.add(MigrateDocumentsToInvoiceIndexType.class);
    availableActions.add(MigrateInvoiceDocument.class);
    availableActions.add(MigratePages.class);
    availableActions.add(UpdateInvoiceDocument.class);
    availableActions.add(UpdateInvoiceDocumentNumUnique.class);
    availableActions.add(MigrateDocCopyTask.class);
    availableActions.add(MigrateStatuses.class);
    availableActions.add(MigrateCachingStatus.class);
    availableActions.add(MigrateDocumentsToOrderIndexType.class);
    availableActions.add(MigrateOrderDocuments.class);
    availableActions.add(MigrateOrdersForRoquette.class);
    availableActions.add(MigrateTaxes.class);
    availableActions.add(UsersDataExporter.class);
    availableActions.add(UpdateInvoicesDoubleAmountZonesAndExchangeRate.class);
    availableActions.add(UpdateOCRInvoiceStatus.class);
  }

  /**
   * CLI entry point.
   *
   * @param args Arguments passed to StandaloneAction
   */
  public static void main(final String[] args) {
    final CommandLine cmd = parseArgs(args);
    if (cmd == null) {
      return;
    }

    if (cmd.hasOption(HELP_OPTION)) {
      printHelp();
      System.out.println("");
    }

    final Class<? extends StandaloneAction> action;
    if (StringUtils.isNotEmpty(cmd.getOptionValue(PROPERTIES_TYPE))) {
      int type = Integer.parseInt(cmd.getOptionValue(PROPERTIES_TYPE));
      action = availableActions.get(type - 1);
    }
    else {
      action = readAction(displayActions());
    }

    if (action != null) {
      start(action, cmd.getOptionValue(PROPERTIES_OPTION), args);
    }
  }

  /**
   * Main entry point of standalone app.
   *
   * @param type implementation
   * @param args Arguments passed to StandaloneAction
   */
  public static void start(final Class<? extends StandaloneAction> type, final String propertiesFileName, final String[] args) {
    logger.info("Starting process");

    AnnotationConfigApplicationContext ctx = null;
    try {
      logger.info("Initialiazing Spring Context");
      ctx = new AnnotationConfigApplicationContext();
      ctx.register(StandaloneConfiguration.class);
      if (propertiesFileName != null) {
        ctx.getEnvironment()
            .getSystemProperties()
            .put(StandaloneConfiguration.PROPERTIES_LOCATION_NAME, propertiesFileName);
      }
      ctx.refresh();

      logger.info("Registering bean {}", type);
      ctx.register(type);

      logger.info("Starting action {}", type);
      ctx.getBean(StandaloneAction.class)
          .doAction(args);
    }
    catch (final Exception e) {
      logger.error(e.getMessage(), e);
    }
    finally {
      logger.info("Ending process");
      if (ctx != null) {
        ctx.close();
      }

    }
  }

  private static CommandLine parseArgs(final String[] args) {
    final CommandLineParser parser = new DefaultParser();
    try {
      return parser.parse(cliOptions, args);
    }
    catch (final ParseException e) {
      printHelp();
      System.err.println("Error during parsing of command line arguments : " + e.getMessage());
      return null;
    }
  }

  private static void printHelp() {
    new HelpFormatter().printHelp("Standalone", cliOptions);
  }

  private static int displayActions() {
    System.out.println("Available standalone actions : ");

    int i = 0;
    for (Class<? extends StandaloneAction> availableAction : availableActions) {
      System.out.println(++i + ") " + availableAction.getSimpleName());
    }
    System.out.println("");
    System.out.println("0) Quit");

    return i;
  }

  private static Class<? extends StandaloneAction> readAction(final int max) {
    try (final Scanner in = new Scanner(System.in)) {
      int choice;
      do {
        System.out.print("Choose wanted actions : ");
        choice = in.nextInt(10);
      }
      while (choice < 0 || choice > max);

      return choice > 0 ? availableActions.get(choice - 1) : null;
    }
  }

  private StandaloneMain() {
    throw new AssertionError();
  }
}
