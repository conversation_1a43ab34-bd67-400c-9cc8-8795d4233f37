package com.byzaneo.generix.standalone.action;

import static com.byzaneo.commons.bean.FileType.XCBL;
import static com.byzaneo.generix.edocument.service.InvoiceServiceImpl.PREDICATE_INVOICE_XCBL;
import static com.byzaneo.xtrade.xcbl.bean.InvoiceIndexer.INDEXER_NAME;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.*;
import java.util.*;

import javax.xml.bind.JAXBException;
import javax.xml.stream.XMLStreamException;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.commons.util.*;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.xtrade.api.Indexable;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.*;

public class UpdateInvoicesDoubleAmountZonesAndExchangeRate implements StandaloneAction {
  private final static Logger log = getLogger(UpdateInvoicesDoubleAmountZonesAndExchangeRate.class);

  static final int BATCH_MAX_SIZE = 1000;
  @Autowired
  @Qualifier(DocumentService.SERVICE_NAME)
  protected DocumentService documentService;

  @Transactional
  public void doAction(String[] args) {
    List<Indexable> indexablesMigrated = new ArrayList<>();
    InvoiceIndexer invoiceIndexer = SpringContextHelper.getBean(InvoiceIndexer.class, INDEXER_NAME);
    Pageable pageable = Pageable.ofSize(BATCH_MAX_SIZE);
    long saved = 0;
    Query query = new QueryBuilder().append("creationDate > date(-8760h)")
        .asc("to")
        .query();
    long total = documentService.countIndexable(InvoiceIndex.class, query);
    List<Document> documents = documentService.searchIndexedDocuments(Document.class, InvoiceIndex.class, query, pageable);
    log.info("* * * * * Start migrating double amount zones and exchange rate for invoices. * * * * *");
    log.info("Query used: " + query.toString());
    log.info("Total number of invoice to migrate: {}", total);
    while (documents.size() != 0) {
      for (Document document : documents)
        try {
          Invoice invoice = createInvoiceBasedOnXcbl(document);
          invoiceIndexer.populateMonetaryAmountTAC(invoice, document.getIndexValue());
          indexablesMigrated.add(document.getIndexValue());
        }
        catch (Exception e) {
          log.error("Can't migrate invoice " + document.getReference(), e);
        }
      saved += indexablesMigrated.size();
      documentService.saveIndexables(indexablesMigrated);
      indexablesMigrated.clear();
      log.info("Migrated {}/{} invoices", saved, total);
      pageable = pageable.next();
      documents = documentService.searchIndexedDocuments(Document.class, InvoiceIndex.class, query, pageable);
    }
    log.info("* * * * * End migrating double amount zones and exchange rate for invoices. * * * * *");
  }

  public Invoice createInvoiceBasedOnXcbl(Document document) throws XMLStreamException, JAXBException, IOException {
    File invoiceFile = null;
    if (document.getFiles()
        .stream()
        .filter(PREDICATE_INVOICE_XCBL)
        .count() > 1) {
      MessageHelper.error("Document invalid : official index is not unique");
    }
    else {
      invoiceFile = document.getFiles()
          .stream()
          // PREDICATE_INVOICE_XCBL_TYPE instead of PREDICATE_INVOICE_XCBL
          .filter(dof -> XCBL.equals(dof.getType()))
          .findFirst()
          .map(DocumentFile::getFile)
          .orElse(null);
    }
    return JAXBHelper.unmarshal(Invoice.class, invoiceFile, new InvalidValueAdapter());
  }
}